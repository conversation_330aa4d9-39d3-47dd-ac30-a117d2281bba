<!DOCTYPE html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="profile" href="https://gmpg.org/xfn/11">

<title>BetterDocs Development &#8211; Just another WordPress site</title><link rel="stylesheet" href="http://betterdocs.test/wp-content/cache/min/1/392b3d43e1874971bef78c9baca6980a.css" data-minify="1" />
<meta name='robots' content='noindex,nofollow' />
<link href='https://fonts.gstatic.com' crossorigin rel='preconnect' />
<link rel="alternate" type="application/rss+xml" title="BetterDocs Development &raquo; Feed" href="http://betterdocs.test/feed/" />
<link rel="alternate" type="application/rss+xml" title="BetterDocs Development &raquo; Comments Feed" href="http://betterdocs.test/comments/feed/" />
<style>
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	


<style id='astra-theme-css-inline-css'>
html{font-size:93.75%;}a,.page-title{color:#0274be;}a:hover,a:focus{color:#3a3a3a;}body,button,input,select,textarea,.ast-button,.ast-custom-button{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;font-weight:inherit;font-size:15px;font-size:1rem;}blockquote{color:#000000;}.site-title{font-size:35px;font-size:2.3333333333333rem;}.ast-archive-description .ast-archive-title{font-size:40px;font-size:2.6666666666667rem;}.site-header .site-description{font-size:15px;font-size:1rem;}.entry-title{font-size:40px;font-size:2.6666666666667rem;}.comment-reply-title{font-size:24px;font-size:1.6rem;}.ast-comment-list #cancel-comment-reply-link{font-size:15px;font-size:1rem;}h1,.entry-content h1{font-size:40px;font-size:2.6666666666667rem;}h2,.entry-content h2{font-size:30px;font-size:2rem;}h3,.entry-content h3{font-size:25px;font-size:1.6666666666667rem;}h4,.entry-content h4{font-size:20px;font-size:1.3333333333333rem;}h5,.entry-content h5{font-size:18px;font-size:1.2rem;}h6,.entry-content h6{font-size:15px;font-size:1rem;}.ast-single-post .entry-title,.page-title{font-size:30px;font-size:2rem;}#secondary,#secondary button,#secondary input,#secondary select,#secondary textarea{font-size:15px;font-size:1rem;}::selection{background-color:#0274be;color:#ffffff;}body,h1,.entry-title a,.entry-content h1,h2,.entry-content h2,h3,.entry-content h3,h4,.entry-content h4,h5,.entry-content h5,h6,.entry-content h6,.wc-block-grid__product-title{color:#3a3a3a;}.tagcloud a:hover,.tagcloud a:focus,.tagcloud a.current-item{color:#ffffff;border-color:#0274be;background-color:#0274be;}.main-header-menu .menu-link,.ast-header-custom-item a{color:#3a3a3a;}.main-header-menu .menu-item:hover > .menu-link,.main-header-menu .menu-item:hover > .ast-menu-toggle,.main-header-menu .ast-masthead-custom-menu-items a:hover,.main-header-menu .menu-item.focus > .menu-link,.main-header-menu .menu-item.focus > .ast-menu-toggle,.main-header-menu .current-menu-item > .menu-link,.main-header-menu .current-menu-ancestor > .menu-link,.main-header-menu .current-menu-item > .ast-menu-toggle,.main-header-menu .current-menu-ancestor > .ast-menu-toggle{color:#0274be;}input:focus,input[type="text"]:focus,input[type="email"]:focus,input[type="url"]:focus,input[type="password"]:focus,input[type="reset"]:focus,input[type="search"]:focus,textarea:focus{border-color:#0274be;}input[type="radio"]:checked,input[type=reset],input[type="checkbox"]:checked,input[type="checkbox"]:hover:checked,input[type="checkbox"]:focus:checked,input[type=range]::-webkit-slider-thumb{border-color:#0274be;background-color:#0274be;box-shadow:none;}.site-footer a:hover + .post-count,.site-footer a:focus + .post-count{background:#0274be;border-color:#0274be;}.footer-adv .footer-adv-overlay{border-top-style:solid;border-top-color:#7a7a7a;}.ast-comment-meta{line-height:1.666666667;font-size:12px;font-size:0.8rem;}.single .nav-links .nav-previous,.single .nav-links .nav-next,.single .ast-author-details .author-title,.ast-comment-meta{color:#0274be;}.entry-meta,.entry-meta *{line-height:1.45;color:#0274be;}.entry-meta a:hover,.entry-meta a:hover *,.entry-meta a:focus,.entry-meta a:focus *{color:#3a3a3a;}.ast-404-layout-1 .ast-404-text{font-size:200px;font-size:13.333333333333rem;}.widget-title{font-size:21px;font-size:1.4rem;color:#3a3a3a;}#cat option,.secondary .calendar_wrap thead a,.secondary .calendar_wrap thead a:visited{color:#0274be;}.secondary .calendar_wrap #today,.ast-progress-val span{background:#0274be;}.secondary a:hover + .post-count,.secondary a:focus + .post-count{background:#0274be;border-color:#0274be;}.calendar_wrap #today > a{color:#ffffff;}.ast-pagination a,.page-links .page-link,.single .post-navigation a{color:#0274be;}.ast-pagination a:hover,.ast-pagination a:focus,.ast-pagination > span:hover:not(.dots),.ast-pagination > span.current,.page-links > .page-link,.page-links .page-link:hover,.post-navigation a:hover{color:#3a3a3a;}.ast-header-break-point .ast-mobile-menu-buttons-minimal.menu-toggle{background:transparent;color:#0274be;}.ast-header-break-point .ast-mobile-menu-buttons-outline.menu-toggle{background:transparent;border:1px solid #0274be;color:#0274be;}.ast-header-break-point .ast-mobile-menu-buttons-fill.menu-toggle{background:#0274be;}.wp-block-buttons.aligncenter{justify-content:center;}@media (min-width:1200px){.ast-separate-container.ast-right-sidebar .entry-content .wp-block-image.alignfull,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-image.alignfull,.ast-separate-container.ast-right-sidebar .entry-content .wp-block-cover.alignfull,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-cover.alignfull{margin-left:-6.67em;margin-right:-6.67em;max-width:unset;width:unset;}.ast-separate-container.ast-right-sidebar .entry-content .wp-block-image.alignwide,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-image.alignwide,.ast-separate-container.ast-right-sidebar .entry-content .wp-block-cover.alignwide,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-cover.alignwide{margin-left:-20px;margin-right:-20px;max-width:unset;width:unset;}.wp-block-group .has-background{padding:20px;}}@media (min-width:1200px){.ast-separate-container.ast-right-sidebar .entry-content .wp-block-group.alignwide,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-group.alignwide,.ast-separate-container.ast-right-sidebar .entry-content .wp-block-cover.alignwide,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-cover.alignwide,.ast-no-sidebar.ast-separate-container .entry-content .wp-block-group.alignwide,.ast-no-sidebar.ast-separate-container .entry-content .wp-block-cover.alignwide{margin-left:-20px;margin-right:-20px;padding-left:20px;padding-right:20px;}.ast-separate-container.ast-right-sidebar .entry-content .wp-block-group.alignfull,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-group.alignfull,.ast-no-sidebar.ast-separate-container .entry-content .wp-block-group.alignfull,.ast-separate-container.ast-right-sidebar .entry-content .wp-block-cover.alignfull,.ast-separate-container.ast-left-sidebar .entry-content .wp-block-cover.alignfull,.ast-no-sidebar.ast-separate-container .entry-content .wp-block-cover.alignfull{margin-left:-6.67em;margin-right:-6.67em;padding-left:6.67em;padding-right:6.67em;}.ast-plain-container.ast-right-sidebar .entry-content .wp-block-group.alignwide,.ast-plain-container.ast-left-sidebar .entry-content .wp-block-group.alignwide,.ast-plain-container.ast-right-sidebar .entry-content .wp-block-group.alignfull,.ast-plain-container.ast-left-sidebar .entry-content .wp-block-group.alignfull{padding-left:20px;padding-right:20px;}.ast-plain-container.ast-no-sidebar .entry-content .alignwide .wp-block-group__inner-container,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .wp-block-group__inner-container,.ast-plain-container.ast-no-sidebar .entry-content .alignwide .wp-block-cover__inner-container,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .wp-block-cover__inner-container{max-width:1240px;margin-left:auto;margin-right:auto;padding-left:20px;padding-right:20px;}.ast-plain-container.ast-no-sidebar .entry-content .alignwide .wp-block-cover__inner-container,.ast-plain-container.ast-no-sidebar .entry-content .alignfull .wp-block-cover__inner-container{width:1240px;}.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-group.alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-group.alignfull{margin-left:0;margin-right:0;}.wp-block-cover-image.alignwide .wp-block-cover__inner-container,.wp-block-cover.alignwide .wp-block-cover__inner-container,.wp-block-cover-image.alignfull .wp-block-cover__inner-container,.wp-block-cover.alignfull .wp-block-cover__inner-container{width:100%;}.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-cover.alignwide,.ast-page-builder-template.ast-left-sidebar .entry-content .wp-block-cover.alignwide,.ast-page-builder-template.ast-right-sidebar .entry-content .wp-block-cover.alignwide,.ast-page-builder-template.ast-no-sidebar .entry-content .wp-block-cover.alignfull,.ast-page-builder-template.ast-left-sidebar .entry-content .wp-block-cover.alignfull,.ast-page-builder-template.ast-right-sidebar .entry-content .wp-block-cover.alignful{padding-right:0;padding-left:0;}}.wp-block-columns{margin-bottom:unset;}.wp-block-image.size-full{margin:2rem 0;}.wp-block-separator.has-background{padding:0;}.wp-block-gallery{margin-bottom:1.6em;}.wp-block-group{padding-top:4em;padding-bottom:4em;}.wp-block-group__inner-container .wp-block-columns:last-child,.wp-block-group__inner-container :last-child,.wp-block-table table{margin-bottom:0;}.blocks-gallery-grid{width:100%;}.wp-block-navigation-link__content{padding:5px 0;}.wp-block-group .wp-block-group .has-text-align-center,.wp-block-group .wp-block-column .has-text-align-center{max-width:100%;}.has-text-align-center{margin:0 auto;}@media (min-width:1200px){.wp-block-cover__inner-container,.alignwide .wp-block-group__inner-container,.alignfull .wp-block-group__inner-container{max-width:1200px;margin:0 auto;}.wp-block-group.alignnone,.wp-block-group.aligncenter,.wp-block-group.alignleft,.wp-block-group.alignright,.wp-block-group.alignwide,.wp-block-columns.alignwide{margin:2rem 0 1rem 0;}}@media (max-width:1200px){.wp-block-group{padding:3em;}.wp-block-group .wp-block-group{padding:1.5em;}.wp-block-columns,.wp-block-column{margin:1rem 0;}}@media (min-width:921px){.wp-block-columns .wp-block-group{padding:2em;}}@media (max-width:544px){.wp-block-cover-image .wp-block-cover__inner-container,.wp-block-cover .wp-block-cover__inner-container{width:unset;}.wp-block-cover,.wp-block-cover-image{padding:2em 0;}.wp-block-group,.wp-block-cover{padding:2em;}.wp-block-media-text__media img,.wp-block-media-text__media video{width:unset;max-width:100%;}.wp-block-media-text.has-background .wp-block-media-text__content{padding:1em;}}@media (min-width:544px){.entry-content .wp-block-media-text.has-media-on-the-right .wp-block-media-text__content{padding:0 8% 0 0;}.entry-content .wp-block-media-text .wp-block-media-text__content{padding:0 0 0 8%;}.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-bottom-left > *,.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-bottom-right > *,.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-top-left > *,.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-top-right > *,.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-center-right > *,.ast-plain-container .site-content .entry-content .has-custom-content-position.is-position-center-left > *{margin:0;}}@media (max-width:544px){.entry-content .wp-block-media-text .wp-block-media-text__content{padding:8% 0;}.wp-block-media-text .wp-block-media-text__media img{width:auto;max-width:100%;}}@media (max-width:921px){#secondary.secondary{padding-top:0;}.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single{padding:1.5em 2.14em;}.ast-separate-container #primary,.ast-separate-container #secondary{padding:1.5em 0;}.ast-separate-container.ast-right-sidebar #secondary{padding-left:1em;padding-right:1em;}.ast-separate-container.ast-two-container #secondary{padding-left:0;padding-right:0;}.ast-page-builder-template .entry-header #secondary{margin-top:1.5em;}.ast-page-builder-template #secondary{margin-top:1.5em;}#primary,#secondary{padding:1.5em 0;margin:0;}.ast-left-sidebar #content > .ast-container{display:flex;flex-direction:column-reverse;width:100%;}.ast-author-box img.avatar{margin:20px 0 0 0;}.ast-pagination{padding-top:1.5em;text-align:center;}.ast-pagination .next.page-numbers{display:inherit;float:none;}}@media (max-width:921px){.ast-page-builder-template.ast-left-sidebar #secondary{padding-right:20px;}.ast-page-builder-template.ast-right-sidebar #secondary{padding-left:20px;}.ast-right-sidebar #primary{padding-right:0;}.ast-right-sidebar #secondary{padding-left:0;}.ast-left-sidebar #primary{padding-left:0;}.ast-left-sidebar #secondary{padding-right:0;}.ast-pagination .prev.page-numbers{padding-left:.5em;}.ast-pagination .next.page-numbers{padding-right:.5em;}}@media (min-width:922px){.ast-separate-container.ast-right-sidebar #primary,.ast-separate-container.ast-left-sidebar #primary{border:0;}.ast-separate-container.ast-right-sidebar #secondary,.ast-separate-container.ast-left-sidebar #secondary{border:0;margin-left:auto;margin-right:auto;}.ast-separate-container.ast-two-container #secondary .widget:last-child{margin-bottom:0;}.ast-separate-container .ast-comment-list li .comment-respond{padding-left:2.66666em;padding-right:2.66666em;}.ast-author-box{-js-display:flex;display:flex;}.ast-author-bio{flex:1;}.error404.ast-separate-container #primary,.search-no-results.ast-separate-container #primary{margin-bottom:4em;}}@media (min-width:922px){.ast-right-sidebar #primary{border-right:1px solid #eee;}.ast-right-sidebar #secondary{border-left:1px solid #eee;margin-left:-1px;}.ast-left-sidebar #primary{border-left:1px solid #eee;}.ast-left-sidebar #secondary{border-right:1px solid #eee;margin-right:-1px;}.ast-separate-container.ast-two-container.ast-right-sidebar #secondary{padding-left:30px;padding-right:0;}.ast-separate-container.ast-two-container.ast-left-sidebar #secondary{padding-right:30px;padding-left:0;}}.wp-block-button .wp-block-button__link,{color:#ffffff;}.wp-block-button .wp-block-button__link{border-style:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;border-color:#0274be;background-color:#0274be;color:#ffffff;font-family:inherit;font-weight:inherit;line-height:1;border-radius:2px;padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.wp-block-button .wp-block-button__link:hover,.wp-block-button .wp-block-button__link:focus{color:#ffffff;background-color:#3a3a3a;border-color:#3a3a3a;}.menu-toggle,button,.ast-button,.ast-custom-button,.button,input#submit,input[type="button"],input[type="submit"],input[type="reset"]{border-style:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;color:#ffffff;border-color:#0274be;background-color:#0274be;border-radius:2px;padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;font-family:inherit;font-weight:inherit;line-height:1;}button:focus,.menu-toggle:hover,button:hover,.ast-button:hover,.button:hover,input[type=reset]:hover,input[type=reset]:focus,input#submit:hover,input#submit:focus,input[type="button"]:hover,input[type="button"]:focus,input[type="submit"]:hover,input[type="submit"]:focus{color:#ffffff;background-color:#3a3a3a;border-color:#3a3a3a;}@media (min-width:921px){.ast-container{max-width:100%;}}@media (min-width:544px){.ast-container{max-width:100%;}}@media (max-width:544px){.ast-separate-container .ast-article-post,.ast-separate-container .ast-article-single{padding:1.5em 1em;}.ast-separate-container #content .ast-container{padding-left:0.54em;padding-right:0.54em;}.ast-separate-container #secondary{padding-top:0;}.ast-separate-container.ast-two-container #secondary .widget{margin-bottom:1.5em;padding-left:1em;padding-right:1em;}.ast-separate-container .comments-count-wrapper{padding:1.5em 1em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 1em;margin-bottom:1.5em;}.ast-separate-container .ast-comment-list .bypostauthor{padding:.5em;}.ast-separate-container .ast-archive-description{padding:1.5em 1em;}.ast-search-menu-icon.ast-dropdown-active .search-field{width:170px;}.ast-separate-container .comment-respond{padding:1.5em 1em;}}@media (max-width:544px){.ast-comment-list .children{margin-left:0.66666em;}.ast-separate-container .ast-comment-list .bypostauthor li{padding:0 0 0 .5em;}}@media (max-width:921px){.ast-mobile-header-stack .main-header-bar .ast-search-menu-icon{display:inline-block;}.ast-header-break-point.ast-header-custom-item-outside .ast-mobile-header-stack .main-header-bar .ast-search-icon{margin:0;}.ast-comment-avatar-wrap img{max-width:2.5em;}.comments-area{margin-top:1.5em;}.ast-separate-container .comments-count-wrapper{padding:2em 2.14em;}.ast-separate-container .ast-comment-list li.depth-1{padding:1.5em 2.14em;}.ast-separate-container .comment-respond{padding:2em 2.14em;}}@media (max-width:921px){.ast-header-break-point .main-header-bar .ast-search-menu-icon.slide-search .search-form{right:0;}.ast-header-break-point .ast-mobile-header-stack .main-header-bar .ast-search-menu-icon.slide-search .search-form{right:-1em;}.ast-comment-avatar-wrap{margin-right:0.5em;}}@media (min-width:545px){.ast-page-builder-template .comments-area,.single.ast-page-builder-template .entry-header,.single.ast-page-builder-template .post-navigation{max-width:1240px;margin-left:auto;margin-right:auto;}}@media (max-width:921px){.ast-archive-description .ast-archive-title{font-size:40px;}.entry-title{font-size:30px;}h1,.entry-content h1{font-size:30px;}h2,.entry-content h2{font-size:25px;}h3,.entry-content h3{font-size:20px;}.ast-single-post .entry-title,.page-title{font-size:30px;}}@media (max-width:544px){.ast-archive-description .ast-archive-title{font-size:40px;}.entry-title{font-size:30px;}h1,.entry-content h1{font-size:30px;}h2,.entry-content h2{font-size:25px;}h3,.entry-content h3{font-size:20px;}.ast-single-post .entry-title,.page-title{font-size:30px;}}@media (max-width:921px){html{font-size:85.5%;}}@media (max-width:544px){html{font-size:85.5%;}}@media (min-width:922px){.ast-container{max-width:1240px;}}@font-face {font-family: "Astra";src: url(http://betterdocs.test/wp-content/themes/astra/assets/fonts/astra.woff) format("woff"),url(http://betterdocs.test/wp-content/themes/astra/assets/fonts/astra.ttf) format("truetype"),url(http://betterdocs.test/wp-content/themes/astra/assets/fonts/astra.svg#astra) format("svg");font-weight: normal;font-style: normal;font-display: fallback;}@media (max-width:921px) {.main-header-bar .main-header-bar-navigation{display:none;}}.ast-desktop .main-header-menu.submenu-with-border .sub-menu,.ast-desktop .main-header-menu.submenu-with-border .astra-full-megamenu-wrapper{border-color:#0274be;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu{border-top-width:2px;border-right-width:0px;border-left-width:0px;border-bottom-width:0px;border-style:solid;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .sub-menu{top:-2px;}.ast-desktop .main-header-menu.submenu-with-border .sub-menu .menu-link,.ast-desktop .main-header-menu.submenu-with-border .children .menu-link{border-bottom-width:0px;border-style:solid;border-color:#eaeaea;}@media (min-width:922px){.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu:hover > .sub-menu,.main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu.focus > .sub-menu{margin-left:-0px;}}.ast-small-footer{border-top-style:solid;border-top-width:1px;border-top-color:#7a7a7a;}.ast-small-footer-wrap{text-align:center;}@media (max-width:920px){.ast-404-layout-1 .ast-404-text{font-size:100px;font-size:6.6666666666667rem;}}.ast-breadcrumbs .trail-browse,.ast-breadcrumbs .trail-items,.ast-breadcrumbs .trail-items li{display:inline-block;margin:0;padding:0;border:none;background:inherit;text-indent:0;}.ast-breadcrumbs .trail-browse{font-size:inherit;font-style:inherit;font-weight:inherit;color:inherit;}.ast-breadcrumbs .trail-items{list-style:none;}.trail-items li::after{padding:0 0.3em;content:"\00bb";}.trail-items li:last-of-type::after{display:none;}.ast-header-break-point .main-header-bar{border-bottom-width:1px;}@media (min-width:922px){.main-header-bar{border-bottom-width:1px;}}@media (min-width:922px){#primary{width:70%;}#secondary{width:30%;}}.ast-safari-browser-less-than-11 .main-header-menu .menu-item, .ast-safari-browser-less-than-11 .main-header-bar .ast-masthead-custom-menu-items{display:block;}.main-header-menu .menu-item, .main-header-bar .ast-masthead-custom-menu-items{-js-display:flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;}.main-header-menu > .menu-item > .menu-link{height:100%;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-js-display:flex;display:flex;}.ast-primary-menu-disabled .main-header-bar .ast-masthead-custom-menu-items{flex:unset;}
</style>








<style id='betterdocs-instant-answer-inline-css'>
.betterdocs-widget-container{z-index:9999}.betterdocs-conversation-container, .betterdocs-footer-wrapper, .betterdocs-launcher, .betterdocs-ask-wrapper .betterdocs-ask-submit{background-color:#dd3333}.betterdocs-footer-wrapper .bd-ia-feedback-wrap, .betterdocs-footer-wrapper .bd-ia-feedback-response{background-color:#dd3333}.betterdocs-header-wrapper .betterdocs-header .inner-container.betterdocs-active-answer .toggle:first-of-type > p, .betterdocs-header-wrapper .betterdocs-header .inner-container.betterdocs-active-ask .toggle:last-of-type > p{color:#dd3333}.betterdocs-header-wrapper .betterdocs-header .inner-container.betterdocs-active-answer .toggle:first-of-type svg, .betterdocs-header-wrapper .betterdocs-header .inner-container.betterdocs-active-ask .toggle:last-of-type svg{fill:#dd3333}.betterdocs-header-wrapper .betterdocs-header .inner-container, .betterdocs-footer-wrapper .betterdocs-footer-emo > div{background-color:#dd9933}.betterdocs-launcher[type=button], .betterdocs-launcher[type=button]:focus {background-color:#eeee22}.betterdocs-launcher[type=button]:hover {background-color:#ed497d}.betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ans-header > h3, .betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ask-header > h3{color:#000000}.betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ans-header > h3.bd-ia-subtitle, .betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ask-header > h3.bd-ia-subtitle {font-size:24px}.betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ans-header > p, .betterdocs-header-wrapper .betterdocs-sub-header.betterdocs-ask-header > p{color:#595959}.betterdocs-search-wrap.MuiPaper-root, .betterdocs-search-bar .betterdocs-search-wrap .betterdocs-search-container input{background-color:#bcbcbc}.betterdocs-search-bar .betterdocs-search-wrap .betterdocs-search-container input{color:#15847f}.betterdocs-search-bar .betterdocs-search-wrap .betterdocs-search-icon {fill:#1e73be}.betterdocs-messages-container .betterdocs-card-link { background-color:#d1d1d1}.betterdocs-messages-container .betterdocs-card-link .betterdocs-card-title-wrapper .betterdocs-card-title { color:#000000}.betterdocs-messages-container .betterdocs-card-link .betterdocs-card-body-wrapper .betterdocs-card-body { color:#000000}.betterdocs-footer-wrapper .betterdocs-footer-label p { color:#000000}.betterdocs-footer-wrapper .betterdocs-emo { fill:#dd9933}.betterdocs-footer-wrapper .bd-ia-feedback-response .feedback-success-icon {fill: #dd9933}.betterdocs-footer-wrapper .bd-ia-feedback-response .feedback-success-title {color: #000000}.betterdocs-tab-ask .betterdocs-ask-wrapper input[type="text"], .betterdocs-tab-ask .betterdocs-ask-wrapper input[type="email"], .betterdocs-tab-ask .betterdocs-ask-wrapper textarea { background-color: #a3a3a3}.betterdocs-tab-ask .betterdocs-ask-wrapper .betterdocs-ask-submit { background-color: #eeee22}.betterdocs-tab-ask .betterdocs-ask-wrapper .betterdocs-ask-submit:hover { background-color: #8224e3}.betterdocs-tab-ask .betterdocs-ask-wrapper .betterdocs-ask-submit.betterdocs-disable-submit { background-color: #828282}.betterdocs-tab-ask .betterdocs-ask-wrapper .betterdocs-ask-submit.betterdocs-disable-submit:hover { background-color: #dd3333}.betterdocs-ask-wrapper input:not([type="submit"]), .betterdocs-ask-wrapper textarea, .betterdocs-ask-wrapper .betterdocs-attach-button { color: #000000}.betterdocs-ask-wrapper .betterdocs-attach-button { fill: #000000}.betterdocs-ask-wrapper input:not([type="submit"])::placeholder, .betterdocs-ask-wrapper textarea::placeholder { color: #000000}.betterdocs-ask-wrapper input:not([type="submit"]), .betterdocs-ask-wrapper textarea { color: #000000 !important;}
</style>





<link rel='stylesheet' id='woocommerce-smallscreen-css'  href='http://betterdocs.test/wp-content/themes/astra/assets/css/minified/compatibility/woocommerce/woocommerce-smallscreen.min.css?ver=2.6.1' media='only screen and (max-width: 921px)' />

<style id='woocommerce-general-inline-css'>
.woocommerce span.onsale, .wc-block-grid__product .wc-block-grid__product-onsale{background-color:#0274be;color:#ffffff;}.woocommerce a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled], .woocommerce input.button:disabled:hover, .woocommerce input.button:disabled[disabled]:hover, .woocommerce #respond input#submit, .woocommerce button.button.alt.disabled, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link, .wc-block-grid__product-onsale{color:#ffffff;border-color:#0274be;background-color:#0274be;}.woocommerce a.button:hover, .woocommerce button.button:hover, .woocommerce .woocommerce-message a.button:hover,.woocommerce #respond input#submit:hover,.woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, .woocommerce input.button:hover, .woocommerce button.button.alt.disabled:hover, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{color:#ffffff;border-color:#3a3a3a;background-color:#3a3a3a;}.woocommerce-message, .woocommerce-info{border-top-color:#0274be;}.woocommerce-message::before,.woocommerce-info::before{color:#0274be;}.woocommerce ul.products li.product .price, .woocommerce div.product p.price, .woocommerce div.product span.price, .widget_layered_nav_filters ul li.chosen a, .woocommerce-page ul.products li.product .ast-woo-product-category, .wc-layered-nav-rating a{color:#3a3a3a;}.woocommerce nav.woocommerce-pagination ul,.woocommerce nav.woocommerce-pagination ul li{border-color:#0274be;}.woocommerce nav.woocommerce-pagination ul li a:focus, .woocommerce nav.woocommerce-pagination ul li a:hover, .woocommerce nav.woocommerce-pagination ul li span.current{background:#0274be;color:#ffffff;}.woocommerce-MyAccount-navigation-link.is-active a{color:#3a3a3a;}.woocommerce .widget_price_filter .ui-slider .ui-slider-range, .woocommerce .widget_price_filter .ui-slider .ui-slider-handle{background-color:#0274be;}.woocommerce a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce-cart table.cart td.actions .button, .woocommerce form.checkout_coupon .button, .woocommerce #respond input#submit, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link{border-radius:2px;padding-top:10px;padding-right:40px;padding-bottom:10px;padding-left:40px;}.woocommerce .star-rating, .woocommerce .comment-form-rating .stars a, .woocommerce .star-rating::before{color:#0274be;}.woocommerce div.product .woocommerce-tabs ul.tabs li.active:before{background:#0274be;}.ast-site-header-cart a{color:#3a3a3a;}.ast-site-header-cart a:focus, .ast-site-header-cart a:hover, .ast-site-header-cart .current-menu-item a{color:#0274be;}.ast-cart-menu-wrap .count, .ast-cart-menu-wrap .count:after{border-color:#0274be;color:#0274be;}.ast-cart-menu-wrap:hover .count{color:#ffffff;background-color:#0274be;}.ast-site-header-cart .widget_shopping_cart .total .woocommerce-Price-amount{color:#0274be;}.woocommerce a.remove:hover, .ast-woocommerce-cart-menu .main-header-menu .woocommerce-custom-menu-item .menu-item:hover > .menu-link.remove:hover{color:#0274be;border-color:#0274be;background-color:#ffffff;}.ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce .widget_shopping_cart .woocommerce-mini-cart__buttons .checkout.wc-forward{color:#ffffff;border-color:#3a3a3a;background-color:#3a3a3a;}.site-header .ast-site-header-cart-data .button.wc-forward, .site-header .ast-site-header-cart-data .button.wc-forward:hover{color:#ffffff;}.below-header-user-select .ast-site-header-cart .widget, .ast-above-header-section .ast-site-header-cart .widget a, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a{color:#3a3a3a;}.below-header-user-select .ast-site-header-cart .widget_shopping_cart a:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a:hover, .below-header-user-select .ast-site-header-cart .widget_shopping_cart a.remove:hover, .ast-above-header-section .ast-site-header-cart .widget_shopping_cart a.remove:hover{color:#0274be;}@media (min-width:545px) and (max-width:921px){.woocommerce.tablet-columns-6 ul.products li.product, .woocommerce-page.tablet-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.tablet-columns-5 ul.products li.product, .woocommerce-page.tablet-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.tablet-columns-4 ul.products li.product, .woocommerce-page.tablet-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.tablet-columns-3 ul.products li.product, .woocommerce-page.tablet-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.tablet-columns-2 ul.products li.product, .woocommerce-page.tablet-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.tablet-columns-1 ul.products li.product, .woocommerce-page.tablet-columns-1 ul.products li.product{width:100%;}.woocommerce div.product .related.products ul.products li.product{width:calc(33.33% - 14px);}}@media (min-width:545px) and (max-width:921px){.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(33.33% - 14px);margin-right:20px;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(3n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(3n+1){clear:left;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n){margin-right:0;clear:right;}.woocommerce.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.tablet-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.tablet-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.tablet-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.tablet-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.tablet-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.tablet-columns-6 ul.products li.product:nth-child(6n+1){clear:left;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n){margin-right:0;clear:right;}.woocommerce div.product .related.products ul.products li.product:nth-child(3n+1){clear:left;}}@media (min-width:922px){.woocommerce #reviews #comments{width:55%;float:left;}.woocommerce #reviews #review_form_wrapper{width:45%;float:right;padding-left:2em;}.woocommerce form.checkout_coupon{width:50%;}}@media (max-width:921px){.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack.ast-no-menu-items .ast-site-header-cart{padding-right:0;padding-left:0;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .main-header-bar{text-align:center;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-site-header-cart, .ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-1.ast-mobile-header-stack .ast-mobile-menu-buttons{display:inline-block;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-2.ast-mobile-header-inline .site-branding{flex:auto;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .site-branding{flex:0 0 100%;}.ast-header-break-point.ast-woocommerce-cart-menu .header-main-layout-3.ast-mobile-header-stack .main-header-container{display:flex;justify-content:center;}.woocommerce-cart .woocommerce-shipping-calculator .button{width:100%;}.woocommerce div.product div.images, .woocommerce div.product div.summary, .woocommerce #content div.product div.images, .woocommerce #content div.product div.summary, .woocommerce-page div.product div.images, .woocommerce-page div.product div.summary, .woocommerce-page #content div.product div.images, .woocommerce-page #content div.product div.summary{float:none;width:100%;}.woocommerce-cart table.cart td.actions .ast-return-to-shop{display:block;text-align:center;margin-top:1em;}}@media (max-width:544px){.ast-separate-container .ast-woocommerce-container{padding:.54em 1em 1.33333em;}.woocommerce-message, .woocommerce-error, .woocommerce-info{display:flex;flex-wrap:wrap;}.woocommerce-message a.button, .woocommerce-error a.button, .woocommerce-info a.button{order:1;margin-top:.5em;}.woocommerce.mobile-columns-6 ul.products li.product, .woocommerce-page.mobile-columns-6 ul.products li.product{width:calc(16.66% - 16.66px);}.woocommerce.mobile-columns-5 ul.products li.product, .woocommerce-page.mobile-columns-5 ul.products li.product{width:calc(20% - 16px);}.woocommerce.mobile-columns-4 ul.products li.product, .woocommerce-page.mobile-columns-4 ul.products li.product{width:calc(25% - 15px);}.woocommerce.mobile-columns-3 ul.products li.product, .woocommerce-page.mobile-columns-3 ul.products li.product{width:calc(33.33% - 14px);}.woocommerce.mobile-columns-2 ul.products li.product, .woocommerce-page.mobile-columns-2 ul.products li.product{width:calc(50% - 10px);}.woocommerce.mobile-columns-1 ul.products li.product, .woocommerce-page.mobile-columns-1 ul.products li.product{width:100%;}.woocommerce .woocommerce-ordering, .woocommerce-page .woocommerce-ordering{float:none;margin-bottom:2em;width:100%;}.woocommerce ul.products a.button, .woocommerce-page ul.products a.button{padding:0.5em 0.75em;}.woocommerce div.product .related.products ul.products li.product{width:calc(50% - 10px);}.woocommerce table.cart td.actions .button, .woocommerce #content table.cart td.actions .button, .woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button{padding-left:1em;padding-right:1em;}.woocommerce #content table.cart .button, .woocommerce-page #content table.cart .button{width:100%;}.woocommerce #content table.cart .product-thumbnail, .woocommerce-page #content table.cart .product-thumbnail{display:block;text-align:center !important;}.woocommerce #content table.cart .product-thumbnail::before, .woocommerce-page #content table.cart .product-thumbnail::before{display:none;}.woocommerce #content table.cart td.actions .coupon, .woocommerce-page #content table.cart td.actions .coupon{float:none;}.woocommerce #content table.cart td.actions .coupon .button, .woocommerce-page #content table.cart td.actions .coupon .button{flex:1;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li a, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li a{display:block;}}@media (max-width:544px){.woocommerce[class*="columns-"].columns-3 > ul.products li.product, .woocommerce[class*="columns-"].columns-4 > ul.products li.product, .woocommerce[class*="columns-"].columns-5 > ul.products li.product, .woocommerce[class*="columns-"].columns-6 > ul.products li.product{width:calc(50% - 10px);margin-right:20px;}.woocommerce-page[class*=columns-] ul.products li.product:nth-child(n), .woocommerce[class*=columns-] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce-page[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-].columns-6>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-3>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-4>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-5>ul.products li.product:nth-child(2n), .woocommerce[class*=columns-].columns-6>ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce[class*="columns-"].columns-3 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-4 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-5 > ul.products li.product:nth-child(2n+1), .woocommerce[class*="columns-"].columns-6 > ul.products li.product:nth-child(2n+1){clear:left;}.woocommerce[class*="columns-"] ul.products li.product:nth-child(n), .woocommerce-page[class*="columns-"] ul.products li.product:nth-child(n){margin-right:20px;clear:none;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n), .woocommerce div.product .related.products ul.products li.product:nth-child(2n){margin-right:0;clear:right;}.woocommerce.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce-page.mobile-columns-6 ul.products li.product:nth-child(6n+1), .woocommerce.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce-page.mobile-columns-5 ul.products li.product:nth-child(5n+1), .woocommerce.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce-page.mobile-columns-4 ul.products li.product:nth-child(4n+1), .woocommerce.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce-page.mobile-columns-3 ul.products li.product:nth-child(3n+1), .woocommerce.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce-page.mobile-columns-2 ul.products li.product:nth-child(2n+1), .woocommerce div.product .related.products ul.products li.product:nth-child(2n+1){clear:left;}.woocommerce ul.products a.button.loading::after, .woocommerce-page ul.products a.button.loading::after{display:inline-block;margin-left:5px;position:initial;}.woocommerce.mobile-columns-1 ul.products li.product:nth-child(n), .woocommerce-page.mobile-columns-1 ul.products li.product:nth-child(n){margin-right:0;}.woocommerce #content div.product .woocommerce-tabs ul.tabs li, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li{display:block;margin-right:0;}}@media (min-width:922px){.ast-woo-shop-archive .site-content > .ast-container{max-width:1240px;}}@media (min-width:922px){.woocommerce #content .ast-woocommerce-container div.product div.images, .woocommerce .ast-woocommerce-container div.product div.images, .woocommerce-page #content .ast-woocommerce-container div.product div.images, .woocommerce-page .ast-woocommerce-container div.product div.images{width:50%;}.woocommerce #content .ast-woocommerce-container div.product div.summary, .woocommerce .ast-woocommerce-container div.product div.summary, .woocommerce-page #content .ast-woocommerce-container div.product div.summary, .woocommerce-page .ast-woocommerce-container div.product div.summary{width:46%;}.woocommerce.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce.woocommerce-checkout form #customer_details.col2-set .col-2, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-1, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set .col-2{float:none;width:auto;}}.woocommerce a.button , .woocommerce button.button.alt ,.woocommerce-page table.cart td.actions .button, .woocommerce-page #content table.cart td.actions .button , .woocommerce a.button.alt ,.woocommerce .woocommerce-message a.button , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout, .woocommerce button.button.alt.disabled , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link {border:solid;border-top-width:0;border-right-width:0;border-left-width:0;border-bottom-width:0;border-color:#0274be;}.woocommerce a.button:hover , .woocommerce button.button.alt:hover , .woocommerce-page table.cart td.actions .button:hover, .woocommerce-page #content table.cart td.actions .button:hover, .woocommerce a.button.alt:hover ,.woocommerce .woocommerce-message a.button:hover , .ast-site-header-cart .widget_shopping_cart .buttons .button.checkout:hover , .woocommerce button.button.alt.disabled:hover , .wc-block-grid__products .wc-block-grid__product .wp-block-button__link:hover{border-color:#3a3a3a;}@media (min-width:922px){.woocommerce.woocommerce-checkout form #customer_details.col2-set, .woocommerce-page.woocommerce-checkout form #customer_details.col2-set{width:55%;float:left;margin-right:4.347826087%;}.woocommerce.woocommerce-checkout form #order_review, .woocommerce.woocommerce-checkout form #order_review_heading, .woocommerce-page.woocommerce-checkout form #order_review, .woocommerce-page.woocommerce-checkout form #order_review_heading{width:40%;float:right;margin-right:0;clear:right;}}
</style>
<style id='woocommerce-inline-inline-css'>
.woocommerce form .form-row .required { visibility: visible; }
</style>
<!--[if IE]>
<script src='http://betterdocs.test/wp-content/themes/astra/assets/js/minified/flexibility.min.js?ver=2.6.1' id='astra-flexibility-js'></script>
<script id='astra-flexibility-js-after'>
flexibility(document.documentElement);</script>
<![endif]-->
<script src='http://betterdocs.test/wp-includes/js/jquery/jquery.js?ver=1.12.4-wp' id='jquery-core-js'></script>





<link rel="https://api.w.org/" href="http://betterdocs.test/wp-json/" /><link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://betterdocs.test/xmlrpc.php?rsd" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://betterdocs.test/wp-includes/wlwmanifest.xml" /> 
<meta name="generator" content="WordPress 5.5.3" />
<meta name="generator" content="WooCommerce 4.7.0" />
	<style type="text/css">
		.betterdocs-wraper.betterdocs-main-wraper {
						background-color: #ffffff;		
																				}
		.betterdocs-archive-wrap.betterdocs-archive-main {
			padding-top: 50px;
			padding-bottom: 50px;
			padding-left: 0px;
			padding-right: 0px;
		}
		.betterdocs-archive-wrap.betterdocs-archive-main {
			width: 100%;
			max-width: 1600px;
		}
		.betterdocs-categories-wrap.single-kb.layout-masonry .docs-single-cat-wrap {
			margin-bottom: 15px;
		}
		.betterdocs-categories-wrap.single-kb.layout-flex .docs-single-cat-wrap {
			margin: 15px; 
		}
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap .docs-cat-title-wrap { 
			padding-top: 20px; 
		}
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap .docs-cat-title-wrap, 
		.betterdocs-archive-main .docs-item-container { 
			padding-right: 20px;
			padding-left: 20px;  
		}
		.betterdocs-archive-main .docs-item-container { 
			padding-bottom: 20px; 
		}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap,
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap.docs-cat-list-2-box {
			padding-top: 20px; 
			padding-right: 20px;
			padding-left: 20px; 
			padding-bottom: 20px; 
		}
		.betterdocs-categories-wrap.betterdocs-category-box .docs-single-cat-wrap p{
						color: #566e8b;
					}
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap,
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap .docs-cat-title-wrap {
						border-top-left-radius: 5px;
									border-top-right-radius: 5px;
					}
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap,
		.betterdocs-categories-wrap.single-kb .docs-single-cat-wrap .docs-item-container {
						border-bottom-right-radius: 5px;
									border-bottom-left-radius: 5px;
					}
		.betterdocs-category-list .betterdocs-categories-wrap .docs-single-cat-wrap,
		.betterdocs-category-box.white-bg .docs-single-cat-wrap,
		.betterdocs-categories-wrap.white-bg .docs-single-cat-wrap {
						background-color: #fff;
					}
		.betterdocs-category-box.single-kb.ash-bg .docs-single-cat-wrap {
						background-color: #f8f8fc;
					}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap:hover,
		.betterdocs-categories-wrap.single-kb.white-bg .docs-single-cat-wrap.docs-cat-list-2-box:hover {
						background-color: #fff;
					}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap img {
						margin-bottom: 20px;
					}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap .docs-cat-title,
		.pro-layout-4.single-kb .docs-cat-list-2-box-content .docs-cat-title {
						margin-bottom: 15px;
					}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap p {
						margin-bottom: 15px;
					}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap span {
					}
		.docs-cat-title > img { 
			height: 32px; 
		}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap img { 
			height: 80px; 
		}
		.single-kb .docs-cat-title-inner h3,
		.betterdocs-category-box.single-kb .docs-single-cat-wrap .docs-cat-title,
		.single-kb .docs-cat-list-2-box .docs-cat-title,
		.single-kb .docs-cat-list-2-items .docs-cat-title {
			font-size: 20px;
		}
		.docs-cat-title-inner h3 {
			color: #528ffe; 
		}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap .docs-cat-title,
		.single-kb .docs-cat-list-2 .docs-cat-title {
			color: #333333;
		}
				.docs-cat-title-inner {
			border-color: #528ffe; 
		}
		.docs-cat-title-inner span {
			color: #ffffff; 
			font-size: 15px;
		}
		.betterdocs-category-box.single-kb .docs-single-cat-wrap span,
		.single-kb .docs-cat-list-2-box .title-count span {
			color: #707070; 
			font-size: 15px;
		}
		.betterdocs-categories-wrap .docs-item-count {
			background-color: #528ffe; 
		}

		.betterdocs-categories-wrap.single-kb .docs-cat-title-inner span {
			background-color: rgba(82,143,254,0.44);
			width: 30px; 
			height: 30px;
		}
		.betterdocs-categories-wrap.single-kb .docs-item-container {
			background-color: #ffffff;
		}
		.betterdocs-categories-wrap.single-kb .docs-item-container li,
		.betterdocs-categories-wrap.single-kb .docs-item-container .docs-sub-cat-title {
			margin-top: 10px;
			margin-right: 10px;
			margin-bottom: 10px;
			margin-left: 10px;
		}
		.betterdocs-categories-wrap.single-kb .docs-item-container li svg {
			fill: #566e8b;
			font-size: 15px;
		}
		.betterdocs-categories-wrap.single-kb li a {
			color: #566e8b;
			font-size: 15px;
		}
				.betterdocs-categories-wrap.single-kb .docs-item-container .docs-sub-cat li a {
			color: #566e8b;
		}
						.betterdocs-categories-wrap.single-kb .docs-item-container .docs-sub-cat li a:hover {
			color: #566e8b;
		}
						.betterdocs-categories-wrap.single-kb .docs-item-container .docs-sub-cat li svg {
			color: #566e8b;
		}
				.betterdocs-categories-wrap.single-kb li a:hover {
			color: #566e8b;
		}
		.betterdocs-categories-wrap.single-kb .docs-item-container .docs-sub-cat-title svg {
			fill: #566e8b;
			font-size: 15px;
		}
		.betterdocs-categories-wrap.single-kb .docs-sub-cat-title a {
			color: #566e8b;
			font-size: 17px;
		}
		.betterdocs-categories-wrap.single-kb .docs-sub-cat-title a:hover {
			color: #566e8b;
		}
		.docs-cat-link-btn, .docs-cat-link-btn:visited {
			background-color: #ffffff;
			font-size: 16px;
			color: #528ffe;
			border-color: #528ffe;
			border-top-left-radius: 50px;
			border-top-right-radius: 50px;
			border-bottom-right-radius: 50px;
			border-bottom-left-radius: 50px;
			padding-top: 10px;
			padding-right: 20px;
			padding-bottom: 10px;
			padding-left: 20px;
		}
		.docs-cat-link-btn:hover {
			background-color: #528ffe;
			color: #fff;
			border-color: #528ffe;
		}
		.betterdocs-single-bg .betterdocs-content-area, .betterdocs-single-bg .betterdocs-content-full {
			background-color: ;	
		}
		.betterdocs-single-wraper .betterdocs-content-area {
			padding-top: 30px;
			padding-right: 25px;
			padding-bottom: 30px;
			padding-left: 25px;
		}
		.betterdocs-single-wraper .betterdocs-content-area .docs-single-main {
			padding-top: 20px;
			padding-right: 20px;
			padding-bottom: 20px;
			padding-left: 20px;
		}
		.betterdocs-single-layout2 .docs-content-full-main .doc-single-content-wrapper {
			padding-top: 0px;
			padding-right: 0px;
			padding-bottom: 0px;
			padding-left: 0px;
		}
		.betterdocs-single-layout3 .docs-content-full-main .doc-single-content-wrapper {
			padding-top: 0px;
			padding-right: 0px;
			padding-bottom: 0px;
			padding-left: 0px;
		}
		.docs-single-title .betterdocs-entry-title {
			font-size: 36px;
			color: #3f5876;
		}
		.betterdocs-breadcrumb .betterdocs-breadcrumb-item a {
			font-size: 16px;
			color: #566e8b;
		}
		.betterdocs-breadcrumb .betterdocs-breadcrumb-list .betterdocs-breadcrumb-item a:hover {
			color: #566e8b;
		}
		.betterdocs-breadcrumb .breadcrumb-delimiter {
			color: #566e8b;
		}
		.betterdocs-breadcrumb-item.current span {
			font-size: 16px;
			color: #528fff;
		}
		.betterdocs-toc {
			background-color: #fff;
			padding-top: 20px;
			padding-right: 25px;
			padding-bottom: 20px;
			padding-left: 20px;
		}
		.betterdocs-entry-content .betterdocs-toc {
			margin-bottom: 20px;
		}
		.sticky-toc-container {
			width: 320px;
		}
		.sticky-toc-container.toc-sticky {
			z-index: 2;
			margin-top: 0px;
		}
		.betterdocs-toc > .toc-title {
			color: #3f5876;
			font-size: 18px;
		}
		.betterdocs-entry-content .betterdocs-toc.collapsible-sm .angle-icon {
			color: #3f5876;
		}
		.betterdocs-toc > .toc-list a {
			color: #566e8b;
			font-size: 14px;
			margin-top: 5px;
			margin-right: 0px;
			margin-bottom: 5px;
			margin-left: 0px;
		}
		.betterdocs-toc > .toc-list li a:before {
			font-size: 12px;
			color: #566e8b;
		}
		.betterdocs-toc > .toc-list li:before {
			padding-top: 5px;
		}
		.betterdocs-toc > .toc-list a:hover {
			color: #528fff;
		}
		.feedback-form-link .feedback-form-icon svg, .feedback-form-link .feedback-form-icon img {
			width: 26px;
		}
		.betterdocs-toc > .toc-list a.active {
			color: #528fff;
		}
		.betterdocs-content {
			color: #4d4d4d;
			font-size: 16px;
		}
		.betterdocs-social-share .betterdocs-social-share-heading h5 {
			color: #566e8b;
		}
		.betterdocs-entry-footer .feedback-form-link {
			color: #566e8b;
			font-size: 15px;
		}
		.betterdocs-entry-footer .feedback-update-form .feedback-form-link:hover {
			color: #566e8b;
		}
		.docs-navigation a {
			color: #3f5876;
			font-size: 16px;
		}
		.docs-navigation a:hover {
			color: #3f5876;
		}
		.docs-navigation a svg{
			fill: #5edf8e;
			width: 16px;
		}
		.betterdocs-entry-footer .update-date{
			color: #566e8b;
			font-size: 14px;
		}
		.betterdocs-credit p{
			color: #201d3a;
			font-size: 14px;
		}
		.betterdocs-credit p a{
			color: #528fff;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap,
		.betterdocs-full-sidebar-left {
			background-color: #ffffff;
		}
		.betterdocs-single-layout1 .betterdocs-sidebar-content .betterdocs-categories-wrap {
						border-top-left-radius: 5px;
									border-top-right-radius: 5px;
									border-bottom-right-radius: 5px;
									border-bottom-left-radius: 5px;
					}
		.betterdocs-sidebar-content .docs-single-cat-wrap .docs-cat-title-wrap {
			background-color: #ffffff;
		}
		.betterdocs-sidebar-content .docs-cat-title > img{
			height: 24px;
		}
		.betterdocs-sidebar-content .docs-cat-title-inner h3{
			color: #3f5876;
			font-size: 16px;
		}
		.betterdocs-sidebar-content .docs-cat-title-inner h3:hover {
			color: #3f5876 !important;
		}
		.betterdocs-sidebar-content .docs-cat-title-inner .cat-list-arrow-down {
			color: #3f5876;
		}
		.betterdocs-sidebar-content .docs-single-cat-wrap .active-title .docs-cat-title-inner h3,
		.betterdocs-sidebar-content .active-title .docs-cat-title-inner h3,
		.betterdocs-full-sidebar-left .docs-cat-title-wrap::after {
			color: #3f5876;
		}
		.betterdocs-sidebar-content .docs-item-count {
			background-color: #528ffe;
		}
		.betterdocs-sidebar-content .docs-item-count span {
			background-color: rgba(82, 143, 255, 0.2);
			color: #ffffff;
			font-size: 12px;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap .docs-single-cat-wrap {
			margin-top: 5px;
			margin-right: 0px;
			margin-bottom: 5px;
			margin-left: 0px;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap, .betterdocs-full-sidebar-left .betterdocs-categories-wrap {
			padding-top: 0px;
			padding-right: 0px;
			padding-bottom: 0px;
			padding-left: 0px;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap .docs-single-cat-wrap .docs-cat-title-wrap {
			padding-top: 10px;
			padding-right: 15px;
			padding-bottom: 10px;
			padding-left: 15px;
		}
		.betterdocs-single-layout2 .betterdocs-full-sidebar-left .betterdocs-sidebar-content .betterdocs-categories-wrap .docs-cat-title-inner {
						background-color: #ffffff;		
						padding-top: 10px;
			padding-right: 15px;
			padding-bottom: 10px;
			padding-left: 15px;
		}
		.betterdocs-sidebar-content .docs-item-container{
			background-color: #ffffff;
		}
		.betterdocs-sidebar-content .docs-single-cat-wrap .docs-cat-title-wrap.active-title{
			background-color: rgba(90, 148, 255, .1);
			border-color: #528fff;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap .docs-item-container li {
			padding-left: 0;
			margin-top: 10px;
			margin-right: 10px;
			margin-bottom: 10px;
			margin-left: 10px;
		}
		.betterdocs-single-layout2 .betterdocs-sidebar-content .betterdocs-categories-wrap .docs-item-container li {
			margin-right: 0 !important;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap li a {
			color: #566e8b;
			font-size: 14px;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap li a:hover {
			color: #528fff;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap li svg {
			fill: #566e8b;
			font-size: 14px;
		}
		.betterdocs-sidebar-content .betterdocs-categories-wrap li a.active,
		.betterdocs-sidebar-content .betterdocs-categories-wrap li.sub-list a.active {
			color: #528fff;
		}	
		.betterdocs-category-wraper.betterdocs-single-wraper{
																				}	
		.betterdocs-category-wraper.betterdocs-single-wraper .docs-listing-main .docs-category-listing{
						background-color: #ffffff;
						margin-top: 0px;
			margin-right: 0px;
			margin-bottom: 0px;
			margin-left: 0px;
			padding-top: 30px;
			padding-right: 30px;
			padding-bottom: 30px;
			padding-left: 30px;
			border-radius: 5px;
		}
		.betterdocs-category-wraper .docs-category-listing .docs-cat-title h3 {
			color: #566e8b;
			font-size: 20px;
			margin-top: 0px;
			margin-right: 0px;
			margin-bottom: 20px;
			margin-left: 0px;
		}
		.betterdocs-category-wraper .docs-category-listing .docs-cat-title p {
			color: #566e8b;
			font-size: 14px;
			margin-top: 0px;
			margin-right: 0px;
			margin-bottom: 20px;
			margin-left: 0px;
		}
		.docs-category-listing .docs-list ul li, 
		.docs-category-listing .docs-list .docs-sub-cat-title {
			margin-top: 10px;
			margin-right: 0px;
			margin-bottom: 10px;
			margin-left: 0px;
		}
		.docs-category-listing .docs-list ul li svg {
			fill: #566e8b;
			font-size: 16px;
		}
		.docs-category-listing .docs-list ul li a {
			color: #566e8b;
			font-size: 14px;
		}
		.docs-category-listing .docs-list ul li a:hover {
			color: #528ffe;
		}
		.betterdocs-search-form-wrap{
						background-color: #f7f7f7;
																					padding-top: 50px;
			padding-right: 20px;
			padding-bottom: 50px;
			padding-left: 20px;
		}
		.betterdocs-search-heading h2 {
			line-height: 1.2;
			font-size: 40px;
			color: #566e8b;
			margin-top: 0px;
			margin-right: 0px;
			margin-bottom: 20px;
			margin-left: 0px;
		}
		.betterdocs-search-heading h3 {
			line-height: 1.2;
			font-size: 16px;
			color: #566e8b;
			margin-top: 0px;
			margin-right: 0px;
			margin-bottom: 20px;
			margin-left: 0px;
		}
		.betterdocs-searchform {
			background-color: #ffffff;
			border-radius: 8px;
			padding-top: 22px;
			padding-right: 15px;
			padding-bottom: 22px;
			padding-left: 15px;
		}
		.betterdocs-searchform .betterdocs-search-field{
			font-size: 18px;
			color: #595959;
		}
		.betterdocs-searchform svg.docs-search-icon {
			fill: #444b54;
			height: 30px;
		}
		.docs-search-close path.close-line {
			fill: #ff697b;	
		}
		.docs-search-close path.close-border {
			fill: #444b54;	
		}
		.docs-search-loader {
			stroke: #444b54;	
		}
		.betterdocs-searchform svg.docs-search-icon:hover {
			fill: #444b54;
		}
		.betterdocs-live-search .docs-search-result {
			width: 100%;
			max-width: 800px;
			background-color: #fff;
			border-color: #f1f1f1;
		}
		.betterdocs-search-result-wrap::before {
			border-color: transparent transparent #fff;
		}
		.betterdocs-live-search .docs-search-result li {
			border-color: #f5f5f5;
		}
		.betterdocs-live-search .docs-search-result li a {
			font-size: 16px;
			color: #444444;
			padding-top: 10px;
			padding-right: 10px;
			padding-bottom: 10px;
			padding-left: 10px;
		}
		.betterdocs-live-search .docs-search-result li:only-child {
			font-size: 16px;
			color: #444444;
		}
		.betterdocs-live-search .docs-search-result li:hover {
			background-color: #f5f5f5;
		}
		.betterdocs-live-search .docs-search-result li a:hover {
			color: #444444;
		}
		.betterdocs-category-box.pro-layout-3 .docs-single-cat-wrap img,
		.docs-cat-list-2-box img {
			margin-right: 20px;
		}
		.betterdocs-wraper .betterdocs-search-form-wrap.cat-layout-4 {
			padding-bottom: 130px;
		}
	</style>
	
    	<style type="text/css">
					.betterdocs-wraper.betterdocs-mkb-wraper {
								background-color: #ffffff;		
																											}
			.betterdocs-archive-wrap.betterdocs-archive-mkb {
				padding-top: 50px;
				padding-bottom: 50px;
				padding-left: 0px;
				padding-right: 0px;
			}
			.betterdocs-archive-wrap.betterdocs-archive-mkb {
				width: 100%;
				max-width: 1600px;
			}
			.betterdocs-categories-wrap.multiple-kb.layout-masonry .docs-single-cat-wrap {
				margin-bottom: 15px;
			}
			.betterdocs-categories-wrap.multiple-kb.layout-flex .docs-single-cat-wrap {
				margin: 15px; 
			}
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap .docs-cat-title-wrap { 
				padding-top: 20px; 
			}
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap .docs-cat-title-wrap, 
			.betterdocs-archive-mkb .docs-item-container { 
				padding-right: 20px;
				padding-left: 20px;  
			}
			.betterdocs-archive-mkb .docs-item-container { 
				padding-bottom: 20px; 
			}
			.betterdocs-categories-wrap.multiple-kb.betterdocs-category-box .docs-single-cat-wrap,
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap.docs-cat-list-2-box {
				padding-top: 20px; 
				padding-right: 20px;
				padding-left: 20px; 
				padding-bottom: 20px; 
			}
			.betterdocs-categories-wrap.betterdocs-category-box .docs-single-cat-wrap p{
							}
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap,
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap .docs-cat-title-wrap {
								border-top-left-radius: 5px;
												border-top-right-radius: 5px;
							}
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap,
			.betterdocs-categories-wrap.multiple-kb .docs-single-cat-wrap .docs-item-container {
								border-bottom-right-radius: 5px;
												border-bottom-left-radius: 5px;
							}
			.betterdocs-category-box.multiple-kb.ash-bg .docs-single-cat-wrap {
								background-color: #f8f8fc;
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap:hover,
			.betterdocs-categories-wrap.multiple-kb.white-bg .docs-single-cat-wrap.docs-cat-list-2-box:hover {
								background-color: #fff;
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap img {
								margin-bottom: 20px;
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap .docs-cat-title {
								margin-bottom: 15px;
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap p {
								margin-bottom: 15px;
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap span {
							}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap img { 
				height: 80px; 
			}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap p { 
				color: #566e8bpx;
			}
			.multiple-kb .docs-cat-title-inner h3,
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap .docs-cat-title,
			.multiple-kb .docs-cat-list-2-box .docs-cat-title {
				font-size: 20px;
			}
			.betterdocs-category-box.multiple-kb .docs-single-cat-wrap .docs-cat-title,
			.multiple-kb .docs-cat-list-2 .docs-cat-title {
				color: #333333;
			}
			.betterdocs-categories-wrap.multiple-kb.betterdocs-category-box .docs-single-cat-wrap span,
			.multiple-kb .docs-cat-list-2-box .title-count span {
				color: #707070; 
				font-size: 15px;
			}
					
		
		.docs-cat-list-2-items .docs-cat-title {
			font-size: 18px;
		}
		.betterdocs-category-box.pro-layout-3 .docs-single-cat-wrap img,
		.docs-cat-list-2-box img {
						height: 60px;
						width: auto;
			margin-bottom: 0px !important;
		}
		
		.betterdocs-archive-wrap.cat-layout-4 {
			margin-top: -135px;
		}
		.betterdocs-archive-wrap .betterdocs-categories-wrap .docs-cat-list-2-items .docs-item-container li,
		.betterdocs-archive-wrap .betterdocs-categories-wrap .docs-cat-list-2-items .docs-item-container .docs-sub-cat-title {
			margin-left: 0;
			margin-right: 0;
		}
		.docs-cat-list-2-items .docs-cat-link-btn {
			margin-left: 0;
			margin-right: 0;
		}
		.betterdocs-single-bg .betterdocs-content-full {
			background-color: ;
		}
		.betterdocs-single-wraper .betterdocs-content-full {
			padding-right: 25px;
			padding-left: 25px;
		}
				.betterdocs-article-reactions .betterdocs-article-reactions-heading h5 {
			color: #566e8b;
		}
		.betterdocs-article-reaction-links li a {
			background-color: #00b88a;
		}
		.betterdocs-article-reaction-links li a:hover {
			background-color: #fff;
		}
		.betterdocs-article-reaction-links li a svg path {
			fill: #fff;
		}
		.betterdocs-article-reaction-links li a:hover svg path {
			fill: #00b88a;
		}
	</style>
    
	

	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<style>.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><style media="print">#wpadminbar { display:none; }</style>
	<style media="screen">
	html { margin-top: 32px !important; }
	* html body { margin-top: 32px !important; }
	@media screen and ( max-width: 782px ) {
		html { margin-top: 46px !important; }
		* html body { margin-top: 46px !important; }
	}
</style>
	</head>

<body itemtype='https://schema.org/Blog' itemscope='itemscope' class="home-page bp-nouveau home blog admin-bar no-customize-support theme-astra flexia-core-1.4.2 woocommerce-no-js ast-desktop ast-separate-container ast-right-sidebar astra-2.6.1 ast-header-custom-item-inside ast-inherit-site-logo-transparent elementor-default elementor-kit-4253 no-js">

		<div id="wpadminbar" class="nojq nojs">
							<a class="screen-reader-shortcut" href="#wp-toolbar" tabindex="1">Skip to toolbar</a>
						<div class="quicklinks" id="wp-toolbar" role="navigation" aria-label="Toolbar">
				<ul id='wp-admin-bar-root-default' class="ab-top-menu"><li id='wp-admin-bar-wp-logo' class="menupop"><div class="ab-item ab-empty-item" tabindex="0" aria-haspopup="true"><span class="ab-icon"></span><span class="screen-reader-text">About WordPress</span></div><div class="ab-sub-wrapper"><ul id='wp-admin-bar-wp-logo-external' class="ab-sub-secondary ab-submenu"><li id='wp-admin-bar-wporg'><a class='ab-item' href='https://wordpress.org/'>WordPress.org</a></li><li id='wp-admin-bar-documentation'><a class='ab-item' href='https://codex.wordpress.org/'>Documentation</a></li><li id='wp-admin-bar-support-forums'><a class='ab-item' href='https://wordpress.org/support/'>Support</a></li><li id='wp-admin-bar-feedback'><a class='ab-item' href='https://wordpress.org/support/forum/requests-and-feedback'>Feedback</a></li></ul></div></li><li id='wp-admin-bar-bp-login'><a class='ab-item' href='http://betterdocs.test/wp-login.php?redirect_to=http%3A%2F%2Fbetterdocs.test%2Fdocs%2Fbetterdocs%2Fbase%2Fwordpress-review-popup%2F'>Log In</a></li><li id='wp-admin-bar-elementor_inspector' class="menupop"><div class="ab-item ab-empty-item" aria-haspopup="true">Elementor Debugger</div><div class="ab-sub-wrapper"><ul id='wp-admin-bar-elementor_inspector-default' class="ab-submenu"><li id='wp-admin-bar-elementor_inspector_theme' class="menupop"><div class="ab-item ab-empty-item" aria-haspopup="true"><span class="wp-admin-bar-arrow" aria-hidden="true"></span>Theme</div><div class="ab-sub-wrapper"><ul id='wp-admin-bar-elementor_inspector_theme-default' class="ab-submenu"><li id='wp-admin-bar-elementor_inspector_log_theme_0'><div class="ab-item ab-empty-item" target='_blank'>Template File: No Templates for condition > Astra - index.php</div></li></ul></div></li></ul></div></li></ul><ul id='wp-admin-bar-top-secondary' class="ab-top-secondary ab-top-menu"><li id='wp-admin-bar-search' class="admin-bar-search"><div class="ab-item ab-empty-item" tabindex="-1"><form action="http://betterdocs.test/" method="get" id="adminbarsearch"><input class="adminbar-input" name="s" id="adminbar-search" type="text" value="" maxlength="150" /><label for="adminbar-search" class="screen-reader-text">Search</label><input type="submit" class="adminbar-button" value="Search"/></form></div></li></ul>			</div>
					</div>

		<div 
	class="hfeed site" id="page">
	<a class="skip-link screen-reader-text" href="#content">Skip to content</a>

	
	
		<header
			class="site-header ast-primary-submenu-animation-fade header-main-layout-1 ast-primary-menu-enabled ast-logo-title-inline ast-hide-custom-menu-mobile ast-menu-toggle-icon ast-mobile-header-inline" id="masthead" itemtype="https://schema.org/WPHeader" itemscope="itemscope" itemid="#masthead"		>

			
			
<div class="main-header-bar-wrap">
	<div class="main-header-bar">
				<div class="ast-container">

			<div class="ast-flex main-header-container">
				
		<div class="site-branding">
			<div
			class="ast-site-identity" itemtype="https://schema.org/Organization" itemscope="itemscope"			>
				<div class="ast-site-title-wrap">
						<h1 class="site-title" itemprop="name">
				<a href="http://betterdocs.test/" rel="home" itemprop="url" >
					BetterDocs Development
				</a>
			</h1>
						
					</div>			</div>
		</div>

		<!-- .site-branding -->
				<div class="ast-mobile-menu-buttons">

			
					<div class="ast-button-wrap">
			<button type="button" class="menu-toggle main-header-menu-toggle  ast-mobile-menu-buttons-minimal "  aria-controls='primary-menu' aria-expanded='false'>
				<span class="screen-reader-text">Main Menu</span>
				<span class="menu-toggle-icon"></span>
							</button>
		</div>
			
			
		</div>
			<div class="ast-main-header-bar-alignment"><div class="main-header-bar-navigation"><nav class="site-navigation" id="site-navigation" itemtype="https://schema.org/SiteNavigationElement" itemscope="itemscope" class="ast-flex-grow-1 navigation-accessibility" aria-label="Site Navigation"><div id="primary-menu" class="main-navigation"><ul class="main-header-menu ast-nav-menu ast-flex ast-justify-content-flex-end  submenu-with-border astra-menu-animation-fade "><li class="page_item page-item-4422 menu-item"><a href="http://betterdocs.test/cart/" class="menu-link">Cart</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4423 menu-item"><a href="http://betterdocs.test/checkout/" class="menu-link">Checkout</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4399 menu-item"><a href="http://betterdocs.test/members/" class="menu-link">Members</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4424 menu-item"><a href="http://betterdocs.test/my-account/" class="menu-link">My account</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4398 menu-item"><a href="http://betterdocs.test/news-feed/" class="menu-link">News Feed</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-2 menu-item"><a href="http://betterdocs.test/sample-page/" class="menu-link">Sample Page</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4421 menu-item"><a href="http://betterdocs.test/shop/" class="menu-link">Shop</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li><li class="page_item page-item-4400 menu-item"><a href="http://betterdocs.test/terms-of-service/" class="menu-link">Terms of Service</a><button class="ast-menu-toggle" aria-expanded="false"><span class="screen-reader-text">Menu Toggle</span></button></li></ul></div></nav></div></div>			</div><!-- Main Header Container -->
		</div><!-- ast-row -->
			</div> <!-- Main Header Bar -->
</div> <!-- Main Header Bar Wrap -->

			
		</header><!-- #masthead -->

		
	
	
	<div id="content" class="site-content">

		<div class="ast-container">

		

	<div id="primary" class="content-area primary">

		
					<main id="main" class="site-main">

				
					<div class="ast-row">
					

<article 
	class="post-1 post type-post status-publish format-standard hentry category-uncategorized ast-col-sm-12 ast-article-post" id="post-1" itemtype="https://schema.org/CreativeWork" itemscope="itemscope">

	
	<div class="ast-post-format- ast-no-thumb blog-layout-1">

	<div class="post-content ast-col-md-12">

		<div class="ast-blog-featured-section post-thumb ast-col-md-12"></div>		<header class="entry-header">
			<h2 class="entry-title" itemprop="headline"><a href="http://betterdocs.test/hello-world/" rel="bookmark">Hello world!</a></h2>			<div class="entry-meta">			<span class="comments-link">
				<a href="http://betterdocs.test/hello-world/#comments">1 Comment</a>			</span>

			 / <span class="cat-links"><a href="http://betterdocs.test/category/uncategorized/" rel="category tag">Uncategorized</a></span> / By <span class="posted-by vcard author" itemtype="https://schema.org/Person" itemscope="itemscope" itemprop="author">			<a title="View all posts by admin" 
				href="http://betterdocs.test/author/admin/" rel="author"
				class="url fn n" itemprop="url"				>
				<span
				class="author-name" itemprop="name"				>admin</span>
			</a>
		</span>

		</div>		</header><!-- .entry-header -->
		
		<div class="entry-content clear"
			itemprop="text"		>

			
			<p>Welcome to WordPress. This is your first post. Edit or delete it, then start writing!</p>

			
					</div><!-- .entry-content .clear -->
	</div><!-- .post-content -->

</div> <!-- .blog-layout-1 -->

	
</article><!-- #post-## -->


					
					</div>
				
			</main><!-- #main -->
			
		<div class='ast-pagination'></div>
		
	</div><!-- #primary -->


	<div class="widget-area secondary" id="secondary" role="complementary" itemtype="https://schema.org/WPSideBar" itemscope="itemscope">
	<div class="sidebar-main" >

		
		
			<aside id="search-2" class="widget widget_search"><form role="search" method="get" class="search-form" action="http://betterdocs.test/">
				<label>
					<span class="screen-reader-text">Search for:</span>
					<input type="search" class="search-field" placeholder="Search &hellip;" value="" name="s" />
				</label>
				<input type="submit" class="search-submit" value="Search" />
			</form></aside>
		<aside id="recent-posts-2" class="widget widget_recent_entries">
		<h2 class="widget-title">Recent Posts</h2><nav role="navigation" aria-label="Recent Posts">
		<ul>
											<li>
					<a href="http://betterdocs.test/hello-world/">Hello world!</a>
									</li>
					</ul>

		</nav></aside><aside id="recent-comments-2" class="widget widget_recent_comments"><h2 class="widget-title">Recent Comments</h2><nav role="navigation" aria-label="Recent Comments"><ul id="recentcomments"><li class="recentcomments"><span class="comment-author-link"><a href='https://wordpress.org/' rel='external nofollow ugc' class='url'>A WordPress Commenter</a></span> on <a href="http://betterdocs.test/hello-world/#comment-1">Hello world!</a></li></ul></nav></aside><aside id="archives-2" class="widget widget_archive"><h2 class="widget-title">Archives</h2><nav role="navigation" aria-label="Archives">
			<ul>
					<li><a href='http://betterdocs.test/2020/11/'>November 2020</a></li>
			</ul>

			</nav></aside><aside id="categories-2" class="widget widget_categories"><h2 class="widget-title">Categories</h2><nav role="navigation" aria-label="Categories">
			<ul>
					<li class="cat-item cat-item-1"><a href="http://betterdocs.test/category/uncategorized/">Uncategorized</a>
</li>
			</ul>

			</nav></aside><aside id="meta-2" class="widget widget_meta"><h2 class="widget-title">Meta</h2><nav role="navigation" aria-label="Meta">
		<ul>
						<li><a href="http://betterdocs.test/wp-login.php">Log in</a></li>
			<li><a href="http://betterdocs.test/feed/">Entries feed</a></li>
			<li><a href="http://betterdocs.test/comments/feed/">Comments feed</a></li>

			<li><a href="https://wordpress.org/">WordPress.org</a></li>
		</ul>

		</nav></aside><aside id="woocommerce_product_categories-1" class="widget woocommerce widget_product_categories"><h2 class="widget-title">Categories</h2><ul class="product-categories"><li class="cat-item cat-item-57 cat-parent"><a href="http://betterdocs.test/product-category/clothing/">Clothing</a><ul class='children'>
<li class="cat-item cat-item-60"><a href="http://betterdocs.test/product-category/clothing/accessories/">Accessories</a></li>
<li class="cat-item cat-item-59"><a href="http://betterdocs.test/product-category/clothing/hoodies/">Hoodies</a></li>
<li class="cat-item cat-item-58"><a href="http://betterdocs.test/product-category/clothing/tshirts/">Tshirts</a></li>
</ul>
</li>
<li class="cat-item cat-item-62"><a href="http://betterdocs.test/product-category/decor/">Decor</a></li>
<li class="cat-item cat-item-61"><a href="http://betterdocs.test/product-category/music/">Music</a></li>
<li class="cat-item cat-item-56"><a href="http://betterdocs.test/product-category/uncategorized/">Uncategorized</a></li>
</ul></aside><aside id="woocommerce_products-1" class="widget woocommerce widget_products"><h2 class="widget-title">Featured</h2><ul class="product_list_widget"><li>
	
	<a href="http://betterdocs.test/product/sunglasses/">
		<img width="300" height="300" src="http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-300x300.jpg" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail" alt="" loading="lazy" srcset="http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-300x300.jpg 300w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-100x100.jpg 100w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-600x600.jpg 600w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-150x150.jpg 150w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-768x768.jpg 768w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2-624x624.jpg 624w, http://betterdocs.test/wp-content/uploads/2020/11/sunglasses-2.jpg 801w" sizes="(max-width: 300px) 100vw, 300px" />		<span class="product-title">Sunglasses</span>
	</a>

			<div class="star-rating"><span style="width:0%">Rated <strong class="rating">0</strong> out of 5</span></div>	
	<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>90</bdi></span>
	</li>
<li>
	
	<a href="http://betterdocs.test/product/hoodie-with-zipper/">
		<img width="300" height="300" src="http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-300x300.jpg" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail" alt="" loading="lazy" srcset="http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-300x300.jpg 300w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-100x100.jpg 100w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-600x600.jpg 600w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-150x150.jpg 150w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-768x768.jpg 768w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2-624x624.jpg 624w, http://betterdocs.test/wp-content/uploads/2020/11/hoodie-with-zipper-2.jpg 800w" sizes="(max-width: 300px) 100vw, 300px" />		<span class="product-title">Hoodie with Zipper</span>
	</a>

			<div class="star-rating"><span style="width:0%">Rated <strong class="rating">0</strong> out of 5</span></div>	
	<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>45</bdi></span>
	</li>
<li>
	
	<a href="http://betterdocs.test/product/cap/">
		<img width="300" height="300" src="http://betterdocs.test/wp-content/uploads/2020/11/cap-2-300x300.jpg" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail" alt="" loading="lazy" srcset="http://betterdocs.test/wp-content/uploads/2020/11/cap-2-300x300.jpg 300w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2-100x100.jpg 100w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2-600x600.jpg 600w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2-150x150.jpg 150w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2-768x768.jpg 768w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2-624x624.jpg 624w, http://betterdocs.test/wp-content/uploads/2020/11/cap-2.jpg 801w" sizes="(max-width: 300px) 100vw, 300px" />		<span class="product-title">Cap</span>
	</a>

			<div class="star-rating"><span style="width:0%">Rated <strong class="rating">0</strong> out of 5</span></div>	
	<del><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>18</bdi></span></del> <ins><span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>16</bdi></span></ins>
	</li>
<li>
	
	<a href="http://betterdocs.test/product/v-neck-t-shirt/">
		<img width="300" height="300" src="http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-300x300.jpg" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail" alt="" loading="lazy" srcset="http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-300x300.jpg 300w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-100x100.jpg 100w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-600x599.jpg 600w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-150x150.jpg 150w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-768x767.jpg 768w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2-624x623.jpg 624w, http://betterdocs.test/wp-content/uploads/2020/11/vneck-tee-2.jpg 801w" sizes="(max-width: 300px) 100vw, 300px" />		<span class="product-title">V-Neck T-Shirt</span>
	</a>

			<div class="star-rating"><span style="width:0%">Rated <strong class="rating">0</strong> out of 5</span></div>	
	<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>15</bdi></span> &ndash; <span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">&#2547;&nbsp;</span>20</bdi></span>
	</li>
</ul></aside>
		
		
	</div><!-- .sidebar-main -->
</div><!-- #secondary -->


			
			</div> <!-- ast-container -->

		</div><!-- #content -->

		
		
		
		<footer
			class="site-footer" id="colophon" itemtype="https://schema.org/WPFooter" itemscope="itemscope" itemid="#colophon"		>

			
			
<div class="ast-small-footer footer-sml-layout-1">
	<div class="ast-footer-overlay">
		<div class="ast-container">
			<div class="ast-small-footer-wrap" >
									<div class="ast-small-footer-section ast-small-footer-section-1" >
						Copyright &copy; 2020 <span class="ast-footer-site-title">BetterDocs Development</span> | Powered by <a href="https://wpastra.com/">Astra WordPress Theme</a>					</div>
				
				
			</div><!-- .ast-row .ast-small-footer-wrap -->
		</div><!-- .ast-container -->
	</div><!-- .ast-footer-overlay -->
</div><!-- .ast-small-footer-->

			
		</footer><!-- #colophon -->
		
		
	</div><!-- #page -->

	
	<div id="betterdocs-ia" class="betterdocs-right"></div>	
	

<script id='astra-theme-js-js-extra'>
var astra = {"break_point":"921","isRtl":""};
</script>




<script id='betterdocs-js-extra'>
var betterdocspublic = {"ajax_url":"http:\/\/betterdocs.test\/wp-admin\/admin-ajax.php","post_id":"1","copy_text":"Copied","sticky_toc_offset":"100"};
</script>













<script id='wp-util-js-extra'>
var _wpUtilSettings = {"ajax":{"url":"\/wp-admin\/admin-ajax.php"}};
</script>





















<script id='betterdocs-instant-answer-js-extra'>
var betterdocs = {"CHAT":{"label":"Ask","subtitle":"Stuck with something? Send us a message.","subtitle_two":"Generally, we reply within 24-48 hours."},"ANSWER":{"label":"Answer","subtitle":"Instant Answer"},"URL":"http:\/\/betterdocs.test?rest_route=\/wp\/v2\/docs&include=3845,3844,3850,3849,3851","SEARCH":{"SEARCH_URL":"http:\/\/betterdocs.test?rest_route=\/wp\/v2\/docs","SEARCH_PLACEHOLDER":"Search in ...","OOPS":"Opps...","NOT_FOUND":"We couldn\u2019t find any articles that match your search. Try searching for a\n        new term."},"FEEDBACK":{"DISPLAY":true,"SUCCESS":"Thanks for your feedback","TEXT":"How did you feel?","URL":"http:\/\/betterdocs.test?rest_route=\/betterdocs\/feedback"},"RESPONSE":{"title":"Thanks for the feedback"},"ASKFORM":{"NAME":"Name","EMAIL":"Email Address","SUBJECT":"Subject","TEXTAREA":"How can we help?","ATTACHMENT":"Only supports .jpg, .png, .jpeg, .gif files","SENDING":"Sending","SEND":"Send"},"ASK_URL":"http:\/\/betterdocs.test?rest_route=\/betterdocs\/ask","THANKS":{"title":"Thanks","text":"Your Message Has Been Sent Successfully"}};
</script>


<script id='bp-nouveau-js-extra'>
var BP_Nouveau = {"ajaxurl":"http:\/\/betterdocs.test\/wp-admin\/admin-ajax.php","only_admin_notice":"As you are the only organizer of this group, you cannot leave it. You can either delete the group or promote another member to be an organizer first and then leave the group.","is_friend_confirm":"Are you sure you want to remove your connection with this member?","confirm":"Are you sure?","confirm_delete_set":"Are you sure you want to delete this set? This cannot be undone.","show_x_comments":"View previous comments","unsaved_changes":"Your profile has unsaved changes. If you leave the page, the changes will be lost.","object_nav_parent":"#buddypress","empty_field":"New Field","objects":{"0":"activity","1":"members","4":"xprofile","9":"settings","10":"notifications"},"nonces":{"activity":"8196302d12","members":"86492c73d6","xprofile":"bbb262eea9","settings":"ee49daf13e","notifications":"60b04c69a0"},"activity":{"params":{"user_id":0,"object":"user","backcompat":false,"post_nonce":"0bf85930ad","excluded_hosts":[],"is_activity_edit":false,"errors":{"empty_post_update":"Sorry, Your update cannot be empty."},"avatar_url":false,"avatar_width":50,"avatar_height":50,"user_display_name":false,"user_domain":"","avatar_alt":"Profile photo of ","autoload":true,"objects":{"profile":{"text":"Post in: Profile","autocomplete_placeholder":"","priority":5}}},"strings":{"whatsnewPlaceholder":"Write here or use @ to mention someone.","whatsnewLabel":"Post what's new","whatsnewpostinLabel":"Post in","postUpdateButton":"Post Update","cancelButton":"Cancel","commentLabel":"%d Comment","commentsLabel":"%d Comments","loadingMore":"Loading..."}}};
</script>


<script id='heartbeat-js-extra'>
var heartbeatSettings = {"ajaxurl":"\/wp-admin\/admin-ajax.php"};
</script>


<script id='wc-add-to-cart-js-extra'>
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_view_cart":"View cart","cart_url":"http:\/\/betterdocs.test\/cart\/","is_cart":"","cart_redirect_after_add":"no"};
</script>


<script id='woocommerce-js-extra'>
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%"};
</script>

<script id='wc-cart-fragments-js-extra'>
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_9cc32e6a818220488f24535ae916863f","fragment_name":"wc_fragments_9cc32e6a818220488f24535ae916863f","request_timeout":"5000"};
</script>


			
			
	<script src="http://betterdocs.test/wp-content/cache/min/1/99115d4fbf7ec1764d92b3171bc7a212.js" data-minify="1" defer></script></body>
</html>

<!-- This website is like a Rocket, isn't it? Performance optimized by WP Rocket. Learn more: https://wp-rocket.me - Debug: cached@1606997503 -->