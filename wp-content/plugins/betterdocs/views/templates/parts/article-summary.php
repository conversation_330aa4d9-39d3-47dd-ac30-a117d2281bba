<?php
/**
 * Template part for displaying Article Summary
 *
 * <AUTHOR>
 * @package BetterDocs/Templates/Parts
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Set default parameters
$post_id = isset( $post_id ) ? $post_id : get_the_ID();
$custom_title = isset( $custom_title ) ? $custom_title : '';
$show_title = isset( $show_title ) ? $show_title : true;
$widget_type = isset( $widget_type ) ? $widget_type : 'template';
$blockId = isset( $blockId ) ? $blockId : '';

// Generate wrapper classes - always start with base class
$wrapper_classes = [ 'betterdocs-article-summary' ];

// Add widget-specific classes
if ( $widget_type === 'blocks' && ! empty( $blockId ) ) {
	$wrapper_classes[] = $blockId;
}
if ( $widget_type === 'elementor' ) {
	$wrapper_classes[] = 'betterdocs-elementor';
}

// Ensure we always have the base article summary class
if ( ! in_array( 'betterdocs-article-summary', $wrapper_classes ) ) {
	array_unshift( $wrapper_classes, 'betterdocs-article-summary' );
}

// Create collapsible arrow SVG icons (angle-right and angle-down)
$collapsible_arrow = "<svg class='angle-icon angle-right' height='14' width='14' aria-hidden='true' focusable='false' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'><path fill='currentColor' d='M96 96c-8.2 0-16.4 3.1-22.6 9.4c-12.5 12.5-12.5 32.8 0 45.3L205.5 256 73.4 389.3c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3l-160-160C112.4 99.1 104.2 96 96 96z'/></svg><svg class='angle-icon angle-down' height='14' width='14' style='display: none;' aria-hidden='true' focusable='false' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 320 512'><path fill='currentColor' d='M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z'/></svg>";

// Determine title text
$title_text = ! empty( $custom_title ) ? $custom_title : __( 'Article Summary', 'betterdocs' );

// Generate wrapper attributes - always use our classes for consistency
$wrapper_attr = 'class="' . esc_attr( implode( ' ', $wrapper_classes ) ) . '"';

// If custom wrapper attributes are provided (from blocks/elementor), merge them
if ( isset( $custom_wrapper_attr ) && ! empty( $custom_wrapper_attr ) && is_string( $custom_wrapper_attr ) ) {
	// Extract any additional attributes but keep our classes
	if ( strpos( $custom_wrapper_attr, 'style=' ) !== false ) {
		preg_match( '/style="([^"]*)"/', $custom_wrapper_attr, $style_matches );
		if ( ! empty( $style_matches[1] ) ) {
			$wrapper_attr .= ' style="' . esc_attr( $style_matches[1] ) . '"';
		}
	}
}

// Ensure style variables are strings
$title_style = isset( $title_style ) && is_string( $title_style ) ? $title_style : '';
$content_style = isset( $content_style ) && is_string( $content_style ) ? $content_style : '';
$icon_style = isset( $icon_style ) && is_string( $icon_style ) ? $icon_style : '';
$loading_style = isset( $loading_style ) && is_string( $loading_style ) ? $loading_style : '';
?>

<div <?php echo $wrapper_attr; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?> id="betterdocs-article-summary" data-post-id="<?php echo esc_attr( $post_id ); ?>" data-post-type="<?php echo esc_attr( get_post_type( $post_id ) ); ?>">
	<?php if ( $show_title ) : ?>
		<div class="betterdocs-summary-header" id="betterdocs-summary-toggle">
			<h3 class="betterdocs-summary-title" <?php echo ! empty( $title_style ) ? 'style="' . esc_attr( $title_style ) . '"' : ''; ?>>
				<span <?php echo ! empty( $icon_style ) ? 'style="' . esc_attr( $icon_style ) . '"' : ''; ?>>
					<?php echo $collapsible_arrow; //phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped -- SVG icons cannot be escaped ?>
				</span>
				<?php echo esc_html( $title_text ); ?>
			</h3>
		</div>
	<?php endif; ?>

	<div class="betterdocs-summary-content" id="betterdocs-summary-content" style="display: none;">
		<div class="betterdocs-summary-loading" id="betterdocs-summary-loading" <?php echo ! empty( $loading_style ) ? 'style="' . esc_attr( $loading_style ) . '"' : ''; ?>>
			<?php echo esc_html__( 'Thinking...', 'betterdocs' ); ?>
		</div>
		<div class="betterdocs-summary-text" id="betterdocs-summary-text" style="display: none;" <?php echo ! empty( $content_style ) ? 'data-style="' . esc_attr( $content_style ) . '"' : ''; ?>></div>
	</div>
</div>
