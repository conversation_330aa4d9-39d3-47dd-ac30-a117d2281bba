// Article Summary Block Styles
.betterdocs-article-summary {
    background-color: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;

    .betterdocs-summary-header {
        cursor: pointer;
        user-select: none;

        &:hover {
            .betterdocs-summary-title {
                opacity: 0.8;
            }
        }
    }

    .betterdocs-summary-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: opacity 0.2s ease;

        img {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .betterdocs-summary-arrow {
            margin-left: auto;

            .angle-icon {
                width: 12px;
                height: 12px;
                transition: transform 0.2s ease;
                flex-shrink: 0;

                &.angle-right {
                    display: block;
                }

                &.angle-down {
                    display: none;
                }
            }
        }
    }

    .betterdocs-summary-content {
        margin-top: 15px;

        .betterdocs-summary-loading {
            color: #666666;
            font-style: italic;
            padding: 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;

            img {
                width: 20px;
                height: 20px;
                flex-shrink: 0;
            }
        }

        .betterdocs-summary-text,
        .betterdocs-summary-preview {
            color: #555555;
            font-size: 14px;
            line-height: 1.6;

            p {
                margin: 0 0 10px 0;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .summary-content {
                p {
                    margin-bottom: 15px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .summary-error {
                color: #e74c3c;
                font-style: italic;
				font-size: 14px;
            }
        }
    }

    // Editor specific styles
    &.is-selected {
        .betterdocs-summary-header {
            outline: 2px solid #007cba;
            outline-offset: 2px;
        }
    }

    // Responsive design
    @media (max-width: 768px) {
        padding: 15px;
        margin: 15px 0;

        .betterdocs-summary-title {
            font-size: 14px;

            img {
                width: 18px;
                height: 18px;
            }

            .betterdocs-summary-arrow .angle-icon {
                width: 10px;
                height: 10px;
            }
        }

        .betterdocs-summary-content {
            .betterdocs-summary-text,
            .betterdocs-summary-preview {
                font-size: 13px;
            }
        }
    }
}
