// Article Summary Block Styles
.betterdocs-article-summary {
    background-color: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;

    .betterdocs-summary-header {
        cursor: pointer;
        user-select: none;

        &:hover {
            .betterdocs-summary-title {
                opacity: 0.8;
            }
        }
    }

    .betterdocs-summary-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: opacity 0.2s ease;

        img {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }

        .betterdocs-summary-arrow {
            margin-left: auto;

            .angle-icon {
                width: 12px;
                height: 12px;
                transition: transform 0.2s ease;
                flex-shrink: 0;

                &.angle-right {
                    display: block;
                }

                &.angle-down {
                    display: none;
                }
            }
        }
    }

    .betterdocs-summary-content {
        margin-top: 15px;

        .betterdocs-summary-loading {
            color: #666666;
            font-style: italic;
            padding: 10px 0;
            display: flex;
            align-items: center;
            text-align: left;
            font-size: 14px;
            gap: 1px;

            img {
                width: 30px;
                height: auto;
				display: inline-block;
                flex-shrink: 0;
            }

            .betterdocs-thinking-text {
                display: flex;
                align-items: baseline;
            }

            .betterdocs-thinking-dots {
                display: inline-block;
                min-width: 20px;
                text-align: left;

                &::after {
                    content: '';
                    animation: betterdocs-thinking-dots 1.5s infinite;
                }
            }
        }

        .betterdocs-summary-text,
        .betterdocs-summary-preview {
            color: #555555;
            font-size: 14px;
            line-height: 1.6;

            p {
                margin: 0 0 10px 0;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .summary-content {
                p {
					margin-top: 0;
                    margin-bottom: 15px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            .summary-error {
                color: #e74c3c;
                font-style: italic;
				font-size: 14px;
            }
        }
    }

    // Responsive design
    @media (max-width: 768px) {
        padding: 15px;
        margin: 15px 0;

        .betterdocs-summary-title {
            font-size: 14px;

            img {
                width: 18px;
                height: 18px;
            }

            .betterdocs-summary-arrow .angle-icon {
                width: 10px;
                height: 10px;
            }
        }

        .betterdocs-summary-content {
            .betterdocs-summary-text,
            .betterdocs-summary-preview {
                font-size: 13px;
            }
        }
    }
}

// Keyframes for animated thinking dots
@keyframes betterdocs-thinking-dots {
    0% { content: ''; }
    16.66% { content: '.'; }
    33.33% { content: '..'; }
    50% { content: '...'; }
    66.66% { content: '....'; }
    83.33% { content: '.....'; }
    100% { content: ''; }
}
