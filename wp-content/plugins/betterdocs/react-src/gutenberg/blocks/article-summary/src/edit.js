import { __ } from "@wordpress/i18n";
import { useBlockProps } from "@wordpress/block-editor";
import { useEffect } from "@wordpress/element";

import Inspector from "./inspector";
import Style from "./style";

const Edit = (props) => {
    const { attributes, setAttributes, clientId } = props;
    const {
        blockId,
        customTitle,
        showTitle,
        iconSize,
    } = attributes;

    // Set unique block ID
    useEffect(() => {
        if (!blockId) {
            setAttributes({ blockId: `betterdocs-article-summary-${clientId}` });
        }
    }, [clientId, blockId, setAttributes]);

    const blockProps = useBlockProps({
        className: `betterdocs-article-summary ${blockId || ''}`,
    });

    // Determine title text
    const titleText = customTitle || __("Article Summary", "betterdocs");

    return (
        <>
            <Inspector {...props} />
            <Style attributes={attributes} setAttributes={setAttributes} clientId={clientId} />

            <div {...blockProps}>
                {showTitle && (
                    <div className="betterdocs-summary-header">
                        <h3 className="betterdocs-summary-title">
                            <span>
                                <svg
                                    className="angle-icon angle-right"
                                    width={iconSize}
                                    height={iconSize}
                                    viewBox="0 0 320 512"
                                    fill="currentColor"
                                    style={{ display: "none" }}
                                >
                                    <path d="M96 96c-8.2 0-16.4 3.1-22.6 9.4c-12.5 12.5-12.5 32.8 0 45.3L205.5 256 73.4 389.3c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3l-160-160C112.4 99.1 104.2 96 96 96z" />
                                </svg>
                                <svg
                                    className="angle-icon angle-down"
                                    width={iconSize}
                                    height={iconSize}
                                    viewBox="0 0 320 512"
                                    fill="currentColor"
                                >
                                    <path d="M143 352.3L7 216.3c-9.4-9.4-9.4-24.6 0-33.9l22.6-22.6c9.4-9.4 24.6-9.4 33.9 0l96.4 96.4 96.4-96.4c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9l-136 136c-9.2 9.4-24.4 9.4-33.8 0z" />
                                </svg>
                            </span>
                            {titleText}
                        </h3>
                    </div>
                )}

                <div className="betterdocs-summary-content" style={{ display: "block" }}>
                    <div className="betterdocs-summary-preview">
                        <p>
                            {__(
                                "This is a preview of the Article Summary block. The actual AI-generated summary will appear here when viewed on the frontend.",
                                "betterdocs"
                            )}
                        </p>
                        <p>
                            {__(
                                "The summary will be automatically generated based on the article content using OpenAI technology.",
                                "betterdocs"
                            )}
                        </p>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Edit;
