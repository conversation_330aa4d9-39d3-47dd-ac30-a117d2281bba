import { __ } from '@wordpress/i18n';
import { InspectorControls } from '@wordpress/block-editor';
import { BaseControl, ButtonGroup, Button, PanelBody, TabPanel, TextareaControl, ToggleControl, SelectControl } from '@wordpress/components';

import {
    typoPrefix_code,
    typoPrefix_copyButton
} from "./typographyPrefixConstants";

import {
    CODE_SNIPPET_WRAPPER_MARGIN,
    CODE_SNIPPET_WRAPPER_PADDING,
    CODE_SNIPPET_WRAPPER_BORDER_SHADOW,
    CODE_SNIPPET_WRAPPER_BG,
    CODE_SNIPPET_COPY_BUTTON_MARGIN,
    CODE_SNIPPET_COPY_BUTTON_PADDING,
    CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW,
    CODE_SNIPPET_COPY_BUTTON_BG,
    LANGUAGE_OPTIONS,
    THEME_OPTIONS,
} from './constants';

import ResponsiveDimensionsControl from "../../../util/dimensions-control-v2";
import ColorControl from "../../../util/color-control";
import TypographyDropdown from "../../../util/typography-control-v2";
import BackgroundControl from "../../../util/background-control";
import BorderShadowControl from "../../../util/border-shadow-control";
import objAttributes from './attributes';

const Inspector = ({attributes, setAttributes}) => {
    const {
        codeContent,
        language,
        showLanguageLabel,
        showCopyButton,
        showLineNumbers,
        theme,
        codeColor,
        copyButtonColor,
        copyButtonHoverColor,
        lineNumberColor,
        lineNumberBgColor,
        resOption
    } = attributes;

    const resRequiredProps = {
        setAttributes,
        resOption,
        attributes,
        objAttributes,
    };

    return(
        <InspectorControls key="controls">
            <div className="eb-panel-control">
            <TabPanel
                className="eb-parent-tab-panel"
                activeClass="active-tab"
                tabs={[
                    {
                        name:"general",
                        title:__("General", "betterdocs"),
                        className: "eb-tab general"
                    },
                    {
                    name: "styles",
                    title: __("Styles", "betterdocs"),
                    className: "eb-tab styles",
                    }
                ]}
            >
            {
                (tab) => {
                    return(
                            <div className={"eb-tab-controls " + tab.name}>
                                {tab.name == 'general' &&
                                <>
                                <PanelBody title={__("Code Settings", "betterdocs")} initialOpen={true}>
                                    <SelectControl
                                        label={__("Language", "betterdocs")}
                                        value={language}
                                        options={LANGUAGE_OPTIONS}
                                        onChange={(value) => setAttributes({ language: value })}
                                        help={__("Select the programming language for syntax highlighting.", "betterdocs")}
                                        __next40pxDefaultSize
                                        __nextHasNoMarginBottom
                                    />

                                    <TextareaControl
                                        label={__("Code Content", "betterdocs")}
                                        value={codeContent}
                                        onChange={(value) => setAttributes({ codeContent: value })}
                                        placeholder={__("Paste or type your code here...", "betterdocs")}
                                        rows={10}
                                        help={__("Enter your code snippet. Syntax highlighting will be applied based on the selected language.", "betterdocs")}
                                    />
                                </PanelBody>

                                <PanelBody title={__("Display Options", "betterdocs")} initialOpen={false}>
                                    <ToggleControl
                                        label={__("Show Language Label", "betterdocs")}
                                        checked={showLanguageLabel}
                                        onChange={(value) => setAttributes({ showLanguageLabel: value })}
                                        help={__("Display the programming language label in the header.", "betterdocs")}
                                    />

                                    <ToggleControl
                                        label={__("Show Copy Button", "betterdocs")}
                                        checked={showCopyButton}
                                        onChange={(value) => setAttributes({ showCopyButton: value })}
                                        help={__("Display a copy-to-clipboard button in the header.", "betterdocs")}
                                    />

                                    <ToggleControl
                                        label={__("Show Line Numbers", "betterdocs")}
                                        checked={showLineNumbers}
                                        onChange={(value) => setAttributes({ showLineNumbers: value })}
                                        help={__("Display line numbers in the code block.", "betterdocs")}
                                    />

                                    <BaseControl
                                        label={__("Theme", "betterdocs")}
                                        help={__("Choose the color theme for the code block.", "betterdocs")}
                                    >
                                        <ButtonGroup>
                                            {THEME_OPTIONS.map((option) => (
                                                <Button
                                                    key={option.value}
                                                    variant={theme === option.value ? 'primary' : 'secondary'}
                                                    onClick={() => setAttributes({ theme: option.value })}
                                                >
                                                    {option.label}
                                                </Button>
                                            ))}
                                        </ButtonGroup>
                                    </BaseControl>
                                </PanelBody>
                                </>}
                                {tab.name === 'styles' && (
                                    <>
                                        <PanelBody title={__("Wrapper", "betterdocs")} initialOpen={false}>
                                            <ResponsiveDimensionsControl
                                                baseLabel={__("Margin", "betterdocs")}
                                                resRequiredProps={resRequiredProps}
                                                controlName={CODE_SNIPPET_WRAPPER_MARGIN}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__("Padding", "betterdocs")}
                                                resRequiredProps={resRequiredProps}
                                                controlName={CODE_SNIPPET_WRAPPER_PADDING}
                                            />
                                            <BackgroundControl
                                                resRequiredProps={resRequiredProps}
                                                controlName={CODE_SNIPPET_WRAPPER_BG}
                                                baseLabel={__("Background", "betterdocs")}
                                            />
                                            <BorderShadowControl
                                                resRequiredProps={resRequiredProps}
                                                controlName={CODE_SNIPPET_WRAPPER_BORDER_SHADOW}
                                                baseLabel={__("Border & Shadow", "betterdocs")}
                                            />
                                        </PanelBody>

                                        <PanelBody title={__("Code Styles", "betterdocs")} initialOpen={false}>
                                            <TypographyDropdown
                                                baseLabel={__("Typography", "betterdocs")}
                                                typographyPrefixConstant={typoPrefix_code}
                                                resRequiredProps={resRequiredProps}
                                                defaultFontSize={14}
                                            />

                                            <ColorControl
                                                label={__("Text Color", "betterdocs")}
                                                color={codeColor}
                                                onChange={(colorValue) => {
                                                    setAttributes({codeColor: colorValue})
                                                }}
                                            />

                                            {showLineNumbers && (
                                                <>
                                                    <ColorControl
                                                        label={__("Line Number Color", "betterdocs")}
                                                        color={lineNumberColor}
                                                        onChange={(colorValue) => {
                                                            setAttributes({lineNumberColor: colorValue})
                                                        }}
                                                    />

                                                    <ColorControl
                                                        label={__("Line Number Background", "betterdocs")}
                                                        color={lineNumberBgColor}
                                                        onChange={(colorValue) => {
                                                            setAttributes({lineNumberBgColor: colorValue})
                                                        }}
                                                    />
                                                </>
                                            )}
                                        </PanelBody>

                                        {showCopyButton && (
                                            <PanelBody title={__("Copy Button", "betterdocs")} initialOpen={false}>
                                                <ResponsiveDimensionsControl
                                                    baseLabel={__("Margin", "betterdocs")}
                                                    resRequiredProps={resRequiredProps}
                                                    controlName={CODE_SNIPPET_COPY_BUTTON_MARGIN}
                                                />
                                                <ResponsiveDimensionsControl
                                                    baseLabel={__("Padding", "betterdocs")}
                                                    resRequiredProps={resRequiredProps}
                                                    controlName={CODE_SNIPPET_COPY_BUTTON_PADDING}
                                                />

                                                <TypographyDropdown
                                                    baseLabel={__("Typography", "betterdocs")}
                                                    typographyPrefixConstant={typoPrefix_copyButton}
                                                    resRequiredProps={resRequiredProps}
                                                    defaultFontSize={12}
                                                />

                                                <ColorControl
                                                    label={__("Text Color", "betterdocs")}
                                                    color={copyButtonColor}
                                                    onChange={(colorValue) => {
                                                        setAttributes({copyButtonColor: colorValue})
                                                    }}
                                                />

                                                <ColorControl
                                                    label={__("Hover Text Color", "betterdocs")}
                                                    color={copyButtonHoverColor}
                                                    onChange={(colorValue) => {
                                                        setAttributes({copyButtonHoverColor: colorValue})
                                                    }}
                                                />

                                                <BackgroundControl
                                                    resRequiredProps={resRequiredProps}
                                                    controlName={CODE_SNIPPET_COPY_BUTTON_BG}
                                                    baseLabel={__("Background", "betterdocs")}
                                                />

                                                <BorderShadowControl
                                                    resRequiredProps={resRequiredProps}
                                                    controlName={CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW}
                                                    baseLabel={__("Border & Shadow", "betterdocs")}
                                                />
                                            </PanelBody>
                                        )}
                                    </>
                                )}
                            </div>
                        )
                }
            }
            </TabPanel>
            </div>
        </InspectorControls>
    );
}

export default Inspector;
