import { __ } from "@wordpress/i18n";

// Dimension control constants
export const CODE_SNIPPET_WRAPPER_MARGIN = "codeSnippetWrapperMargin";
export const CODE_SNIPPET_WRAPPER_PADDING = "codeSnippetWrapperPadding";
export const CODE_SNIPPET_WRAPPER_BORDER_SHADOW = "codeSnippetWrapperBorderShadow";
export const CODE_SNIPPET_WRAPPER_BG = "codeSnippetWrapperBG";

export const CODE_SNIPPET_COPY_BUTTON_MARGIN = "codeSnippetCopyButtonMargin";
export const CODE_SNIPPET_COPY_BUTTON_PADDING = "codeSnippetCopyButtonPadding";
export const CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW = "codeSnippetCopyButtonBorderShadow";
export const CODE_SNIPPET_COPY_BUTTON_BG = "codeSnippetCopyButtonBG";

// Language Options
export const LANGUAGE_OPTIONS = [
    { label: __("JavaScript", "betterdocs"), value: "javascript" },
    { label: __("PHP", "betterdocs"), value: "php" },
    { label: __("Python", "betterdocs"), value: "python" },
    { label: __("Java", "betterdocs"), value: "java" },
    { label: __("Ruby", "betterdocs"), value: "ruby" },
    { label: __("Bash", "betterdocs"), value: "bash" },
    { label: __("JSON", "betterdocs"), value: "json" },
    { label: __("YAML", "betterdocs"), value: "yaml" },
    { label: __("HTML", "betterdocs"), value: "html" },
    { label: __("CSS", "betterdocs"), value: "css" },
    { label: __("SQL", "betterdocs"), value: "sql" },
    { label: __("XML", "betterdocs"), value: "xml" },
    { label: __("C++", "betterdocs"), value: "cpp" },
    { label: __("C#", "betterdocs"), value: "csharp" },
    { label: __("Go", "betterdocs"), value: "go" },
    { label: __("Rust", "betterdocs"), value: "rust" },
    { label: __("Swift", "betterdocs"), value: "swift" },
    { label: __("Kotlin", "betterdocs"), value: "kotlin" },
    { label: __("TypeScript", "betterdocs"), value: "typescript" },
];

// Theme Options
export const THEME_OPTIONS = [
    { label: __("Light Theme", "betterdocs"), value: "light" },
    { label: __("Dark Theme", "betterdocs"), value: "dark" },
];

// Default Code Placeholder
export const DEFAULT_CODE_PLACEHOLDER = `// Paste or type your code here…
// Select language above to activate highlighting.

function helloWorld() {
    console.log("Hello, World!");
    return "Welcome to BetterDocs Code Snippet!";
}

helloWorld();`;
