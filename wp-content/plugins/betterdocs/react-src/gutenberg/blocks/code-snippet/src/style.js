import { useEffect } from "@wordpress/element";
import { select } from "@wordpress/data";

import {
    generateTypographyStyles,
    generateDimensionsControlStyles,
    generateBackgroundControlStyles,
    generateBorderShadowStyles,
    duplicateBlockIdFix,
    softMinifyCssStrings,
    isCssExists
} from "../../../util/helpers";

import { usePreviewDeviceType } from '../../../hooks';

import {
    typoPrefix_code,
    typoPrefix_copyButton
} from "./typographyPrefixConstants";

import {
    CODE_SNIPPET_WRAPPER_MARGIN,
    CODE_SNIPPET_WRAPPER_PADDING,
    CODE_SNIPPET_WRAPPER_BORDER_SHADOW,
    CODE_SNIPPET_WRAPPER_BG,
    CODE_SNIPPET_COPY_BUTTON_MARGIN,
    CODE_SNIPPET_COPY_BUTTON_PADDING,
    CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW,
    CODE_SNIPPET_COPY_BUTTON_BG,
} from './constants';

const Style = ({ attributes, setAttributes, clientId }) => {
    const {
        blockId,
        blockMeta,
        resOption,
        theme,
        codeColor,
        copyButtonColor,
        copyButtonHoverColor,
        lineNumberColor,
        lineNumberBgColor,
    } = attributes;

    // Generate typography styles
    const { typoStylesDesktop: codeTypoDesk, typoStylesTab: codeTypoTab, typoStylesMobile: codeTypoMobile } = generateTypographyStyles({
        attributes,
        defaultFontSize: 14,
        prefixConstant: typoPrefix_code,
    });

    const { typoStylesDesktop: copyButtonTypoDesk, typoStylesTab: copyButtonTypoTab, typoStylesMobile: copyButtonTypoMobile } = generateTypographyStyles({
        attributes,
        defaultFontSize: 12,
        prefixConstant: typoPrefix_copyButton,
    });

    // Generate dimension styles
    const { dimensionStylesDesktop: wrapperMarginDesk, dimensionStylesMobile: wrapperMarginMob, dimensionStylesTab: wrapperMarginTab } = generateDimensionsControlStyles({
        controlName: CODE_SNIPPET_WRAPPER_MARGIN,
        styleFor: 'margin',
        attributes
    });

    const { dimensionStylesDesktop: wrapperPaddingDesk, dimensionStylesMobile: wrapperPaddingMob, dimensionStylesTab: wrapperPaddingTab } = generateDimensionsControlStyles({
        controlName: CODE_SNIPPET_WRAPPER_PADDING,
        styleFor: 'padding',
        attributes
    });

    const { dimensionStylesDesktop: copyButtonMarginDesk, dimensionStylesMobile: copyButtonMarginMob, dimensionStylesTab: copyButtonMarginTab } = generateDimensionsControlStyles({
        controlName: CODE_SNIPPET_COPY_BUTTON_MARGIN,
        styleFor: 'margin',
        attributes
    });

    const { dimensionStylesDesktop: copyButtonPaddingDesk, dimensionStylesMobile: copyButtonPaddingMob, dimensionStylesTab: copyButtonPaddingTab } = generateDimensionsControlStyles({
        controlName: CODE_SNIPPET_COPY_BUTTON_PADDING,
        styleFor: 'padding',
        attributes
    });

    // Generate CSS styles
    const desktopStyles = `
        .betterdocs-code-snippet-block.${blockId} {
            ${wrapperMarginDesk}
            ${wrapperPaddingDesk}
        }

        .betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet pre code {
            ${codeTypoDesk ? codeTypoDesk.trim() : ''}
            color: ${codeColor};
        }
        .betterdocs-code-snippet-block.${blockId} .line-numbers {
            background-color: ${lineNumberBgColor};
        }

        .betterdocs-code-snippet-block.${blockId} .line-numbers .line-number {
            color: ${lineNumberColor};
        }

        .betterdocs-code-snippet-block.${blockId} .copy-button {
            ${copyButtonTypoDesk ? copyButtonTypoDesk.trim() : ''}
            ${copyButtonMarginDesk}
            ${copyButtonPaddingDesk}
            color: ${copyButtonColor};
            background: transparent;
            border: 1px solid currentColor;
            border-radius: 8px;
            padding: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .betterdocs-code-snippet-block.${blockId} .copy-button:hover {
            color: ${copyButtonHoverColor};
            border-color: ${copyButtonHoverColor};
        }

        .betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet.theme-dark pre code {
            color: #e6edf3;
        }

		.betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet.theme-dark .line-numbers {
            background-color: #161b22;
            border-right: 1px solid #30363d;
        }

        .betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet.theme-dark .line-numbers .line-number {
            color: #656d76;
        }
    `;

    const tabStyles = `
        .betterdocs-code-snippet-block.${blockId} {
            ${wrapperMarginTab}
            ${wrapperPaddingTab}
        }

        .betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet pre code {
            ${codeTypoTab ? codeTypoTab.trim() : ''}
        }

        .betterdocs-code-snippet-block.${blockId} .copy-button {
            ${copyButtonTypoTab ? copyButtonTypoTab.trim() : ''}
            ${copyButtonMarginTab}
            ${copyButtonPaddingTab}
        }
    `;

    const mobileStyles = `
        .betterdocs-code-snippet-block.${blockId} {
            ${wrapperMarginMob}
            ${wrapperPaddingMob}
        }

        .betterdocs-code-snippet-block.${blockId} .betterdocs-code-snippet pre code {
            ${codeTypoMobile ? codeTypoMobile.trim() : ''}
        }

        .betterdocs-code-snippet-block.${blockId} .copy-button {
            ${copyButtonTypoMobile ? copyButtonTypoMobile.trim() : ''}
            ${copyButtonMarginMob}
            ${copyButtonPaddingMob}
        }
    `;

    //Desktop CSS Styles
    const desktopAllStyles = softMinifyCssStrings(`
        ${isCssExists(desktopStyles) ? desktopStyles : ''}
    `);

    //Tablet CSS Styles
    const tabAllStyles = softMinifyCssStrings(`
        ${isCssExists(tabStyles) ? tabStyles : ''}
    `);

    //Mobile CSS Styles
    const mobileAllStyles = softMinifyCssStrings(`
        ${isCssExists(mobileStyles) ? mobileStyles : ''}
    `);

    //Save Styles In An Object Form On Block Meta Attribute
    useEffect(() => {
        const styleObject = {
            desktop: desktopAllStyles,
            tab: tabAllStyles,
            mobile: mobileAllStyles,
        };

        if (JSON.stringify(blockMeta) != JSON.stringify(styleObject)) {
            setAttributes({ blockMeta: styleObject });
        }
    }, [attributes]);

    /**
     * resOption: __experimentalPreviewDeviceType()
     * Set Responsive Option
     */
    usePreviewDeviceType(setAttributes);

    //Generate Unique Block ID For Every Block Instance
    useEffect(() => {
        const BLOCK_PREFIX = "betterdocs-code-snippet";
        duplicateBlockIdFix({
            BLOCK_PREFIX,
            blockId,
            setAttributes,
            select,
            clientId,
        });
    });

    return (
        <style>
            {`
                ${desktopAllStyles}

                ${resOption === 'Tablet' ? tabAllStyles : ''}
                ${resOption === 'Mobile' ? mobileAllStyles : ''}

                @media all and (max-width: 1024px) {
                    ${tabAllStyles}
                }

                @media all and (max-width: 767px) {
                    ${mobileAllStyles}
                }
            `}
        </style>
    );
};

export default Style;
