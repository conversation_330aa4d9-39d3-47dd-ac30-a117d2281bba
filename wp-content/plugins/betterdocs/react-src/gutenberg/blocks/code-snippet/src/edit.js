import { __ } from '@wordpress/i18n';
import { useBlockProps } from '@wordpress/block-editor';
import { useEffect, useRef } from '@wordpress/element';

import Inspector from './inspector';
import Style from './style';
import { DEFAULT_CODE_PLACEHOLDER } from './constants';

const Edit = (props) => {
    const { attributes, setAttributes, clientId, isSelected } = props;
    const {
        blockId,
        blockMeta,
        codeContent,
        language,
        showLanguageLabel,
        showCopyButton,
        showLineNumbers,
        theme,
        resOption
    } = attributes;

    // Set unique block ID
    useEffect(() => {
        const BLOCK_PREFIX = 'betterdocs-code-snippet';
        if (!blockId) {
            setAttributes({ blockId: BLOCK_PREFIX + '-' + clientId.substr(0, 8) });
        }
    }, []);

    // Set block meta
    useEffect(() => {
        setAttributes({
            blockMeta: {
                desktop: resOption === 'Desktop' ? true : false,
                tab: resOption === 'Tablet' ? true : false,
                mobile: resOption === 'Mobile' ? true : false,
            },
        });
    }, [resOption]);

    const blockRef = useRef(null);

    const blockProps = useBlockProps({
        className: `betterdocs-code-snippet-block ${blockId}`,
        ref: blockRef
    });

    // Initialize tooltip functionality for block editor
    useEffect(() => {
        if (!blockRef.current) return;

        const copyButton = blockRef.current.querySelector('.copy-button');
        const tooltip = blockRef.current.querySelector('.betterdocs-code-snippet-tooltip');

        if (!copyButton || !tooltip) return;

        // Position tooltip function
        const positionTooltip = () => {
            const buttonRect = copyButton.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();

            // Position above the button, centered
            const left = buttonRect.left + (buttonRect.width / 2) - (tooltipRect.width / 2);
            const top = buttonRect.top - tooltipRect.height - 8;

            tooltip.style.left = Math.max(8, left) + 'px';
            tooltip.style.top = Math.max(8, top) + 'px';
        };

        const showTooltip = () => {
            tooltip.classList.add('show');
            positionTooltip();
        };

        const hideTooltip = () => tooltip.classList.remove('show');

        // Copy button click handler for block editor
        const handleCopyClick = () => {
            const originalText = tooltip.textContent;
            tooltip.textContent = 'Copied!';

            // Reposition tooltip after text change
            if (tooltip.classList.contains('show')) {
                positionTooltip();
            }

            setTimeout(() => {
                tooltip.textContent = originalText;
                // Reposition tooltip back to original size
                if (tooltip.classList.contains('show')) {
                    positionTooltip();
                }
            }, 2000);
        };

        copyButton.addEventListener('mouseenter', showTooltip);
        copyButton.addEventListener('mouseleave', hideTooltip);
        copyButton.addEventListener('blur', hideTooltip);
        copyButton.addEventListener('click', handleCopyClick);

        // Cleanup
        return () => {
            copyButton.removeEventListener('mouseenter', showTooltip);
            copyButton.removeEventListener('mouseleave', hideTooltip);
            copyButton.removeEventListener('blur', hideTooltip);
            copyButton.removeEventListener('click', handleCopyClick);
        };
    }, [showCopyButton, showLanguageLabel]);

    const displayContent = codeContent || DEFAULT_CODE_PLACEHOLDER;

    // Show header if either language label or copy button is enabled
    const showHeader = showLanguageLabel || showCopyButton;

    return (
        <>
            {isSelected && <Inspector {...props} />}
            <Style {...props} />
            <div {...blockProps}>
                <div className={`betterdocs-code-snippet theme-${theme}`}>
                    {showHeader && (
                        <div className="code-snippet-header">
                            {showLanguageLabel && (
                                <span className="language-label">{language.toUpperCase()}</span>
                            )}
                            {showCopyButton && (
                                <div className="betterdocs-code-snippet-copy-container">
                                    <button className="copy-button" aria-label={__('Copy to clipboard', 'betterdocs')}>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" fill="currentColor"/>
                                        </svg>
                                    </button>
                                    <div className="betterdocs-code-snippet-tooltip">{__('Copy to clipboard', 'betterdocs')}</div>
                                </div>
                            )}
                        </div>
                    )}
                    <div className="code-snippet-content">
                        {showLineNumbers && (
                            <div className="line-numbers">
                                {displayContent.split('\n').map((_, index) => (
                                    <span key={index} className="line-number">
                                        {index + 1}
                                    </span>
                                ))}
                            </div>
                        )}
                        <pre className={`language-${language}`}>
                            <code>{displayContent}</code>
                        </pre>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Edit;
