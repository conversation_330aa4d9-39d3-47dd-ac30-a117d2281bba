/**
 * BetterDocs Code Snippet Block Editor Styles
 */

.betterdocs-code-snippet-block {
    .betterdocs-code-snippet {
        position: relative;
        border-radius: 6px;
        overflow: hidden;
        background: #f8f9fa;
        border: 1px solid #e9ecef;

        &.theme-dark {
            background: #0d1117;
            border-color: #30363d;

            .code-snippet-header {
                background: #161b22;
                border-bottom-color: #30363d;

                .copy-button {
                    color: #a0aec0;
                    border-color: #a0aec0;

                    &:hover {
                        color: #e2e8f0;
                        border-color: #e2e8f0;
                    }
                }
            }

            .language-label {
                color: #8b949e;
            }

            pre code {
                background: #0d1117;
                color: #e6edf3;
            }

            .line-numbers {
                background: #161b22;
                border-right: 1px solid #30363d;

                .line-number {
                    color: #656d76;
                }
            }
        }

        .code-snippet-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 16px;
            background: #f1f3f4;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;

            .language-label {
                font-weight: 600;
                color: #6c757d;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .copy-button {
                background: transparent;
                color: #6c757d;
                border: 1px solid currentColor;
                border-radius: 8px;
                padding: 8px;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;

                svg {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .code-snippet-content {
            display: flex;
            position: relative;

            .line-numbers {
                background: #f8f9fa;
                border-right: 1px solid #e9ecef;
                padding: 16px 8px;
                min-width: 40px;
                text-align: right;
                user-select: none;
                flex-shrink: 0;

                .line-number {
                    display: block;
                    line-height: 1.5;
                    font-size: 12px;
                    color: #6c757d;
                    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                }
            }

            pre {
                flex: 1;
                margin: 0;
                padding: 16px;
                overflow-x: auto;
                background: transparent;
                font-size: 14px;
                line-height: 1.5;

                code {
                    background: transparent;
                    padding: 0;
                    border: none;
                    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                    color: #212529;
                    white-space: pre;
                    word-wrap: normal;
                    word-break: normal;
                    word-spacing: normal;
                    tab-size: 4;
                }
            }
        }
    }

    /* Tooltip Styles for Block Editor */
    .betterdocs-code-snippet-tooltip {
        position: fixed;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.2;
        white-space: nowrap;
        border-radius: 6px;
        pointer-events: none;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;

        /* Light theme tooltip */
        background-color: #f6f8fa;
        color: #24292f;
        border: 1px solid #6c757d;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

        /* Show tooltip on hover */
        &.show {
            opacity: 1;
            visibility: visible;
        }
    }

    /* Dark theme tooltip for block editor */
    .betterdocs-code-snippet.theme-dark .betterdocs-code-snippet-tooltip {
        background-color: #24292f;
        color: #f6f8fa;
        border-color: #a0aec0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    /* Copy button container for tooltip positioning */
    .betterdocs-code-snippet-copy-container {
        position: relative;
        display: inline-block;
    }

    // Responsive adjustments
    @media (max-width: 768px) {
        .betterdocs-code-snippet {
            .code-snippet-header {
                padding: 6px 12px;
                font-size: 11px;
            }

            .code-snippet-content {
                .line-numbers {
                    padding: 12px 6px;
                    min-width: 35px;
                }

                pre {
                    padding: 12px;
                    font-size: 13px;
                }
            }
        }
    }
}
