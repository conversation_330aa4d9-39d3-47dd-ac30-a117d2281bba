import { __ } from "@wordpress/i18n";

import {
    CODE_SNIPPET_WRAPPER_MARGIN,
    CODE_SNIPPET_WRAPPER_PADDING,
    CODE_SNIPPET_WRAPPER_BORDER_SHADOW,
    CODE_SNIPPET_WRAPPER_BG,
    CODE_SNIPPET_COPY_BUTTON_MARGIN,
    CODE_SNIPPET_COPY_BUTTON_PADDING,
    CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW,
    CODE_SNIPPET_COPY_BUTTON_BG,
} from './constants';

import {
    generateTypographyAttributes,
    generateDimensionsAttributes,
    generateBackgroundAttributes,
    generateBorderShadowAttributes,
} from '../../../util/helpers';

import * as prefixes from './typographyPrefixConstants';

const attributes = {
    // Required attributes for responsive options and asset generation
    resOption: {
        type: 'string',
        default: 'Desktop',
    },
    blockId: {
        type: 'string',
    },
    blockRoot: {
        type: 'string',
        default: 'betterdocs_code_snippet',
    },
    blockMeta: {
        type: 'object',
    },

    // Content attributes
    codeContent: {
        type: "string",
        default: "",
    },
    language: {
        type: "string",
        default: "javascript",
    },
    showLanguageLabel: {
        type: "boolean",
        default: true,
    },
    showCopyButton: {
        type: "boolean",
        default: true,
    },
    showLineNumbers: {
        type: "boolean",
        default: false,
    },
    theme: {
        type: "string",
        default: "light",
    },

    // Typography attributes
    ...generateTypographyAttributes(Object.values(prefixes)),

    // Color attributes
    codeColor: {
        type: "string",
        default: "#333333",
    },
    copyButtonColor: {
        type: "string",
        default: null,
    },
    copyButtonHoverColor: {
        type: "string",
        default: null,
    },
    lineNumberColor: {
        type: "string",
        default: "#999999",
    },
    lineNumberBgColor: {
        type: "string",
        default: "#f5f5f5",
    },

    // Dimension attributes with null defaults for responsive consistency
    ...generateDimensionsAttributes(CODE_SNIPPET_WRAPPER_MARGIN),
    ...generateDimensionsAttributes(CODE_SNIPPET_WRAPPER_PADDING),
    ...generateBackgroundAttributes(CODE_SNIPPET_WRAPPER_BG),
    ...generateBorderShadowAttributes(CODE_SNIPPET_WRAPPER_BORDER_SHADOW),

    ...generateDimensionsAttributes(CODE_SNIPPET_COPY_BUTTON_MARGIN, {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    }),
    ...generateDimensionsAttributes(CODE_SNIPPET_COPY_BUTTON_PADDING, {
        top: 8,
        right: 12,
        bottom: 8,
        left: 12
    }),
    ...generateBackgroundAttributes(CODE_SNIPPET_COPY_BUTTON_BG),
    ...generateBorderShadowAttributes(CODE_SNIPPET_COPY_BUTTON_BORDER_SHADOW),
};

export default attributes;
