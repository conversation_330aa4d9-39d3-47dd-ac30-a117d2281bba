@import "../common";

/**Article Summary**/
.betterdocs-article-summary {
    margin: 20px 0;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;

    .betterdocs-summary-header {
        cursor: pointer;
        padding: 15px 20px;
        background: #ffffff;
        border-bottom: 1px solid #e1e5e9;
        transition: background-color 0.2s ease;

        &:hover {
            background: #f8f9fa;
        }

        .betterdocs-summary-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;

            .angle-icon {
                width: 14px;
                height: 14px;
                transition: transform 0.2s ease;
                flex-shrink: 0;

                &.angle-right {
                    display: block;
                }

                &.angle-down {
                    display: none;
                }
            }
        }
    }

    .betterdocs-summary-content {
        padding: 20px;
        background: #ffffff;

        .betterdocs-summary-loading {
            color: #7f8c8d;
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        .betterdocs-summary-text {
            .summary-content {
                line-height: 1.6;
                color: #2c3e50;

                p {
                    margin-bottom: 12px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                ul, ol {
                    margin: 12px 0;
                    padding-left: 20px;

                    li {
                        margin-bottom: 6px;
                    }
                }
            }

            .summary-error {
                color: #e74c3c;
                font-style: italic;
                text-align: center;
                padding: 10px;
                background: #fdf2f2;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                margin: 0;
				font-size: 14px;
            }
        }
    }

    @include responsive-below("tablet") {
        margin: 15px 0;

        .betterdocs-summary-header {
            padding: 12px 15px;

            .betterdocs-summary-title {
                font-size: 15px;
            }
        }

        .betterdocs-summary-content {
            padding: 15px;
        }
    }
}
