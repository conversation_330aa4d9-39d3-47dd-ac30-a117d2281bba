/* BetterDocs Code Snippet Frontend Styles */

.betterdocs-code-snippet-wrapper {
    position: relative;
    margin: 1.5rem 0;
    border-radius: 8px;
    overflow: hidden;
    font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', '<PERSON><PERSON><PERSON>', 'Courier New', monospace;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Light Theme */
    &.theme-light {
        background-color: #ffffff;
        border: 1px solid #e1e5e9;

        .betterdocs-code-snippet-header {
            background-color: #f6f8fa;
            border-bottom: 1px solid #e1e5e9;
        }

        .betterdocs-code-snippet-language {
            color: #586069;
        }

        .betterdocs-code-snippet-code {
            background-color: #ffffff;
            color: #24292e;
        }

        .betterdocs-code-snippet-line-numbers {
            background-color: #f6f8fa;
            color: #586069;
            border-right: 1px solid #e1e5e9;
        }

        .betterdocs-code-snippet-copy-button {
            background: transparent;
            color: #6c757d;
            border: 1px solid currentColor;
            border-radius: 8px;
            padding: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;

            svg {
                width: 16px;
                height: 16px;
            }
        }
    }

    /* Dark Theme */
    &.theme-dark {
        background-color: #0d1117;
        border: 1px solid #30363d;

        .betterdocs-code-snippet-header {
            background-color: #161b22;
            border-bottom: 1px solid #30363d;
        }

        .betterdocs-code-snippet-language {
            color: #8b949e;
        }

        .betterdocs-code-snippet-code {
            background-color: #0d1117;
            color: #e6edf3;
        }

        .betterdocs-code-snippet-line-numbers {
            background-color: #161b22;
            color: #656d76;
            border-right: 1px solid #30363d;
        }

        .betterdocs-code-snippet-copy-button {
            background: transparent;
            color: #a0aec0;
            border: 1px solid currentColor;
            border-radius: 8px;
            padding: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;

            svg {
                width: 16px;
                height: 16px;
            }

            &:hover {
                color: #e2e8f0;
                border-color: #e2e8f0;
            }

            &:active {
                color: #cbd5e0;
                border-color: #cbd5e0;
            }

			&:focus {
				outline: none;
			}
        }
    }
}

.betterdocs-code-snippet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    min-height: 3rem;
}

.betterdocs-code-snippet-language {
    font-family: inherit;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
    font-size: 0.8125rem;
}

.betterdocs-code-snippet-copy-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.875rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;

    &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:active {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    &:focus {
        outline: none;
    }

    svg {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
    }
}

.betterdocs-code-snippet-content {
    display: flex;
    position: relative;
    overflow: hidden;
}

.betterdocs-code-snippet-line-numbers {
    display: flex;
    flex-direction: column;
    padding: 1rem 0.75rem;
    font-size: 0.8125rem;
    line-height: 1.5;
    text-align: right;
    user-select: none;
    min-width: 3rem;
    flex-shrink: 0;

    .line-number {
        height: 1.5em;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-variant-numeric: tabular-nums;
    }
}

.betterdocs-code-snippet-code {
    flex: 1;
    margin: 0;
    padding: 1rem;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: pre;
    overflow-x: auto;
    background: transparent;
    border: none;
    min-height: 3rem;

    code {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        background: transparent;
        padding: 0;
        border: none;
        display: block;
        white-space: pre;
    }

    /* Custom scrollbar for webkit browsers */
    &::-webkit-scrollbar {
        height: 8px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4px;

        &:hover {
            background: rgba(0, 0, 0, 0.5);
        }
    }
}

/* Tooltip Styles */
.betterdocs-code-snippet-tooltip {
    position: fixed;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    border-radius: 6px;
    pointer-events: none;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;

    /* Light theme tooltip */
    background-color: #f6f8fa;
    color: #24292f;
    border: 1px solid #6c757d;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    /* Show tooltip on hover */
    &.show {
        opacity: 1;
        visibility: visible;
    }
}

/* Dark theme tooltip */
.betterdocs-code-snippet-wrapper.theme-dark .betterdocs-code-snippet-tooltip {
    background-color: #24292f;
    color: #f6f8fa;
    border-color: #a0aec0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    &::after {
        border-top-color: #24292f;
    }
}

/* Copy button container for tooltip positioning */
.betterdocs-code-snippet-copy-container {
    position: relative;
    display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .betterdocs-code-snippet-wrapper {
        margin: 1rem 0;
        border-radius: 6px;
    }

    .betterdocs-code-snippet-header {
        padding: 0.625rem 0.75rem;
        font-size: 0.8125rem;
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
        min-height: auto;
    }

    .betterdocs-code-snippet-copy-button {
        padding: 0.375rem 0.625rem;
        font-size: 0.8125rem;
        align-self: flex-end;

        svg {
            width: 14px;
            height: 14px;
        }
    }

    .betterdocs-code-snippet-line-numbers {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
        min-width: 2.5rem;
    }

    .betterdocs-code-snippet-code {
        padding: 0.75rem;
        font-size: 0.8125rem;
    }
}

@media (max-width: 480px) {
    .betterdocs-code-snippet-wrapper {
        margin: 0.75rem 0;
        border-radius: 4px;
    }

    .betterdocs-code-snippet-header {
        padding: 0.5rem;
    }

    .betterdocs-code-snippet-copy-button {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        gap: 0.25rem;

        svg {
            width: 12px;
            height: 12px;
        }
    }

    .betterdocs-code-snippet-line-numbers {
        padding: 0.5rem 0.375rem;
        font-size: 0.6875rem;
        min-width: 2rem;
    }

    .betterdocs-code-snippet-code {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .betterdocs-code-snippet-wrapper {
        border-width: 2px;

        &.theme-light {
            border-color: #000000;

            .betterdocs-code-snippet-header {
                border-bottom-color: #000000;
            }

            .betterdocs-code-snippet-line-numbers {
                border-right-color: #000000;
            }
        }

        &.theme-dark {
            border-color: #ffffff;

            .betterdocs-code-snippet-header {
                border-bottom-color: #ffffff;
            }

            .betterdocs-code-snippet-line-numbers {
                border-right-color: #ffffff;
            }
        }
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .betterdocs-code-snippet-wrapper,
    .betterdocs-code-snippet-copy-button,
    .betterdocs-code-snippet-tooltip {
        transition: none;
    }
}

/* Print styles */
@media print {
    .betterdocs-code-snippet-wrapper {
        box-shadow: none;
        border: 1px solid #000000;
        break-inside: avoid;
    }

    .betterdocs-code-snippet-header {
        background-color: #f5f5f5 !important;
        border-bottom: 1px solid #000000;
    }

    .betterdocs-code-snippet-copy-button {
        display: none;
    }

    .betterdocs-code-snippet-code {
        background-color: #ffffff !important;
        color: #000000 !important;
        overflow: visible;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .betterdocs-code-snippet-line-numbers {
        background-color: #f5f5f5 !important;
        color: #666666 !important;
        border-right: 1px solid #000000;
    }
}
