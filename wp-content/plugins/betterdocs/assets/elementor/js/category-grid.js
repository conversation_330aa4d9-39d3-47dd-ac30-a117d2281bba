(()=>{"use strict";var e={81101:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=e=>(0,r.createElement)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},(0,r.createElement)("mask",{id:"mask0_8026_2609",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"25",height:"24"},(0,r.createElement)("rect",{x:"0.5",width:"24",height:"24",fill:"#D9D9D9"})),(0,r.createElement)("g",{mask:"url(#mask0_8026_2609)"},(0,r.createElement)("path",{d:"M6.9 19L5.5 17.6L11.1 12L5.5 6.4L6.9 5L12.5 10.6L18.1 5L19.5 6.4L13.9 12L19.5 17.6L18.1 19L12.5 13.4L6.9 19Z",fill:"#667085"})))},80079:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=()=>(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"21",height:"20",viewBox:"0 0 21 20",fill:"none"},(0,r.createElement)("g",{clipPath:"url(#clip0_7196_26074)"},(0,r.createElement)("mask",{id:"mask0_7196_26074",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"21",height:"20"},(0,r.createElement)("rect",{x:"0.5",width:"20",height:"20",fill:"#C4C4C4"})),(0,r.createElement)("g",{mask:"url(#mask0_7196_26074)"},(0,r.createElement)("path",{d:"M7.99967 15.0013H12.9997C13.2358 15.0013 13.4337 14.9214 13.5934 14.7617C13.7531 14.602 13.833 14.4041 13.833 14.168C13.833 13.9319 13.7531 13.7339 13.5934 13.5742C13.4337 13.4145 13.2358 13.3346 12.9997 13.3346H7.99967C7.76356 13.3346 7.56565 13.4145 7.40592 13.5742C7.2462 13.7339 7.16634 13.9319 7.16634 14.168C7.16634 14.4041 7.2462 14.602 7.40592 14.7617C7.56565 14.9214 7.76356 15.0013 7.99967 15.0013ZM7.99967 11.668H12.9997C13.2358 11.668 13.4337 11.5881 13.5934 11.4284C13.7531 11.2687 13.833 11.0707 13.833 10.8346C13.833 10.5985 13.7531 10.4006 13.5934 10.2409C13.4337 10.0812 13.2358 10.0013 12.9997 10.0013H7.99967C7.76356 10.0013 7.56565 10.0812 7.40592 10.2409C7.2462 10.4006 7.16634 10.5985 7.16634 10.8346C7.16634 11.0707 7.2462 11.2687 7.40592 11.4284C7.56565 11.5881 7.76356 11.668 7.99967 11.668ZM5.49967 18.3346C5.04134 18.3346 4.64898 18.1714 4.32259 17.8451C3.9962 17.5187 3.83301 17.1263 3.83301 16.668V3.33464C3.83301 2.8763 3.9962 2.48394 4.32259 2.15755C4.64898 1.83116 5.04134 1.66797 5.49967 1.66797H11.4788C11.7011 1.66797 11.9129 1.70964 12.1143 1.79297C12.3156 1.8763 12.4927 1.99436 12.6455 2.14714L16.6872 6.1888C16.84 6.34158 16.958 6.51866 17.0413 6.72005C17.1247 6.92144 17.1663 7.13325 17.1663 7.35547V16.668C17.1663 17.1263 17.0031 17.5187 16.6768 17.8451C16.3504 18.1714 15.958 18.3346 15.4997 18.3346H5.49967ZM11.333 6.66797C11.333 6.90408 11.4129 7.102 11.5726 7.26172C11.7323 7.42144 11.9302 7.5013 12.1663 7.5013H15.4997L11.333 3.33464V6.66797Z",fill:"#D0D5DD"}))),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_7196_26074"},(0,r.createElement)("rect",{x:"0.5",width:"20",height:"20",rx:"6.4",fill:"white"}))))},27488:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=({content:e="",wordLimit:t})=>{const[a,c]=(0,r.useState)(!1),n=(l=a?e:((e,t)=>{if(!e)return"";const a=e.split(/\s+/);return a.slice(0,t).join(" ")+(a.length>t?"...":"")})(e,t),l?l.replace(/<\/?p[^>]*>/g,""):""),s=((o=e)?o.trim().split(/\s+/).length:0)>t;var l,o;return(0,r.createElement)("div",{className:"content-sub"},(0,r.createElement)("p",null,(0,r.createElement)("span",{dangerouslySetInnerHTML:{__html:n}}),(0,r.createElement)("span",{onClick:()=>{c(!a)},className:"show-more-btn",style:{cursor:"pointer"}},s&&!a&&"Show More",a&&"Show Less")))}},70580:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=()=>(0,r.createElement)("span",null,(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"21",height:"20",viewBox:"0 0 21 20",fill:"none"},(0,r.createElement)("mask",{id:"mask0_7196_26080",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"21",height:"20"},(0,r.createElement)("rect",{x:"0.5",width:"20",height:"20",fill:"#D9D9D9"})),(0,r.createElement)("g",{mask:"url(#mask0_7196_26080)"},(0,r.createElement)("path",{d:"M12.1667 12.4993C12.4029 12.4993 12.6077 12.4125 12.7813 12.2389C12.9549 12.0653 13.0417 11.8605 13.0417 11.6243C13.0417 11.3882 12.9549 11.1834 12.7813 11.0098C12.6077 10.8362 12.4029 10.7493 12.1667 10.7493C11.9306 10.7493 11.7258 10.8362 11.5522 11.0098C11.3786 11.1834 11.2917 11.3882 11.2917 11.6243C11.2917 11.8605 11.3786 12.0653 11.5522 12.2389C11.7258 12.4125 11.9306 12.4993 12.1667 12.4993ZM12.1667 9.83268C12.3195 9.83268 12.4619 9.77713 12.5938 9.66602C12.7258 9.5549 12.8056 9.40907 12.8334 9.22852C12.8612 9.06185 12.9202 8.90907 13.0105 8.77018C13.1008 8.63129 13.264 8.44379 13.5001 8.20768C13.9167 7.79102 14.1945 7.45421 14.3334 7.19727C14.4723 6.94032 14.5417 6.63824 14.5417 6.29102C14.5417 5.66602 14.323 5.1556 13.8855 4.75977C13.448 4.36393 12.8751 4.16602 12.1667 4.16602C11.7084 4.16602 11.2917 4.27018 10.9167 4.47852C10.5417 4.68685 10.2431 4.98546 10.0209 5.37435C9.93758 5.51324 9.93064 5.65907 10.0001 5.81185C10.0695 5.96463 10.1876 6.07574 10.3542 6.14518C10.507 6.21463 10.6563 6.22157 10.8022 6.16602C10.948 6.11046 11.0695 6.01324 11.1667 5.87435C11.2917 5.69379 11.4376 5.55838 11.6042 5.4681C11.7709 5.37782 11.9584 5.33268 12.1667 5.33268C12.5001 5.33268 12.7709 5.42643 12.9792 5.61393C13.1876 5.80143 13.2917 6.0549 13.2917 6.37435C13.2917 6.56879 13.2362 6.75282 13.1251 6.92643C13.014 7.10004 12.8195 7.31879 12.5417 7.58268C12.139 7.9299 11.882 8.19727 11.7709 8.38477C11.6598 8.57227 11.5904 8.84657 11.5626 9.20768C11.5487 9.37435 11.6008 9.52018 11.7188 9.64518C11.8369 9.77018 11.9862 9.83268 12.1667 9.83268ZM7.16675 14.9993C6.70841 14.9993 6.31605 14.8362 5.98966 14.5098C5.66328 14.1834 5.50008 13.791 5.50008 13.3327V3.33268C5.50008 2.87435 5.66328 2.48199 5.98966 2.1556C6.31605 1.82921 6.70841 1.66602 7.16675 1.66602H17.1667C17.6251 1.66602 18.0174 1.82921 18.3438 2.1556C18.6702 2.48199 18.8334 2.87435 18.8334 3.33268V13.3327C18.8334 13.791 18.6702 14.1834 18.3438 14.5098C18.0174 14.8362 17.6251 14.9993 17.1667 14.9993H7.16675ZM3.83341 18.3327C3.37508 18.3327 2.98272 18.1695 2.65633 17.8431C2.32994 17.5167 2.16675 17.1243 2.16675 16.666V5.83268C2.16675 5.59657 2.24661 5.39865 2.40633 5.23893C2.56605 5.07921 2.76397 4.99935 3.00008 4.99935C3.23619 4.99935 3.43411 5.07921 3.59383 5.23893C3.75355 5.39865 3.83341 5.59657 3.83341 5.83268V16.666H14.6667C14.9029 16.666 15.1008 16.7459 15.2605 16.9056C15.4202 17.0653 15.5001 17.2632 15.5001 17.4993C15.5001 17.7355 15.4202 17.9334 15.2605 18.0931C15.1008 18.2528 14.9029 18.3327 14.6667 18.3327H3.83341Z",fill:"#D0D5DD"}))))},94084:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=()=>(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none"},(0,r.createElement)("mask",{id:"mask0_7196_26007",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"17",height:"16"},(0,r.createElement)("rect",{x:"0.5",width:"16",height:"16",fill:"#D0D5DD"})),(0,r.createElement)("g",{mask:"url(#mask0_7196_26007)"},(0,r.createElement)("path",{d:"M3.1661 13.3327C2.79943 13.3327 2.48554 13.2021 2.22443 12.941C1.96332 12.6799 1.83276 12.366 1.83276 11.9993V3.99935C1.83276 3.63268 1.96332 3.31879 2.22443 3.05768C2.48554 2.79657 2.79943 2.66602 3.1661 2.66602H6.6161C6.79387 2.66602 6.96332 2.69935 7.12443 2.76602C7.28554 2.83268 7.42721 2.92713 7.54943 3.04935L8.49943 3.99935H13.8328C14.1994 3.99935 14.5133 4.1299 14.7744 4.39102C15.0355 4.65213 15.1661 4.96602 15.1661 5.33268V11.9993C15.1661 12.366 15.0355 12.6799 14.7744 12.941C14.5133 13.2021 14.1994 13.3327 13.8328 13.3327H3.1661Z",fill:"#D0D5DD"})))},6679:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=e=>{const{placeholder:t,heading:a,subheading:c,headingTag:n="h2",subheadingTag:s="h3",buttonText:l,searchButton:o}=e;return(0,r.createElement)("div",{className:"betterdocs-search-layout-1"},(a||c)&&(0,r.createElement)("div",{className:"search-header"},a&&(0,r.createElement)(n,{className:"search-heading"},a),c&&(0,r.createElement)(s,{className:"search-subheading"},c)),(0,r.createElement)("div",{className:"search-bar",onClick:e.handleSearchFieldClick},(0,r.createElement)("div",{className:"search-input-wrapper"},(0,r.createElement)("svg",{className:"search-icon",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_7075_27802)"},(0,r.createElement)("path",{d:"M14.4631 13.6407L17.8394 17.0162L16.724 18.1317L13.3485 14.7554C12.0925 15.7622 10.5303 16.3098 8.92061 16.3075C5.00435 16.3075 1.82593 13.1291 1.82593 9.21285C1.82593 5.29658 5.00435 2.11816 8.92061 2.11816C12.8369 2.11816 16.0153 5.29658 16.0153 9.21285C16.0176 10.8226 15.47 12.3848 14.4631 13.6407ZM12.8818 13.0558C13.8823 12.027 14.441 10.6479 14.4387 9.21285C14.4387 6.16371 11.969 3.69476 8.92061 3.69476C5.87147 3.69476 3.40252 6.16371 3.40252 9.21285C3.40252 12.2612 5.87147 14.7309 8.92061 14.7309C10.3557 14.7332 11.7347 14.1745 12.7636 13.174L12.8818 13.0558Z",fill:"#98A2B3"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_7075_27802"},(0,r.createElement)("rect",{width:"18.9192",height:"18.9192",fill:"white",transform:"translate(0.248535 0.540039)"})))),t&&(0,r.createElement)("span",{className:"search-input"},t)),l&&o&&(0,r.createElement)("span",{className:"search-button"},l)))}},99895:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=({placeholder:e})=>(0,r.createElement)("div",{className:"betterdocs-live-search betterdocs-sidebar-search betterdocs-search-popup"},(0,r.createElement)("form",{className:"betterdocs-searchform betterdocs-advance-searchform",onSubmit:e=>e.preventDefault()},(0,r.createElement)("div",{className:"betterdocs-searchform-input-wrap"},(0,r.createElement)("svg",{className:"doc-search-icon",width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_7196_25183)"},(0,r.createElement)("path",{d:"M19.0266 17.8469L22.5958 21.4152L21.4166 22.5943L17.8483 19.0252C16.5206 20.0895 14.8691 20.6684 13.1675 20.666C9.02748 20.666 5.66748 17.306 5.66748 13.166C5.66748 9.02602 9.02748 5.66602 13.1675 5.66602C17.3075 5.66602 20.6675 9.02602 20.6675 13.166C20.6699 14.8677 20.091 16.5191 19.0266 17.8469ZM17.355 17.2285C18.4126 16.1409 19.0032 14.683 19.0008 13.166C19.0008 9.94268 16.39 7.33268 13.1675 7.33268C9.94415 7.33268 7.33415 9.94268 7.33415 13.166C7.33415 16.3885 9.94415 18.9993 13.1675 18.9993C14.6845 19.0017 16.1424 18.4111 17.23 17.3535L17.355 17.2285Z",fill:"#98A2B3"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_7196_25183"},(0,r.createElement)("rect",{width:"20",height:"20",fill:"white",transform:"translate(4 4)"})))),e&&(0,r.createElement)("span",{className:"betterdocs-search-command"},e)),(0,r.createElement)("span",{className:"command-key"},"⌘ K")))},62055:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=()=>(0,r.createElement)("svg",{className:"doc-search-icon",width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#clip0_7196_25183)"},(0,r.createElement)("path",{d:"M19.0266 17.8469L22.5958 21.4152L21.4166 22.5943L17.8483 19.0252C16.5206 20.0895 14.8691 20.6684 13.1675 20.666C9.02748 20.666 5.66748 17.306 5.66748 13.166C5.66748 9.02602 9.02748 5.66602 13.1675 5.66602C17.3075 5.66602 20.6675 9.02602 20.6675 13.166C20.6699 14.8677 20.091 16.5191 19.0266 17.8469ZM17.355 17.2285C18.4126 16.1409 19.0032 14.683 19.0008 13.166C19.0008 9.94268 16.39 7.33268 13.1675 7.33268C9.94415 7.33268 7.33415 9.94268 7.33415 13.166C7.33415 16.3885 9.94415 18.9993 13.1675 18.9993C14.6845 19.0017 16.1424 18.4111 17.23 17.3535L17.355 17.2285Z",fill:"#98A2B3"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"clip0_7196_25183"},(0,r.createElement)("rect",{width:"20",height:"20",fill:"white",transform:"translate(4 4)"}))))},68066:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=()=>(0,r.createElement)("svg",{width:"217",height:"216",viewBox:"0 0 217 216",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"mask0_7201_2527",style:{maskType:"luminance"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"217",height:"216"},(0,r.createElement)("path",{d:"M216.333 0H0.687012V215.646H216.333V0Z",fill:"white"})),(0,r.createElement)("g",{mask:"url(#mask0_7201_2527)"},(0,r.createElement)("path",{d:"M108.51 193.362C156.149 193.362 194.768 154.743 194.768 107.104C194.768 59.4649 156.149 20.8457 108.51 20.8457C60.8709 20.8457 22.2517 59.4649 22.2517 107.104C22.2517 154.743 60.8709 193.362 108.51 193.362Z",fill:"#F2F4F7"}),(0,r.createElement)("path",{d:"M76.1632 45.6855H142.68C149.033 45.6855 154.182 50.8344 154.182 57.1866V145.622C154.182 151.974 149.033 157.123 142.68 157.123H76.1632C69.811 157.123 64.6621 151.974 64.6621 145.622V57.1866C64.6621 50.8344 69.811 45.6855 76.1632 45.6855Z",fill:"url(#paint0_linear_7201_2527)"}),(0,r.createElement)("path",{d:"M76.0913 60.0625H104.988C106.933 60.0625 108.51 61.6396 108.51 63.5847C108.51 65.5298 106.933 67.1069 104.988 67.1069H76.0913C74.1462 67.1069 72.5691 65.5298 72.5691 63.5847C72.5691 61.6396 74.1462 60.0625 76.0913 60.0625Z",fill:"#344054"}),(0,r.createElement)("path",{d:"M76.0913 80.0449H140.929C142.874 80.0449 144.451 81.622 144.451 83.5671C144.451 85.5122 142.874 87.0893 140.929 87.0893H76.0913C74.1462 87.0893 72.5691 85.5122 72.5691 83.5671C72.5691 81.622 74.1462 80.0449 76.0913 80.0449Z",fill:"#DCEAE9"}),(0,r.createElement)("path",{d:"M76.0913 100.027H140.929C142.874 100.027 144.451 101.604 144.451 103.55C144.451 105.495 142.874 107.072 140.929 107.072H76.0913C74.1462 107.072 72.5691 105.495 72.5691 103.55C72.5691 101.604 74.1462 100.027 76.0913 100.027Z",fill:"#DCEAE9"}),(0,r.createElement)("path",{d:"M76.0913 120.01H140.929C142.874 120.01 144.451 121.587 144.451 123.532C144.451 125.477 142.874 127.054 140.929 127.054H76.0913C74.1462 127.054 72.5691 125.477 72.5691 123.532C72.5691 121.587 74.1462 120.01 76.0913 120.01Z",fill:"#DCEAE9"}),(0,r.createElement)("path",{d:"M76.0913 139.994H140.929C142.874 139.994 144.451 141.571 144.451 143.516C144.451 145.461 142.874 147.039 140.929 147.039H76.0913C74.1462 147.039 72.5691 145.461 72.5691 143.516C72.5691 141.571 74.1462 139.994 76.0913 139.994Z",fill:"#DCEAE9"}),(0,r.createElement)("path",{d:"M207.815 33.3711H164.628C162.616 33.3711 160.984 35.0876 160.984 37.206V58.5685C160.984 60.6862 162.616 62.4034 164.628 62.4034H207.815C209.828 62.4034 211.459 60.6862 211.459 58.5685V37.206C211.459 35.0876 209.828 33.3711 207.815 33.3711Z",fill:"white"}),(0,r.createElement)("path",{d:"M171.766 52.4735C174.148 52.4735 176.079 50.5427 176.079 48.1606C176.079 45.7784 174.148 43.8477 171.766 43.8477C169.384 43.8477 167.453 45.7784 167.453 48.1606C167.453 50.5427 169.384 52.4735 171.766 52.4735Z",fill:"#C5D8D3"}),(0,r.createElement)("path",{d:"M186.142 43.8477H200.519C202.901 43.8477 204.832 45.7784 204.832 48.1606C204.832 50.5427 202.901 52.4735 200.519 52.4735H186.142C183.76 52.4735 181.83 50.5427 181.83 48.1606C181.83 45.7784 183.76 43.8477 186.142 43.8477Z",fill:"#DCEAE9"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M133.747 162.139C143.005 162.139 151.609 159.339 158.759 154.539L188.014 182.466L198.624 170.156L170.394 143.208C175.608 135.87 178.674 126.9 178.674 117.213C178.674 92.4018 158.559 72.2871 133.747 72.2871C108.935 72.2871 88.8213 92.4018 88.8213 117.213C88.8213 142.025 108.935 162.139 133.747 162.139ZM172.067 117.213C172.067 138.22 155.037 155.249 134.031 155.249C113.025 155.249 95.9965 138.22 95.9965 117.213C95.9965 96.2072 113.025 79.1784 134.031 79.1784C155.037 79.1784 172.067 96.2072 172.067 117.213Z",fill:"#90B8B1"}),(0,r.createElement)("path",{d:"M134.028 155.982C155.664 155.982 173.203 138.603 173.203 117.166C173.203 95.7285 155.664 78.3496 134.028 78.3496C112.391 78.3496 94.8523 95.7285 94.8523 117.166C94.8523 138.603 112.391 155.982 134.028 155.982Z",fill:"white",fillOpacity:"0.3"}),(0,r.createElement)("path",{d:"M140.801 117.167L150.282 107.724C151.17 106.789 151.657 105.538 151.657 104.227C151.657 102.042 150.311 100.118 148.433 99.3831C146.556 98.6487 144.403 99.2656 143.166 100.842L134.034 112.34L124.902 100.842C123.665 99.2656 121.512 98.6487 119.635 99.3831C117.758 100.118 116.412 102.042 116.412 104.227C116.412 105.538 116.899 106.789 117.787 107.724L127.268 117.167L117.787 126.609C116.278 128.228 116.278 130.856 117.787 132.474C119.295 134.093 121.767 134.093 123.276 132.474L134.034 120.68L144.793 132.474C145.547 133.312 146.562 133.73 147.578 133.73C148.593 133.73 149.608 133.312 150.362 132.474C151.871 130.856 151.871 128.228 150.362 126.609L140.801 117.167Z",fill:"#344054"}),(0,r.createElement)("defs",null,(0,r.createElement)("linearGradient",{id:"paint0_linear_7201_2527",x1:"64.6621",y1:"57.6866",x2:"155.982",y2:"57.6866",gradientUnits:"userSpaceOnUse"},(0,r.createElement)("stop",{stopColor:"white"}),(0,r.createElement)("stop",{offset:"1",stopColor:"#DCEAE9"})))))},84593:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(51609),c=a(1455),n=a.n(c),s=a(6679),l=a(99895),o=a(31296);const i=e=>{const[t,a]=(0,r.useState)(""),[c,i]=(0,r.useState)(!1),[d,m]=(0,r.useState)([]),[h,u]=(0,r.useState)([]),[p,C]=(0,r.useState)([]),{layout:g,placeholder:b,heading:f,subheading:E,headingTag:_,subheadingTag:w,buttonText:v,numberofdocs:y,numberoffaqs:k,doc_ids:A,doc_categories_ids:x,faq_categories_ids:L,searchButton:M,enable_docs_search:D,enable_faq_search:N,topWrapperRef:S=null}=e,H=(0,r.useRef)(null);(0,r.useEffect)((()=>{const e=e=>{"Escape"===e.key&&i(!1),"k"===e.key&&(e.metaKey||e.ctrlKey)&&i(!0)};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[]),(0,r.useEffect)((()=>{(async()=>{try{const e="betterdocs/v1/",t="get-terms?taxonomy=doc_category",a=await n()({path:`${e}${t}`});m(a)}catch(e){console.error("Error fetching doc categories:",e)}})()}),[]),(0,r.useEffect)((()=>{(async()=>{try{const e=await n()({path:`betterdocs/v1/search?initial=true&per_page=${y}${A?`&doc_ids=${A}`:""}${x?`&doc_categories_ids=${x}`:""}`});u(e);const t=await n()({path:`betterdocs/v1/search?initial=true&post_type=betterdocs_faq&per_page=${k}${L?`&faq_categories_ids=${L}`:""}`});C(t)}catch(e){console.error("Error fetching initial results:",e)}})()}),[y,k,A,x,L]);const Z=()=>{i(!0)},q=()=>{i(!1)},$=e=>{H.current&&!H.current.contains(e.target)&&q()};return(0,r.useEffect)((()=>(c?document.addEventListener("click",$):document.removeEventListener("click",$),()=>{document.removeEventListener("click",$)})),[c]),(0,r.createElement)(r.Fragment,null,"betterdocs-search-modal-sidebar"===g&&(0,r.createElement)("div",{onClick:Z},(0,r.createElement)(l.A,{placeholder:b})),"betterdocs-search-modal-layout-1"===g&&(0,r.createElement)(s.A,{handleSearchFieldClick:Z,placeholder:b,heading:f,subheading:E,headingTag:_,subheadingTag:w,buttonText:v,searchButton:M}),c&&(0,r.createElement)("div",{ref:H},(0,r.createElement)(o.A,{topWrapperRef:S,initialSearchQuery:t,closeModal:q,docCategories:d,popularDocs:h,recentFaqs:p,placeholder:b,enableFaqSearch:N,enableDocsSearch:D})))}},31296:(e,t,a)=>{a.d(t,{A:()=>g});var r=a(51609),c=a(52619),n=a(27723),s=a(94084),l=a(80079),o=a(62055),i=a(70580),d=a(68066),m=a(81101),h=a(27488),u=a(22068),p=a(1455),C=a.n(p);const g=({initialSearchQuery:e,closeModal:t,docCategories:a,popularDocs:p,recentFaqs:g,placeholder:b,enableFaqSearch:f,enableDocsSearch:E,topWrapperRef:_=null})=>{const[w,v]=(0,r.useState)(e||""),[y,k]=(0,r.useState)(""),[A,x]=(0,r.useState)([]),[L,M]=(0,r.useState)("docs"),[D,N]=(0,r.useState)(!1),S=(0,r.useRef)(null),H=(0,r.useRef)({}),Z=e=>{const t=document.createElement("textarea");return t.innerHTML=e,t.value},q=(0,r.useCallback)((()=>{let e;return(...t)=>{e&&clearTimeout(e),e=setTimeout((()=>(async(e,t=!1)=>{const a=`search-insert?s=${e}${t?"&no_result=1":""}`;try{await C()({path:`betterdocs/v1/${a}`})}catch(e){console.error("Error storing search keyword:",e)}})(...t)),300)}})(),[]),$=(0,r.useCallback)((async(e,t,a)=>{try{const r="betterdocs/v1/";let c=`${betterdocsSearchModalConfig.rest_url}${r}search?s=${encodeURIComponent(e)}`;if("docs"===a&&t&&(c+=`&doc_category=${encodeURIComponent(t)}`),H.current[c])x(H.current[c]);else{const t=await C()({path:`${r}search?s=${encodeURIComponent(e)}`,headers:{"X-WP-Nonce":searchModalConfig?.nonce}});H.current[c]=t,x(t),q(e,0===t.length)}}catch(e){console.error("Error fetching search results:",e)}}),[q]);(0,r.useEffect)((()=>{w.length>=betterdocsSearchModalConfig.search_letter_limit||y?(N(!0),$(w,y,L)):(N(!1),x("docs"===L?p:g))}),[w,y,L,$,p,g]);const T=e=>A.filter((t=>t.post_type===e)),F=T("betterdocs_faq").length>0,B=[E?{label:(0,n.__)("Docs","betterdocs"),type:"docs",icon:(0,r.createElement)(l.A,null)}:{},...F&&f?[{label:(0,n.__)("FAQ","betterdocs"),type:"betterdocs_faq",icon:(0,r.createElement)(i.A,null)}]:[]];return(0,r.useEffect)((()=>{S.current&&S.current.focus()}),[]),(0,r.useEffect)((()=>{null==E&&null==f||!E&&!f?M(""):E&&null!=E?f&&null!=f||M("docs"):M("betterdocs_faq")}),[]),(0,r.createElement)("div",{className:"betterdocs-modal-overlay",onClick:()=>{t()}},(0,r.createElement)("div",{className:"betterdocs-search-wrapper",style:{display:"block"}},(0,r.createElement)("div",{className:"betterdocs-search-details",onClick:e=>{e.stopPropagation()}},(0,r.createElement)("div",{className:"xmark"},(0,r.createElement)(u.A,{onClick:t,width:"20",height:"20",fill:"#475467"})),(0,r.createElement)("div",{className:"betterdocs-search-header"},(0,r.createElement)(o.A,null),(0,r.createElement)("div",{className:"betterdocs-searchform-input-wrap"},(0,r.createElement)("input",{type:"text",className:"betterdocs-search-field","aria-label":(0,n.__)("Search Input","betterdocs"),placeholder:b,value:w,onChange:e=>v(e.target.value),ref:S}),w?(0,r.createElement)(m.A,{className:"clear-icon",onClick:e=>{e.stopPropagation(),v(""),N(!1),S.current.focus()}}):(0,r.createElement)("span",{className:"esc-button"},(0,n.__)("ESC","betterdocs"))),betterdocsSearchModalConfig.advance_search&&a.length>0&&"docs"===L&&(0,c.applyFilters)("betterdocs_search_modal_category_select",[],[y,e=>{S.current&&S.current.value.trim()&&k(e?e.value:"")},a,_])),betterdocsSearchModalConfig.advance_search&&(0,c.applyFilters)("betterdocs_search_modal_popular_keyword",[],[v,N,y,L,$,_]),(0,r.createElement)("div",{className:"betterdocs-search-content"},(0,r.createElement)("div",{className:"betterdocs-search-info-tab betterdocs-search-tabs"},B.map((e=>{if(Object.keys(e)?.length>0)return(0,r.createElement)("div",{key:e.type,className:"betterdocs-tab-items "+(L===e.type?"active":""),onClick:()=>M(e.type)},e.icon&&(0,r.createElement)("span",{className:"tab-icon"},e.icon),(0,r.createElement)("span",null,e.label))}))),(0,r.createElement)("div",{className:"betterdocs-search-items-wrapper betterdocs-search-tab-content-wrapper"},T(L).length>0?T(L).map(((e,t)=>(0,r.createElement)("div",{className:"betterdocs-search-item-content",key:t},(0,r.createElement)("div",{className:"betterdocs-search-item-list"},(0,r.createElement)("div",{className:"content-main"},(0,r.createElement)(l.A,null),e.permalink&&"docs"===e.post_type?(0,r.createElement)("a",{className:"hasperma",href:e.permalink},(0,r.createElement)("h4",null,Z(e.title))):(0,r.createElement)("h4",null,Z(e.title))),"betterdocs_faq"===e.post_type&&(0,r.createElement)(h.A,{content:e.content,wordLimit:30}),"docs"===e.post_type&&(0,r.createElement)("div",{className:"content-sub"},(0,r.createElement)(s.A,null),(0,r.createElement)("h5",null,e.taxonomies&&e.taxonomies.length>0?Z(e.taxonomies):"No taxonomy")))))):D&&(0,r.createElement)("div",{className:"betterdocs-search-items-not-found"},(0,r.createElement)(d.A,null),(0,r.createElement)("h4",null,"docs"===L?(0,n.__)("No Docs found for","betterdocs"):(0,n.__)("No FAQ found for","betterdocs"),' "',w,'"')))))))}},22068:(e,t,a)=>{a.d(t,{A:()=>c});var r=a(51609);const c=e=>(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",width:e.width||"24",height:e.height||"24",fill:e.fill||"currentColor",...e},(0,r.createElement)("path",{d:"M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"}))},51609:e=>{e.exports=window.React},1455:e=>{e.exports=window.wp.apiFetch},52619:e=>{e.exports=window.wp.hooks},27723:e=>{e.exports=window.wp.i18n}},t={};function a(r){var c=t[r];if(void 0!==c)return c.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r=a(51609);function c(e,t){let a=t(".betterdocs-category-grid-inner-wrapper.masonry",e);if(0!=a?.length){var r=window.matchMedia("(max-width: 767px)"),c=window.matchMedia("(max-width: 1024px)");a.each(((e,t)=>{var a;let n=0,s=0;switch(!0){case r.matches:n=t.getAttribute("data-column_mobile"),s=t.getAttribute("data-column_space_mobile");break;case c.matches:n=t.getAttribute("data-column_tab"),s=t.getAttribute("data-column_space_tab");break;default:n=t.getAttribute("data-column_desktop"),s=null!==(a=t.getAttribute("data-column_space_desktop"))&&void 0!==a?a:15}s=parseInt(s),n=parseInt(n);let l=t.querySelectorAll(".betterdocs-single-category-wrapper"),o=(n-1)*s;t&&(l.forEach((e=>{e.style.width=`calc((100% - ${o}px) / ${n})`})),new Masonry(t,{itemSelector:".betterdocs-single-category-wrapper",percentPosition:!0,gutter:s}))}))}else(t(".betterdocs-single-category-wrapper",e)||[]).each(((e,t)=>{t.removeAttribute("style")}))}function n(e,t){1!=betterdocsCategoryGridConfig?.is_betterdocs_templates&&(t(".betterdocs-category-grid-inner-wrapper .betterdocs-single-category-wrapper .betterdocs-category-link-btn").on("click",(function(e){let a=t(t(t(t(this)?.parent())?.parent())?.parent())?.parent(),r=t(a).attr("data-mkb-slug");if(r?.length>0){var c=new Date,n=c.setDate(c.getDate()+30).toString();document.cookie=`last_knowledge_base=${r}; expires=${n}; path=/;`}})),t(".betterdocs-category-grid-inner-wrapper .betterdocs-single-category-wrapper .betterdocs-body .betterdocs-articles-list li a").on("click",(function(e){let a=t(t(t(t(t(t(this).parent()).parent()).parent()).parent()).parent()).parent(),r=t(a).attr("data-mkb-slug");if(r?.length>0){var c=new Date,n=c.setDate(c.getDate()+30).toString();document.cookie=`last_knowledge_base=${r}; expires=${n}; path=/;`}})))}const s=window.ReactDOM;var l=a.n(s),o=a(84593);jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/betterdocs-category-grid.default",c),elementorFrontend.hooks.addAction("frontend/element_ready/betterdocs-category-grid.default",n),elementorFrontend.hooks.addAction("frontend/element_ready/betterdocs-sidebar.default",(()=>{const e=document.querySelectorAll("[id=betterdocs-search-modal]");if(e)for(let t of e){const e=t?.className,a={placeholder:t.getAttribute("data-placeholder"),heading:t.getAttribute("data-heading"),subheading:t.getAttribute("data-subheading"),headingTag:t.getAttribute("data-headingtag"),subheadingTag:t.getAttribute("data-subheadingtag"),buttonText:t.getAttribute("data-buttontext"),numberofdocs:t.getAttribute("data-numberofdocs"),numberoffaqs:t.getAttribute("data-numberoffaqs"),doc_ids:t.getAttribute("data-doc_ids"),doc_categories_ids:t.getAttribute("data-doc_categories_ids"),faq_categories_ids:t.getAttribute("data-faq_categories_ids")};l().render((0,r.createElement)(o.A,{layout:e,...a}),t)}})),elementorFrontend.hooks.addAction("frontend/element_ready/betterdocs-search-form.default",(()=>{const e=document.querySelectorAll("[id=betterdocs-search-modal]");if(e)for(let t of e){const e=t?.className,a={placeholder:t.getAttribute("data-placeholder"),heading:t.getAttribute("data-heading"),subheading:t.getAttribute("data-subheading"),headingTag:t.getAttribute("data-headingtag"),subheadingTag:t.getAttribute("data-subheadingtag"),buttonText:t.getAttribute("data-buttontext"),numberofdocs:t.getAttribute("data-numberofdocs"),numberoffaqs:t.getAttribute("data-numberoffaqs"),docterms:t.getAttribute("data-docterms"),faqterms:t.getAttribute("data-faqterms"),searchButton:t.getAttribute("data-searchbutton"),doc_ids:t.getAttribute("data-doc_ids"),doc_categories_ids:t.getAttribute("data-doc_categories_ids"),faq_categories_ids:t.getAttribute("data-faq_categories_ids")};l().render((0,r.createElement)(o.A,{layout:e,...a}),t)}}))}))})();