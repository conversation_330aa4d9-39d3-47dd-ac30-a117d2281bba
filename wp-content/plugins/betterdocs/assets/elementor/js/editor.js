jQuery(window).on("elementor/frontend/init",(function(){var e=!1;elementorFrontend.isEditMode()&&(e=!0),e&&parent.document.addEventListener("mousedown",(function(e){var t=parent.document.querySelectorAll(".elementor-element--promotion");if(t.length>0)for(var o=0;o<t.length;o++)if(t[o].contains(e.target)){var n=parent.document.querySelector("#elementor-element--promotion__dialog");if(t[o].querySelector(".icon > i").classList.toString().indexOf("betterdocs-icon")>=0){var r=n.querySelectorAll(".dialog-buttons-action");if(r.length>0)for(o=0;o<r.length;o++)r[o].style.display="none";if(e.stopImmediatePropagation(),null===n.querySelector(".betterdocs-dialog-buttons-action")){var l=document.createElement("a"),a=document.createTextNode("Upgrade BetterDocs");l.setAttribute("href","https://betterdocs.co/upgrade"),l.setAttribute("target","_blank"),l.classList.add("dialog-button","dialog-action","dialog-buttons-action","elementor-button","elementor-button-success","betterdocs-dialog-buttons-action"),l.appendChild(a),n.querySelector(".dialog-buttons-action").insertAdjacentHTML("afterend",l.outerHTML)}else n.querySelector(".betterdocs-dialog-buttons-action").style.display=""}else n.querySelector(".dialog-buttons-action").style.display="",null!==n.querySelector(".betterdocs-dialog-buttons-action")&&(n.querySelector(".betterdocs-dialog-buttons-action").style.display="none");break}}))}));