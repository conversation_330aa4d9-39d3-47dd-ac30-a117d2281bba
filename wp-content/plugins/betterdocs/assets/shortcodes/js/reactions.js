jQuery(document).ready((function(e){e(".betterdocs-feelings,.betterdocs-emoji").on("click",(function(t){var c;t.preventDefault(),console.log("clicked");let r=t.currentTarget.dataset.feelings,o=null!==(c=betterdocsReactionsConfig)&&void 0!==c?c:void 0;if(null!=o&&null!=o.FEEDBACK&&null!=o.FEEDBACK.DISPLAY&&1==o.FEEDBACK.DISPLAY){var s=o.FEEDBACK.URL;s.indexOf("?")>-1?s+="/"+o.post_id+"&feelings="+r:s+="/"+o.post_id+"?feelings="+r,jQuery.ajax({url:s,method:"POST",success:function(t){!0===t&&(e(".betterdocs-article-reactions .betterdocs-article-reactions-heading,.betterdocs-article-reactions .betterdocs-article-reaction-links,.layout-3.betterdocs-article-reactions h5,.layout-3.betterdocs-article-reactions .betterdocs-article-reaction-links").fadeOut(1e3),e(".betterdocs-article-reactions.layout-1, .betterdocs-article-reactions.layout-2 .betterdocs-article-reactions-box,.layout-3.betterdocs-article-reactions .betterdocs-article-reactions-sidebar, .betterdocs-article-reactions-blocks .betterdocs-article-reactions-sidebar .betterdocs-article-reaction-links, .betterdocs-blocks .betterdocs-article-reactions-box, .betterdocs-blocks.betterdocs-article-reactions").html('<p class="feedback-message">'+o.FEEDBACK.SUCCESS+"</p>").fadeIn(1e3))}})}}))}));