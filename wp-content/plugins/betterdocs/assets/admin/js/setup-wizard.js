(()=>{"use strict";jQuery(document).ready((function(e){function t(t){return e(t).is(":checked")}function n(e){var t=jQuery(".betterdocs-tabnav-wrap ul.tab-nav li.nav-item"),n=localStorage.getItem("betterdocsQswTabNumberTracking");return n=parseInt(n)?parseInt(n):0,parseInt(n)<t.length?(n+=e,localStorage.setItem("betterdocsQswTabNumberTracking",n)):parseInt(n)>=t.length&&localStorage.setItem("betterdocsQswTabNumberTracking",t.length-1),parseInt(n)}function r(){var e=localStorage.getItem("betterdocsQswTabNumberTracking"),t=jQuery(".betterdocs-tabnav-wrap ul.tab-nav li.nav-item");return parseInt(e)>=t.length&&localStorage.setItem("betterdocsQswTabNumberTracking",t.length-1),e?parseInt(e):0}jQuery(".betterdocs-setup-wizard").length&&function(){jQuery("#betterdocs-prev-option").on("click",(function(e){e.preventDefault(),c(-1),n(-1)})),jQuery("#betterdocs-next-option").on("click",(function(t){t.preventDefault(),c(1),0===r()&&function(){if(e("#betterdocs_user_email_address").is(":checked")){var t={nonce:e('.betterdocs-setup-wizard input[name="betterdocsqswnonce"]').val(),action:"optin_wizard_action_betterdocs",admin_email:e("#betterdocs_user_email_address").val()};jQuery.post(ajaxurl,t,(function(t){t.hasOwnProperty("success")&&t.success&&e(".notice-betterdocs").remove()}))}}(),n(1)})),jQuery("#betterdocsqswemailskipbutton").on("click",(function(e){e.preventDefault(),c(1),n(1)}));var o=r();function a(e){var t=jQuery(".tab-content");for(let n=0;n<=t.length;n++)n===e?jQuery(t[n]).addClass("active"):jQuery(t[n]).removeClass("active");if(0==e?(document.getElementById("betterdocs-prev-option").style.display="none",jQuery(".bottom-notice-left").show(),jQuery("#betterdocsqswemailskipbutton").show()):(document.getElementById("betterdocs-prev-option").style.display="inline",jQuery(".bottom-notice-left").hide(),jQuery("#betterdocsqswemailskipbutton").hide()),e==t.length-1)return document.getElementById("betterdocs-next-option").innerHTML=bdquicksetup.finish_txt,!1;document.getElementById("betterdocs-next-option").innerHTML=bdquicksetup.next_txt}function s(e){var t=jQuery(".nav-item");for(let n=0;n<=t.length;n++)n<=e?jQuery(t[n]).addClass("tab-active"):jQuery(t[n]).hasClass("tab-active")&&jQuery(t[n]).removeClass("tab-active")}function c(r){var c=document.getElementsByClassName("tab-content");(o+=r)<c.length?(a(o),s(o)):(function(){var n=e('.betterdocs-setup-wizard input[name="betterdocsqswnonce"]').val(),r=t('.betterdocs-setup-wizard input[name="builtin_doc_page"]'),o=t('.betterdocs-setup-wizard input[name="enable_disable"]'),a=e('.betterdocs-setup-wizard input[name="docs_slug"]').val(),s="";t(".betterdocs-setup-wizard .activate_plugin")&&(s=e(".betterdocs-setup-wizard .activate_plugin").val());var c={action:"better_docs_quick_setup_wizard_data_save",security:n,activate_plugin:s,builtin_doc_page:r,docs_slug:a,enable_disable:o};jQuery.post(ajaxurl,c,(function(e){}))}(),swal({title:"Good job!",text:"Setup is Complete.",icon:"success",timer:2e3}).then((function(){location=location.search,location.search.replace("betterdocs-setup","betterdocs-settings"),location=location.origin+location.pathname+location.search.replace("betterdocs-setup","betterdocs-settings")+"&saved=true"})),o=c.length-1,localStorage.getItem("betterdocsQswTabNumberTracking")<=5?n(-2):n(-1))}a(o),s(o)}(),e(".betterdocs-pro-feature-checkbox label").on("click",(function(){var t=document.createElement("p"),n=document.createElement("a");n.setAttribute("href","https://betterdocs.co/"),n.innerText="Premium",n.style.color="red";var r=e(this).find(".nx-pro-label");r.hasClass("has-to-update")&&(n.innerText="Latest Pro v"+r.text().toString().replace(/[ >=<]/g,"")),t.innerHTML="You need to upgrade to the <strong>"+n.outerHTML+" </strong> Version to use this module.",swal({title:"Opps...",content:t,icon:"warning",buttons:[!1,"Close"],dangerMode:!0})})),jQuery("#betterdocs_user_email_address").on("click",(function(t){!0===jQuery(this).prop("checked")?e(this).removeClass("invalid"):e(this).addClass("invalid")})),jQuery(".btn-collect").on("click",(function(){jQuery("p.whatwecollecttext").toggle()})),jQuery('input[name="docs_slug"]').on("change",(function(){var e={action:"better_docs_setup_generate_live_url",docs_slug:jQuery(this).val()};jQuery.post(ajaxurl,e,(function(e){var t=JSON.parse(e);jQuery("#bdgotocustomize").attr("href",t.customizerurl),jQuery("#bdgotodocspage").attr("href",t.siteurl)}))}))}))})();