jQuery(document).ready((function(e){function t(e=null,t=null,o=!1,r=".doc-category-image-wrapper",a=".doc-category-image-id"){if(null===e)return document.querySelectorAll(r).forEach((e=>{let t=e.querySelector(".custom_media_image"),o=e.querySelector("img");t&&t.remove(),o&&(o.style.display="block")})),void document.querySelectorAll(a).forEach((e=>{e.value=""}));const n=e.target.closest(".form-field"),i=n.querySelector(r),c=n.querySelector(a);if(o)c.value="",i.querySelector(".custom_media_image")?.remove(),i.querySelector("img").style.display="block";else{var l;let e=i.querySelector(".custom_media_image");if(e){var d;e.src=null!==(d=t?.url)&&void 0!==d?d:""}else{var s;const e=document.createElement("img");e.src=null!==(s=t?.url)&&void 0!==s?s:"",e.style.margin="0px",e.style.padding="0px",e.style.maxHeight="100px",e.style.float="none",e.classList.add("custom_media_image"),i.querySelector("img").style.display="none",i.append(e)}c.value=null!==(l=t?.id)&&void 0!==l?l:""}}var o,r,a;o=!0,r=wp.media.editor.send.attachment,e("body").on("click",".betterdocs_tax_media_button.button",(function(a){let n="#"+e(this).attr("id"),i=e(n);return o=!0,wp.media.frame?.state()?.get("selection")?.reset(),wp.media.editor.send.attachment=function(e,i){if(!o)return r.apply(n,[e,i]);t(a,i)},wp.media.editor.open(i),!1})),e("body").on("click",".doc_tax_media_remove",(e=>t(e,null,!0))),e(document).ajaxComplete((function(o,r,a){var n=a.data.split("&");if(-1!==e.inArray("action=add-tag",n)){var i=r.responseXML;$response=e(i).find("term_id").text(),""!=$response&&t()}})),null!=window?.betterdocsCategorySorting&&function(e,t=".taxonomy-doc_category"){if(void 0===e)return;var o=jQuery;let r=parseInt(e.paged),a=parseInt(o("#"+e.per_page_id).val());const n=r>0?(r-1)*a:0,i=document.querySelector(t+" #the-list");null==i||i.querySelector("tr:first-child").classList.contains("no-items")||o(i).sortable({placeholder:"betterdocs-drag-drop-item-placeholder",axis:"y",start:function(e,t){const r=o(t.item[0]);o(".betterdocs-drag-drop-item-placeholder").css("height",r.css("height"))},update:function(t,r){const a=[];o(i).find("tr.ui-sortable-handle").each((function(){const e=o(this),t={term_id:e.attr("id").replace("tag-",""),order:parseInt(e.index())+1};a.push(t)}));const c={action:e.action,data:a,base_index:n,nonce:e.nonce};o.ajax({type:"POST",url:e.ajaxurl,data:c,dataType:"JSON",success:function(e){},error:function(e){console.error("Ordering Error: ",e)}})}})}(window.betterdocsCategorySorting,null!==(a=window?.betterdocsCategorySorting?.selector)&&void 0!==a?a:".taxonomy-doc_category")}));