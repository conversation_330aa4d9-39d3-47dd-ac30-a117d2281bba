(()=>{class e{constructor(e){this.config=e,this.initialize(),this.init()}init(){this.settingsTab(),this.sortable(),this.copyToClipboard(),this.enableDarkMode(),this.generateSampleData()}initialize(){var e=jQuery;this.body=e("body"),this.droppableUl=e(".betterdocs-single-listing ul"),this.copyBtn=e(".betterdocs-settings-input-text span"),this.darkModeBtn=e("#betterdocs-mode-toggle"),this.settingsMenu=e(".betterdocs-settings-menu li")}settingsTab(){this.settingsMenu.on("click",(function(e){var t=jQuery,a=t(this).data("tab");t(this).addClass("active").siblings().removeClass("active"),t("#betterdocs-"+a).addClass("active").siblings().removeClass("active")}))}get_query_vars(e){var t={};return window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi,((e,a,o)=>t[a]=o)),""!==e?t[e]:t}enableDarkMode(){var e=this;this.darkModeBtn.prop("checked",Boolean(this.config.dark_mode)),this.darkModeBtn.on("click",(function(t){e.body.toggleClass("betterdocs-dark-mode"),jQuery.ajax({type:"POST",url:e.config.ajaxurl,data:{action:"betterdocs_dark_mode",mode:t.currentTarget.checked?1:0,nonce:e.config.doc_cat_order_nonce},dataType:"JSON",success:function(t){0==t?.success&&e.body.toggleClass("betterdocs-dark-mode")}})}))}permalinkStructure(){var e=$("#multiple_kb"),t=$("#permalink_structure"),a=t.val();a&&!e.is(":checked")&&t.val(a.replace(/%knowledge_base%\/?/g,""))}copyToClipboard(){var e=this;this.copyBtn.on("click",(function(t){var a=this.previousSibling;a.select(),document.execCommand("copy"),this.firstChild.textContent=e.config.text,a.setSelectionRange(0,99999)}))}setView(){}sortable(){var e=this;this.droppableUl.each(((t,a)=>{var o=jQuery(a),s=o.data("category_id");o.hasClass("docs-droppable"),o.sortable({connectWith:"ul.docs-droppable",placeholder:"betterdocs-post-list-placeholder",start:function(e,t){const a=jQuery(t.item[0]);jQuery(".betterdocs-post-list-placeholder").css("height",a.css("height"))},receive:function(t,a){const o=a.item;if(o.siblings(".betterdocs-no-docs").remove(),null!=s){const t={action:"update_docs_term",object_id:o.data("id"),prev_term_id:a.sender.data("category_id"),list_term_id:s,doc_cat_order_nonce:e.config.doc_cat_order_nonce};jQuery.ajax({type:"POST",url:e.config.ajaxurl,data:t,dataType:"JSON",success:function(e){}})}},update:function(t,a){const i=[];if(o.find("li.ui-sortable-handle").each((function(){const e=jQuery(this);i.push(e.data("id"))})),null!=s){const t={action:"update_doc_order_by_category",docs_ordering_data:i,list_term_id:s,doc_cat_order_nonce:e.config.doc_cat_order_nonce};jQuery.ajax({type:"POST",url:e.config.ajaxurl,data:t,dataType:"JSON",success:function(e){}})}}})}))}generateSampleData(){let e=jQuery(".generate-sample-data"),t=this?.config?.generate_data_url,a=this?.config?.nonce;e.on("click",(function(o){o.preventDefault(),e.text("Generating..."),jQuery?.ajax({type:"POST",url:t,data:{action:"create-dummy-data",_wpnonce:a},dataType:"JSON",success:function(t){"success"==t?.status&&(e?.text("Generated Successfully"),window.location.reload())}})}))}}!function(){"use strict";new e(window?.betterdocs_admin)}(jQuery)})();