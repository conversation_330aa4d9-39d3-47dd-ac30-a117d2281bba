!function(t){"use strict";t(document).ready((function(){t(".customize-control-betterdocs-multi-dimension").each((function(){let n=t(this).find(".betterdocs-dimension-control"),e=t(this).find(".betterdocs-dimension-input-1"),i=t(this).find(".betterdocs-dimension-input-2"),s=t(this).find(".betterdocs-dimension-input-3"),o=t(this).find(".betterdocs-dimension-input-4"),d={input1:e.val(),input2:i.val(),input3:s.val(),input4:o.val()};n.val(JSON.stringify(d)).change(),t(".betterdocs-dimension-disconnected").on("click",(function(){var n=t(this).data("element-connect");t(this).parent().parent(".betterdocs-dimension-fields").find("input").addClass("connected").removeClass("disconnected").attr("data-element-connect",n),t(this).parent(".betterdocs-dimension-link").addClass("connected")})),t(".betterdocs-dimension-connected").on("click",(function(){t(this).parent().parent(".betterdocs-dimension-fields").find("input").removeClass("connected").addClass("disconnected").attr("data-element-connect",""),t(this).parent(".betterdocs-dimension-link").removeClass("connected")})),t(document).on("input",".betterdocs-dimension-input.connected",(function(){var n=t(this).attr("data-element-connect"),e=t(this).val();let i=t(this).parents(".customize-control-betterdocs-multi-dimension").find(".betterdocs-dimension-control"),s=JSON.parse(i.val());t(this).parent().parent(".betterdocs-dimension-fields").find('.connected[ data-element-connect="'+n+'" ]').each((function(n,o){t(this).val(e).change();let d=t(this).attr("data-input");s[d]=void 0===t(this).val()||""===t(this).val()?"0":t(this).val(),i.val(JSON.stringify(s)).change()}))})),t(document).on("input",".betterdocs-dimension-input.disconnected",(function(){let n=t(this).parents(".customize-control-betterdocs-multi-dimension").find(".betterdocs-dimension-control"),e=JSON.parse(n.val());e[t(this).attr("data-input")]=void 0===t(this).val()||""===t(this).val()?"0":t(this).val(),n.val(JSON.stringify(e)).change()}))}));var n=t(".customize-control-betterdocs-multi-dimension"),e={};for(var i of n)e[t(i).attr("id")]=t(i).children("input").val();t(".betterdocs-customizer-reset.betterdocs-multi-dimension").on("click",(function(n){var i=e[t(this).parent().parent().attr("id")],s=JSON.parse(e[t(this).parent().parent().attr("id")]).input1,o=JSON.parse(e[t(this).parent().parent().attr("id")]).input2,d=JSON.parse(e[t(this).parent().parent().attr("id")]).input3,a=JSON.parse(e[t(this).parent().parent().attr("id")]).input4;t(this).parent().next().next().val(i).change(),t(this).parent().next().next().next().find(".betterdocs-dimension-input-1").val(s),t(this).parent().next().next().next().find(".betterdocs-dimension-input-2").val(o),t(this).parent().next().next().next().find(".betterdocs-dimension-input-3").val(d),t(this).parent().next().next().next().find(".betterdocs-dimension-input-4").val(a)}))}))}(jQuery);