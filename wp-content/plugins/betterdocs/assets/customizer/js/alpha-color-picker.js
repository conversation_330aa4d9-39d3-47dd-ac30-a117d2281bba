(()=>{function i(i){var t;return(i=i.replace(/ /g,"")).match(/rgba\(\d+\,\d+\,\d+\,([^\)]+)\)/)?(t=100*parseFloat(i.match(/rgba\(\d+\,\d+\,\d+\,([^\)]+)\)/)[1]).toFixed(2),t=parseInt(t)):t=100,t}function t(i,t,r,n){var o,e,c;o=t.data("a8cIris"),e=t.data("wpWpColorPicker"),o._color._alpha=i,c=o._color.toString(),t.val(c),e.toggler.css({"background-color":c}),n&&a(i,r),t.wpColorPicker("color",c)}function a(i,t){t.slider("value",i),t.find(".ui-slider-handle").text(i.toString())}Color.prototype.toString=function(i){if("no-alpha"==i)return this.toCSS("rgba","1").replace(/\s+/g,"");if(1>this._alpha)return this.toCSS("rgba",this._alpha).replace(/\s+/g,"");var t=parseInt(this._color,10).toString(16);if(this.error)return"";if(t.length<6)for(var a=6-t.length-1;a>=0;a--)t="0"+t;return"#"+t},jQuery(document).ready((function(r){r(".betterdocs-alpha-color-control").each((function(){var n,o,e,c,l,s,d,p,u;n=r(this),o=n.val().replace(/\s+/g,""),e=n.attr("data-palette"),c=n.attr("data-show-opacity"),l=n.attr("data-default-color"),s={change:function(t,a){var r,o,e;r=n.attr("data-customize-setting-link"),o=n.wpColorPicker("color"),l==o&&(e=i(o),p.find(".ui-slider-handle").text(e)),wp.customize(r,(function(i){i.set(o)})),d.find(".transparency").css("background-color",a.color.toString("no-alpha"))},palettes:-1!==e.indexOf("|")?e.split("|"):"false"!=e},n.wpColorPicker(s),d=n.parents(".wp-picker-container:first"),r('<div class="alpha-color-picker-container"><div class="min-click-zone click-zone"></div><div class="max-click-zone click-zone"></div><div class="alpha-slider"></div><div class="transparency"></div></div>').appendTo(d.find(".wp-picker-holder")),p=d.find(".alpha-slider"),u={create:function(i,t){var a=r(this).slider("value");r(this).find(".ui-slider-handle").text(a),r(this).siblings(".transparency ").css("background-color",o)},value:i(o),range:"max",step:1,min:0,max:100,animate:300},p.slider(u),"true"==c&&p.find(".ui-slider-handle").addClass("show-opacity"),d.find(".min-click-zone").on("click",(function(){t(0,n,p,!0)})),d.find(".max-click-zone").on("click",(function(){t(100,n,p,!0)})),d.find(".iris-palette").on("click",(function(){var t,o;a(o=i(t=r(this).css("background-color")),p),100!=o&&(t=t.replace(/[^,]+(?=\))/,(o/100).toFixed(2))),n.wpColorPicker("color",t)})),d.find(".button.wp-picker-clear").on("click",(function(){var i=n.attr("data-customize-setting-link");n.wpColorPicker("color",""),wp.customize(i,(function(i){i.set("")})),a(100,p)})),d.find(".button.wp-picker-default").on("click",(function(){a(i(l),p)})),n.on("input",(function(){a(i(r(this).val()),p)})),p.slider().on("slide",(function(i,a){t(parseFloat(a.value)/100,n,p,!1),r(this).find(".ui-slider-handle").text(a.value)}))}))}))})();