!function(e){wp.customize.bind("ready",(function(){t();var r=jQuery(".betterdocs-customizer-reset.betterdocs-range-value");r.each((function(){e(r).on("click",(function(t){t.preventDefault();var r=e(this).next(".betterdcos-range-slider"),a=e(this).next(".betterdcos-range-slider").data("default-val"),i=r.find(".betterdcos-range-slider__range").attr("suffix");r.find(".betterdcos-range-slider__range").val(a).trigger("change"),r.find(".betterdcos-range-slider__value").html(a+i)}))}))}));var t=function(){var t=e(".betterdcos-range-slider"),r=e(".betterdcos-range-slider__range"),a=e(".betterdcos-range-slider__value");t.each((function(){a.each((function(){var t=e(this).prev().attr("value"),r=e(this).prev().attr("suffix")?e(this).prev().attr("suffix"):"";e(this).html(t+r)})),r.on("input",(function(){var t=e(this).attr("suffix")?e(this).attr("suffix"):"";e(this).next(a).html(this.value+t)}))}))}}(jQuery);