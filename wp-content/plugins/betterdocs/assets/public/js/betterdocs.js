(()=>{var e={22104:e=>{"use strict";e.exports=ClipboardJS}},t={};function o(s){var r=t[s];if(void 0!==r)return r.exports;var i=t[s]={exports:{}};return e[s](i,i.exports,o),i.exports}class s{constructor(e){this.config=e,this.initialize(),this.init()}init(){this.categoryListAccordion(),this.feedbackForm(),this.printToc(),this.stickyTocContainer(),this.tocClose(),this.cloneTocContentToAfter(),this.copyToClipboard(),this.subCategoryExpand(),this.tocSmallCollapisble(),this.sidebarOpen(),this.tocOpen(),this.articleSummary()}tocSmallCollapisble(){$=jQuery,$(".betterdocs-toc.collapsible-sm .toc-title").each((function(){$(this).click((function(e){e.preventDefault(),$(this).children(".angle-icon").toggle(),$(this).next(".toc-list").slideToggle()}))}))}initialize(){var e=jQuery;this.body=e("body"),this.catTitleList=e(".betterdocs-sidebar-content .betterdocs-category-grid-wrapper .betterdocs-single-category-wrapper .betterdocs-category-header"),this.currentActiveCat=e(".betterdocs-sidebar-content .betterdocs-category-grid-wrapper .betterdocs-single-category-wrapper.active"),this.listSidebarCatTitles=e(".betterdocs-sidebar-content .betterdocs-sidebar-list-wrapper .betterdocs-sidebar-list .betterdocs-category-header"),this.listSidebarCurrentCat=e(".betterdocs-sidebar-content .betterdocs-sidebar-list-wrapper .betterdocs-sidebar-list.active"),this.formLink=e("a[name=betterdocs-form-modal]"),this.feedbackFormWrap=e("#betterdocs-feedback-form"),this.feedbackFormFields=e("#betterdocs-feedback-form input, #betterdocs-feedback-form textarea")}categoryListAccordion(){this.listSidebarCurrentCat.find(".betterdocs-body").css("display","block"),this.listSidebarCurrentCat.siblings().find(".betterdocs-body").css("display","none"),this.listSidebarCatTitles.on("click",(function(e){e.preventDefault();let t=jQuery(e.target).closest(".betterdocs-sidebar-list");t.find(".betterdocs-body").slideToggle(),t.toggleClass("active").siblings().removeClass("active").find(".betterdocs-body").slideUp()}))}onScroll(){var e=jQuery;let t;e(this).find(".betterdocs-content-heading")?.map(((o,s)=>{s?.getBoundingClientRect()?.top-140<0&&(t=e(s)?.attr("id"))})),null!=t&&e(".sticky-toc-container .betterdocs-toc .toc-list a, .betterdocs-full-sidebar-right .betterdocs-toc .toc-list a, .wp-block-group.is-position-sticky .betterdocs-toc .toc-list a, .betterdocs-elementor .betterdocs-toc .toc-list a").each((function(){var o=e(this);e(o).attr("href")=="#"+t&&(e(".betterdocs-toc .toc-list a").removeClass("active"),o.addClass("active"))}))}stickyTocContainer(){var e=jQuery;let t=this;var o=e("#betterdocs-sidebar");if(e(".betterdocs-toc").length>0&&o.length){var s=e(".betterdocs-toc").clone();e(".sticky-toc-container").append(s)}e(window).on("scroll",(function(){var t=document.querySelector(".betterdocs-sidebar-content"),s=e(".sticky-toc-container .betterdocs-toc .toc-list li a")?.length>0;if(null!==t){var r=e(".sticky-toc-container"),i=e(".betterdocs-sidebar-content").outerHeight(),c=t.getBoundingClientRect(),a=Math.abs(c?.top);c?.top<0&&i<=a&&s?r.addClass("toc-sticky"):r.removeClass("toc-sticky"),o.closest(".elementor-widget-wrap")?.length>0?e(window).scrollTop()>=o.closest(".elementor-widget-wrap").offset()?.top+o.closest(".elementor-widget-wrap").outerHeight()-window.innerHeight&&!o.hasClass("betterdocs-el-single-sidebar")&&r?.removeClass("toc-sticky"):o.closest(".wp-block-column")?.length>0?e(window).scrollTop()>=o.closest(".wp-block-column").offset()?.top+o.closest(".wp-block-column").outerHeight()-window.innerHeight&&!o.hasClass("betterdocs-el-single-sidebar")&&r?.removeClass("toc-sticky"):e(window).scrollTop()>=o.offset()?.top+o.outerHeight()-window.innerHeight&&!o.hasClass("betterdocs-el-single-sidebar")&&r?.removeClass("toc-sticky")}})),e(document).on("scroll",t?.onScroll);var r=e(".betterdocs-toc .toc-list a");r.on("click",(function(o){o.preventDefault(),e(document).off("scroll"),r.each((function(){e(this).removeClass("active")})),e(this).addClass("active");var s=decodeURIComponent(this.hash),i=e(s),c=i.offset()?.top;e("html, body").stop().animate({scrollTop:c-betterdocsConfig?.sticky_toc_offset},"slow",(function(){e(document).on("scroll",t?.onScroll)}))}))}tocClose(){var e=jQuery;e(".close-toc").on("click",(function(t){t.preventDefault(),e(".sticky-toc-container").remove(".sticky-toc-container")}))}sidebarOpen(){var e=jQuery;e(window).width()<=768&&(e(".betterdocs-sidebar-icon").on("click",(function(t){t.stopPropagation(),e(".betterdocs-mobile-sidebar-wrapper").length||e("#betterdocs-sidebar, #betterdocs-sidebar-left, #betterdocs-full-sidebar-left").wrap('<div class="betterdocs-mobile-sidebar-wrapper"></div>'),e(".betterdocs-mobile-sidebar-wrapper").parent().is(".betterdocs-content-wrapper")||e(".betterdocs-content-wrapper").append(e(".betterdocs-mobile-sidebar-wrapper")),e(".betterdocs-mobile-sidebar-wrapper").fadeIn(),e("#betterdocs-sidebar, #betterdocs-sidebar-left, #betterdocs-full-sidebar-left").css({left:"-300px",display:"block"}).animate({left:"0"},300)})),e(document).on("click.betterdocs-left-sidebar",(function(t){e(t.target).closest("#betterdocs-sidebar, #betterdocs-sidebar-left, #betterdocs-full-sidebar-left, .betterdocs-sidebar-icon").length||e("#betterdocs-sidebar, #betterdocs-sidebar-left, #betterdocs-full-sidebar-left").animate({left:"-300px"},300,(function(){e(".betterdocs-mobile-sidebar-wrapper").fadeOut()}))})),e("#betterdocs-sidebar, #betterdocs-sidebar-left, #betterdocs-full-sidebar-left").on("click",(function(e){e.stopPropagation()})))}tocOpen(){var e=jQuery;e(window).width()<=768&&(e(".betterdocs-toc-icon").on("click",(function(t){t.stopPropagation(),e(".betterdocs-mobile-toc-wrapper").length||e("#betterdocs-sidebar-right").wrap('<div class="betterdocs-mobile-toc-wrapper"></div>'),e(".betterdocs-mobile-toc-wrapper").parent().is(".betterdocs-content-wrapper")||e(".betterdocs-content-wrapper").append(e(".betterdocs-mobile-toc-wrapper")),e(".betterdocs-mobile-toc-wrapper").fadeIn(),e("#betterdocs-sidebar-right").css({right:"-300px",display:"block"}).animate({right:"0"},300)})),e(document).on("click.betterdocs-right-sidebar",(function(t){e(t.target).closest("#betterdocs-sidebar-right, .betterdocs-toc-icon").length||e("#betterdocs-sidebar-right").animate({right:"-300px"},300,(function(){e(".betterdocs-mobile-toc-wrapper").fadeOut()}))})),e("#betterdocs-sidebar-right").on("click",(function(e){e.stopPropagation()})))}delay(e,t){setTimeout(e,t)}printToc(){this.reusablePrintFunction(".betterdocs-print-btn"),this.reusablePrintFunction(".betterdocs-print-pdf-2")}cloneTocContentToAfter(){let e=jQuery,t=e(".betterdocs-single-layout-3 #betterdocs-sidebar-right .betterdocs-toc .toc-list a");e.each(t,(function(t,o){e(this).addClass(`toc-item-link-${t+1}`),e(`<style>.toc-item-link-${t+1}:after {height: ${o.offsetHeight+10}px}</style>`).appendTo("head")}))}reusablePrintFunction(e){var t=jQuery;t("body").on("click",e,(function(e){let o="";t("#betterdocs-entry-title").length?o=document.getElementById("betterdocs-entry-title").innerHTML:t(".wp-block-post-title").length&&(o=document.getElementsByClassName("wp-block-post-title")[0]?.innerHTML);var s=document.getElementById("betterdocs-single-content").innerHTML,r=document.createElement("div");r.innerHTML="<h1>"+o+"</h1> "+s,r.id="new-doc-print";var i=document.getElementById("betterdocs-single-content").offsetWidth,c=t(window).height(),a=window.open("","","left=50%,top=10%,width="+i+",height="+c+",toolbar=0,scrollbars=0,status=0"),n=Array.from(document.styleSheets).map((e=>{try{return Array.from(e.cssRules).map((e=>e.cssText)).join("")}catch(t){return console.log("Access to stylesheet blocked by CORS policy:",e.href),""}})).join("\n"),d=a.document.createElement("style");d.textContent=n,a.document.head.appendChild(d),a.document.body.innerHTML=r.outerHTML,a.document.close(),a.focus(),a.print(),setTimeout((function(){a.close()}),300)}))}copyToClipboard(){let e=jQuery;if(e(".batterdocs-anchor").length){const t=function(t){t.preventDefault(),t.stopPropagation();let o=new ClipboardJS(t.target);o.on("success",(function(t){e(document).find("div.tooltip-box").text(betterdocsConfig.copy_text),t.clearSelection(),e(t.trigger).addClass("copied"),setTimeout((function(){e(t.trigger).removeClass("copied")}),2e3),o.destroy()}));let s=ClipboardJS.copy(t.target.dataset.clipboardText);o.emit(s?"success":"error",{action:"copy",text:s,trigger:t.target,clearSelection(){t.target&&t.target.focus(),window.getSelection().removeAllRanges()}})};e('a.batterdocs-anchor[href*="#"]').hover((function(){var t=e(this).attr("data-title");e("<div/>",{text:t,class:"tooltip-box"}).appendTo(this)}),(function(){e(this).find("div.tooltip-box").remove()})).on("click",t),function(){if("undefined"!=typeof self&&self.Prism&&self.document)if(Prism.plugins.toolbar){var e=window.ClipboardJS||void 0;e||(e=o(22104));var t=[];if(!e){var s=document.createElement("script"),r=document.querySelector("head");s.onload=function(){if(e=window.ClipboardJS)for(;t.length;)t.pop()()},s.src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.0/clipboard.min.js",r.appendChild(s)}Prism.plugins.toolbar.registerButton("copy-to-clipboard",(function(o){var s=document.createElement("button");return s.textContent="Copy",e?r():t.push(r),s;function r(){var t=new e(s,{text:function(){return o.code}});t.on("success",(function(){s.textContent="Copied!",i()})),t.on("error",(function(){s.textContent="Press Ctrl+C to copy",i()}))}function i(){setTimeout((function(){s.textContent="Copy"}),5e3)}}))}else console.warn("Copy to Clipboard plugin loaded before Toolbar plugin.")}()}}feedbackForm(){var e=jQuery,t=this;let o=e("#betterdocs-form-modal"),s=e("#betterdocs-form-modal .modal-content");this.formLink.click((function(e){e.preventDefault(),o.fadeIn(500)})),e(document).mouseup((function(e){s.is(e.target)||0!==s.has(e.target).length||o.fadeOut()})),e(".betterdocs-modalwindow .close").click((function(e){e.preventDefault(),o.fadeOut(500)})),this.feedbackFormFields.on("keyup",(function(){e(this).removeClass("val-error"),e(this).siblings(".error-message").remove()})),this.feedbackFormWrap.on("submit",(function(o){o.preventDefault();var s=e(this),r=e("#message_name"),i=e("#message_email"),c=e("#message_subject"),a=e("#message_text");t.betterdocsFeedbackFormSubmit(s,r,i,c,a)})),this.betterdocsFeedbackFormSubmit=function(o,s,r,i,c){let a;a&&a.abort(),a=e.ajax({url:betterdocsSubmitFormConfig.ajax_url,type:"post",data:{action:"betterdocs_feedback_form_submit",form:o.serializeArray(),postID:betterdocsSubmitFormConfig.post_id,message_name:s.val(),message_email:r.val(),message_subject:i.val(),message_text:c.val(),security:betterdocsSubmitFormConfig.nonce},beforeSend:function(){},success:function(i){i.success?1==i.success?(e(".response").html('<span class="success-message">'+i.data.message+"</span>"),o[0].reset(),t.delay((function(){e(".betterdocs-modalwindow").fadeOut(500),e(".response .success-message").remove()}),3e3)):e(".response").html('<span class="error-message">'+i.sentMessage+"</span>"):0==i.success&&(i.data.message.name&&0==s.hasClass("val-error")&&(s.addClass("val-error"),e(".form-name").append('<span class="error-message">'+i.data.message.name+"</span>")),i.data.message.email&&0==r.hasClass("val-error")&&(r.addClass("val-error"),e(".form-email").append('<span class="error-message">'+i.data.message.email+"</span>")),i.data.message.message&&0==c.hasClass("val-error")&&(c.addClass("val-error"),e(".form-message").append('<span class="error-message">'+i.data.message.message+"</span>")))}})}}subCategoryExpand(){var e=jQuery;let t=e(".betterdocs-nested-category-list.betterdocs-current-category.active");t.length>0&&t.each((function(t){e(this).prev().children(".toggle-arrow").toggle()}))}articleSummary(){var e=jQuery,t=e("#betterdocs-article-summary"),o=e("#betterdocs-summary-toggle"),s=e("#betterdocs-summary-content"),r=e("#betterdocs-summary-loading"),i=e("#betterdocs-summary-text"),c=!1;0!==o.length&&0!==t.length&&o.on("click",(function(o){o.preventDefault(),s.is(":visible")?(s.slideUp(),e(this).find(".angle-right").show(),e(this).find(".angle-down").hide()):(s.slideDown(),e(this).find(".angle-right").hide(),e(this).find(".angle-down").show(),c||function(){r.show(),i.hide();var o=parseInt(t.data("post-id"))||0,s=t.data("post-type")||"",a=e("#betterdocs-single-content").text()||e(".betterdocs-content").text()||"",n=e("#betterdocs-entry-title").text()||e(".betterdocs-entry-heading").text()||document.title;a.trim()?o<=0?(r.hide(),i.html('<p class="summary-error">Invalid post ID for summary generation.</p>').show()):"docs"!==s?(r.hide(),i.html('<p class="summary-error">Article summary is only available for documentation posts.</p>').show()):e.ajax({url:betterdocsConfig.ajax_url,type:"POST",data:{action:"betterdocs_generate_article_summary",post_id:o,post_title:n,post_content:a.substring(0,4e3),nonce:betterdocsConfig.summary_nonce},success:function(e){if(r.hide(),e.success&&e.data){var t='<div class="summary-content">'+e.data+"</div>";i.html(t);var o=i.data("style");o?i.attr("style",o+"; display: block;"):i.show(),c=!0}else{var s=e.data||betterdocsConfig.summary_error||"Failed to generate article summary.";i.html('<p class="summary-error">'+s+"</p>").show()}},error:function(){r.hide(),i.html('<p class="summary-error">'+(betterdocsConfig.summary_error||"Failed to generate article summary.")+"</p>").show()}}):(r.hide(),i.html("<p>"+(betterdocsConfig.summary_error||"Unable to generate summary for this article.")+"</p>").show())}())}))}}!function(){"use strict";jQuery(document).ready((function(){new s(window?.betterdocsConfig)}))}()})();