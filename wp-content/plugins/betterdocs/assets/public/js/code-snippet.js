!function(){"use strict";window.BetterDocsCodeSnippet=window.BetterDocsCodeSnippet||{};let e=!1,t=!1,o=!1;function n(e){if(document.querySelector(`link[href="${e}"]`))return;const t=document.createElement("link");t.rel="stylesheet",t.href=e,t.onerror=function(){console.warn("BetterDocs Code Snippet: Failed to load stylesheet:",e)},document.head.appendChild(t)}function c(o){if(e)return void(o&&o(!0));if(t){const n=setInterval((function(){!e&&t||(clearInterval(n),o&&o(e))}),100);return}t=!0;const c=document.querySelectorAll(".betterdocs-code-snippet-wrapper.theme-dark"),i=document.querySelectorAll(".betterdocs-code-snippet-wrapper.theme-light");c.length>0&&n("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css"),i.length>0&&n("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css"),function(e,t){const o=document.createElement("script");o.src=e,o.onload=t,o.onerror=function(){console.warn("BetterDocs Code Snippet: Failed to load script:",e),t&&t(!1)},document.head.appendChild(o)}("https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js",(function(n){t=!1,n&&window.hljs?(e=!0,console.log("BetterDocs Code Snippet: Highlight.js loaded successfully")):console.warn("BetterDocs Code Snippet: Failed to load Highlight.js"),o&&o(e)}))}function i(){e&&window.hljs&&document.querySelectorAll(".betterdocs-code-snippet-code:not(.hljs)").forEach((function(e){try{window.hljs.highlightElement(e)}catch(e){console.warn("BetterDocs Code Snippet: Error highlighting code block:",e)}}))}function s(e,t){const o=document.createElement("textarea");o.value=e,o.style.position="fixed",o.style.left="-999999px",o.style.top="-999999px",document.body.appendChild(o);try{o.focus(),o.select();const e=document.execCommand("copy");t&&t(e)}catch(e){console.warn("BetterDocs Code Snippet: Fallback copy failed:",e),t&&t(!1)}finally{document.body.removeChild(o)}}function d(e,t){const o=e.getBoundingClientRect(),n=t.getBoundingClientRect(),c=o.left+o.width/2-n.width/2,i=o.top-n.height-8;t.style.left=Math.max(8,c)+"px",t.style.top=Math.max(8,i)+"px"}function l(e){const t=e.querySelector(".betterdocs-code-snippet-copy-button"),o=e.querySelector(".betterdocs-code-snippet-code code");t&&o&&(function(e){const t=e.closest(".betterdocs-code-snippet-copy-container"),o=t?t.querySelector(".betterdocs-code-snippet-tooltip"):null;o&&(e.addEventListener("mouseenter",(function(){o.classList.add("show"),d(e,o)})),e.addEventListener("mouseleave",(function(){o.classList.remove("show")})),e.addEventListener("blur",(function(){o.classList.remove("show")})),window.addEventListener("scroll",(function(){o.classList.contains("show")&&d(e,o)})),window.addEventListener("resize",(function(){o.classList.contains("show")&&d(e,o)})))}(t),t.removeEventListener("click",t._betterDocsClickHandler),t._betterDocsClickHandler=function(n){n.preventDefault();const c=o.textContent||o.innerText||"";var i,l;c.trim()?(i=c,l=function(o){if(o){!function(e){const t=e.closest(".betterdocs-code-snippet-copy-container"),o=t?t.querySelector(".betterdocs-code-snippet-tooltip"):null;if(o){const t=o.textContent;o.textContent="Copied!",o.classList.contains("show")&&d(e,o),setTimeout((function(){o.textContent=t,o.classList.contains("show")&&d(e,o)}),2e3)}}(t);const o=new CustomEvent("betterdocs-code-copied",{detail:{snippet:e,code:c,language:e.dataset.language}});document.dispatchEvent(o)}else console.error("BetterDocs Code Snippet: Failed to copy code to clipboard"),function(e){const t=e.closest(".betterdocs-code-snippet-copy-container"),o=t?t.querySelector(".betterdocs-code-snippet-tooltip"):null;if(o){const t=o.textContent;o.textContent="Copy Failed",o.classList.contains("show")&&d(e,o),setTimeout((function(){o.textContent=t,o.classList.contains("show")&&d(e,o)}),2e3)}}(t)},navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(i).then((function(){l&&l(!0)})).catch((function(e){console.warn("BetterDocs Code Snippet: Clipboard API failed:",e),s(i,l)})):s(i,l)):console.warn("BetterDocs Code Snippet: No code content to copy")},t.addEventListener("click",t._betterDocsClickHandler))}function r(){const e=document.querySelectorAll(".betterdocs-code-snippet-wrapper");0!==e.length&&(c((function(e){e&&i()})),e.forEach((function(e){"true"===e.dataset.copyButton&&l(e)})),o=!0)}function a(){o=!1,r()}window.BetterDocsCodeSnippet={init:r,reinit:a,initCopyButton:l,applySyntaxHighlighting:i,loadHighlightJs:c},"loading"===document.readyState?document.addEventListener("DOMContentLoaded",r):r(),window.MutationObserver&&new MutationObserver((function(e){let t=!1;e.forEach((function(e){"childList"===e.type&&e.addedNodes.forEach((function(e){1===e.nodeType&&(e.classList&&e.classList.contains("betterdocs-code-snippet-wrapper")||e.querySelector&&e.querySelector(".betterdocs-code-snippet-wrapper"))&&(t=!0)}))})),t&&setTimeout(a,100)})).observe(document.body,{childList:!0,subtree:!0})}();