(()=>{"use strict";var e;(e=jQuery)(document).ready((function(){document.querySelectorAll(".betterdocs-category-grid-inner-wrapper.masonry").forEach((t=>{!function(e,t){let r=t(".betterdocs-category-grid-inner-wrapper.masonry",e);if(0!=r?.length){var a=window.matchMedia("(max-width: 767px)"),o=window.matchMedia("(max-width: 1024px)");r.each(((e,t)=>{var r;let n=0,c=0;switch(!0){case a.matches:n=t.getAttribute("data-column_mobile"),c=t.getAttribute("data-column_space_mobile");break;case o.matches:n=t.getAttribute("data-column_tab"),c=t.getAttribute("data-column_space_tab");break;default:n=t.getAttribute("data-column_desktop"),c=null!==(r=t.getAttribute("data-column_space_desktop"))&&void 0!==r?r:15}c=parseInt(c),n=parseInt(n);let i=t.querySelectorAll(".betterdocs-single-category-wrapper"),s=(n-1)*c;t&&(i.forEach((e=>{e.style.width=`calc((100% - ${s}px) / ${n})`})),new Masonry(t,{itemSelector:".betterdocs-single-category-wrapper",percentPosition:!0,gutter:c}))}))}else(t(".betterdocs-single-category-wrapper",e)||[]).each(((e,t)=>{t.removeAttribute("style")}))}(t.parentElement,e)})),1!=betterdocsCategoryGridConfig?.is_betterdocs_templates&&(e(".betterdocs-category-grid-inner-wrapper .betterdocs-single-category-wrapper .betterdocs-category-link-btn").on("click",(function(t){let r=e(e(e(e(this)?.parent())?.parent())?.parent())?.parent(),a=e(r).attr("data-mkb-slug");if(a?.length>0){var o=new Date,n=o.setDate(o.getDate()+30).toString();document.cookie=`last_knowledge_base=${a}; expires=${n}; path=/;`}})),e(".betterdocs-category-grid-inner-wrapper .betterdocs-single-category-wrapper .betterdocs-body .betterdocs-articles-list li a").on("click",(function(t){let r=e(e(e(e(e(e(this).parent()).parent()).parent()).parent()).parent()).parent(),a=e(r).attr("data-mkb-slug");if(a?.length>0){var o=new Date,n=o.setDate(o.getDate()+30).toString();document.cookie=`last_knowledge_base=${a}; expires=${n}; path=/;`}})))}))})();