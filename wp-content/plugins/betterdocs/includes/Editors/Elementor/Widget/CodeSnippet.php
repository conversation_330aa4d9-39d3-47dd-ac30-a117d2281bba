<?php

namespace WPDeveloper\BetterDocs\Editors\Elementor\Widget;

use WPDeveloper\BetterDocs\Editors\Elementor\BaseWidget;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

class CodeSnippet extends BaseWidget {

    public function get_name() {
        return 'betterdocs-code-snippet';
    }

    public function get_title() {
        return __( 'BetterDocs Code Snippet', 'betterdocs' );
    }

    public function get_categories() {
        return [ 'betterdocs-elements' ];
    }

    public function get_keywords() {
        return [ 'betterdocs-elements', 'code', 'snippet', 'syntax', 'highlight', 'programming', 'betterdocs' ];
    }

    public function get_icon() {
        return 'eicon-code';
    }

    public function get_style_depends() {
        return [ 'betterdocs-code-snippet' ];
    }

    public function get_script_depends() {
        return [ 'betterdocs-code-snippet' ];
    }

    protected function register_controls() {
        // Content Tab
        $this->start_controls_section(
            'content_section',
            [
                'label' => __( 'Code Content', 'betterdocs' ),
                'tab'   => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'code_content',
            [
                'label'       => __( 'Code', 'betterdocs' ),
                'type'        => Controls_Manager::TEXTAREA,
                'rows'        => 10,
                'default'     => "// Paste or type your code here…\n// Select language below to activate highlighting.\n\nfunction helloWorld() {\n    console.log(\"Hello, World!\");\n    return \"Welcome to BetterDocs!\";\n}\n\nhelloWorld();",
                'placeholder' => __( 'Enter your code snippet here...', 'betterdocs' ),
                'description' => __( 'Enter your code snippet. Syntax highlighting will be applied based on the selected language.', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'language',
            [
                'label'   => __( 'Language', 'betterdocs' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'javascript',
                'options' => [
                    'javascript'  => __( 'JavaScript', 'betterdocs' ),
                    'php'         => __( 'PHP', 'betterdocs' ),
                    'python'      => __( 'Python', 'betterdocs' ),
                    'java'        => __( 'Java', 'betterdocs' ),
                    'ruby'        => __( 'Ruby', 'betterdocs' ),
                    'bash'        => __( 'Bash', 'betterdocs' ),
                    'json'        => __( 'JSON', 'betterdocs' ),
                    'yaml'        => __( 'YAML', 'betterdocs' ),
                    'html'        => __( 'HTML', 'betterdocs' ),
                    'css'         => __( 'CSS', 'betterdocs' ),
                    'sql'         => __( 'SQL', 'betterdocs' ),
                    'xml'         => __( 'XML', 'betterdocs' ),
                    'cpp'         => __( 'C++', 'betterdocs' ),
                    'csharp'      => __( 'C#', 'betterdocs' ),
                    'go'          => __( 'Go', 'betterdocs' ),
                    'rust'        => __( 'Rust', 'betterdocs' ),
                    'swift'       => __( 'Swift', 'betterdocs' ),
                    'kotlin'      => __( 'Kotlin', 'betterdocs' ),
                    'typescript'  => __( 'TypeScript', 'betterdocs' ),
                ],
                'description' => __( 'Select the programming language for syntax highlighting.', 'betterdocs' ),
            ]
        );

        $this->end_controls_section();

        // Settings Tab
        $this->start_controls_section(
            'settings_section',
            [
                'label' => __( 'Display Settings', 'betterdocs' ),
                'tab'   => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'show_language_label',
            [
                'label'        => __( 'Show Language Label', 'betterdocs' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'betterdocs' ),
                'label_off'    => __( 'Hide', 'betterdocs' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'description'  => __( 'Display the programming language label in the header.', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'show_copy_button',
            [
                'label'        => __( 'Show Copy Button', 'betterdocs' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'betterdocs' ),
                'label_off'    => __( 'Hide', 'betterdocs' ),
                'return_value' => 'yes',
                'default'      => 'yes',
                'description'  => __( 'Display a copy-to-clipboard button in the header.', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'show_line_numbers',
            [
                'label'        => __( 'Show Line Numbers', 'betterdocs' ),
                'type'         => Controls_Manager::SWITCHER,
                'label_on'     => __( 'Show', 'betterdocs' ),
                'label_off'    => __( 'Hide', 'betterdocs' ),
                'return_value' => 'yes',
                'default'      => 'no',
                'description'  => __( 'Display line numbers in the code block.', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'theme',
            [
                'label'   => __( 'Theme', 'betterdocs' ),
                'type'    => Controls_Manager::SELECT,
                'default' => 'light',
                'options' => [
                    'light' => __( 'Light Theme', 'betterdocs' ),
                    'dark'  => __( 'Dark Theme', 'betterdocs' ),
                ],
                'description' => __( 'Choose the color theme for the code block.', 'betterdocs' ),
            ]
        );

        $this->end_controls_section();

        // Style Tab - Wrapper
        $this->start_controls_section(
            'wrapper_style_section',
            [
                'label' => __( 'Wrapper', 'betterdocs' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'wrapper_margin',
            [
                'label'      => __( 'Margin', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-wrapper' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'wrapper_padding',
            [
                'label'      => __( 'Padding', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-wrapper' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name'     => 'wrapper_background',
                'label'    => __( 'Background', 'betterdocs' ),
                'types'    => [ 'classic', 'gradient' ],
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-wrapper',
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'     => 'wrapper_border',
                'label'    => __( 'Border', 'betterdocs' ),
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-wrapper',
            ]
        );

        $this->add_responsive_control(
            'wrapper_border_radius',
            [
                'label'      => __( 'Border Radius', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-wrapper' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name'     => 'wrapper_box_shadow',
                'label'    => __( 'Box Shadow', 'betterdocs' ),
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-wrapper',
            ]
        );

        $this->end_controls_section();

        // Style Tab - Code
        $this->start_controls_section(
            'code_style_section',
            [
                'label' => __( 'Code Styles', 'betterdocs' ),
                'tab'   => Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'     => 'code_typography',
                'label'    => __( 'Typography', 'betterdocs' ),
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-code',
            ]
        );

        $this->add_control(
            'code_color',
            [
                'label'     => __( 'Text Color', 'betterdocs' ),
                'type'      => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .betterdocs-code-snippet-code' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'code_padding',
            [
                'label'      => __( 'Padding', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-code' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Tab - Copy Button
        $this->start_controls_section(
            'copy_button_style_section',
            [
                'label'     => __( 'Copy Button', 'betterdocs' ),
                'tab'       => Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_copy_button' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name'     => 'copy_button_typography',
                'label'    => __( 'Typography', 'betterdocs' ),
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-copy-button',
            ]
        );

        $this->start_controls_tabs( 'copy_button_style_tabs' );

        $this->start_controls_tab(
            'copy_button_normal_tab',
            [
                'label' => __( 'Normal', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'copy_button_color',
            [
                'label'     => __( 'Text Color', 'betterdocs' ),
                'type'      => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .betterdocs-code-snippet-copy-button' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name'     => 'copy_button_background',
                'label'    => __( 'Background', 'betterdocs' ),
                'types'    => [ 'classic', 'gradient' ],
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-copy-button',
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'copy_button_hover_tab',
            [
                'label' => __( 'Hover', 'betterdocs' ),
            ]
        );

        $this->add_control(
            'copy_button_hover_color',
            [
                'label'     => __( 'Text Color', 'betterdocs' ),
                'type'      => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .betterdocs-code-snippet-copy-button:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name'     => 'copy_button_hover_background',
                'label'    => __( 'Background', 'betterdocs' ),
                'types'    => [ 'classic', 'gradient' ],
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-copy-button:hover',
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_responsive_control(
            'copy_button_padding',
            [
                'label'      => __( 'Padding', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-copy-button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'separator'  => 'before',
            ]
        );

        $this->add_responsive_control(
            'copy_button_margin',
            [
                'label'      => __( 'Margin', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', 'em', '%' ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-copy-button' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name'     => 'copy_button_border',
                'label'    => __( 'Border', 'betterdocs' ),
                'selector' => '{{WRAPPER}} .betterdocs-code-snippet-copy-button',
            ]
        );

        $this->add_responsive_control(
            'copy_button_border_radius',
            [
                'label'      => __( 'Border Radius', 'betterdocs' ),
                'type'       => Controls_Manager::DIMENSIONS,
                'size_units' => [ 'px', '%' ],
                'default'    => [
                    'top'      => '8',
                    'right'    => '8',
                    'bottom'   => '8',
                    'left'     => '8',
                    'unit'     => 'px',
                    'isLinked' => true,
                ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-copy-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Tab - Line Numbers
        $this->start_controls_section(
            'line_numbers_style_section',
            [
                'label'     => __( 'Line Numbers', 'betterdocs' ),
                'tab'       => Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_line_numbers' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'line_numbers_color',
            [
                'label'     => __( 'Text Color', 'betterdocs' ),
                'type'      => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .betterdocs-code-snippet-line-numbers' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'line_numbers_background_color',
            [
                'label'     => __( 'Background Color', 'betterdocs' ),
                'type'      => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .betterdocs-code-snippet-line-numbers' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'line_numbers_width',
            [
                'label'      => __( 'Width', 'betterdocs' ),
                'type'       => Controls_Manager::SLIDER,
                'size_units' => [ 'px', 'em' ],
                'range'      => [
                    'px' => [
                        'min' => 30,
                        'max' => 100,
                    ],
                    'em' => [
                        'min' => 2,
                        'max' => 6,
                    ],
                ],
                'selectors'  => [
                    '{{WRAPPER}} .betterdocs-code-snippet-line-numbers' => 'min-width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    public function view_params() {
        $settings = $this->get_settings_for_display();

        return [
            'code_content'       => $settings['code_content'],
            'language'           => $settings['language'],
            'show_language_label' => $settings['show_language_label'] === 'yes',
            'show_copy_button'   => $settings['show_copy_button'] === 'yes',
            'show_line_numbers'  => $settings['show_line_numbers'] === 'yes',
            'theme'              => $settings['theme'],
            'widget_type'        => 'elementor',
        ];
    }

    protected function render_callback() {
        $this->views( 'widgets/code-snippet' );
    }
}
