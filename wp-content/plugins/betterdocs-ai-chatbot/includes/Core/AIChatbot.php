<?php

namespace WPDeveloper\BetterDocsChatbot\Core;

use WPDeveloper\BetterDocs\Utils\Base;
use WPDeveloper\BetterDocsChatbot\Utils\Helper;
use WPDeveloper\BetterDocs\Admin\Builder\GlobalFields;

class AIChatbot extends Base
{

	public function __construct()
	{
		add_action('admin_init', [$this, 'send_data']);
	}

	public static $api_url = 'https://ai-chatbot.betterdocs.co';

	public static function is_ai_chatbot_enabled()
	{
		$settings = get_option('betterdocs_settings');

		return isset($settings['enable_ai_chatbot']) ? $settings['enable_ai_chatbot'] : false;
	}

	public static function get_endpoint($post_type)
	{
		$post_type_object  = get_object_vars(get_post_type_object($post_type));
		$site_url          = get_site_url();
		$rest_api_endpoint = '';

		if ($post_type_object && !empty($post_type_object['rest_base'])) {
			$rest_api_endpoint = '/wp-json/wp/v2/' . $post_type_object['rest_base'];
		} else if ($post_type_object && !empty($post_type_object['show_in_rest'])) {
			$rest_api_endpoint = '/wp-json/wp/v2/' . $post_type;
		}


		return $site_url . $rest_api_endpoint;
	}

	public static function strip_gutenberg_blocks($content)
	{
		// Remove Gutenberg HTML comments
		$content = preg_replace('/<!-- wp:(.*?) -->/s', '', $content);
		$content = preg_replace('/<!-- \/wp:(.*?) -->/s', '', $content);

		// Remove other HTML tags
		$content = wp_strip_all_tags($content);

		return $content;
	}

	public static function clean_text($text)
	{
		// Remove extra spaces
		$text = preg_replace('/\s+/', ' ', $text);

		// Trim leading and trailing spaces
		$text = trim($text);

		return $text;
	}

	public static function get_the_error_message($response)
	{
		// Define the regex pattern to capture the message
		$pattern = '/"message":\s*"([^"]*)"/';

		// Check if the pattern matches the JSON string
		if (preg_match($pattern, $response, $matches)) {
			// Return only the message
			return $matches[1];
		}

		// Return null if no message is found
		return $response;
	}


	public static function encrypted_api_key($plaintext, $key)
	{
		$key = base64_decode($key);

		$ivlen          = openssl_cipher_iv_length($cipher = "AES-256-CBC");
		$iv             = openssl_random_pseudo_bytes($ivlen);
		$ciphertext_raw = openssl_encrypt($plaintext, $cipher, $key, $options = OPENSSL_RAW_DATA, $iv);
		$hmac           = hash_hmac('sha256', $ciphertext_raw, $key, $as_binary = true);
		$encrypted_data = base64_encode($iv . $hmac . $ciphertext_raw);

		// Store the encrypted API key in the WordPress options table
		update_option('encrypted_api_key', $encrypted_data);

		return $encrypted_data;
	}

	/**
	 * Process a post object to prepare it for API usage
	 *
	 * @param object $post The post object to process
	 * @return object The processed post object
	 */
	private static function process_post_data($post)
	{
		// Clean the content
		$clean_content = self::strip_gutenberg_blocks($post->body);
		$plain_text    = self::clean_text($clean_content);

		// Format the date
		$date                 = new \DateTime($post->published_date);
		$post->published_date = $date->format('Y-m-d');

		// Only add permalink if the post is publicly accessible
		$post_status = get_post_status($post->id);
		$post_type = get_post_type($post->id);

		// Skip URL if post type doesn't exist or can't be determined
		if ($post_type) {
			$post_type_obj = get_post_type_object($post_type);
			$is_public_post = false;

			// Check if post is published and post type is public
			if ($post_status === 'publish') {
				if ($post_type_obj && $post_type_obj->public) {
					$is_public_post = true;
				} else {
					// For non-public post types, check if they have publicly_queryable set to true
					// This handles cases like BetterDocs docs that might have custom access
					$is_public_post = $post_type_obj && $post_type_obj->publicly_queryable;
				}
			}

			if ($is_public_post) {
				// Check if the post is password protected
				$post_password = get_post_field('post_password', $post->id);
				if (empty($post_password)) {
					$permalink = get_permalink($post->id);
					// Verify the permalink is valid and not the site's 404 URL
					if ($permalink && $permalink !== home_url('404')) {
						// Test if the URL is actually accessible
						$post->url = $permalink;
					}
				}
			}
		}

		$post->body = $plain_text;

		return $post;
	}

	public static function get_posts_data($post_types, $index)
	{
		global $wpdb;

		$batch_size = BETTERDOCS_DOCS_PER_PAGE; // Number of posts to fetch per batch
		$offset     = $index * $batch_size;

		// Ensure post_types is an array
		if ( ! is_array( $post_types ) ) {
			$post_types = [ $post_types ];
		}

		// Create a placeholder string for the IN clause
		$placeholders = implode( ',', array_fill( 0, count( $post_types ), '%s' ) );

		$query = $wpdb->prepare("
			SELECT ID as id, post_title as title, post_content as body, post_date as published_date
			FROM {$wpdb->posts}
			WHERE post_type IN ($placeholders)
			AND post_status = 'publish'
			AND post_password = ''
			LIMIT %d OFFSET %d",
			array_merge( $post_types, [ $batch_size, $offset ] ) // Merge array values for placeholders
		);

		$posts = $wpdb->get_results($query);

		$batch_posts = [];

		// Process posts and add permalinks
		foreach ($posts as $post) {
			$batch_posts[] = self::process_post_data($post);
		}

		return $batch_posts;
	}

	/**
	 * Get posts data by IDs, filtering out already synced posts
	 *
	 * @param array $post_types Array of post types to fetch
	 * @return array Array of post data
	 */
	public static function get_posts_data_by_ids($post_types = [])
	{
		global $wpdb;

		// Retrieve both saved post IDs and error post IDs
		$saved_post_ids = get_option('saved_docs_post_ids', []);
		$error_post_ids = get_option('betterdocs_ai_chatbot_error_posts', []);

		// Merge both arrays and remove duplicates
		$post_ids = array_unique(array_merge($saved_post_ids, $error_post_ids));

		// If no post IDs or post types provided, return an empty array
		if (empty($post_ids) || !is_array($post_ids) || empty($post_types) || !is_array($post_types)) {
			return [];
		}

		// Get already synced post IDs
		$synced_post_ids = get_option('betterdocs_ai_chatbot_synced_posts', []);

		// Filter out already synced posts
		$new_post_ids = array_diff($post_ids, $synced_post_ids);

		// If there are no new posts to sync, return an empty array
		if (empty($new_post_ids)) {
			return [];
		}

		// Sanitize post IDs and post types
		$new_post_ids = array_map('intval', $new_post_ids);
		$post_types   = array_map('sanitize_text_field', $post_types);

		// Prepare placeholders
		$post_ids_placeholders   = implode(',', array_fill(0, count($new_post_ids), '%d'));
		$post_types_placeholders = implode(',', array_fill(0, count($post_types), '%s'));

		// Merge values for prepare()
		$prepare_values = array_merge($post_types, $new_post_ids);

		// Build the query
		$query = $wpdb->prepare(
			"SELECT ID as id, post_title as title, post_content as body, post_date as published_date
			FROM {$wpdb->posts}
			WHERE post_type IN ($post_types_placeholders)
			AND post_status = 'publish'
			AND post_password = ''
			AND ID IN ($post_ids_placeholders)",
			$prepare_values
		);

		// Fetch the posts
		$posts = $wpdb->get_results($query);

		// Process the posts found
		$batch_posts = [];

		foreach ($posts as $post) {
			$batch_posts[] = self::process_post_data($post);
		}

		return $batch_posts;
	}



	/**
	 * Fetch data for AI chatbot based on the operation type
	 *
	 * @param int $index The batch index
	 * @return array Array of post data
	 */
	public static function fetch_ai_chatbot_data($index)
	{
		$posts = [];
		$post_type = self::get_posttype();

		// Check if this is a sync for new docs
		if (get_option('enable_sync_embed_docs')) {
			// For "Sync New Docs", only get the posts that haven't been synced yet
			$posts = self::get_posts_data_by_ids($post_type);
		} else {
			// For "Embed All Docs", get all posts in batches
			$posts = self::get_posts_data($post_type, $index);
		}

		return $posts;
	}

	public static function get_openai_api_key()
	{
		$encripted_api_key = get_option('encrypted_api_key');
		$openai_api_key    = betterdocs()->settings->get('ai_chatbot_api_key', '');

		if (empty($openai_api_key)) {
			self::handle_error('Invalid API key', 'you must provide a valid openai api key.');

			return;
		}

		if (empty($encripted_api_key)) {
			$encripted_api_key = self::encrypted_api_key($openai_api_key, '0kXsYZmsHgvIB85miXWlq3nigYYD4PktOSVvOs3vlbA=');
		}

		return $encripted_api_key;
	}

	public static function get_domain()
	{
		$home_url   = home_url();
		$parsed_url = parse_url($home_url);
		$domain     = isset($parsed_url['host']) ? $parsed_url['host'] : '';

		return preg_replace('/^www\./', '', $domain);
	}


	public static function get_embed_model()
	{
		$openai_embedding_model = betterdocs()->settings->get('ai_chatbot_embed_model', '');

		return $openai_embedding_model;
	}

	public static function get_chat_model()
	{
		$openai_chat_model = betterdocs()->settings->get('ai_chatbot_chat_model', '');

		return $openai_chat_model;
	}

	public static function get_posttype()
	{
		$post_types = betterdocs()->settings->get( 'ai_chatbot_allowed_post_types' );
		return AIChatbot::allowed_post_types($post_types);
	}

	public static function allowed_post_types($arg)
	{
		$post_types = betterdocs()->settings->get( 'ai_chatbot_allowed_post_types' );
		if (in_array('all', $arg)) {
			$post_types = AIChatbot::get_available_post_types();
			$post_types = array_keys($post_types);
		}

		return $post_types;
	}

	public static function create_token($licenseKey)
	{

		$url = self::$api_url . '/token/new';

		$body = [
			"license" => $licenseKey,
			"item_id" => BETTERDOCS_CHATBOT_SL_ITEM_ID,
			"url"     => self::get_domain(),
			"version" => PLUGIN_VERSION
		];

		$response = wp_remote_post($url, [
			'method'  => 'POST',
			'body'    => wp_json_encode($body),
			'headers' => [
				'Content-Type' => 'application/json',
			],
			'timeout' => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
		]);

		if (is_wp_error($response)) {
			return 'Error: ' . $response->get_error_message();
			self::handle_error('Failed to create access token', $response);
		}

		$response_body = wp_remote_retrieve_body($response);

		return json_decode($response_body);
	}

	/**
	 * Create a WordPress site collection for the AI chatbot
	 *
	 * @param string $bearer_token The bearer token for API authentication
	 * @param bool $delete_existing Whether to delete the existing collection before creating a new one
	 */
	public static function create_wp_site_collection($bearer_token, $delete_existing = true)
	{
		// Only delete the existing collection if $delete_existing is true
		if ($delete_existing) {
			// Try to delete the existing collection
			self::delete_wp_site_collection($bearer_token);
		}

		// Try to create the new collection
		$create_response = self::send_create_request($bearer_token);

		// Handle error during creation
		if (is_wp_error($create_response)) {
			self::handle_error('Failed to create site collection', $create_response);
			return;
		}

		// Parse the response body
		$response_body = wp_remote_retrieve_body($create_response);
		$response_data = json_decode($response_body, true);

		// Check for a successful response
		if (isset($response_data['status']) && $response_data['status'] === 'success') {
			$body_data = [
				'domain'                 => self::get_domain(),
				'openai_embedding_model' => self::get_embed_model(),
			];
			update_option('model_body_data', $body_data);
			update_option('is_ai_chatbot_bg_complete', 'running');

		} else {
			self::handle_error('Failed to create site collection: Invalid response', $response_data);
		}
	}

	// Sends the request to delete the WP site collection
	public static function delete_wp_site_collection($bearer_token)
	{
		if (!self::is_valid_token($bearer_token)) {
			return;
		}

		$url  = self::$api_url . '/v1/wp-site-collection';
		$data = ['domain' => self::get_domain()];

		$response = wp_remote_request($url, [
			'method'      => 'DELETE',
			'body'        => wp_json_encode($data),
			'headers'     => [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer ' . $bearer_token
			],
			'timeout'     => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
			'data_format' => 'body',
		]);

		if (is_wp_error($response)) {
			// Return the error to handle it gracefully
			return new \WP_Error('request_failed', 'Request Error: ' . $response->get_error_message());
		}

		return $response;
	}

	// Sends the request to create the WP site collection
	private static function send_create_request($bearer_token)
	{
		$url  = self::$api_url . '/v1/wp-site-collection';
		$data = [
			'domain'      => self::get_domain(),
			'openai_data' => [
				'openai_embedding_model' => self::get_embed_model()
			]
		];

		return wp_remote_post($url, [
			'method'      => 'POST',
			'body'        => wp_json_encode($data),
			'headers'     => [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer ' . $bearer_token
			],
			'timeout'     => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
			'data_format' => 'body',
		]);
	}

	// Centralized error handler for cleaner code
	private static function handle_error($message, $response)
	{
		update_option('is_ai_chatbot_bg_complete', 'failed_create_collection');
		update_option('ai_chatbot_notice_error_message', $message . ': ' . self::get_the_error_message(json_encode($response)));
		update_option('show_ai_chatbot_process_notices', true);
	}

	public static function is_valid_token($token)
	{
		if (empty($token) || !is_string($token) || $token === 'Invalid License') {
			return false;
		}

		return true;
	}


	public static function check_openai_quota($api_key)
	{
		$url      = 'https://api.openai.com/v1/chat/completions';
		$response = wp_remote_post($url, [
			'headers' => [
				'Authorization' => 'Bearer ' . $api_key,
				'Content-Type'  => 'application/json',
			],
			'body'    => json_encode([
				'model'    => 'gpt-4',
				'messages' => [['role' => 'system', 'content' => 'ok']]
			]),
			'timeout' => 300,
		]);

		if (is_wp_error($response)) {
			return json_encode([
				'error' => [
					'message' => "API request failed: " . $response->get_error_message(),
					'type'    => 'request_error'
				]
			]);
		}

		$status_code  = wp_remote_retrieve_response_code($response);
		$body         = wp_remote_retrieve_body($response);
		$decoded_body = json_decode($body, true);

		if ($status_code === 200) {
			return json_encode($decoded_body);  // Return the decoded response for success
		}

		// If there is an error in the response body (e.g., quota exceeded)
		$error_message = isset($decoded_body['error']['message']) ? $decoded_body['error']['message'] : 'Unknown error';

		return json_encode([
			'error' => [
				'message' => $error_message,
				'details' => $decoded_body
			]
		]);
	}


	public static function check_openai_key_status($api_key)
	{
		// Make a request to the models endpoint to check if the API key is valid
		$response = wp_remote_get('https://api.openai.com/v1/models', [
			'headers' => [
				'Authorization' => 'Bearer ' . $api_key,
			],
		]);


		if (is_wp_error($response)) {
			// Return an error if the request fails
			return 'API request failed: ' . $response->get_error_message();
		}

		// Check the HTTP status code for success (200)
		$status_code = wp_remote_retrieve_response_code($response);
		if ($status_code === 200) {
			// If successful, the API key is valid
			// Check the response headers for usage or quota information
			$headers = wp_remote_retrieve_headers($response);

			// Example: Check the `openai-usage` header for quota information (if provided by OpenAI)
			$usage = isset($headers['openai-usage']) ? $headers['openai-usage'] : 'No usage data available';

			return "API Key is valid. Usage data: " . $usage;
		} else {
			// If the status code is not 200, there might be an issue with the API key or quota
			$body = wp_remote_retrieve_body($response);

			return "Error: " . $body;
		}
	}

	public static function get_total_posts_count()
    {
        global $wpdb;
        $post_types = self::get_posttype();

        if (!is_array($post_types)) {
            $post_types = [$post_types];
        }

        $placeholders = implode(',', array_fill(0, count($post_types), '%s'));
        $query = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->posts}
             WHERE post_type IN ($placeholders)
             AND post_status = 'publish'
             AND post_password = ''",
            $post_types
        );

        return (int) $wpdb->get_var($query);
    }

	/**
	 * Embed WordPress posts in the AI chatbot collection
	 *
	 * @param int $batch_index The batch index to process
	 * @param string $bearer_token The bearer token for API authentication
	 * @param bool $is_sync_new_docs Whether this is a sync for new docs only (true) or a full embed (false)
	 * @return array Status information about the operation
	 */
	public static function embed_wp_post_collection($batch_index, $bearer_token, $is_sync_new_docs = false)
	{
		try {
			// Update total posts count
			update_option('betterdocs_ai_chatbot_total_posts', AIChatbot::get_total_posts_count());

			// Fetch posts data for this batch
			// For "Sync New Docs", this will only return posts that haven't been synced yet
			$posts = self::fetch_ai_chatbot_data($batch_index);

			// Get the post IDs that are being processed
			$post_ids = [];
			foreach ($posts as $post) {
				$post_ids[] = $post->id;
			}

			if (empty($posts)) {
				if ($is_sync_new_docs) {
					return [
						'status' => 'success',
						'message' => 'No new posts found to sync',
						'batch_index' => $batch_index,
						'success_count' => 0,
						'total_processed' => 0,
						'success_posts' => []
					];
				} else {
					return [
						'status' => 'error',
						'message' => 'No posts found for batch ' . $batch_index
					];
				}
			}

			// Prepare API request data
			$api_data = [
				"posts" => $posts,
				"domain" => self::get_domain(),
				"openai_data" => [
					"openai_api_key" => self::get_openai_api_key(),
					"openai_embedding_model" => self::get_embed_model()
				]
			];

			// Make the API request
			$response = wp_remote_post(self::$api_url . '/v1/embed-wp-post', [
				'method' => 'POST',
				'body' => json_encode($api_data),
				'headers' => [
					'Content-Type' => 'application/json',
					'Authorization' => 'Bearer ' . $bearer_token
				],
				'timeout' => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT
			]);

			if (is_wp_error($response)) {
				$error_message = 'API Request Failed: ' . $response->get_error_message();
				throw new \Exception($error_message);
			}

			$response_code = wp_remote_retrieve_response_code($response);
			$body = json_decode(wp_remote_retrieve_body($response), true);

			if ($response_code !== 200) {
				$error_message = 'API Error: ' . ($body['message'] ?? 'Unknown error');
				throw new \Exception($error_message);
			}

			// Extract successful post IDs from response
			$success_posts = $body['success_posts'] ?? [];
			$error_posts = $body['error_posts'] ?? [];

			if (!empty($success_posts)) {
				$existing = get_option('betterdocs_ai_chatbot_synced_posts', []);
				$updated = array_unique(array_merge($existing, $success_posts));
				update_option('betterdocs_ai_chatbot_synced_posts', $updated);
			}

			if (!empty($error_posts)) {
				$existing_error = get_option('betterdocs_ai_chatbot_error_posts', []);
				$updated_error = array_unique(array_merge($existing_error, $error_posts));
				update_option('betterdocs_ai_chatbot_error_posts', $updated_error);
			}

			return [
				'status' => 'success',
				'batch_index' => $batch_index,
				'success_count' => count($success_posts),
				'total_processed' => count($posts),
				'success_posts' => $success_posts
			];

		} catch (\Exception $e) {
			return [
				'status' => 'error',
				'batch_index' => $batch_index,
				'message' => $e->getMessage()
			];
		}
	}


	/**
	 * Embed a newly created WordPress post in the AI chatbot collection
	 *
	 * @param object $post The post object to embed
	 * @param string $bearer_token The bearer token for API authentication
	 * @return mixed Response data or WP_Error
	 */
	public static function embed_new_create_wp_post_collection($post, $bearer_token)
	{
		// If no bearer token is provided, try to get it from options
		if (empty($bearer_token)) {
			$bearer_token = get_option('ai_chatbot_request_bareer_token');
		}

		if (!self::is_valid_token($bearer_token)) {
			return new \WP_Error('invalid_token', 'Invalid or missing API token');
		}

		$url = self::$api_url . '/v1/embed-wp-post';

		$data = [
			"posts"       => [$post],
			"domain"      => self::get_domain(),
			"openai_data" => [
				"openai_api_key"         => self::get_openai_api_key(),
				"openai_embedding_model" => self::get_embed_model()
			]
		];

		$response = wp_remote_post($url, [
			'method'      => 'POST',
			'body'        => wp_json_encode($data),
			'headers'     => [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer ' . $bearer_token
			],
			'timeout'     => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
			'data_format' => 'body',
		]);

		if (is_wp_error($response)) {
			return new \WP_Error('request_failed', 'Request Error: ' . $response->get_error_message());
		}

		$status_code = wp_remote_retrieve_response_code($response);
		$body = wp_remote_retrieve_body($response);
		$response_data = json_decode($body, true);

		if ($status_code == 200) {
			// Add the post ID to the synced posts list
			if (isset($post->ID) && !empty($post->ID)) {
				$existing_synced = get_option('betterdocs_ai_chatbot_synced_posts', []);
				if (!in_array($post->ID, $existing_synced)) {
					$existing_synced[] = $post->ID;
					update_option('betterdocs_ai_chatbot_synced_posts', $existing_synced);
				}
			}

			return $response_data;
		} else {
			// If there was an error, add the post ID to the error posts list
			if (isset($post->ID) && !empty($post->ID)) {
				$existing_errors = get_option('betterdocs_ai_chatbot_error_posts', []);
				if (!in_array($post->ID, $existing_errors)) {
					$existing_errors[] = $post->ID;
					update_option('betterdocs_ai_chatbot_error_posts', $existing_errors);
				}
			}

			return new \WP_Error('http_error', 'HTTP Error: ' . $status_code . ' Response: ' . $body);
		}
	}

	/**
	 * Update an existing WordPress post in the AI chatbot collection
	 *
	 * @param object $post The post object to update
	 * @param string $bearer_token The bearer token for API authentication
	 * @return mixed Response data or WP_Error
	 */
	public static function update_wp_post_collection($post, $bearer_token)
	{
		// If no bearer token is provided, try to get it from options
		if (empty($bearer_token)) {
			$bearer_token = get_option('ai_chatbot_request_bareer_token');
		}

		if (!self::is_valid_token($bearer_token)) {
			return new \WP_Error('invalid_token', 'Invalid or missing API token');
		}

		$url = self::$api_url . '/v1/embed-wp-post';

		$data = [
			"posts"       => [$post],
			"domain"      => self::get_domain(),
			"openai_data" => [
				"openai_api_key"         => self::get_openai_api_key(),
				"openai_embedding_model" => self::get_embed_model()
			]
		];

		$response = wp_remote_request($url, [
			'method'      => 'PUT',
			'body'        => wp_json_encode($data),
			'headers'     => [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer ' . $bearer_token
			],
			'timeout'     => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
			'data_format' => 'body',
		]);

		if (is_wp_error($response)) {
			error_log('Error updating post: ' . $response->get_error_message());
			return new \WP_Error('request_failed', 'Request Error: ' . $response->get_error_message());
		}

		$status_code = wp_remote_retrieve_response_code($response);
		$body = wp_remote_retrieve_body($response);
		$response_data = json_decode($body, true);

		if ($status_code == 200) {
			// Add the post ID to the synced posts list if it's not already there
			if (isset($post->ID) && !empty($post->ID)) {
				$existing_synced = get_option('betterdocs_ai_chatbot_synced_posts', []);
				if (!in_array($post->ID, $existing_synced)) {
					$existing_synced[] = $post->ID;
					update_option('betterdocs_ai_chatbot_synced_posts', $existing_synced);
					error_log('Added post ID ' . $post->ID . ' to synced posts list');
				}
			}

			return $response_data;
		} else {
			// If there was an error, add the post ID to the error posts list
			if (isset($post->ID) && !empty($post->ID)) {
				$existing_errors = get_option('betterdocs_ai_chatbot_error_posts', []);
				if (!in_array($post->ID, $existing_errors)) {
					$existing_errors[] = $post->ID;
					update_option('betterdocs_ai_chatbot_error_posts', $existing_errors);
					error_log('Added post ID ' . $post->ID . ' to error posts list');
				}
			}

			error_log('HTTP Error updating post: ' . $status_code . ' Response: ' . $body);
			return new \WP_Error('http_error', 'HTTP Error: ' . $status_code . ' Response: ' . $body);
		}
	}


	public static function query_post_to_server($query, $email, $lang = '') {
		global $wpdb;

		try {
			// Validate essential configurations
			$domain = self::get_domain();
			$api_key = self::get_openai_api_key();
			$chat_model = self::get_chat_model();
			$embed_model = self::get_embed_model();
			$enable_ai_chatbot = function_exists('betterdocs') ? betterdocs()->settings->get('enable_ai_chatbot', false) : false;
			$chatbot_license = get_option('betterdocs_chatbot_software__license_status');
			$pro_license = get_option('betterdocs_pro_software__license_status');

			// Configuration validation
			if (empty($domain) || empty($api_key) || empty($chat_model) || empty($embed_model)) {
				throw new \Exception(__('One or more required configurations are missing.', 'betterdocs-ai-chatbot'));
			}

			if (!$enable_ai_chatbot) {
				throw new \Exception(__('AI Chatbot is not enabled. Please configure settings.', 'betterdocs-ai-chatbot'));
			}

			if ($chatbot_license !== 'valid' || $pro_license !== 'valid') {
				throw new \Exception(__('Activate licenses for BetterDocs PRO & AI Chatbot Addon.', 'betterdocs-ai-chatbot'));
			}

			// Prepare API request
			$url = self::$api_url . '/v1/query-post';
			if (!empty($lang)) {
				$url .= "?lang=" . urlencode($lang);
			}
			$token = get_option('ai_chatbot_request_bareer_token');
			$session_id = $_COOKIE['chatbot_session_id'] ?? null;

			// Retrieve conversation data
			$conversation_data = Helper::get_conversation_tokens_data($session_id);
			$prev_index = isset($conversation_data['previous_context_index']) && is_array($conversation_data['previous_context_index']) && count($conversation_data['previous_context_index']) > 0
				? $conversation_data['previous_context_index']
				: [];

			$data = [
				"query" => $query,
				"domain" => $domain,
				"conversation" => $conversation_data['conversation'] ?? [],
				"previous_context_index" => $prev_index,
				"openai_data" => [
					"openai_api_key" => $api_key,
					"openai_chat_model" => $chat_model,
					"openai_embedding_model" => $embed_model
				]
			];

			// Make API request with retry on timeout
			$response = wp_remote_post($url, [
				'method' => 'POST',
				'body' => wp_json_encode($data),
				'headers' => [
					'Content-Type' => 'application/json',
					'Authorization' => 'Bearer ' . $token
				],
				'timeout' => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
				'data_format' => 'body',
			]);

			// Handle timeout retry
			if (is_wp_error($response) && strpos($response->get_error_message(), 'cURL error 28') !== false) {
				$response = wp_remote_post($url, [
					'method' => 'POST',
					'body' => wp_json_encode($data),
					'headers' => [
						'Content-Type' => 'application/json',
						'Authorization' => 'Bearer ' . $token
					],
					'timeout' => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
					'data_format' => 'body',
				]);
			}

			if (is_wp_error($response)) {
				throw new \Exception(__('API communication failed: ', 'betterdocs-ai-chatbot') . $response->get_error_message());
			}

			$status_code = wp_remote_retrieve_response_code($response);
			$body = wp_remote_retrieve_body($response);
			$decoded_body = json_decode($body, true);

			if ($status_code !== 200) {
				$error_msg = $decoded_body['message'] ?? __('Unknown API error', 'betterdocs-ai-chatbot');
				throw new \Exception(sprintf(__('API Error (%d): %s', 'betterdocs-ai-chatbot'), $status_code, $error_msg));
			}

			// Handle session ID
			if (!$session_id) {
				$session_id = uniqid('session_', true);
				setcookie('chatbot_session_id', $session_id, time() + (30 * 24 * 60 * 60), '/');
			}

			// Prepare conversation data
			$conversations_table = "{$wpdb->prefix}betterdocs_ai_chatbot_conversations";
			$existing_conversation = $wpdb->get_var(
				$wpdb->prepare("SELECT conversation FROM $conversations_table WHERE session_id = %s", $session_id)
			);

			$new_conversation_entries = array_map(function ($entry) {
				return [
					'id' => md5(uniqid(rand(), true)),
					'text' => $entry['content'],
					'type' => $entry['role'] === 'user' ? 'sent' : 'received',
					'timestamp' => current_time('mysql')
				];
			}, $decoded_body['conversation']);

			// Start database transaction
			$wpdb->query('START TRANSACTION');

			try {
				if ($existing_conversation) {
					$existing_conversation_data = json_decode($existing_conversation, true) ?: [];

					if (!empty($new_conversation_entries)) {
						$last_sent = $new_conversation_entries[count($new_conversation_entries) - 2];
						$last_received = $new_conversation_entries[count($new_conversation_entries) - 1];
						$last_received['timestamp'] = current_time('mysql');

						$existing_conversation_data[] = $last_sent;
						$existing_conversation_data[] = $last_received;

						$update_result = $wpdb->update(
							$conversations_table,
							[
								'conversation' => json_encode($existing_conversation_data),
								'updated_at' => current_time('mysql')
							],
							['session_id' => $session_id]
						);

						if ($update_result === false) {
							throw new \Exception(__('Failed to update conversation', 'betterdocs-ai-chatbot'));
						}
					}
				} else {
					$initial_message = [[
						'id' => md5(uniqid(rand(), true)),
						'text' => betterdocs()->settings->get('ai_chatbot_welcome_message'),
						'type' => 'received',
						'timestamp' => current_time('mysql')
					]];

					$new_conversation_entries = array_merge($initial_message, $new_conversation_entries);

					$insert_result = $wpdb->insert($conversations_table, [
						'session_id' => $session_id,
						'conversation' => json_encode($new_conversation_entries),
						'created_at' => current_time('mysql'),
						'updated_at' => current_time('mysql'),
					]);

					if ($insert_result === false) {
						throw new \Exception(__('Failed to create conversation', 'betterdocs-ai-chatbot'));
					}
				}

				// Commit transaction
				$wpdb->query('COMMIT');

			} catch (\Exception $e) {
				$wpdb->query('ROLLBACK');
				throw new \Exception($e->getMessage());
			}

			// Save conversation tokens if email exists
			if (!empty($email)) {
				Helper::save_conversation_tokens_data($email, $body);
			}

			return [
				'success' => true,
				'message' => __('Request successful', 'betterdocs-ai-chatbot'),
				'data' => [
					'conversation' => $decoded_body['conversation'],
					'context' => $decoded_body['context'] ?? [],
					'previous_context_index' => $decoded_body['previous_context_index'] ?? []
				]
			];

		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => $e->getMessage(),
				'data' => [
					'conversation' => [
						[
							'content' => $e->getMessage(),
							'role' => 'assistant',
							'timestamp' => current_time('mysql')
						]
					]
				]
			];
		}
	}



	public static function get_docs_count($post_types)
	{
		global $wpdb;

		// Ensure $post_types is an array
		if (!is_array($post_types)) {
			$post_types = [$post_types];
		}

		// Prepare placeholders for SQL query
		$placeholders = implode(',', array_fill(0, count($post_types), '%s'));

		$query = $wpdb->prepare(
			"SELECT COUNT(*) as post_count
			FROM {$wpdb->posts}
			WHERE post_type IN ($placeholders)
			AND post_status = 'publish'",
			...$post_types
		);

		$docs_count = $wpdb->get_var($query);

		return $docs_count;
	}


	/**
	 * Send data to the AI chatbot API
	 *
	 * @param bool $delete_existing Whether to delete the existing collection before creating a new one
	 */
	public static function send_data($delete_existing = true)
	{
		$token = get_option('ai_chatbot_request_bareer_token');
		$key   = get_option('betterdocs_chatbot_software__license');

		if (empty($key)) {
			self::handle_error('Missing license key', 'Please enter a valid license key to proceed.');
			return;
		}

		// Step 1: Revoke the old token if it exists and is valid
		if (!empty($token) && is_string($token) && $token !== 'Invalid License') {
			$revoke_response = self::revoke_token($token);
			if (is_wp_error($revoke_response)) {
				error_log('Failed to revoke token: ' . $revoke_response->get_error_message());
			}
		} else {
			update_option('ai_chatbot_request_bareer_token', '');
		}

		// Step 2: Create a new token using the license key
		$access = self::create_token($key);
		if (is_wp_error($access)) {
			self::handle_error('Failed to create new token', $access);
			return;  // Stop execution on error
		}

		if (isset($access->status) && $access->status == 'error') {
			self::handle_error('Failed to create new token', $access);
			return;
		}

		// Save the new token
		if (isset($access->token)) {
			update_option('ai_chatbot_request_bareer_token', $access->token);
			$token = $access->token;
		}

		// Step 3: Update notices and create the WP site collection
		// Pass the $delete_existing parameter to determine whether to delete the existing collection
		self::create_wp_site_collection($token, $delete_existing);
		update_option('show_ai_chatbot_process_notices', true);
		update_option('ai_chatbot_notice_dismissed', false);
	}


	// Revoke the token using the revoke endpoint
	public static function revoke_token($token)
	{
		if (!self::is_valid_token($token)) {
			return;
		}

		$url  = self::$api_url . '/v1/token/revoke';
		$data = ['token' => $token];

		$response = wp_remote_post($url, [
			'method'      => 'POST',
			'body'        => wp_json_encode($data),
			'headers'     => [
				'Content-Type'  => 'application/json',
				'Authorization' => 'Bearer ' . $token
			],
			'timeout'     => BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT,
			'data_format' => 'body',
		]);

		if (is_wp_error($response)) {
			return new \WP_Error('request_failed', 'Failed to revoke token: ' . $response->get_error_message());
		}

		return $response;
	}

	public static function get_available_post_types() {
		$post_types = get_post_types( [
			'public'   => true,
			'show_ui'  => true,
		], 'objects' );

		// Exclude specific post types
		$excluded_post_types = [
			'elementor_library',  // Elementor Templates
			'wp_block',           // Reusable Blocks
			'attachment',         // Media
			'nav_menu_item',      // Navigation Menus
			'custom_css',         // Custom CSS
			'customize_changeset', // Customizer Changesets
			'oembed_cache',       // oEmbed Cache
			'user_request',       // User Requests (Privacy)
			'wp_template',        // Block Templates
			'wp_template_part',   // Block Template Parts
			'e-floating-buttons',   // Floating Elements
		];

		$options = [];
		foreach ( $post_types as $post_type ) {
			if ( ! in_array( $post_type->name, $excluded_post_types, true ) ) {
				$options[ $post_type->name ] = $post_type->label;
			}
		}

		$options[ 'betterdocs_faq' ] = __( 'BetterDocs FAQ', 'betterdocs-ai-chatbot' );

		return GlobalFields::normalize_fields( $options );
	}
}
