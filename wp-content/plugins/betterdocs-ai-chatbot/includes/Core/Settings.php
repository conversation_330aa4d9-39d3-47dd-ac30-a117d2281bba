<?php

namespace WPDeveloper\BetterDocsChatbot\Core;

use WPDeveloper\BetterDocs\Admin\Builder\GlobalFields;
use WPDeveloper\BetterDocs\Admin\Builder\Rules;
use WPDeveloper\BetterDocs\Utils\Database;
use WPDeveloper\BetterDocsPro\Core\Settings as FreeSettings;
use WPDeveloper\BetterDocsChatbot\Core\AIChatbot;

class Settings extends FreeSettings {
	public function __construct( Database $database ) {
		parent::__construct( $database );

		add_filter( 'betterdocs_settings_ai_chatbot_fields', [ $this, 'ai_chatbot_fields' ] );
		add_filter( 'betterdocs_license_fields', [ $this, 'chatbot_license_fields' ] );
		add_filter( 'betterdocs_settings_args', [ $this, '_args' ], 12 );
	}

	public function _args( $args ){
		$args['submit']['rules'] = array_merge( $args['submit']['rules'], [ Rules::is( 'config.active', 'tab-ai-chatbot', true ) ]);

		return $args;
	}

	public function enqueue( $hook ) {
		if ( $hook !== 'betterdocs_page_betterdocs-settings' ) {
			return;
		}
		wp_enqueue_media();

		parent::enqueue( $hook );

		betterdocs_pro()->assets->localize( 'betterdocs-pro-settings', 'betterdocsProAdminSettings', [
			'ai_chabot_url' => admin_url( 'admin.php?page=betterdocs-ai-chatbot' )
		] );

		betterdocs_chatbot()->assets->enqueue( 'betterdocs-ai-chatbot-history', 'css/betterdocs.chatbot-history.min.css' );
	}

	/**
	 * A list of default settings.
	 *
	 * @param array $defaults
	 *
	 * @return array
	 */
	public function _default( $defaults ) {
		$_chatbot_defaults = [
			'ai_chatbot_api_key'          => '',
			'ai_chatbot_embed_model'      => '',
			'ai_chatbot_chat_model'       => '',
			'ai_chatbot_title'       	  => __('We typically reply in a few minutes', 'betterdocs-ai-chatbot'),
			'ai_chatbot_subtitle'         => __('We help your business grow by connecting you to your customers.', 'betterdocs-ai-chatbot'),
			'ai_chatbot_welcome_message'  => sprintf(
				__('Hi, welcome to BetterDocs 👋 You’re speaking with AI Agent - I’m here to answer your questions & help you out.', 'betterdocs-ai-chatbot'),
				get_bloginfo( 'name' ) // This dynamically inserts the site title
			),
			'ai_chatbot_icon' 			=> [],
			'ai_chatbot_allowed_post_types' => [ 'docs' ],

		];

		return array_merge( $defaults, $_chatbot_defaults );
	}

	/**
	 * A list of default settings (ONLY PRO)
	 *
	 * @return array
	 */
	public function get_pro_defaults() {
		return $this->_default( [] );
	}

	public function ai_chatbot_fields( $args ) {
		//var_dump($args); die;
		$args['fields']['ai_chatbot_title'] = [
			'name'           => 'ai_chatbot_title',
			'type'           => 'textarea',
			'label'          => __( 'Chatbot Title', 'betterdocs-ai-chatbot' ),
			'default'        => __('We typically reply in a few minutes', 'betterdocs-ai-chatbot'),
			'priority'       => 10,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,

		];
		
		$args['fields']['ai_chatbot_subtitle'] = [
			'name'           => 'ai_chatbot_subtitle',
			'type'           => 'textarea',
			'label'          => __( 'Chatbot Sub Title', 'betterdocs-ai-chatbot' ),
			'default'        => __('We help your business grow by connecting you to your customers.', 'betterdocs-ai-chatbot'),
			'priority'       => 11,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,

		];

		$args['fields']['ai_chatbot_welcome_message'] = [
			'name'           => 'ai_chatbot_welcome_message',
			'type'           => 'textarea',
			'label'          => __( 'Welcome Message', 'betterdocs-ai-chatbot' ),
			'default'        => sprintf(
								__('Hi, welcome to BetterDocs 👋 You’re speaking with AI Agent - I’m here to answer your questions & help you out.', 'betterdocs-ai-chatbot'),
								get_bloginfo('name') // This dynamically inserts the site title
							),
			'priority'       => 12,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,

		];

		$args['fields']['ai_chatbot_icon'] = [
			'name'     => 'ai_chatbot_icon',
			'type'     => 'media',
			'value'    => '',
			'label'    => __( 'Upload Chatbot Icon', 'betterdocs-ai-chatbot' ),
			'priority' => 12,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,

		];

		$args['fields']['ai_chatbot_api_key'] = [
			'name'           => 'ai_chatbot_api_key',
			'type'           => 'text',
			'label'          => __( 'API Key*', 'betterdocs-ai-chatbot' ),
			'placeholder'    => __( 'API Key', 'betterdocs-ai-chatbot' ),
			'label_subtitle' => __( 'Check out this <a target="_blank" href="' . esc_url( 'https://betterdocs.co/docs/ai-chatbot/' ) . '">documentation</a> to find out to generate your OpenAI API Key', 'betterdocs-ai-chatbot' ),
			'default'        => '',
			'priority'       => 13,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,

		];

		$args['fields']['ai_chatbot_embed_model'] = [
			'name'            => 'ai_chatbot_embed_model',
			'type'            => 'embed_model_select',
			'label'           => __( 'Embed Model*', 'betterdocs-ai-chatbot' ),
			'label_subtitle'  => __( 'Select to integrate a conversational AI model for real-time interactions within your application.', 'betterdocs-ai-chatbot' ),
			'priority'        => 15,
			'multiple'        => false,
			'default'         => [],
			'options_tooltip' => true,
			'options'         => GlobalFields::normalize_fields( [
				'text-embedding-3-large' => [
					'value'   => 'text-embedding-3-large',
					'label'   => __( 'text-embedding-3-large', 'betterdocs-ai-chatbot' ),
					'tooltip' => __( 'The most accurate model for understanding and responding to complex questions', 'betterdocs-ai-chatbot' )
				],
				'text-embedding-3-small' => [
					'value'   => 'text-embedding-3-small',
					'label'   => __( 'text-embedding-3-small', 'betterdocs-ai-chatbot' ),
					'tooltip' => __( 'A fast and efficient model for handling document-based queries with improved accuracy', 'betterdocs-ai-chatbot' )
				],
				'text-embedding-ada-002' => [
					'value'   => 'text-embedding-ada-002',
					'label'   => __( 'ada v2', 'betterdocs-ai-chatbot' ),
					'tooltip' => __( 'A reliable and cost-effective model for general document understanding tasks.', 'betterdocs-ai-chatbot' )
				]
			] ),
			'rules'           => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),

		];

		$args['fields']['ai_chatbot_chat_model'] = [
			'name'     => 'ai_chatbot_chat_model',
			'type'     => 'select',
			'label'    => __( 'Chat Model*', 'betterdocs-ai-chatbot' ),
			'priority' => 20,
			'multiple' => false,
			'default'  => [],
			'options'  => GlobalFields::normalize_fields( [
				'gpt-4o'      => 'GPT-4o',
				'gpt-4o-mini' => 'GPT-4o Mini'
			] ),
			'rules'    => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),

		];

		$posts = AIChatbot::get_available_post_types();
		
		$all_post_type = [
			'all' => [
				'value' => 'all',
				'label' => 'All'
			]
		];

		$posts = array_merge($all_post_type, $posts);

		$args['fields']['ai_chatbot_allowed_post_types'] = [
			'name'     => 'ai_chatbot_allowed_post_types',
			'type'     => 'checkbox-select',
			'label'    => __( 'Retrievable Post Types', 'betterdocs-ai-chatbot' ),
			'placeholder' => __( 'Select retrievable post types', 'betterdocs-ai-chatbot' ),
			'label_subtitle' => __( 'Choose which post types the AI Chatbot can retrieve from, tailoring its responses to your website’s content structure.', 'betterdocs-ai-chatbot' ),
			'priority' => 1,
			'multiple' => true,
			'search'   => true,
			'is_pro'   => true,
			'default'  => [ 'docs' ],
			'options'  => $posts,
			'priority'       => 21,
			'rules'          => Rules::logicalRule( [
				Rules::is( 'enable_ai_chatbot', true ),
				Rules::is( 'enable_ai_chatbot', get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ),
			], 'and' ),
			'x'              => true,
			'filterValue'    => 'all'

		];


		if ( get_option( 'betterdocs_chatbot_software__license_status' ) === 'valid' && get_option( 'betterdocs_pro_software__license_status' ) === 'valid' ) {
			$args['fields']['ai_chatbot_chat_submit'] = [
				'name'     => 'ai_chatbot_chat_submit',
				'text'     => [
					'normal'  => __( 'Save', 'betterdocs-ai-chatbot' ),
					'saved'   => __( 'Save', 'betterdocs-ai-chatbot' ),
					'loading' => __( 'Saving...', 'betterdocs-ai-chatbot' )
				],
				'type'     => 'button',
				'priority' => 30,
				'ajax'     => [
					'on'   => 'click',
					'api'  => '/betterdocs/v1/save-aichatbot-settings',
					'data' => [
						'enable_ai_chatbot'      => '@enable_ai_chatbot',
						'ai_chatbot_api_key'     => '@ai_chatbot_api_key',
						'ai_chatbot_embed_model' => '@ai_chatbot_embed_model',
						'ai_chatbot_chat_model'  => '@ai_chatbot_chat_model',
						'ai_chatbot_title'  	 => '@ai_chatbot_title',
						'ai_chatbot_subtitle'  	 => '@ai_chatbot_subtitle',
						'ai_chatbot_welcome_message'  => '@ai_chatbot_welcome_message',
						'ai_chatbot_icon'    => '@ai_chatbot_icon',
						'ai_chatbot_allowed_post_types'  => '@ai_chatbot_allowed_post_types',
					],
					'swal' => [
						'text'      => __( 'Settings saved successfully.', 'betterdocs-ai-chatbot' ),
						'icon'      => 'success',
						'autoClose' => 2000
					],
					'reload' => true
				]
			];
		}

		// $args['fields']['enable_ai_chatbot']['disabled'] = get_option( 'betterdocs_chatbot_software__license_status', '' ) !== 'valid';

		$args['fields']['enable_ai_chatbot']['disabled'] = ! ( get_option( 'betterdocs_pro_software__license_status', false ) === 'valid' && get_option( 'betterdocs_chatbot_software__license_status', false ) === 'valid' );

		return $args;
	}

	private function get_available_post_types() {
		$post_types = get_post_types( [
			'public'   => true,
			'show_ui'  => true,
		], 'objects' );
	
		// Exclude specific post types
		$excluded_post_types = [
			'elementor_library',  // Elementor Templates
			'wp_block',           // Reusable Blocks
			'attachment',         // Media
			'nav_menu_item',      // Navigation Menus
			'custom_css',         // Custom CSS
			'customize_changeset', // Customizer Changesets
			'oembed_cache',       // oEmbed Cache
			'user_request',       // User Requests (Privacy)
			'wp_template',        // Block Templates
			'wp_template_part',   // Block Template Parts
			'e-floating-buttons',   // Floating Elements
		];
	
		$options = [];
		foreach ( $post_types as $post_type ) {
			if ( ! in_array( $post_type->name, $excluded_post_types, true ) ) {
				$options[ $post_type->name ] = $post_type->label;
			}
		}

		$options[ 'betterdocs_faq' ] = __( 'BetterDocs FAQ', 'betterdocs-ai-chatbot' );
	
		return $this->normalize_options( $options );
	}

	public function chatbot_license_fields( $fields ) {
		$fields['betterdocs_chatbot_licnese'] = [
			'name'    => 'betterdocs_chatbot_licnese',
			'type'    => 'action',
			'action'  => 'betterdocs_chatbot_licnese',
			'label'   => __( 'Chatbot License', 'betterdocs-ai-chatbot' ),
			'logourl' => BETTERDOCS_ABSURL . 'assets/admin/images/betterdocs-icon.svg'
		];

		return $fields;
	}
}
