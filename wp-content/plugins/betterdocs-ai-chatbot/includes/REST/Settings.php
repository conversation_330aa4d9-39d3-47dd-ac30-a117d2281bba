<?php

namespace WPDeveloper\BetterDocsChatbot\REST;

use WP_REST_Request;
use WPD<PERSON>loper\BetterDocs\Core\BaseAPI;
use WPD<PERSON>loper\BetterDocsChatbot\Core\AIChatbot;

class Settings extends BaseAPI {

	public function permission_check(): bool {
		return current_user_can( 'edit_docs_settings' );
	}

    public function register() {
        if(
            get_option('betterdocs_chatbot_software__license_status') === 'valid' &&
            get_option('betterdocs_pro_software__license_status') === 'valid'
        ){
            $this->post( 'save-aichatbot-settings', [ $this, 'save_aichatbot' ] );
        }
    }

    public function save_aichatbot( WP_REST_Request $request ) {
        $data = $request->get_params();

        betterdocs()->settings->save( 'enable_ai_chatbot', $data['enable_ai_chatbot'] );
        betterdocs()->settings->save( 'ai_chatbot_title', $data['ai_chatbot_title'] );
        betterdocs()->settings->save( 'ai_chatbot_subtitle', $data['ai_chatbot_subtitle'] );
        betterdocs()->settings->save( 'ai_chatbot_welcome_message', $data['ai_chatbot_welcome_message'] );
        betterdocs()->settings->save( 'ai_chatbot_icon', $data['ai_chatbot_icon'] );

        $api_status = AIChatbot::check_openai_quota($data['ai_chatbot_api_key']);

        $api_status = json_decode($api_status, true);  // Decode the JSON response

        // Check if required data is empty
        if ( empty( $data['ai_chatbot_api_key'] ) ||
             empty( $data['ai_chatbot_embed_model'] ) ||
             empty( $data['ai_chatbot_allowed_post_types'] ) ||
             empty( $data['ai_chatbot_chat_model'] ) ) {
            return [
                'status'  => 'error',
                'message' => __('Required fields cannot be empty.', 'betterdocs-ai-chatbot'),
            ];
        }

        if (isset($api_status['error'])) {  // Check if the 'error' key exists in the response
            $error_message = $api_status['error']['message'] ?? 'Unknown error';
            return [
                'status'  => 'error',
                'message' => $error_message,
            ];
        }


        // Get previous values
        $prev_api_key      = betterdocs()->settings->get( 'ai_chatbot_api_key', '' );
        $prev_embed_model  = betterdocs()->settings->get( 'ai_chatbot_embed_model', '' );
        $prev_post_type = betterdocs()->settings->get('ai_chatbot_allowed_post_types' );

        // Save settings if all required fields are provided
        betterdocs()->settings->save( 'ai_chatbot_api_key', $data['ai_chatbot_api_key'] );
        betterdocs()->settings->save( 'ai_chatbot_embed_model', $data['ai_chatbot_embed_model'] );
        betterdocs()->settings->save( 'ai_chatbot_chat_model', $data['ai_chatbot_chat_model'] );
        betterdocs()->settings->save( 'ai_chatbot_allowed_post_types', $data['ai_chatbot_allowed_post_types'] );

        // Execute if ai_chatbot_embed_model has changed
        if ( $prev_embed_model !== $data['ai_chatbot_embed_model'] || $prev_post_type !== $data['ai_chatbot_allowed_post_types'] ) {
            update_option( 'is_ai_chatbot_bg_complete', 'running' );
            update_option( 'background_process_status', 1 );

            // If the embedding model has changed, we need to delete the existing collection
            // If only the post types have changed, we can keep the existing collection
            $delete_existing = ($prev_embed_model !== $data['ai_chatbot_embed_model']);

            error_log('Settings changed: embedding model changed: ' . ($delete_existing ? 'yes' : 'no'));
            AIChatbot::send_data($delete_existing);
        }


		$docs_count = AIChatbot::get_docs_count( AIChatbot::allowed_post_types($data['ai_chatbot_allowed_post_types']) ); // Calculate the number of iterations needed
        update_option( 'docs_count', $docs_count );


		if($docs_count < 1){
			update_option( 'background_process_status', 0 );
			update_option( 'is_ai_chatbot_bg_complete', 'complete' );
			update_option( 'show_ai_chatbot_process_notices', 0 );
		}

        // Execute if ai_chatbot_api_key has changed
        if ( $prev_api_key !== $data['ai_chatbot_api_key'] ) {
            $openai_api_key = $data['ai_chatbot_api_key'];
            AIChatbot::encrypted_api_key( $openai_api_key, '0kXsYZmsHgvIB85miXWlq3nigYYD4PktOSVvOs3vlbA=' );
        }

        return [
            'status'  => 'success',
            'message' => __( 'Settings saved successfully.', 'betterdocs-ai-chatbot' ),
        ];
    }
}
