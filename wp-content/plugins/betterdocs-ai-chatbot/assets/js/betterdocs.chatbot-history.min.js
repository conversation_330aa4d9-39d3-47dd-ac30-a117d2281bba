(()=>{var e={57:(e,t,n)=>{"use strict";n.d(t,{Bk:()=>c,Kk:()=>o,Ri:()=>i,TV:()=>s,VK:()=>a,o2:()=>l});var r=n(133);n(723);const o=e=>e?(r.xI.setOptions({gfm:!0,breaks:!0,headerIds:!1}),{__html:(0,r.xI)(e)}):"",a=()=>{const e=navigator.userAgent;let t="Unknown Browser",n="",r="";return e.includes("Edg/")?(t="Edge",n=e.match(/Edg\/(\d+)/)?.[1]||""):e.includes("Chrome/")&&e.includes("Safari/")?(t="Chrome",n=e.match(/Chrome\/(\d+)/)?.[1]||""):e.includes("Safari/")&&!e.includes("Chrome/")?(t="Safari",n=e.match(/Version\/(\d+)/)?.[1]||""):e.includes("Firefox/")?(t="Mozila Firefox",n=e.match(/Firefox\/(\d+)/)?.[1]||""):(e.includes("MSIE")||e.includes("Trident/"))&&(t="Internet Explorer",n=e.match(/(?:MSIE |rv:)(\d+)/)?.[1]||""),e.includes("Macintosh")?r="Mac OS":e.includes("Windows")?r="Windows":e.includes("Linux")?r="Linux":e.includes("Android")?r="Android":(e.includes("iPhone")||e.includes("iPad"))&&(r="iOS"),`${t} ${n} on ${r}`},s=(e,t,n={})=>{"number"==typeof n&&(n={expiresDays:n});const r={expiresDays:7,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax",...n};let o=`${e}=${encodeURIComponent(t)}`;if(r.expiresDays){const e=new Date;e.setTime(e.getTime()+24*r.expiresDays*60*60*1e3),o+=`; expires=${e.toUTCString()}`}o+=`; path=${r.path}`,r.secure&&(o+="; Secure"),r.sameSite&&(o+=`; SameSite=${r.sameSite}`),document.cookie=o},i=e=>{const t=document.cookie.split("; ");for(let n of t){const[t,r]=n.split("=");if(t===e)return decodeURIComponent(r)}return null},l=(()=>{const e={text:(e,t=1e3)=>"string"!=typeof e?"":e.replace(/[<>"'`;\\/]/g,"").slice(0,t),email:e=>{const t=e.toLowerCase().trim();return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?t:""},sessionId:e=>e.replace(/[^a-zA-Z0-9_-]/g,"").slice(0,64)};return{sanitize:(t,n)=>e[t]?e[t](n):""}})(),c=()=>{let e=i("chatbot_session_id");return e&&(/^session_[a-f0-9]{14}\.[a-f0-9]{8}$/.test(e)||/^session_[a-z0-9]{9,}\d+$/.test(e))||(e=u(),s("chatbot_session_id",e,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"})),e},u=()=>{try{const e=Date.now()/1e3,t=Math.floor(e%1*1e6);return`session_${Math.floor(e).toString(16)}${t}.${crypto.getRandomValues(new Uint8Array(4)).reduce(((e,t)=>e+t.toString(16).padStart(2,"0")),"")}`}catch(e){return`session_${Math.random().toString(36).substring(2,11)}${Date.now()}`}}},59:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CARRIAGE_RETURN_PLACEHOLDER_REGEX=t.CARRIAGE_RETURN_PLACEHOLDER=t.CARRIAGE_RETURN_REGEX=t.CARRIAGE_RETURN=t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES=void 0,t.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES.reduce((function(e,t){return e[t.toLowerCase()]=t,e}),{}),t.CARRIAGE_RETURN="\r",t.CARRIAGE_RETURN_REGEX=new RegExp(t.CARRIAGE_RETURN,"g"),t.CARRIAGE_RETURN_PLACEHOLDER="__HTML_DOM_PARSER_CARRIAGE_RETURN_PLACEHOLDER_".concat(Date.now(),"__"),t.CARRIAGE_RETURN_PLACEHOLDER_REGEX=new RegExp(t.CARRIAGE_RETURN_PLACEHOLDER,"g")},95:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0;var a=n(994),s=n(399);o(n(399),t);var i={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},l=function(){function e(e,t,n){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=i),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:i,this.elementCB=null!=n?n:null}return e.prototype.onparserinit=function(e){this.parser=e},e.prototype.onreset=function(){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},e.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},e.prototype.onerror=function(e){this.handleCallback(e)},e.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)},e.prototype.onopentag=function(e,t){var n=this.options.xmlMode?a.ElementType.Tag:void 0,r=new s.Element(e,t,void 0,n);this.addNode(r),this.tagStack.push(r)},e.prototype.ontext=function(e){var t=this.lastNode;if(t&&t.type===a.ElementType.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{var n=new s.Text(e);this.addNode(n),this.lastNode=n}},e.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===a.ElementType.Comment)this.lastNode.data+=e;else{var t=new s.Comment(e);this.addNode(t),this.lastNode=t}},e.prototype.oncommentend=function(){this.lastNode=null},e.prototype.oncdatastart=function(){var e=new s.Text(""),t=new s.CDATA([e]);this.addNode(t),e.parent=t,this.lastNode=e},e.prototype.oncdataend=function(){this.lastNode=null},e.prototype.onprocessinginstruction=function(e,t){var n=new s.ProcessingInstruction(e,t);this.addNode(n)},e.prototype.handleCallback=function(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e},e.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null},e}();t.DomHandler=l,t.default=l},97:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,n,h=(e=(0,r.escapeSpecialCharacters)(e)).match(i),p=h&&h[1]?h[1].toLowerCase():"";switch(p){case o:var m=d(e);return l.test(e)||null===(t=null==(w=m.querySelector(a))?void 0:w.parentNode)||void 0===t||t.removeChild(w),c.test(e)||null===(n=null==(w=m.querySelector(s))?void 0:w.parentNode)||void 0===n||n.removeChild(w),m.querySelectorAll(o);case a:case s:var g=u(e).querySelectorAll(p);return c.test(e)&&l.test(e)?g[0].parentNode.childNodes:g;default:return f?f(e):(w=u(e,s).querySelector(s)).childNodes;var w}};var r=n(166),o="html",a="head",s="body",i=/<([a-zA-Z]+[0-9]?)/,l=/<head[^]*>/i,c=/<body[^]*>/i,u=function(e,t){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},d=function(e,t){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},h="object"==typeof window&&window.DOMParser;if("function"==typeof h){var p=new h;u=d=function(e,t){return t&&(e="<".concat(t,">").concat(e,"</").concat(t,">")),p.parseFromString(e,"text/html")}}if("object"==typeof document&&document.implementation){var m=document.implementation.createHTMLDocument();u=function(e,t){if(t){var n=m.documentElement.querySelector(t);return n&&(n.innerHTML=e),m}return m.documentElement.innerHTML=e,m}}var f,g="object"==typeof document&&document.createElement("template");g&&g.content&&(f=function(e){return g.innerHTML=e,g.content.childNodes})},133:(e,t,n)=>{"use strict";n.d(t,{xI:()=>le});let r={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function o(e){r=e}const a=/[&<>"']/,s=new RegExp(a.source,"g"),i=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,l=new RegExp(i.source,"g"),c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},u=e=>c[e];function d(e,t){if(t){if(a.test(e))return e.replace(s,u)}else if(i.test(e))return e.replace(l,u);return e}const h=/(^|[^\[])\^/g;function p(e,t){let n="string"==typeof e?e:e.source;t=t||"";const r={replace:(e,t)=>{let o="string"==typeof t?t:t.source;return o=o.replace(h,"$1"),n=n.replace(e,o),r},getRegex:()=>new RegExp(n,t)};return r}function m(e){try{e=encodeURI(e).replace(/%25/g,"%")}catch{return null}return e}const f={exec:()=>null};function g(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let r=!1,o=t;for(;--o>=0&&"\\"===n[o];)r=!r;return r?"|":" |"})).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function w(e,t,n){const r=e.length;if(0===r)return"";let o=0;for(;o<r;){const a=e.charAt(r-o-1);if(a!==t||n){if(a===t||!n)break;o++}else o++}return e.slice(0,r-o)}function b(e,t,n,r){const o=t.href,a=t.title?d(t.title):null,s=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;const e={type:"link",raw:n,href:o,title:a,text:s,tokens:r.inlineTokens(s)};return r.state.inLink=!1,e}return{type:"image",raw:n,href:o,title:a,text:d(s)}}class v{options;rules;lexer;constructor(e){this.options=e||r}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^(?: {1,4}| {0,3}\t)/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:w(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const r=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=r.length?e.slice(r.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=w(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:w(t[0],"\n")}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let e=w(t[0],"\n").split("\n"),n="",r="";const o=[];for(;e.length>0;){let t=!1;const a=[];let s;for(s=0;s<e.length;s++)if(/^ {0,3}>/.test(e[s]))a.push(e[s]),t=!0;else{if(t)break;a.push(e[s])}e=e.slice(s);const i=a.join("\n"),l=i.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,"\n    $1").replace(/^ {0,3}>[ \t]?/gm,"");n=n?`${n}\n${i}`:i,r=r?`${r}\n${l}`:l;const c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(l,o,!0),this.lexer.state.top=c,0===e.length)break;const u=o[o.length-1];if("code"===u?.type)break;if("blockquote"===u?.type){const t=u,a=t.raw+"\n"+e.join("\n"),s=this.blockquote(a);o[o.length-1]=s,n=n.substring(0,n.length-t.raw.length)+s.raw,r=r.substring(0,r.length-t.text.length)+s.text;break}if("list"!==u?.type);else{const t=u,a=t.raw+"\n"+e.join("\n"),s=this.list(a);o[o.length-1]=s,n=n.substring(0,n.length-u.raw.length)+s.raw,r=r.substring(0,r.length-t.raw.length)+s.raw,e=a.substring(o[o.length-1].raw.length).split("\n")}}return{type:"blockquote",raw:n,tokens:o,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,o={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const a=new RegExp(`^( {0,3}${n})((?:[\t ][^\\n]*)?(?:\\n|$))`);let s=!1;for(;e;){let n=!1,r="",i="";if(!(t=a.exec(e)))break;if(this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let l=t[2].split("\n",1)[0].replace(/^\t+/,(e=>" ".repeat(3*e.length))),c=e.split("\n",1)[0],u=!l.trim(),d=0;if(this.options.pedantic?(d=2,i=l.trimStart()):u?d=t[1].length+1:(d=t[2].search(/[^ ]/),d=d>4?1:d,i=l.slice(d),d+=t[1].length),u&&/^[ \t]*$/.test(c)&&(r+=c+"\n",e=e.substring(c.length+1),n=!0),!n){const t=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),n=new RegExp(`^ {0,${Math.min(3,d-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),o=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:\`\`\`|~~~)`),a=new RegExp(`^ {0,${Math.min(3,d-1)}}#`),s=new RegExp(`^ {0,${Math.min(3,d-1)}}<(?:[a-z].*>|!--)`,"i");for(;e;){const h=e.split("\n",1)[0];let p;if(c=h,this.options.pedantic?(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  "),p=c):p=c.replace(/\t/g,"    "),o.test(c))break;if(a.test(c))break;if(s.test(c))break;if(t.test(c))break;if(n.test(c))break;if(p.search(/[^ ]/)>=d||!c.trim())i+="\n"+p.slice(d);else{if(u)break;if(l.replace(/\t/g,"    ").search(/[^ ]/)>=4)break;if(o.test(l))break;if(a.test(l))break;if(n.test(l))break;i+="\n"+c}u||c.trim()||(u=!0),r+=h+"\n",e=e.substring(h.length+1),l=p.slice(d)}}o.loose||(s?o.loose=!0:/\n[ \t]*\n[ \t]*$/.test(r)&&(s=!0));let h,p=null;this.options.gfm&&(p=/^\[[ xX]\] /.exec(i),p&&(h="[ ] "!==p[0],i=i.replace(/^\[[ xX]\] +/,""))),o.items.push({type:"list_item",raw:r,task:!!p,checked:h,loose:!1,text:i,tokens:[]}),o.raw+=r}o.items[o.items.length-1].raw=o.items[o.items.length-1].raw.trimEnd(),o.items[o.items.length-1].text=o.items[o.items.length-1].text.trimEnd(),o.raw=o.raw.trimEnd();for(let e=0;e<o.items.length;e++)if(this.lexer.state.top=!1,o.items[e].tokens=this.lexer.blockTokens(o.items[e].text,[]),!o.loose){const t=o.items[e].tokens.filter((e=>"space"===e.type)),n=t.length>0&&t.some((e=>/\n.*\n/.test(e.raw)));o.loose=n}if(o.loose)for(let e=0;e<o.items.length;e++)o.items[e].loose=!0;return o}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(!t)return;if(!/[:|]/.test(t[2]))return;const n=g(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),o=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[],a={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const e of r)/^ *-+: *$/.test(e)?a.align.push("right"):/^ *:-+: *$/.test(e)?a.align.push("center"):/^ *:-+ *$/.test(e)?a.align.push("left"):a.align.push(null);for(let e=0;e<n.length;e++)a.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:a.align[e]});for(const e of o)a.rows.push(g(e,a.header.length).map(((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:a.align[t]}))));return a}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:d(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=w(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),b(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const e=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return b(n,e,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&(!r[3]||!n.match(/[\p{L}\p{N}]/u))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){const n=[...r[0]].length-1;let o,a,s=n,i=0;const l="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=l.exec(t));){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(a=[...o].length,r[3]||r[4]){s+=a;continue}if((r[5]||r[6])&&n%3&&!((n+a)%3)){i+=a;continue}if(s-=a,s>0)continue;a=Math.min(a,a+s+i);const t=[...r[0]][0].length,l=e.slice(0,n+r.index+t+a);if(Math.min(n,a)%2){const e=l.slice(1,-1);return{type:"em",raw:l,text:e,tokens:this.lexer.inlineTokens(e)}}const c=l.slice(2,-2);return{type:"strong",raw:l,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),r=/^ /.test(e)&&/ $/.test(e);return n&&r&&(e=e.substring(1,e.length-1)),e=d(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=d(t[1]),n="mailto:"+e):(e=d(t[1]),n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=d(t[0]),n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=d(t[0]),n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let e;return e=this.lexer.state.inRawBlock?t[0]:d(t[0]),{type:"text",raw:t[0],text:e}}}}const y=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,k=/(?:[*+-]|\d{1,9}[.)])/,E=p(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,k).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),x=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,C=/(?!\s*\])(?:\\.|[^\[\]\\])+/,_=p(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",C).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),T=p(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,k).getRegex(),A="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",S=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,N=p("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",S).replace("tag",A).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),L=p(x).replace("hr",y).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",A).getRegex(),O={blockquote:p(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",L).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:_,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:y,html:N,lheading:E,list:T,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:L,table:f,text:/^[^\n]+/},R=p("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",y).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",A).getRegex(),M={...O,table:R,paragraph:p(x).replace("hr",y).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",R).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",A).getRegex()},P={...O,html:p("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",S).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:f,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:p(x).replace("hr",y).replace("heading"," *#{1,6} *[^\n]").replace("lheading",E).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},I=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,D=/^( {2,}|\\)\n(?!\s*$)/,z="\\p{P}\\p{S}",$=p(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,z).getRegex(),B=p(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,z).getRegex(),H=p("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,z).getRegex(),q=p("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,z).getRegex(),F=p(/\\([punct])/,"gu").replace(/punct/g,z).getRegex(),j=p(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),U=p(S).replace("(?:--\x3e|$)","--\x3e").getRegex(),V=p("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",U).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Z=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,G=p(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Z).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),W=p(/^!?\[(label)\]\[(ref)\]/).replace("label",Z).replace("ref",C).getRegex(),Y=p(/^!?\[(ref)\](?:\[\])?/).replace("ref",C).getRegex(),K={_backpedal:f,anyPunctuation:F,autolink:j,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:D,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:f,emStrongLDelim:B,emStrongRDelimAst:H,emStrongRDelimUnd:q,escape:I,link:G,nolink:Y,punctuation:$,reflink:W,reflinkSearch:p("reflink|nolink(?!\\()","g").replace("reflink",W).replace("nolink",Y).getRegex(),tag:V,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:f},Q={...K,link:p(/^!?\[(label)\]\((.*?)\)/).replace("label",Z).getRegex(),reflink:p(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Z).getRegex()},X={...K,escape:p(I).replace("])","~|])").getRegex(),url:p(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},J={...X,br:p(D).replace("{2,}","*").getRegex(),text:p(X.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ee={normal:O,gfm:M,pedantic:P},te={normal:K,gfm:X,breaks:J,pedantic:Q};class ne{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||r,this.options.tokenizer=this.options.tokenizer||new v,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:ee.normal,inline:te.normal};this.options.pedantic?(t.block=ee.pedantic,t.inline=te.pedantic):this.options.gfm&&(t.block=ee.gfm,this.options.breaks?t.inline=te.breaks:t.inline=te.gfm),this.tokenizer.rules=t}static get rules(){return{block:ee,inline:te}}static lex(e,t){return new ne(t).lex(e)}static lexInline(e,t){return new ne(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){let r,o,a;for(this.options.pedantic&&(e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))))if(r=this.tokenizer.space(e))e=e.substring(r.raw.length),1===r.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(r);else if(r=this.tokenizer.code(e))e=e.substring(r.raw.length),o=t[t.length-1],!o||"paragraph"!==o.type&&"text"!==o.type?t.push(r):(o.raw+="\n"+r.raw,o.text+="\n"+r.text,this.inlineQueue[this.inlineQueue.length-1].src=o.text);else if(r=this.tokenizer.fences(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.heading(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.hr(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.blockquote(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.list(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.html(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.def(e))e=e.substring(r.raw.length),o=t[t.length-1],!o||"paragraph"!==o.type&&"text"!==o.type?this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title}):(o.raw+="\n"+r.raw,o.text+="\n"+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=o.text);else if(r=this.tokenizer.table(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.lheading(e))e=e.substring(r.raw.length),t.push(r);else{if(a=e,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startBlock.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(a=e.substring(0,t+1))}if(this.state.top&&(r=this.tokenizer.paragraph(a)))o=t[t.length-1],n&&"paragraph"===o?.type?(o.raw+="\n"+r.raw,o.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=o.text):t.push(r),n=a.length!==e.length,e=e.substring(r.raw.length);else if(r=this.tokenizer.text(e))e=e.substring(r.raw.length),o=t[t.length-1],o&&"text"===o.type?(o.raw+="\n"+r.raw,o.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=o.text):t.push(r);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,o,a,s,i,l=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(a=this.tokenizer.rules.inline.reflinkSearch.exec(l));)e.includes(a[0].slice(a[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(a=this.tokenizer.rules.inline.blockSkip.exec(l));)l=l.slice(0,a.index)+"["+"a".repeat(a[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(a=this.tokenizer.rules.inline.anyPunctuation.exec(l));)l=l.slice(0,a.index)+"++"+l.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(s||(i=""),s=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((r=>!!(n=r.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,l,i))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(o=e,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startInline.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(o=e.substring(0,t+1))}if(n=this.tokenizer.inlineText(o))e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(i=n.raw.slice(-1)),s=!0,r=t[t.length-1],r&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}else e=e.substring(n.raw.length),t.push(n);return t}}class re{options;parser;constructor(e){this.options=e||r}space(e){return""}code({text:e,lang:t,escaped:n}){const r=(t||"").match(/^\S*/)?.[0],o=e.replace(/\n$/,"")+"\n";return r?'<pre><code class="language-'+d(r)+'">'+(n?o:d(o,!0))+"</code></pre>\n":"<pre><code>"+(n?o:d(o,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){const t=e.ordered,n=e.start;let r="";for(let t=0;t<e.items.length;t++){const n=e.items[t];r+=this.listitem(n)}const o=t?"ol":"ul";return"<"+o+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+o+">\n"}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});e.loose?e.tokens.length>0&&"paragraph"===e.tokens[0].type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+e.tokens[0].tokens[0].text)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" "}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){const o=e.rows[t];n="";for(let e=0;e<o.length;e++)n+=this.tablecell(o[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${e}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const r=this.parser.parseInline(n),o=m(e);if(null===o)return r;let a='<a href="'+(e=o)+'"';return t&&(a+=' title="'+t+'"'),a+=">"+r+"</a>",a}image({href:e,title:t,text:n}){const r=m(e);if(null===r)return n;let o=`<img src="${e=r}" alt="${n}"`;return t&&(o+=` title="${t}"`),o+=">",o}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):e.text}}class oe{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class ae{options;renderer;textRenderer;constructor(e){this.options=e||r,this.options.renderer=this.options.renderer||new re,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new oe}static parse(e,t){return new ae(t).parse(e)}static parseInline(e,t){return new ae(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const o=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]){const e=o,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}const a=o;switch(a.type){case"space":n+=this.renderer.space(a);continue;case"hr":n+=this.renderer.hr(a);continue;case"heading":n+=this.renderer.heading(a);continue;case"code":n+=this.renderer.code(a);continue;case"table":n+=this.renderer.table(a);continue;case"blockquote":n+=this.renderer.blockquote(a);continue;case"list":n+=this.renderer.list(a);continue;case"html":n+=this.renderer.html(a);continue;case"paragraph":n+=this.renderer.paragraph(a);continue;case"text":{let o=a,s=this.renderer.text(o);for(;r+1<e.length&&"text"===e[r+1].type;)o=e[++r],s+="\n"+this.renderer.text(o);n+=t?this.renderer.paragraph({type:"paragraph",raw:s,text:s,tokens:[{type:"text",raw:s,text:s}]}):s;continue}default:{const e='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const o=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[o.type]){const e=this.options.extensions.renderers[o.type].call({parser:this},o);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(o.type)){n+=e||"";continue}}const a=o;switch(a.type){case"escape":case"text":n+=t.text(a);break;case"html":n+=t.html(a);break;case"link":n+=t.link(a);break;case"image":n+=t.image(a);break;case"strong":n+=t.strong(a);break;case"em":n+=t.em(a);break;case"codespan":n+=t.codespan(a);break;case"br":n+=t.br(a);break;case"del":n+=t.del(a);break;default:{const e='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}}class se{options;block;constructor(e){this.options=e||r}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?ne.lex:ne.lexInline}provideParser(){return this.block?ae.parse:ae.parseInline}}const ie=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=ae;Renderer=re;TextRenderer=oe;Lexer=ne;Tokenizer=v;Hooks=se;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{const e=r;for(const r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(const r of e.rows)for(const e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{const e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{const e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach((r=>{const o=e[r].flat(1/0);n=n.concat(this.walkTokens(o,t))})):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach((e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach((e=>{if(!e.name)throw new Error("extension name required");if("renderer"in e){const n=t.renderers[e.name];t.renderers[e.name]=n?function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");const n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)})),n.extensions=t),e.renderer){const t=this.defaults.renderer||new re(this.defaults);for(const n in e.renderer){if(!(n in t))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;const r=n,o=e.renderer[r],a=t[r];t[r]=(...e)=>{let n=o.apply(t,e);return!1===n&&(n=a.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){const t=this.defaults.tokenizer||new v(this.defaults);for(const n in e.tokenizer){if(!(n in t))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;const r=n,o=e.tokenizer[r],a=t[r];t[r]=(...e)=>{let n=o.apply(t,e);return!1===n&&(n=a.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){const t=this.defaults.hooks||new se;for(const n in e.hooks){if(!(n in t))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;const r=n,o=e.hooks[r],a=t[r];se.passThroughHooks.has(n)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(o.call(t,e)).then((e=>a.call(t,e)));const n=o.call(t,e);return a.call(t,n)}:t[r]=(...e)=>{let n=o.apply(t,e);return!1===n&&(n=a.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){const t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}})),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return ne.lex(e,t??this.defaults)}parser(e,t){return ae.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{const r={...n},o={...this.defaults,...r},a=this.onError(!!o.silent,!!o.async);if(!0===this.defaults.async&&!1===r.async)return a(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return a(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return a(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=e);const s=o.hooks?o.hooks.provideLexer():e?ne.lex:ne.lexInline,i=o.hooks?o.hooks.provideParser():e?ae.parse:ae.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(t):t).then((e=>s(e,o))).then((e=>o.hooks?o.hooks.processAllTokens(e):e)).then((e=>o.walkTokens?Promise.all(this.walkTokens(e,o.walkTokens)).then((()=>e)):e)).then((e=>i(e,o))).then((e=>o.hooks?o.hooks.postprocess(e):e)).catch(a);try{o.hooks&&(t=o.hooks.preprocess(t));let e=s(t,o);o.hooks&&(e=o.hooks.processAllTokens(e)),o.walkTokens&&this.walkTokens(e,o.walkTokens);let n=i(e,o);return o.hooks&&(n=o.hooks.postprocess(n)),n}catch(e){return a(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+d(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function le(e,t){return ie.parse(e,t)}le.options=le.setOptions=function(e){return ie.setOptions(e),le.defaults=ie.defaults,o(le.defaults),le},le.getDefaults=function(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}},le.defaults=r,le.use=function(...e){return ie.use(...e),le.defaults=ie.defaults,o(le.defaults),le},le.walkTokens=function(e,t){return ie.walkTokens(e,t)},le.parseInline=ie.parseInline,le.Parser=ae,le.parser=ae.parse,le.Renderer=re,le.TextRenderer=oe,le.Lexer=ne,le.lexer=ne.lex,le.Tokenizer=v,le.Hooks=se,le.parse=le,le.options,le.setOptions,le.use,le.walkTokens,le.parseInline,ae.parse,ne.lex},166:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatAttributes=a,t.escapeSpecialCharacters=function(e){return e.replace(o.CARRIAGE_RETURN_REGEX,o.CARRIAGE_RETURN_PLACEHOLDER)},t.revertEscapedCharacters=i,t.formatDOM=function e(t,n,o){void 0===n&&(n=null);for(var l,c=[],u=0,d=t.length;u<d;u++){var h=t[u];switch(h.nodeType){case 1:var p=s(h.nodeName);(l=new r.Element(p,a(h.attributes))).children=e("template"===p?h.content.childNodes:h.childNodes,l);break;case 3:l=new r.Text(i(h.nodeValue));break;case 8:l=new r.Comment(h.nodeValue);break;default:continue}var m=c[u-1]||null;m&&(m.next=l),l.parent=n,l.prev=m,l.next=null,c.push(l)}return o&&((l=new r.ProcessingInstruction(o.substring(0,o.indexOf(" ")).toLowerCase(),o)).next=c[0]||null,l.parent=n,c.unshift(l),c[1]&&(c[1].prev=c[0])),c};var r=n(95),o=n(59);function a(e){for(var t={},n=0,r=e.length;n<r;n++){var o=e[n];t[o.name]=o.value}return t}function s(e){return function(e){return o.CASE_SENSITIVE_TAG_NAMES_MAP[e]}(e=e.toLowerCase())||e}function i(e){return e.replace(o.CARRIAGE_RETURN_PLACEHOLDER_REGEX,o.CARRIAGE_RETURN)}},270:function(e){e.exports=function(){"use strict";function e(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function t(t,n){return t.get(e(t,n))}function n(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}const r={},o=e=>new Promise((t=>{if(!e)return t();const n=window.scrollX,o=window.scrollY;r.restoreFocusTimeout=setTimeout((()=>{r.previousActiveElement instanceof HTMLElement?(r.previousActiveElement.focus(),r.previousActiveElement=null):document.body&&document.body.focus(),t()}),100),window.scrollTo(n,o)})),a="swal2-",s=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"].reduce(((e,t)=>(e[t]=a+t,e)),{}),i=["success","warning","info","question","error"].reduce(((e,t)=>(e[t]=a+t,e)),{}),l="SweetAlert2:",c=e=>e.charAt(0).toUpperCase()+e.slice(1),u=e=>{console.warn(`${l} ${"object"==typeof e?e.join(" "):e}`)},d=e=>{console.error(`${l} ${e}`)},h=[],p=(e,t=null)=>{var n;n=`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`,h.includes(n)||(h.push(n),u(n))},m=e=>"function"==typeof e?e():e,f=e=>e&&"function"==typeof e.toPromise,g=e=>f(e)?e.toPromise():Promise.resolve(e),w=e=>e&&Promise.resolve(e)===e,b=()=>document.body.querySelector(`.${s.container}`),v=e=>{const t=b();return t?t.querySelector(e):null},y=e=>v(`.${e}`),k=()=>y(s.popup),E=()=>y(s.icon),x=()=>y(s.title),C=()=>y(s["html-container"]),_=()=>y(s.image),T=()=>y(s["progress-steps"]),A=()=>y(s["validation-message"]),S=()=>v(`.${s.actions} .${s.confirm}`),N=()=>v(`.${s.actions} .${s.cancel}`),L=()=>v(`.${s.actions} .${s.deny}`),O=()=>v(`.${s.loader}`),R=()=>y(s.actions),M=()=>y(s.footer),P=()=>y(s["timer-progress-bar"]),I=()=>y(s.close),D=()=>{const e=k();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),n=Array.from(t).sort(((e,t)=>{const n=parseInt(e.getAttribute("tabindex")||"0"),r=parseInt(t.getAttribute("tabindex")||"0");return n>r?1:n<r?-1:0})),r=e.querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n'),o=Array.from(r).filter((e=>"-1"!==e.getAttribute("tabindex")));return[...new Set(n.concat(o))].filter((e=>ee(e)))},z=()=>H(document.body,s.shown)&&!H(document.body,s["toast-shown"])&&!H(document.body,s["no-backdrop"]),$=()=>{const e=k();return!!e&&H(e,s.toast)},B=(e,t)=>{if(e.textContent="",t){const n=(new DOMParser).parseFromString(t,"text/html"),r=n.querySelector("head");r&&Array.from(r.childNodes).forEach((t=>{e.appendChild(t)}));const o=n.querySelector("body");o&&Array.from(o.childNodes).forEach((t=>{t instanceof HTMLVideoElement||t instanceof HTMLAudioElement?e.appendChild(t.cloneNode(!0)):e.appendChild(t)}))}},H=(e,t)=>{if(!t)return!1;const n=t.split(/\s+/);for(let t=0;t<n.length;t++)if(!e.classList.contains(n[t]))return!1;return!0},q=(e,t,n)=>{if(((e,t)=>{Array.from(e.classList).forEach((n=>{Object.values(s).includes(n)||Object.values(i).includes(n)||Object.values(t.showClass||{}).includes(n)||e.classList.remove(n)}))})(e,t),!t.customClass)return;const r=t.customClass[n];r&&("string"==typeof r||r.forEach?V(e,r):u(`Invalid type of customClass.${n}! Expected string or iterable object, got "${typeof r}"`))},F=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${s.popup} > .${s[t]}`);case"checkbox":return e.querySelector(`.${s.popup} > .${s.checkbox} input`);case"radio":return e.querySelector(`.${s.popup} > .${s.radio} input:checked`)||e.querySelector(`.${s.popup} > .${s.radio} input:first-child`);case"range":return e.querySelector(`.${s.popup} > .${s.range} input`);default:return e.querySelector(`.${s.popup} > .${s.input}`)}},j=e=>{if(e.focus(),"file"!==e.type){const t=e.value;e.value="",e.value=t}},U=(e,t,n)=>{e&&t&&("string"==typeof t&&(t=t.split(/\s+/).filter(Boolean)),t.forEach((t=>{Array.isArray(e)?e.forEach((e=>{n?e.classList.add(t):e.classList.remove(t)})):n?e.classList.add(t):e.classList.remove(t)})))},V=(e,t)=>{U(e,t,!0)},Z=(e,t)=>{U(e,t,!1)},G=(e,t)=>{const n=Array.from(e.children);for(let e=0;e<n.length;e++){const r=n[e];if(r instanceof HTMLElement&&H(r,t))return r}},W=(e,t,n)=>{n===`${parseInt(n)}`&&(n=parseInt(n)),n||0===parseInt(n)?e.style.setProperty(t,"number"==typeof n?`${n}px`:n):e.style.removeProperty(t)},Y=(e,t="flex")=>{e&&(e.style.display=t)},K=e=>{e&&(e.style.display="none")},Q=(e,t="block")=>{e&&new MutationObserver((()=>{J(e,e.innerHTML,t)})).observe(e,{childList:!0,subtree:!0})},X=(e,t,n,r)=>{const o=e.querySelector(t);o&&o.style.setProperty(n,r)},J=(e,t,n="flex")=>{t?Y(e,n):K(e)},ee=e=>!(!e||!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),te=e=>!!(e.scrollHeight>e.clientHeight),ne=e=>{const t=window.getComputedStyle(e),n=parseFloat(t.getPropertyValue("animation-duration")||"0"),r=parseFloat(t.getPropertyValue("transition-duration")||"0");return n>0||r>0},re=(e,t=!1)=>{const n=P();n&&ee(n)&&(t&&(n.style.transition="none",n.style.width="100%"),setTimeout((()=>{n.style.transition=`width ${e/1e3}s linear`,n.style.width="0%"}),10))},oe=`\n <div aria-labelledby="${s.title}" aria-describedby="${s["html-container"]}" class="${s.popup}" tabindex="-1">\n   <button type="button" class="${s.close}"></button>\n   <ul class="${s["progress-steps"]}"></ul>\n   <div class="${s.icon}"></div>\n   <img class="${s.image}" />\n   <h2 class="${s.title}" id="${s.title}"></h2>\n   <div class="${s["html-container"]}" id="${s["html-container"]}"></div>\n   <input class="${s.input}" id="${s.input}" />\n   <input type="file" class="${s.file}" />\n   <div class="${s.range}">\n     <input type="range" />\n     <output></output>\n   </div>\n   <select class="${s.select}" id="${s.select}"></select>\n   <div class="${s.radio}"></div>\n   <label class="${s.checkbox}">\n     <input type="checkbox" id="${s.checkbox}" />\n     <span class="${s.label}"></span>\n   </label>\n   <textarea class="${s.textarea}" id="${s.textarea}"></textarea>\n   <div class="${s["validation-message"]}" id="${s["validation-message"]}"></div>\n   <div class="${s.actions}">\n     <div class="${s.loader}"></div>\n     <button type="button" class="${s.confirm}"></button>\n     <button type="button" class="${s.deny}"></button>\n     <button type="button" class="${s.cancel}"></button>\n   </div>\n   <div class="${s.footer}"></div>\n   <div class="${s["timer-progress-bar-container"]}">\n     <div class="${s["timer-progress-bar"]}"></div>\n   </div>\n </div>\n`.replace(/(^|\n)\s*/g,""),ae=()=>{r.currentInstance.resetValidationMessage()},se=e=>{const t=(()=>{const e=b();return!!e&&(e.remove(),Z([document.documentElement,document.body],[s["no-backdrop"],s["toast-shown"],s["has-column"]]),!0)})();if("undefined"==typeof window||"undefined"==typeof document)return void d("SweetAlert2 requires document to initialize");const n=document.createElement("div");n.className=s.container,t&&V(n,s["no-transition"]),B(n,oe),n.dataset.swal2Theme=e.theme;const r="string"==typeof(o=e.target)?document.querySelector(o):o;var o;r.appendChild(n),e.topLayer&&(n.setAttribute("popover",""),n.showPopover()),(e=>{const t=k();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")})(e),(e=>{"rtl"===window.getComputedStyle(e).direction&&V(b(),s.rtl)})(r),(()=>{const e=k(),t=G(e,s.input),n=G(e,s.file),r=e.querySelector(`.${s.range} input`),o=e.querySelector(`.${s.range} output`),a=G(e,s.select),i=e.querySelector(`.${s.checkbox} input`),l=G(e,s.textarea);t.oninput=ae,n.onchange=ae,a.onchange=ae,i.onchange=ae,l.oninput=ae,r.oninput=()=>{ae(),o.value=r.value},r.onchange=()=>{ae(),o.value=r.value}})()},ie=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):"object"==typeof e?le(e,t):e&&B(t,e)},le=(e,t)=>{e.jquery?ce(t,e):B(t,e.toString())},ce=(e,t)=>{if(e.textContent="",0 in t)for(let n=0;n in t;n++)e.appendChild(t[n].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},ue=(e,t)=>{const n=R(),r=O();n&&r&&(t.showConfirmButton||t.showDenyButton||t.showCancelButton?Y(n):K(n),q(n,t,"actions"),function(e,t,n){const r=S(),o=L(),a=N();r&&o&&a&&(he(r,"confirm",n),he(o,"deny",n),he(a,"cancel",n),function(e,t,n,r){r.buttonsStyling?(V([e,t,n],s.styled),r.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",r.confirmButtonColor),r.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",r.denyButtonColor),r.cancelButtonColor&&n.style.setProperty("--swal2-cancel-button-background-color",r.cancelButtonColor),de(e),de(t),de(n)):Z([e,t,n],s.styled)}(r,o,a,n),n.reverseButtons&&(n.toast?(e.insertBefore(a,r),e.insertBefore(o,r)):(e.insertBefore(a,t),e.insertBefore(o,t),e.insertBefore(r,t))))}(n,r,t),B(r,t.loaderHtml||""),q(r,t,"loader"))};function de(e){const t=window.getComputedStyle(e),n=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-outline",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${n}`))}function he(e,t,n){const r=c(t);J(e,n[`show${r}Button`],"inline-block"),B(e,n[`${t}ButtonText`]||""),e.setAttribute("aria-label",n[`${t}ButtonAriaLabel`]||""),e.className=s[t],q(e,n,`${t}Button`)}const pe=(e,t)=>{const n=b();n&&(function(e,t){"string"==typeof t?e.style.background=t:t||V([document.documentElement,document.body],s["no-backdrop"])}(n,t.backdrop),function(e,t){t&&(t in s?V(e,s[t]):(u('The "position" parameter is not valid, defaulting to "center"'),V(e,s.center)))}(n,t.position),function(e,t){t&&V(e,s[`grow-${t}`])}(n,t.grow),q(n,t,"container"))};var me={innerParams:new WeakMap,domCache:new WeakMap};const fe=["input","file","range","select","radio","checkbox","textarea"],ge=e=>{if(!e.input)return;if(!xe[e.input])return void d(`Unexpected type of input! Expected ${Object.keys(xe).join(" | ")}, got "${e.input}"`);const t=ke(e.input);if(!t)return;const n=xe[e.input](t,e);Y(t),e.inputAutoFocus&&setTimeout((()=>{j(n)}))},we=(e,t)=>{const n=k();if(!n)return;const r=F(n,e);if(r){(e=>{for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;["id","type","value","style"].includes(n)||e.removeAttribute(n)}})(r);for(const e in t)r.setAttribute(e,t[e])}},be=e=>{if(!e.input)return;const t=ke(e.input);t&&q(t,e,"input")},ve=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},ye=(e,t,n)=>{if(n.inputLabel){const r=document.createElement("label"),o=s["input-label"];r.setAttribute("for",e.id),r.className=o,"object"==typeof n.customClass&&V(r,n.customClass.inputLabel),r.innerText=n.inputLabel,t.insertAdjacentElement("beforebegin",r)}},ke=e=>{const t=k();if(t)return G(t,s[e]||s.input)},Ee=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:w(t)||u(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},xe={};xe.text=xe.email=xe.password=xe.number=xe.tel=xe.url=xe.search=xe.date=xe["datetime-local"]=xe.time=xe.week=xe.month=(e,t)=>(Ee(e,t.inputValue),ye(e,e,t),ve(e,t),e.type=t.input,e),xe.file=(e,t)=>(ye(e,e,t),ve(e,t),e),xe.range=(e,t)=>{const n=e.querySelector("input"),r=e.querySelector("output");return Ee(n,t.inputValue),n.type=t.input,Ee(r,t.inputValue),ye(n,e,t),e},xe.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const n=document.createElement("option");B(n,t.inputPlaceholder),n.value="",n.disabled=!0,n.selected=!0,e.appendChild(n)}return ye(e,e,t),e},xe.radio=e=>(e.textContent="",e),xe.checkbox=(e,t)=>{const n=F(k(),"checkbox");n.value="1",n.checked=Boolean(t.inputValue);const r=e.querySelector("span");return B(r,t.inputPlaceholder||t.inputLabel),n},xe.textarea=(e,t)=>{Ee(e,t.inputValue),ve(e,t),ye(e,e,t);return setTimeout((()=>{if("MutationObserver"in window){const n=parseInt(window.getComputedStyle(k()).width);new MutationObserver((()=>{if(!document.body.contains(e))return;const r=e.offsetWidth+(o=e,parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight));var o;r>n?k().style.width=`${r}px`:W(k(),"width",t.width)})).observe(e,{attributes:!0,attributeFilter:["style"]})}})),e};const Ce=(e,t)=>{const n=C();n&&(Q(n),q(n,t,"htmlContainer"),t.html?(ie(t.html,n),Y(n,"block")):t.text?(n.textContent=t.text,Y(n,"block")):K(n),((e,t)=>{const n=k();if(!n)return;const r=me.innerParams.get(e),o=!r||t.input!==r.input;fe.forEach((e=>{const r=G(n,s[e]);r&&(we(e,t.inputAttributes),r.className=s[e],o&&K(r))})),t.input&&(o&&ge(t),be(t))})(e,t))},_e=(e,t)=>{for(const[n,r]of Object.entries(i))t.icon!==n&&Z(e,r);V(e,t.icon&&i[t.icon]),Se(e,t),Te(),q(e,t,"icon")},Te=()=>{const e=k();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),n=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let e=0;e<n.length;e++)n[e].style.backgroundColor=t},Ae=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let n=e.innerHTML,r="";t.iconHtml?r=Ne(t.iconHtml):"success"===t.icon?(r='\n  <div class="swal2-success-circular-line-left"></div>\n  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n  <div class="swal2-success-circular-line-right"></div>\n',n=n.replace(/ style=".*?"/g,"")):"error"===t.icon?r='\n  <span class="swal2-x-mark">\n    <span class="swal2-x-mark-line-left"></span>\n    <span class="swal2-x-mark-line-right"></span>\n  </span>\n':t.icon&&(r=Ne({question:"?",warning:"!",info:"i"}[t.icon])),n.trim()!==r.trim()&&B(e,r)},Se=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const n of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])X(e,n,"background-color",t.iconColor);X(e,".swal2-success-ring","border-color",t.iconColor)}},Ne=e=>`<div class="${s["icon-content"]}">${e}</div>`;let Le=!1,Oe=0,Re=0,Me=0,Pe=0;const Ie=e=>{const t=k();if(e.target===t||E().contains(e.target)){Le=!0;const n=$e(e);Oe=n.clientX,Re=n.clientY,Me=parseInt(t.style.insetInlineStart)||0,Pe=parseInt(t.style.insetBlockStart)||0,V(t,"swal2-dragging")}},De=e=>{const t=k();if(Le){let{clientX:n,clientY:r}=$e(e);t.style.insetInlineStart=`${Me+(n-Oe)}px`,t.style.insetBlockStart=`${Pe+(r-Re)}px`}},ze=()=>{const e=k();Le=!1,Z(e,"swal2-dragging")},$e=e=>{let t=0,n=0;return e.type.startsWith("mouse")?(t=e.clientX,n=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,n=e.touches[0].clientY),{clientX:t,clientY:n}},Be=(e,t)=>{const n=b(),r=k();if(n&&r){if(t.toast){W(n,"width",t.width),r.style.width="100%";const e=O();e&&r.insertBefore(e,E())}else W(r,"width",t.width);W(r,"padding",t.padding),t.color&&(r.style.color=t.color),t.background&&(r.style.background=t.background),K(A()),He(r,t),t.draggable&&!t.toast?(V(r,s.draggable),(e=>{e.addEventListener("mousedown",Ie),document.body.addEventListener("mousemove",De),e.addEventListener("mouseup",ze),e.addEventListener("touchstart",Ie),document.body.addEventListener("touchmove",De),e.addEventListener("touchend",ze)})(r)):(Z(r,s.draggable),(e=>{e.removeEventListener("mousedown",Ie),document.body.removeEventListener("mousemove",De),e.removeEventListener("mouseup",ze),e.removeEventListener("touchstart",Ie),document.body.removeEventListener("touchmove",De),e.removeEventListener("touchend",ze)})(r))}},He=(e,t)=>{const n=t.showClass||{};e.className=`${s.popup} ${ee(e)?n.popup:""}`,t.toast?(V([document.documentElement,document.body],s["toast-shown"]),V(e,s.toast)):V(e,s.modal),q(e,t,"popup"),"string"==typeof t.customClass&&V(e,t.customClass),t.icon&&V(e,s[`icon-${t.icon}`])},qe=e=>{const t=document.createElement("li");return V(t,s["progress-step"]),B(t,e),t},Fe=e=>{const t=document.createElement("li");return V(t,s["progress-step-line"]),e.progressStepsDistance&&W(t,"width",e.progressStepsDistance),t},je=(e,t)=>{Be(0,t),pe(0,t),((e,t)=>{const n=T();if(!n)return;const{progressSteps:r,currentProgressStep:o}=t;r&&0!==r.length&&void 0!==o?(Y(n),n.textContent="",o>=r.length&&u("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),r.forEach(((e,a)=>{const i=qe(e);if(n.appendChild(i),a===o&&V(i,s["active-progress-step"]),a!==r.length-1){const e=Fe(t);n.appendChild(e)}}))):K(n)})(0,t),((e,t)=>{const n=me.innerParams.get(e),r=E();if(r){if(n&&t.icon===n.icon)return Ae(r,t),void _e(r,t);if(t.icon||t.iconHtml){if(t.icon&&-1===Object.keys(i).indexOf(t.icon))return d(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),void K(r);Y(r),Ae(r,t),_e(r,t),V(r,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Te)}else K(r)}})(e,t),((e,t)=>{const n=_();n&&(t.imageUrl?(Y(n,""),n.setAttribute("src",t.imageUrl),n.setAttribute("alt",t.imageAlt||""),W(n,"width",t.imageWidth),W(n,"height",t.imageHeight),n.className=s.image,q(n,t,"image")):K(n))})(0,t),((e,t)=>{const n=x();n&&(Q(n),J(n,t.title||t.titleText,"block"),t.title&&ie(t.title,n),t.titleText&&(n.innerText=t.titleText),q(n,t,"title"))})(0,t),((e,t)=>{const n=I();n&&(B(n,t.closeButtonHtml||""),q(n,t,"closeButton"),J(n,t.showCloseButton),n.setAttribute("aria-label",t.closeButtonAriaLabel||""))})(0,t),Ce(e,t),ue(0,t),((e,t)=>{const n=M();n&&(Q(n),J(n,t.footer,"block"),t.footer&&ie(t.footer,n),q(n,t,"footer"))})(0,t);const n=k();"function"==typeof t.didRender&&n&&t.didRender(n),r.eventEmitter.emit("didRender",n)},Ue=()=>{var e;return null===(e=S())||void 0===e?void 0:e.click()},Ve=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Ze=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Ge=(e,t)=>{var n;const r=D();if(r.length)return-2===(e+=t)&&(e=r.length-1),e===r.length?e=0:-1===e&&(e=r.length-1),void r[e].focus();null===(n=k())||void 0===n||n.focus()},We=["ArrowRight","ArrowDown"],Ye=["ArrowLeft","ArrowUp"],Ke=(e,t,n)=>{e&&(t.isComposing||229===t.keyCode||(e.stopKeydownPropagation&&t.stopPropagation(),"Enter"===t.key?Qe(t,e):"Tab"===t.key?Xe(t):[...We,...Ye].includes(t.key)?Je(t.key):"Escape"===t.key&&et(t,e,n)))},Qe=(e,t)=>{if(!m(t.allowEnterKey))return;const n=F(k(),t.input);if(e.target&&n&&e.target instanceof HTMLElement&&e.target.outerHTML===n.outerHTML){if(["textarea","file"].includes(t.input))return;Ue(),e.preventDefault()}},Xe=e=>{const t=e.target,n=D();let r=-1;for(let e=0;e<n.length;e++)if(t===n[e]){r=e;break}e.shiftKey?Ge(r,-1):Ge(r,1),e.stopPropagation(),e.preventDefault()},Je=e=>{const t=R(),n=S(),r=L(),o=N();if(!(t&&n&&r&&o))return;const a=[n,r,o];if(document.activeElement instanceof HTMLElement&&!a.includes(document.activeElement))return;const s=We.includes(e)?"nextElementSibling":"previousElementSibling";let i=document.activeElement;if(i){for(let e=0;e<t.children.length;e++){if(i=i[s],!i)return;if(i instanceof HTMLButtonElement&&ee(i))break}i instanceof HTMLButtonElement&&i.focus()}},et=(e,t,n)=>{m(t.allowEscapeKey)&&(e.preventDefault(),n(Ve.esc))};var tt={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const nt=()=>{Array.from(document.body.children).forEach((e=>{e.hasAttribute("data-previous-aria-hidden")?(e.setAttribute("aria-hidden",e.getAttribute("data-previous-aria-hidden")||""),e.removeAttribute("data-previous-aria-hidden")):e.removeAttribute("aria-hidden")}))},rt="undefined"!=typeof window&&!!window.GestureEvent,ot=()=>{const e=b();if(!e)return;let t;e.ontouchstart=e=>{t=at(e)},e.ontouchmove=e=>{t&&(e.preventDefault(),e.stopPropagation())}},at=e=>{const t=e.target,n=b(),r=C();return!(!n||!r||st(e)||it(e)||t!==n&&(te(n)||!(t instanceof HTMLElement)||((e,t)=>{let n=e;for(;n&&n!==t;){if(te(n))return!0;n=n.parentElement}return!1})(t,r)||"INPUT"===t.tagName||"TEXTAREA"===t.tagName||te(r)&&r.contains(t)))},st=e=>e.touches&&e.touches.length&&"stylus"===e.touches[0].touchType,it=e=>e.touches&&e.touches.length>1;let lt=null;const ct=e=>{null===lt&&(document.body.scrollHeight>window.innerHeight||"scroll"===e)&&(lt=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${lt+(()=>{const e=document.createElement("div");e.className=s["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t})()}px`)};function ut(e,t,n,a){$()?bt(e,a):(o(n).then((()=>bt(e,a))),Ze(r)),rt?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),z()&&(null!==lt&&(document.body.style.paddingRight=`${lt}px`,lt=null),(()=>{if(H(document.body,s.iosfix)){const e=parseInt(document.body.style.top,10);Z(document.body,s.iosfix),document.body.style.top="",document.body.scrollTop=-1*e}})(),nt()),Z([document.documentElement,document.body],[s.shown,s["height-auto"],s["no-backdrop"],s["toast-shown"]])}function dt(e){e=ft(e);const t=tt.swalPromiseResolve.get(this),n=ht(this);this.isAwaitingPromise?e.isDismissed||(mt(this),t(e)):n&&t(e)}const ht=e=>{const t=k();if(!t)return!1;const n=me.innerParams.get(e);if(!n||H(t,n.hideClass.popup))return!1;Z(t,n.showClass.popup),V(t,n.hideClass.popup);const r=b();return Z(r,n.showClass.backdrop),V(r,n.hideClass.backdrop),gt(e,t,n),!0};function pt(e){const t=tt.swalPromiseReject.get(this);mt(this),t&&t(e)}const mt=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,me.innerParams.get(e)||e._destroy())},ft=e=>void 0===e?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),gt=(e,t,n)=>{var o;const a=b(),s=ne(t);"function"==typeof n.willClose&&n.willClose(t),null===(o=r.eventEmitter)||void 0===o||o.emit("willClose",t),s?wt(e,t,a,n.returnFocus,n.didClose):ut(e,a,n.returnFocus,n.didClose)},wt=(e,t,n,o,a)=>{r.swalCloseEventFinishedCallback=ut.bind(null,e,n,o,a);const s=function(e){var n;e.target===t&&(null===(n=r.swalCloseEventFinishedCallback)||void 0===n||n.call(r),delete r.swalCloseEventFinishedCallback,t.removeEventListener("animationend",s),t.removeEventListener("transitionend",s))};t.addEventListener("animationend",s),t.addEventListener("transitionend",s)},bt=(e,t)=>{setTimeout((()=>{var n;"function"==typeof t&&t.bind(e.params)(),null===(n=r.eventEmitter)||void 0===n||n.emit("didClose"),e._destroy&&e._destroy()}))},vt=e=>{let t=k();if(t||new er,t=k(),!t)return;const n=O();$()?K(E()):yt(t,e),Y(n),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},yt=(e,t)=>{const n=R(),r=O();n&&r&&(!t&&ee(S())&&(t=S()),Y(n),t&&(K(t),r.setAttribute("data-button-to-replace",t.className),n.insertBefore(r,t)),V([e,n],s.loading))},kt=e=>e.checked?1:0,Et=e=>e.checked?e.value:null,xt=e=>e.files&&e.files.length?null!==e.getAttribute("multiple")?e.files:e.files[0]:null,Ct=(e,t)=>{const n=k();if(!n)return;const r=e=>{"select"===t.input?function(e,t,n){const r=G(e,s.select);if(!r)return;const o=(e,t,r)=>{const o=document.createElement("option");o.value=r,B(o,t),o.selected=At(r,n.inputValue),e.appendChild(o)};t.forEach((e=>{const t=e[0],n=e[1];if(Array.isArray(n)){const e=document.createElement("optgroup");e.label=t,e.disabled=!1,r.appendChild(e),n.forEach((t=>o(e,t[1],t[0])))}else o(r,n,t)})),r.focus()}(n,Tt(e),t):"radio"===t.input&&function(e,t,n){const r=G(e,s.radio);if(!r)return;t.forEach((e=>{const t=e[0],o=e[1],a=document.createElement("input"),i=document.createElement("label");a.type="radio",a.name=s.radio,a.value=t,At(t,n.inputValue)&&(a.checked=!0);const l=document.createElement("span");B(l,o),l.className=s.label,i.appendChild(a),i.appendChild(l),r.appendChild(i)}));const o=r.querySelectorAll("input");o.length&&o[0].focus()}(n,Tt(e),t)};f(t.inputOptions)||w(t.inputOptions)?(vt(S()),g(t.inputOptions).then((t=>{e.hideLoading(),r(t)}))):"object"==typeof t.inputOptions?r(t.inputOptions):d("Unexpected type of inputOptions! Expected object, Map or Promise, got "+typeof t.inputOptions)},_t=(e,t)=>{const n=e.getInput();n&&(K(n),g(t.inputValue).then((r=>{n.value="number"===t.input?`${parseFloat(r)||0}`:`${r}`,Y(n),n.focus(),e.hideLoading()})).catch((t=>{d(`Error in inputValue promise: ${t}`),n.value="",Y(n),n.focus(),e.hideLoading()})))};const Tt=e=>{const t=[];return e instanceof Map?e.forEach(((e,n)=>{let r=e;"object"==typeof r&&(r=Tt(r)),t.push([n,r])})):Object.keys(e).forEach((n=>{let r=e[n];"object"==typeof r&&(r=Tt(r)),t.push([n,r])})),t},At=(e,t)=>!!t&&t.toString()===e.toString(),St=(e,t)=>{const n=me.innerParams.get(e);if(!n.input)return void d(`The "input" parameter is needed to be set when using returnInputValueOn${c(t)}`);const r=e.getInput(),o=((e,t)=>{const n=e.getInput();if(!n)return null;switch(t.input){case"checkbox":return kt(n);case"radio":return Et(n);case"file":return xt(n);default:return t.inputAutoTrim?n.value.trim():n.value}})(e,n);n.inputValidator?Nt(e,o,t):r&&!r.checkValidity()?(e.enableButtons(),e.showValidationMessage(n.validationMessage||r.validationMessage)):"deny"===t?Lt(e,o):Mt(e,o)},Nt=(e,t,n)=>{const r=me.innerParams.get(e);e.disableInput(),Promise.resolve().then((()=>g(r.inputValidator(t,r.validationMessage)))).then((r=>{e.enableButtons(),e.enableInput(),r?e.showValidationMessage(r):"deny"===n?Lt(e,t):Mt(e,t)}))},Lt=(e,t)=>{const n=me.innerParams.get(e||void 0);n.showLoaderOnDeny&&vt(L()),n.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then((()=>g(n.preDeny(t,n.validationMessage)))).then((n=>{!1===n?(e.hideLoading(),mt(e)):e.close({isDenied:!0,value:void 0===n?t:n})})).catch((t=>Rt(e||void 0,t)))):e.close({isDenied:!0,value:t})},Ot=(e,t)=>{e.close({isConfirmed:!0,value:t})},Rt=(e,t)=>{e.rejectPromise(t)},Mt=(e,t)=>{const n=me.innerParams.get(e||void 0);n.showLoaderOnConfirm&&vt(),n.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then((()=>g(n.preConfirm(t,n.validationMessage)))).then((n=>{ee(A())||!1===n?(e.hideLoading(),mt(e)):Ot(e,void 0===n?t:n)})).catch((t=>Rt(e||void 0,t)))):Ot(e,t)};function Pt(){const e=me.innerParams.get(this);if(!e)return;const t=me.domCache.get(this);K(t.loader),$()?e.icon&&Y(E()):It(t),Z([t.popup,t.actions],s.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const It=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Y(t[0],"inline-block"):!ee(S())&&!ee(L())&&!ee(N())&&K(e.actions)};function Dt(){const e=me.innerParams.get(this),t=me.domCache.get(this);return t?F(t.popup,e.input):null}function zt(e,t,n){const r=me.domCache.get(e);t.forEach((e=>{r[e].disabled=n}))}function $t(e,t){const n=k();if(n&&e)if("radio"===e.type){const e=n.querySelectorAll(`[name="${s.radio}"]`);for(let n=0;n<e.length;n++)e[n].disabled=t}else e.disabled=t}function Bt(){zt(this,["confirmButton","denyButton","cancelButton"],!1)}function Ht(){zt(this,["confirmButton","denyButton","cancelButton"],!0)}function qt(){$t(this.getInput(),!1)}function Ft(){$t(this.getInput(),!0)}function jt(e){const t=me.domCache.get(this),n=me.innerParams.get(this);B(t.validationMessage,e),t.validationMessage.className=s["validation-message"],n.customClass&&n.customClass.validationMessage&&V(t.validationMessage,n.customClass.validationMessage),Y(t.validationMessage);const r=this.getInput();r&&(r.setAttribute("aria-invalid","true"),r.setAttribute("aria-describedby",s["validation-message"]),j(r),V(r,s.inputerror))}function Ut(){const e=me.domCache.get(this);e.validationMessage&&K(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),Z(t,s.inputerror))}const Vt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},Zt=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],Gt={allowEnterKey:void 0},Wt=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],Yt=e=>Object.prototype.hasOwnProperty.call(Vt,e),Kt=e=>-1!==Zt.indexOf(e),Qt=e=>Gt[e],Xt=e=>{Yt(e)||u(`Unknown parameter "${e}"`)},Jt=e=>{Wt.includes(e)&&u(`The parameter "${e}" is incompatible with toasts`)},en=e=>{const t=Qt(e);t&&p(e,t)},tn=e=>{!1===e.backdrop&&e.allowOutsideClick&&u('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe"].includes(e.theme)&&u(`Invalid theme "${e.theme}". Expected "light", "dark", "auto", "minimal", "borderless", or "embed-iframe"`);for(const t in e)Xt(t),e.toast&&Jt(t),en(t)};function nn(e){const t=b(),n=k(),r=me.innerParams.get(this);if(!n||H(n,r.hideClass.popup))return void u("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");const o=rn(e),a=Object.assign({},r,o);tn(a),t.dataset.swal2Theme=a.theme,je(this,a),me.innerParams.set(this,a),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const rn=e=>{const t={};return Object.keys(e).forEach((n=>{Kt(n)?t[n]=e[n]:u(`Invalid parameter to update: ${n}`)})),t};function on(){const e=me.domCache.get(this),t=me.innerParams.get(this);t?(e.popup&&r.swalCloseEventFinishedCallback&&(r.swalCloseEventFinishedCallback(),delete r.swalCloseEventFinishedCallback),"function"==typeof t.didDestroy&&t.didDestroy(),r.eventEmitter.emit("didDestroy"),an(this)):sn(this)}const an=e=>{sn(e),delete e.params,delete r.keydownHandler,delete r.keydownTarget,delete r.currentInstance},sn=e=>{e.isAwaitingPromise?(ln(me,e),e.isAwaitingPromise=!0):(ln(tt,e),ln(me,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},ln=(e,t)=>{for(const n in e)e[n].delete(t)};var cn=Object.freeze({__proto__:null,_destroy:on,close:dt,closeModal:dt,closePopup:dt,closeToast:dt,disableButtons:Ht,disableInput:Ft,disableLoading:Pt,enableButtons:Bt,enableInput:qt,getInput:Dt,handleAwaitingPromise:mt,hideLoading:Pt,rejectPromise:pt,resetValidationMessage:Ut,showValidationMessage:jt,update:nn});const un=(e,t,n)=>{t.popup.onclick=()=>{e&&(dn(e)||e.timer||e.input)||n(Ve.close)}},dn=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let hn=!1;const pn=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(hn=!0)}}},mn=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(t){e.popup.onmouseup=()=>{},(t.target===e.popup||t.target instanceof HTMLElement&&e.popup.contains(t.target))&&(hn=!0)}}},fn=(e,t,n)=>{t.container.onclick=r=>{hn?hn=!1:r.target===t.container&&m(e.allowOutsideClick)&&n(Ve.backdrop)}},gn=e=>e instanceof Element||(e=>"object"==typeof e&&e.jquery)(e);const wn=()=>{if(r.timeout)return(()=>{const e=P();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const n=t/parseInt(window.getComputedStyle(e).width)*100;e.style.width=`${n}%`})(),r.timeout.stop()},bn=()=>{if(r.timeout){const e=r.timeout.start();return re(e),e}};let vn=!1;const yn={};const kn=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const e in yn){const n=t.getAttribute(e);if(n)return void yn[e].fire({template:n})}};r.eventEmitter=new class{constructor(){this.events={}}_getHandlersByEventName(e){return void 0===this.events[e]&&(this.events[e]=[]),this.events[e]}on(e,t){const n=this._getHandlersByEventName(e);n.includes(t)||n.push(t)}once(e,t){const n=(...r)=>{this.removeListener(e,n),t.apply(this,r)};this.on(e,n)}emit(e,...t){this._getHandlersByEventName(e).forEach((e=>{try{e.apply(this,t)}catch(e){console.error(e)}}))}removeListener(e,t){const n=this._getHandlersByEventName(e),r=n.indexOf(t);r>-1&&n.splice(r,1)}removeAllListeners(e){void 0!==this.events[e]&&(this.events[e].length=0)}reset(){this.events={}}};var En=Object.freeze({__proto__:null,argsToParams:e=>{const t={};return"object"!=typeof e[0]||gn(e[0])?["title","html","icon"].forEach(((n,r)=>{const o=e[r];"string"==typeof o||gn(o)?t[n]=o:void 0!==o&&d(`Unexpected type of ${n}! Expected "string" or "Element", got ${typeof o}`)})):Object.assign(t,e[0]),t},bindClickHandler:function(e="data-swal-template"){yn[e]=this,vn||(document.body.addEventListener("click",kn),vn=!0)},clickCancel:()=>{var e;return null===(e=N())||void 0===e?void 0:e.click()},clickConfirm:Ue,clickDeny:()=>{var e;return null===(e=L())||void 0===e?void 0:e.click()},enableLoading:vt,fire:function(...e){return new this(...e)},getActions:R,getCancelButton:N,getCloseButton:I,getConfirmButton:S,getContainer:b,getDenyButton:L,getFocusableElements:D,getFooter:M,getHtmlContainer:C,getIcon:E,getIconContent:()=>y(s["icon-content"]),getImage:_,getInputLabel:()=>y(s["input-label"]),getLoader:O,getPopup:k,getProgressSteps:T,getTimerLeft:()=>r.timeout&&r.timeout.getTimerLeft(),getTimerProgressBar:P,getTitle:x,getValidationMessage:A,increaseTimer:e=>{if(r.timeout){const t=r.timeout.increase(e);return re(t,!0),t}},isDeprecatedParameter:Qt,isLoading:()=>{const e=k();return!!e&&e.hasAttribute("data-loading")},isTimerRunning:()=>!(!r.timeout||!r.timeout.isRunning()),isUpdatableParameter:Kt,isValidParameter:Yt,isVisible:()=>ee(k()),mixin:function(e){return class extends(this){_main(t,n){return super._main(t,Object.assign({},e,n))}}},off:(e,t)=>{e?t?r.eventEmitter.removeListener(e,t):r.eventEmitter.removeAllListeners(e):r.eventEmitter.reset()},on:(e,t)=>{r.eventEmitter.on(e,t)},once:(e,t)=>{r.eventEmitter.once(e,t)},resumeTimer:bn,showLoading:vt,stopTimer:wn,toggleTimer:()=>{const e=r.timeout;return e&&(e.running?wn():bn())}});class xn{constructor(e,t){this.callback=e,this.remaining=t,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=(new Date).getTime()-this.started.getTime()),this.remaining}increase(e){const t=this.running;return t&&this.stop(),this.remaining+=e,t&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Cn=["swal-title","swal-html","swal-footer"],Tn=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach((e=>{Pn(e,["name","value"]);const n=e.getAttribute("name"),r=e.getAttribute("value");n&&r&&(t[n]="boolean"==typeof Vt[n]?"false"!==r:"object"==typeof Vt[n]?JSON.parse(r):r)})),t},An=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach((e=>{const n=e.getAttribute("name"),r=e.getAttribute("value");n&&r&&(t[n]=new Function(`return ${r}`)())})),t},Sn=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach((e=>{Pn(e,["type","color","aria-label"]);const n=e.getAttribute("type");n&&["confirm","cancel","deny"].includes(n)&&(t[`${n}ButtonText`]=e.innerHTML,t[`show${c(n)}Button`]=!0,e.hasAttribute("color")&&(t[`${n}ButtonColor`]=e.getAttribute("color")),e.hasAttribute("aria-label")&&(t[`${n}ButtonAriaLabel`]=e.getAttribute("aria-label")))})),t},Nn=e=>{const t={},n=e.querySelector("swal-image");return n&&(Pn(n,["src","width","height","alt"]),n.hasAttribute("src")&&(t.imageUrl=n.getAttribute("src")||void 0),n.hasAttribute("width")&&(t.imageWidth=n.getAttribute("width")||void 0),n.hasAttribute("height")&&(t.imageHeight=n.getAttribute("height")||void 0),n.hasAttribute("alt")&&(t.imageAlt=n.getAttribute("alt")||void 0)),t},Ln=e=>{const t={},n=e.querySelector("swal-icon");return n&&(Pn(n,["type","color"]),n.hasAttribute("type")&&(t.icon=n.getAttribute("type")),n.hasAttribute("color")&&(t.iconColor=n.getAttribute("color")),t.iconHtml=n.innerHTML),t},On=e=>{const t={},n=e.querySelector("swal-input");n&&(Pn(n,["type","label","placeholder","value"]),t.input=n.getAttribute("type")||"text",n.hasAttribute("label")&&(t.inputLabel=n.getAttribute("label")),n.hasAttribute("placeholder")&&(t.inputPlaceholder=n.getAttribute("placeholder")),n.hasAttribute("value")&&(t.inputValue=n.getAttribute("value")));const r=Array.from(e.querySelectorAll("swal-input-option"));return r.length&&(t.inputOptions={},r.forEach((e=>{Pn(e,["value"]);const n=e.getAttribute("value");if(!n)return;const r=e.innerHTML;t.inputOptions[n]=r}))),t},Rn=(e,t)=>{const n={};for(const r in t){const o=t[r],a=e.querySelector(o);a&&(Pn(a,[]),n[o.replace(/^swal-/,"")]=a.innerHTML.trim())}return n},Mn=e=>{const t=Cn.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach((e=>{const n=e.tagName.toLowerCase();t.includes(n)||u(`Unrecognized element <${n}>`)}))},Pn=(e,t)=>{Array.from(e.attributes).forEach((n=>{-1===t.indexOf(n.name)&&u([`Unrecognized attribute "${n.name}" on <${e.tagName.toLowerCase()}>.`,t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."])}))},In=e=>{const t=b(),n=k();"function"==typeof e.willOpen&&e.willOpen(n),r.eventEmitter.emit("willOpen",n);const o=window.getComputedStyle(document.body).overflowY;Bn(t,n,e),setTimeout((()=>{zn(t,n)}),10),z()&&($n(t,e.scrollbarPadding,o),(()=>{const e=b();Array.from(document.body.children).forEach((t=>{t.contains(e)||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")||""),t.setAttribute("aria-hidden","true"))}))})()),$()||r.previousActiveElement||(r.previousActiveElement=document.activeElement),"function"==typeof e.didOpen&&setTimeout((()=>e.didOpen(n))),r.eventEmitter.emit("didOpen",n),Z(t,s["no-transition"])},Dn=e=>{const t=k();if(e.target!==t)return;const n=b();t.removeEventListener("animationend",Dn),t.removeEventListener("transitionend",Dn),n.style.overflowY="auto"},zn=(e,t)=>{ne(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",Dn),t.addEventListener("transitionend",Dn)):e.style.overflowY="auto"},$n=(e,t,n)=>{(()=>{if(rt&&!H(document.body,s.iosfix)){const e=document.body.scrollTop;document.body.style.top=-1*e+"px",V(document.body,s.iosfix),ot()}})(),t&&"hidden"!==n&&ct(n),setTimeout((()=>{e.scrollTop=0}))},Bn=(e,t,n)=>{V(e,n.showClass.backdrop),n.animation?(t.style.setProperty("opacity","0","important"),Y(t,"grid"),setTimeout((()=>{V(t,n.showClass.popup),t.style.removeProperty("opacity")}),10)):Y(t,"grid"),V([document.documentElement,document.body],s.shown),n.heightAuto&&n.backdrop&&!n.toast&&V([document.documentElement,document.body],s["height-auto"])};var Hn=(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),qn=(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL");function Fn(e){(function(e){e.inputValidator||("email"===e.input&&(e.inputValidator=Hn),"url"===e.input&&(e.inputValidator=qn))})(e),e.showLoaderOnConfirm&&!e.preConfirm&&u("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),function(e){(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(u('Target parameter is not valid, defaulting to "body"'),e.target="body")}(e),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />")),se(e)}let jn;var Un=new WeakMap;class Vn{constructor(...t){if(n(this,Un,void 0),"undefined"==typeof window)return;jn=this;const r=Object.freeze(this.constructor.argsToParams(t));var o,a,s;this.params=r,this.isAwaitingPromise=!1,o=Un,a=this,s=this._main(jn.params),o.set(e(o,a),s)}_main(e,t={}){if(tn(Object.assign({},t,e)),r.currentInstance){const e=tt.swalPromiseResolve.get(r.currentInstance),{isAwaitingPromise:t}=r.currentInstance;r.currentInstance._destroy(),t||e({isDismissed:!0}),z()&&nt()}r.currentInstance=jn;const n=Gn(e,t);Fn(n),Object.freeze(n),r.timeout&&(r.timeout.stop(),delete r.timeout),clearTimeout(r.restoreFocusTimeout);const o=Wn(jn);return je(jn,n),me.innerParams.set(jn,n),Zn(jn,o,n)}then(e){return t(Un,this).then(e)}finally(e){return t(Un,this).finally(e)}}const Zn=(e,t,n)=>new Promise(((o,a)=>{const s=t=>{e.close({isDismissed:!0,dismiss:t})};tt.swalPromiseResolve.set(e,o),tt.swalPromiseReject.set(e,a),t.confirmButton.onclick=()=>{(e=>{const t=me.innerParams.get(e);e.disableButtons(),t.input?St(e,"confirm"):Mt(e,!0)})(e)},t.denyButton.onclick=()=>{(e=>{const t=me.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?St(e,"deny"):Lt(e,!1)})(e)},t.cancelButton.onclick=()=>{((e,t)=>{e.disableButtons(),t(Ve.cancel)})(e,s)},t.closeButton.onclick=()=>{s(Ve.close)},((e,t,n)=>{e.toast?un(e,t,n):(pn(t),mn(t),fn(e,t,n))})(n,t,s),((e,t,n)=>{Ze(e),t.toast||(e.keydownHandler=e=>Ke(t,e,n),e.keydownTarget=t.keydownListenerCapture?window:k(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)})(r,n,s),((e,t)=>{"select"===t.input||"radio"===t.input?Ct(e,t):["text","email","number","tel","textarea"].some((e=>e===t.input))&&(f(t.inputValue)||w(t.inputValue))&&(vt(S()),_t(e,t))})(e,n),In(n),Yn(r,n,s),Kn(t,n),setTimeout((()=>{t.container.scrollTop=0}))})),Gn=(e,t)=>{const n=(e=>{const t="string"==typeof e.template?document.querySelector(e.template):e.template;if(!t)return{};const n=t.content;return Mn(n),Object.assign(Tn(n),An(n),Sn(n),Nn(n),Ln(n),On(n),Rn(n,Cn))})(e),r=Object.assign({},Vt,t,n,e);return r.showClass=Object.assign({},Vt.showClass,r.showClass),r.hideClass=Object.assign({},Vt.hideClass,r.hideClass),!1===r.animation&&(r.showClass={backdrop:"swal2-noanimation"},r.hideClass={}),r},Wn=e=>{const t={popup:k(),container:b(),actions:R(),confirmButton:S(),denyButton:L(),cancelButton:N(),loader:O(),closeButton:I(),validationMessage:A(),progressSteps:T()};return me.domCache.set(e,t),t},Yn=(e,t,n)=>{const r=P();K(r),t.timer&&(e.timeout=new xn((()=>{n("timer"),delete e.timeout}),t.timer),t.timerProgressBar&&(Y(r),q(r,t,"timerProgressBar"),setTimeout((()=>{e.timeout&&e.timeout.running&&re(t.timer)}))))},Kn=(e,t)=>{if(!t.toast)return m(t.allowEnterKey)?void(Qn(e)||Xn(e,t)||Ge(-1,1)):(p("allowEnterKey"),void Jn())},Qn=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const e of t)if(e instanceof HTMLElement&&ee(e))return e.focus(),!0;return!1},Xn=(e,t)=>t.focusDeny&&ee(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&ee(e.cancelButton)?(e.cancelButton.focus(),!0):!(!t.focusConfirm||!ee(e.confirmButton)||(e.confirmButton.focus(),0)),Jn=()=>{document.activeElement instanceof HTMLElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};if("undefined"!=typeof window&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/864e5>3&&setTimeout((()=>{document.body.style.pointerEvents="none";const e=document.createElement("audio");e.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",e.loop=!0,document.body.appendChild(e),setTimeout((()=>{e.play().catch((()=>{}))}),2500)}),500):localStorage.setItem("swal-initiation",`${e}`)}Vn.prototype.disableButtons=Ht,Vn.prototype.enableButtons=Bt,Vn.prototype.getInput=Dt,Vn.prototype.disableInput=Ft,Vn.prototype.enableInput=qt,Vn.prototype.hideLoading=Pt,Vn.prototype.disableLoading=Pt,Vn.prototype.showValidationMessage=jt,Vn.prototype.resetValidationMessage=Ut,Vn.prototype.close=dt,Vn.prototype.closePopup=dt,Vn.prototype.closeModal=dt,Vn.prototype.closeToast=dt,Vn.prototype.rejectPromise=pt,Vn.prototype.update=nn,Vn.prototype._destroy=on,Object.assign(Vn,En),Object.keys(cn).forEach((e=>{Vn[e]=function(...t){return jn&&jn[e]?jn[e](...t):null}})),Vn.DismissReason=Ve,Vn.version="11.21.2";const er=Vn;return er.default=er,er}(),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2),"undefined"!=typeof document&&function(e,t){var n=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-footer-border-color: #eee;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-confirm-button-border: 0;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-border: 0;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-border: 0;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-button-transition);box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border:var(--swal2-confirm-button-border);border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border:var(--swal2-deny-button-border);border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border:var(--swal2-cancel-button-border);border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), black 10%)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), black 20%)}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-outline)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);color:inherit;font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:1px solid #d9d9d9;border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}')},399:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),a=this&&this.__assign||function(){return a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.CDATA=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var s=n(994),i=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(e){return void 0===e&&(e=!1),E(this,e)},e}();t.Node=i;var l=function(e){function t(t){var n=e.call(this)||this;return n.data=t,n}return o(t,e),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:!1,configurable:!0}),t}(i);t.DataNode=l;var c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Text,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(l);t.Text=c;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Comment,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(l);t.Comment=u;var d=function(e){function t(t,n){var r=e.call(this,n)||this;return r.name=t,r.type=s.ElementType.Directive,r}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(l);t.ProcessingInstruction=d;var h=function(e){function t(t){var n=e.call(this)||this;return n.children=t,n}return o(t,e),Object.defineProperty(t.prototype,"firstChild",{get:function(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:!1,configurable:!0}),t}(i);t.NodeWithChildren=h;var p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.CDATA,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(h);t.CDATA=p;var m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Root,t}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(h);t.Document=m;var f=function(e){function t(t,n,r,o){void 0===r&&(r=[]),void 0===o&&(o="script"===t?s.ElementType.Script:"style"===t?s.ElementType.Style:s.ElementType.Tag);var a=e.call(this,r)||this;return a.name=t,a.attribs=n,a.type=o,a}return o(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var n,r;return{name:t,value:e.attribs[t],namespace:null===(n=e["x-attribsNamespace"])||void 0===n?void 0:n[t],prefix:null===(r=e["x-attribsPrefix"])||void 0===r?void 0:r[t]}}))},enumerable:!1,configurable:!0}),t}(h);function g(e){return(0,s.isTag)(e)}function w(e){return e.type===s.ElementType.CDATA}function b(e){return e.type===s.ElementType.Text}function v(e){return e.type===s.ElementType.Comment}function y(e){return e.type===s.ElementType.Directive}function k(e){return e.type===s.ElementType.Root}function E(e,t){var n;if(void 0===t&&(t=!1),b(e))n=new c(e.data);else if(v(e))n=new u(e.data);else if(g(e)){var r=t?x(e.children):[],o=new f(e.name,a({},e.attribs),r);r.forEach((function(e){return e.parent=o})),null!=e.namespace&&(o.namespace=e.namespace),e["x-attribsNamespace"]&&(o["x-attribsNamespace"]=a({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(o["x-attribsPrefix"]=a({},e["x-attribsPrefix"])),n=o}else if(w(e)){r=t?x(e.children):[];var s=new p(r);r.forEach((function(e){return e.parent=s})),n=s}else if(k(e)){r=t?x(e.children):[];var i=new m(r);r.forEach((function(e){return e.parent=i})),e["x-mode"]&&(i["x-mode"]=e["x-mode"]),n=i}else{if(!y(e))throw new Error("Not implemented yet: ".concat(e.type));var l=new d(e.name,e.data);null!=e["x-name"]&&(l["x-name"]=e["x-name"],l["x-publicId"]=e["x-publicId"],l["x-systemId"]=e["x-systemId"]),n=l}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function x(e){for(var t=e.map((function(e){return E(e,!0)})),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}t.Element=f,t.isTag=g,t.isCDATA=w,t.isText=b,t.isComment=v,t.isDirective=y,t.isDocument=k,t.hasChildren=function(e){return Object.prototype.hasOwnProperty.call(e,"children")},t.cloneNode=E},401:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,i=/^[;\s]*/,l=/^\s+|\s+$/g,c="";function u(e){return e?e.replace(l,c):c}e.exports=function(e,l){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];l=l||{};var d=1,h=1;function p(e){var t=e.match(n);t&&(d+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function m(){var e={line:d,column:h};return function(t){return t.position=new f(e),v(),t}}function f(e){this.start=e,this.end={line:d,column:h},this.source=l.source}f.prototype.content=e;var g=[];function w(t){var n=new Error(l.source+":"+d+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=d,n.column=h,n.source=e,!l.silent)throw n;g.push(n)}function b(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function v(){b(r)}function y(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var t=m();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;c!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,c===e.charAt(n-1))return w("End of comment missing");var r=e.slice(2,n-2);return h+=2,p(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}function E(){var e=m(),n=b(o);if(n){if(k(),!b(a))return w("property missing ':'");var r=b(s),l=e({type:"declaration",property:u(n[0].replace(t,c)),value:r?u(r[0].replace(t,c)):c});return b(i),l}}return v(),function(){var e,t=[];for(y(t);e=E();)!1!==e&&(t.push(e),y(t));return t}()}},403:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(440)),o=n(467);function a(e,t){var n={};return e&&"string"==typeof e?((0,r.default)(e,(function(e,r){e&&r&&(n[(0,o.camelCase)(e,t)]=r)})),n):n}a.default=a,e.exports=a},404:(e,t,n)=>{"use strict";n.d(t,{A:()=>ne});const{entries:r,setPrototypeOf:o,isFrozen:a,getPrototypeOf:s,getOwnPropertyDescriptor:i}=Object;let{freeze:l,seal:c,create:u}=Object,{apply:d,construct:h}="undefined"!=typeof Reflect&&Reflect;l||(l=function(e){return e}),c||(c=function(e){return e}),d||(d=function(e,t,n){return e.apply(t,n)}),h||(h=function(e,t){return new e(...t)});const p=S(Array.prototype.forEach),m=S(Array.prototype.lastIndexOf),f=S(Array.prototype.pop),g=S(Array.prototype.push),w=S(Array.prototype.splice),b=S(String.prototype.toLowerCase),v=S(String.prototype.toString),y=S(String.prototype.match),k=S(String.prototype.replace),E=S(String.prototype.indexOf),x=S(String.prototype.trim),C=S(Object.prototype.hasOwnProperty),_=S(RegExp.prototype.test),T=(A=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(A,t)});var A;function S(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return d(e,t,r)}}function N(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b;o&&o(e,null);let r=t.length;for(;r--;){let o=t[r];if("string"==typeof o){const e=n(o);e!==o&&(a(t)||(t[r]=e),o=e)}e[o]=!0}return e}function L(e){for(let t=0;t<e.length;t++)C(e,t)||(e[t]=null);return e}function O(e){const t=u(null);for(const[n,o]of r(e))C(e,n)&&(Array.isArray(o)?t[n]=L(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=O(o):t[n]=o);return t}function R(e,t){for(;null!==e;){const n=i(e,t);if(n){if(n.get)return S(n.get);if("function"==typeof n.value)return S(n.value)}e=s(e)}return function(){return null}}const M=l(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),P=l(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=l(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),D=l(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),z=l(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),$=l(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),B=l(["#text"]),H=l(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),q=l(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),F=l(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),j=l(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),U=c(/\{\{[\w\W]*|[\w\W]*\}\}/gm),V=c(/<%[\w\W]*|[\w\W]*%>/gm),Z=c(/\$\{[\w\W]*/gm),G=c(/^data-[\-\w.\u00B7-\uFFFF]+$/),W=c(/^aria-[\-\w]+$/),Y=c(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),K=c(/^(?:\w+script|data):/i),Q=c(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),X=c(/^html$/i),J=c(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:W,ATTR_WHITESPACE:Q,CUSTOM_ELEMENT:J,DATA_ATTR:G,DOCTYPE_NAME:X,ERB_EXPR:V,IS_ALLOWED_URI:Y,IS_SCRIPT_OR_DATA:K,MUSTACHE_EXPR:U,TMPLIT_EXPR:Z});const te=function(){return"undefined"==typeof window?null:window};var ne=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return n.isSupported=!1,n;let{document:o}=t;const a=o,s=a.currentScript,{DocumentFragment:i,HTMLTemplateElement:c,Node:d,Element:h,NodeFilter:A,NamedNodeMap:S=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:L,DOMParser:U,trustedTypes:V}=t,Z=h.prototype,G=R(Z,"cloneNode"),W=R(Z,"remove"),K=R(Z,"nextSibling"),Q=R(Z,"childNodes"),J=R(Z,"parentNode");if("function"==typeof c){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let ne,re="";const{implementation:oe,createNodeIterator:ae,createDocumentFragment:se,getElementsByTagName:ie}=o,{importNode:le}=a;let ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof r&&"function"==typeof J&&oe&&void 0!==oe.createHTMLDocument;const{MUSTACHE_EXPR:ue,ERB_EXPR:de,TMPLIT_EXPR:he,DATA_ATTR:pe,ARIA_ATTR:me,IS_SCRIPT_OR_DATA:fe,ATTR_WHITESPACE:ge,CUSTOM_ELEMENT:we}=ee;let{IS_ALLOWED_URI:be}=ee,ve=null;const ye=N({},[...M,...P,...I,...z,...B]);let ke=null;const Ee=N({},[...H,...q,...F,...j]);let xe=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ce=null,_e=null,Te=!0,Ae=!0,Se=!1,Ne=!0,Le=!1,Oe=!0,Re=!1,Me=!1,Pe=!1,Ie=!1,De=!1,ze=!1,$e=!0,Be=!1,He=!0,qe=!1,Fe={},je=null;const Ue=N({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ve=null;const Ze=N({},["audio","video","img","source","image","track"]);let Ge=null;const We=N({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",Ke="http://www.w3.org/2000/svg",Qe="http://www.w3.org/1999/xhtml";let Xe=Qe,Je=!1,et=null;const tt=N({},[Ye,Ke,Qe],v);let nt=N({},["mi","mo","mn","ms","mtext"]),rt=N({},["annotation-xml"]);const ot=N({},["title","style","font","a","script"]);let at=null;const st=["application/xhtml+xml","text/html"];let it=null,lt=null;const ct=o.createElement("form"),ut=function(e){return e instanceof RegExp||e instanceof Function},dt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!lt||lt!==e){if(e&&"object"==typeof e||(e={}),e=O(e),at=-1===st.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,it="application/xhtml+xml"===at?v:b,ve=C(e,"ALLOWED_TAGS")?N({},e.ALLOWED_TAGS,it):ye,ke=C(e,"ALLOWED_ATTR")?N({},e.ALLOWED_ATTR,it):Ee,et=C(e,"ALLOWED_NAMESPACES")?N({},e.ALLOWED_NAMESPACES,v):tt,Ge=C(e,"ADD_URI_SAFE_ATTR")?N(O(We),e.ADD_URI_SAFE_ATTR,it):We,Ve=C(e,"ADD_DATA_URI_TAGS")?N(O(Ze),e.ADD_DATA_URI_TAGS,it):Ze,je=C(e,"FORBID_CONTENTS")?N({},e.FORBID_CONTENTS,it):Ue,Ce=C(e,"FORBID_TAGS")?N({},e.FORBID_TAGS,it):O({}),_e=C(e,"FORBID_ATTR")?N({},e.FORBID_ATTR,it):O({}),Fe=!!C(e,"USE_PROFILES")&&e.USE_PROFILES,Te=!1!==e.ALLOW_ARIA_ATTR,Ae=!1!==e.ALLOW_DATA_ATTR,Se=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ne=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Le=e.SAFE_FOR_TEMPLATES||!1,Oe=!1!==e.SAFE_FOR_XML,Re=e.WHOLE_DOCUMENT||!1,Ie=e.RETURN_DOM||!1,De=e.RETURN_DOM_FRAGMENT||!1,ze=e.RETURN_TRUSTED_TYPE||!1,Pe=e.FORCE_BODY||!1,$e=!1!==e.SANITIZE_DOM,Be=e.SANITIZE_NAMED_PROPS||!1,He=!1!==e.KEEP_CONTENT,qe=e.IN_PLACE||!1,be=e.ALLOWED_URI_REGEXP||Y,Xe=e.NAMESPACE||Qe,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,rt=e.HTML_INTEGRATION_POINTS||rt,xe=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(xe.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(xe.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(xe.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Le&&(Ae=!1),De&&(Ie=!0),Fe&&(ve=N({},B),ke=[],!0===Fe.html&&(N(ve,M),N(ke,H)),!0===Fe.svg&&(N(ve,P),N(ke,q),N(ke,j)),!0===Fe.svgFilters&&(N(ve,I),N(ke,q),N(ke,j)),!0===Fe.mathMl&&(N(ve,z),N(ke,F),N(ke,j))),e.ADD_TAGS&&(ve===ye&&(ve=O(ve)),N(ve,e.ADD_TAGS,it)),e.ADD_ATTR&&(ke===Ee&&(ke=O(ke)),N(ke,e.ADD_ATTR,it)),e.ADD_URI_SAFE_ATTR&&N(Ge,e.ADD_URI_SAFE_ATTR,it),e.FORBID_CONTENTS&&(je===Ue&&(je=O(je)),N(je,e.FORBID_CONTENTS,it)),He&&(ve["#text"]=!0),Re&&N(ve,["html","head","body"]),ve.table&&(N(ve,["tbody"]),delete Ce.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,re=ne.createHTML("")}else void 0===ne&&(ne=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(V,s)),null!==ne&&"string"==typeof re&&(re=ne.createHTML(""));l&&l(e),lt=e}},ht=N({},[...P,...I,...D]),pt=N({},[...z,...$]),mt=function(e){g(n.removed,{element:e});try{J(e).removeChild(e)}catch(t){W(e)}},ft=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Ie||De)try{mt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){let t=null,n=null;if(Pe)e="<remove></remove>"+e;else{const t=y(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===at&&Xe===Qe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=ne?ne.createHTML(e):e;if(Xe===Qe)try{t=(new U).parseFromString(r,at)}catch(e){}if(!t||!t.documentElement){t=oe.createDocument(Xe,"template",null);try{t.documentElement.innerHTML=Je?re:r}catch(e){}}const a=t.body||t.documentElement;return e&&n&&a.insertBefore(o.createTextNode(n),a.childNodes[0]||null),Xe===Qe?ie.call(t,Re?"html":"body")[0]:Re?t.documentElement:a},wt=function(e){return ae.call(e.ownerDocument||e,e,A.SHOW_ELEMENT|A.SHOW_COMMENT|A.SHOW_TEXT|A.SHOW_PROCESSING_INSTRUCTION|A.SHOW_CDATA_SECTION,null)},bt=function(e){return e instanceof L&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof S)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},vt=function(e){return"function"==typeof d&&e instanceof d};function yt(e,t,r){p(e,(e=>{e.call(n,t,r,lt)}))}const kt=function(e){let t=null;if(yt(ce.beforeSanitizeElements,e,null),bt(e))return mt(e),!0;const r=it(e.nodeName);if(yt(ce.uponSanitizeElement,e,{tagName:r,allowedTags:ve}),Oe&&e.hasChildNodes()&&!vt(e.firstElementChild)&&_(/<[/\w!]/g,e.innerHTML)&&_(/<[/\w!]/g,e.textContent))return mt(e),!0;if(7===e.nodeType)return mt(e),!0;if(Oe&&8===e.nodeType&&_(/<[/\w]/g,e.data))return mt(e),!0;if(!ve[r]||Ce[r]){if(!Ce[r]&&xt(r)){if(xe.tagNameCheck instanceof RegExp&&_(xe.tagNameCheck,r))return!1;if(xe.tagNameCheck instanceof Function&&xe.tagNameCheck(r))return!1}if(He&&!je[r]){const t=J(e)||e.parentNode,n=Q(e)||e.childNodes;if(n&&t)for(let r=n.length-1;r>=0;--r){const o=G(n[r],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,K(e))}}return mt(e),!0}return e instanceof h&&!function(e){let t=J(e);t&&t.tagName||(t={namespaceURI:Xe,tagName:"template"});const n=b(e.tagName),r=b(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===Ke?t.namespaceURI===Qe?"svg"===n:t.namespaceURI===Ye?"svg"===n&&("annotation-xml"===r||nt[r]):Boolean(ht[n]):e.namespaceURI===Ye?t.namespaceURI===Qe?"math"===n:t.namespaceURI===Ke?"math"===n&&rt[r]:Boolean(pt[n]):e.namespaceURI===Qe?!(t.namespaceURI===Ke&&!rt[r])&&!(t.namespaceURI===Ye&&!nt[r])&&!pt[n]&&(ot[n]||!ht[n]):!("application/xhtml+xml"!==at||!et[e.namespaceURI]))}(e)?(mt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!_(/<\/no(script|embed|frames)/i,e.innerHTML)?(Le&&3===e.nodeType&&(t=e.textContent,p([ue,de,he],(e=>{t=k(t,e," ")})),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),yt(ce.afterSanitizeElements,e,null),!1):(mt(e),!0)},Et=function(e,t,n){if($e&&("id"===t||"name"===t)&&(n in o||n in ct))return!1;if(Ae&&!_e[t]&&_(pe,t));else if(Te&&_(me,t));else if(!ke[t]||_e[t]){if(!(xt(e)&&(xe.tagNameCheck instanceof RegExp&&_(xe.tagNameCheck,e)||xe.tagNameCheck instanceof Function&&xe.tagNameCheck(e))&&(xe.attributeNameCheck instanceof RegExp&&_(xe.attributeNameCheck,t)||xe.attributeNameCheck instanceof Function&&xe.attributeNameCheck(t))||"is"===t&&xe.allowCustomizedBuiltInElements&&(xe.tagNameCheck instanceof RegExp&&_(xe.tagNameCheck,n)||xe.tagNameCheck instanceof Function&&xe.tagNameCheck(n))))return!1}else if(Ge[t]);else if(_(be,k(n,ge,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==E(n,"data:")||!Ve[e])if(Se&&!_(fe,k(n,ge,"")));else if(n)return!1;return!0},xt=function(e){return"annotation-xml"!==e&&y(e,we)},Ct=function(e){yt(ce.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||bt(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ke,forceKeepAttr:void 0};let o=t.length;for(;o--;){const a=t[o],{name:s,namespaceURI:i,value:l}=a,c=it(s),u=l;let d="value"===s?u:x(u);if(r.attrName=c,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,yt(ce.uponSanitizeAttribute,e,r),d=r.attrValue,!Be||"id"!==c&&"name"!==c||(ft(s,e),d="user-content-"+d),Oe&&_(/((--!?|])>)|<\/(style|title)/i,d)){ft(s,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){ft(s,e);continue}if(!Ne&&_(/\/>/i,d)){ft(s,e);continue}Le&&p([ue,de,he],(e=>{d=k(d,e," ")}));const h=it(e.nodeName);if(Et(h,c,d)){if(ne&&"object"==typeof V&&"function"==typeof V.getAttributeType)if(i);else switch(V.getAttributeType(h,c)){case"TrustedHTML":d=ne.createHTML(d);break;case"TrustedScriptURL":d=ne.createScriptURL(d)}if(d!==u)try{i?e.setAttributeNS(i,s,d):e.setAttribute(s,d),bt(e)?mt(e):f(n.removed)}catch(t){ft(s,e)}}else ft(s,e)}yt(ce.afterSanitizeAttributes,e,null)},_t=function e(t){let n=null;const r=wt(t);for(yt(ce.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)yt(ce.uponSanitizeShadowNode,n,null),kt(n),Ct(n),n.content instanceof i&&e(n.content);yt(ce.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,o=null,s=null,l=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!vt(e)){if("function"!=typeof e.toString)throw T("toString is not a function");if("string"!=typeof(e=e.toString()))throw T("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Me||dt(t),n.removed=[],"string"==typeof e&&(qe=!1),qe){if(e.nodeName){const t=it(e.nodeName);if(!ve[t]||Ce[t])throw T("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof d)r=gt("\x3c!----\x3e"),o=r.ownerDocument.importNode(e,!0),1===o.nodeType&&"BODY"===o.nodeName||"HTML"===o.nodeName?r=o:r.appendChild(o);else{if(!Ie&&!Le&&!Re&&-1===e.indexOf("<"))return ne&&ze?ne.createHTML(e):e;if(r=gt(e),!r)return Ie?null:ze?re:""}r&&Pe&&mt(r.firstChild);const c=wt(qe?e:r);for(;s=c.nextNode();)kt(s),Ct(s),s.content instanceof i&&_t(s.content);if(qe)return e;if(Ie){if(De)for(l=se.call(r.ownerDocument);r.firstChild;)l.appendChild(r.firstChild);else l=r;return(ke.shadowroot||ke.shadowrootmode)&&(l=le.call(a,l,!0)),l}let u=Re?r.outerHTML:r.innerHTML;return Re&&ve["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&_(X,r.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+u),Le&&p([ue,de,he],(e=>{u=k(u,e," ")})),ne&&ze?ne.createHTML(u):u},n.setConfig=function(){dt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Me=!0},n.clearConfig=function(){lt=null,Me=!1},n.isValidAttribute=function(e,t,n){lt||dt({});const r=it(e),o=it(t);return Et(r,o,n)},n.addHook=function(e,t){"function"==typeof t&&g(ce[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=m(ce[e],t);return-1===n?void 0:w(ce[e],n,1)[0]}return f(ce[e])},n.removeHooks=function(e){ce[e]=[]},n.removeAllHooks=function(){ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()},414:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];var t=e.match(s),n=t?t[1]:void 0;return(0,a.formatDOM)((0,o.default)(e),null,n)};var o=r(n(97)),a=n(166),s=/<(![a-zA-Z\s]+)>/},440:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,o.default)(e),a="function"==typeof t;return r.forEach((function(e){if("declaration"===e.type){var r=e.property,o=e.value;a?t(r,o,e):o&&((n=n||{})[r]=o)}})),n};var o=r(n(401))},467:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,o=/^[^-]+$/,a=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,i=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||o.test(e)||n.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(a,l)).replace(r,i))}},609:e=>{"use strict";e.exports=window.React},614:(e,t)=>{t.SAME=0,t.CAMELCASE=1,t.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},619:e=>{"use strict";e.exports=window.wp.hooks},642:()=>{},723:e=>{"use strict";e.exports=window.wp.i18n},749:(e,t,n)=>{"use strict";var r=n(609),o=n(619),a=n(723),s=n(404);const i=()=>(0,r.createElement)("svg",{width:38,height:46,viewBox:"0 0 38 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"a",fill:"#fff"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z"})),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("path",{d:"m33.764 11.967 1.979-1.979zm0 23.745 1.979 1.979zm-9.1 1.093v-2.798h-1.04l-.788.679zm-9.955 8.582h-2.798v6.107l4.625-3.988zm0-8.582h2.798v-2.798H14.71zM4.235 35.712l-1.978 1.979zm22.973-7.815 1.748 2.185 2.185-1.748-1.748-2.185zm-16.463-.039-2.111-1.836-1.837 2.112L8.91 29.97zm14.598-2.292 2.185-1.748-1.748-2.185-2.185 1.748zm-12.64.04 1.837-2.112-2.111-1.836-1.837 2.111zm-.885-6.151v-2.798H9.019v2.798zm0 2.798H9.019v2.798h2.799zm2.798-2.798h2.799v-2.798h-2.799zm0 2.798v2.798h2.799v-2.798zm8.769 0h-2.799v2.798h2.799zm0-2.798v-2.798h-2.799v2.798zm2.798 2.798v2.798h2.799v-2.798zm0-2.798h2.799v-2.798h-2.799zm1.212-11.38h-16.79v5.597h16.79zm8.348 1.913c-1.153-1.153-2.555-1.572-3.882-1.75-1.245-.168-2.787-.162-4.466-.162v5.596c1.838 0 2.935.006 3.72.112.361.048.542.106.623.139l.038.018.01.005h-.001q-.002-.003 0 0zm1.912 8.348c0-1.68.006-3.22-.161-4.467-.179-1.326-.598-2.728-1.751-3.88l-3.958 3.957q.003.001 0 0v-.002l.005.01.018.04c.033.08.09.26.139.621.106.786.112 1.883.112 3.721zm0 11.007V18.336H32.06v11.007zm-1.912 8.348c1.153-1.153 1.572-2.555 1.75-3.881.168-1.246.162-2.788.162-4.467H32.06c0 1.838-.006 2.935-.112 3.721-.049.36-.106.54-.14.622l-.021.047q0-.002 0 0zm-8.349 1.912c1.68 0 3.222.006 4.468-.161 1.326-.178 2.728-.598 3.88-1.751l-3.957-3.958q-.002.003 0 .001l.002-.001-.01.006-.04.017c-.08.034-.261.09-.621.14-.787.105-1.883.11-3.721.11zm-2.73 0h2.73v-5.596h-2.73zm-8.128 7.903 9.955-8.581-3.655-4.24-9.954 8.582zm-4.625-10.7v8.58h5.596v-8.58zm-1.306 2.797h4.104v-5.596h-4.104zm-8.348-1.912c1.153 1.153 2.555 1.573 3.881 1.75 1.246.168 2.787.162 4.467.162v-5.596c-1.838 0-2.935-.006-3.721-.112-.36-.048-.541-.105-.622-.139l-.039-.017-.01-.006.002.001q0 .002 0 0zM.344 29.343c0 1.68-.006 3.22.162 4.467.178 1.326.598 2.728 1.75 3.881l3.958-3.958q-.002-.002 0 0l.001.002-.006-.01-.017-.039c-.034-.081-.09-.262-.14-.622-.105-.786-.111-1.883-.111-3.721zm0-11.007v11.007h5.597V18.336zm1.913-8.348C1.104 11.141.684 12.543.506 13.87c-.168 1.246-.162 2.788-.162 4.467h5.597c0-1.838.006-2.935.112-3.72.048-.361.105-.542.139-.623l.017-.038.006-.01-.001.001q-.002.001 0 0zm8.348-1.912c-1.68 0-3.221-.006-4.467.161-1.326.179-2.728.598-3.881 1.751l3.957 3.958q.003-.003 0 0h-.001l.01-.005.039-.018c.081-.033.261-.09.622-.139.786-.106 1.883-.112 3.721-.112zm14.854 17.636c-4.21 3.368-9.1 3.32-12.877.034L8.909 29.97c5.97 5.192 13.887 5.04 20.047.112zm-2.301 1.603 1.864 2.33 4.37-3.496-1.864-2.33zm-12.29.402c2.313 2.012 5.075 3.117 7.99 3.119 2.905.002 5.74-1.09 8.233-3.084l-3.496-4.37c-1.65 1.32-3.28 1.858-4.733 1.857-1.442 0-2.928-.533-4.322-1.745zm1.989 1.977 1.959-2.252-4.224-3.673-1.958 2.253zm-3.838-10.24v2.799h5.597v-2.798zm5.597-2.797h-2.798v5.596h2.798zm2.799 5.596v-2.798h-5.597v2.798zm-5.597 2.798h2.798v-5.596h-2.798zm14.365-2.798v-2.798h-5.597v2.798zm0-2.799h-2.798v5.597h2.798zm-2.798 0v2.799h5.596v-2.798zm0 2.8h2.798v-5.597h-2.798z",fill:"#00B682",className:"chatbotpath",mask:"url(#a)"}),(0,r.createElement)("path",{d:"M19 13.673V2.48",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"2.798"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.142 25.239v-5.783H.157v5.783zm31.716-5.783v5.783h2.985v-5.783z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("circle",{cx:19,cy:"3.411",r:"2.798",fill:"#00B682",className:"chatbotpath"})),l=()=>(0,r.createElement)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#a)"},(0,r.createElement)("path",{d:"M9.666 11.66H8.11v-1.556h1.556zm6.225 0h-1.556v-1.556h1.556zm6.224-1.556v3.112H20.56v3.89q0 .488-.182.912-.183.426-.499.742a2.34 2.34 0 0 1-1.653.68h-3.21l-5.349 4.572V19.44h-3.89a2.34 2.34 0 0 1-1.653-.68 2.3 2.3 0 0 1-.499-.743 2.3 2.3 0 0 1-.182-.912v-3.89H1.886v-3.112h1.556V7.77q0-.486.182-.912.183-.425.499-.742a2.34 2.34 0 0 1 1.653-.68h5.446V2.895a1.5 1.5 0 0 1-.559-.572 1.66 1.66 0 0 1-.219-.778q0-.327.122-.607a1.6 1.6 0 0 1 .328-.487q.207-.207.499-.34.291-.134.607-.122.33 0 .608.122.28.12.487.328.205.207.34.499t.122.607a1.55 1.55 0 0 1-.779 1.35v2.54h5.447q.486 0 .912.183.424.183.741.498a2.34 2.34 0 0 1 .681 1.654v2.334zM19.003 7.77a.75.75 0 0 0-.231-.547.75.75 0 0 0-.547-.231H5.776a.75.75 0 0 0-.547.23.75.75 0 0 0-.231.548v9.337q0 .316.23.547.232.23.548.23h5.446v2.748l3.21-2.748h3.793q.316 0 .547-.23a.75.75 0 0 0 .23-.547zM8.657 13.386q.67.67 1.532 1.022.864.352 1.811.364.949 0 1.812-.352a4.5 4.5 0 0 0 1.532-1.034l1.094 1.107A6.22 6.22 0 0 1 12 16.328a6.2 6.2 0 0 1-2.395-.474 6.4 6.4 0 0 1-2.042-1.361z",fill:"#00B682",className:"chatbotpath"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"a"},(0,r.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"}))));var c=n(57);const u=()=>(0,r.createElement)("svg",{width:8,height:12,viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"M5.1 6 1.2 2.1a.95.95 0 0 1-.275-.7q0-.426.275-.7a.95.95 0 0 1 .7-.275q.424 0 .7.275l4.6 4.6q.15.15.212.325.063.174.063.375 0 .2-.063.375A.9.9 0 0 1 7.2 6.7l-4.6 4.6a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275.95.95 0 0 1-.275-.7q0-.426.275-.7z",fill:"#1C1B1F"})),d=()=>(0,r.createElement)("svg",{width:18,height:16,viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"m16.865 8.925-15.4 6.5a.99.99 0 0 1-.95-.087q-.45-.288-.45-.838v-13q0-.55.45-.837a.99.99 0 0 1 .95-.088l15.4 6.5q.624.276.625.925 0 .65-.625.925M2.064 13l11.85-5-11.85-5v3.5l6 1.5-6 1.5z",fill:"#fff"}));var h=n(849);const p=({timestamp:e,timezone:t})=>{const[n,o]=(0,r.useState)(""),s=(0,r.useCallback)((e=>{if(!e)return(0,a.__)("Just now","betterdocs-ai-chatbot");let n;try{if(n="string"==typeof e&&e.includes(" ")?new Date(e.replace(" ","T")+"Z"):e instanceof Date?e:new Date(e),isNaN(n?.getTime()))return(0,a.__)("Just now","betterdocs-ai-chatbot");const r=new Intl.DateTimeFormat("en-US",{timeZone:t||"UTC",hour:"numeric",minute:"numeric",hour12:!0}),o=new Date,s=new Date(o.toLocaleString("en-US",{timeZone:t||"UTC"})),i=new Date(n.toLocaleString("en-US",{timeZone:t||"UTC"})),l=(s.getTime()-i.getTime())/1e3,c=r.format(n);if(l<60)return(0,a.__)("Just now","betterdocs-ai-chatbot");if(l<3600){const e=Math.floor(l/60);return(0,a.sprintf)((0,a._n)("%d minute ago","%d minutes ago",e,"betterdocs-ai-chatbot"),e)}if(l<86400){const e=Math.floor(l/3600);return(0,a.sprintf)((0,a._n)("%d hour ago","%d hours ago",e,"betterdocs-ai-chatbot"),e)}const u=new Date(s);if(u.setDate(u.getDate()-1),i.getDate()===u.getDate()&&i.getMonth()===u.getMonth()&&i.getFullYear()===u.getFullYear())return(0,a.sprintf)((0,a.__)("Yesterday (%s)","betterdocs-ai-chatbot"),c);const d=new Intl.DateTimeFormat("en-US",{timeZone:t||"UTC",day:"2-digit",month:"short",year:"numeric"});return(0,a.sprintf)("%s (%s)",d.format(n),c)}catch(e){return console.error("Error calculating time ago:",e),(0,a.__)("Just now","betterdocs-ai-chatbot")}}),[t]);return(0,r.useEffect)((()=>{o(s(e));const t=setInterval((()=>{o(s(e))}),6e4);return()=>clearInterval(t)}),[e,s]),(0,r.createElement)("span",{className:"text-gray-500"},n)},m=()=>(0,r.createElement)("div",{className:"generic-loader"},(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:40,height:40,preserveAspectRatio:"xMidYMid",style:{background:"0 0",shapeRendering:"auto"},viewBox:"0 0 100 100"},(0,r.createElement)("circle",{cx:50,cy:50,r:26,fill:"none",stroke:"#16ca9e",strokeDasharray:"122.52211349000194 42.840704496667314",strokeWidth:10},(0,r.createElement)("animateTransform",{attributeName:"transform",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"}))));var f=n(133);n(642);const g=()=>{const[e,t]=(0,r.useState)(null),[n,o]=(0,r.useState)(""),[g,w]=(0,r.useState)([]),[b,v]=(0,r.useState)(!1),[y,k]=(0,r.useState)(!1),[E,x]=(0,r.useState)(""),[C,_]=(0,r.useState)(!0),[T,A]=(0,r.useState)(""),[S,N]=(0,r.useState)((0,c.Ri)("userEmail")||""),[L]=(0,r.useState)(betterdocsAIChatbot?.welcome_message),[O,R]=(0,r.useState)(null),[M,P]=(0,r.useState)(!1),[I,D]=(0,r.useState)(!1),[z,$]=(0,r.useState)(null),[B,H]=(0,r.useState)(!1),[q,F]=(0,r.useState)(!1),j=(0,r.useRef)(null),U=(0,r.useRef)(null),[V,Z]=(0,r.useState)(null),[G,W]=(0,r.useState)({city:"",country:""}),[Y,K]=(0,r.useState)("offline"),Q=(new Date).toISOString().slice(0,19).replace("T"," "),X=()=>(new Date).toISOString().slice(0,19).replace("T"," ");(0,r.useEffect)((()=>{const e=(0,c.Bk)();t(e)}),[]),(0,r.useEffect)((()=>{localStorage.setItem("userStatus","online");const e=()=>{localStorage.setItem("userStatus","online")};return window.addEventListener("mousemove",e),window.addEventListener("keydown",e),()=>{window.removeEventListener("mousemove",e),window.removeEventListener("keydown",e),localStorage.removeItem("userStatus")}}),[]),(0,r.useEffect)((()=>{(async()=>{try{const e=await fetch("https://ipinfo.io/json");if(!e.ok)throw new Error("Network response was not ok");const t=await(e?.json());W(t)}catch(e){Z(e.message)}})()}),[]);const J=async()=>{if(""===n.trim())return;const t=n?.trim(),r=X();if(w((e=>[...e,{text:t,type:navigator?.onLine?"sent":"failed",timestamp:r}])),o(""),x(""),j.current&&setTimeout((()=>{j.current.scrollTop=j.current.scrollHeight}),50),navigator.onLine){k(!0);try{const n=(betterdocsAIChatbot?.locale||"en_US").split("_")[0],r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/query-post`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t,email:S,session_id:e,lang:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"API request failed")}const o=await r.json();if(!o?.success)throw new Error(o.message||"Request failed");{const e=o?.data?.conversation||[],t=e[e.length-1],n=t?.text||"No response content available";v(!0),ee(n)}}catch(e){k(!1),w((t=>[...t,{text:e.message,type:"received",timestamp:X()}]))}}},ee=e=>{let t=0,n="";x("");const r=X();k(!0),R(r);const o=()=>{if(t<e?.length)n+=e?.charAt(t),x(n),t++,setTimeout(o,10);else{const e=(0,c.Kk)(n).__html;x(e),k(!1),w((t=>[...t,{text:e,type:"received",timestamp:r}]))}};o()},te=e=>{const t=new Set;let n=null;return e.map((e=>{if(!e.timestamp){const t=X();return{...e,timestamp:t}}if(t.has(e.timestamp)||e.timestamp===n){let r;r=e.timestamp.includes("T")&&e.timestamp.includes("Z")?new Date(e.timestamp):new Date(e.timestamp.replace(" ","T")+"Z"),r.setMilliseconds(r.getMilliseconds()+1);const o=r.toISOString().slice(0,19).replace("T"," ");return n=o,t.add(o),{...e,timestamp:o}}return n=e.timestamp,t.add(e.timestamp),e}))},ne=async()=>{if(""===T?.trim()||!ae(T?.trim()))return D(!0),void setTimeout((()=>D(!1)),3e3);(0,c.TV)("userEmail",T,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),(0,c.TV)("showEmailField","false",{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),N(T),P(!0),setTimeout((()=>P(!1)),3e3),_(!1)};(0,r.useEffect)((()=>{S&&(async()=>{if(S&&e){F(!0);try{const t=await(0,h.GI)(S,e);t?.conversation&&(w(t.conversation),(0,h.$H)(S,e))}catch(e){w([{text:L,type:"received",timestamp:X()}])}finally{F(!1)}}})()}),[S,e,L]),(0,r.useEffect)((()=>{const e=X(),t=S?(0,c.Ri)(S):null;if(S&&t){const e=JSON.parse(t);w(te(e?.conversation||[]))}else S||w([{text:L,type:"received",timestamp:e}])}),[S]);const re=(0,r.useRef)(0),oe=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{oe.current&&clearTimeout(oe.current)}),[]),(0,r.useEffect)((()=>{S&&g&&0!==g.length&&e&&(oe.current&&clearTimeout(oe.current),oe.current=setTimeout((()=>{const t=Date.now();if(t-re.current<2e3)return;re.current=t;const n=te(g),r={email:S,main_information:{location:`${G?.city}, ${G?.country}`,country:G?.country,time:X(),timezone:G?.timezone,language:navigator?.language},device_information:{IP_address:G?.ip,browser:(0,c.VK)()},isBlocked:!1,conversation:n.map((e=>({text:e?.text,type:e?.type,timestamp:e?.timestamp||X()})))};(0,h.Tt)(S,r,e).then((t=>{t?.success&&(0,h.$H)(S,e)})).catch((e=>{console.error("Error saving conversation:",e)}))}),500))}),[G,g,S,e]),(0,r.useEffect)((()=>{if(U?.current){const e=()=>H(!0),t=()=>H(!1),n=U?.current;return n.addEventListener("focus",e),n.addEventListener("blur",t),()=>{n.removeEventListener("focus",e),n.removeEventListener("blur",t)}}}),[]),(0,r.useEffect)((()=>{j.current&&(j.current.scrollTop=j?.current?.scrollHeight)}),[g,y,E,I,M]);const ae=e=>e?!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||($("Please enter a valid email"),!1):($("Email is required"),!1);return(0,r.createElement)("div",{className:"chat-container"},(0,r.createElement)("div",{className:"betterdocs-chatbot-header betterdocs-ia-common-header"},(0,r.createElement)("h2",null,(0,a.__)("Chatbot","betterdocs-ai-chatbot"))),(0,r.createElement)("div",{className:"chat-content-wrapper"},(0,r.createElement)("div",{className:"chat-body",ref:j},(0,r.createElement)("div",{className:"top-content"},(0,r.createElement)("div",{className:"chat-icon"},(0,r.createElement)(i,null)),betterdocsAIChatbot?.title&&(0,r.createElement)("h3",{className:"heading-title"},betterdocsAIChatbot.title),betterdocsAIChatbot?.subtitle&&(0,r.createElement)("p",{className:"chat-description"},betterdocsAIChatbot.subtitle)),q&&(0,r.createElement)(m,null),!q&&g&&g?.map(((e,t)=>(0,r.createElement)("div",{key:t,className:`message ${e?.type}`},"sent"===e?.type||"failed"===e?.type?(0,r.createElement)("div",{className:"query"},e?.text,"failed"==e?.type&&(0,r.createElement)("span",{className:"status"},(0,a.__)("Sending Failed","betterdocs-ai-chatbot"))):"received"===e?.type?(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"message-text"},(0,r.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:s.A.sanitize((0,f.xI)(e?.text))}}),(0,r.createElement)("span",{className:"message-received-time"},e.timestamp&&(0,r.createElement)(p,{timestamp:e?.timestamp,timezone:G?.timezone,isNewMesage:b,setNewMessage:v})))):(0,r.createElement)(r.Fragment,null)))),y&&E&&(0,r.createElement)("div",{className:"message typing received message-active"},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"message-text"},(0,r.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:s.A?.sanitize(E)}}),(0,r.createElement)("span",{className:"message-received-time"},(0,r.createElement)(p,{timestamp:O||Q,timezone:G?.timezone,isNewMesage:b,setNewMessage:v}))))),y&&!E&&(0,r.createElement)("div",{className:"message typing received message-active"},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"text thinking-dots"},(0,a.__)("Thinking","betterdocs-ai-chatbot"),(0,r.createElement)("span",{className:"dots"})))),C&&"false"!==(0,c.Ri)("showEmailField")&&(0,r.createElement)("div",{className:"message received email-field-wrapper "+(B?"focused":"")},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"text"},(0,a.__)("Enter email to keep conversation alive.","betterdocs-ai-chatbot"))),(0,r.createElement)("div",{className:"email-field-container"},(0,r.createElement)("div",{className:"email-field"},(0,r.createElement)("input",{ref:U,type:"email",id:"email",value:T,onChange:e=>{A(e?.target?.value)},onKeyDown:e=>{"Enter"===e?.key&&ne()},placeholder:(0,a.__)("Enter your email","betterdocs-ai-chatbot"),required:!0}),(0,r.createElement)("div",{className:"email-icon",onClick:ne},(0,r.createElement)(u,null))),I&&(0,r.createElement)("div",{className:"error-message-container"},z)))),M&&(0,r.createElement)("div",{className:"thankyou-message-container"},(0,a.__)("Thanks! We should reply in a moment.","betterdocs-ai-chatbot")),(0,r.createElement)("div",{className:"chat-footer"},(0,r.createElement)("div",{className:"message-input"+(C&&"false"!==(0,c.Ri)("showEmailField")?" disabled":"")},(0,r.createElement)("input",{placeholder:(0,a.__)("Type a message...","betterdocs-ai-chatbot"),value:n,onChange:e=>{const t=e.target.value,n=c.o2.sanitize("text",t);o(n)},onKeyDown:e=>{y||"Enter"!==e?.key||e?.shiftKey||(e.preventDefault(),J())},rows:"2"}),(0,r.createElement)("button",{onClick:J,style:y?{pointerEvents:"none",opacity:".6"}:{},disabled:y},(0,r.createElement)(d,null))))))},w=()=>(0,r.createElement)("svg",{width:22,height:24,viewBox:"0 0 22 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"a",fill:"#fff"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"})),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("path",{d:"m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",mask:"url(#a)"}),(0,r.createElement)("path",{d:"M11 7V1",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"1.5"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("circle",{cx:11,cy:"1.5",r:"1.5",fill:"#00B682",className:"chatbotpath"})),b=()=>(0,r.createElement)("svg",{width:21,height:24,viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",stroke:"#344054",strokeWidth:"1.5"}),(0,r.createElement)("path",{d:"M6.2 14c2.3 2 5.3 2 7.8 0",stroke:"#344054",strokeWidth:"1.6"}),(0,r.createElement)("path",{d:"M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",stroke:"#344054",strokeWidth:"1.5"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#344054"}),(0,r.createElement)("circle",{cx:"10.1",cy:"1.5",r:"1.5",fill:"#344054"}));(0,o.addFilter)("tab_chatbot_preview","betterdocs/chattab-preview",((e,t)=>(e.push({id:"chatbot",class:"betterdocs-ia-chatbot",type:"tab",title:(0,a.__)("Chatbot","betterdocs-ai-chatbot"),default:!1,icon:betterdocsAIChatbot?.ai_chatbot_icon?.url?(0,r.createElement)("img",{src:betterdocsAIChatbot.ai_chatbot_icon.url,width:"24",height:"24",style:{opacity:"chatbot"===t?1:.7,filter:"chatbot"===t?"brightness(1.1)":"none"}}):"chatbot"===t?(0,r.createElement)(w,null):(0,r.createElement)(b,null),component:g,showTab:!0,showTabInComponent:!0}),e)))},794:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){void 0===e&&(e={});var n={},c=Boolean(e.type&&i[e.type]);for(var u in e){var d=e[u];if((0,r.isCustomAttribute)(u))n[u]=d;else{var h=u.toLowerCase(),p=l(h);if(p){var m=(0,r.getPropertyInfo)(p);switch(a.includes(p)&&s.includes(t)&&!c&&(p=l("default"+h)),n[p]=d,m&&m.type){case r.BOOLEAN:n[p]=!0;break;case r.OVERLOADED_BOOLEAN:""===d&&(n[p]=!0)}}else o.PRESERVE_CUSTOM_ATTRIBUTES&&(n[u]=d)}}return(0,o.setStyleProp)(e.style,n),n};var r=n(913),o=n(824),a=["checked","value"],s=["input","select","textarea"],i={reset:!0,submit:!0};function l(e){return r.possibleStandardNames[e]}},824:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.returnFirstArg=t.canTextBeChildOfNode=t.ELEMENTS_WITH_NO_TEXT_CHILDREN=t.PRESERVE_CUSTOM_ATTRIBUTES=void 0,t.isCustomComponent=function(e,t){return e.includes("-")?!s.has(e):Boolean(t&&"string"==typeof t.is)},t.setStyleProp=function(e,t){if("string"==typeof e)if(e.trim())try{t.style=(0,a.default)(e,i)}catch(e){t.style={}}else t.style={}};var o=n(609),a=r(n(403)),s=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]),i={reactCompat:!0};t.PRESERVE_CUSTOM_ATTRIBUTES=Number(o.version.split(".")[0])>=16,t.ELEMENTS_WITH_NO_TEXT_CHILDREN=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]),t.canTextBeChildOfNode=function(e){return!t.ELEMENTS_WITH_NO_TEXT_CHILDREN.has(e.name)},t.returnFirstArg=function(e){return e}},849:(e,t,n)=>{"use strict";n.d(t,{$H:()=>s,GI:()=>o,Tt:()=>i,_5:()=>a,eB:()=>l});var r=n(57);const o=async(e,t,n)=>{try{const r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/get-current-user-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e,filter_term:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"Failed to fetch conversation")}return r.json()}catch(e){throw console.error("Error fetching conversation:",e),e}},a=async(e="",t=0,n=10)=>{const r={filter_term:e,offset:t,limit:n},o=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/get-chats`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":betterdocsAIChatbot.nonce},credentials:"include",body:JSON.stringify(r)});if(!o.ok){const e=await o.json();throw new Error(e.message||"Failed to fetch chats")}return o.json()},s=async(e,t)=>{try{const n=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/update-online-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e})});if(!n.ok){const e=await n.json();throw new Error(e.message||"Online status update failed")}return n.json()}catch(e){throw console.error("Error updating online status:",e),e}},i=async(e,t,n)=>{try{const r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/save-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,conversation:t,session_id:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"Failed to save conversation")}return r.json()}catch(e){throw console.error("Error saving conversation:",e),e}},l=async(e,t,n)=>{const o={session_id:(0,r.Ri)("chatbot_session_id")||n,user_email:e,passed_time:t},a=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/get-online-status`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":betterdocsAIChatbot.nonce},credentials:"include",body:JSON.stringify(o)});if(!a.ok){const e=await a.json();throw new Error(e.message||"Failed to get online status")}return a.json()}},884:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.htmlToDOM=t.domToReact=t.attributesToProps=t.Text=t.ProcessingInstruction=t.Element=t.Comment=void 0,t.default=function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");return e?(0,s.default)((0,o.default)(e,(null==t?void 0:t.htmlparser2)||l),t):[]};var o=r(n(414));t.htmlToDOM=o.default;var a=r(n(794));t.attributesToProps=a.default;var s=r(n(926));t.domToReact=s.default;var i=n(95);Object.defineProperty(t,"Comment",{enumerable:!0,get:function(){return i.Comment}}),Object.defineProperty(t,"Element",{enumerable:!0,get:function(){return i.Element}}),Object.defineProperty(t,"ProcessingInstruction",{enumerable:!0,get:function(){return i.ProcessingInstruction}}),Object.defineProperty(t,"Text",{enumerable:!0,get:function(){return i.Text}});var l={lowerCaseAttributeNames:!1}},913:(e,t,n)=>{"use strict";function r(e,t,n,r,o,a,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=s}const o={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach((e=>{o[e]=new r(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((([e,t])=>{o[e]=new r(e,1,!1,t,null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((e=>{o[e]=new r(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((e=>{o[e]=new r(e,2,!1,e,null,!1,!1)})),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach((e=>{o[e]=new r(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((e=>{o[e]=new r(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((e=>{o[e]=new r(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((e=>{o[e]=new r(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((e=>{o[e]=new r(e,5,!1,e.toLowerCase(),null,!1,!1)}));const a=/[\-\:]([a-z])/g,s=e=>e[1].toUpperCase();["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach((e=>{const t=e.replace(a,s);o[t]=new r(t,1,!1,e,null,!1,!1)})),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach((e=>{const t=e.replace(a,s);o[t]=new r(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((e=>{const t=e.replace(a,s);o[t]=new r(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((e=>{o[e]=new r(e,1,!1,e.toLowerCase(),null,!1,!1)})),o.xlinkHref=new r("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((e=>{o[e]=new r(e,1,!1,e.toLowerCase(),null,!0,!0)}));const{CAMELCASE:i,SAME:l,possibleStandardNames:c}=n(614),u=RegExp.prototype.test.bind(new RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),d=Object.keys(c).reduce(((e,t)=>{const n=c[t];return n===l?e[t]=t:n===i?e[t.toLowerCase()]=t:e[t]=n,e}),{});t.BOOLEAN=3,t.BOOLEANISH_STRING=2,t.NUMERIC=5,t.OVERLOADED_BOOLEAN=4,t.POSITIVE_NUMERIC=6,t.RESERVED=0,t.STRING=1,t.getPropertyInfo=function(e){return o.hasOwnProperty(e)?o[e]:null},t.isCustomAttribute=u,t.possibleStandardNames=d},926:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,n){void 0===n&&(n={});for(var r=[],o="function"==typeof n.replace,c=n.transform||s.returnFirstArg,u=n.library||i,d=u.cloneElement,h=u.createElement,p=u.isValidElement,m=t.length,f=0;f<m;f++){var g=t[f];if(o){var w=n.replace(g,f);if(p(w)){m>1&&(w=d(w,{key:w.key||f})),r.push(c(w,g,f));continue}}if("text"!==g.type){var b=g,v={};l(b)?((0,s.setStyleProp)(b.attribs.style,b.attribs),v=b.attribs):b.attribs&&(v=(0,a.default)(b.attribs,b.name));var y=void 0;switch(g.type){case"script":case"style":g.children[0]&&(v.dangerouslySetInnerHTML={__html:g.children[0].data});break;case"tag":"textarea"===g.name&&g.children[0]?v.defaultValue=g.children[0].data:g.children&&g.children.length&&(y=e(g.children,n));break;default:continue}m>1&&(v.key=f),r.push(c(h(g.name,v,y),g,f))}else{var k=!g.data.trim().length;if(k&&g.parent&&!(0,s.canTextBeChildOfNode)(g.parent))continue;if(n.trim&&k)continue;r.push(c(g.data,g,f))}}return 1===r.length?r[0]:r};var o=n(609),a=r(n(794)),s=n(824),i={cloneElement:o.cloneElement,createElement:o.createElement,isValidElement:o.isValidElement};function l(e){return s.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===e.type&&(0,s.isCustomComponent)(e.name,e.attribs)}},994:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0,function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(n=t.ElementType||(t.ElementType={})),t.isTag=function(e){return e.type===n.Tag||e.type===n.Script||e.type===n.Style},t.Root=n.Root,t.Text=n.Text,t.Directive=n.Directive,t.Comment=n.Comment,t.Script=n.Script,t.Style=n.Style,t.Tag=n.Tag,t.CDATA=n.CDATA,t.Doctype=n.Doctype}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(609),t=n.n(e),r=n(723),o=n(619);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var s={ac:!0,ad:!0,ae:!0,af:!0,afar:!0,ag:!0,ai:!0,al:!0,am:!0,ao:!0,aq:!0,ar:!0,as:!0,at:!0,au:!0,"au-aboriginal":!0,"au-act":!0,"au-nsw":!0,"au-nt":!0,"au-qld":!0,"au-tas":!0,"au-vic":!0,"au-wa":!0,aw:!0,ax:!0,az:!0,ba:!0,bb:!0,bd:!0,be:!0,bf:!0,bg:!0,bh:!0,bi:!0,bj:!0,bl:!0,bm:!0,bn:!0,bo:!0,"bq-bo":!0,"bq-sa":!0,"bq-se":!0,bq:!0,br:!0,bs:!0,bt:!0,bv:!0,bw:!0,by:!0,bz:!0,"ca-bc":!0,ca:!0,cc:!0,cd:!0,cf:!0,cg:!0,"ch-gr":!0,ch:!0,ci:!0,ck:!0,cl:!0,cm:!0,"cn-xj":!0,cn:!0,co:!0,cp:!0,cq:!0,cr:!0,cu:!0,cv:!0,cw:!0,cx:!0,cy:!0,cz:!0,de:!0,dg:!0,dj:!0,dk:!0,dm:!0,do:!0,dz:!0,ea:!0,earth:!0,east_african_federation:!0,easter_island:!0,"ec-w":!0,ec:!0,ee:!0,eg:!0,eh:!0,er:!0,"es-ar":!0,"es-ce":!0,"es-cn":!0,"es-ct":!0,"es-ga":!0,"es-ib":!0,"es-ml":!0,"es-pv":!0,"es-variant":!0,es:!0,"et-or":!0,"et-ti":!0,et:!0,eu:!0,european_union:!0,ewe:!0,fi:!0,fj:!0,fk:!0,fm:!0,fo:!0,"fr-20r":!0,"fr-bre":!0,"fr-cp":!0,fr:!0,fx:!0,ga:!0,"gb-con":!0,"gb-eng":!0,"gb-nir":!0,"gb-ork":!0,"gb-sct":!0,"gb-wls":!0,gb:!0,gd:!0,"ge-ab":!0,ge:!0,gf:!0,gg:!0,gh:!0,gi:!0,gl:!0,gm:!0,gn:!0,gp:!0,gq:!0,gr:!0,gs:!0,gt:!0,gu:!0,guarani:!0,gw:!0,gy:!0,hausa:!0,hk:!0,hm:!0,hmong:!0,hn:!0,hr:!0,ht:!0,hu:!0,ic:!0,id:!0,"id-jb":!0,"id-jt":!0,ie:!0,il:!0,im:!0,"in-as":!0,"in-gj":!0,"in-ka":!0,"in-or":!0,"in-tn":!0,in:!0,io:!0,iq:!0,ir:!0,is:!0,"it-23":!0,"it-82":!0,"it-88":!0,it:!0,je:!0,jm:!0,jo:!0,jp:!0,kanuri:!0,ke:!0,kg:!0,kh:!0,ki:!0,kikuyu:!0,km:!0,kn:!0,kongo:!0,kp:!0,kr:!0,kurdistan:!0,kw:!0,ky:!0,kz:!0,la:!0,lb:!0,lc:!0,li:!0,lk:!0,lr:!0,ls:!0,lt:!0,lu:!0,lv:!0,ly:!0,ma:!0,malayali:!0,manipur:!0,maori:!0,mc:!0,md:!0,me:!0,mf:!0,mg:!0,mh:!0,mizoram:!0,mk:!0,ml:!0,mm:!0,mn:!0,mo:!0,mp:!0,mq:!0,mr:!0,ms:!0,mt:!0,mu:!0,mv:!0,mw:!0,mx:!0,my:!0,mz:!0,na:!0,nato:!0,nc:!0,ne:!0,nf:!0,ng:!0,ni:!0,"nl-fr":!0,nl:!0,no:!0,northern_cyprus:!0,np:!0,nr:!0,nu:!0,nz:!0,occitania:!0,olympics:!0,om:!0,otomi:!0,pa:!0,pe:!0,pf:!0,pg:!0,ph:!0,"pk-jk":!0,"pk-sd":!0,pk:!0,pl:!0,pm:!0,pn:!0,pr:!0,ps:!0,"pt-20":!0,"pt-30":!0,pt:!0,pw:!0,py:!0,qa:!0,quechua:!0,re:!0,ro:!0,rs:!0,ru:!0,"ru-ba":!0,"ru-ce":!0,"ru-cu":!0,"ru-da":!0,"ru-ko":!0,"ru-ta":!0,"ru-ud":!0,rw:!0,sa:!0,sami:!0,sb:!0,sc:!0,sd:!0,se:!0,sg:!0,"sh-ac":!0,"sh-hl":!0,"sh-ta":!0,sh:!0,si:!0,sj:!0,sk:!0,sl:!0,sm:!0,sn:!0,so:!0,somaliland:!0,south_ossetia:!0,soviet_union:!0,sr:!0,ss:!0,st:!0,su:!0,sv:!0,sx:!0,sy:!0,sz:!0,ta:!0,tc:!0,td:!0,tf:!0,tg:!0,th:!0,tibet:!0,tj:!0,tk:!0,tl:!0,tm:!0,tn:!0,to:!0,torres_strait_islands:!0,tr:!0,transnistria:!0,tt:!0,tv:!0,tw:!0,tz:!0,ua:!0,ug:!0,uk:!0,um:!0,un:!0,united_nations:!0,"us-hi":!0,us:!0,uy:!0,uz:!0,va:!0,vc:!0,ve:!0,vg:!0,vi:!0,vn:!0,vu:!0,wf:!0,wiphala:!0,ws:!0,xk:!0,xx:!0,ye:!0,yorubaland:!0,yt:!0,za:!0,zm:!0,zw:!0},i=["countryCode","cdnUrl"],l=function(e){var n,r=e.countryCode,o=e.cdnUrl,l=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)t.indexOf(n=a[r])>=0||(o[n]=e[n]);return o}(e,i);return t().createElement("img",a({"data-testid":"circle-country-flag"},function(e,t,n,r){return void 0===r&&(r=""),a({},n,{title:n.title||e,height:n.height||100,src:""+(t||"https://hatscripts.github.io/circle-flags/flags/")+r+e+".svg"})}((n=r,s[n]?n:"xx").toLowerCase(),o,l)))},c=n(849);const u=({timestamp:t,timezone:n,showTime:o})=>{const[a,s]=(0,e.useState)(""),i=(e=>{try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(new Date),!0}catch{return!1}})(n)?n:"UTC";let l;try{if(l="string"==typeof t&&t.includes(" ")?new Date(t.replace(" ","T")+"Z"):t instanceof Date?t:new Date(t),isNaN(l.getTime()))throw new Error("Invalid timestamp format")}catch(e){console.error("Error parsing timestamp:",e),l=new Date}const c=new Intl.DateTimeFormat("en-US",{timeZone:i,hour:"numeric",minute:"numeric",hour12:!0}),u=(0,e.useCallback)((()=>{const e=new Date,t=new Date(e.toLocaleString("en-US",{timeZone:i})),n=new Date(l.toLocaleString("en-US",{timeZone:i})),a=(t.getTime()-n.getTime())/1e3,s=c.format(l);if(a<60)return(0,r.__)("Just now","betterdocs-ai-chatbot");if(a<3600){const e=Math.floor(a/60);return(0,r.sprintf)((0,r._n)("%d minute ago","%d minutes ago",e,"betterdocs-ai-chatbot"),e)}if(a<86400){const e=Math.floor(a/3600);return(0,r.sprintf)((0,r._n)("%d hour ago","%d hours ago",e,"betterdocs-ai-chatbot"),e)}const u=new Date(t);if(u.setDate(u.getDate()-1),n.getDate()===u.getDate()&&n.getMonth()===u.getMonth()&&n.getFullYear()===u.getFullYear()){let e=(0,r.__)("Yesterday","betterdocs-ai-chatbot");return o&&(e+=` (${s})`),e}let d=new Intl.DateTimeFormat("en-US",{timeZone:i,day:"2-digit",month:"short",year:"numeric"}).format(l);return o&&(d+=` (${s})`),d}),[l,i,o,c]);return(0,e.useEffect)((()=>{s(u());const e=setInterval((()=>{s(u())}),6e4);return()=>clearInterval(e)}),[u]),(0,e.createElement)("span",{className:"text-gray-500"},a)},d=({currentConversation:t})=>{const[n,o]=(0,e.useState)(!1);return(0,e.createElement)("span",{onClick:e=>{e?.stopPropagation(),navigator?.clipboard?.writeText(t),o(!0),setTimeout((()=>o(!1)),2e3)}},t,(0,e.createElement)("span",{className:"copy-icon"},(0,e.createElement)("svg",{className:"btd-icon btd-duplicate",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"mask0",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"17",height:"16"},(0,e.createElement)("rect",{x:"0.753906",width:"16",height:"16",fill:"#D9D9D9"})),(0,e.createElement)("g",{mask:"url(#mask0)"},(0,e.createElement)("path",{d:"M6.75391 12.0026C6.38724 12.0026 6.07335 11.872 5.81224 11.6109C5.55113 11.3498 5.42057 11.0359 5.42057 10.6693V2.66927C5.42057 2.3026 5.55113 1.98872 5.81224 1.7276C6.07335 1.46649 6.38724 1.33594 6.75391 1.33594H12.7539C13.1206 1.33594 13.4345 1.46649 13.6956 1.7276C13.9567 1.98872 14.0872 2.3026 14.0872 2.66927V10.6693C14.0872 11.0359 13.9567 11.3498 13.6956 11.6109C13.4345 11.872 13.1206 12.0026 12.7539 12.0026H6.75391ZM6.75391 10.6693H12.7539V2.66927H6.75391V10.6693ZM4.08724 14.6693C3.72057 14.6693 3.40668 14.5387 3.14557 14.2776C2.88446 14.0165 2.75391 13.7026 2.75391 13.3359V4.0026H4.08724V13.3359H11.4206V14.6693H4.08724Z",fill:"#475467"}))),n&&(0,e.createElement)("span",{className:"tooltip visible"},(0,r.__)("Copied","betterdocs-ai-chatbot"))))},h=({currentConversation:t,onlineStatus:n,activeProfileInfo:o,setActiveProfileInfo:a,setActiveChatbox:s})=>(0,e.createElement)("div",{className:"betterdocs-col_264"+(o?"":" betterdocs-cal_unactive-phone")},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_profile"},(0,e.createElement)("ul",null,(0,e.createElement)("li",{className:"betterdocs-author_info-list_item"},(0,e.createElement)("div",{className:"back-btn active-back_btn",onClick:()=>(a(!1),void s(!0))},(0,e.createElement)("svg",{width:"20",height:"20",viewBox:"0 0 1024 1024",class:"icon",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64"}),(0,e.createElement)("path",{d:"m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312z"}))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_image"},(0,e.createElement)("div",{className:"author-condition"+(n?" active":"")}),(0,e.createElement)("div",{className:"author-image"},(0,e.createElement)("img",{src:t?.profile_info&&t?.profile_info?.entry?.[0]?.thumbnailUrl||""})),(0,e.createElement)("div",{className:"author-cuntry"},(0,e.createElement)(l,{countryCode:t?.main_information?.country?.toLowerCase(),width:"12",height:"12"}))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_info"},(0,e.createElement)("div",null,(0,e.createElement)("h4",{className:"betterdocs-ai_chatbot-author_name"},t?.profile_info&&t?.profile_info?.entry?.[0]?.displayName),(0,e.createElement)("span",{className:"betterdocs-ai_chatbot-author_msg"},(0,e.createElement)(d,{currentConversation:t?.user_email}))))),(0,e.createElement)("li",{className:"betterdocs-author_info-list_item"},(0,e.createElement)("ul",{className:"other-info"},(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("h4",{className:"other-header"},(0,r.__)("Main Information","betterdocs-ai-chatbot"))),t?.main_information?.country&&(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("svg",{width:"12",height:"15",viewBox:"0 0 12 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M5.98185 14.6437L1.79348 10.4554C-0.519701 8.14218 -0.519701 4.39181 1.79348 2.07863C4.10666 -0.234544 7.85703 -0.234544 10.1703 2.07863C12.4834 4.39181 12.4834 8.14218 10.1703 10.4554L5.98185 14.6437ZM9.23945 9.5246C11.0386 7.72551 11.0386 4.80852 9.23945 3.00938C7.44036 1.21024 4.52336 1.21024 2.72423 3.00938C0.925088 4.80852 0.925088 7.72551 2.72423 9.5246L5.98185 12.7823L9.23945 9.5246ZM5.98185 7.58329C5.25487 7.58329 4.66557 6.99399 4.66557 6.26701C4.66557 5.54005 5.25487 4.95073 5.98185 4.95073C6.70884 4.95073 7.29813 5.54005 7.29813 6.26701C7.29813 6.99399 6.70884 7.58329 5.98185 7.58329Z",fill:"#475467"}))),(0,e.createElement)("span",{className:"text"},t?.main_information?.location)),t?.main_information?.timezone&&(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M6.99844 13.6453C3.35336 13.6453 0.398438 10.6904 0.398438 7.04531C0.398438 3.40023 3.35336 0.445312 6.99844 0.445312C10.6435 0.445312 13.5984 3.40023 13.5984 7.04531C13.5984 10.6904 10.6435 13.6453 6.99844 13.6453ZM6.99844 12.3253C9.91452 12.3253 12.2784 9.96139 12.2784 7.04531C12.2784 4.12925 9.91452 1.76531 6.99844 1.76531C4.08237 1.76531 1.71844 4.12925 1.71844 7.04531C1.71844 9.96139 4.08237 12.3253 6.99844 12.3253ZM7.65844 7.04531H10.2984V8.36531H6.33844V3.74531H7.65844V7.04531Z",fill:"#475467"}))),(0,e.createElement)("span",{className:"text"},((e,t)=>{const n={timeZone:e,hour:"2-digit",minute:"2-digit",hour12:!0},r=new Intl.DateTimeFormat(t,n),o=new Date;return r?.format(o)})(t?.main_information?.timezone,t?.main_information?.language)," (Local)")),t?.main_information?.language&&(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("svg",{width:"15",height:"13",viewBox:"0 0 15 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M11.1834 5.67437L14.0593 12.8643H12.6508L11.8658 10.9034H9.1924L8.40869 12.8643H7.00077L9.87609 5.67437H11.1834ZM5.62749 0.445312V1.75258H9.54928V3.05984L8.26306 3.06023C7.74689 4.60644 6.93397 6.00491 5.89646 7.17837C6.36897 7.59977 6.8805 7.97358 7.42334 8.29405L6.93273 9.52131C6.23125 9.1243 5.57483 8.64996 4.9736 8.10908C3.80649 9.15953 2.43067 9.95912 0.92063 10.4285L0.570061 9.1675C1.86159 8.7597 3.04266 8.07561 4.05251 7.17909C3.31544 6.34636 2.69185 5.39974 2.20731 4.36729L3.67107 4.36734C4.03928 5.04613 4.47696 5.67652 4.97364 6.2476C5.77671 5.32472 6.42525 4.24694 6.8756 3.0605L0.398438 3.05984V1.75258H4.32023V0.445312H5.62749ZM10.5297 7.56022L9.71465 9.59615H11.3435L10.5297 7.56022Z",fill:"#475467"}))),(0,e.createElement)("span",{className:"text"},t?.main_information.language)))),(0,e.createElement)("li",{className:"betterdocs-author_info-list_item"},(0,e.createElement)("ul",{className:"other-info"},(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("h4",{className:"other-header"},(0,r.__)("Device Information","betterdocs-ai-chatbot"))),t?.device_info?.IP_address&&(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("svg",{width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M7 0C9.28445 0 11.1364 1.85192 11.1364 4.13636C11.1364 4.20344 11.1348 4.27014 11.1316 4.33643C12.5111 4.82182 13.5 6.13635 13.5 7.68182C13.5 9.63991 11.9126 11.2273 9.95455 11.2273H4.04545C2.08735 11.2273 0.5 9.63991 0.5 7.68182C0.5 6.13635 1.48886 4.82182 2.86839 4.33643C2.86523 4.27014 2.86364 4.20344 2.86364 4.13636C2.86364 1.85192 4.71555 0 7 0ZM7 1.18182C5.36825 1.18182 4.04545 2.50462 4.04545 4.13636C4.04545 4.1845 4.04659 4.23246 4.04887 4.28021L4.09073 5.15917L3.26063 5.45125C2.3226 5.78128 1.68182 6.67071 1.68182 7.68182C1.68182 8.9872 2.74005 10.0455 4.04545 10.0455H9.95455C11.2599 10.0455 12.3182 8.9872 12.3182 7.68182C12.3182 6.37591 11.2605 5.31818 9.95455 5.31818C8.91041 5.31818 8.02446 5.99519 7.71157 6.93414L6.59015 6.56033C7.05939 5.15184 8.38864 4.13636 9.95455 4.13636C9.95455 2.50462 8.63174 1.18182 7 1.18182Z",fill:"#475467"}))),(0,e.createElement)("span",{className:"text"},t?.device_info?.IP_address)),t?.device_info?.browser&&(0,e.createElement)("li",{className:"other-info_list"},(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("svg",{width:"12",height:"14",viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M3.25441 8.30078C3.25148 8.3544 3.25 8.40829 3.25 8.4625C3.25 10.0107 4.38922 11.55 6.50001 11.55C8.0424 11.55 9.44289 11.1243 10.4 10.5402V12.7182C9.23736 13.2163 7.87314 13.5 6.50001 13.5C2.92369 13.5 1.3 11.2446 1.3 8.4625C1.3 6.36215 2.62684 4.56208 4.51281 3.80592C3.60034 4.831 3.25 5.92184 3.25 6.34688L9.75001 6.35C9.75001 4.13598 8.09368 2.45 5.85001 2.45C2.6 2.45 0.649162 5.04201 0 6.34929C0.188273 3.25432 2.60594 0.5 5.85001 0.5C9.23001 0.5 11.7 3.11914 11.7 6.35V8.3H3.25L3.25441 8.30078Z",fill:"#475467"}))),(0,e.createElement)("span",{className:"text"},t?.device_info?.browser)))))));var p=n(404),m=n(133);const f=({currentConversation:t})=>{const n=(0,e.useRef)();return(0,e.useEffect)((()=>{if(n?.current){const{scrollHeight:e}=n?.current;n?.current?.scrollTo({top:e})}}),[n?.current,t]),(0,e.createElement)("div",{className:"betterdocs-chat_box"},(0,e.createElement)("ul",{className:"betterdocs-msg_list",ref:n},t?.conversation?.map((n=>{const r=n?.text;return"sent"==n?.type?(0,e.createElement)("li",{className:"betterdocs-msg_item sender"},(0,e.createElement)("div",{className:"author-image"},(0,e.createElement)("img",{src:t?.profile_info?.entry?.[0]?.thumbnailUrl||""})),(0,e.createElement)("div",{className:"sender-wrapper"},(0,e.createElement)("div",{className:"text-chatbot",dangerouslySetInnerHTML:{__html:p.A?.sanitize((0,m.xI)(r))}}),(0,e.createElement)("span",{className:"time newtime"},(0,e.createElement)(u,{timestamp:n?.timestamp,timezone:t?.main_information?.timezone,showTime:!0})))):(0,e.createElement)("li",{className:"betterdocs-msg_item reply"},(0,e.createElement)("div",{className:"reply-wrapper"},(0,e.createElement)("div",{className:"text-chatbot",dangerouslySetInnerHTML:{__html:p.A?.sanitize((0,m.xI)(r))}}),(0,e.createElement)("span",{className:"time newtime"},(0,e.createElement)(u,{timestamp:n?.timestamp,timezone:t?.main_information?.timezone,showTime:!0}))),(0,e.createElement)("div",{className:"reply-image"},(0,e.createElement)("svg",{width:"21",height:"24",viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M8.55159 19.4V18.65H7.80159H5.60156C4.63755 18.65 3.98999 18.6484 3.50695 18.5835C3.04549 18.5214 2.848 18.4142 2.71768 18.2839L2.18735 18.8142L2.71768 18.2839C2.58736 18.1536 2.48014 17.9561 2.4181 17.4946C2.35316 17.0116 2.35156 16.364 2.35156 15.4V9.5C2.35156 8.53599 2.35316 7.88843 2.4181 7.40539C2.48014 6.94393 2.58736 6.74643 2.71768 6.61612C2.848 6.4858 3.04549 6.37858 3.50695 6.31654C3.98999 6.25159 4.63755 6.25 5.60156 6.25H14.6016C15.5656 6.25 16.2132 6.25159 16.6962 6.31654C17.1577 6.37858 17.3552 6.4858 17.4855 6.61612L18.0158 6.08579L17.4855 6.61612C17.6158 6.74643 17.723 6.94393 17.7851 7.40539C17.85 7.88843 17.8516 8.53599 17.8516 9.5V15.4C17.8516 16.364 17.85 17.0116 17.7851 17.4946C17.723 17.9561 17.6158 18.1536 17.4855 18.2839C17.3552 18.4142 17.1577 18.5214 16.6962 18.5835C16.2132 18.6484 15.5656 18.65 14.6016 18.65H13.1374H12.8588L12.6477 18.8319L8.55159 22.3632V19.4Z",stroke:"#ECFDF3","stroke-width":"1.5"}),(0,e.createElement)("path",{d:"M6.19922 14C8.49921 16 11.4992 16 13.9992 14",stroke:"#ECFDF3","stroke-width":"1.6"}),(0,e.createElement)("path",{d:"M7 10.1016V11.6016",stroke:"#ECFDF3","stroke-width":"1.5"}),(0,e.createElement)("path",{d:"M13.1992 10.1016V11.6016",stroke:"#ECFDF3","stroke-width":"1.5"}),(0,e.createElement)("path",{d:"M10.1016 7.00083L10.1016 1",stroke:"#ECFDF3","stroke-width":"1.5"}),(0,e.createElement)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.6 13.2016L1.6 10.1016H0V13.2016H1.6ZM18.6 10.1016V13.2016H20.2V10.1016H18.6Z",fill:"#00B884"}),(0,e.createElement)("circle",{cx:"10.1016",cy:"1.5",r:"0.75",fill:"#00B884",stroke:"#ECFDF3","stroke-width":"1.5"}))))}))))},g=e.createContext({});function w({baseColor:e,highlightColor:t,width:n,height:r,borderRadius:o,circle:a,direction:s,duration:i,enableAnimation:l=!0,customHighlightBackground:c}){const u={};return"rtl"===s&&(u["--animation-direction"]="reverse"),"number"==typeof i&&(u["--animation-duration"]=`${i}s`),l||(u["--pseudo-element-display"]="none"),"string"!=typeof n&&"number"!=typeof n||(u.width=n),"string"!=typeof r&&"number"!=typeof r||(u.height=r),"string"!=typeof o&&"number"!=typeof o||(u.borderRadius=o),a&&(u.borderRadius="50%"),void 0!==e&&(u["--base-color"]=e),void 0!==t&&(u["--highlight-color"]=t),"string"==typeof c&&(u["--custom-highlight-background"]=c),u}function b({count:t=1,wrapper:n,className:r,containerClassName:o,containerTestId:a,circle:s=!1,style:i,...l}){var c,u,d;const h=e.useContext(g),p={...l};for(const[e,t]of Object.entries(l))void 0===t&&delete p[e];const m={...h,...p,circle:s},f={...i,...w(m)};let b="react-loading-skeleton";r&&(b+=` ${r}`);const v=null!==(c=m.inline)&&void 0!==c&&c,y=[],k=Math.ceil(t);for(let n=0;n<k;n++){let r=f;if(k>t&&n===k-1){const e=null!==(u=r.width)&&void 0!==u?u:"100%",n=t%1,o="number"==typeof e?e*n:`calc(${e} * ${n})`;r={...r,width:o}}const o=e.createElement("span",{className:b,style:r,key:n},"‌");v?y.push(o):y.push(e.createElement(e.Fragment,{key:n},o,e.createElement("br",null)))}return e.createElement("span",{className:o,"data-testid":a,"aria-live":"polite","aria-busy":null===(d=m.enableAnimation)||void 0===d||d},n?y.map(((t,r)=>e.createElement(n,{key:r},t))):y)}n(642);const v=()=>{const t=(0,e.useRef)(),[n,o]=(0,e.useState)([]),[a,s]=(0,e.useState)(null),[i,d]=(0,e.useState)(!0),[p,m]=(0,e.useState)(null),[g,w]=(0,e.useState)(null),[v,y]=(0,e.useState)(!1),[k,E]=(0,e.useState)(""),[x,C]=(0,e.useState)(""),[_,T]=(0,e.useState)([]),[A,S]=(0,e.useState)(!1),[N,L]=(0,e.useState)(!1),[O,R]=(0,e.useState)(!0),[M,P]=(0,e.useState)(!1),[I,D]=(0,e.useState)(0),[z,$]=(0,e.useState)(!0),[B,H]=(0,e.useState)(!1),q=(0,e.useRef)(),F=(0,e.useCallback)((e=>{B||(q?.current&&q?.current?.disconnect(),q.current=new IntersectionObserver((e=>{e[0]?.isIntersecting&&z&&D((e=>e+1))})),e&&q?.current?.observe(e))}),[B,z]);(0,e.useEffect)((()=>{if(t?.current){const e=t?.current?.getBoundingClientRect()?.top+window?.scrollY;window.scrollTo({top:e-100})}}),[t?.current]),(0,e.useEffect)((()=>{const e=setTimeout((()=>{D(0),$(!0),j()}),500);return()=>clearTimeout(e)}),[k]),(0,e.useEffect)((()=>{k||j()}),[I]),(0,e.useEffect)((()=>{if(_?.length>0){const e=[...new Map(_.map((e=>[e.session_id,e]))).values()],t=e.sort(((e,t)=>{if(!e?.conversation?.length||!t?.conversation?.length)return 0;const n=new Date(e.conversation[e.conversation.length-1]?.timestamp).getTime();return new Date(t.conversation[t.conversation.length-1]?.timestamp).getTime()-n}));if(!g||!e.find((e=>e.session_id===g))){const e=t[0];e&&e.user_email&&e.session_id&&(w(e.session_id),s(e),(0,c.eB)(e.user_email,2,e.session_id).then((e=>y(e))).catch((e=>console.error("Error fetching online status:",e))))}}else w(null),s(null)}),[_]);const j=async()=>{if(k)try{P(!0),$(!1);let e=await(0,c._5)(k);const t=[...new Map(e.map((e=>[e.session_id,e]))).values()];T(t),o(t),0===e.length&&(s(null),w(null))}catch(e){console.error("Error fetching filtered chats:",e)}finally{P(!1),d(!1)}else{const e=10*I,t=10;try{H(!0),P(!1);let n=await(0,c._5)("",e,t);o((e=>{const t=0===I?n:[...e,...n];return[...new Map(t.map((e=>[e.session_id,e]))).values()]})),T((e=>{const t=0===I?n:[...e,...n];return[...new Map(t.map((e=>[e.session_id,e]))).values()]})),n?.length<t&&$(!1)}catch(e){console.error("Error fetching all chats:",e)}finally{H(!1),d(!1)}}};return i?(0,e.createElement)("div",{className:"betterdocs-dashboard-container"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-body"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-row"},(0,e.createElement)("div",{className:"betterdocs-col_874"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-row"},(0,e.createElement)("div",{className:"betterdocs-col_306",style:{paddingTop:"16px",paddingLeft:"8px",paddingRight:"8px",paddingBottom:"16px"}},(0,e.createElement)(b,{count:35})),(0,e.createElement)("div",{className:"betterdocs-col_568 betterdocs-cal_unactive-phone",style:{paddingTop:"16px",paddingLeft:"8px",paddingRight:"8px",paddingBottom:"16px"}},(0,e.createElement)(b,{count:35})))),(0,e.createElement)("div",{className:"betterdocs-col_264 betterdocs-cal_unactive-phone",style:{paddingTop:"16px",paddingLeft:"8px",paddingRight:"8px",paddingBottom:"16px"}},(0,e.createElement)(b,{count:35}))))):(0,e.createElement)("div",{className:"betterdocs-dashboard-container",ref:t},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-body"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-row"},(0,e.createElement)("div",{className:"betterdocs-col_874"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-row"},(0,e.createElement)("div",{className:"betterdocs-col_306 "+(O?"":" hidden-user-item")},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-src_bar"},(0,e.createElement)("form",{action:"#",onSubmit:e=>e?.preventDefault()},(0,e.createElement)("div",{className:"betterdocs-src_icon"},(0,e.createElement)("button",{type:"submit",className:"betterdocs-src_btn"},(0,e.createElement)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M14 14L11.1 11.1M12.6667 7.33333C12.6667 10.2789 10.2789 12.6667 7.33333 12.6667C4.38781 12.6667 2 10.2789 2 7.33333C2 4.38781 4.38781 2 7.33333 2C10.2789 2 12.6667 4.38781 12.6667 7.33333Z",stroke:"#667085",strokeWidth:"1.33333",strokeLinecap:"round",strokeLinejoin:"round"}))),(0,e.createElement)("input",{type:"search",placeholder:"Search in name, message, location",className:"betterdocs-ai_chatbot-src_input",value:k,onChange:e=>{E(e?.target?.value),P(!0)}})))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author"},(0,e.createElement)("ul",{className:"betterdocs-ai_chatbot-author_list"},M&&(0,e.createElement)("div",{className:"search-loading",style:{textAlign:"center",padding:"10px 0"}},(0,r.__)("Searching...","betterdocs-ai-chatbot")),!M&&_?.length<1?(0,e.createElement)("li",{className:"betterdocs-ai_chatbot-author_list-item"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-no_conversation-icon_bg"},(0,e.createElement)("span",null,(0,e.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M3.665 12C3.665 7.3938 7.40263 3.665 12 3.665C16.5974 3.665 20.335 7.3938 20.335 12C20.335 16.6067 16.6067 20.335 12 20.335C10.5286 20.335 9.14339 19.9549 7.95304 19.2841C7.69142 19.1367 7.37419 19.089 7.06845 19.1734L3.9723 20.0277L4.82689 16.9307C4.91089 16.6262 4.86406 16.3101 4.71762 16.0488C4.04498 14.8487 3.665 13.4711 3.665 12Z",stroke:"#B42318","stroke-width":"1.33","stroke-miterlimit":"10"}),(0,e.createElement)("path",{d:"M9 12H9.075",stroke:"#B42318","stroke-width":"1.33","stroke-miterlimit":"10","stroke-linecap":"round"}),(0,e.createElement)("path",{d:"M12.0742 12H12.1492",stroke:"#B42318","stroke-width":"1.33","stroke-miterlimit":"10","stroke-linecap":"round"}),(0,e.createElement)("path",{d:"M15.1484 12H15.2234",stroke:"#B42318","stroke-width":"1.33","stroke-miterlimit":"10","stroke-linecap":"round"})))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_info"},(0,e.createElement)("h4",{className:"betterdocs-ai_chatbot-author_name no-conversation_text"},k?(0,r.__)("No conversation found","betterdocs-ai-chatbot"):(0,r.__)("You have no conversation","betterdocs-ai-chatbot")))):(0,e.createElement)(e.Fragment,null,[...new Map(_.map((e=>[e.session_id,e]))).values()].sort(((e,t)=>{if(!e?.conversation?.length||!t?.conversation?.length)return 0;const n=new Date(e.conversation[e.conversation.length-1]?.timestamp).getTime();return new Date(t.conversation[t.conversation.length-1]?.timestamp).getTime()-n})).map(((t,n)=>{if(!t?.user_email||!t?.profile_info?.entry)return null;const r=g===t.session_id?" author-chat_see":"",o=(e=>{if(e)return(e=>e.replace(/<[^>]*>/g,""))(e).split(/\s+/).slice(0,4).join(" ")+(e.split(/\s+/).length>4?"...":"")})(t.conversation&&t.conversation[t.conversation.length-1]?.text||""),a=t.conversation[t.conversation.length-1]?.timestamp,i=!k&&n===_.length-2;return(0,e.createElement)("li",{key:t?.session_id,className:`betterdocs-ai_chatbot-author_list-item${r}`,onClick:()=>(async e=>{w(e?.session_id),s(e);try{const t=await(0,c.eB)(e?.user_email,2,e?.session_id);y(t)}catch(e){console.error("Error fetching online status:",e)}L(!0),R(!1)})(t),ref:i?F:null},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_image"},(0,e.createElement)("div",{className:"author-image"},(0,e.createElement)("img",{src:t?.profile_info?.entry?.[0]?.thumbnailUrl||""})),(0,e.createElement)("div",{className:"author-cuntry"},(0,e.createElement)(l,{countryCode:t?.main_information?.country?.toLowerCase(),width:"12",height:"12"}))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_info"},(0,e.createElement)("div",null,(0,e.createElement)("h4",{className:"betterdocs-ai_chatbot-author_name"},t?.profile_info?.entry?.[0]?.displayName||""),(0,e.createElement)("p",{className:"betterdocs-ai_chatbot-author_msg"},o)),(0,e.createElement)("span",{className:"betterdocs-ai_chatbot-author_date"},(0,e.createElement)(u,{timestamp:a,timezone:t?.main_information?.timezone,showTime:!1}))))})),B&&(0,e.createElement)("div",{className:"search-loading",style:{textAlign:"center",padding:"10px 0"}},(0,r.__)("Loading...","betterdocs-ai-chatbot")))))),(0,e.createElement)("div",{className:`betterdocs-col_568 betterdocs-cal_unactive-phone${A?" hidden-chatbox":""}${N?" show-chatbox":""}${_?.length<1?" no-conversation":""} `},_?.length>0&&(0,e.createElement)("div",{className:"betterdocs-empty_space-63"},(0,e.createElement)("div",{className:"acitve-phone"},(0,e.createElement)("div",{className:"back-btn active-back_btn",onClick:()=>(S(!1),L(!1),void R(!0))},(0,e.createElement)("svg",{width:"20",height:"20",viewBox:"0 0 1024 1024",class:"icon",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64"}),(0,e.createElement)("path",{d:"m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312z"}))),a?.profile_info?.entry?.[0]?.displayName&&(0,e.createElement)("div",{className:"active-wrapper",onClick:()=>(S(!0),void L(!1))},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_image"},(0,e.createElement)("div",{className:"author-image"},(0,e.createElement)("img",{src:a?.profile_info?.entry?.[0]?.thumbnailUrl||""}))),(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-author_info"},(0,e.createElement)("h4",{className:"betterdocs-ai_chatbot-author_name"},a?.profile_info?.entry?.[0]?.displayName||""))))),_?.length<1&&(0,e.createElement)("div",{className:"betterdocs-cal_100Per betterdocs-cal_unactive-phone"},(0,e.createElement)("div",{className:"betterdocs-ai_chatbot-welcome_icon"},(0,e.createElement)("svg",{width:"112",height:"96",viewBox:"0 0 112 96",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"mask0_9274_27635",maskUnits:"userSpaceOnUse",x:"-3",y:"-11",width:"118",height:"118"},(0,e.createElement)("path",{d:"M114.53 -10.1641H-2.54297V106.909H114.53V-10.1641Z",fill:"white"})),(0,e.createElement)("g",{mask:"url(#mask0_9274_27635)"},(0,e.createElement)("path",{d:"M56.0011 94.8148C81.8642 94.8148 102.83 73.8485 102.83 47.9855C102.83 22.1225 81.8642 1.15625 56.0011 1.15625C30.1381 1.15625 9.17188 22.1225 9.17188 47.9855C9.17188 73.8485 30.1381 94.8148 56.0011 94.8148Z",fill:"#F2F4F7"}),(0,e.createElement)("path",{d:"M38.4353 14.6484H74.5473C77.9959 14.6484 80.7912 17.4438 80.7912 20.8923V68.9037C80.7912 72.3522 77.9959 75.1476 74.5473 75.1476H38.4353C34.9867 75.1476 32.1914 72.3522 32.1914 68.9037V20.8923C32.1914 17.4438 34.9867 14.6484 38.4353 14.6484Z",fill:"url(#paint0_linear_9274_27635)"}),(0,e.createElement)("path",{d:"M109.917 7.95312H86.4703C85.378 7.95312 84.4922 8.88503 84.4922 10.0351V21.6327C84.4922 22.7824 85.378 23.7147 86.4703 23.7147H109.917C111.009 23.7147 111.895 22.7824 111.895 21.6327V10.0351C111.895 8.88503 111.009 7.95312 109.917 7.95312Z",fill:"white"}),(0,e.createElement)("path",{d:"M90.3415 18.3236C91.6347 18.3236 92.6829 17.2754 92.6829 15.9821C92.6829 14.6888 91.6347 13.6406 90.3415 13.6406C89.0482 13.6406 88 14.6888 88 15.9821C88 17.2754 89.0482 18.3236 90.3415 18.3236Z",fill:"#C5D8D3"}),(0,e.createElement)("path",{d:"M98.1462 13.6406H105.951C107.244 13.6406 108.292 14.6888 108.292 15.9821C108.292 17.2754 107.244 18.3236 105.951 18.3236H98.1462C96.8529 18.3236 95.8047 17.2754 95.8047 15.9821C95.8047 14.6888 96.8529 13.6406 98.1462 13.6406Z",fill:"#DCEAE9"}),(0,e.createElement)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M19.4396 25.418C19.4326 24.8616 19.3627 24.3 19.223 23.7349C18.7333 21.7517 16.5487 20.4783 14.2236 20.179C11.8997 19.8797 9.48253 20.5611 8.55102 22.2863C8.01834 23.2725 7.94497 24.1224 8.16273 24.8393C8.37931 25.5523 8.89365 26.1431 9.5918 26.604C11.5383 27.8867 14.9475 28.1486 16.503 27.6124C17.2222 27.3638 17.9251 27.0645 18.6092 26.7218C18.2181 28.9642 16.7602 31.0883 14.8144 33.0024C10.5858 37.1628 4.02419 40.3207 0.450726 41.4817C0.258726 41.5441 0.15219 41.7568 0.211897 41.9574C0.271604 42.1579 0.475702 42.2703 0.667702 42.2079C4.3118 41.0239 11.0014 37.7989 15.3136 33.5562C17.5415 31.365 19.1286 28.8978 19.3998 26.3043C24.4359 23.4941 28.5713 18.3956 32.1073 14.1045C32.2388 13.9464 32.2213 13.7057 32.0687 13.5683C31.9161 13.4321 31.6866 13.4493 31.5551 13.6085C28.1631 17.724 24.2267 22.6192 19.4396 25.418ZM18.7075 25.8258C18.7352 25.2003 18.6763 24.565 18.5175 23.9242C18.094 22.2075 16.1475 21.1925 14.1346 20.9334C12.9011 20.7749 11.6324 20.9057 10.6373 21.37C10.0117 21.6616 9.49658 22.0846 9.18595 22.6602C8.77814 23.4153 8.68995 24.0612 8.85697 24.6091C9.02438 25.1609 9.4396 25.6042 9.98048 25.9601C11.7545 27.1297 14.8585 27.3782 16.2743 26.8896C17.1091 26.602 17.9196 26.243 18.7075 25.8258Z",fill:"#344054"}),(0,e.createElement)("path",{d:"M96.9743 85.4486C98.2674 85.4486 99.3157 84.4002 99.3157 83.1071C99.3157 81.8139 98.2674 80.7656 96.9743 80.7656C95.6811 80.7656 94.6328 81.8139 94.6328 83.1071C94.6328 84.4002 95.6811 85.4486 96.9743 85.4486Z",fill:"#DCEAE9"}),(0,e.createElement)("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M17.5164 77.0157C17.9343 76.8593 18.3741 76.6509 18.7132 76.3492C19.116 75.9913 19.2799 75.5301 19.3895 75.0446C19.5308 74.4206 19.5874 73.7564 19.7587 73.1316C19.8219 72.8994 19.9441 72.8116 19.9964 72.773C20.1286 72.6747 20.2625 72.6481 20.3882 72.6583C20.5372 72.6696 20.7421 72.7289 20.8771 72.9919C20.8963 73.0298 20.9212 73.0872 20.938 73.1656C20.9501 73.2234 20.9583 73.4032 20.9712 73.4778C21.0036 73.6608 21.0305 73.8438 21.0563 74.0276C21.1409 74.6399 21.1897 75.1601 21.4574 75.7229C21.8207 76.4866 22.1848 76.9537 22.6785 77.1609C23.1562 77.3611 23.7267 77.3232 24.4561 77.1664C24.5255 77.1488 24.5942 77.1336 24.6621 77.1211C24.9837 77.0622 25.2912 77.2842 25.3544 77.6214C25.4176 77.9582 25.2131 78.2864 24.8943 78.3605C24.828 78.3761 24.7624 78.3906 24.6976 78.4042C23.7119 78.661 22.5708 79.5773 21.9078 80.3796C21.7037 80.6271 21.4044 81.319 21.0992 81.7599C20.874 82.0858 20.6207 82.3 20.4081 82.3761C20.2656 82.4272 20.1458 82.4191 20.0467 82.3933C19.9023 82.3562 19.7829 82.2739 19.6912 82.1431C19.6412 82.0717 19.5948 81.9761 19.5725 81.8536C19.562 81.7947 19.5608 81.6452 19.5612 81.5773C19.4988 81.3521 19.4227 81.1324 19.3673 80.9053C19.235 80.3629 18.9755 80.0198 18.6672 79.5664C18.3788 79.1422 18.0689 78.8753 17.6147 78.6626C17.5558 78.6473 17.0789 78.5244 16.9107 78.4542C16.6648 78.3508 16.5474 78.1775 16.5048 78.0842C16.4326 77.9262 16.4252 77.788 16.4397 77.6725C16.4611 77.5024 16.5337 77.3568 16.6625 77.2393C16.7421 77.1664 16.8611 77.0953 17.0207 77.0606C17.1437 77.0337 17.4707 77.0181 17.5164 77.0157ZM20.3132 76.1322C20.3351 76.1841 20.3589 76.236 20.3839 76.2887C20.9158 77.4072 21.5109 78.0319 22.2344 78.3348L22.2586 78.3445C21.7747 78.7227 21.3364 79.1453 21.002 79.5504C20.8639 79.7174 20.6816 80.0639 20.4842 80.419C20.305 79.8064 20.0124 79.3732 19.644 78.8308C19.3622 78.4171 19.0676 78.1057 18.705 77.852C18.9864 77.7002 19.2545 77.5223 19.4883 77.3147C19.8773 76.9685 20.1345 76.5677 20.3132 76.1322Z",fill:"#C5D8D3"})),(0,e.createElement)("g",{"clip-path":"url(#clip0_9274_27635)"},(0,e.createElement)("rect",{x:"37.2656",y:"24.5859",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"#DCEAE9"}),(0,e.createElement)("path",{d:"M42.6081 31.8516C39.967 31.8516 39.0391 33.19 39.0391 34.0822V35.4206H46.1772V34.0822C46.1772 33.19 45.2492 31.8516 42.6081 31.8516Z",fill:"white"}),(0,e.createElement)("path",{d:"M42.6092 31.4058C43.7179 31.4058 44.6168 30.507 44.6168 29.3982C44.6168 28.2895 43.7179 27.3906 42.6092 27.3906C41.5004 27.3906 40.6016 28.2895 40.6016 29.3982C40.6016 30.507 41.5004 31.4058 42.6092 31.4058Z",fill:"white"})),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"25.6016",width:"23.3612",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"}),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"31.6953",width:"18.5366",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"}),(0,e.createElement)("g",{opacity:"0.9"},(0,e.createElement)("g",{"clip-path":"url(#clip1_9274_27635)"},(0,e.createElement)("rect",{x:"37.2656",y:"40.1953",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"#DCEAE9"}),(0,e.createElement)("path",{d:"M42.6081 47.4609C39.967 47.4609 39.0391 48.7993 39.0391 49.6916V51.03H46.1772V49.6916C46.1772 48.7993 45.2492 47.4609 42.6081 47.4609Z",fill:"white"}),(0,e.createElement)("path",{d:"M42.6092 47.0152C43.7179 47.0152 44.6168 46.1164 44.6168 45.0076C44.6168 43.8988 43.7179 43 42.6092 43C41.5004 43 40.6016 43.8988 40.6016 45.0076C40.6016 46.1164 41.5004 47.0152 42.6092 47.0152Z",fill:"white"})),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"41.2109",width:"23.3612",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"}),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"47.3047",width:"18.5366",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"})),(0,e.createElement)("g",{opacity:"0.41"},(0,e.createElement)("g",{"clip-path":"url(#clip2_9274_27635)"},(0,e.createElement)("rect",{x:"37.2656",y:"55.8125",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"#DCEAE9"}),(0,e.createElement)("path",{d:"M42.6081 63.0703C39.967 63.0703 39.0391 64.4087 39.0391 65.301V66.6394H46.1772V65.301C46.1772 64.4087 45.2492 63.0703 42.6081 63.0703Z",fill:"white"}),(0,e.createElement)("path",{d:"M42.6092 62.6246C43.7179 62.6246 44.6168 61.7257 44.6168 60.617C44.6168 59.5082 43.7179 58.6094 42.6092 58.6094C41.5004 58.6094 40.6016 59.5082 40.6016 60.617C40.6016 61.7257 41.5004 62.6246 42.6092 62.6246Z",fill:"white"})),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"56.8281",width:"23.3612",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"}),(0,e.createElement)("rect",{opacity:"0.58",x:"50.9766",y:"62.9219",width:"18.5366",height:"2.53926",rx:"1.26963",fill:"#DCEAE9"})),(0,e.createElement)("g",{filter:"url(#filter0_d_9274_27635)"},(0,e.createElement)("path",{d:"M72.3074 83.1776C84.0537 83.1776 93.5756 73.7427 93.5756 62.1044C93.5756 50.4662 84.0537 41.0312 72.3074 41.0312C60.561 41.0312 51.0391 50.4662 51.0391 62.1044C51.0391 73.7427 60.561 83.1776 72.3074 83.1776Z",fill:"white","fill-opacity":"0.3","shape-rendering":"crispEdges"}),(0,e.createElement)("path",{d:"M93.3805 62.1044C93.3805 73.6332 83.9476 82.9825 72.3074 82.9825C60.6671 82.9825 51.2342 73.6332 51.2342 62.1044C51.2342 50.5756 60.6671 41.2264 72.3074 41.2264C83.9476 41.2264 93.3805 50.5756 93.3805 62.1044Z",stroke:"white","stroke-width":"0.390244","shape-rendering":"crispEdges"})),(0,e.createElement)("path",{d:"M76.1326 62.4L80.5642 57.9856C80.9795 57.5488 81.2069 56.9685 81.1982 56.3677C81.1898 55.7669 80.9459 55.1934 80.5185 54.7683C80.0911 54.3436 79.5138 54.101 78.9093 54.0926C78.3049 54.0842 77.7209 54.31 77.2814 54.7226L72.8401 59.1371L68.4085 54.7226C68.1952 54.4985 67.9388 54.3191 67.6545 54.1948C67.3703 54.0708 67.0639 54.0046 66.7534 54.0002C66.4429 53.9959 66.1348 54.0533 65.8472 54.1692C65.5596 54.2855 65.2981 54.4575 65.0784 54.6759C64.859 54.894 64.6856 55.1537 64.569 55.4397C64.4521 55.7256 64.3943 56.032 64.3987 56.3408C64.403 56.6493 64.4696 56.9537 64.5946 57.2363C64.7192 57.5189 64.9 57.7736 65.1258 57.9856L69.5647 62.4L65.1258 66.8144C64.9 67.0264 64.7192 67.2811 64.5946 67.5637C64.4696 67.8463 64.403 68.1507 64.3987 68.4592C64.3943 68.7679 64.4521 69.0741 64.569 69.3603C64.6856 69.6463 64.859 69.906 65.0784 70.1241C65.2981 70.3421 65.5596 70.5145 65.8472 70.6308C66.1348 70.7467 66.4429 70.8041 66.7534 70.7998C67.0639 70.7954 67.3703 70.7292 67.6545 70.6052C67.9388 70.4809 68.1952 70.3015 68.4085 70.0774L72.8498 65.6629L77.2915 70.0774C77.735 70.4654 78.3106 70.6711 78.9016 70.6519C79.4923 70.6331 80.0534 70.3909 80.4708 69.9749C80.8881 69.5586 81.13 69.0001 81.1475 68.4131C81.1649 67.8258 80.9563 67.2539 80.5642 66.8144L76.1326 62.4Z",fill:"#344054"}),(0,e.createElement)("defs",null,(0,e.createElement)("filter",{id:"filter0_d_9274_27635",x:"41.6732",y:"33.2264",width:"61.2669",height:"60.8801",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB"},(0,e.createElement)("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),(0,e.createElement)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,e.createElement)("feOffset",{dy:"1.56098"}),(0,e.createElement)("feGaussianBlur",{stdDeviation:"4.68293"}),(0,e.createElement)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,e.createElement)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"}),(0,e.createElement)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_9274_27635"}),(0,e.createElement)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_9274_27635",result:"shape"})),(0,e.createElement)("linearGradient",{id:"paint0_linear_9274_27635",x1:"56.4913",y1:"14.6484",x2:"56.4913",y2:"75.1476",gradientUnits:"userSpaceOnUse"},(0,e.createElement)("stop",{"stop-color":"white"}),(0,e.createElement)("stop",{offset:"0.7188","stop-color":"#FAFAFA"})),(0,e.createElement)("clipPath",{id:"clip0_9274_27635"},(0,e.createElement)("rect",{x:"37.2656",y:"24.5859",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"white"})),(0,e.createElement)("clipPath",{id:"clip1_9274_27635"},(0,e.createElement)("rect",{x:"37.2656",y:"40.1953",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"white"})),(0,e.createElement)("clipPath",{id:"clip2_9274_27635"},(0,e.createElement)("rect",{x:"37.2656",y:"55.8125",width:"10.6649",height:"10.6649",rx:"5.33244",fill:"white"}))))),(0,e.createElement)("h4",{className:"betterdocs-ai_chatbot-welcome_text"},(0,r.__)("Welcome to BetterDocs Chatbot","betterdocs-ai-chatbot")),(0,e.createElement)("p",{className:"betterdocs-ai_chatbot-welcome_sub-text"},(0,r.__)("No chat selected","betterdocs-ai-chatbot"))),a&&(0,e.createElement)(f,{currentConversation:{...a,conversation:[...a?.conversation]}})))),a&&a?.user_email&&(0,e.createElement)(h,{currentConversation:a,onlineStatus:v,activeProfileInfo:A,setActiveProfileInfo:S,setActiveChatbox:L}))))};function y(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=y(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const k=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=y(e))&&(r&&(r+=" "),r+=t);return r},E=e=>"number"==typeof e&&!isNaN(e),x=e=>"string"==typeof e,C=e=>"function"==typeof e,_=e=>x(e)||C(e)?e:null,T=t=>(0,e.isValidElement)(t)||x(t)||C(t)||E(t);function A(t){let{enter:n,exit:r,appendPosition:o=!1,collapse:a=!0,collapseDuration:s=300}=t;return function(t){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:d,isIn:h,playToast:p}=t;const m=o?`${n}--${l}`:n,f=o?`${r}--${l}`:r,g=(0,e.useRef)(0);return(0,e.useLayoutEffect)((()=>{const e=d.current,t=m.split(" "),n=r=>{r.target===d.current&&(p(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===g.current&&"animationcancel"!==r.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)}),[]),(0,e.useEffect)((()=>{const e=d.current,t=()=>{e.removeEventListener("animationend",t),a?function(e,t,n){void 0===n&&(n=300);const{scrollHeight:r,style:o}=e;requestAnimationFrame((()=>{o.minHeight="initial",o.height=r+"px",o.transition=`all ${n}ms`,requestAnimationFrame((()=>{o.height="0",o.padding="0",o.margin="0",setTimeout(t,n)}))}))}(e,u,s):u()};h||(c?t():(g.current=1,e.className+=` ${f}`,e.addEventListener("animationend",t)))}),[h]),e.createElement(e.Fragment,null,i)}}function S(e,t){return null!=e?{content:e.content,containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,status:t}:{}}const N=new Map;let L=[];const O=new Set,R=e=>O.forEach((t=>t(e))),M=()=>N.size>0;function P(e,t){var n;if(t)return!(null==(n=N.get(t))||!n.isToastActive(e));let r=!1;return N.forEach((t=>{t.isToastActive(e)&&(r=!0)})),r}function I(e,t){T(e)&&(M()||L.push({content:e,options:t}),N.forEach((n=>{n.buildToast(e,t)})))}function D(e,t){N.forEach((n=>{null!=t&&null!=t&&t.containerId?(null==t?void 0:t.containerId)===n.id&&n.toggle(e,null==t?void 0:t.id):n.toggle(e,null==t?void 0:t.id)}))}function z(t){const{subscribe:n,getSnapshot:r,setProps:o}=(0,e.useRef)(function(t){const n=t.containerId||1;return{subscribe(r){const o=function(t,n,r){let o=1,a=0,s=[],i=[],l=[],c=n;const u=new Map,d=new Set,h=()=>{l=Array.from(u.values()),d.forEach((e=>e()))},p=e=>{i=null==e?[]:i.filter((t=>t!==e)),h()},m=t=>{const{toastId:n,onOpen:o,updateId:a,children:s}=t.props,l=null==a;t.staleId&&u.delete(t.staleId),u.set(n,t),i=[...i,t.props.toastId].filter((e=>e!==t.staleId)),h(),r(S(t,l?"added":"updated")),l&&C(o)&&o((0,e.isValidElement)(s)&&s.props)};return{id:t,props:c,observe:e=>(d.add(e),()=>d.delete(e)),toggle:(e,t)=>{u.forEach((n=>{null!=t&&t!==n.props.toastId||C(n.toggle)&&n.toggle(e)}))},removeToast:p,toasts:u,clearQueue:()=>{a-=s.length,s=[]},buildToast:(n,i)=>{if((e=>{let{containerId:n,toastId:r,updateId:o}=e;const a=n?n!==t:1!==t,s=u.has(r)&&null==o;return a||s})(i))return;const{toastId:l,updateId:d,data:f,staleId:g,delay:w}=i,b=()=>{p(l)},v=null==d;v&&a++;const y={...c,style:c.toastStyle,key:o++,...Object.fromEntries(Object.entries(i).filter((e=>{let[t,n]=e;return null!=n}))),toastId:l,updateId:d,data:f,closeToast:b,isIn:!1,className:_(i.className||c.toastClassName),bodyClassName:_(i.bodyClassName||c.bodyClassName),progressClassName:_(i.progressClassName||c.progressClassName),autoClose:!i.isLoading&&(k=i.autoClose,A=c.autoClose,!1===k||E(k)&&k>0?k:A),deleteToast(){const t=u.get(l),{onClose:n,children:o}=t.props;C(n)&&n((0,e.isValidElement)(o)&&o.props),r(S(t,"removed")),u.delete(l),a--,a<0&&(a=0),s.length>0?m(s.shift()):h()}};var k,A;y.closeButton=c.closeButton,!1===i.closeButton||T(i.closeButton)?y.closeButton=i.closeButton:!0===i.closeButton&&(y.closeButton=!T(c.closeButton)||c.closeButton);let N=n;(0,e.isValidElement)(n)&&!x(n.type)?N=(0,e.cloneElement)(n,{closeToast:b,toastProps:y,data:f}):C(n)&&(N=n({closeToast:b,toastProps:y,data:f}));const L={content:N,props:y,staleId:g};c.limit&&c.limit>0&&a>c.limit&&v?s.push(L):E(w)?setTimeout((()=>{m(L)}),w):m(L)},setProps(e){c=e},setToggle:(e,t)=>{u.get(e).toggle=t},isToastActive:e=>i.some((t=>t===e)),getSnapshot:()=>l}}(n,t,R);N.set(n,o);const a=o.observe(r);return L.forEach((e=>I(e.content,e.options))),L=[],()=>{a(),N.delete(n)}},setProps(e){var t;null==(t=N.get(n))||t.setProps(e)},getSnapshot(){var e;return null==(e=N.get(n))?void 0:e.getSnapshot()}}}(t)).current;o(t);const a=(0,e.useSyncExternalStore)(n,r,r);return{getToastToRender:function(e){if(!a)return[];const n=new Map;return t.newestOnTop&&a.reverse(),a.forEach((e=>{const{position:t}=e.props;n.has(t)||n.set(t,[]),n.get(t).push(e)})),Array.from(n,(t=>e(t[0],t[1])))},isToastActive:P,count:null==a?void 0:a.length}}function $(t){const[n,r]=(0,e.useState)(!1),[o,a]=(0,e.useState)(!1),s=(0,e.useRef)(null),i=(0,e.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:l,pauseOnHover:c,closeToast:u,onClick:d,closeOnClick:h}=t;var p,m;function f(){r(!0)}function g(){r(!1)}function w(e){const r=s.current;i.canDrag&&r&&(i.didMove=!0,n&&g(),i.delta="x"===t.draggableDirection?e.clientX-i.start:e.clientY-i.start,i.start!==e.clientX&&(i.canCloseOnClick=!1),r.style.transform=`translate3d(${"x"===t.draggableDirection?`${i.delta}px, var(--y)`:`0, calc(${i.delta}px + var(--y))`},0)`,r.style.opacity=""+(1-Math.abs(i.delta/i.removalDistance)))}function b(){document.removeEventListener("pointermove",w),document.removeEventListener("pointerup",b);const e=s.current;if(i.canDrag&&i.didMove&&e){if(i.canDrag=!1,Math.abs(i.delta)>i.removalDistance)return a(!0),t.closeToast(),void t.collapseAll();e.style.transition="transform 0.2s, opacity 0.2s",e.style.removeProperty("transform"),e.style.removeProperty("opacity")}}null==(m=N.get((p={id:t.toastId,containerId:t.containerId,fn:r}).containerId||1))||m.setToggle(p.id,p.fn),(0,e.useEffect)((()=>{if(t.pauseOnFocusLoss)return document.hasFocus()||g(),window.addEventListener("focus",f),window.addEventListener("blur",g),()=>{window.removeEventListener("focus",f),window.removeEventListener("blur",g)}}),[t.pauseOnFocusLoss]);const v={onPointerDown:function(e){if(!0===t.draggable||t.draggable===e.pointerType){i.didMove=!1,document.addEventListener("pointermove",w),document.addEventListener("pointerup",b);const n=s.current;i.canCloseOnClick=!0,i.canDrag=!0,n.style.transition="none","x"===t.draggableDirection?(i.start=e.clientX,i.removalDistance=n.offsetWidth*(t.draggablePercent/100)):(i.start=e.clientY,i.removalDistance=n.offsetHeight*(80===t.draggablePercent?1.5*t.draggablePercent:t.draggablePercent)/100)}},onPointerUp:function(e){const{top:n,bottom:r,left:o,right:a}=s.current.getBoundingClientRect();"touchend"!==e.nativeEvent.type&&t.pauseOnHover&&e.clientX>=o&&e.clientX<=a&&e.clientY>=n&&e.clientY<=r?g():f()}};return l&&c&&(v.onMouseEnter=g,t.stacked||(v.onMouseLeave=f)),h&&(v.onClick=e=>{d&&d(e),i.canCloseOnClick&&u()}),{playToast:f,pauseToast:g,isRunning:n,preventExitTransition:o,toastRef:s,eventHandlers:v}}function B(t){let{delay:n,isRunning:r,closeToast:o,type:a="default",hide:s,className:i,style:l,controlledProgress:c,progress:u,rtl:d,isIn:h,theme:p}=t;const m=s||c&&0===u,f={...l,animationDuration:`${n}ms`,animationPlayState:r?"running":"paused"};c&&(f.transform=`scaleX(${u})`);const g=k("Toastify__progress-bar",c?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated",`Toastify__progress-bar-theme--${p}`,`Toastify__progress-bar--${a}`,{"Toastify__progress-bar--rtl":d}),w=C(i)?i({rtl:d,type:a,defaultClassName:g}):k(g,i),b={[c&&u>=1?"onTransitionEnd":"onAnimationEnd"]:c&&u<1?null:()=>{h&&o()}};return e.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":m},e.createElement("div",{className:`Toastify__progress-bar--bg Toastify__progress-bar-theme--${p} Toastify__progress-bar--${a}`}),e.createElement("div",{role:"progressbar","aria-hidden":m?"true":"false","aria-label":"notification timer",className:w,style:f,...b}))}let H=1;const q=()=>""+H++;function F(e){return e&&(x(e.toastId)||E(e.toastId))?e.toastId:q()}function j(e,t){return I(e,t),t.toastId}function U(e,t){return{...t,type:t&&t.type||e,toastId:F(t)}}function V(e){return(t,n)=>j(t,U(e,n))}function Z(e,t){return j(e,U("default",t))}Z.loading=(e,t)=>j(e,U("default",{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1,...t})),Z.promise=function(e,t,n){let r,{pending:o,error:a,success:s}=t;o&&(r=x(o)?Z.loading(o,n):Z.loading(o.render,{...n,...o}));const i={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},l=(e,t,o)=>{if(null==t)return void Z.dismiss(r);const a={type:e,...i,...n,data:o},s=x(t)?{render:t}:t;return r?Z.update(r,{...a,...s}):Z(s.render,{...a,...s}),o},c=C(e)?e():e;return c.then((e=>l("success",s,e))).catch((e=>l("error",a,e))),c},Z.success=V("success"),Z.info=V("info"),Z.error=V("error"),Z.warning=V("warning"),Z.warn=Z.warning,Z.dark=(e,t)=>j(e,U("default",{theme:"dark",...t})),Z.dismiss=function(e){!function(e){var t;if(M()){if(null==e||x(t=e)||E(t))N.forEach((t=>{t.removeToast(e)}));else if(e&&("containerId"in e||"id"in e)){const t=N.get(e.containerId);t?t.removeToast(e.id):N.forEach((t=>{t.removeToast(e.id)}))}}else L=L.filter((t=>null!=e&&t.options.toastId!==e))}(e)},Z.clearWaitingQueue=function(e){void 0===e&&(e={}),N.forEach((t=>{!t.props.limit||e.containerId&&t.id!==e.containerId||t.clearQueue()}))},Z.isActive=P,Z.update=function(e,t){void 0===t&&(t={});const n=((e,t)=>{var n;let{containerId:r}=t;return null==(n=N.get(r||1))?void 0:n.toasts.get(e)})(e,t);if(n){const{props:r,content:o}=n,a={delay:100,...r,...t,toastId:t.toastId||e,updateId:q()};a.toastId!==e&&(a.staleId=e);const s=a.render||o;delete a.render,j(s,a)}},Z.done=e=>{Z.update(e,{progress:1})},Z.onChange=function(e){return O.add(e),()=>{O.delete(e)}},Z.play=e=>D(!0,e),Z.pause=e=>D(!1,e);const G="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,W=t=>{let{theme:n,type:r,isLoading:o,...a}=t;return e.createElement("svg",{viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===n?"currentColor":`var(--toastify-icon-color-${r})`,...a})},Y={info:function(t){return e.createElement(W,{...t},e.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(t){return e.createElement(W,{...t},e.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(t){return e.createElement(W,{...t},e.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(t){return e.createElement(W,{...t},e.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return e.createElement("div",{className:"Toastify__spinner"})}},K=t=>{const{isRunning:n,preventExitTransition:r,toastRef:o,eventHandlers:a,playToast:s}=$(t),{closeButton:i,children:l,autoClose:c,onClick:u,type:d,hideProgressBar:h,closeToast:p,transition:m,position:f,className:g,style:w,bodyClassName:b,bodyStyle:v,progressClassName:y,progressStyle:E,updateId:x,role:_,progress:T,rtl:A,toastId:S,deleteToast:N,isIn:L,isLoading:O,closeOnClick:R,theme:M}=t,P=k("Toastify__toast",`Toastify__toast-theme--${M}`,`Toastify__toast--${d}`,{"Toastify__toast--rtl":A},{"Toastify__toast--close-on-click":R}),I=C(g)?g({rtl:A,position:f,type:d,defaultClassName:P}):k(P,g),D=function(t){let{theme:n,type:r,isLoading:o,icon:a}=t,s=null;const i={theme:n,type:r};return!1===a||(C(a)?s=a({...i,isLoading:o}):(0,e.isValidElement)(a)?s=(0,e.cloneElement)(a,i):o?s=Y.spinner():(e=>e in Y)(r)&&(s=Y[r](i))),s}(t),z=!!T||!c,H={closeToast:p,type:d,theme:M};let q=null;return!1===i||(q=C(i)?i(H):(0,e.isValidElement)(i)?(0,e.cloneElement)(i,H):function(t){let{closeToast:n,theme:r,ariaLabel:o="close"}=t;return e.createElement("button",{className:`Toastify__close-button Toastify__close-button--${r}`,type:"button",onClick:e=>{e.stopPropagation(),n(e)},"aria-label":o},e.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},e.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(H)),e.createElement(m,{isIn:L,done:N,position:f,preventExitTransition:r,nodeRef:o,playToast:s},e.createElement("div",{id:S,onClick:u,"data-in":L,className:I,...a,style:w,ref:o},e.createElement("div",{...L&&{role:_},className:C(b)?b({type:d}):k("Toastify__toast-body",b),style:v},null!=D&&e.createElement("div",{className:k("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!O})},D),e.createElement("div",null,l)),q,e.createElement(B,{...x&&!z?{key:`pb-${x}`}:{},rtl:A,theme:M,delay:c,isRunning:n,isIn:L,closeToast:p,hide:h,type:d,style:E,className:y,controlledProgress:z,progress:T||0})))},Q=function(e,t){return void 0===t&&(t=!1),{enter:`Toastify--animate Toastify__${e}-enter`,exit:`Toastify--animate Toastify__${e}-exit`,appendPosition:t}},X=A(Q("bounce",!0)),J=(A(Q("slide",!0)),A(Q("zoom")),A(Q("flip")),{position:"top-right",transition:X,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light"});function ee(t){let n={...J,...t};const r=t.stacked,[o,a]=(0,e.useState)(!0),s=(0,e.useRef)(null),{getToastToRender:i,isToastActive:l,count:c}=z(n),{className:u,style:d,rtl:h,containerId:p}=n;function m(e){const t=k("Toastify__toast-container",`Toastify__toast-container--${e}`,{"Toastify__toast-container--rtl":h});return C(u)?u({position:e,rtl:h,defaultClassName:t}):k(t,_(u))}function f(){r&&(a(!0),Z.play())}return G((()=>{if(r){var e;const t=s.current.querySelectorAll('[data-in="true"]'),r=12,a=null==(e=n.position)?void 0:e.includes("top");let i=0,l=0;Array.from(t).reverse().forEach(((e,t)=>{const n=e;n.classList.add("Toastify__toast--stacked"),t>0&&(n.dataset.collapsed=`${o}`),n.dataset.pos||(n.dataset.pos=a?"top":"bot");const s=i*(o?.2:1)+(o?0:r*t);n.style.setProperty("--y",`${a?s:-1*s}px`),n.style.setProperty("--g",`${r}`),n.style.setProperty("--s",""+(1-(o?l:0))),i+=n.offsetHeight,l+=.025}))}}),[o,c,r]),e.createElement("div",{ref:s,className:"Toastify",id:p,onMouseEnter:()=>{r&&(a(!1),Z.pause())},onMouseLeave:f},i(((t,n)=>{const o=n.length?{...d}:{...d,pointerEvents:"none"};return e.createElement("div",{className:m(t),style:o,key:`container-${t}`},n.map((t=>{let{content:n,props:o}=t;return e.createElement(K,{...o,stacked:r,collapseAll:f,isIn:l(o.toastId,o.containerId),style:o.style,key:`toast-${o.key}`},n)})))})))}const te=window.wp.apiFetch;var ne=n.n(te);const re=class{constructor(){this.config=window?.wpdeveloperLicenseManagerConfig}post=async(e,t={})=>await this.request({endpoint:e,method:"POST",...t});delete=async(e,t={})=>await this.request({endpoint:e,method:"DELETE",...t});get=async(e,t={})=>await this.request({endpoint:e,method:"GET",...t});getPath=(e,t="GET")=>"ajax"===this.config.apiType?`${this.config?.api_url}?action=${this?.config?.action}/${e}`:`${this.config?.api_url}${e}`;request=async({endpoint:e,...t})=>{if(t.url=this.getPath(e,t?.method),"ajax"===this.config.apiType&&"GET"!==t?.method){let r=null!=t?.data?{...t?.data}:{};r={...r,action:`${this?.config?.action}/${e}`,_nonce:this?.config?.nonce},t.headers={};const o=new FormData;for(var n in r)o.append(n,r[n]);t.body=o,delete t.data}return await ne()(t).catch((e=>console.error("Licensing Error: ",e)))};useQuery=e=>new URLSearchParams(e);getParam=(e,t=null)=>this.useQuery(location?.search).get(e)||t};var oe=n(884);const ae=oe.default||oe,se={position:"bottom-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,progress:void 0,icon:!1},ie=(e,t,n)=>(e=e||null,t=t||null,new Promise(((r,o)=>{n=n||{};const a={...se,...n,onClose:r};"success"==e&&Z.success(t,a),"error"==e&&Z.error(t,a),"info"==e&&Z.info(t,a),"warning"==e&&Z.warning(t,a),e||o()}))),le=window.wp.element,ce=({email:t,submitOTP:n,resendOTP:o,isRequestSending:a,isSendingResendRequest:s})=>{const[i,l]=(0,le.useState)("");return(0,e.createElement)("div",{className:"wpdeveloper-verification-msg"},(0,e.createElement)("p",null,(0,r.__)("License Verification code has been sent to this ","betterdocs-ai-chatbot")," ",(0,e.createElement)("strong",null,t),(0,r.__)(". Please check your email for the code & insert it below","betterdocs-ai-chatbot")),(0,e.createElement)("div",{className:"wpdeveloper-verification-input-container"},(0,e.createElement)("div",{className:"wpdeveloper-verification-input"},(0,e.createElement)("input",{type:"text",value:i,onChange:e=>l(e.target.value),placeholder:(0,r.__)("Enter Your Verification Code","betterdocs-ai-chatbot")}),(0,e.createElement)("button",{type:"button",disabled:0===i.length,className:0===i.length?"disabled":"verify",onClick:()=>n(i)},a?(0,r.__)("Verifying...","betterdocs-ai-chatbot"):(0,r.__)("Verify","betterdocs-ai-chatbot"))),(0,e.createElement)("div",{className:"wpdeveloper-verification-note"},(0,e.createElement)("p",null,(0,r.__)("Haven’t received an email? Retry clicking on ")," ",(0,e.createElement)("a",{onClick:o,style:{cursor:"pointer",margin:0}},s?(0,r.__)("Resending...","betterdocs-ai-chatbot"):(0,r.__)("Resend","betterdocs-ai-chatbot")),(0,r.__)(" button. Please note that this verification code will expire after 15 minutes.","betterdocs-ai-chatbot"),(0,e.createElement)("a",{target:"_blank",href:"https://betterdocs.co/docs/veirfy-authenticate-betterdocs-pro-license-key/",className:"info-btn"},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 26 26",fill:"none"},(0,e.createElement)("circle",{cx:"12.5137",cy:"13.4902",r:"8.51367",fill:"#12B76A"}),(0,e.createElement)("circle",{cx:"12.5137",cy:"13.4902",r:"10.5137",stroke:"#12B76A",strokeOpacity:"0.2",strokeWidth:"4"}),(0,e.createElement)("mask",{id:"mask0_2664_21361",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"7",y:"8",width:"11",height:"11"},(0,e.createElement)("rect",{x:"7.40625",y:"8.38281",width:"10.2164",height:"10.2164",fill:"#D9D9D9"})),(0,e.createElement)("g",{mask:"url(#mask0_2664_21361)"},(0,e.createElement)("path",{d:"M12.5115 11.359C12.2774 11.359 12.077 11.2756 11.9102 11.1089C11.7435 10.9422 11.6602 10.7417 11.6602 10.5076C11.6602 10.2735 11.7435 10.0731 11.9102 9.90634C12.077 9.73961 12.2774 9.65625 12.5115 9.65625C12.7456 9.65625 12.9461 9.73961 13.1128 9.90634C13.2795 10.0731 13.3629 10.2735 13.3629 10.5076C13.3629 10.7417 13.2795 10.9422 13.1128 11.1089C12.9461 11.2756 12.7456 11.359 12.5115 11.359ZM11.873 17.3186V12.2104H13.15V17.3186H11.873Z",fill:"white"}))))))))},ue=({licenseData:t={},setIsLicenseActive:n,isActive:o,className:a=""})=>{const[s,i]=(0,e.useState)(!1),[l,c]=(0,e.useState)(t?.license_key||""),[u,d]=(0,e.useState)(t?.license_status||""),[h,p]=(0,e.useState)(null),[m,f]=(0,e.useState)(!1),[g,w]=(0,e.useState)(!1),[b,v]=(0,e.useState)(""),[y,k]=(0,e.useState)(""),[E,x]=(0,e.useState)(""),[C,_]=(0,e.useState)(!1);(0,e.useEffect)((()=>{(t?.license_key||o)&&c(t?.license_key||""),t?.license_status&&d(t.license_status)}),[t,o]),(0,e.useEffect)((()=>{t&&0!==Object.keys(t).length||(async()=>ne()({path:"betterdocs-chatbot/v1/license/get-license",method:"POST",data:{}}).then((e=>e)))().then((e=>{d(e?.status||""),c(e?.key||"")}))}),[t]);return(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:`wpdeveloper-licensing-form ${o?"activated":"deactivated"} ${"pending"===C?"pending":""}`},(0,e.createElement)("label",{className:`${a} wpdeveloper-licensing-form-inner`},(0,e.createElement)("div",{className:"betterdocs-license-icon"},(0,e.createElement)("i",{className:"btd-icon btd-key"})),l&&"valid"==u?(0,e.createElement)("input",{id:"wp-betterdocs-pro-license-key",className:"wpdeveloper-licensing-form-input activated",placeholder:"Place Your License Key and Activate",onChange:e=>{c(e.target.value),i(!0)},value:l,disabled:!0}):(0,e.createElement)("input",{className:"wpdeveloper-licensing-form-input",placeholder:"Place Your License Key and Activate",onChange:e=>{c(e.target.value),i(!0),_(!1)},value:l||""}),"true"==u||"valid"==u||o?(0,e.createElement)("button",{id:"submit",type:"button",className:s?"wpdeveloper-licensing-form-button activated changed":"wpdeveloper-licensing-form-button activated",onClick:()=>(p(!0),void(async()=>ne()({path:"betterdocs-chatbot/v1/license/deactivate",method:"POST"}).then((e=>e)))().then((e=>{if(p(null),i(!1),!0===e.success)d(e?.license),n(!1),_(!1),c(""),ie("success",(0,r.__)("Your License is Successfully Deactivated.","betterdocs-ai-chatbot"));else{let t=e.data;ie("error",(0,r.__)(t,"betterdocs-ai-chatbot"))}})).catch((e=>{p(null),ie("error",(0,r.__)(e?.message,"betterdocs-ai-chatbot"))})))},1==h?"Request Sending...":"Deactivate License"):(0,e.createElement)("button",{id:"submit",type:"button",className:s?"wpdeveloper-licensing-form-button deactivated changed":"wpdeveloper-licensing-form-button disabled",onClick:()=>(()=>{p(!0),_("true");let e={license_key:l};p(null),i(!1),(async e=>ne()({path:"betterdocs-chatbot/v1/license/activate",method:"POST",data:e}).then((e=>e)))(e).then((e=>{if(p(null),i(!1),!0===e.success)"required_otp"===e.license?(v(e?.license),x(e?.customer_email),k(l),_("pending"),ie("info",(0,r.__)("License Verification Required.","betterdocs-ai-chatbot"))):(d(e?.license),n(!0),c(e?.license_key),ie("success",(0,r.__)("Your License is Successfully Activated.","betterdocs-ai-chatbot")));else{_(!1);let t=e.data;ie("error",(0,r.__)(t,"betterdocs-ai-chatbot"))}})).catch((e=>{_(!1),ie("error",(0,r.__)(e.message,"betterdocs-ai-chatbot"))}))})(),disabled:!l||"pending"===C},"true"===C?"Request Sending...":"pending"===C?"Verification Required":"Activate License"))),"required_otp"===b&&(0,e.createElement)(ce,{email:E,submitOTP:e=>{let t={otp:e,license:y,license_key:y};f(!0),(async e=>ne()({path:"betterdocs-chatbot/v1/license/submit-otp",method:"POST",data:e}).then((e=>e)))(t).then((e=>{f(!1),c(e?.license_key),v(e?.license),d(e?.license),n(!0),ie("success",(0,r.__)("Your License is Successfully Activated.","betterdocs-ai-chatbot"))})).catch((e=>{f(!1),ie("error",(0,r.__)(e?.message,"betterdocs-ai-chatbot"))}))},resendOTP:()=>{let e={license_key:y,license:y};w(!0),(async e=>ne()({path:"betterdocs-chatbot/v1/license/resend-otp",method:"POST",data:e}).then((e=>e)))(e).then((e=>{w(!1),v(e?.license),k(l),ie("success",(0,r.__)("Successfully Resent","betterdocs-ai-chatbot"))})).catch((e=>{w(!1),ie("error",(0,r.__)(e?.message,"betterdocs-ai-chatbot"))}))},isRequestSending:m,isSendingResendRequest:g}))},de=[(0,e.createElement)(e.Fragment,null,ae(sprintf((0,r.__)("Log in to <a href='%s' target='_blank'>your account</a> to get your BetterDocs AI Chatbot license key.","betterdocs-ai-chatbot"),"https://store.wpdeveloper.com/"))),(0,e.createElement)(e.Fragment,null,ae(sprintf((0,r.__)("If you do not have a license key, get the <a href='%s' target='_blank'>BetterDocs AI Chatbot</a> addon now.","betterdocs-ai-chatbot"),"https://betterdocs.co/ai-chatbot/"))),(0,e.createElement)(e.Fragment,null,(0,r.__)("Copy the license key from your account and paste it below.","betterdocs-ai-chatbot")),(0,e.createElement)(e.Fragment,null,(0,r.__)('Click on the "Activate License" button and then enter the verification code sent to your email.',"betterdocs-ai-chatbot"))],he=({apiFetch:t,licenseData:n,config:o,...a})=>{var s;const[i,l]=(0,e.useState)("valid"===n?.license_status),[c,u]=(0,e.useState)(!1);return(0,e.useEffect)((()=>{"valid"===n?.license_status&&l(!0)}),[n]),(0,e.createElement)("div",{className:`wpdeveloper-licensing-wrapper ${null!==(s=a?.className)&&void 0!==s?s:""}`},(0,e.createElement)("div",{className:"wpdeveloper-licensing-header"},(0,e.createElement)("div",{className:"wpdeveloper-licensing-left-container"},(0,e.createElement)("div",{className:"icon "+(i?"activated":"deactivated")},(0,e.createElement)("i",{className:"btd-icon btd-"+(i?"lock-open":"lock")})),(0,e.createElement)("div",{className:"content"},(0,e.createElement)("h3",{className:"heading"},i?(0,r.__)("You Have Activated BetterDocs AI Chatbot","betterdocs-ai-chatbot"):(0,r.__)("Unlock BetterDocs AI Chatbot With License Key","betterdocs-ai-chatbot")),(0,e.createElement)("p",{className:"description"},i?(0,r.__)("Congratulations! Enjoy BetterDocs AI Chatbot features, and get automatic updates & priority support!","betterdocs-ai-chatbot"):(0,r.__)("Enter your BetterDocs Chatbot license key in the input field below to activate and use BetterDocs AI Chatbot.","betterdocs-ai-chatbot")))),(0,e.createElement)("div",{className:"wpdeveloper-licensing-right-container"},i?(0,e.createElement)("p",{className:"active-badge"},(0,e.createElement)("i",{className:"btd-icon btd-tick"}),(0,r.__)("Activated","betterdocs-ai-chatbot")):(0,e.createElement)("p",{className:"step-button "+(c?"show":"hide"),onClick:()=>u(!c)},(0,e.createElement)("span",{className:"text"},(0,r.__)("How to get license key?","betterdocs-ai-chatbot")),(0,e.createElement)("span",{className:"icon"},(0,e.createElement)("i",{className:"btd-icon btd-arrow-up"}))))),(0,e.createElement)("div",{className:`wpdeveloper-licensing-steps-wrapper ${c?"show":"hide"} ${i?"hidden":""}`},(0,e.createElement)("ul",{className:"wpdeveloper-licensing-steps"},de?.map(((t,n)=>(0,e.createElement)("li",{key:n,className:"wpdeveloper-licensing-step"},(0,e.createElement)("span",{className:"wpdeveloper-licensing-step-count"},++n),(0,e.createElement)("span",{className:"wpdeveloper-licensing-step-content"},t)))))),(0,e.createElement)(ue,{licenseData:n,setIsLicenseActive:l,isActive:i}))};class pe{constructor(e){var t;this.config={...window?.wpdeveloperLicenseManagerConfig,...e},this.licenseData=null!==(t=window?.wpdeveloperChatbotLicenseData)&&void 0!==t?t:{},this.api=new re}render=()=>{var t;return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(ee,{position:"bottom-right",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0,progress:void 0,theme:"colored",limit:4}),(0,e.createElement)(he,{className:null!==(t=this.config?.classes?.wrapper)&&void 0!==t?t:"",licenseData:this.licenseData,config:this.config,apiFetch:this.api}))}}var me="undefined"==typeof window||"Deno"in globalThis;function fe(){}function ge(e,t){return"function"==typeof e?e(t):e}function we(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:a,queryKey:s,stale:i}=e;if(s)if(r){if(t.queryHash!==ve(s,t.options))return!1}else if(!ke(t.queryKey,s))return!1;if("all"!==n){const e=t.isActive();if("active"===n&&!e)return!1;if("inactive"===n&&e)return!1}return!("boolean"==typeof i&&t.isStale()!==i||o&&o!==t.state.fetchStatus||a&&!a(t))}function be(e,t){const{exact:n,status:r,predicate:o,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(n){if(ye(t.options.mutationKey)!==ye(a))return!1}else if(!ke(t.options.mutationKey,a))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function ve(e,t){return(t?.queryKeyHashFn||ye)(e)}function ye(e){return JSON.stringify(e,((e,t)=>Ce(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t))}function ke(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((n=>ke(e[n],t[n])))}function Ee(e,t){if(e===t)return e;const n=xe(e)&&xe(t);if(n||Ce(e)&&Ce(t)){const r=n?e:Object.keys(e),o=r.length,a=n?t:Object.keys(t),s=a.length,i=n?[]:{};let l=0;for(let o=0;o<s;o++){const s=n?o:a[o];(!n&&r.includes(s)||n)&&void 0===e[s]&&void 0===t[s]?(i[s]=void 0,l++):(i[s]=Ee(e[s],t[s]),i[s]===e[s]&&void 0!==e[s]&&l++)}return o===s&&l===o?e:i}return t}function xe(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Ce(e){if(!_e(e))return!1;const t=e.constructor;if(void 0===t)return!0;const n=t.prototype;return!!_e(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function _e(e){return"[object Object]"===Object.prototype.toString.call(e)}function Te(e,t,n){return"function"==typeof n.structuralSharing?n.structuralSharing(e,t):!1!==n.structuralSharing?Ee(e,t):t}function Ae(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function Se(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Ne=Symbol();function Le(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==Ne?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}var Oe=e=>setTimeout(e,0),Re=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},o=Oe;const a=r=>{t?e.push(r):o((()=>{n(r)}))};return{batch:a=>{let s;t++;try{s=a()}finally{t--,t||(()=>{const t=e;e=[],t.length&&o((()=>{r((()=>{t.forEach((e=>{n(e)}))}))}))})()}return s},batchCalls:e=>(...t)=>{a((()=>{e(...t)}))},schedule:a,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{o=e}}}(),Me=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Pe=new class extends Me{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!me&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},Ie=new class extends Me{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!me&&window.addEventListener){const t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#r}};function De(e){return Math.min(1e3*2**e,3e4)}function ze(e){return"online"!==(e??"online")||Ie.isOnline()}var $e=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function Be(e){return e instanceof $e}function He(e){let t,n=!1,r=0,o=!1;const a=function(){let e,t;const n=new Promise(((n,r)=>{e=n,t=r}));function r(e){Object.assign(n,e),delete n.resolve,delete n.reject}return n.status="pending",n.catch((()=>{})),n.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},n.reject=e=>{r({status:"rejected",reason:e}),t(e)},n}(),s=()=>Pe.isFocused()&&("always"===e.networkMode||Ie.isOnline())&&e.canRun(),i=()=>ze(e.networkMode)&&e.canRun(),l=n=>{o||(o=!0,e.onSuccess?.(n),t?.(),a.resolve(n))},c=n=>{o||(o=!0,e.onError?.(n),t?.(),a.reject(n))},u=()=>new Promise((n=>{t=e=>{(o||s())&&n(e)},e.onPause?.()})).then((()=>{t=void 0,o||e.onContinue?.()})),d=()=>{if(o)return;let t;const a=0===r?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(l).catch((t=>{if(o)return;const a=e.retry??(me?0:3),i=e.retryDelay??De,l="function"==typeof i?i(r,t):i,h=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,t);var p;!n&&h?(r++,e.onFail?.(r,t),(p=l,new Promise((e=>{setTimeout(e,p)}))).then((()=>s()?void 0:u())).then((()=>{n?c(t):d()}))):c(t)}))};return{promise:a,cancel:t=>{o||(c(new $e(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:i,start:()=>(i()?d():u().then(d),a)}}var qe=class{#o;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#o=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(me?1/0:3e5))}clearGcTimeout(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}},Fe=class extends qe{#a;#s;#i;#l;#c;#u;#d;constructor(e){super(),this.#d=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#i=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#a=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#a,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#i.remove(this)}setData(e,t){const n=Te(this.state.data,e,this.options);return this.#h({data:n,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),n}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(fe).catch(fe):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#a)}isActive(){return this.observers.some((e=>{return!1!==("function"==typeof(t=e.options.enabled)?t(this):t);var t}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Ne||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!function(e,t){return Math.max(e+(t||0)-Date.now(),0)}(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#c&&(this.#d?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#i.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const n=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,n.signal)})},o={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{const e=Le(this.options,t),n={client:this.#l,queryKey:this.queryKey,meta:this.meta};return r(n),this.#d=!1,this.options.persister?this.options.persister(e,n,this):e(n)}};r(o),this.options.behavior?.onFetch(o,this),this.#s=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===o.fetchOptions?.meta||this.#h({type:"fetch",meta:o.fetchOptions?.meta});const a=e=>{Be(e)&&e.silent||this.#h({type:"error",error:e}),Be(e)||(this.#i.config.onError?.(e,this),this.#i.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=He({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:n.abort.bind(n),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void a(e)}this.#i.config.onSuccess?.(e,this),this.#i.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else a(new Error(`${this.queryHash} data is undefined`))},onError:a,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#c.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...(n=t.data,r=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:ze(r.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=e.error;return Be(o)&&o.revert&&this.#s?{...this.#s,fetchStatus:"idle"}:{...t,error:o,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}var n,r})(this.state),Re.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#i.notify({query:this,type:"updated",action:e})}))}},je=class extends Me{constructor(e={}){super(),this.config=e,this.#p=new Map}#p;build(e,t,n){const r=t.queryKey,o=t.queryHash??ve(r,t);let a=this.get(o);return a||(a=new Fe({client:e,queryKey:r,queryHash:o,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(a)),a}add(e){this.#p.has(e.queryHash)||(this.#p.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#p.get(e.queryHash);t&&(e.destroy(),t===e&&this.#p.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){Re.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#p.get(e)}getAll(){return[...this.#p.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>we(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>we(e,t))):t}notify(e){Re.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){Re.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){Re.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},Ue=class extends qe{#m;#f;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#f=e.mutationCache,this.#m=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#m.includes(e)||(this.#m.push(e),this.clearGcTimeout(),this.#f.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#m=this.#m.filter((t=>t!==e)),this.scheduleGc(),this.#f.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#m.length||("pending"===this.state.status?this.scheduleGc():this.#f.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#c=He({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#f.canRun(this)});const n="pending"===this.state.status,r=!this.#c.canStart();try{if(n)t();else{this.#h({type:"pending",variables:e,isPaused:r}),await(this.#f.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:r})}const o=await this.#c.start();return await(this.#f.config.onSuccess?.(o,e,this.state.context,this)),await(this.options.onSuccess?.(o,e,this.state.context)),await(this.#f.config.onSettled?.(o,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(o,null,e,this.state.context)),this.#h({type:"success",data:o}),o}catch(t){try{throw await(this.#f.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#f.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#f.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),Re.batch((()=>{this.#m.forEach((t=>{t.onMutationUpdate(e)})),this.#f.notify({mutation:this,type:"updated",action:e})}))}},Ve=class extends Me{constructor(e={}){super(),this.config=e,this.#g=new Set,this.#w=new Map,this.#b=0}#g;#w;#b;build(e,t,n){const r=new Ue({mutationCache:this,mutationId:++this.#b,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){this.#g.add(e);const t=Ze(e);if("string"==typeof t){const n=this.#w.get(t);n?n.push(e):this.#w.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#g.delete(e)){const t=Ze(e);if("string"==typeof t){const n=this.#w.get(t);if(n)if(n.length>1){const t=n.indexOf(e);-1!==t&&n.splice(t,1)}else n[0]===e&&this.#w.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=Ze(e);if("string"==typeof t){const n=this.#w.get(t),r=n?.find((e=>"pending"===e.state.status));return!r||r===e}return!0}runNext(e){const t=Ze(e);if("string"==typeof t){const n=this.#w.get(t)?.find((t=>t!==e&&t.state.isPaused));return n?.continue()??Promise.resolve()}return Promise.resolve()}clear(){Re.batch((()=>{this.#g.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#g.clear(),this.#w.clear()}))}getAll(){return Array.from(this.#g)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>be(t,e)))}findAll(e={}){return this.getAll().filter((t=>be(e,t)))}notify(e){Re.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return Re.batch((()=>Promise.all(e.map((e=>e.continue().catch(fe))))))}};function Ze(e){return e.options.scope?.id}function Ge(e){return{onFetch:(t,n)=>{const r=t.options,o=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],s=t.state.data?.pageParams||[];let i={pages:[],pageParams:[]},l=0;const c=async()=>{let n=!1;const c=Le(t.options,t.fetchOptions),u=async(e,r,o)=>{if(n)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const a={client:t.client,queryKey:t.queryKey,pageParam:r,direction:o?"backward":"forward",meta:t.options.meta};var s;s=a,Object.defineProperty(s,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",(()=>{n=!0})),t.signal)});const i=await c(a),{maxPages:l}=t.options,u=o?Se:Ae;return{pages:u(e.pages,i,l),pageParams:u(e.pageParams,r,l)}};if(o&&a.length){const e="backward"===o,t={pages:a,pageParams:s},n=(e?Ye:We)(r,t);i=await u(t,n,e)}else{const t=e??a.length;do{const e=0===l?s[0]??r.initialPageParam:We(r,i);if(l>0&&null==e)break;i=await u(i,e),l++}while(l<t)}return i};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=c}}}function We(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function Ye(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}new class{#v;#f;#u;#y;#k;#E;#x;#C;constructor(e={}){this.#v=e.queryCache||new je,this.#f=e.mutationCache||new Ve,this.#u=e.defaultOptions||{},this.#y=new Map,this.#k=new Map,this.#E=0}mount(){this.#E++,1===this.#E&&(this.#x=Pe.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#v.onFocus())})),this.#C=Ie.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#v.onOnline())})))}unmount(){this.#E--,0===this.#E&&(this.#x?.(),this.#x=void 0,this.#C?.(),this.#C=void 0)}isFetching(e){return this.#v.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#f.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#v.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=this.#v.build(this,t),r=n.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(ge(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#v.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=this.#v.get(r.queryHash),a=o?.state.data,s=function(e,t){return"function"==typeof e?e(t):e}(t,a);if(void 0!==s)return this.#v.build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Re.batch((()=>this.#v.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,n)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#v.get(t.queryHash)?.state}removeQueries(e){const t=this.#v;Re.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const n=this.#v;return Re.batch((()=>(n.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const n={revert:!0,...t},r=Re.batch((()=>this.#v.findAll(e).map((e=>e.cancel(n)))));return Promise.all(r).then(fe).catch(fe)}invalidateQueries(e,t={}){return Re.batch((()=>(this.#v.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=Re.batch((()=>this.#v.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(fe)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(r).then(fe)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const n=this.#v.build(this,t);return n.isStaleByTime(ge(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(fe).catch(fe)}fetchInfiniteQuery(e){return e.behavior=Ge(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(fe).catch(fe)}ensureInfiniteQueryData(e){return e.behavior=Ge(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ie.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#v}getMutationCache(){return this.#f}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#y.set(ye(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#y.values()],n={};return t.forEach((t=>{ke(e,t.queryKey)&&Object.assign(n,t.defaultOptions)})),n}setMutationDefaults(e,t){this.#k.set(ye(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#k.values()],n={};return t.forEach((t=>{ke(e,t.mutationKey)&&Object.assign(n,t.defaultOptions)})),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ve(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Ne&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#v.clear(),this.#f.clear()}},n(749);var Ke=n(270),Qe=n.n(Ke);(0,o.addFilter)("betterdocs_chatbot_licnese","BetterDocs",(function(){const t=new pe({logo:betterdocsAdminSettings?.logoURL});return(0,e.createElement)("div",null,t.render())}));const Xe=({Component:t,fallback:n})=>(0,e.createElement)(e.Suspense,{fallback:null!=n?n:"Loading..."},(0,e.createElement)(t,null));(0,o.addFilter)("betterdocs_routes","betterdocs/ai-chatbot",(t=>(t.push({path:"betterdocs-ai-chatbot",element:(0,e.createElement)(Xe,{Component:v}),exact:!0}),t))),(0,o.addFilter)("betterdocs_admin_header_title","betterdocsChatbot",((e,t)=>"betterdocs-ai-chatbot"===t?(0,r.__)("AI Chatbot Conversation History","betterdocs-ai-chatbot"):e)),(0,o.addFilter)("quickBuilder_fieldValue","betterdocsChatbot",(function(e,t){return("enable_ai_chatbot"!==t||!e||"valid"===betterdocs.PRO_LICENSE&&"valid"===betterdocs.CHATBOT_LICENSE)&&e})),(0,o.addAction)("quickBuilder_setFieldValue","betterdocsChatbot",(function(e,t,n){"enable_ai_chatbot"!==e||!t||"valid"===betterdocs.PRO_LICENSE&&"valid"===betterdocs.CHATBOT_LICENSE||((e=null)=>(null===e&&(e=sprintf(`<div class="icon">\n                <i class="btd-icon btd-crown"></i>\n            </div>\n            <h3 class="title">${(0,r.__)("Activate BetterDocs PRO & BetterDocs AI Chatbot Addon With Your License Key","betterdocs-ai-chatbot")}</h3>\n            <p class="description">${(0,r.__)("To activate the BetterDocs PRO and BetterDocs AI Chatbot Addon, go to the <strong>License Tab</strong> and unlock with your license keys","betterdocs-ai-chatbot")}</p>\n            \n            <div class="footer">\n                <a href='%s'>${(0,r.__)("Activate License Key","betterdocs-ai-chatbot")}</a>\n            </div>`,"/wp-admin/admin.php?page=betterdocs-settings&tab=tab-license")),((e={})=>{var t,n,o,a,s;return Qe().mixin({target:null!==(t=e?.target)&&void 0!==t?t:"#betterdocsQuickBuilder",html:e?.html,title:null!==(n=e?.title)&&void 0!==n?n:(0,r.__)("Title Goes Here: title","betterdocs-ai-chatbot"),text:null!==(o=e?.text)&&void 0!==o?o:(0,r.__)("Text Goes Here: text","betterdocs-ai-chatbot"),icon:null!==(a=e?.icon)&&void 0!==a?a:e?.type||"success",timer:null!==(s=e?.timer)&&void 0!==s?s:null,...e})})({showConfirmButton:!1,showDenyButton:!1,icon:!1,title:!1,showCloseButton:!0,closeButtonHtml:'<svg class="betterdocs-close-button" xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">\n                            <path d="M1.69687 14L0.296875 12.6L5.89687 7L0.296875 1.4L1.69687 0L7.29688 5.6L12.8969 0L14.2969 1.4L8.69688 7L14.2969 12.6L12.8969 14L7.29688 8.4L1.69687 14Z" fill="#475467"/>\n                          </svg>',customClass:{container:"betterdocs-pro-alert-container",popup:"betterdocs-pro-alert-popup",closeButton:"betterdocs-pro-alert-close-button",htmlContainer:"betterdocs-pro-alert-html"},html:e})))().fire()}))})()})();