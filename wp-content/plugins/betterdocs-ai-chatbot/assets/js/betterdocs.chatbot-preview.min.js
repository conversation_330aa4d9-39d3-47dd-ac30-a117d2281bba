(()=>{"use strict";var e={57:(e,t,n)=>{n.d(t,{Bk:()=>c,Kk:()=>s,Ri:()=>a,TV:()=>o,VK:()=>i,o2:()=>l});var r=n(133);n(723);const s=e=>e?(r.xI.setOptions({gfm:!0,breaks:!0,headerIds:!1}),{__html:(0,r.xI)(e)}):"",i=()=>{const e=navigator.userAgent;let t="Unknown Browser",n="",r="";return e.includes("Edg/")?(t="Edge",n=e.match(/Edg\/(\d+)/)?.[1]||""):e.includes("Chrome/")&&e.includes("Safari/")?(t="Chrome",n=e.match(/Chrome\/(\d+)/)?.[1]||""):e.includes("Safari/")&&!e.includes("Chrome/")?(t="Safari",n=e.match(/Version\/(\d+)/)?.[1]||""):e.includes("Firefox/")?(t="Mozila Firefox",n=e.match(/Firefox\/(\d+)/)?.[1]||""):(e.includes("MSIE")||e.includes("Trident/"))&&(t="Internet Explorer",n=e.match(/(?:MSIE |rv:)(\d+)/)?.[1]||""),e.includes("Macintosh")?r="Mac OS":e.includes("Windows")?r="Windows":e.includes("Linux")?r="Linux":e.includes("Android")?r="Android":(e.includes("iPhone")||e.includes("iPad"))&&(r="iOS"),`${t} ${n} on ${r}`},o=(e,t,n={})=>{"number"==typeof n&&(n={expiresDays:n});const r={expiresDays:7,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax",...n};let s=`${e}=${encodeURIComponent(t)}`;if(r.expiresDays){const e=new Date;e.setTime(e.getTime()+24*r.expiresDays*60*60*1e3),s+=`; expires=${e.toUTCString()}`}s+=`; path=${r.path}`,r.secure&&(s+="; Secure"),r.sameSite&&(s+=`; SameSite=${r.sameSite}`),document.cookie=s},a=e=>{const t=document.cookie.split("; ");for(let n of t){const[t,r]=n.split("=");if(t===e)return decodeURIComponent(r)}return null},l=(()=>{const e={text:(e,t=1e3)=>"string"!=typeof e?"":e.replace(/[<>"'`;\\/]/g,"").slice(0,t),email:e=>{const t=e.toLowerCase().trim();return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?t:""},sessionId:e=>e.replace(/[^a-zA-Z0-9_-]/g,"").slice(0,64)};return{sanitize:(t,n)=>e[t]?e[t](n):""}})(),c=()=>{let e=a("chatbot_session_id");return e&&(/^session_[a-f0-9]{14}\.[a-f0-9]{8}$/.test(e)||/^session_[a-z0-9]{9,}\d+$/.test(e))||(e=h(),o("chatbot_session_id",e,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"})),e},h=()=>{try{const e=Date.now()/1e3,t=Math.floor(e%1*1e6);return`session_${Math.floor(e).toString(16)}${t}.${crypto.getRandomValues(new Uint8Array(4)).reduce(((e,t)=>e+t.toString(16).padStart(2,"0")),"")}`}catch(e){return`session_${Math.random().toString(36).substring(2,11)}${Date.now()}`}}},133:(e,t,n)=>{n.d(t,{xI:()=>le});let r={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function s(e){r=e}const i=/[&<>"']/,o=new RegExp(i.source,"g"),a=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,l=new RegExp(a.source,"g"),c={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},h=e=>c[e];function u(e,t){if(t){if(i.test(e))return e.replace(o,h)}else if(a.test(e))return e.replace(l,h);return e}const p=/(^|[^\[])\^/g;function m(e,t){let n="string"==typeof e?e:e.source;t=t||"";const r={replace:(e,t)=>{let s="string"==typeof t?t:t.source;return s=s.replace(p,"$1"),n=n.replace(e,s),r},getRegex:()=>new RegExp(n,t)};return r}function d(e){try{e=encodeURI(e).replace(/%25/g,"%")}catch{return null}return e}const f={exec:()=>null};function g(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let r=!1,s=t;for(;--s>=0&&"\\"===n[s];)r=!r;return r?"|":" |"})).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function k(e,t,n){const r=e.length;if(0===r)return"";let s=0;for(;s<r;){const i=e.charAt(r-s-1);if(i!==t||n){if(i===t||!n)break;s++}else s++}return e.slice(0,r-s)}function b(e,t,n,r){const s=t.href,i=t.title?u(t.title):null,o=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;const e={type:"link",raw:n,href:s,title:i,text:o,tokens:r.inlineTokens(o)};return r.state.inLink=!1,e}return{type:"image",raw:n,href:s,title:i,text:u(o)}}class x{options;rules;lexer;constructor(e){this.options=e||r}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^(?: {1,4}| {0,3}\t)/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:k(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const r=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=r.length?e.slice(r.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=k(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:k(t[0],"\n")}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let e=k(t[0],"\n").split("\n"),n="",r="";const s=[];for(;e.length>0;){let t=!1;const i=[];let o;for(o=0;o<e.length;o++)if(/^ {0,3}>/.test(e[o]))i.push(e[o]),t=!0;else{if(t)break;i.push(e[o])}e=e.slice(o);const a=i.join("\n"),l=a.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,"\n    $1").replace(/^ {0,3}>[ \t]?/gm,"");n=n?`${n}\n${a}`:a,r=r?`${r}\n${l}`:l;const c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(l,s,!0),this.lexer.state.top=c,0===e.length)break;const h=s[s.length-1];if("code"===h?.type)break;if("blockquote"===h?.type){const t=h,i=t.raw+"\n"+e.join("\n"),o=this.blockquote(i);s[s.length-1]=o,n=n.substring(0,n.length-t.raw.length)+o.raw,r=r.substring(0,r.length-t.text.length)+o.text;break}if("list"!==h?.type);else{const t=h,i=t.raw+"\n"+e.join("\n"),o=this.list(i);s[s.length-1]=o,n=n.substring(0,n.length-h.raw.length)+o.raw,r=r.substring(0,r.length-t.raw.length)+o.raw,e=i.substring(s[s.length-1].raw.length).split("\n")}}return{type:"blockquote",raw:n,tokens:s,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const i=new RegExp(`^( {0,3}${n})((?:[\t ][^\\n]*)?(?:\\n|$))`);let o=!1;for(;e;){let n=!1,r="",a="";if(!(t=i.exec(e)))break;if(this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let l=t[2].split("\n",1)[0].replace(/^\t+/,(e=>" ".repeat(3*e.length))),c=e.split("\n",1)[0],h=!l.trim(),u=0;if(this.options.pedantic?(u=2,a=l.trimStart()):h?u=t[1].length+1:(u=t[2].search(/[^ ]/),u=u>4?1:u,a=l.slice(u),u+=t[1].length),h&&/^[ \t]*$/.test(c)&&(r+=c+"\n",e=e.substring(c.length+1),n=!0),!n){const t=new RegExp(`^ {0,${Math.min(3,u-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),n=new RegExp(`^ {0,${Math.min(3,u-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),s=new RegExp(`^ {0,${Math.min(3,u-1)}}(?:\`\`\`|~~~)`),i=new RegExp(`^ {0,${Math.min(3,u-1)}}#`),o=new RegExp(`^ {0,${Math.min(3,u-1)}}<(?:[a-z].*>|!--)`,"i");for(;e;){const p=e.split("\n",1)[0];let m;if(c=p,this.options.pedantic?(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  "),m=c):m=c.replace(/\t/g,"    "),s.test(c))break;if(i.test(c))break;if(o.test(c))break;if(t.test(c))break;if(n.test(c))break;if(m.search(/[^ ]/)>=u||!c.trim())a+="\n"+m.slice(u);else{if(h)break;if(l.replace(/\t/g,"    ").search(/[^ ]/)>=4)break;if(s.test(l))break;if(i.test(l))break;if(n.test(l))break;a+="\n"+c}h||c.trim()||(h=!0),r+=p+"\n",e=e.substring(p.length+1),l=m.slice(u)}}s.loose||(o?s.loose=!0:/\n[ \t]*\n[ \t]*$/.test(r)&&(o=!0));let p,m=null;this.options.gfm&&(m=/^\[[ xX]\] /.exec(a),m&&(p="[ ] "!==m[0],a=a.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:r,task:!!m,checked:p,loose:!1,text:a,tokens:[]}),s.raw+=r}s.items[s.items.length-1].raw=s.items[s.items.length-1].raw.trimEnd(),s.items[s.items.length-1].text=s.items[s.items.length-1].text.trimEnd(),s.raw=s.raw.trimEnd();for(let e=0;e<s.items.length;e++)if(this.lexer.state.top=!1,s.items[e].tokens=this.lexer.blockTokens(s.items[e].text,[]),!s.loose){const t=s.items[e].tokens.filter((e=>"space"===e.type)),n=t.length>0&&t.some((e=>/\n.*\n/.test(e.raw)));s.loose=n}if(s.loose)for(let e=0;e<s.items.length;e++)s.items[e].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(!t)return;if(!/[:|]/.test(t[2]))return;const n=g(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[],i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const e of r)/^ *-+: *$/.test(e)?i.align.push("right"):/^ *:-+: *$/.test(e)?i.align.push("center"):/^ *:-+ *$/.test(e)?i.align.push("left"):i.align.push(null);for(let e=0;e<n.length;e++)i.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:i.align[e]});for(const e of s)i.rows.push(g(e,i.header.length).map(((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:i.align[t]}))));return i}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:u(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=k(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),b(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const e=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return b(n,e,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&(!r[3]||!n.match(/[\p{L}\p{N}]/u))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){const n=[...r[0]].length-1;let s,i,o=n,a=0;const l="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=l.exec(t));){if(s=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!s)continue;if(i=[...s].length,r[3]||r[4]){o+=i;continue}if((r[5]||r[6])&&n%3&&!((n+i)%3)){a+=i;continue}if(o-=i,o>0)continue;i=Math.min(i,i+o+a);const t=[...r[0]][0].length,l=e.slice(0,n+r.index+t+i);if(Math.min(n,i)%2){const e=l.slice(1,-1);return{type:"em",raw:l,text:e,tokens:this.lexer.inlineTokens(e)}}const c=l.slice(2,-2);return{type:"strong",raw:l,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),r=/^ /.test(e)&&/ $/.test(e);return n&&r&&(e=e.substring(1,e.length-1)),e=u(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=u(t[1]),n="mailto:"+e):(e=u(t[1]),n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=u(t[0]),n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=u(t[0]),n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let e;return e=this.lexer.state.inRawBlock?t[0]:u(t[0]),{type:"text",raw:t[0],text:e}}}}const w=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,v=/(?:[*+-]|\d{1,9}[.)])/,y=m(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,v).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),E=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,T=/(?!\s*\])(?:\\.|[^\[\]\\])+/,_=m(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",T).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),z=m(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,v).getRegex(),S="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",A=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,N=m("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",A).replace("tag",S).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),R=m(E).replace("hr",w).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex(),I={blockquote:m(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",R).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:_,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:w,html:N,lheading:y,list:z,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:R,table:f,text:/^[^\n]+/},M=m("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",w).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex(),C={...I,table:M,paragraph:m(E).replace("hr",w).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",M).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",S).getRegex()},$={...I,html:m("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",A).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:f,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:m(E).replace("hr",w).replace("heading"," *#{1,6} *[^\n]").replace("lheading",y).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},L=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,D=/^( {2,}|\\)\n(?!\s*$)/,O="\\p{P}\\p{S}",P=m(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,O).getRegex(),H=m(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,O).getRegex(),U=m("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,O).getRegex(),q=m("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,O).getRegex(),B=m(/\\([punct])/,"gu").replace(/punct/g,O).getRegex(),F=m(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),j=m(A).replace("(?:--\x3e|$)","--\x3e").getRegex(),W=m("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",j).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Z=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,V=m(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Z).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),G=m(/^!?\[(label)\]\[(ref)\]/).replace("label",Z).replace("ref",T).getRegex(),Y=m(/^!?\[(ref)\](?:\[\])?/).replace("ref",T).getRegex(),Q={_backpedal:f,anyPunctuation:B,autolink:F,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:D,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:f,emStrongLDelim:H,emStrongRDelimAst:U,emStrongRDelimUnd:q,escape:L,link:V,nolink:Y,punctuation:P,reflink:G,reflinkSearch:m("reflink|nolink(?!\\()","g").replace("reflink",G).replace("nolink",Y).getRegex(),tag:W,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:f},X={...Q,link:m(/^!?\[(label)\]\((.*?)\)/).replace("label",Z).getRegex(),reflink:m(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Z).getRegex()},K={...Q,escape:m(L).replace("])","~|])").getRegex(),url:m(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},J={...K,br:m(D).replace("{2,}","*").getRegex(),text:m(K.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},ee={normal:I,gfm:C,pedantic:$},te={normal:Q,gfm:K,breaks:J,pedantic:X};class ne{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||r,this.options.tokenizer=this.options.tokenizer||new x,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:ee.normal,inline:te.normal};this.options.pedantic?(t.block=ee.pedantic,t.inline=te.pedantic):this.options.gfm&&(t.block=ee.gfm,this.options.breaks?t.inline=te.breaks:t.inline=te.gfm),this.tokenizer.rules=t}static get rules(){return{block:ee,inline:te}}static lex(e,t){return new ne(t).lex(e)}static lexInline(e,t){return new ne(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){let r,s,i;for(this.options.pedantic&&(e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))))if(r=this.tokenizer.space(e))e=e.substring(r.raw.length),1===r.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(r);else if(r=this.tokenizer.code(e))e=e.substring(r.raw.length),s=t[t.length-1],!s||"paragraph"!==s.type&&"text"!==s.type?t.push(r):(s.raw+="\n"+r.raw,s.text+="\n"+r.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.fences(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.heading(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.hr(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.blockquote(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.list(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.html(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.def(e))e=e.substring(r.raw.length),s=t[t.length-1],!s||"paragraph"!==s.type&&"text"!==s.type?this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title}):(s.raw+="\n"+r.raw,s.text+="\n"+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(r=this.tokenizer.table(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.lheading(e))e=e.substring(r.raw.length),t.push(r);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startBlock.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(i=e.substring(0,t+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i)))s=t[t.length-1],n&&"paragraph"===s?.type?(s.raw+="\n"+r.raw,s.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);else if(r=this.tokenizer.text(e))e=e.substring(r.raw.length),s=t[t.length-1],s&&"text"===s.type?(s.raw+="\n"+r.raw,s.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(r);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,s,i,o,a,l=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(l));)e.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(l));)l=l.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(i=this.tokenizer.rules.inline.anyPunctuation.exec(l));)l=l.slice(0,i.index)+"++"+l.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(o||(a=""),o=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((r=>!!(n=r.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,l,a))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startInline.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(s=e.substring(0,t+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(a=n.raw.slice(-1)),o=!0,r=t[t.length-1],r&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}else e=e.substring(n.raw.length),t.push(n);return t}}class re{options;parser;constructor(e){this.options=e||r}space(e){return""}code({text:e,lang:t,escaped:n}){const r=(t||"").match(/^\S*/)?.[0],s=e.replace(/\n$/,"")+"\n";return r?'<pre><code class="language-'+u(r)+'">'+(n?s:u(s,!0))+"</code></pre>\n":"<pre><code>"+(n?s:u(s,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){const t=e.ordered,n=e.start;let r="";for(let t=0;t<e.items.length;t++){const n=e.items[t];r+=this.listitem(n)}const s=t?"ol":"ul";return"<"+s+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+s+">\n"}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});e.loose?e.tokens.length>0&&"paragraph"===e.tokens[0].type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+e.tokens[0].tokens[0].text)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" "}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){const s=e.rows[t];n="";for(let e=0;e<s.length;e++)n+=this.tablecell(s[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${e}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const r=this.parser.parseInline(n),s=d(e);if(null===s)return r;let i='<a href="'+(e=s)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+r+"</a>",i}image({href:e,title:t,text:n}){const r=d(e);if(null===r)return n;let s=`<img src="${e=r}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):e.text}}class se{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class ie{options;renderer;textRenderer;constructor(e){this.options=e||r,this.options.renderer=this.options.renderer||new re,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new se}static parse(e,t){return new ie(t).parse(e)}static parseInline(e,t){return new ie(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const e=s,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}const i=s;switch(i.type){case"space":n+=this.renderer.space(i);continue;case"hr":n+=this.renderer.hr(i);continue;case"heading":n+=this.renderer.heading(i);continue;case"code":n+=this.renderer.code(i);continue;case"table":n+=this.renderer.table(i);continue;case"blockquote":n+=this.renderer.blockquote(i);continue;case"list":n+=this.renderer.list(i);continue;case"html":n+=this.renderer.html(i);continue;case"paragraph":n+=this.renderer.paragraph(i);continue;case"text":{let s=i,o=this.renderer.text(s);for(;r+1<e.length&&"text"===e[r+1].type;)s=e[++r],o+="\n"+this.renderer.text(s);n+=t?this.renderer.paragraph({type:"paragraph",raw:o,text:o,tokens:[{type:"text",raw:o,text:o}]}):o;continue}default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const e=this.options.extensions.renderers[s.type].call({parser:this},s);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=e||"";continue}}const i=s;switch(i.type){case"escape":case"text":n+=t.text(i);break;case"html":n+=t.html(i);break;case"link":n+=t.link(i);break;case"image":n+=t.image(i);break;case"strong":n+=t.strong(i);break;case"em":n+=t.em(i);break;case"codespan":n+=t.codespan(i);break;case"br":n+=t.br(i);break;case"del":n+=t.del(i);break;default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}}class oe{options;block;constructor(e){this.options=e||r}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?ne.lex:ne.lexInline}provideParser(){return this.block?ie.parse:ie.parseInline}}const ae=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=ie;Renderer=re;TextRenderer=se;Lexer=ne;Tokenizer=x;Hooks=oe;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{const e=r;for(const r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(const r of e.rows)for(const e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{const e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{const e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach((r=>{const s=e[r].flat(1/0);n=n.concat(this.walkTokens(s,t))})):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach((e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach((e=>{if(!e.name)throw new Error("extension name required");if("renderer"in e){const n=t.renderers[e.name];t.renderers[e.name]=n?function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");const n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)})),n.extensions=t),e.renderer){const t=this.defaults.renderer||new re(this.defaults);for(const n in e.renderer){if(!(n in t))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;const r=n,s=e.renderer[r],i=t[r];t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=i.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){const t=this.defaults.tokenizer||new x(this.defaults);for(const n in e.tokenizer){if(!(n in t))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;const r=n,s=e.tokenizer[r],i=t[r];t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){const t=this.defaults.hooks||new oe;for(const n in e.hooks){if(!(n in t))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;const r=n,s=e.hooks[r],i=t[r];oe.passThroughHooks.has(n)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(s.call(t,e)).then((e=>i.call(t,e)));const n=s.call(t,e);return i.call(t,n)}:t[r]=(...e)=>{let n=s.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){const t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}})),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return ne.lex(e,t??this.defaults)}parser(e,t){return ie.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{const r={...n},s={...this.defaults,...r},i=this.onError(!!s.silent,!!s.async);if(!0===this.defaults.async&&!1===r.async)return i(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return i(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));s.hooks&&(s.hooks.options=s,s.hooks.block=e);const o=s.hooks?s.hooks.provideLexer():e?ne.lex:ne.lexInline,a=s.hooks?s.hooks.provideParser():e?ie.parse:ie.parseInline;if(s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then((e=>o(e,s))).then((e=>s.hooks?s.hooks.processAllTokens(e):e)).then((e=>s.walkTokens?Promise.all(this.walkTokens(e,s.walkTokens)).then((()=>e)):e)).then((e=>a(e,s))).then((e=>s.hooks?s.hooks.postprocess(e):e)).catch(i);try{s.hooks&&(t=s.hooks.preprocess(t));let e=o(t,s);s.hooks&&(e=s.hooks.processAllTokens(e)),s.walkTokens&&this.walkTokens(e,s.walkTokens);let n=a(e,s);return s.hooks&&(n=s.hooks.postprocess(n)),n}catch(e){return i(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+u(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function le(e,t){return ae.parse(e,t)}le.options=le.setOptions=function(e){return ae.setOptions(e),le.defaults=ae.defaults,s(le.defaults),le},le.getDefaults=function(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}},le.defaults=r,le.use=function(...e){return ae.use(...e),le.defaults=ae.defaults,s(le.defaults),le},le.walkTokens=function(e,t){return ae.walkTokens(e,t)},le.parseInline=ae.parseInline,le.Parser=ie,le.parser=ie.parse,le.Renderer=re,le.TextRenderer=se,le.Lexer=ne,le.lexer=ne.lex,le.Tokenizer=x,le.Hooks=oe,le.parse=le,le.options,le.setOptions,le.use,le.walkTokens,le.parseInline,ie.parse,ne.lex},404:(e,t,n)=>{n.d(t,{A:()=>ne});const{entries:r,setPrototypeOf:s,isFrozen:i,getPrototypeOf:o,getOwnPropertyDescriptor:a}=Object;let{freeze:l,seal:c,create:h}=Object,{apply:u,construct:p}="undefined"!=typeof Reflect&&Reflect;l||(l=function(e){return e}),c||(c=function(e){return e}),u||(u=function(e,t,n){return e.apply(t,n)}),p||(p=function(e,t){return new e(...t)});const m=A(Array.prototype.forEach),d=A(Array.prototype.lastIndexOf),f=A(Array.prototype.pop),g=A(Array.prototype.push),k=A(Array.prototype.splice),b=A(String.prototype.toLowerCase),x=A(String.prototype.toString),w=A(String.prototype.match),v=A(String.prototype.replace),y=A(String.prototype.indexOf),E=A(String.prototype.trim),T=A(Object.prototype.hasOwnProperty),_=A(RegExp.prototype.test),z=(S=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p(S,t)});var S;function A(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];return u(e,t,r)}}function N(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b;s&&s(e,null);let r=t.length;for(;r--;){let s=t[r];if("string"==typeof s){const e=n(s);e!==s&&(i(t)||(t[r]=e),s=e)}e[s]=!0}return e}function R(e){for(let t=0;t<e.length;t++)T(e,t)||(e[t]=null);return e}function I(e){const t=h(null);for(const[n,s]of r(e))T(e,n)&&(Array.isArray(s)?t[n]=R(s):s&&"object"==typeof s&&s.constructor===Object?t[n]=I(s):t[n]=s);return t}function M(e,t){for(;null!==e;){const n=a(e,t);if(n){if(n.get)return A(n.get);if("function"==typeof n.value)return A(n.value)}e=o(e)}return function(){return null}}const C=l(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),$=l(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),L=l(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),D=l(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),O=l(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),P=l(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),H=l(["#text"]),U=l(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),q=l(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),B=l(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=l(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),j=c(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=c(/<%[\w\W]*|[\w\W]*%>/gm),Z=c(/\$\{[\w\W]*/gm),V=c(/^data-[\-\w.\u00B7-\uFFFF]+$/),G=c(/^aria-[\-\w]+$/),Y=c(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Q=c(/^(?:\w+script|data):/i),X=c(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=c(/^html$/i),J=c(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:G,ATTR_WHITESPACE:X,CUSTOM_ELEMENT:J,DATA_ATTR:V,DOCTYPE_NAME:K,ERB_EXPR:W,IS_ALLOWED_URI:Y,IS_SCRIPT_OR_DATA:Q,MUSTACHE_EXPR:j,TMPLIT_EXPR:Z});const te=function(){return"undefined"==typeof window?null:window};var ne=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return n.isSupported=!1,n;let{document:s}=t;const i=s,o=i.currentScript,{DocumentFragment:a,HTMLTemplateElement:c,Node:u,Element:p,NodeFilter:S,NamedNodeMap:A=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:R,DOMParser:j,trustedTypes:W}=t,Z=p.prototype,V=M(Z,"cloneNode"),G=M(Z,"remove"),Q=M(Z,"nextSibling"),X=M(Z,"childNodes"),J=M(Z,"parentNode");if("function"==typeof c){const e=s.createElement("template");e.content&&e.content.ownerDocument&&(s=e.content.ownerDocument)}let ne,re="";const{implementation:se,createNodeIterator:ie,createDocumentFragment:oe,getElementsByTagName:ae}=s,{importNode:le}=i;let ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof r&&"function"==typeof J&&se&&void 0!==se.createHTMLDocument;const{MUSTACHE_EXPR:he,ERB_EXPR:ue,TMPLIT_EXPR:pe,DATA_ATTR:me,ARIA_ATTR:de,IS_SCRIPT_OR_DATA:fe,ATTR_WHITESPACE:ge,CUSTOM_ELEMENT:ke}=ee;let{IS_ALLOWED_URI:be}=ee,xe=null;const we=N({},[...C,...$,...L,...O,...H]);let ve=null;const ye=N({},[...U,...q,...B,...F]);let Ee=Object.seal(h(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Te=null,_e=null,ze=!0,Se=!0,Ae=!1,Ne=!0,Re=!1,Ie=!0,Me=!1,Ce=!1,$e=!1,Le=!1,De=!1,Oe=!1,Pe=!0,He=!1,Ue=!0,qe=!1,Be={},Fe=null;const je=N({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let We=null;const Ze=N({},["audio","video","img","source","image","track"]);let Ve=null;const Ge=N({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ye="http://www.w3.org/1998/Math/MathML",Qe="http://www.w3.org/2000/svg",Xe="http://www.w3.org/1999/xhtml";let Ke=Xe,Je=!1,et=null;const tt=N({},[Ye,Qe,Xe],x);let nt=N({},["mi","mo","mn","ms","mtext"]),rt=N({},["annotation-xml"]);const st=N({},["title","style","font","a","script"]);let it=null;const ot=["application/xhtml+xml","text/html"];let at=null,lt=null;const ct=s.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},ut=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!lt||lt!==e){if(e&&"object"==typeof e||(e={}),e=I(e),it=-1===ot.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,at="application/xhtml+xml"===it?x:b,xe=T(e,"ALLOWED_TAGS")?N({},e.ALLOWED_TAGS,at):we,ve=T(e,"ALLOWED_ATTR")?N({},e.ALLOWED_ATTR,at):ye,et=T(e,"ALLOWED_NAMESPACES")?N({},e.ALLOWED_NAMESPACES,x):tt,Ve=T(e,"ADD_URI_SAFE_ATTR")?N(I(Ge),e.ADD_URI_SAFE_ATTR,at):Ge,We=T(e,"ADD_DATA_URI_TAGS")?N(I(Ze),e.ADD_DATA_URI_TAGS,at):Ze,Fe=T(e,"FORBID_CONTENTS")?N({},e.FORBID_CONTENTS,at):je,Te=T(e,"FORBID_TAGS")?N({},e.FORBID_TAGS,at):I({}),_e=T(e,"FORBID_ATTR")?N({},e.FORBID_ATTR,at):I({}),Be=!!T(e,"USE_PROFILES")&&e.USE_PROFILES,ze=!1!==e.ALLOW_ARIA_ATTR,Se=!1!==e.ALLOW_DATA_ATTR,Ae=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ne=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Re=e.SAFE_FOR_TEMPLATES||!1,Ie=!1!==e.SAFE_FOR_XML,Me=e.WHOLE_DOCUMENT||!1,Le=e.RETURN_DOM||!1,De=e.RETURN_DOM_FRAGMENT||!1,Oe=e.RETURN_TRUSTED_TYPE||!1,$e=e.FORCE_BODY||!1,Pe=!1!==e.SANITIZE_DOM,He=e.SANITIZE_NAMED_PROPS||!1,Ue=!1!==e.KEEP_CONTENT,qe=e.IN_PLACE||!1,be=e.ALLOWED_URI_REGEXP||Y,Ke=e.NAMESPACE||Xe,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,rt=e.HTML_INTEGRATION_POINTS||rt,Ee=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ee.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ee.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ee.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Re&&(Se=!1),De&&(Le=!0),Be&&(xe=N({},H),ve=[],!0===Be.html&&(N(xe,C),N(ve,U)),!0===Be.svg&&(N(xe,$),N(ve,q),N(ve,F)),!0===Be.svgFilters&&(N(xe,L),N(ve,q),N(ve,F)),!0===Be.mathMl&&(N(xe,O),N(ve,B),N(ve,F))),e.ADD_TAGS&&(xe===we&&(xe=I(xe)),N(xe,e.ADD_TAGS,at)),e.ADD_ATTR&&(ve===ye&&(ve=I(ve)),N(ve,e.ADD_ATTR,at)),e.ADD_URI_SAFE_ATTR&&N(Ve,e.ADD_URI_SAFE_ATTR,at),e.FORBID_CONTENTS&&(Fe===je&&(Fe=I(Fe)),N(Fe,e.FORBID_CONTENTS,at)),Ue&&(xe["#text"]=!0),Me&&N(xe,["html","head","body"]),xe.table&&(N(xe,["tbody"]),delete Te.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw z('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw z('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,re=ne.createHTML("")}else void 0===ne&&(ne=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const s="dompurify"+(n?"#"+n:"");try{return e.createPolicy(s,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(W,o)),null!==ne&&"string"==typeof re&&(re=ne.createHTML(""));l&&l(e),lt=e}},pt=N({},[...$,...L,...D]),mt=N({},[...O,...P]),dt=function(e){g(n.removed,{element:e});try{J(e).removeChild(e)}catch(t){G(e)}},ft=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Le||De)try{dt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){let t=null,n=null;if($e)e="<remove></remove>"+e;else{const t=w(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===it&&Ke===Xe&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=ne?ne.createHTML(e):e;if(Ke===Xe)try{t=(new j).parseFromString(r,it)}catch(e){}if(!t||!t.documentElement){t=se.createDocument(Ke,"template",null);try{t.documentElement.innerHTML=Je?re:r}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(s.createTextNode(n),i.childNodes[0]||null),Ke===Xe?ae.call(t,Me?"html":"body")[0]:Me?t.documentElement:i},kt=function(e){return ie.call(e.ownerDocument||e,e,S.SHOW_ELEMENT|S.SHOW_COMMENT|S.SHOW_TEXT|S.SHOW_PROCESSING_INSTRUCTION|S.SHOW_CDATA_SECTION,null)},bt=function(e){return e instanceof R&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof A)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},xt=function(e){return"function"==typeof u&&e instanceof u};function wt(e,t,r){m(e,(e=>{e.call(n,t,r,lt)}))}const vt=function(e){let t=null;if(wt(ce.beforeSanitizeElements,e,null),bt(e))return dt(e),!0;const r=at(e.nodeName);if(wt(ce.uponSanitizeElement,e,{tagName:r,allowedTags:xe}),Ie&&e.hasChildNodes()&&!xt(e.firstElementChild)&&_(/<[/\w!]/g,e.innerHTML)&&_(/<[/\w!]/g,e.textContent))return dt(e),!0;if(7===e.nodeType)return dt(e),!0;if(Ie&&8===e.nodeType&&_(/<[/\w]/g,e.data))return dt(e),!0;if(!xe[r]||Te[r]){if(!Te[r]&&Et(r)){if(Ee.tagNameCheck instanceof RegExp&&_(Ee.tagNameCheck,r))return!1;if(Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(r))return!1}if(Ue&&!Fe[r]){const t=J(e)||e.parentNode,n=X(e)||e.childNodes;if(n&&t)for(let r=n.length-1;r>=0;--r){const s=V(n[r],!0);s.__removalCount=(e.__removalCount||0)+1,t.insertBefore(s,Q(e))}}return dt(e),!0}return e instanceof p&&!function(e){let t=J(e);t&&t.tagName||(t={namespaceURI:Ke,tagName:"template"});const n=b(e.tagName),r=b(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===Qe?t.namespaceURI===Xe?"svg"===n:t.namespaceURI===Ye?"svg"===n&&("annotation-xml"===r||nt[r]):Boolean(pt[n]):e.namespaceURI===Ye?t.namespaceURI===Xe?"math"===n:t.namespaceURI===Qe?"math"===n&&rt[r]:Boolean(mt[n]):e.namespaceURI===Xe?!(t.namespaceURI===Qe&&!rt[r])&&!(t.namespaceURI===Ye&&!nt[r])&&!mt[n]&&(st[n]||!pt[n]):!("application/xhtml+xml"!==it||!et[e.namespaceURI]))}(e)?(dt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!_(/<\/no(script|embed|frames)/i,e.innerHTML)?(Re&&3===e.nodeType&&(t=e.textContent,m([he,ue,pe],(e=>{t=v(t,e," ")})),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),wt(ce.afterSanitizeElements,e,null),!1):(dt(e),!0)},yt=function(e,t,n){if(Pe&&("id"===t||"name"===t)&&(n in s||n in ct))return!1;if(Se&&!_e[t]&&_(me,t));else if(ze&&_(de,t));else if(!ve[t]||_e[t]){if(!(Et(e)&&(Ee.tagNameCheck instanceof RegExp&&_(Ee.tagNameCheck,e)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(e))&&(Ee.attributeNameCheck instanceof RegExp&&_(Ee.attributeNameCheck,t)||Ee.attributeNameCheck instanceof Function&&Ee.attributeNameCheck(t))||"is"===t&&Ee.allowCustomizedBuiltInElements&&(Ee.tagNameCheck instanceof RegExp&&_(Ee.tagNameCheck,n)||Ee.tagNameCheck instanceof Function&&Ee.tagNameCheck(n))))return!1}else if(Ve[t]);else if(_(be,v(n,ge,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==y(n,"data:")||!We[e])if(Ae&&!_(fe,v(n,ge,"")));else if(n)return!1;return!0},Et=function(e){return"annotation-xml"!==e&&w(e,ke)},Tt=function(e){wt(ce.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||bt(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ve,forceKeepAttr:void 0};let s=t.length;for(;s--;){const i=t[s],{name:o,namespaceURI:a,value:l}=i,c=at(o),h=l;let u="value"===o?h:E(h);if(r.attrName=c,r.attrValue=u,r.keepAttr=!0,r.forceKeepAttr=void 0,wt(ce.uponSanitizeAttribute,e,r),u=r.attrValue,!He||"id"!==c&&"name"!==c||(ft(o,e),u="user-content-"+u),Ie&&_(/((--!?|])>)|<\/(style|title)/i,u)){ft(o,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){ft(o,e);continue}if(!Ne&&_(/\/>/i,u)){ft(o,e);continue}Re&&m([he,ue,pe],(e=>{u=v(u,e," ")}));const p=at(e.nodeName);if(yt(p,c,u)){if(ne&&"object"==typeof W&&"function"==typeof W.getAttributeType)if(a);else switch(W.getAttributeType(p,c)){case"TrustedHTML":u=ne.createHTML(u);break;case"TrustedScriptURL":u=ne.createScriptURL(u)}if(u!==h)try{a?e.setAttributeNS(a,o,u):e.setAttribute(o,u),bt(e)?dt(e):f(n.removed)}catch(t){ft(o,e)}}else ft(o,e)}wt(ce.afterSanitizeAttributes,e,null)},_t=function e(t){let n=null;const r=kt(t);for(wt(ce.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)wt(ce.uponSanitizeShadowNode,n,null),vt(n),Tt(n),n.content instanceof a&&e(n.content);wt(ce.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,s=null,o=null,l=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!xt(e)){if("function"!=typeof e.toString)throw z("toString is not a function");if("string"!=typeof(e=e.toString()))throw z("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Ce||ut(t),n.removed=[],"string"==typeof e&&(qe=!1),qe){if(e.nodeName){const t=at(e.nodeName);if(!xe[t]||Te[t])throw z("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof u)r=gt("\x3c!----\x3e"),s=r.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?r=s:r.appendChild(s);else{if(!Le&&!Re&&!Me&&-1===e.indexOf("<"))return ne&&Oe?ne.createHTML(e):e;if(r=gt(e),!r)return Le?null:Oe?re:""}r&&$e&&dt(r.firstChild);const c=kt(qe?e:r);for(;o=c.nextNode();)vt(o),Tt(o),o.content instanceof a&&_t(o.content);if(qe)return e;if(Le){if(De)for(l=oe.call(r.ownerDocument);r.firstChild;)l.appendChild(r.firstChild);else l=r;return(ve.shadowroot||ve.shadowrootmode)&&(l=le.call(i,l,!0)),l}let h=Me?r.outerHTML:r.innerHTML;return Me&&xe["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&_(K,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),Re&&m([he,ue,pe],(e=>{h=v(h,e," ")})),ne&&Oe?ne.createHTML(h):h},n.setConfig=function(){ut(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Ce=!0},n.clearConfig=function(){lt=null,Ce=!1},n.isValidAttribute=function(e,t,n){lt||ut({});const r=at(e),s=at(t);return yt(r,s,n)},n.addHook=function(e,t){"function"==typeof t&&g(ce[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=d(ce[e],t);return-1===n?void 0:k(ce[e],n,1)[0]}return f(ce[e])},n.removeHooks=function(e){ce[e]=[]},n.removeAllHooks=function(){ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()},609:e=>{e.exports=window.React},619:e=>{e.exports=window.wp.hooks},642:()=>{},723:e=>{e.exports=window.wp.i18n},849:(e,t,n)=>{n.d(t,{$H:()=>s,GI:()=>r,Tt:()=>i}),n(57);const r=async(e,t,n)=>{try{const r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/get-current-user-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e,filter_term:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"Failed to fetch conversation")}return r.json()}catch(e){throw console.error("Error fetching conversation:",e),e}},s=async(e,t)=>{try{const n=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/update-online-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e})});if(!n.ok){const e=await n.json();throw new Error(e.message||"Online status update failed")}return n.json()}catch(e){throw console.error("Error updating online status:",e),e}},i=async(e,t,n)=>{try{const r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/save-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,conversation:t,session_id:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"Failed to save conversation")}return r.json()}catch(e){throw console.error("Error saving conversation:",e),e}}}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r=n(609),s=n(619),i=n(723),o=n(404);const a=()=>(0,r.createElement)("svg",{width:38,height:46,viewBox:"0 0 38 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"a",fill:"#fff"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z"})),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("path",{d:"m33.764 11.967 1.979-1.979zm0 23.745 1.979 1.979zm-9.1 1.093v-2.798h-1.04l-.788.679zm-9.955 8.582h-2.798v6.107l4.625-3.988zm0-8.582h2.798v-2.798H14.71zM4.235 35.712l-1.978 1.979zm22.973-7.815 1.748 2.185 2.185-1.748-1.748-2.185zm-16.463-.039-2.111-1.836-1.837 2.112L8.91 29.97zm14.598-2.292 2.185-1.748-1.748-2.185-2.185 1.748zm-12.64.04 1.837-2.112-2.111-1.836-1.837 2.111zm-.885-6.151v-2.798H9.019v2.798zm0 2.798H9.019v2.798h2.799zm2.798-2.798h2.799v-2.798h-2.799zm0 2.798v2.798h2.799v-2.798zm8.769 0h-2.799v2.798h2.799zm0-2.798v-2.798h-2.799v2.798zm2.798 2.798v2.798h2.799v-2.798zm0-2.798h2.799v-2.798h-2.799zm1.212-11.38h-16.79v5.597h16.79zm8.348 1.913c-1.153-1.153-2.555-1.572-3.882-1.75-1.245-.168-2.787-.162-4.466-.162v5.596c1.838 0 2.935.006 3.72.112.361.048.542.106.623.139l.038.018.01.005h-.001q-.002-.003 0 0zm1.912 8.348c0-1.68.006-3.22-.161-4.467-.179-1.326-.598-2.728-1.751-3.88l-3.958 3.957q.003.001 0 0v-.002l.005.01.018.04c.033.08.09.26.139.621.106.786.112 1.883.112 3.721zm0 11.007V18.336H32.06v11.007zm-1.912 8.348c1.153-1.153 1.572-2.555 1.75-3.881.168-1.246.162-2.788.162-4.467H32.06c0 1.838-.006 2.935-.112 3.721-.049.36-.106.54-.14.622l-.021.047q0-.002 0 0zm-8.349 1.912c1.68 0 3.222.006 4.468-.161 1.326-.178 2.728-.598 3.88-1.751l-3.957-3.958q-.002.003 0 .001l.002-.001-.01.006-.04.017c-.08.034-.261.09-.621.14-.787.105-1.883.11-3.721.11zm-2.73 0h2.73v-5.596h-2.73zm-8.128 7.903 9.955-8.581-3.655-4.24-9.954 8.582zm-4.625-10.7v8.58h5.596v-8.58zm-1.306 2.797h4.104v-5.596h-4.104zm-8.348-1.912c1.153 1.153 2.555 1.573 3.881 1.75 1.246.168 2.787.162 4.467.162v-5.596c-1.838 0-2.935-.006-3.721-.112-.36-.048-.541-.105-.622-.139l-.039-.017-.01-.006.002.001q0 .002 0 0zM.344 29.343c0 1.68-.006 3.22.162 4.467.178 1.326.598 2.728 1.75 3.881l3.958-3.958q-.002-.002 0 0l.001.002-.006-.01-.017-.039c-.034-.081-.09-.262-.14-.622-.105-.786-.111-1.883-.111-3.721zm0-11.007v11.007h5.597V18.336zm1.913-8.348C1.104 11.141.684 12.543.506 13.87c-.168 1.246-.162 2.788-.162 4.467h5.597c0-1.838.006-2.935.112-3.72.048-.361.105-.542.139-.623l.017-.038.006-.01-.001.001q-.002.001 0 0zm8.348-1.912c-1.68 0-3.221-.006-4.467.161-1.326.179-2.728.598-3.881 1.751l3.957 3.958q.003-.003 0 0h-.001l.01-.005.039-.018c.081-.033.261-.09.622-.139.786-.106 1.883-.112 3.721-.112zm14.854 17.636c-4.21 3.368-9.1 3.32-12.877.034L8.909 29.97c5.97 5.192 13.887 5.04 20.047.112zm-2.301 1.603 1.864 2.33 4.37-3.496-1.864-2.33zm-12.29.402c2.313 2.012 5.075 3.117 7.99 3.119 2.905.002 5.74-1.09 8.233-3.084l-3.496-4.37c-1.65 1.32-3.28 1.858-4.733 1.857-1.442 0-2.928-.533-4.322-1.745zm1.989 1.977 1.959-2.252-4.224-3.673-1.958 2.253zm-3.838-10.24v2.799h5.597v-2.798zm5.597-2.797h-2.798v5.596h2.798zm2.799 5.596v-2.798h-5.597v2.798zm-5.597 2.798h2.798v-5.596h-2.798zm14.365-2.798v-2.798h-5.597v2.798zm0-2.799h-2.798v5.597h2.798zm-2.798 0v2.799h5.596v-2.798zm0 2.8h2.798v-5.597h-2.798z",fill:"#00B682",className:"chatbotpath",mask:"url(#a)"}),(0,r.createElement)("path",{d:"M19 13.673V2.48",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"2.798"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.142 25.239v-5.783H.157v5.783zm31.716-5.783v5.783h2.985v-5.783z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("circle",{cx:19,cy:"3.411",r:"2.798",fill:"#00B682",className:"chatbotpath"})),l=()=>(0,r.createElement)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("g",{clipPath:"url(#a)"},(0,r.createElement)("path",{d:"M9.666 11.66H8.11v-1.556h1.556zm6.225 0h-1.556v-1.556h1.556zm6.224-1.556v3.112H20.56v3.89q0 .488-.182.912-.183.426-.499.742a2.34 2.34 0 0 1-1.653.68h-3.21l-5.349 4.572V19.44h-3.89a2.34 2.34 0 0 1-1.653-.68 2.3 2.3 0 0 1-.499-.743 2.3 2.3 0 0 1-.182-.912v-3.89H1.886v-3.112h1.556V7.77q0-.486.182-.912.183-.425.499-.742a2.34 2.34 0 0 1 1.653-.68h5.446V2.895a1.5 1.5 0 0 1-.559-.572 1.66 1.66 0 0 1-.219-.778q0-.327.122-.607a1.6 1.6 0 0 1 .328-.487q.207-.207.499-.34.291-.134.607-.122.33 0 .608.122.28.12.487.328.205.207.34.499t.122.607a1.55 1.55 0 0 1-.779 1.35v2.54h5.447q.486 0 .912.183.424.183.741.498a2.34 2.34 0 0 1 .681 1.654v2.334zM19.003 7.77a.75.75 0 0 0-.231-.547.75.75 0 0 0-.547-.231H5.776a.75.75 0 0 0-.547.23.75.75 0 0 0-.231.548v9.337q0 .316.23.547.232.23.548.23h5.446v2.748l3.21-2.748h3.793q.316 0 .547-.23a.75.75 0 0 0 .23-.547zM8.657 13.386q.67.67 1.532 1.022.864.352 1.811.364.949 0 1.812-.352a4.5 4.5 0 0 0 1.532-1.034l1.094 1.107A6.22 6.22 0 0 1 12 16.328a6.2 6.2 0 0 1-2.395-.474 6.4 6.4 0 0 1-2.042-1.361z",fill:"#00B682",className:"chatbotpath"})),(0,r.createElement)("defs",null,(0,r.createElement)("clipPath",{id:"a"},(0,r.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"}))));var c=n(57);const h=()=>(0,r.createElement)("svg",{width:8,height:12,viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"M5.1 6 1.2 2.1a.95.95 0 0 1-.275-.7q0-.426.275-.7a.95.95 0 0 1 .7-.275q.424 0 .7.275l4.6 4.6q.15.15.212.325.063.174.063.375 0 .2-.063.375A.9.9 0 0 1 7.2 6.7l-4.6 4.6a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275.95.95 0 0 1-.275-.7q0-.426.275-.7z",fill:"#1C1B1F"})),u=()=>(0,r.createElement)("svg",{width:18,height:16,viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"m16.865 8.925-15.4 6.5a.99.99 0 0 1-.95-.087q-.45-.288-.45-.838v-13q0-.55.45-.837a.99.99 0 0 1 .95-.088l15.4 6.5q.624.276.625.925 0 .65-.625.925M2.064 13l11.85-5-11.85-5v3.5l6 1.5-6 1.5z",fill:"#fff"}));var p=n(849);const m=({timestamp:e,timezone:t})=>{const[n,s]=(0,r.useState)(""),o=(0,r.useCallback)((e=>{if(!e)return(0,i.__)("Just now","betterdocs-ai-chatbot");let n;try{if(n="string"==typeof e&&e.includes(" ")?new Date(e.replace(" ","T")+"Z"):e instanceof Date?e:new Date(e),isNaN(n?.getTime()))return(0,i.__)("Just now","betterdocs-ai-chatbot");const r=new Intl.DateTimeFormat("en-US",{timeZone:t||"UTC",hour:"numeric",minute:"numeric",hour12:!0}),s=new Date,o=new Date(s.toLocaleString("en-US",{timeZone:t||"UTC"})),a=new Date(n.toLocaleString("en-US",{timeZone:t||"UTC"})),l=(o.getTime()-a.getTime())/1e3,c=r.format(n);if(l<60)return(0,i.__)("Just now","betterdocs-ai-chatbot");if(l<3600){const e=Math.floor(l/60);return(0,i.sprintf)((0,i._n)("%d minute ago","%d minutes ago",e,"betterdocs-ai-chatbot"),e)}if(l<86400){const e=Math.floor(l/3600);return(0,i.sprintf)((0,i._n)("%d hour ago","%d hours ago",e,"betterdocs-ai-chatbot"),e)}const h=new Date(o);if(h.setDate(h.getDate()-1),a.getDate()===h.getDate()&&a.getMonth()===h.getMonth()&&a.getFullYear()===h.getFullYear())return(0,i.sprintf)((0,i.__)("Yesterday (%s)","betterdocs-ai-chatbot"),c);const u=new Intl.DateTimeFormat("en-US",{timeZone:t||"UTC",day:"2-digit",month:"short",year:"numeric"});return(0,i.sprintf)("%s (%s)",u.format(n),c)}catch(e){return console.error("Error calculating time ago:",e),(0,i.__)("Just now","betterdocs-ai-chatbot")}}),[t]);return(0,r.useEffect)((()=>{s(o(e));const t=setInterval((()=>{s(o(e))}),6e4);return()=>clearInterval(t)}),[e,o]),(0,r.createElement)("span",{className:"text-gray-500"},n)},d=()=>(0,r.createElement)("div",{className:"generic-loader"},(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:40,height:40,preserveAspectRatio:"xMidYMid",style:{background:"0 0",shapeRendering:"auto"},viewBox:"0 0 100 100"},(0,r.createElement)("circle",{cx:50,cy:50,r:26,fill:"none",stroke:"#16ca9e",strokeDasharray:"122.52211349000194 42.840704496667314",strokeWidth:10},(0,r.createElement)("animateTransform",{attributeName:"transform",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"}))));var f=n(133);n(642);const g=()=>{const[e,t]=(0,r.useState)(null),[n,s]=(0,r.useState)(""),[g,k]=(0,r.useState)([]),[b,x]=(0,r.useState)(!1),[w,v]=(0,r.useState)(!1),[y,E]=(0,r.useState)(""),[T,_]=(0,r.useState)(!0),[z,S]=(0,r.useState)(""),[A,N]=(0,r.useState)((0,c.Ri)("userEmail")||""),[R]=(0,r.useState)(betterdocsAIChatbot?.welcome_message),[I,M]=(0,r.useState)(null),[C,$]=(0,r.useState)(!1),[L,D]=(0,r.useState)(!1),[O,P]=(0,r.useState)(null),[H,U]=(0,r.useState)(!1),[q,B]=(0,r.useState)(!1),F=(0,r.useRef)(null),j=(0,r.useRef)(null),[W,Z]=(0,r.useState)(null),[V,G]=(0,r.useState)({city:"",country:""}),[Y,Q]=(0,r.useState)("offline"),X=(new Date).toISOString().slice(0,19).replace("T"," "),K=()=>(new Date).toISOString().slice(0,19).replace("T"," ");(0,r.useEffect)((()=>{const e=(0,c.Bk)();t(e)}),[]),(0,r.useEffect)((()=>{localStorage.setItem("userStatus","online");const e=()=>{localStorage.setItem("userStatus","online")};return window.addEventListener("mousemove",e),window.addEventListener("keydown",e),()=>{window.removeEventListener("mousemove",e),window.removeEventListener("keydown",e),localStorage.removeItem("userStatus")}}),[]),(0,r.useEffect)((()=>{(async()=>{try{const e=await fetch("https://ipinfo.io/json");if(!e.ok)throw new Error("Network response was not ok");const t=await(e?.json());G(t)}catch(e){Z(e.message)}})()}),[]);const J=async()=>{if(""===n.trim())return;const t=n?.trim(),r=K();if(k((e=>[...e,{text:t,type:navigator?.onLine?"sent":"failed",timestamp:r}])),s(""),E(""),F.current&&setTimeout((()=>{F.current.scrollTop=F.current.scrollHeight}),50),navigator.onLine){v(!0);try{const n=(betterdocsAIChatbot?.locale||"en_US").split("_")[0],r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/query-post`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t,email:A,session_id:e,lang:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"API request failed")}const s=await r.json();if(!s?.success)throw new Error(s.message||"Request failed");{const e=s?.data?.conversation||[],t=e[e.length-1],n=t?.text||"No response content available";x(!0),ee(n)}}catch(e){v(!1),k((t=>[...t,{text:e.message,type:"received",timestamp:K()}]))}}},ee=e=>{let t=0,n="";E("");const r=K();v(!0),M(r);const s=()=>{if(t<e?.length)n+=e?.charAt(t),E(n),t++,setTimeout(s,10);else{const e=(0,c.Kk)(n).__html;E(e),v(!1),k((t=>[...t,{text:e,type:"received",timestamp:r}]))}};s()},te=e=>{const t=new Set;let n=null;return e.map((e=>{if(!e.timestamp){const t=K();return{...e,timestamp:t}}if(t.has(e.timestamp)||e.timestamp===n){let r;r=e.timestamp.includes("T")&&e.timestamp.includes("Z")?new Date(e.timestamp):new Date(e.timestamp.replace(" ","T")+"Z"),r.setMilliseconds(r.getMilliseconds()+1);const s=r.toISOString().slice(0,19).replace("T"," ");return n=s,t.add(s),{...e,timestamp:s}}return n=e.timestamp,t.add(e.timestamp),e}))},ne=async()=>{if(""===z?.trim()||!ie(z?.trim()))return D(!0),void setTimeout((()=>D(!1)),3e3);(0,c.TV)("userEmail",z,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),(0,c.TV)("showEmailField","false",{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),N(z),$(!0),setTimeout((()=>$(!1)),3e3),_(!1)};(0,r.useEffect)((()=>{A&&(async()=>{if(A&&e){B(!0);try{const t=await(0,p.GI)(A,e);t?.conversation&&(k(t.conversation),(0,p.$H)(A,e))}catch(e){k([{text:R,type:"received",timestamp:K()}])}finally{B(!1)}}})()}),[A,e,R]),(0,r.useEffect)((()=>{const e=K(),t=A?(0,c.Ri)(A):null;if(A&&t){const e=JSON.parse(t);k(te(e?.conversation||[]))}else A||k([{text:R,type:"received",timestamp:e}])}),[A]);const re=(0,r.useRef)(0),se=(0,r.useRef)(null);(0,r.useEffect)((()=>()=>{se.current&&clearTimeout(se.current)}),[]),(0,r.useEffect)((()=>{A&&g&&0!==g.length&&e&&(se.current&&clearTimeout(se.current),se.current=setTimeout((()=>{const t=Date.now();if(t-re.current<2e3)return;re.current=t;const n=te(g),r={email:A,main_information:{location:`${V?.city}, ${V?.country}`,country:V?.country,time:K(),timezone:V?.timezone,language:navigator?.language},device_information:{IP_address:V?.ip,browser:(0,c.VK)()},isBlocked:!1,conversation:n.map((e=>({text:e?.text,type:e?.type,timestamp:e?.timestamp||K()})))};(0,p.Tt)(A,r,e).then((t=>{t?.success&&(0,p.$H)(A,e)})).catch((e=>{console.error("Error saving conversation:",e)}))}),500))}),[V,g,A,e]),(0,r.useEffect)((()=>{if(j?.current){const e=()=>U(!0),t=()=>U(!1),n=j?.current;return n.addEventListener("focus",e),n.addEventListener("blur",t),()=>{n.removeEventListener("focus",e),n.removeEventListener("blur",t)}}}),[]),(0,r.useEffect)((()=>{F.current&&(F.current.scrollTop=F?.current?.scrollHeight)}),[g,w,y,L,C]);const ie=e=>e?!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||(P("Please enter a valid email"),!1):(P("Email is required"),!1);return(0,r.createElement)("div",{className:"chat-container"},(0,r.createElement)("div",{className:"betterdocs-chatbot-header betterdocs-ia-common-header"},(0,r.createElement)("h2",null,(0,i.__)("Chatbot","betterdocs-ai-chatbot"))),(0,r.createElement)("div",{className:"chat-content-wrapper"},(0,r.createElement)("div",{className:"chat-body",ref:F},(0,r.createElement)("div",{className:"top-content"},(0,r.createElement)("div",{className:"chat-icon"},(0,r.createElement)(a,null)),betterdocsAIChatbot?.title&&(0,r.createElement)("h3",{className:"heading-title"},betterdocsAIChatbot.title),betterdocsAIChatbot?.subtitle&&(0,r.createElement)("p",{className:"chat-description"},betterdocsAIChatbot.subtitle)),q&&(0,r.createElement)(d,null),!q&&g&&g?.map(((e,t)=>(0,r.createElement)("div",{key:t,className:`message ${e?.type}`},"sent"===e?.type||"failed"===e?.type?(0,r.createElement)("div",{className:"query"},e?.text,"failed"==e?.type&&(0,r.createElement)("span",{className:"status"},(0,i.__)("Sending Failed","betterdocs-ai-chatbot"))):"received"===e?.type?(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"message-text"},(0,r.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:o.A.sanitize((0,f.xI)(e?.text))}}),(0,r.createElement)("span",{className:"message-received-time"},e.timestamp&&(0,r.createElement)(m,{timestamp:e?.timestamp,timezone:V?.timezone,isNewMesage:b,setNewMessage:x})))):(0,r.createElement)(r.Fragment,null)))),w&&y&&(0,r.createElement)("div",{className:"message typing received message-active"},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"message-text"},(0,r.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:o.A?.sanitize(y)}}),(0,r.createElement)("span",{className:"message-received-time"},(0,r.createElement)(m,{timestamp:I||X,timezone:V?.timezone,isNewMesage:b,setNewMessage:x}))))),w&&!y&&(0,r.createElement)("div",{className:"message typing received message-active"},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"text thinking-dots"},(0,i.__)("Thinking","betterdocs-ai-chatbot"),(0,r.createElement)("span",{className:"dots"})))),T&&"false"!==(0,c.Ri)("showEmailField")&&(0,r.createElement)("div",{className:"message received email-field-wrapper "+(H?"focused":"")},(0,r.createElement)("div",{className:"message-content"},(0,r.createElement)("div",{className:"avatar"},(0,r.createElement)(l,null)),(0,r.createElement)("div",{className:"text"},(0,i.__)("Enter email to keep conversation alive.","betterdocs-ai-chatbot"))),(0,r.createElement)("div",{className:"email-field-container"},(0,r.createElement)("div",{className:"email-field"},(0,r.createElement)("input",{ref:j,type:"email",id:"email",value:z,onChange:e=>{S(e?.target?.value)},onKeyDown:e=>{"Enter"===e?.key&&ne()},placeholder:(0,i.__)("Enter your email","betterdocs-ai-chatbot"),required:!0}),(0,r.createElement)("div",{className:"email-icon",onClick:ne},(0,r.createElement)(h,null))),L&&(0,r.createElement)("div",{className:"error-message-container"},O)))),C&&(0,r.createElement)("div",{className:"thankyou-message-container"},(0,i.__)("Thanks! We should reply in a moment.","betterdocs-ai-chatbot")),(0,r.createElement)("div",{className:"chat-footer"},(0,r.createElement)("div",{className:"message-input"+(T&&"false"!==(0,c.Ri)("showEmailField")?" disabled":"")},(0,r.createElement)("input",{placeholder:(0,i.__)("Type a message...","betterdocs-ai-chatbot"),value:n,onChange:e=>{const t=e.target.value,n=c.o2.sanitize("text",t);s(n)},onKeyDown:e=>{w||"Enter"!==e?.key||e?.shiftKey||(e.preventDefault(),J())},rows:"2"}),(0,r.createElement)("button",{onClick:J,style:w?{pointerEvents:"none",opacity:".6"}:{},disabled:w},(0,r.createElement)(u,null))))))},k=()=>(0,r.createElement)("svg",{width:22,height:24,viewBox:"0 0 22 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"a",fill:"#fff"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"})),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("path",{d:"m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",mask:"url(#a)"}),(0,r.createElement)("path",{d:"M11 7V1",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"1.5"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#00B682",className:"chatbotpath"}),(0,r.createElement)("circle",{cx:11,cy:"1.5",r:"1.5",fill:"#00B682",className:"chatbotpath"})),b=()=>(0,r.createElement)("svg",{width:21,height:24,viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("path",{d:"M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",stroke:"#344054",strokeWidth:"1.5"}),(0,r.createElement)("path",{d:"M6.2 14c2.3 2 5.3 2 7.8 0",stroke:"#344054",strokeWidth:"1.6"}),(0,r.createElement)("path",{d:"M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",stroke:"#344054",strokeWidth:"1.5"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#344054"}),(0,r.createElement)("circle",{cx:"10.1",cy:"1.5",r:"1.5",fill:"#344054"}));(0,s.addFilter)("tab_chatbot_preview","betterdocs/chattab-preview",((e,t)=>(e.push({id:"chatbot",class:"betterdocs-ia-chatbot",type:"tab",title:(0,i.__)("Chatbot","betterdocs-ai-chatbot"),default:!1,icon:betterdocsAIChatbot?.ai_chatbot_icon?.url?(0,r.createElement)("img",{src:betterdocsAIChatbot.ai_chatbot_icon.url,width:"24",height:"24",style:{opacity:"chatbot"===t?1:.7,filter:"chatbot"===t?"brightness(1.1)":"none"}}):"chatbot"===t?(0,r.createElement)(k,null):(0,r.createElement)(b,null),component:g,showTab:!0,showTabInComponent:!0}),e)))})();