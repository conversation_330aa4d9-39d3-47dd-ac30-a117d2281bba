=== BetterDocs AI Chatbot ===
Contributors: wpdevteam
Donate link: https://wpdeveloper.com
Tags: knowledge base, docs, documentation, documents, faq page, doc, knowledge, table of content, TOC, knowledgebase, faqs, doc page, best documentation plugin, support, customer support, aichatbot
Requires at least: 5.0
Tested up to: 6.7
Requires PHP: 7.0
Stable tag: 1.2.1
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

A better documentation and knowledgebase plugin for WordPress

== Description ==

Do you want to reduce your support pressure immediately? How about you creating a stunning and resourceful knowledge base for your customers? 🤔

82% of customers prefer to support through an online knowledge base and actually get annoyed to create support tickets as its lengthy process. So creating an informative Documentation page can help to enhance your customer experience.

But how do you create a stunning docs page easily in WordPress site without any coding? Well, we’ve got you covered. 😎

## 📒 Create Stunning Knowledge Base To Scale Customer Support ##

[BetterDocs](https://betterdocs.co/) will help you to create and organize your documentation page in a beautiful way that will make your visitors find any help article easily. It will facilitate your client to take faster decisions and get helped on the spot by self-servicing instead of avoiding the lengthy conversation.

## 🔥 Power Up Knowledge Base To Reduce Support Tickets ##

BetterDocs will help you to give the exact solution and encourage self-servicing to reduce support workload.

### 🌟 Top Features ###

- Choose stunning premade template design to organize your knowledge Base
- Sort posts in Table of Content or Sticky TOC to provide absolute user experience
- Customize documentation page in advance by adding shortcode & page builder widgets
- In-built advanced live search will help visitors to get the exact docs solution
- Integrated with Analytics to track and evaluate the performance


## 📋 Interactive Table of Content (TOC) ##
Sort multiple posts using TOC or Sticky TOC and give exact docs solution to your visitors on spot & give absolute support. This stunning TOC moves with your scroll, so your visitors can always go to other pages easily.

## ⚙️ Advanced Integration With Analytics (PRO) ##

Track and evaluate activities on your documentation page and improve customer experience. Also, analyze the site traffic to get insights about your Knowledge Base.

## 🚀 Backed By A Trusted Team ##

This Documentation plugin is brought to you by the team behind [WPDeveloper](https://wpdeveloper.com/), a dedicated marketplace for WordPress, trusted by 400,000+ happy users.

## 👨‍💻 DOCUMENTATION AND SUPPORT ##

- For documentation and tutorials go to our [Documentation](https://betterdocs.co/docs/)
- If you have any more questions, visit our support on the Plugin’s Forum
- For more information about features, FAQs and documentation, check out our website at [BetterDocs](https://betterdocs.co/)

## 💙 Loved BetterDocs? ##

- Join our [Facebook Group](https://www.facebook.com/groups/wpdeveloper.com/)
- Learn from our tutorials on [Youtube Channel](https://wpdeveloper.com/go/youtube-channel)
- Or rate us on WordPress

## 🔥 WHAT’S NEXT ##

If you like this docs plugin, then consider checking out our other projects:

[Essential Addons For Elementor](https://wordpress.org/plugins/essential-addons-for-elementor-lite/) – Most popular Elementor extensions with 300,000+ active users in the WordPress repository.
[NotificationX](https://wordpress.org/plugins/notificationx/) – Best Social Proof & FOMO Marketing Solution
[WP Scheduled Posts](https://wordpress.org/plugins/wp-scheduled-posts/) – Complete solution for WordPress Post Scheduling to manage schedules through an editorial calendar.

Visit WPDeveloper to learn more about how to do better in WordPress with [Help Tutorial, Tips & Tricks](https://wpdeveloper.com/blog)!


== Installation ==

Follow the following steps to install the plugin.

e.g.

1. Upload `betterdocs-ai-chatbot.zip` to the `/wp-content/plugins/` directory
1. Activate the plugin through the 'Plugins' menu in WordPress
1. Place `<?php do_action('plugin_name_hook'); ?>` in your templates

== Frequently Asked Questions ==

= Does it work with any WordPress theme? =

Yes, it will work with any standard WordPress theme.


== Screenshots ==


== Changelog ==

= 1.2.1 - 26/05/2025 =

- Improvement: The AI chatbot now responds based on the site's active language using translation settings.
- Improvement: The chatbot will no longer include URLs for posts that are not publicly viewable (e.g., private posts, - betterdocs_faq, drafts, etc.).
- Fixed: The chatbot did not reflect new or updated docs even after clicking "Sync New Posts".
- Fixed: The chatbot failed to save repeated user messages in the chat history.
- Few minor bug fixes and improvements.

= 1.2.0 - 08/05/2025 =

- Added: Cross-Domain Support.
- Improvement: Migrated all chatbot responses from admin-ajax to REST API for better performance and scalability.
- Fixed: ‘Save’ button disappearing in settings panel when the AI Chatbot was disabled.
- Fixed: Chatbot header color not updating based on admin settings.
- Fixed: User questions were not recorded in the chatbot history if the server responded without an answer.
- Few minor bug fixes and improvements.

= 1.1.1 - 17/03/2025 =
- Few minor bug fixes and improvements.

= 1.1.0 - 17/03/2025 =
- Added: Option to select Retrievable Post Types.
- Added: Option to change Chatbot Title and Subtitle.
- Fixed: Failed to save chatbot data in certain cases.
- Fixed: Token generation failure in certain cases.
- Fixed: Chat timestamps always being displayed in UTC time.
- Few minor bug fixes and improvements.

= 1.0.1 - 03/02/2025 =
- Improved: Implemented real-time updates for chat timestamps. 
- Improved: Chat order now follows LIFO (Last In, First Out) in the admin panel.
- Added: Customization option in settings for the welcome message.
- Added: .pot file for multilingual support.
- Added: Copy-to-clipboard icon for user email.
- Few minor bug fixes and improvements.

= 1.0.0 - 14/01/2025 =
- Initial Release


[See changelog for all versions](https://betterdocs.co/changelog/).


== Upgrade Notice ==

