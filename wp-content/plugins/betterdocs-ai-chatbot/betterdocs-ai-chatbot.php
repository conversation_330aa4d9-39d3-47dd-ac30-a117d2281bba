<?php

/**
 * Plugin Name:       BetterDocs AI Chatbot
 * Plugin URI:        https://betterdocs.co/
 * Description:       A powerful addon for BetterDocs to provide instant and advanced AI-driven answers from your knowledge base while reducing queries with the smart support system. Your website visitors can quickly and accurately get solutions by interacting with the BetterDocs AI chatbot.
 * Version:           1.2.1
 * Author:            WPDeveloper
 * Author URI:        https://wpdeveloper.com
 * License:           GPL-3.0+
 * License URI:       http://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       betterdocs-ai-chatbot
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
use WPDeveloper\BetterDocsChatbot\Plugin;


defined( 'ABSPATH' ) || exit;


define( 'BETTERDOCS_CHATBOT_FILE', __FILE__ );
define( 'BETTERDOCS_CHATBOT_VERSION', '1.2.1' );

define( 'BETTERDOCS_CHATBOT_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
define( 'BETTERDOCS_ASSETS_DIR_PATH', BETTERDOCS_CHATBOT_ROOT_DIR_PATH . 'assets/' );
define('BETTERDOCS_DOCS_PER_PAGE', 20);
define('BETTERDOCS_AI_CHATBOT_REQUEST_TIMEOUT', 300);
define( 'PLUGIN_HASH_KEY', 'bOKfmtGHgAHKJW3xIEcCUkoOHOojVh4C' ); // Use a long, random string here.


require_once __DIR__ . '/vendor/autoload.php';

function betterdocs_chatbot() {
	return Plugin::get_instance();
}

betterdocs_chatbot();
