import { addFilter } from "@wordpress/hooks";
import { __ } from "@wordpress/i18n";
import Chatbot from "./Chatbot";
import ChatbotActiveIcon from "./components/icons/ChatbotActiveIcon.js";
import ChatbotTabIcon from "./components/icons/ChatbotTabIcon.js";

addFilter('tab_chatbot_preview', 'betterdocs/chattab-preview', (tabs, activeTabClass) => {
    // Determine which icon to use based on whether custom icon is uploaded and tab state
    const getIcon = () => {
        if (betterdocsAIChatbot?.ai_chatbot_icon?.url) {
            // Use uploaded icon for both active and inactive states
            // You could add opacity or filter for active state if needed
            return (
                <img 
                    src={betterdocsAIChatbot.ai_chatbot_icon.url} 
                    width="24" 
                    height="24" 
                    style={{
                        opacity: activeTabClass === 'chatbot' ? 1 : 0.7,
                        filter: activeTabClass === 'chatbot' ? 'brightness(1.1)' : 'none'
                    }}
                />
            );
        } else {
            // Use default SVG icons
            return activeTabClass === 'chatbot' ? (
                <ChatbotActiveIcon />
            ) : (
                <ChatbotTabIcon />
            );
        }
    };

    tabs.push({
        id: "chatbot",
        class: "betterdocs-ia-chatbot",
        type: "tab",
        title: __("Chatbot", "betterdocs-ai-chatbot"),
        default: false,
        icon: getIcon(),
        component: Chatbot,
        showTab: true,
        showTabInComponent: true,
    });
    
    return tabs;
});