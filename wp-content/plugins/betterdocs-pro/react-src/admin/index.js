import React, {act, Suspense} from "react";
import {addFilter} from "@wordpress/hooks";
import {__} from "@wordpress/i18n";
import BetterDocsAnalytics from "./Analytics";
import { decodeEntities } from "@wordpress/html-entities";

//mkb filter
addFilter(
    "betterdocs_kb_filter_section",
    "betterdocs-pro",
    (defaultValue, [Component, mkbFilter, setMkbFilterValue, page, postStatus]) => {
        if (page !== 'betterdocs-admin' || postStatus === 'trash') {
            return null;
        }

        return (
            <Component
                heading={"Knowledge Base"}
                selectedValue={mkbFilter}
                setValue={setMkbFilterValue}
            />
        );
    }
);

//mkb table header
addFilter('mkb_table_header', 'betterdocs-pro', (defaultValue) => {
    return (
        <th className="knowledge-base">
                <span className="betterdocs-dashboard-list-header-title">
                    {__("Knowledge Base", "betterdocs-pro")}
                </span>
        </th>
    );
});

addFilter('mkb_table_data', 'betterdocs-pro', (defaultValue, [Component, knowledgebaseInfo]) => {
    return <td>
        <Component knowledgebase={knowledgebaseInfo}/>
    </td>;
});

//quick edit mkb filter
addFilter(
    "quick_edit_mkb_filter",
    "betterdocs-pro",
    (defaultData, [docData, setQuickEditData, MultiSelect]) => {
        return (
            <MultiSelect
                name="knowledge_base"
                endpoint="wp/v2/knowledge_base"
                doc={docData}
                setQuickEditData={setQuickEditData}
                label={__("Knowledge Base", "betterdocs-pro")}
                placeholder={__("Search Knowlegdebase", "betterdocs-pro")}
            />
        );
    }
);

const ComponentLoader = ({Component, fallback}) => {
    return (
        <Suspense fallback={fallback ?? "Loading..."}>
            <Component/>
        </Suspense>
    );
};

addFilter(
    "betterdocs_admin_header_title",
    "betterdocs-pro",
    (content, page) => {
        switch (page) {
            case "betterdocs-analytics":
                return "Analytics";
        }

        return content;
    }
);

const analyticsRoute = {
    path: "betterdocs-analytics",
    element: <ComponentLoader Component={BetterDocsAnalytics}/>,
    exact: true,
};

addFilter("betterdocs_routes", "betterdocs-pro", (routes) => {
    const filteredRoutes = routes.filter(
        (route) => route.path !== analyticsRoute.path
    );
    return [...filteredRoutes, analyticsRoute];
});

addFilter('contextMenuListTable', 'betterdocs-pro', (menus, doc) => {
    const { doc_category, knowledge_base } = doc;

    const doc_categories = betterdocs_pro_admin?.access_control?.control_access_restrict_doc_category ?? [];
	const docs           = betterdocs_pro_admin?.access_control?.control_access_restrict_docs ?? [];
	const multiple_kb	 = betterdocs_pro_admin?.multiple_kb ?? false;

	const mkb_categories 		   = betterdocs_pro_admin?.access_control?.control_access_restrict_multiple_kb;
	const mkb_based_doc_categories = betterdocs_pro_admin?.access_control?.control_access_restrict_doc_category_kb;
	const mkb_based_docs           = betterdocs_pro_admin?.access_control?.control_access_restrict_docs_kb;

    if( !multiple_kb && doc_categories['all'] != undefined ) {
        let action = doc_categories['all'];
        if( action == 'view' ) {
            delete menus['edit'];
            delete menus['quickEdit'];
            delete menus['trash'];
        } else if( action == 'edit-only' ) {
            delete menus['trash'];
            delete menus['view'];
        }
    } else if( !multiple_kb && doc_category?.length > 0 && typeof doc_categories == 'object' && Object.values(doc_categories)?.length > 0 && typeof docs == 'object' && Object.values(docs)?.length == 0 ) {
		doc_category?.map((category_id) => {
			if( doc_categories[category_id] != undefined && doc_categories[category_id] == 'view' ) {
				delete menus['edit'];
				delete menus['quickEdit'];
				delete menus['trash'];
			} else if( doc_categories[category_id] != undefined && doc_categories[category_id] == 'edit-only' ) {
				delete menus['trash'];
				delete menus['view'];
			}
		})
	} else if( !multiple_kb && doc_category?.length > 0 && typeof doc_categories == 'object' && Object.values(doc_categories)?.length > 0 && typeof docs == 'object' && Object.values(docs)?.length > 0 ) {
		if( docs[doc?.id] != undefined && docs[doc?.id] == 'view' ) {
			delete menus['edit'];
			delete menus['quickEdit'];
			delete menus['trash'];
		} else if( docs[doc?.id] != undefined && docs[doc?.id] == 'edit-only' ) {
			delete menus['trash'];
			delete menus['view'];
		}
	}

	if( multiple_kb ){
		let does_current_mkb_has_view 	       = false;
		let does_current_doc_category_has_view = false;
		let does_current_doc_has_view	       = false;

		let does_current_mkb_has_edit_only 			= false;
		let does_current_doc_category_has_edit_only = false;
		let does_current_doc_has_edit_only 			= false;

		for( let index in knowledge_base ){
			if( mkb_categories != undefined && mkb_categories[knowledge_base[index]] != undefined && mkb_categories[knowledge_base[index]] == 'view' ) {
				does_current_mkb_has_view = true;
				break;
			} else if( mkb_categories != undefined && mkb_categories[knowledge_base[index]] != undefined && mkb_categories[knowledge_base[index]] == 'edit-only' ) {
				does_current_mkb_has_edit_only = true;
				break;
			}
		}

		for( let index in doc_category ) {
			if( mkb_based_doc_categories != undefined && mkb_based_doc_categories[doc_category[index]] != undefined && mkb_based_doc_categories[doc_category[index]] == 'view' ) {
				does_current_doc_category_has_view = true;
				break;
			} else if( mkb_based_doc_categories != undefined && mkb_based_doc_categories[doc_category[index]] != undefined && mkb_based_doc_categories[doc_category[index]] == 'edit-only'  ) {
				does_current_doc_category_has_edit_only = true;
				break;
			}
		}

		if( mkb_based_docs != undefined && mkb_based_docs[doc?.id] != undefined && mkb_based_docs[doc?.id] == 'view' ) {
			does_current_doc_has_view = true;
		} else if( mkb_based_docs != undefined && mkb_based_docs[doc?.id] != undefined && mkb_based_docs[doc?.id] == 'edit-only' ) {
			does_current_doc_has_edit_only = true;
		}

		if( does_current_mkb_has_view || ( does_current_mkb_has_view && does_current_doc_category_has_view ) || ( does_current_mkb_has_view && does_current_doc_category_has_view && does_current_doc_has_view ) ) {
			delete menus['edit'];
			delete menus['quickEdit'];
			delete menus['trash'];
		} else if( does_current_mkb_has_edit_only || ( does_current_mkb_has_edit_only && does_current_doc_category_has_edit_only ) || ( does_current_mkb_has_edit_only && does_current_doc_category_has_edit_only && does_current_doc_has_edit_only ) ) {
			delete menus['trash'];
			delete menus['view'];
		}
	}
    return {...menus};
});

addFilter('dashboardGridViewItem', 'betterdocs-pro', (actions, doc, term, termId) => {
	const doc_categories 		   = betterdocs_pro_admin?.access_control?.control_access_restrict_doc_category ?? [];
	const docs           		   = betterdocs_pro_admin?.access_control?.control_access_restrict_docs ?? [];
	const multiple_kb	 		   = betterdocs_pro_admin?.multiple_kb ?? false;
	const mkb_categories 		   = betterdocs_pro_admin?.access_control?.control_access_restrict_multiple_kb;
	const mkb_based_doc_categories = betterdocs_pro_admin?.access_control?.control_access_restrict_doc_category_kb;
	const mkb_based_docs           = betterdocs_pro_admin?.access_control?.control_access_restrict_docs_kb;

    if( !multiple_kb && doc_categories['all'] != undefined ) {
        let action = doc_categories['all'];
        if( action == 'view' ) {
            delete actions['editAction'];
            delete actions['deleteAction'];
        } else if( action == 'edit-only' ) {
            delete actions['deleteAction'];
            delete actions['viewAction'];
        }
    } else if( !multiple_kb && doc_categories[termId] != undefined && doc_categories[termId] == 'view' && Object.values(docs)?.length == 0 ) {
        delete actions['editAction'];
        delete actions['deleteAction'];
        actions['editDoc'] = <a href="#">{decodeEntities(doc?.title)}<span className="status">{` - ${doc?.status?.charAt(0).toUpperCase()}${doc?.status?.slice(1)}`}</span></a>;
    } else if( !multiple_kb && doc_categories[termId] != undefined && doc_categories[termId] == 'view' &&  Object.values(docs)?.length > 0 && docs[doc?.id] == 'view') {
        delete actions['editAction'];
        delete actions['deleteAction'];
        actions['editDoc'] = <a href="#">{decodeEntities(doc?.title)}<span className="status">{` - ${doc?.status?.charAt(0).toUpperCase()}${doc?.status?.slice(1)}`}</span></a>;
    } else if( !multiple_kb && doc_categories[termId] != undefined && doc_categories[termId] == 'edit-only' &&  Object.values(docs)?.length == 0 ) {
        delete actions['deleteAction'];
        delete actions['viewAction'];
    } else if( !multiple_kb && doc_categories[termId] != undefined && doc_categories[termId] == 'edit-only' &&  Object.values(docs)?.length > 0 && docs[doc?.id] == 'edit-only') {
        delete actions['deleteAction'];
        delete actions['viewAction'];
    }

    if( multiple_kb ) {
        let does_current_mkb_has_view 	       = false;
        let does_current_doc_category_has_view = false;
        let does_current_doc_has_view	       = false;

        let does_current_mkb_has_edit_only = false;
        let does_current_doc_category_has_edit_only = false;
        let does_current_doc_has_edit_only = false;

        const { knowledge_base_ids, id } = term;

        for( let index in knowledge_base_ids ) {
            if( mkb_categories != undefined && mkb_categories[knowledge_base_ids[index]] != undefined && mkb_categories[knowledge_base_ids[index]] == 'view' ){
                does_current_mkb_has_view = true;
                break;
            } else if( mkb_categories != undefined && mkb_categories[knowledge_base_ids[index]] != undefined && mkb_categories[knowledge_base_ids[index]] == 'edit-only' ) {
                does_current_mkb_has_edit_only = true;
            }
        }

        if( mkb_based_doc_categories != undefined && mkb_based_doc_categories[id] != undefined && mkb_based_doc_categories[id] == 'view' ) {
            does_current_doc_category_has_view = true;
        } else if( mkb_based_doc_categories != undefined && mkb_based_doc_categories[id] != undefined && mkb_based_doc_categories[id] == 'edit-only' ) {
            does_current_doc_category_has_edit_only = true;
        }

        if( mkb_based_docs != undefined && mkb_based_docs[doc?.id] != undefined && mkb_based_docs[doc?.id] == 'view' ) {
            does_current_doc_has_view = true;
        } else if( mkb_based_docs != undefined && mkb_based_docs[doc?.id] != undefined && mkb_based_docs[doc?.id] == 'edit-only' ) {
            does_current_doc_has_edit_only = true;
        }

        if( does_current_mkb_has_view || ( does_current_mkb_has_view && does_current_doc_category_has_view ) || ( does_current_mkb_has_view && does_current_doc_category_has_view && does_current_doc_has_view ) ) {
            delete actions['editAction'];
            delete actions['deleteAction'];
            actions['editDoc'] = <a href="#">{decodeEntities(doc?.title)}<span className="status">{` - ${doc?.status?.charAt(0).toUpperCase()}${doc?.status?.slice(1)}`}</span></a>;
        } else if( does_current_mkb_has_edit_only || ( does_current_mkb_has_edit_only && does_current_doc_category_has_edit_only ) || ( does_current_mkb_has_edit_only && does_current_doc_category_has_edit_only && does_current_doc_has_edit_only ) ) {
            delete actions['deleteAction'];
            delete actions['viewAction'];
        }
    }

    return {...actions};
});
