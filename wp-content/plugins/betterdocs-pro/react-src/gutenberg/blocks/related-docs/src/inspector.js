import { __ } from "@wordpress/i18n";

import {
    PanelBody,
    TextControl,
    BaseControl,
    ToggleControl,
    SelectControl,
    TabPanel,
} from "@wordpress/components";

import { useEffect } from "react";
import { select } from "@wordpress/data";
import objAttributes from "./attributes";
import { InspectorControls } from "@wordpress/block-editor";
import {
    RELATED_DOCS_BOX_WRAPPER_BACKGROUND,
    RELATED_DOCS_BOX_WRAPPER_PADDING,
    RELATED_DOCS_BOX_WRAPPER_MARGIN,
    RELATED_DOCS_HEADING_PADDING,
    RELATED_DOCS_HEADING_MARGIN,
    RELATED_DOCS_LIST_PADDING,
    RELATED_DOCS_LIST_MARGIN,
    RELATED_DOCS_HEADING_BACKGROUND,
    RELATED_DOCS_LIST_BACKGROUND,
    LAYOUT,
} from "./constants";
import BackgroundControl from "../../../util/background-control";
import TypographyDropdown from "../../../util/typography-control-v2";
import ColorControl from "../../../util/color-control";
import {
    related_docs_heading_typography,
    related_docs_list_typography,
} from "./typographyPrefixConstants";
import ResponsiveDimensionsControl from "../../../util/dimensions-control-v2";

const Inspector = ({ attributes, setAttributes }) => {
    const { resOption, headingColor, listColor, relatedDocsHeading, layout } =
        attributes;
    const editorStoreForGettingPreivew =
        betterdocs_style_handler.editor_type === "edit-site"
            ? "core/edit-site"
            : "core/editor";

    useEffect(() => {
        // this is for setting the resOption attribute to desktop/tab/mobile depending on the added 'eb-res-option-' class only the first time once
        setAttributes({
            resOption: select(
                editorStoreForGettingPreivew
            ).__experimentalGetPreviewDeviceType(),
        });
    }, []);

    const resRequiredProps = {
        setAttributes,
        resOption,
        attributes,
        objAttributes,
    };

    return (
        <InspectorControls key="controls">
            <div className="eb-panel-control">
                <TabPanel
                    className="eb-parent-tab-panel"
                    activeClass="active-tab"
                    tabs={[
                        {
                            name: "general",
                            title: __("General", "betterdocs"),
                            className: "eb-tab general",
                        },
                        {
                            name: "styles",
                            title: __("Style", "betterdocs-pro"),
                            className: "eb-tab styles",
                        }
                    ]}
                >
                    {(tab) => (
                        <>
                            <div className={"eb-tab-controls " + tab.name}>
                                {tab.name == "general" && (
                                    <PanelBody
                                        title={__("General", "betterdocs-pro")}
                                    >

                                        <SelectControl
                                            label={__("Select layout", "betterdocs")}
                                            value={layout}
                                            options={LAYOUT}
                                            onChange={(newLayout) =>
                                                setAttributes({
                                                    layout: newLayout,
                                                })
                                            }
                                            __next40pxDefaultSize
                                            __nextHasNoMarginBottom
                                        />
                                        <TextControl
                                            label={__(
                                                "Heading",
                                                "betterdocs-pro"
                                            )}
                                            value={relatedDocsHeading}
                                            onChange={(relatedDocsHeading) =>
                                                setAttributes({
                                                    relatedDocsHeading:
                                                        relatedDocsHeading,
                                                })
                                            }
                                        />
                                    </PanelBody>
                                )}
                                {tab.name === "styles" && (
                                    <>
                                        <PanelBody
                                            title={__(
                                                "Wrapper Box",
                                                "betterdocs-pro"
                                            )}
                                        >
                                            <BackgroundControl
                                                controlName={
                                                    RELATED_DOCS_BOX_WRAPPER_BACKGROUND
                                                }
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                noOverlay={true}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Padding",
                                                    "betterdocs"
                                                )}
                                                resRequiredProps={resRequiredProps}
                                                controlName={RELATED_DOCS_BOX_WRAPPER_PADDING}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Margin",
                                                    "betterdocs"
                                                )}
                                                resRequiredProps={resRequiredProps}
                                                controlName={RELATED_DOCS_BOX_WRAPPER_MARGIN}
                                            />
                                        </PanelBody>
                                        <PanelBody
                                            title={__(
                                                "Heading",
                                                "betterdocs-pro"
                                            )}
                                        >
                                            <BackgroundControl
                                                controlName={
                                                    RELATED_DOCS_HEADING_BACKGROUND
                                                }
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                noOverlay={true}
                                            />
                                            <ColorControl
                                                label={__(
                                                    "Color",
                                                    "betterdocs-pro"
                                                )}
                                                color={headingColor}
                                                onChange={(headingColor) =>
                                                    setAttributes({
                                                        headingColor:
                                                            headingColor,
                                                    })
                                                }
                                            />
                                            <TypographyDropdown
                                                baseLabel={__(
                                                    "Typography",
                                                    "betterdocs-pro"
                                                )}
                                                typographyPrefixConstant={
                                                    related_docs_heading_typography
                                                }
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                defaultFontSize={15}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Padding",
                                                    "betterdocs"
                                                )}
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                controlName={
                                                    RELATED_DOCS_HEADING_PADDING
                                                }
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Margin",
                                                    "betterdocs"
                                                )}
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                controlName={
                                                    RELATED_DOCS_HEADING_MARGIN
                                                }
                                            />
                                        </PanelBody>
                                        <PanelBody
                                            title={__("List", "betterdocs-pro")}
                                        >
                                            <BackgroundControl
                                                controlName={
                                                    RELATED_DOCS_LIST_BACKGROUND
                                                }
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                noOverlay={true}
                                            />
                                            <ColorControl
                                                label={__(
                                                    "Color",
                                                    "betterdocs-pro"
                                                )}
                                                color={listColor}
                                                onChange={(listColor) =>
                                                    setAttributes({
                                                        listColor: listColor,
                                                    })
                                                }
                                            />
                                            <TypographyDropdown
                                                baseLabel={__(
                                                    "Typography",
                                                    "betterdocs-pro"
                                                )}
                                                typographyPrefixConstant={
                                                    related_docs_list_typography
                                                }
                                                resRequiredProps={
                                                    resRequiredProps
                                                }
                                                defaultFontSize={15}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Padding",
                                                    "betterdocs-pro"
                                                )}
                                                resRequiredProps={resRequiredProps}
                                                controlName={RELATED_DOCS_LIST_PADDING}
                                            />
                                            <ResponsiveDimensionsControl
                                                baseLabel={__(
                                                    "Margin",
                                                    "betterdocs-pro"
                                                )}
                                                resRequiredProps={resRequiredProps}
                                                controlName={
                                                    RELATED_DOCS_LIST_MARGIN
                                                }
                                            />
                                        </PanelBody>
                                    </>
                                )}
                            </div>
                        </>
                    )}
                </TabPanel>
            </div>
        </InspectorControls>
    );
};

export default Inspector;
