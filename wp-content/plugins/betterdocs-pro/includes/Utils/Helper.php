<?php

namespace WPDeveloper\BetterDocsPro\Utils;

use WPDeveloper\BetterDocsChatbot\Dependencies\WPDeveloper\WPBGProcess\AIChatbotBackgroundProcessing;


class Helper {
    public static function get_plugins( $plugin_basename = null ) {
        if ( ! function_exists( 'get_plugins' ) ) {
            include_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugins = get_plugins();
        return $plugin_basename == null ? $plugins : isset( $plugins[$plugin_basename] );
    }

    public static function is_plugin_active( $plugin_basename ) {
        if ( ! function_exists( 'is_plugin_active' ) ) {
            include_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        return is_plugin_active( $plugin_basename );
    }

    public static function strip_javascript_inline($html) {
        // Remove script tags and their contents
        $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html);

        // Remove javascript: protocols but keep the anchor tags
        $html = preg_replace('/href\s*=\s*(["\'])\s*javascript:.*?\1/i', 'href="#"', $html);

        // Remove all event handlers (onclick, onload, etc) but keep the elements
        $html = preg_replace('/\s+on\w+\s*=\s*(["\'])?[^"\']*\1?/i', '', $html);

        // Remove any inline javascript: in attributes
        $html = preg_replace('/javascript\s*:/i', '', $html);

        return $html;
    }

    public static function AIChatbot(){
        new AIChatbotBackgroundProcessing();
    }

    public static function remove_all_from_content_restriction_for_doc_categories( &$terms ) {
        foreach( $terms as $term_id => $term_status ) {
            if( $term_id == 'all' ) {
                unset( $terms['all'] );
            }
        }
    }
}
