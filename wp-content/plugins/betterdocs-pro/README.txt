=== BetterDocs Pro ===
Contributors: wpdevteam, re_enter_rupok
Donate link: https://wpdeveloper.com
Tags: knowledge base, docs, documentation, documents, faq page, doc, knowledge, table of content, TOC, knowledgebase, faqs, doc page, best documentation plugin, support, customer support
Requires at least: 5.0
Tested up to: 6.7
Requires PHP: 7.0
Stable tag: 3.5.8
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

A better documentation and knowledgebase plugin for WordPress

== Description ==

Do you want to reduce your support pressure immediately? How about you creating a stunning and resourceful knowledge base for your customers? 🤔

82% of customers prefer to support through an online knowledge base and actually get annoyed to create support tickets as its lengthy process. So creating an informative Documentation page can help to enhance your customer experience.

But how do you create a stunning docs page easily in WordPress site without any coding? Well, we’ve got you covered. 😎

## 📒 Create Stunning Knowledge Base To Scale Customer Support ##

[BetterDocs](https://betterdocs.co/) will help you to create and organize your documentation page in a beautiful way that will make your visitors find any help article easily. It will facilitate your client to take faster decisions and get helped on the spot by self-servicing instead of avoiding the lengthy conversation.

## 🔥 Power Up Knowledge Base To Reduce Support Tickets ##

BetterDocs will help you to give the exact solution and encourage self-servicing to reduce support workload.

### 🌟 Top Features ###

- Choose stunning premade template design to organize your knowledge Base
- Sort posts in Table of Content or Sticky TOC to provide absolute user experience
- Customize documentation page in advance by adding shortcode & page builder widgets
- In-built advanced live search will help visitors to get the exact docs solution
- Integrated with Analytics to track and evaluate the performance


## 📋 Interactive Table of Content (TOC) ##
Sort multiple posts using TOC or Sticky TOC and give exact docs solution to your visitors on spot & give absolute support. This stunning TOC moves with your scroll, so your visitors can always go to other pages easily.

## ⚙️ Advanced Integration With Analytics (PRO) ##

Track and evaluate activities on your documentation page and improve customer experience. Also, analyze the site traffic to get insights about your Knowledge Base.

## 🚀 Backed By A Trusted Team ##

This Documentation plugin is brought to you by the team behind [WPDeveloper](https://wpdeveloper.com/), a dedicated marketplace for WordPress, trusted by 400,000+ happy users.

## 👨‍💻 DOCUMENTATION AND SUPPORT ##

- For documentation and tutorials go to our [Documentation](https://betterdocs.co/docs/)
- If you have any more questions, visit our support on the Plugin’s Forum
- For more information about features, FAQs and documentation, check out our website at [BetterDocs](https://betterdocs.co/)

## 💙 Loved BetterDocs? ##

- Join our [Facebook Group](https://www.facebook.com/groups/wpdeveloper.com/)
- Learn from our tutorials on [Youtube Channel](https://wpdeveloper.com/go/youtube-channel)
- Or rate us on WordPress

## 🔥 WHAT’S NEXT ##

If you like this docs plugin, then consider checking out our other projects:

[Essential Addons For Elementor](https://wordpress.org/plugins/essential-addons-for-elementor-lite/) – Most popular Elementor extensions with 300,000+ active users in the WordPress repository.
[NotificationX](https://wordpress.org/plugins/notificationx/) – Best Social Proof & FOMO Marketing Solution
[WP Scheduled Posts](https://wordpress.org/plugins/wp-scheduled-posts/) – Complete solution for WordPress Post Scheduling to manage schedules through an editorial calendar.

Visit WPDeveloper to learn more about how to do better in WordPress with [Help Tutorial, Tips & Tricks](https://wpdeveloper.com/blog)!


== Installation ==

Follow the following steps to install the plugin.

e.g.

1. Upload `betterdocs.php` to the `/wp-content/plugins/` directory
1. Activate the plugin through the 'Plugins' menu in WordPress
1. Place `<?php do_action('plugin_name_hook'); ?>` in your templates

== Frequently Asked Questions ==

= Does it work with any WordPress theme? =

Yes, it will work with any standard WordPress theme.


== Screenshots ==


== Changelog ==

= 3.5.8 - 26/05/2025 =

- Added: Option to enable or disable individual sections and tabs in the Instant Answer settings.
- Improvement: Updated cross-domain support to follow the configuration of the new Instant Answer settings.
- Few minor bug fixes and improvements.

= 3.5.7 - 19/05/2025 =

- Fixed: "Enable Non-Latin Alphabetical Order" setting was not working correctly for the Encyclopedia Elementor widget.
- Few minor bug fixes and improvements.

= 3.5.6 - 08/05/2025 =

- Added: Cross-Domain Support for AI Chatbot in Instant Answer.
- Few minor bug fixes and improvements


= 3.5.5 - 22/04/2025 =

- Added: Option to show/hide alphabetical items on the Encyclopedia page.
- Added: Glossary Single Template pattern support for block-based themes.
- Added: Classic UI Button in the Glossaries Builder.
- Added: Glossaries template support for Elementor Theme Builder.
- Fixed: Redirect URL issue in the Internal Knowledge Base.
- Fixed: WPML translation issue for Instant Answer Single Docs.
- Fixed: Attachment display issues when editing Single Docs with Elementor.
- Fixed: Category visibility issue for the "BetterDocs Tab View List" widget in Edit Mode.
- Few minor bug fixes and improvements.

= 3.5.4 - 17/03/2025 =
- Few minor bug fixes and improvements.

= 3.5.3 - 12/02/2025 =
- Added: FAQ menu visibility control based on user roles.
- Added: Encyclopedia Retro layout for blocks, widgets, and templates.
- Added: Option to download Analytics search data in CSV format.
- Few minor bug fixes and improvements.

= 3.5.2 - 03/02/2025 =
 - Fixed: Removed deprecated ".elementor-widget-container" selector from the CSS file.
 - Few minor bug fixes and improvements.

= 3.5.1 - 21/01/2025 =
 - Fixed: Restricted doc visibility on popular docs.
 - Fixed: Rendering incorrect MKB category on single doc breadcrumb.
 - Fixed: Single Glossaries retaining inline styles.
 - Few minor bug fixes and improvements.

= 3.5.0 - 14/01/2025 =
- Added: AI Chatbot Implementation inside Instant Answers
- Few Minor Bug Fixes & Improvements

= 3.4.11 - 05/12/2024 =
- Fixed: Throwing Fatal error in PHP 7.2
- Few minor bug fixes and improvements

= 3.4.10 - 20/11/2024 =
- Fixed: Conflict with the Essential Blocks “Accordion block”.
- Few minor bug fixes and improvements

= 3.4.9 - 14/11/2024 =
- Added: Sleek Layout for the Multiple KB block
- Added: Multiple KB Sleek Layout pattern
- Improvement: Glossaries performance
- Fixed: Internal KB restriction and search not working
- Few minor bug fixes and improvements

= 3.4.8 - 10/10/2024 =
- Added: Hamburger navigation for Sidebar and Table of Contents on mobile devices in pro layouts.
- Fixed: Related docs not showing all documents from the specified category.
- Few minor bug fixes and improvements.

= 3.4.7 - 01/10/2024 =
- Added: New Sleek layout for Multiple KB in both Customizer and Elementor widgets.
- Few minor bug fixes and improvements

= 3.4.6 - 22/09/2024 =
- Fixed: Glossaries not working with Cyrillic alphabets.
- Fixed: Glossary tooltip not showing popup texts.
- Fixed: Enabling Encyclopedia creates a style issue in the sidebar.
- Fixed: Glossary formatting issue.
- Fixed: User role "author" cannot check the analytics, showing a message "Sorry, No Data Found. Please try applying different filters."
- Improvement: Instant answer users can now close the IA by pressing the ESC key.
- Few minor bug fixes and improvements

= 3.4.5 - 13/08/2024 =
- Fixed: Popular Docs widget displays drafted docs in the list.
- Improvement: Added advanced search to the search block.
- Improvement: Added "Box Per Tabs" option to the BetterDocs Tab View List Elementor widget.
- Fixed: Admin Dashboard “Select Knowledge Base” not displaying KBs after 10 KBs.
- Fixed: Restricted Internal KB categories appearing in the advanced search Category dropdown.
- Fixed: Restricted Internal KB categories appearing in Instant Answer.
- Fixed: Formatting issues in BetterDocs Glossary.
- Few minor bug fixes and improvements

= 3.4.4 - 25/07/2024 =
- Fixed - Uncaught TypeError: array_merge(): Argument #1 must be of type array in ContentRestrictions.php
- Few minor bug fixes and improvements

[See changelog for all versions](https://betterdocs.co/changelog/).


== Upgrade Notice ==

