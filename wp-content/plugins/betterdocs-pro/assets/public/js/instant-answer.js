/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./Pages/DocListFaq.js":
/*!*****************************!*\
  !*** ./Pages/DocListFaq.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Api/globalQueryApi */ "./components/Api/globalQueryApi.js");
/* harmony import */ var _components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Partials/Header/Header */ "./components/Partials/Header/Header.js");
/* harmony import */ var _components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Util/GenericLoader */ "./components/Util/GenericLoader.js");
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js");
/* harmony import */ var _components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/hooks/useSearchResults */ "./components/hooks/useSearchResults.js");
/* harmony import */ var _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/BDSettings/Constants */ "./components/BDSettings/Constants.js");










const DocListFaq = ({
  DocList
}) => {
  const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
  const {
    termInfo
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_components_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  const {
    term_id,
    taxonomy,
    term_name
  } = termInfo;
  const [searchMode, Loading, searchResults] = (0,_components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_7__.useSearchResults)(searchKeyword, _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.RESOURCES_DOC_CATEGORY_SWITCH, _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.RESOURCES_FAQ_SWITCH);
  const {
    data: docList,
    isLoading,
    isSuccess
  } = (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({
    queryKey: ["taxDocs", {
      taxonomy: taxonomy,
      term_id: term_id,
      terms_per_page: 100
    }],
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_3__.QueryApi,
    staleTime: Infinity
  });
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_4__["default"], {
    enableBackButton: true,
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.RESOURCES_MAIN_TITLE,
    headingLayout: "resources-list",
    enableSearch: true,
    extraClass: "list-page-header",
    searchCallback: setSearchKeyword,
    searchKeyword: searchKeyword
  }), isLoading && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_5__["default"], null), isSuccess && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: docList,
    headingContent: term_name,
    contentType: "post_type"
  }), Loading && searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_5__["default"], null), !Loading && searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: searchResults,
    contentType: "post_type",
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocListFaq);

/***/ }),

/***/ "./Pages/DocViewPage.js":
/*!******************************!*\
  !*** ./Pages/DocViewPage.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_Features_Docs_DocContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Features/Docs/DocContent */ "./components/Features/Docs/DocContent.js");


const DocViewPage = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_DocContent__WEBPACK_IMPORTED_MODULE_1__["default"], null);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocViewPage);

/***/ }),

/***/ "./Pages/FaqPage.js":
/*!**************************!*\
  !*** ./Pages/FaqPage.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Api/globalQueryApi */ "./components/Api/globalQueryApi.js");
/* harmony import */ var _components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Partials/Header/Header */ "./components/Partials/Header/Header.js");
/* harmony import */ var _components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Features/Docs/NoContent */ "./components/Features/Docs/NoContent.js");
/* harmony import */ var _components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/Util/GenericLoader */ "./components/Util/GenericLoader.js");
/* harmony import */ var _components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/hooks/useSearchResults */ "./components/hooks/useSearchResults.js");
/* harmony import */ var _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/BDSettings/Constants */ "./components/BDSettings/Constants.js");










const FaqContent = ({
  DocList
}) => {
  const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
  const [searchMode, Loading, searchResults] = (0,_components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_8__.useSearchResults)(searchKeyword, _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH,
  // Only search for docs if enabled
  _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH // Only search for FAQs if enabled
  );
  let faqQuery;
  let docQuery;
  let docCategoryConditionalQueries = [{
    querykey: "docDefault",
    queryParams: {
      taxonomy: "doc_category",
      terms_per_page: 5,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER_BY,
      parent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_SUBCATEGORY == true ? 1 : 0
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOCTERMS.length == 0
  }, {
    querykey: "docPerPage",
    queryParams: {
      taxonomy: "doc_category",
      terms_per_page: 100,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER_BY,
      parent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_SUBCATEGORY == true ? 1 : 0
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOCTERMS.includes('all')
  }, {
    querykey: "docTerms",
    queryParams: {
      taxonomy: "doc_category",
      terms: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOCTERMS,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOC_TERMS_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOCTERMS.includes('all') && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.DOCTERMS.length > 0
  }];
  let faqConditionalQueries = [{
    querykey: "faqDefault",
    queryParams: {
      post_type: "betterdocs_faq",
      posts_per_page: 5,
      posts_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER,
      posts_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-list" && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQS.length == 0
  }, {
    querykey: "faqAll",
    queryParams: {
      post_type: "betterdocs_faq",
      posts_per_page: 100,
      posts_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER,
      posts_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-list" && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQS.includes('all')
  }, {
    querykey: "faqSpecificList",
    queryParams: {
      post_type: "betterdocs_faq",
      post_ids: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQS,
      posts_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER,
      posts_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_LIST_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-list" && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQS.includes('all')
  }, {
    queryKey: "faqListPerPage",
    queryParams: {
      taxonomy: "betterdocs_faq_category",
      terms_per_page: 5,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-group" && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQTERMS.length == 0
  }, {
    queryKey: "faqListPerPage",
    queryParams: {
      taxonomy: "betterdocs_faq_category",
      terms_per_page: 100,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-group" && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQTERMS.includes("all")
  }, {
    queryKey: "faqSpecficTerms",
    queryParams: {
      taxonomy: "betterdocs_faq_category",
      terms: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQTERMS,
      terms_order: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER,
      terms_orderby: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQ_TERMS_ORDER_BY
    },
    queryFn: _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_4__.QueryApi,
    queryRenderCondition: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType === "faq-group" && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQTERMS.includes("all")
  }];

  /**
   * Run Doc Related Queries
   */
  for (const query of docCategoryConditionalQueries) {
    if (query?.queryRenderCondition) {
      docQuery = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({
        queryKey: [query?.querykey, query?.queryParams],
        queryFn: query?.queryFn,
        staleTime: Infinity,
        enabled: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH // Only run this query if enabled
      });
      break;
    }
  }

  /**
   * Run FAQ Related Queries
   */
  for (const query of faqConditionalQueries) {
    if (query?.queryRenderCondition) {
      faqQuery = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({
        queryKey: [query?.querykey, query?.queryParams],
        queryFn: query?.queryFn,
        staleTime: Infinity,
        enabled: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH // Only run this query if enabled
      });
      break;
    }
  }

  /**
   *  How the data is being fetched and loaded below is explained
   *
   * 1.when search mode (triggered by every keystroke) is enabled and search is loading
   * 2.when search mode (triggered by every keystroke) is enabled and search data has been fetched and finished loading
   * 3.when doc query is disabled and faq query is loading while data is being fetched and search mode is disabled (vice-versa)
   * 4.when doc query is enabled and faq query & doc query is loading while data is being fetched and search mode is disabled
   * 5.when doc query is enabled and search mode is disabled and doc query data has been fetched successfully and doc list has data more than 0
   * 6.when faq query has been fetched successfully and search mode is disabled and faq list has data available more than 0
   */
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-tab-faq-content"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_5__["default"], {
    headingLayout: "resources-category",
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_MAIN_TITLE,
    enableSearch: true,
    extraClass: "resources-page-header",
    searchCallback: setSearchKeyword,
    searchKeyword: searchKeyword,
    enableBackButton: true
  }), searchMode && Loading && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_7__["default"], null), searchMode && !Loading && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: searchResults,
    contentType: "post_type",
    borderRadius: true,
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.SEARCH?.NOT_FOUND
  }), !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && faqQuery?.isLoading && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_7__["default"], null), !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && docQuery?.isLoading && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_7__["default"], null), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && docQuery?.isLoading && faqQuery?.isLoading && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_7__["default"], null), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && !searchMode && docQuery?.isSuccess && docQuery?.data?.length > 0 && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: docQuery?.data,
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_TITLE,
    contentType: "taxonomy",
    borderRadius: true
  }), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && faqQuery?.isSuccess && !searchMode && faqQuery?.data?.length > 0 && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: faqQuery?.data,
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_TITLE,
    contentType: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FAQContentType == "faq-list" ? "post_type" : "taxonomy",
    borderRadius: true
  }), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && faqQuery?.data?.length == 0 && docQuery?.data?.length == 0 && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_6__["default"], {
    content: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("No Faq & Doc Categories Available", "betterdocs-pro"),
    subContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("There are no categories available", "betterdocs-pro")
  }), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && docQuery?.data?.length == 0 && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_6__["default"], {
    content: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("No Doc Categories Available", "betterdocs-pro"),
    subContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("There are no categories available", "betterdocs-pro")
  }), !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && faqQuery?.data?.length == 0 && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_6__["default"], {
    content: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("No FAQ Available", "betterdocs-pro"),
    subContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("There are no FAQs available", "betterdocs-pro")
  }), !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_DOC_CATEGORY_SWITCH && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.RESOURCES_FAQ_SWITCH && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_6__["default"], {
    content: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("No Content Available", "betterdocs-pro"),
    subContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please enable Doc Categories or FAQ in settings", "betterdocs-pro")
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FaqContent);

/***/ }),

/***/ "./Pages/HomePage.js":
/*!***************************!*\
  !*** ./Pages/HomePage.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js");
/* harmony import */ var _components_Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Api/globalQueryApi */ "./components/Api/globalQueryApi.js");
/* harmony import */ var _components_Util_DocListLoader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Util/DocListLoader */ "./components/Util/DocListLoader.js");
/* harmony import */ var _components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Partials/Header/Header */ "./components/Partials/Header/Header.js");
/* harmony import */ var _components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Util/GenericLoader */ "./components/Util/GenericLoader.js");
/* harmony import */ var _components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/hooks/useSearchResults */ "./components/hooks/useSearchResults.js");
/* harmony import */ var _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/Features/Docs/NoContent */ "./components/Features/Docs/NoContent.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__);











const HomeContent = ({
  DocList
}) => {
  const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
  const [searchMode, Loading, searchResults] = (0,_components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_7__.useSearchResults)(searchKeyword, _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH,
  // Use boolean value directly
  false);
  const [faqSearchMode, faqLoading, faqSearchResults] = (0,_components_hooks_useSearchResults__WEBPACK_IMPORTED_MODULE_7__.useSearchResults)(searchKeyword, false, _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH // Use boolean value directly
  );

  // Only fetch docs data if HOME_DOCS_SWITCH is enabled
  const {
    data,
    isLoading,
    isSuccess
  } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({
    queryKey: ["homeContent"],
    queryFn: () => fetch(_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeContentURL, {
      method: "GET"
    }).then(result => {
      return result.json();
    }),
    staleTime: Infinity,
    enabled: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH // Use boolean value directly
  });

  // Only fetch FAQ data if HOME_FAQ_SWITCH is enabled
  const {
    data: faqData,
    isLoading: faqContentLoading,
    isSuccess: faqSuccess
  } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({
    queryKey: ["homeFaqContent"],
    queryFn: () => fetch(_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ?.FAQ_URL, {
      method: "GET"
    }).then(result => {
      return result.json();
    }),
    staleTime: Infinity,
    enabled: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH // Use boolean value directly
  });
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-tab-home-content"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Partials_Header_Header__WEBPACK_IMPORTED_MODULE_5__["default"], {
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.MAIN_HOME_TITLE,
    headingSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.MAIN_HOME_SUBTITLE,
    headingLayout: "home-layout",
    enableLogo: false,
    enableSearch: true,
    extraClass: "home-page-header",
    searchCallback: setSearchKeyword,
    searchKeyword: searchKeyword
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-tab-main-content"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-home-content-list"
  }, (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && isLoading || _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqContentLoading) && !searchMode && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_6__["default"], null), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && isSuccess && !searchMode && data?.length > 0 && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: data,
    headingContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeTitle,
    borderRadius: true,
    contentType: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeListType,
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeListType == "post_type" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Docs Available", "betterdocs-pro") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Doc Categories Found", "betterdocs-pro")
  }), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqSuccess && !searchMode && faqData?.length > 0 && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: faqData,
    headingContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)('FAQ', "betterdocs-pro"),
    borderRadius: true,
    contentType: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ.HOME_FAQ_CONTENT_TYPE == 'faq-list' ? 'post_type' : 'taxonomy',
    noContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)('No FAQ Available', "betterdocs-pro")
  }), !searchMode && (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && isSuccess && faqSuccess && data?.length == 0 && faqData?.length == 0 || _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && isSuccess && data?.length == 0 || !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqSuccess && faqData?.length == 0) && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_9__["default"], {
    content: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH ? _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeListType == "post_type" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Docs & FAQ Available", "betterdocs-pro") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Doc Categories & FAQ Found", "betterdocs-pro") : _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH ? _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeListType == "post_type" ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Docs Available", "betterdocs-pro") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No Doc Categories Found", "betterdocs-pro") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("No FAQ Available", "betterdocs-pro")
  }), (searchMode && Loading || faqSearchMode && faqLoading) && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_6__["default"], null), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && searchMode && faqSearchMode && !Loading && !faqLoading && searchResults?.length > 0 && faqSearchResults?.length > 0 && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: searchResults,
    headingContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)(_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeTitle, "betterdocs-pro"),
    borderRadius: true,
    contentType: "post_type",
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: faqSearchResults,
    headingContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("FAQ", "betterdocs-pro"),
    borderRadius: true,
    contentType: "post_type",
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  })), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && searchMode && !Loading && (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqSearchMode && !faqLoading && searchResults?.length > 0 && faqSearchResults?.length == 0 || !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && searchResults?.length > 0) && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: searchResults,
    headingContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)(_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HomeContentDocsTitle, "betterdocs-pro"),
    borderRadius: true,
    contentType: "post_type",
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  }), _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqSearchMode && !faqLoading && (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && searchMode && !Loading && searchResults?.length == 0 && faqSearchResults?.length > 0 || !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && faqSearchResults?.length > 0) && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DocList, {
    docList: faqSearchResults,
    headingContent: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("FAQ", "betterdocs-pro"),
    borderRadius: true,
    contentType: "post_type",
    noContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    noSubContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  }), (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH && searchMode && !Loading && searchResults?.length == 0 || !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_DOCS_SWITCH) && (_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH && faqSearchMode && !faqLoading && faqSearchResults?.length == 0 || !_components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.HOME_FAQ_SWITCH) && (searchMode || faqSearchMode) && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Features_Docs_NoContent__WEBPACK_IMPORTED_MODULE_9__["default"], {
    content: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.OOPS,
    subContent: _components_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.SEARCH?.NOT_FOUND
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeContent);

/***/ }),

/***/ "./assets/images/msg-receive.png":
/*!***************************************!*\
  !*** ./assets/images/msg-receive.png ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = __webpack_require__.p + "images/msg-receive.fe68435f.png";

/***/ }),

/***/ "./assets/images/nodoc.png":
/*!*********************************!*\
  !*** ./assets/images/nodoc.png ***!
  \*********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = __webpack_require__.p + "images/nodoc.c8afc2a7.png";

/***/ }),

/***/ "./assets/loaders/messageLoader.gif":
/*!******************************************!*\
  !*** ./assets/loaders/messageLoader.gif ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
module.exports = __webpack_require__.p + "images/messageLoader.d2dc7c6a.gif";

/***/ }),

/***/ "./components/Api/globalQueryApi.js":
/*!******************************************!*\
  !*** ./components/Api/globalQueryApi.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryApi: () => (/* binding */ QueryApi)
/* harmony export */ });
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/url */ "@wordpress/url");
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_url__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");


const QueryApi = ({
  queryKey
}) => {
  const [_, queryParams] = queryKey;
  const {
    parent,
    taxonomy,
    terms,
    terms_per_page,
    term_id,
    post_type,
    post_id,
    post_ids,
    posts_order,
    posts_orderby,
    posts_per_page,
    terms_orderby,
    terms_order
  } = queryParams;
  let basePath = "wp/v2/";
  let postTypeQueryParams = {};
  let taxonomyQueryParams = {
    hide_empty: true
  };
  if (taxonomy != undefined) {
    basePath += taxonomy;
    if (terms?.length > 0) {
      taxonomyQueryParams["include"] = terms.join(",");
    }
    if (parent != undefined && parent == 0) {
      taxonomyQueryParams['parent'] = 0;
    }
    if (terms_orderby != undefined) {
      taxonomyQueryParams["orderby"] = terms_orderby;
    }
    if (terms_order != undefined) {
      taxonomyQueryParams['order'] = terms_order;
    }
    if (terms_per_page != undefined) {
      taxonomyQueryParams["per_page"] = terms_per_page;
    }
    if (term_id != undefined && taxonomy == "doc_category") {
      basePath = basePath.replace(taxonomy, "");
      basePath += "docs";
      taxonomyQueryParams[taxonomy] = term_id;
    }
    if (term_id != undefined && taxonomy == "betterdocs_faq_category") {
      basePath = basePath.replace(taxonomy, "");
      basePath += "betterdocs_faq";
      taxonomyQueryParams[taxonomy] = term_id;
    }
    return fetch(`${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_1__.BASE_URL}${(0,_wordpress_url__WEBPACK_IMPORTED_MODULE_0__.addQueryArgs)(basePath, taxonomyQueryParams)}`, {
      method: "GET"
    }).then(result => {
      return result.json();
    });
  }
  if (post_type != undefined) {
    basePath += post_type + "/";
    if (post_ids?.length > 0) {
      postTypeQueryParams["include"] = post_ids.join(",");
    }
    if (post_id != undefined) {
      basePath += post_id;
    }
    if (posts_order != undefined) {
      postTypeQueryParams['order'] = posts_order;
    }
    if (posts_orderby != undefined) {
      postTypeQueryParams['orderby'] = posts_orderby;
    }
    if (posts_per_page != undefined) {
      postTypeQueryParams["per_page"] = posts_per_page;
    }
    return fetch(`${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_1__.BASE_URL}${(0,_wordpress_url__WEBPACK_IMPORTED_MODULE_0__.addQueryArgs)(basePath, postTypeQueryParams)}`, {
      method: "GET"
    }).then(result => {
      return result.json();
    });
  }
};


/***/ }),

/***/ "./components/Api/search-api.js":
/*!**************************************!*\
  !*** ./components/Api/search-api.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   searchEndpoint: () => (/* binding */ searchEndpoint)
/* harmony export */ });
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/api-fetch */ "@wordpress/api-fetch");
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/url */ "@wordpress/url");
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_url__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");



function searchEndpoint(post_type, keyword) {
  const api_path = `wp/v2/${post_type}`;
  const queryParams = {
    search: keyword
  };
  if (post_type == 'docs') {
    queryParams['orderby'] = 'relevance';
  }
  return _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default()({
    url: `${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}${(0,_wordpress_url__WEBPACK_IMPORTED_MODULE_1__.addQueryArgs)(api_path, queryParams)}`
  });
}

/***/ }),

/***/ "./components/Api/search-insert-api.js":
/*!*********************************************!*\
  !*** ./components/Api/search-insert-api.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   searchInsertEndpoint: () => (/* binding */ searchInsertEndpoint)
/* harmony export */ });
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/api-fetch */ "@wordpress/api-fetch");
/* harmony import */ var _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/url */ "@wordpress/url");
/* harmony import */ var _wordpress_url__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_url__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");



function searchInsertEndpoint(keyword, no_result) {
  const api_path = `betterdocs/v1/search-insert`;
  const queryParams = {
    s: keyword
  };
  if (no_result) {
    queryParams['no_result'] = 1;
  }
  try {
    return _wordpress_api_fetch__WEBPACK_IMPORTED_MODULE_0___default()({
      url: `${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__.BASE_URL}${(0,_wordpress_url__WEBPACK_IMPORTED_MODULE_1__.addQueryArgs)(api_path, queryParams)}`
    });
  } catch (error) {
    console.error('Please update BetterDocs free version: ', error);
    return false;
  }
}

/***/ }),

/***/ "./components/BDSettings/Constants.js":
/*!********************************************!*\
  !*** ./components/BDSettings/Constants.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AI_CHATBOT: () => (/* binding */ AI_CHATBOT),
/* harmony export */   ASKFORM: () => (/* binding */ ASKFORM),
/* harmony export */   BASE_URL: () => (/* binding */ BASE_URL),
/* harmony export */   BDSettings: () => (/* binding */ BDSettings),
/* harmony export */   BRANDING: () => (/* binding */ BRANDING),
/* harmony export */   CHAT: () => (/* binding */ CHAT),
/* harmony export */   DOCTERMS: () => (/* binding */ DOCTERMS),
/* harmony export */   DOC_SUBCATEGORY: () => (/* binding */ DOC_SUBCATEGORY),
/* harmony export */   DOC_TERMS_ORDER: () => (/* binding */ DOC_TERMS_ORDER),
/* harmony export */   DOC_TERMS_ORDER_BY: () => (/* binding */ DOC_TERMS_ORDER_BY),
/* harmony export */   FAQ: () => (/* binding */ FAQ),
/* harmony export */   FAQContentType: () => (/* binding */ FAQContentType),
/* harmony export */   FAQS: () => (/* binding */ FAQS),
/* harmony export */   FAQTERMS: () => (/* binding */ FAQTERMS),
/* harmony export */   FAQ_LIST_ORDER: () => (/* binding */ FAQ_LIST_ORDER),
/* harmony export */   FAQ_LIST_ORDER_BY: () => (/* binding */ FAQ_LIST_ORDER_BY),
/* harmony export */   FAQ_TERMS_ORDER: () => (/* binding */ FAQ_TERMS_ORDER),
/* harmony export */   FAQ_TERMS_ORDER_BY: () => (/* binding */ FAQ_TERMS_ORDER_BY),
/* harmony export */   FEEDBACK_DISPLAY: () => (/* binding */ FEEDBACK_DISPLAY),
/* harmony export */   FEEDBACK_SUCCESS: () => (/* binding */ FEEDBACK_SUCCESS),
/* harmony export */   FEEDBACK_TEXT: () => (/* binding */ FEEDBACK_TEXT),
/* harmony export */   FEEDBACK_URL: () => (/* binding */ FEEDBACK_URL),
/* harmony export */   FILE_UPLOAD_SWITCH: () => (/* binding */ FILE_UPLOAD_SWITCH),
/* harmony export */   HEADER_ICON: () => (/* binding */ HEADER_ICON),
/* harmony export */   HEADER_LOGO: () => (/* binding */ HEADER_LOGO),
/* harmony export */   HOME_DOCS_SWITCH: () => (/* binding */ HOME_DOCS_SWITCH),
/* harmony export */   HOME_FAQ: () => (/* binding */ HOME_FAQ),
/* harmony export */   HOME_FAQ_SWITCH: () => (/* binding */ HOME_FAQ_SWITCH),
/* harmony export */   HOME_TAB_TITLE: () => (/* binding */ HOME_TAB_TITLE),
/* harmony export */   HomeContentDocCategoriesTitle: () => (/* binding */ HomeContentDocCategoriesTitle),
/* harmony export */   HomeContentDocsTitle: () => (/* binding */ HomeContentDocsTitle),
/* harmony export */   HomeContentType: () => (/* binding */ HomeContentType),
/* harmony export */   HomeContentURL: () => (/* binding */ HomeContentURL),
/* harmony export */   HomeListType: () => (/* binding */ HomeListType),
/* harmony export */   HomeTitle: () => (/* binding */ HomeTitle),
/* harmony export */   IA_NONCE: () => (/* binding */ IA_NONCE),
/* harmony export */   LAUNCHER_CLOSE_ICON: () => (/* binding */ LAUNCHER_CLOSE_ICON),
/* harmony export */   LAUNCHER_OPEN_ICON: () => (/* binding */ LAUNCHER_OPEN_ICON),
/* harmony export */   MAIN_HOME_SUBTITLE: () => (/* binding */ MAIN_HOME_SUBTITLE),
/* harmony export */   MAIN_HOME_TITLE: () => (/* binding */ MAIN_HOME_TITLE),
/* harmony export */   MESSAGE_SENT_TEXT: () => (/* binding */ MESSAGE_SENT_TEXT),
/* harmony export */   RESOURCES_DOC_CATEGORY_SWITCH: () => (/* binding */ RESOURCES_DOC_CATEGORY_SWITCH),
/* harmony export */   RESOURCES_DOC_TITLE: () => (/* binding */ RESOURCES_DOC_TITLE),
/* harmony export */   RESOURCES_FAQ_SWITCH: () => (/* binding */ RESOURCES_FAQ_SWITCH),
/* harmony export */   RESOURCES_FAQ_TITLE: () => (/* binding */ RESOURCES_FAQ_TITLE),
/* harmony export */   RESOURCES_MAIN_TITLE: () => (/* binding */ RESOURCES_MAIN_TITLE),
/* harmony export */   RESOURCES_TAB_TITLE: () => (/* binding */ RESOURCES_TAB_TITLE),
/* harmony export */   SEARCH: () => (/* binding */ SEARCH),
/* harmony export */   SEARCH_LETTER_LIMIT: () => (/* binding */ SEARCH_LETTER_LIMIT),
/* harmony export */   TAB_ICONS: () => (/* binding */ TAB_ICONS)
/* harmony export */ });
const BDSettings = betterdocs;
const BASE_URL = BDSettings?.BASE_URL;
const IA_NONCE = BDSettings['IA_NONCE'];
const HOME_DOCS_SWITCH = Boolean(BDSettings['HOME_DOCS_SWITCH']);
const HOME_FAQ_SWITCH = Boolean(BDSettings['HOME_FAQ_SWITCH']);
const CHAT = BDSettings["CHAT"];
const AI_CHATBOT = Boolean(BDSettings["TAB_AI_CHATBOT"]);
const FAQ = BDSettings["FAQ"];
const HOME_FAQ = BDSettings['HOME_FAQ'];
const SEARCH = BDSettings["SEARCH"];
const SEARCH_LETTER_LIMIT = BDSettings["SEARCH_LETTER_LIMIT"];
const ASKFORM = BDSettings["ASKFORM"];
const FILE_UPLOAD_SWITCH = ASKFORM['FILE_UPLOAD_SWITCH'];
const MESSAGE_SENT_TEXT = BDSettings["THANKS"];
const DOC_CATEGORY = BDSettings["DOC_CATEGORY"];
const RESOURCES_DOC_TITLE = DOC_CATEGORY["doc-title"];
const RESOURCES_FAQ_TITLE = FAQ["faq-title"];
const RESOURCES_FAQ_SWITCH = FAQ["faq-switch"];
const FAQContentType = FAQ["faq_content_type"];
const FAQTERMS = FAQ["faq-terms"];
const FAQS = FAQ["faq-list"];
const FAQ_TERMS_ORDER = FAQ['faq-terms-order'];
const FAQ_TERMS_ORDER_BY = FAQ['faq-terms-order-by'];
const FAQ_LIST_ORDER_BY = FAQ['faq-list-orderby'];
const FAQ_LIST_ORDER = FAQ['faq-list-order'];
// const FAQContentNumber = FAQ[FAQContentType + "-number"];
const RESOURCES_DOC_CATEGORY_SWITCH = DOC_CATEGORY["doc-category-switch"];
const DOCTERMS = DOC_CATEGORY["doc-terms"];
const DOC_TERMS_ORDER = DOC_CATEGORY['doc-terms-order'];
const DOC_TERMS_ORDER_BY = DOC_CATEGORY['doc-terms-order-by'];
const DOC_SUBCATEGORY = DOC_CATEGORY['doc-subcategory-switch'];
// const DOC_CATEGORIES_NUMBER = DOC_CATEGORY['doc-categories-number'];
const HomeContentURL = BDSettings?.URL;
const HomeContentType = BDSettings?.HOME_CONTENT;
const HomeContentDocCategoriesTitle = BDSettings?.HOME_CONTENT_DOC_CATEGORY_TITLE;
const HomeContentDocsTitle = BDSettings?.HOME_CONTENT_DOCS_TITLE;
const HomeTitle = HomeContentType == "docs" ? HomeContentDocsTitle : HomeContentDocCategoriesTitle;
const HomeListType = HomeContentType == "docs" ? "post_type" : "taxonomy";
const FEEDBACK_DISPLAY = BDSettings?.FEEDBACK?.DISPLAY;
const FEEDBACK_URL = BDSettings?.FEEDBACK?.URL;
const FEEDBACK_SUCCESS = BDSettings?.FEEDBACK?.SUCCESS;
const FEEDBACK_TEXT = BDSettings?.FEEDBACK?.TEXT;
const MAIN_HOME_TITLE = BDSettings["HOME_TITLE"];
const MAIN_HOME_SUBTITLE = BDSettings["HOME_SUBTITLE"];
const RESOURCES_MAIN_TITLE = BDSettings["RESOURCES_TITLE"];
const RESOURCES_TAB_TITLE = BDSettings["RESOURCES_TAB_TITLE"];
const HEADER_ICON = BDSettings["HEADER_ICON"];
const HEADER_LOGO = BDSettings["HEADER_LOGO"];
const TAB_HOME_ICON = BDSettings["TAB_HOME_ICON"];
const TAB_CHATBOT_ICON = BDSettings["TAB_CHATBOT_ICON"];
const TAB_MESSAGE_ICON = BDSettings["TAB_MESSAGE_ICON"];
const TAB_RESOURCE_ICON = BDSettings["TAB_RESOURCE_ICON"];
const LAUNCHER_OPEN_ICON = BDSettings["LAUNCHER"]?.open_icon;
const LAUNCHER_CLOSE_ICON = BDSettings["LAUNCHER"]?.close_icon;
const BRANDING = BDSettings['BRANDING']?.show;
const HOME_TAB_TITLE = BDSettings['HOME_TAB_TITLE'];
const TAB_ICONS = {
  home: TAB_HOME_ICON?.url,
  chatbot: TAB_CHATBOT_ICON?.url,
  feedback_tab: TAB_MESSAGE_ICON?.url,
  resources: TAB_RESOURCE_ICON?.url
};


/***/ }),

/***/ "./components/Features/Docs/DocContent.js":
/*!************************************************!*\
  !*** ./components/Features/Docs/DocContent.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js");
/* harmony import */ var _Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Api/globalQueryApi */ "./components/Api/globalQueryApi.js");
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _Util_GenericLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../Util/GenericLoader */ "./components/Util/GenericLoader.js");
/* harmony import */ var _icons_LogoFooter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../icons/LogoFooter */ "./components/icons/LogoFooter.js");
/* harmony import */ var _Partials_Header_SingleHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../Partials/Header/SingleHeader */ "./components/Partials/Header/SingleHeader.js");
/* harmony import */ var _Partials_Single_DocFeedback__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../Partials/Single/DocFeedback */ "./components/Partials/Single/DocFeedback.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__);











const DocContent = () => {
  //Global Doc Context State From Root Level Component
  const {
    singleDocInfo,
    feedbackText,
    setFeedbackText
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_4__["default"]);
  const {
    doc_id,
    post_type
  } = singleDocInfo;

  //Single Doc Title On Scroll Related States
  const [scrollData, setScrollData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [scrollTitle, setScrollTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');
  const singleDocTitleWrapper = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);

  //Single Doc Query
  const {
    data: docContent,
    isLoading
  } = (0,react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({
    queryKey: ["docContent", {
      post_type: post_type,
      post_id: doc_id
    }],
    queryFn: _Api_globalQueryApi__WEBPACK_IMPORTED_MODULE_3__.QueryApi,
    staleTime: Infinity
  });

  //Single Doc Reaction
  function SubmitReaction(doc_id, reaction) {
    fetch(`${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FEEDBACK_URL}/${doc_id}?feelings=${reaction}`, {
      method: "POST"
    }).then(result => {
      return result.json();
    }).then(response => {
      if (response) {
        setFeedbackText(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FEEDBACK_SUCCESS);
      } else {
        setFeedbackText("Oops! Something went wrong with your reaction.");
      }
      setTimeout(() => {
        setFeedbackText(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FEEDBACK_TEXT);
      }, 3000);
    });
  }
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, isLoading ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Util_GenericLoader__WEBPACK_IMPORTED_MODULE_5__["default"], null) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-single-docs-wrapper",
    onScroll: e => {
      setScrollData(e?.target?.scrollTop);
      setScrollTitle(docContent?.title?.rendered);
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Partials_Header_SingleHeader__WEBPACK_IMPORTED_MODULE_7__["default"], {
    scrollData: scrollData,
    scrollTitle: scrollTitle,
    contentTitle: singleDocTitleWrapper
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-singleDoc-content"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", {
    className: "doc-title",
    dangerouslySetInnerHTML: {
      __html: docContent?.title?.rendered
    },
    ref: singleDocTitleWrapper
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    dangerouslySetInnerHTML: {
      __html: docContent?.content?.rendered
    }
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-singleDoc-footer"
  }, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.FEEDBACK_DISPLAY && post_type != 'betterdocs_faq' && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Partials_Single_DocFeedback__WEBPACK_IMPORTED_MODULE_8__["default"], {
    sadOnclick: e => {
      let reaction = "sad";
      SubmitReaction(doc_id, reaction);
    },
    neutralOnclick: e => {
      let reaction = "normal";
      SubmitReaction(doc_id, reaction);
    },
    happyOnClick: e => {
      let reaction = "happy";
      SubmitReaction(doc_id, reaction);
    },
    reactionText: feedbackText
  }), _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_9__.BRANDING && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-footer-group"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h5", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)('Powered by', 'betterdocs-pro')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "logoFooter"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("a", {
    href: "https://betterdocs.co/",
    target: "_blank"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_LogoFooter__WEBPACK_IMPORTED_MODULE_6__["default"], null)))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocContent);

/***/ }),

/***/ "./components/Features/Docs/DocList.js":
/*!*********************************************!*\
  !*** ./components/Features/Docs/DocList.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/html-entities */ "@wordpress/html-entities");
/* harmony import */ var _wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _NoContent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NoContent */ "./components/Features/Docs/NoContent.js");
/* harmony import */ var _icons_ListArrow__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../icons/ListArrow */ "./components/icons/ListArrow.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_7__);








const DocList = ({
  docList,
  headingContent,
  borderRadius,
  contentType,
  noContent,
  noSubContent
}) => {
  const {
    setTab,
    setSingleDocInfo,
    setTermInfo
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__["default"]);
  function listClickHandler(docObject, type) {
    if (type == "post_type") {
      //When Clicked Set The Single Doc View
      setTab("single_doc_view");
      setSingleDocInfo({
        doc_id: docObject?.id,
        post_type: docObject.hasOwnProperty("doc_category") ? "docs" : "betterdocs_faq"
      });
    }
    if (type == "taxonomy") {
      setTab("doc_list");
      setTermInfo({
        term_name: docObject?.name,
        term_id: docObject?.id,
        taxonomy: docObject?.taxonomy
      });
    }
  }
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, docList?.length > 0 ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-docs${borderRadius ? " radius-layout" : ""}`
  }, headingContent && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-docs-heading"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", {
    className: "doc-title"
  }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_7__.__)((0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(headingContent), 'betterdocs-pro'))), docList?.map(value => {
    return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "betterdocs-ia-docs-content",
      onClick: () => listClickHandler(value, contentType),
      key: Math?.random()
    }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "content-item"
    }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", {
      dangerouslySetInnerHTML: {
        __html: contentType == "taxonomy" ? (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(value?.name) : value?.title?.rendered
      }
    }), contentType != "taxonomy" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, (0,_wordpress_html_entities__WEBPACK_IMPORTED_MODULE_2__.decodeEntities)(value?.excerpt?.rendered?.replace(/<[^>]+>/g, "")?.replace('[&hellip;]', ''))?.split(' ')?.slice(0, 10)?.join(' '))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "content-icon"
    }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_ListArrow__WEBPACK_IMPORTED_MODULE_5__["default"], null)));
  })) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_NoContent__WEBPACK_IMPORTED_MODULE_4__["default"], {
    content: noContent,
    subContent: noSubContent
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(DocList));

/***/ }),

/***/ "./components/Features/Docs/NoContent.js":
/*!***********************************************!*\
  !*** ./components/Features/Docs/NoContent.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _assets_images_nodoc_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/nodoc.png */ "./assets/images/nodoc.png");
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__);






const NoContent = ({
  content,
  subContent
}) => {
  const {
    setTab
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__["default"]);
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-no-doc"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: _assets_images_nodoc_png__WEBPACK_IMPORTED_MODULE_2__,
    alt: "nodoc"
  }), content && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", null, content), subContent && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, subContent), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: () => {
      setTab('feedback_form');
    }
  }, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)('Contact Us', 'betterdocs-pro')));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NoContent);

/***/ }),

/***/ "./components/Features/Feedback/FeedBackForm.js":
/*!******************************************************!*\
  !*** ./components/Features/Feedback/FeedBackForm.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _assets_loaders_messageLoader_gif__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/loaders/messageLoader.gif */ "./assets/loaders/messageLoader.gif");
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _icons_Upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../icons/Upload */ "./components/icons/Upload.js");
/* harmony import */ var _FileUploadProgress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FileUploadProgress */ "./components/Features/Feedback/FileUploadProgress.js");
/* harmony import */ var _MessageSent__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./MessageSent */ "./components/Features/Feedback/MessageSent.js");
/* harmony import */ var _Partials_Header_FormHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../Partials/Header/FormHeader */ "./components/Partials/Header/FormHeader.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_9__);











const FeedBackForm = () => {
  const {
    feedbackFormInfo,
    setFeedbackFormInfo,
    validation,
    setValidation,
    messageStatus,
    setMessageStatus,
    buttonStatus,
    setButtonStatus
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_3__["default"]);
  const inputReference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  const uploadedFileReference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  const {
    current
  } = inputReference;
  const {
    name,
    subject,
    message,
    email,
    fileData
  } = feedbackFormInfo;
  const {
    message: validationMessage,
    fieldType
  } = validation;

  /**
   * Click(event) and upload files
   */
  function uploadClickHandler(e) {
    current?.click();
  }

  /**
   * Click(event) and upload files(saves files in state)
   */
  function uploadFiles(e) {
    const {
      files
    } = e.target;
    if (!fileExtensionValidation(files)) {
      return;
    }
    if (!fileSizeValidation(files)) {
      return;
    }
    if (validationMessage != "") {
      setValidation(oldValidationState => {
        return {
          ...oldValidationState,
          message: "",
          fieldType: ""
        };
      });
    }
    setFeedbackFormInfo(previousState => {
      const {
        fileData
      } = previousState;
      fileData.push(...files);
      return {
        ...previousState,
        fileData: [...fileData]
      };
    });
  }

  //scrollDown when files are added
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    uploadedFileReference?.current?.scrollIntoView({
      behavior: "smooth",
      block: "end",
      inline: "nearest"
    });
  }, [fileData.length]);

  /**
   * Drag and drop events start
   */
  function handleDragOver(e) {
    e.preventDefault();
  }
  function handleDragLeave(e) {
    e.preventDefault();
  }
  function handleDrop(e) {
    e.preventDefault();
    const {
      files
    } = e.dataTransfer;
    if (!fileExtensionValidation(files)) {
      return;
    }
    if (!fileSizeValidation(files)) {
      return;
    }
    setValidation(oldValidationState => {
      return {
        ...oldValidationState,
        message: "",
        fieldType: ""
      };
    });
    setFeedbackFormInfo(previousState => {
      const {
        fileData
      } = previousState;
      fileData.push(...files);
      return {
        ...previousState,
        fileData: [...fileData]
      };
    });
  }

  /**
   * Validate File Extension
   */
  function fileExtensionValidation(files) {
    let extension;
    let fileName;
    let allowed_extenstions = ["png", "gif", "pdf", "jpeg", "jpg"];
    for (let file of files) {
      fileName = file?.name?.split(".")[0];
      extension = file?.name?.split(".")[file?.name?.split(".")?.length - 1].toLowerCase();
      if (!allowed_extenstions.includes(extension)) {
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: `Only ${allowed_extenstions.join(", ")} are allowed`,
            fieldType: "file"
          };
        });
        return false;
      }
    }
    return true;
  }

  /**
   * Validate Size In Mb
   */
  function fileSizeValidation(files) {
    let maxFileSize = 5;
    let fileSizeInMb;
    for (let file of files) {
      fileSizeInMb = Math.ceil(file?.size / 1024 ** 2);
      if (fileSizeInMb > maxFileSize) {
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: `Max file size limit ${maxFileSize} mb`,
            fieldType: "file"
          };
        });
        return false;
      }
    }
    return true;
  }

  /**
   * When submit is clicked validation takes place
   */
  function formHandler(data) {
    const {
      payload,
      type
    } = data;
    const {
      target
    } = payload;
    const {
      value
    } = target;
    switch (type) {
      case "setEmail":
        setFeedbackFormInfo(previousState => {
          return {
            ...previousState,
            email: value
          };
        });
        break;
      case "setName":
        setFeedbackFormInfo(previousState => {
          return {
            ...previousState,
            name: value
          };
        });
        break;
      case "setSubject":
        setFeedbackFormInfo(previousState => {
          return {
            ...previousState,
            subject: value
          };
        });
        break;
      case "setMessage":
        setFeedbackFormInfo(previousState => {
          return {
            ...previousState,
            message: value
          };
        });
        break;
      case "setFileData":
        setFeedbackFormInfo(previousState => {
          return {
            ...previousState,
            fileData: payload
          };
        });
        break;
    }
  }

  /**
   * When submit is clicked send all data to the server end
   */
  function submitForm() {
    let finalData = new FormData();
    finalData.append("name", name);
    finalData.append("email", email);
    finalData.append("subject", subject);
    finalData.append("message", message);
    for (let i = 0; i < fileData.length; i++) {
      finalData.append("file[]", fileData[i]);
    }
    fetch(betterdocs?.ASK_URL, {
      method: "POST",
      body: finalData
    }).then(res => res.json()).then(result => {
      if (result) {
        setMessageStatus("sent");
        setTimeout(() => {
          //when successful set the message status state to default state
          setMessageStatus("idle");
          //when successful set the message status state to default state
          setButtonStatus("idle");

          //when successful set the feedback form state to default state
          setFeedbackFormInfo({
            email: "",
            name: "",
            subject: "",
            message: "",
            fileData: []
          });
        }, 5000);
      }
    }).catch(error => {
      console.error(error);
    });

    //Sent Or Not Sent, By Default Set The FeedbackForm Validation To Default
    setValidation(oldValidationState => {
      return {
        ...oldValidationState,
        message: "",
        fieldType: ""
      };
    });
  }

  /**
   * Triggers when submit button is clicked
   */
  function submit() {
    let emailRegx = /^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;
    switch (true) {
      case email.length === 0:
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: `${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.EMAIL} Field Must Not Be Empty`,
            fieldType: "email"
          };
        });
        return;
      case !emailRegx.test(email):
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: "Email Address Invalid",
            fieldType: "email"
          };
        });
        return;
      case name.length === 0:
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: `${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.NAME} Field Must Not Be Empty`,
            fieldType: "name"
          };
        });
        return;
      case subject.length === 0:
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: `${_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.SUBJECT} Field Must Not Be Empty`,
            fieldType: "subject"
          };
        });
        return;
      case message.length === 0:
        setValidation(oldValidationState => {
          return {
            ...oldValidationState,
            message: "Text Area Field Must Not Be Empty",
            fieldType: "message"
          };
        });
        return;
    }
    setValidation(oldValidationState => {
      return {
        ...oldValidationState,
        message: "",
        fieldType: ""
      };
    });
    setButtonStatus("submitting");
    submitForm();
  }
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-tab-message-container"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Partials_Header_FormHeader__WEBPACK_IMPORTED_MODULE_7__["default"], {
    enableBackButton: true,
    heading: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_9__.__)(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.CHAT?.subtitle),
    subHeading: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_9__.__)(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.CHAT?.subtitle_two)
  }), messageStatus == "idle" ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-feedback-form"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-group betterdocs-ia-email-group ${fieldType == "email" && "betterdocs-ia-warning-group"}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.EMAIL), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    className: "ia-input",
    name: "ia-email",
    onChange: e => formHandler({
      payload: e,
      type: "setEmail"
    })
  }), fieldType == "email" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "warning-text"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, validationMessage))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-group betterdocs-ia-name-group ${fieldType == "name" && "betterdocs-ia-warning-group"}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.NAME), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    className: "ia-input",
    name: "ia-name",
    onChange: e => formHandler({
      payload: e,
      type: "setName"
    })
  }), fieldType == "name" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "warning-text"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, validationMessage))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-group betterdocs-ia-subject-group ${fieldType == "subject" && "betterdocs-ia-warning-group"}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.SUBJECT), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    className: "ia-input",
    name: "ia-subject",
    onChange: e => formHandler({
      payload: e,
      type: "setSubject"
    })
  }), fieldType == "subject" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "warning-text"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, validationMessage))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-group betterdocs-ia-message-group ${fieldType == "message" && "betterdocs-ia-warning-group"}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.TEXTAREA), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("textarea", {
    className: "ia-message",
    name: "ia-message",
    onChange: e => formHandler({
      payload: e,
      type: "setMessage"
    })
  }), fieldType == "message" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "warning-text"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, validationMessage))), _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.FILE_UPLOAD_SWITCH && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-attachments-group",
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: e => uploadClickHandler(e)
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_Upload__WEBPACK_IMPORTED_MODULE_4__["default"], null)), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_9__.__)('Click To Upload Or Drag and Drop', 'betterdocs-pro')), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.ATTACHMENT), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "file",
    name: "ia-upload-file",
    multiple: true,
    accept: "image/png, image/gif, image/jpeg, image/jpg, .pdf",
    style: {
      display: "none"
    },
    ref: inputReference,
    onChange: uploadFiles
  })), fieldType == "file" && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-group${fieldType === "message" ? " betterdocs-ia-warning-group" : ""}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "warning-text file-warning"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, validationMessage))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-submit"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "submit",
    onClick: () => submit()
  }, buttonStatus == "submitting" ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: _assets_loaders_messageLoader_gif__WEBPACK_IMPORTED_MODULE_2__,
    className: "submit-loader",
    height: 20,
    width: 25
  }) : "", (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, buttonStatus == "idle" ? _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.SEND : _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_8__.ASKFORM?.SENDING)))) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_MessageSent__WEBPACK_IMPORTED_MODULE_6__["default"], null), fileData.length > 0 && messageStatus == "idle" ? fileData.map((file, index) => {
    let fileName = file?.name;
    let fileSizeInMb = Math.ceil(file?.size / 1024 ** 2);
    return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_FileUploadProgress__WEBPACK_IMPORTED_MODULE_5__["default"], {
      fileName: fileName,
      fileSize: fileSizeInMb,
      key: index,
      fileID: index,
      fileDatas: fileData,
      formState: setFeedbackFormInfo,
      ref: uploadedFileReference
    });
  }) : "");
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeedBackForm);

/***/ }),

/***/ "./components/Features/Feedback/FileUploadProgress.js":
/*!************************************************************!*\
  !*** ./components/Features/Feedback/FileUploadProgress.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _icons_FileDelete__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../icons/FileDelete */ "./components/icons/FileDelete.js");
/* harmony import */ var _icons_FileIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../icons/FileIcon */ "./components/icons/FileIcon.js");





const FileUploadProgress = ({
  fileName,
  fileSize,
  fileID,
  fileDatas,
  formState
}, ref) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-progress-wrapper",
    ref: ref
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "progress-content-wrapper"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "progress-icon-details"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_FileIcon__WEBPACK_IMPORTED_MODULE_3__["default"], null), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "progress-details"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h5", {
    className: "brand-info-title"
  }, fileName), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "brand-info-sub"
  }, fileSize + "mb"))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_FileDelete__WEBPACK_IMPORTED_MODULE_2__["default"], {
    className: "progress-remove-icon",
    onClick: () => {
      let filteredFiles = fileDatas.filter((file, index) => {
        return index != fileID;
      });
      formState(previousState => {
        return {
          ...previousState,
          fileData: [...filteredFiles]
        };
      });
    }
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(FileUploadProgress));

/***/ }),

/***/ "./components/Features/Feedback/MessageSent.js":
/*!*****************************************************!*\
  !*** ./components/Features/Feedback/MessageSent.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _assets_images_msg_receive_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/msg-receive.png */ "./assets/images/msg-receive.png");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__);





const MessageSent = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-msg-receive"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: _assets_images_msg_receive_png__WEBPACK_IMPORTED_MODULE_2__,
    alt: "receiveImg"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_3__.MESSAGE_SENT_TEXT?.title)), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_4__.__)(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_3__.MESSAGE_SENT_TEXT?.text)));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageSent);

/***/ }),

/***/ "./components/Features/Feedback/SendMessage.js":
/*!*****************************************************!*\
  !*** ./components/Features/Feedback/SendMessage.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");


// import Arrow from "../../icons/Arrow";

const SendMessage = () => {
  const {
    setTab
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  function sendMsgClickHandler() {
    //When Clicked Set The Single Doc View
    setTab('feedback_form');
  }
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-send-message",
    onClick: sendMsgClickHandler
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-sm-info"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h5", null, "Send us a message"), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "We typically reply in a few minutes")), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-sm-icon"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("i", {
    className: "ia-icon ia-send"
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SendMessage);

/***/ }),

/***/ "./components/Features/Search/Search.js":
/*!**********************************************!*\
  !*** ./components/Features/Search/Search.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _icons_Magnifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../icons/Magnifier */ "./components/icons/Magnifier.js");
/* harmony import */ var _icons_SearchIconClose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../icons/SearchIconClose */ "./components/icons/SearchIconClose.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");




const Search = ({
  searchCallback,
  searchKeyword
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    className: "betterdocs-ia-search"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    className: "betterdocs-ia-search-field",
    onChange: event => searchCallback(event?.target?.value),
    name: "betterdocs-ia-search",
    placeholder: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_3__.SEARCH?.SEARCH_PLACEHOLDER,
    value: searchKeyword
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-search-icon"
  }, searchKeyword?.length > 0 ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_SearchIconClose__WEBPACK_IMPORTED_MODULE_2__["default"], {
    onClick: () => searchCallback("")
  }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_Magnifier__WEBPACK_IMPORTED_MODULE_1__["default"], null)));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Search);

/***/ }),

/***/ "./components/Launcher/Launcher.js":
/*!*****************************************!*\
  !*** ./components/Launcher/Launcher.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _icons_ActiveIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../icons/ActiveIcon */ "./components/icons/ActiveIcon.js");



const Launcher = ({
  toggleState,
  setToggleState
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-launcher-wrapper"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    className: "betterdocs-ia-launcher",
    onClick: () => setToggleState(!toggleState)
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_ActiveIcon__WEBPACK_IMPORTED_MODULE_2__["default"], {
    toogle: toggleState
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Launcher);

/***/ }),

/***/ "./components/Launcher/Preview.js":
/*!****************************************!*\
  !*** ./components/Launcher/Preview.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/hooks */ "@wordpress/hooks");
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _icons_ResourcesIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../icons/ResourcesIcon */ "./components/icons/ResourcesIcon.js");
/* harmony import */ var _icons_ResourcesActiveIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../icons/ResourcesActiveIcon */ "./components/icons/ResourcesActiveIcon.js");
/* harmony import */ var _icons_TabIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../icons/TabIcon */ "./components/icons/TabIcon.js");
/* harmony import */ var _icons_SendMessageIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../icons/SendMessageIcon */ "./components/icons/SendMessageIcon.js");
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _Tab_Tab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../Tab/Tab */ "./components/Tab/Tab.js");
/* harmony import */ var _Tab_TabContent__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../Tab/TabContent */ "./components/Tab/TabContent.js");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _Pages_DocListFaq__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../Pages/DocListFaq */ "./Pages/DocListFaq.js");
/* harmony import */ var _Pages_DocViewPage__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../Pages/DocViewPage */ "./Pages/DocViewPage.js");
/* harmony import */ var _Pages_FaqPage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../Pages/FaqPage */ "./Pages/FaqPage.js");
/* harmony import */ var _Pages_HomePage__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../Pages/HomePage */ "./Pages/HomePage.js");
/* harmony import */ var _Features_Docs_DocList__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../Features/Docs/DocList */ "./components/Features/Docs/DocList.js");
/* harmony import */ var _Features_Feedback_FeedBackForm__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../Features/Feedback/FeedBackForm */ "./components/Features/Feedback/FeedBackForm.js");
/* harmony import */ var _Features_Feedback_SendMessage__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../Features/Feedback/SendMessage */ "./components/Features/Feedback/SendMessage.js");
/* harmony import */ var _Features_Search_Search__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../Features/Search/Search */ "./components/Features/Search/Search.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");











//localization package


// Pages





// Features





//BDSettings Constants

const Preview = () => {
  const [selectedTab, setTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);
  const [termInfo, setTermInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
    taxonomy: "doc_category"
  });
  const mainWrapperStyleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  const [navigationHistory, setNavigationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);
  const [singleDocInfo, setSingleDocInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});
  const [feedbackFormInfo, setFeedbackFormInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
    email: "",
    name: "",
    subject: "",
    message: "",
    fileData: []
  });
  const [validation, setValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({
    message: "",
    fieldType: ""
  });
  const [activeTabClass, setactiveTabClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("home");
  const [messageStatus, setMessageStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("idle");
  const [buttonStatus, setButtonStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("idle");
  const [feedbackText, setFeedbackText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.FEEDBACK_TEXT);
  const Tabs = [{
    id: "home",
    class: "betterdocs-ia-home",
    type: "tab",
    title: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_TAB_TITLE != undefined && _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_TAB_TITLE != '' ? _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_TAB_TITLE : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("Home", "betterdocs"),
    default: true,
    icon: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_TabIcon__WEBPACK_IMPORTED_MODULE_5__["default"], {
      active: activeTabClass === "home",
      icon: "home"
    }),
    component: _Pages_HomePage__WEBPACK_IMPORTED_MODULE_14__["default"],
    require_components: {
      DocList: _Features_Docs_DocList__WEBPACK_IMPORTED_MODULE_15__["default"]
    },
    // Enable only if either HOME_DOCS_SWITCH or HOME_FAQ_SWITCH is true
    showTab: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_DOCS_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_FAQ_SWITCH,
    showTabInComponent: true
  }, ...(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.AI_CHATBOT ? (0,_wordpress_hooks__WEBPACK_IMPORTED_MODULE_2__.applyFilters)('tab_chatbot_preview', [], activeTabClass) : []), {
    id: "feedback_tab",
    class: "betterdocs-ia-message",
    type: "tab",
    title: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.CHAT?.label != "" && _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.CHAT?.label != undefined ? _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.CHAT?.label : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("Message", "betterdocs"),
    default: false,
    icon: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_TabIcon__WEBPACK_IMPORTED_MODULE_5__["default"], {
      active: activeTabClass === "feedback_tab",
      icon: "feedback_tab"
    }),
    component: _Features_Feedback_FeedBackForm__WEBPACK_IMPORTED_MODULE_16__["default"],
    showTab: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.CHAT?.show,
    showTabInComponent: false
  }, {
    id: "resources",
    class: "betterdocs-ia-faq",
    type: "tab",
    title: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_TAB_TITLE != "" && _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_TAB_TITLE != undefined ? _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_TAB_TITLE : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_10__.__)("Resources", "betterdocs"),
    default: false,
    icon: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_TabIcon__WEBPACK_IMPORTED_MODULE_5__["default"], {
      active: activeTabClass === "resources",
      icon: "resources"
    }),
    component: _Pages_FaqPage__WEBPACK_IMPORTED_MODULE_13__["default"],
    require_components: {
      DocList: _Features_Docs_DocList__WEBPACK_IMPORTED_MODULE_15__["default"]
    },
    // Show if either doc categories or FAQs are enabled
    showTab: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_DOC_CATEGORY_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_FAQ_SWITCH,
    showTabInComponent: true
  }, {
    id: "feedback_form",
    class: "",
    type: "page",
    title: "",
    default: false,
    icon: "",
    component: _Features_Feedback_FeedBackForm__WEBPACK_IMPORTED_MODULE_16__["default"],
    showTab: false
  }, {
    id: "single_doc_view",
    class: "",
    type: "page",
    title: "",
    default: false,
    icon: "",
    component: _Pages_DocViewPage__WEBPACK_IMPORTED_MODULE_12__["default"],
    showTab: false
  }, {
    id: "doc_list",
    class: "",
    type: "page",
    title: "",
    default: false,
    icon: "",
    require_components: {
      DocList: _Features_Docs_DocList__WEBPACK_IMPORTED_MODULE_15__["default"]
    },
    component: _Pages_DocListFaq__WEBPACK_IMPORTED_MODULE_11__["default"],
    showTab: true,
    showTabInComponent: true
  }];

  // Function to select the appropriate tab based on visible tabs
  const selectAppropriateTab = () => {
    // Find the default tab, but only if it's visible (showTab is true)
    const defaultTab = Tabs?.find(tab => tab?.default && tab?.showTab);

    // If there's no visible default tab, find the first visible tab
    if (defaultTab) {
      setTab(defaultTab.id);
    } else {
      const firstVisibleTab = Tabs?.find(tab => tab?.showTab);
      if (firstVisibleTab) {
        setTab(firstVisibleTab.id);
      }
    }
  };

  // Initial tab selection when component mounts
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    selectAppropriateTab();
  }, []);

  // Re-select tab when any of the tab visibility switches change
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    selectAppropriateTab();
  }, [_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_DOCS_SWITCH, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.HOME_FAQ_SWITCH, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_DOC_CATEGORY_SWITCH, _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_19__.RESOURCES_FAQ_SWITCH]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    if (!navigationHistory.includes(selectedTab)) {
      setNavigationHistory(previousState => {
        if (selectedTab === "home") {
          return [{
            id: "home"
          }];
        }
        if (selectedTab === "resources") {
          return [{
            id: "resources"
          }];
        }
        if (selectedTab === "chatbot") {
          return [{
            id: "chatbot"
          }];
        }
        if (selectedTab != "resources" && !navigationHistory.includes(selectedTab) || selectedTab != "home" && !navigationHistory.includes(selectedTab)) {
          let prevList = [...previousState];
          if (prevList[prevList.length - 1]?.id != selectedTab) {
            prevList.push({
              id: selectedTab
            });
          }
          return prevList;
        }
      });
    }
  }, [selectedTab]);
  const SelectedTab = Tabs?.find(tab => tab?.id == selectedTab);
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-main-wrapper",
    ref: mainWrapperStyleRef
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_7__["default"].Provider, {
    value: {
      Tabs,
      selectedTab,
      setTab,
      termInfo,
      setTermInfo,
      singleDocInfo,
      setSingleDocInfo,
      mainWrapperStyleRef,
      setNavigationHistory,
      navigationHistory,
      feedbackFormInfo,
      setFeedbackFormInfo,
      validation,
      setValidation,
      activeTabClass,
      setactiveTabClass,
      messageStatus,
      setMessageStatus,
      buttonStatus,
      setButtonStatus,
      feedbackText,
      setFeedbackText
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Tab_TabContent__WEBPACK_IMPORTED_MODULE_9__["default"], null), SelectedTab?.showTab && SelectedTab?.showTabInComponent &&
  // Only show Tab component if there are at least 2 visible tabs
  Tabs.filter(tab => tab?.showTab && tab?.type === "tab").length > 1 ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Tab_Tab__WEBPACK_IMPORTED_MODULE_8__["default"], null) : "")));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Preview);

/***/ }),

/***/ "./components/Partials/Header/BackButton.js":
/*!**************************************************!*\
  !*** ./components/Partials/Header/BackButton.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _icons_BackIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../icons/BackIcon */ "./components/icons/BackIcon.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");






const BackButton = ({
  layout
}) => {
  const {
    Tabs,
    selectedTab,
    setNavigationHistory,
    setTab,
    setactiveTabClass,
    mainWrapperStyleRef
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  let buttonClass = layout == "single-doc" ? "content-icon-back" : "header__back header__button";
  const {
    current
  } = mainWrapperStyleRef;
  const {
    style
  } = current;

  // Find the first visible tab to use as default
  const getFirstVisibleTab = () => {
    const visibleTabs = Tabs.filter(tab => tab.showTab && tab.type === "tab");
    return visibleTabs.length > 0 ? visibleTabs[0].id : null;
  };

  // Get the next available tab that's not the current one
  const getNextAvailableTab = currentTabId => {
    // Define the tab priority order
    const tabPriority = ["home", "resources", "chatbot", "feedback_tab"];

    // Create a map of enabled tabs
    const enabledTabs = {
      home: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.HOME_DOCS_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.HOME_FAQ_SWITCH,
      resources: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.RESOURCES_DOC_CATEGORY_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.RESOURCES_FAQ_SWITCH,
      chatbot: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.AI_CHATBOT,
      feedback_tab: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.CHAT?.show
    };

    // Filter out the current tab and disabled tabs, then get the first one in priority order
    const availableTabs = tabPriority.filter(tabId => tabId !== currentTabId && enabledTabs[tabId]);
    return availableTabs.length > 0 ? availableTabs[0] : getFirstVisibleTab();
  };
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: buttonClass,
    onClick: () => {
      // Special handling for resources tab - if we're on resources tab,
      // we need to navigate to a different tab
      if (selectedTab === "resources") {
        const nextTab = getNextAvailableTab("resources");
        if (nextTab) {
          setTab(nextTab);
          setactiveTabClass(nextTab);
          setNavigationHistory([{
            id: nextTab
          }]);
          if (style?.width != "" && style?.height != "") {
            Object.assign(style, {
              width: "",
              height: ""
            });
          }
          return;
        }
      }

      // Normal back button behavior for other tabs
      setNavigationHistory(previousState => {
        let prevList = [...previousState];
        if (prevList?.length > 0) {
          prevList.pop();

          // If we've emptied the navigation history, add a default tab
          if (prevList?.length < 1) {
            // Check if home tab is enabled
            const homeTabEnabled = _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.HOME_DOCS_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.HOME_FAQ_SWITCH;
            if (homeTabEnabled) {
              // Home tab is enabled, use it
              prevList.push({
                id: "home"
              });
            } else if (_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.RESOURCES_DOC_CATEGORY_SWITCH || _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.RESOURCES_FAQ_SWITCH) {
              // Home tab is disabled but resources tab is enabled
              prevList.push({
                id: "resources"
              });
            } else if (_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.AI_CHATBOT) {
              // Both home and resources are disabled but chatbot is enabled
              prevList.push({
                id: "chatbot"
              });
            } else if (_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.CHAT?.show) {
              // All other tabs are disabled but feedback tab is enabled
              prevList.push({
                id: "feedback_tab"
              });
            } else {
              // Fallback to the first visible tab
              const firstVisibleTab = getFirstVisibleTab();
              if (firstVisibleTab) {
                prevList.push({
                  id: firstVisibleTab
                });
              }
            }
          }
          let currentTab = prevList[prevList.length - 1];
          setTab(currentTab?.id);

          // Update active tab class for main tabs
          if (currentTab?.id === 'home' || currentTab?.id === 'resources' || currentTab?.id === 'chatbot' || currentTab?.id === 'feedback_tab') {
            setactiveTabClass(currentTab?.id);
          }
          return prevList;
        }
        return prevList;
      });
      if (style?.width != "" && style?.height != "") {
        Object.assign(style, {
          width: "",
          height: ""
        });
      }
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_BackIcon__WEBPACK_IMPORTED_MODULE_3__["default"], null));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackButton);

/***/ }),

/***/ "./components/Partials/Header/Expander.js":
/*!************************************************!*\
  !*** ./components/Partials/Header/Expander.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../Tab/Context/TabContext */ "./components/Tab/Context/TabContext.js");
/* harmony import */ var _components_icons_Expand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/icons/Expand */ "./components/icons/Expand.js");





const Expander = () => {
  const {
    mainWrapperStyleRef
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Tab_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  const {
    current
  } = mainWrapperStyleRef;
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "content-icon-expand",
    onClick: () => {
      let {
        style
      } = current;
      let {
        width,
        height
      } = style;
      if (width === "" && height === "") {
        Object.assign(style, {
          width: "686px",
          height: "800px"
        });
      } else {
        Object.assign(style, {
          width: "",
          height: ""
        });
      }
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_icons_Expand__WEBPACK_IMPORTED_MODULE_3__["default"], null));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Expander);

/***/ }),

/***/ "./components/Partials/Header/FormHeader.js":
/*!**************************************************!*\
  !*** ./components/Partials/Header/FormHeader.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BackButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BackButton */ "./components/Partials/Header/BackButton.js");



const FormHeader = ({
  enableBackButton,
  heading,
  subHeading
}) => {
  let Back = enableBackButton ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_BackButton__WEBPACK_IMPORTED_MODULE_2__["default"], null) : '';
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `message__header betterdocs-ia-common-header`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-header-group"
  }, Back, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "header__content"
  }, heading ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", null, heading) : null, subHeading ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, subHeading) : null)));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FormHeader);

/***/ }),

/***/ "./components/Partials/Header/Header.js":
/*!**********************************************!*\
  !*** ./components/Partials/Header/Header.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BackButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BackButton */ "./components/Partials/Header/BackButton.js");
/* harmony import */ var _Features_Search_Search__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../Features/Search/Search */ "./components/Features/Search/Search.js");
/* harmony import */ var _icons_BdocsIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../icons/BdocsIcon */ "./components/icons/BdocsIcon.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");






const Header = ({
  headingContent,
  headingLayout,
  headingSubContent,
  enableLogo,
  enableSearch,
  extraClass,
  enableBackButton,
  searchCallback,
  searchKeyword
}) => {
  let ExtraClass = extraClass != undefined ? ' ' + extraClass : '';
  let Heading = headingContent && headingLayout == 'home-layout' ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h1", {
    className: "betterdocs-title"
  }, headingContent) : headingContent && (headingLayout == 'resources-list' || headingLayout == 'resources-category') ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h2", null, headingContent) : '';
  let Logo = enableLogo ? _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.HEADER_LOGO != undefined && !Array.isArray(_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.HEADER_LOGO) ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.HEADER_LOGO?.url,
    width: 100,
    height: 20
  }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "betterdocs-logo"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_BdocsIcon__WEBPACK_IMPORTED_MODULE_4__["default"], null)) : '';
  let SubHeading = headingSubContent ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", {
    className: "betterdocs-info"
  }, headingSubContent) : '';
  let SearchComponent = enableSearch ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Features_Search_Search__WEBPACK_IMPORTED_MODULE_3__["default"], {
    searchCallback: searchCallback,
    searchKeyword: searchKeyword
  }) : '';
  let Back = enableBackButton ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_BackButton__WEBPACK_IMPORTED_MODULE_2__["default"], null) : '';
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-common-header${ExtraClass}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-header-group"
  }, Logo, Back, Heading, SubHeading), SearchComponent);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);

/***/ }),

/***/ "./components/Partials/Header/SingleHeader.js":
/*!****************************************************!*\
  !*** ./components/Partials/Header/SingleHeader.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BackButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BackButton */ "./components/Partials/Header/BackButton.js");
/* harmony import */ var _Expander__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Expander */ "./components/Partials/Header/Expander.js");




const SingleHeader = ({
  scrollData,
  scrollTitle,
  contentTitle
}) => {
  const {
    current
  } = contentTitle;
  const heightThreshold = current?.offsetHeight != undefined ? current?.offsetHeight : 0;
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-singleDoc-header${scrollData > heightThreshold ? ' on-scroll' : ''}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_BackButton__WEBPACK_IMPORTED_MODULE_2__["default"], {
    layout: "single-doc"
  }), scrollData > heightThreshold && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("h2", null, scrollTitle), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Expander__WEBPACK_IMPORTED_MODULE_3__["default"], null));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleHeader);

/***/ }),

/***/ "./components/Partials/Single/DocFeedback.js":
/*!***************************************************!*\
  !*** ./components/Partials/Single/DocFeedback.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _icons_Neutral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../icons/Neutral */ "./components/icons/Neutral.js");
/* harmony import */ var _icons_Sad__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../icons/Sad */ "./components/icons/Sad.js");
/* harmony import */ var _icons_Happy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../icons/Happy */ "./components/icons/Happy.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../BDSettings/Constants */ "./components/BDSettings/Constants.js");






const DocFeedback = ({
  sadOnclick,
  neutralOnclick,
  happyOnClick,
  reactionText
}) => {
  let successClass = _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.FEEDBACK_TEXT == reactionText ? '' : ' success';
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-footer-feedback${successClass}`
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, reactionText), _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.FEEDBACK_TEXT == reactionText && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-reaction-group"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_Happy__WEBPACK_IMPORTED_MODULE_4__["default"], {
    clickHandler: happyOnClick
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_Sad__WEBPACK_IMPORTED_MODULE_3__["default"], {
    clickHandler: sadOnclick
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_icons_Neutral__WEBPACK_IMPORTED_MODULE_2__["default"], {
    clickHandler: neutralOnclick
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocFeedback);

/***/ }),

/***/ "./components/Tab/Context/TabContext.js":
/*!**********************************************!*\
  !*** ./components/Tab/Context/TabContext.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

const TabContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabContext);

/***/ }),

/***/ "./components/Tab/Tab.js":
/*!*******************************!*\
  !*** ./components/Tab/Tab.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Context/TabContext */ "./components/Tab/Context/TabContext.js");



const Tab = () => {
  const {
    Tabs,
    setTab,
    activeTabClass,
    setactiveTabClass
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  const handleSelectedTab = selectedId => {
    let newSelectedTab = Tabs.find(tab => {
      return tab?.id == selectedId;
    });
    //do not set active class for send message tab
    if (selectedId != "feedback_tab") {
      setactiveTabClass(selectedId);
    }
    setTab(newSelectedTab?.id);
  };
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("ul", {
    className: "betterdocs-ia-tabs"
  }, Tabs.filter(tab => tab?.showTab == true && tab?.type == "tab").map(tab => (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("li", {
    className: activeTabClass == tab?.id ? tab?.class + " active" : tab?.class,
    key: Math.random(),
    onClick: () => handleSelectedTab(tab?.id)
  }, tab?.icon, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, tab?.title))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Tab);

/***/ }),

/***/ "./components/Tab/TabContent.js":
/*!**************************************!*\
  !*** ./components/Tab/TabContent.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Context_TabContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Context/TabContext */ "./components/Tab/Context/TabContext.js");



const TabContent = () => {
  const {
    selectedTab,
    Tabs
  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_Context_TabContext__WEBPACK_IMPORTED_MODULE_2__["default"]);
  const DynamicComponent = Tabs?.find(value => value.id == selectedTab)?.component;
  const RequiredComponents = Tabs?.find(value => value.id == selectedTab)?.require_components;
  const contentDynamicClass = selectedTab == 'home' ? ' home-content' : selectedTab == 'resources' ? ' resources-content' : '';
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: `betterdocs-ia-main-content${contentDynamicClass}`
  }, DynamicComponent && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(DynamicComponent, RequiredComponents));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabContent);

/***/ }),

/***/ "./components/Util/DocListLoader.js":
/*!******************************************!*\
  !*** ./components/Util/DocListLoader.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_content_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-content-loader */ "./node_modules/.pnpm/react-content-loader@6.2.1_react@18.3.1/node_modules/react-content-loader/dist/react-content-loader.es.js");



const DocListLoader = ({
  width,
  height
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_content_loader__WEBPACK_IMPORTED_MODULE_2__["default"], {
    speed: 2,
    width: width,
    height: height,
    viewBox: "0 0 343 397",
    backgroundColor: "#f3f3f3",
    foregroundColor: "#ecebeb",
    style: {
      width: '100%',
      height: 'auto'
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "220",
    y: "130",
    rx: "0",
    ry: "0",
    width: "1",
    height: "1"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "387",
    y: "304",
    rx: "0",
    ry: "0",
    width: "1",
    height: "0"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "1",
    rx: "0",
    ry: "0",
    width: "2",
    height: "394"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "16",
    y: "67",
    rx: "0",
    ry: "0",
    width: "0",
    height: "1"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "155",
    y: "64",
    rx: "0",
    ry: "0",
    width: "1",
    height: "1"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "216",
    y: "79",
    rx: "0",
    ry: "0",
    width: "0",
    height: "1"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "259",
    y: "89",
    rx: "0",
    ry: "0",
    width: "1",
    height: "9"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "337",
    y: "1",
    rx: "0",
    ry: "0",
    width: "2",
    height: "394"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "1",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "29",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "54",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "79",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "104",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "131",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "156",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "181",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "209",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "9",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "19",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "308",
    y: "15",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "41",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "66",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "91",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "116",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "144",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "169",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "309",
    y: "194",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "35",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "60",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "70",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "85",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "95",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "45",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "11",
    y: "110",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "122",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "137",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "147",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "162",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "171",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "187",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "198",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "240",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "272",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "304",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "335",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "2",
    y: "365",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "3",
    y: "393",
    rx: "0",
    ry: "0",
    width: "335",
    height: "2"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "218",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "229",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "247",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "260",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "280",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "293",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "312",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "323",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "342",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "12",
    y: "354",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "13",
    y: "371",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "13",
    y: "382",
    rx: "0",
    ry: "0",
    width: "45",
    height: "6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "308",
    y: "222",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "307",
    y: "254",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "306",
    y: "288",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "306",
    y: "318",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "306",
    y: "348",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", {
    x: "305",
    y: "377",
    rx: "0",
    ry: "0",
    width: "20",
    height: "4"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocListLoader);

/***/ }),

/***/ "./components/Util/GenericLoader.js":
/*!******************************************!*\
  !*** ./components/Util/GenericLoader.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const GenericLoader = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "generic-loader"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 40,
    height: 40,
    preserveAspectRatio: "xMidYMid",
    style: {
      background: "0 0",
      shapeRendering: "auto"
    },
    viewBox: "0 0 100 100"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", {
    cx: 50,
    cy: 50,
    r: 26,
    fill: "none",
    stroke: "#16ca9e",
    strokeDasharray: "122.52211349000194 42.840704496667314",
    strokeWidth: 10
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("animateTransform", {
    attributeName: "transform",
    dur: "1s",
    keyTimes: "0;1",
    repeatCount: "indefinite",
    type: "rotate",
    values: "0 50 50;360 50 50"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GenericLoader);

/***/ }),

/***/ "./components/hooks/useDebounce.js":
/*!*****************************************!*\
  !*** ./components/hooks/useDebounce.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useDebounce: () => (/* binding */ useDebounce)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);


/**
 *
 * @param {string|number} searchKeyword
 * @param {number} delayTimeout
 *
 * @returns {string|number} returns debouncedKeyword
 */
const useDebounce = (searchKeyword, delay = 500) => {
  const [debouncedKeyword, setDebouncedKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(searchKeyword);

  /**
   * Debounce The Search Keyword
   */
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    let timeout = setTimeout(() => {
      setDebouncedKeyword(searchKeyword);
    }, delay);
    return () => {
      clearTimeout(timeout);
    };
  }, [searchKeyword, delay]);
  return debouncedKeyword;
};

/***/ }),

/***/ "./components/hooks/useSearchResults.js":
/*!**********************************************!*\
  !*** ./components/hooks/useSearchResults.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useSearchResults: () => (/* binding */ useSearchResults)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Api_search_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../Api/search-api */ "./components/Api/search-api.js");
/* harmony import */ var _Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Api/search-insert-api */ "./components/Api/search-insert-api.js");
/* harmony import */ var _useDebounce__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useDebounce */ "./components/hooks/useDebounce.js");
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");






/**
 * @param {string} searchKeyword
 * @param {boolean} searchDocs
 * @param {boolean} searchFaq
 *
 * @returns {Array} returns an array of states like [searchMode, Loading, searchResults]
 */
const useSearchResults = (searchKeyword, searchDocs, searchFaq) => {
  const debounedKeyword = (0,_useDebounce__WEBPACK_IMPORTED_MODULE_3__.useDebounce)(searchKeyword, 300);
  const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);
  const [searchMode, setSearchMode] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [Loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    /**
     * Get The Search Results After The Debounce Value Is Attained
     */
    async function fetchResults() {
      var docsResponse;
      var faqResponse;
      var allResponse;
      switch (true) {
        case searchDocs && searchFaq:
          setLoading(true);
          docsResponse = (0,_Api_search_api__WEBPACK_IMPORTED_MODULE_1__.searchEndpoint)("docs", debounedKeyword);
          faqResponse = (0,_Api_search_api__WEBPACK_IMPORTED_MODULE_1__.searchEndpoint)("betterdocs_faq", debounedKeyword);
          allResponse = await Promise.all([docsResponse, faqResponse]);
          let [docResults, faqResults] = allResponse;

          // Insert search keyword based on whether docResults has values or not
          if (debounedKeyword.length >= _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.SEARCH_LETTER_LIMIT) {
            if (docResults.length > 0) {
              await (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(searchKeyword, 0); // Has results
            } else {
              await (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(searchKeyword, 1); // No results
            }
          }
          setSearchResults([...docResults, ...faqResults]);
          setLoading(false);
          break;
        case searchDocs:
          setLoading(true);
          docsResponse = await (0,_Api_search_api__WEBPACK_IMPORTED_MODULE_1__.searchEndpoint)("docs", debounedKeyword);

          // Insert after checking results for docs
          if (debounedKeyword.length >= _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.SEARCH_LETTER_LIMIT) {
            if (docsResponse.length) {
              (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(debounedKeyword, 0);
            } else {
              (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(debounedKeyword, 1);
            }
          }
          setSearchResults([...docsResponse]);
          setLoading(false);
          break;
        case searchFaq:
          setLoading(true);
          faqResponse = await (0,_Api_search_api__WEBPACK_IMPORTED_MODULE_1__.searchEndpoint)("betterdocs_faq", debounedKeyword);

          // Insert after checking results for FAQs
          if (debounedKeyword.length >= _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_4__.SEARCH_LETTER_LIMIT) {
            if (faqResponse.length) {
              (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(debounedKeyword, 0);
            } else {
              (0,_Api_search_insert_api__WEBPACK_IMPORTED_MODULE_2__.searchInsertEndpoint)(debounedKeyword, 1);
            }
          }
          setSearchResults([...faqResponse]);
          setLoading(false);
          break;
      }
    }
    if (debounedKeyword.length !== 0) {
      setSearchMode(true);
      fetchResults();
    } else {
      setSearchMode(false);
    }
  }, [debounedKeyword]);
  return [searchMode, Loading, searchResults];
};

/***/ }),

/***/ "./components/icons/ActiveIcon.js":
/*!****************************************!*\
  !*** ./components/icons/ActiveIcon.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _LauncherIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LauncherIcon */ "./components/icons/LauncherIcon.js");
/* harmony import */ var _LauncherIconClose__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LauncherIconClose */ "./components/icons/LauncherIconClose.js");
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wordpress/hooks */ "@wordpress/hooks");
/* harmony import */ var _wordpress_hooks__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");






const ActiveIcon = ({
  toogle
}) => {
  const [activeOpenImgUrl, setActiveOpenImgUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);
  const [activeCloseImgUrl, setActiveCloseImgUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);
  const [preview, setPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    (0,_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__.addAction)("openIconPreviewAction", "instant_answer", value => {
      const {
        img_url,
        preview
      } = value;
      setActiveOpenImgUrl(img_url);
      setPreview(preview);
    });
    (0,_wordpress_hooks__WEBPACK_IMPORTED_MODULE_4__.addAction)("closeIconPreviewAction", "instant_answer", value => {
      const {
        img_url,
        preview
      } = value;
      setActiveCloseImgUrl(img_url);
      setPreview(preview);
    });
  }, []);

  /**
   * When toogle is false and preview is true from back settings  (Backend)
   */
  if (!toogle && preview) {
    return activeOpenImgUrl != undefined ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: activeOpenImgUrl,
      height: 25,
      width: 25
    }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_LauncherIcon__WEBPACK_IMPORTED_MODULE_2__["default"], null);
  }

  /**
   * When toogle is true and preview is true from back settings (Backend)
   */
  if (toogle && preview) {
    return activeCloseImgUrl != undefined ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: activeCloseImgUrl,
      height: 25,
      width: 25
    }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_LauncherIconClose__WEBPACK_IMPORTED_MODULE_3__["default"], null);
  }

  /**
   * When toogle is true and preview is false(front-end) from back settings (FrontEnd)
   */
  if (toogle && !preview) {
    return _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.LAUNCHER_CLOSE_ICON != undefined ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.LAUNCHER_CLOSE_ICON,
      height: 25,
      width: 25
    }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_LauncherIconClose__WEBPACK_IMPORTED_MODULE_3__["default"], null);
  }

  /**
   * When toogle is false and preview is false(front-end) (FrontEnd)
   */
  if (!toogle && !preview) {
    return _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.LAUNCHER_OPEN_ICON != undefined ? (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_5__.LAUNCHER_OPEN_ICON,
      height: 25,
      width: 25
    }) : (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_LauncherIcon__WEBPACK_IMPORTED_MODULE_2__["default"], null);
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActiveIcon);

/***/ }),

/***/ "./components/icons/BackIcon.js":
/*!**************************************!*\
  !*** ./components/icons/BackIcon.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const BackIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M15.675 21.3 6.9 12.525a.762.762 0 0 1-.175-.25.734.734 0 0 1-.05-.275c0-.1.017-.192.05-.275a.762.762 0 0 1 .175-.25L15.675 2.7a.948.948 0 0 1 .7-.275c.283 0 .517.092.7.275.2.2.3.437.3.712 0 .275-.1.513-.3.713L9.2 12l7.875 7.875c.217.217.317.458.3.725a1.011 1.011 0 0 1-.3.675c-.2.2-.438.3-.712.3a.933.933 0 0 1-.688-.275Z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackIcon);

/***/ }),

/***/ "./components/icons/BdocsIcon.js":
/*!***************************************!*\
  !*** ./components/icons/BdocsIcon.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const BdocsIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    width: "190",
    viewBox: "0 0 366 85",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M63.063 32.6198H44.0562C42.1943 32.6198 40.6873 31.1128 40.6873 29.2509C40.6873 27.389 42.1943 25.882 44.0562 25.882H63.063C64.9249 25.882 66.4319 27.389 66.4319 29.2509C66.4319 31.1128 64.9249 32.6198 63.063 32.6198Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M55.4024 45.7461H36.4065C34.5446 45.7461 33.0376 44.2391 33.0376 42.3772C33.0376 40.5152 34.5446 39.0082 36.4065 39.0082H55.4024C57.2643 39.0082 58.7713 40.5152 58.7713 42.3772C58.7713 44.2391 57.2643 45.7461 55.4024 45.7461Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M47.6545 59.1016H28.6476C26.7857 59.1016 25.2787 57.5946 25.2787 55.7327C25.2787 53.8708 26.7857 52.3638 28.6476 52.3638H47.6545C49.5164 52.3638 51.0234 53.8708 51.0234 55.7327C51.0234 57.5946 49.5164 59.1016 47.6545 59.1016Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M77.0901 40.2531C76.97 40.002 76.8171 39.7727 76.6479 39.5597L81.7204 30.8235C84.4832 26.0731 84.4996 20.3891 81.7586 15.6224C79.0121 10.8502 74.0925 8 68.5832 8H45.6342C40.2559 8 35.2217 10.8993 32.497 15.5678L10.0831 54.1765C7.32026 58.9269 7.30388 64.6109 10.0449 69.3776C12.7913 74.1498 17.7109 77 23.2202 77H62.5443C69.5497 77 75.9599 73.4618 79.6892 67.5266C83.4185 61.5969 83.8334 54.2857 80.7976 47.9738L77.0901 40.2531ZM29.0189 69.8745H23.2148C20.2936 69.8745 17.6782 68.362 16.2203 65.8285C14.7679 63.3005 14.7734 60.2865 16.2422 57.7639L38.667 19.1496C40.114 16.6653 42.7894 15.1255 45.6451 15.1255H68.5941C71.5153 15.1255 74.1307 16.638 75.5886 19.1715C77.041 21.6995 77.0355 24.7135 75.5668 27.2361L53.131 65.8558C51.7004 68.3293 49.0359 69.869 46.1747 69.869H29.0189V69.8745Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M124.384 42.1533C125.82 41.4325 126.913 40.4279 127.65 39.1338C128.387 37.8397 128.758 36.3819 128.758 34.7657C128.758 33.5044 128.534 32.3359 128.081 31.2603C127.633 30.1791 126.929 29.2455 125.979 28.4537C125.023 27.662 123.822 27.045 122.364 26.5918C120.906 26.1441 119.17 25.9148 117.155 25.9148H104.433C104.127 25.9148 103.882 26.1605 103.882 26.4662V59.4183C103.882 59.6749 104.056 59.8988 104.302 59.9534C105.672 60.2646 107.212 60.5212 108.927 60.7178C110.816 60.9362 112.82 61.0399 114.944 61.0399C117.641 61.0399 119.951 60.7997 121.878 60.3137C123.8 59.8278 125.378 59.1343 126.601 58.2389C127.824 57.3379 128.725 56.235 129.299 54.9191C129.872 53.6087 130.161 52.1399 130.161 50.5237C130.161 48.4707 129.659 46.7398 128.649 45.3147C127.639 43.895 126.219 42.8412 124.384 42.1587V42.1533ZM110.516 31.9209C110.516 31.6152 110.767 31.364 111.073 31.364H116.129C118.214 31.3312 119.732 31.708 120.688 32.4997C121.638 33.2914 122.118 34.3725 122.118 35.7376C122.118 37.1791 121.643 38.2984 120.688 39.112C119.732 39.9201 118.231 40.3241 116.183 40.3241H111.078C110.772 40.3241 110.521 40.0729 110.521 39.7672V31.9264L110.516 31.9209ZM121.605 54.4004C120.328 55.2631 118.269 55.6944 115.43 55.6944C114.387 55.6944 113.486 55.6671 112.732 55.6125C112.126 55.5689 111.531 55.487 110.952 55.3614C110.701 55.3068 110.521 55.0774 110.521 54.8208V46.0464C110.521 45.7406 110.767 45.4949 111.073 45.4949H116.669C119.006 45.4949 120.732 45.9426 121.851 46.8435C122.965 47.7445 123.522 48.9839 123.522 50.5674C123.522 52.26 122.883 53.5322 121.605 54.4004Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M273.916 31.2275C272.278 29.715 270.302 28.5466 267.981 27.7221C265.66 26.8976 263.078 26.4826 260.239 26.4826H250.612C250.312 26.4826 250.066 26.7283 250.066 27.0287V60.5213C250.066 60.8216 250.312 61.0673 250.612 61.0673H260.239C263.078 61.0673 265.66 60.6523 267.981 59.8278C270.302 59.0033 272.278 57.8239 273.916 56.2951C275.554 54.7662 276.821 52.9426 277.722 50.8186C278.623 48.6946 279.071 46.3412 279.071 43.7531C279.071 41.165 278.623 38.8062 277.722 36.6877C276.821 34.5637 275.554 32.7509 273.916 31.2384V31.2275ZM271.541 48.5471C270.984 49.9504 270.176 51.1571 269.111 52.1617C268.052 53.1719 266.747 53.9527 265.202 54.5096C263.657 55.0666 261.877 55.345 259.862 55.345H257.257C256.952 55.345 256.706 55.0993 256.706 54.7935V32.7564C256.706 32.4506 256.952 32.2049 257.257 32.2049H259.862C261.877 32.2049 263.657 32.4833 265.202 33.0403C266.747 33.5972 268.052 34.3889 269.111 35.4155C270.171 36.442 270.979 37.6705 271.541 39.112C272.098 40.5535 272.376 42.1314 272.376 43.8623C272.376 45.5932 272.098 47.1548 271.541 48.558V48.5471Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M305.798 38.7516C304.581 37.5995 303.172 36.7095 301.567 36.0816C299.961 35.4537 298.225 35.137 296.358 35.137C294.49 35.137 292.797 35.4591 291.176 36.1089C289.554 36.7586 288.14 37.665 286.944 38.8335C285.743 40.002 284.793 41.3779 284.089 42.9614C283.384 44.5448 283.029 46.2538 283.029 48.0885C283.029 50.0323 283.373 51.7959 284.061 53.3739C284.749 54.9573 285.699 56.3224 286.917 57.4745C288.135 58.6266 289.554 59.5166 291.176 60.1445C292.797 60.7724 294.528 61.0891 296.358 61.0891C298.187 61.0891 299.912 60.7669 301.517 60.1172C303.123 59.4674 304.531 58.572 305.749 57.4199C306.967 56.2678 307.928 54.8918 308.632 53.292C309.336 51.6922 309.691 49.9558 309.691 48.083C309.691 46.2102 309.347 44.4301 308.659 42.8467C307.971 41.2633 307.021 39.8982 305.804 38.7461L305.798 38.7516ZM302.675 50.8895C302.342 51.7522 301.861 52.5057 301.244 53.1555C300.627 53.8052 299.901 54.3076 299.077 54.6679C298.247 55.0283 297.34 55.2085 296.352 55.2085C295.364 55.2085 294.457 55.0283 293.627 54.6679C292.798 54.3076 292.077 53.8052 291.46 53.1555C290.843 52.5057 290.368 51.7522 290.029 50.8895C289.696 50.0268 289.527 49.0931 289.527 48.083C289.527 47.0729 289.696 46.1392 290.029 45.2765C290.362 44.4138 290.843 43.6603 291.46 43.0105C292.077 42.3608 292.798 41.8584 293.627 41.498C294.457 41.1377 295.364 40.9575 296.352 40.9575C297.34 40.9575 298.247 41.1377 299.077 41.498C299.907 41.8584 300.627 42.3608 301.244 43.0105C301.861 43.6603 302.336 44.4138 302.675 45.2765C303.008 46.1392 303.177 47.0729 303.177 48.083C303.177 49.0931 303.008 50.0268 302.675 50.8895Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M331.603 54.7226C330.576 55.0447 329.31 55.2085 327.797 55.2085C326.683 55.2085 325.657 55.0283 324.723 54.668C323.789 54.3076 322.976 53.8162 322.293 53.1828C321.611 52.5549 321.081 51.7959 320.699 50.9168C320.322 50.0377 320.131 49.0931 320.131 48.083C320.131 47.0729 320.322 46.0791 320.699 45.1946C321.076 44.3155 321.605 43.5565 322.293 42.9286C322.976 42.3007 323.795 41.8038 324.75 41.4434C325.7 41.0831 326.754 40.9029 327.906 40.9029C329.31 40.9029 330.522 41.0722 331.548 41.4161C331.92 41.5417 332.264 41.6728 332.586 41.8147C332.952 41.9731 333.356 41.7165 333.356 41.3179V36.5402C333.356 36.3109 333.214 36.1034 333.001 36.027C332.264 35.7649 331.461 35.5574 330.582 35.4045C329.555 35.2243 328.414 35.137 327.158 35.137C325.253 35.137 323.473 35.47 321.818 36.1362C320.164 36.8023 318.733 37.7196 317.527 38.8881C316.32 40.0566 315.37 41.4216 314.665 42.9887C313.967 44.5557 313.612 46.2539 313.612 48.0885C313.612 49.9231 313.945 51.5775 314.611 53.161C315.277 54.7444 316.205 56.1204 317.39 57.2888C318.575 58.4573 320 59.3855 321.654 60.0681C323.309 60.7506 325.144 61.0946 327.158 61.0946C328.594 61.0946 329.79 60.9799 330.746 60.7451C331.537 60.5486 332.285 60.3356 332.99 60.1063C333.214 60.0353 333.361 59.8224 333.361 59.5876V54.8754C333.361 54.4878 332.968 54.2257 332.608 54.3731C332.296 54.4987 331.963 54.6188 331.608 54.728L331.603 54.7226Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M245.299 35.137H244.382C242.438 35.137 240.8 35.5956 239.473 36.5129C238.141 37.4303 237.207 38.5714 236.667 39.9365L236.334 35.7376C236.312 35.4373 236.044 35.208 235.739 35.2353L230.884 35.6775C230.59 35.7048 230.371 35.9615 230.388 36.2563L230.835 42.9013V60.5049C230.835 60.8106 231.081 61.0564 231.387 61.0564H236.76C237.065 61.0564 237.311 60.8106 237.311 60.5049V48.4052C237.311 46.2812 237.884 44.5558 239.036 43.2235C240.189 41.8912 241.805 41.2251 243.891 41.2251C244.338 41.2251 244.77 41.2469 245.174 41.296C245.501 41.3343 245.78 41.0776 245.78 40.75V35.4919C245.78 35.4919 245.758 35.1261 245.294 35.1261L245.299 35.137Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M223.481 54.0236C222.907 54.3458 222.296 54.6188 221.646 54.8317C220.996 55.0501 220.281 55.2194 219.489 55.345C218.698 55.4706 217.819 55.5361 216.847 55.5361C214.362 55.5361 212.424 54.9682 211.021 53.838C209.617 52.7023 208.809 51.2226 208.591 49.388C209.132 50.0377 209.978 50.5401 211.124 50.9004C212.277 51.2608 213.609 51.441 215.116 51.441C218.496 51.4082 221.111 50.6656 222.968 49.2296C224.819 47.7936 225.747 45.757 225.747 43.1306C225.747 40.8264 224.911 38.9209 223.241 37.4138C221.57 35.9014 219.113 35.1479 215.875 35.1479C213.898 35.1479 212.064 35.4919 210.371 36.1744C208.678 36.8569 207.215 37.8015 205.976 39.0082C204.736 40.2149 203.77 41.6345 203.087 43.2726C202.405 44.9106 202.061 46.6961 202.061 48.6399C202.061 50.5837 202.394 52.2709 203.06 53.8216C203.726 55.3668 204.654 56.6827 205.839 57.7584C207.024 58.8395 208.449 59.664 210.103 60.2427C211.758 60.8161 213.576 61.1054 215.553 61.1054C217.709 61.1054 219.544 60.8816 221.056 60.4284C222.422 60.0243 223.519 59.5657 224.354 59.0579C224.518 58.9596 224.606 58.7849 224.606 58.5938V54.3294C224.606 53.909 224.147 53.6414 223.781 53.8598C223.683 53.9199 223.579 53.9799 223.481 54.0345V54.0236ZM211.07 42.2898C212.435 41.1213 214.035 40.5371 215.869 40.5371C216.95 40.5371 217.813 40.7773 218.457 41.2633C219.107 41.7492 219.429 42.4426 219.429 43.3381C219.429 43.9879 219.238 44.5612 218.862 45.0635C218.485 45.5659 217.873 45.9808 217.027 46.303C216.181 46.6251 215.067 46.8599 213.68 47.0019C212.293 47.1438 210.595 47.1821 208.58 47.1111C208.869 45.0635 209.694 43.4528 211.064 42.2843L211.07 42.2898Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M355.655 48.225C354.393 46.9855 352.253 46.0409 349.234 45.3912C348.049 45.14 347.077 44.9161 346.318 44.7141C345.564 44.5175 344.975 44.3101 344.565 44.0917C344.15 43.8732 343.866 43.6494 343.702 43.4146C343.539 43.1798 343.462 42.9013 343.462 42.5792C343.462 41.105 344.975 40.3678 347.994 40.3678C349.359 40.3678 350.719 40.5316 352.067 40.8538C352.974 41.0722 353.858 41.3834 354.721 41.782C355.081 41.9458 355.491 41.6783 355.491 41.2797V36.846C355.491 36.6222 355.36 36.4201 355.158 36.3328C354.202 35.9396 353.165 35.6448 352.04 35.4537C350.779 35.2353 349.43 35.1315 347.994 35.1315C346.378 35.1315 344.909 35.3117 343.599 35.6721C342.283 36.0325 341.153 36.5457 340.197 37.2119C339.242 37.878 338.515 38.6861 338.013 39.6416C337.511 40.5972 337.26 41.6455 337.26 42.7976C337.26 44.8506 337.909 46.4777 339.203 47.679C340.497 48.8857 342.55 49.7757 345.351 50.349C346.575 50.5674 347.574 50.7803 348.344 50.9987C349.119 51.2171 349.731 51.4465 350.178 51.6976C350.626 51.9488 350.932 52.2218 351.096 52.5057C351.259 52.7951 351.336 53.1337 351.336 53.5323C351.336 55.0775 349.698 55.8528 346.427 55.8528C344.811 55.8528 343.178 55.6017 341.546 55.0993C340.465 54.7662 339.433 54.3076 338.455 53.7343C338.089 53.5213 337.636 53.7834 337.636 54.2093V58.6976C337.636 58.905 337.751 59.1016 337.937 59.1944C339.209 59.8333 340.53 60.3028 341.895 60.6031C343.369 60.9253 345.007 61.0891 346.804 61.0891C350.042 61.0891 352.641 60.3957 354.601 59.0142C356.561 57.6274 357.544 55.7327 357.544 53.3193C357.544 51.1625 356.916 49.459 355.655 48.2195V48.225Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M196.158 54.941C195.405 55.2304 194.433 55.3723 193.242 55.3723C192.052 55.3723 190.987 55.0229 190.141 54.3185C189.295 53.6196 188.874 52.402 188.874 50.6766V41.1814H196.961C197.261 41.1814 197.507 40.9357 197.507 40.6354V36.2236C197.507 35.9232 197.261 35.6775 196.961 35.6775H188.874V28.421C188.874 28.0934 188.585 27.8368 188.257 27.875L182.934 28.5466C182.661 28.5793 182.453 28.8141 182.453 29.0871L182.399 51.7522C182.399 54.8809 183.245 57.2179 184.932 58.7685C186.625 60.3138 188.869 61.0891 191.675 61.0891C193.04 61.0891 194.203 60.9635 195.154 60.7124C196.104 60.4612 196.89 60.1554 197.501 59.7951V59.7732V55.1921C197.501 54.7935 197.092 54.5315 196.732 54.6953C196.551 54.7772 196.36 54.8591 196.153 54.941H196.158Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M176.212 54.941C175.459 55.2304 174.487 55.3723 173.297 55.3723C172.106 55.3723 171.041 55.0229 170.195 54.3185C169.349 53.6196 168.928 52.402 168.928 50.6766V41.1814H176.993C177.293 41.1814 177.539 40.9357 177.539 40.6354V36.229C177.539 35.9287 177.293 35.683 176.993 35.683H168.928V28.4264C168.928 28.0988 168.639 27.8422 168.311 27.8804L162.988 28.552C162.715 28.5848 162.507 28.8196 162.507 29.0926L162.453 51.7577C162.453 54.8864 163.299 57.2233 164.986 58.774C166.679 60.3192 168.923 61.0946 171.729 61.0946C173.094 61.0946 174.257 60.969 175.208 60.7178C176.005 60.5103 176.682 60.2592 177.239 59.9752C177.419 59.8824 177.523 59.6913 177.523 59.4893V55.214C177.523 54.8154 177.113 54.5478 176.747 54.7171C176.578 54.7935 176.392 54.87 176.201 54.9464L176.212 54.941Z",
    fill: "white"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M155.819 54.0236C155.245 54.3458 154.634 54.6188 153.984 54.8317C153.334 55.0501 152.619 55.2194 151.827 55.345C151.035 55.4706 150.156 55.5361 149.184 55.5361C146.7 55.5361 144.762 54.9682 143.358 53.838C141.955 52.7023 141.147 51.2226 140.929 49.388C141.469 50.0377 142.316 50.5401 143.462 50.9004C144.614 51.2608 145.947 51.441 147.454 51.441C150.833 51.4082 153.449 50.6656 155.305 49.2296C157.156 47.7936 158.085 45.757 158.085 43.1306C158.085 40.8264 157.249 38.9209 155.578 37.4138C153.908 35.9014 151.45 35.1479 148.213 35.1479C146.236 35.1479 144.401 35.4919 142.709 36.1744C141.016 36.8569 139.553 37.8015 138.313 39.0082C137.074 40.2149 136.107 41.6345 135.425 43.2726C134.742 44.9106 134.398 46.6961 134.398 48.6399C134.398 50.5837 134.731 52.2709 135.398 53.8216C136.064 55.3668 136.992 56.6827 138.177 57.7584C139.362 58.8395 140.787 59.664 142.441 60.2427C144.096 60.8161 145.914 61.1054 147.89 61.1054C150.047 61.1054 151.882 60.8816 153.394 60.4284C154.765 60.0189 155.868 59.5602 156.703 59.0524C156.861 58.9541 156.954 58.7794 156.954 58.5883V54.3185C156.954 53.898 156.496 53.6305 156.135 53.8489C156.032 53.9144 155.928 53.9745 155.824 54.0345L155.819 54.0236ZM143.408 42.2898C144.773 41.1213 146.372 40.5371 148.207 40.5371C149.288 40.5371 150.151 40.7773 150.795 41.2633C151.445 41.7492 151.767 42.4426 151.767 43.3381C151.767 43.9879 151.576 44.5612 151.199 45.0635C150.823 45.5659 150.211 45.9808 149.365 46.303C148.518 46.6251 147.404 46.8599 146.018 47.0019C144.631 47.1438 142.933 47.1821 140.918 47.1111C141.207 45.0635 142.032 43.4528 143.402 42.2843L143.408 42.2898Z",
    fill: "white"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BdocsIcon);

/***/ }),

/***/ "./components/icons/ChatbotActiveIcon.js":
/*!***********************************************!*\
  !*** ./components/icons/ChatbotActiveIcon.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const ChatbotActiveIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    width: 22,
    height: 24,
    viewBox: "0 0 22 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("mask", {
    id: "a",
    fill: "#fff"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",
    fill: "#00B682"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",
    mask: "url(#a)"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M11 7V1",
    stroke: "#00B682",
    strokeWidth: "1.5"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",
    fill: "#00B682"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", {
    cx: 11,
    cy: "1.5",
    r: "1.5",
    fill: "#00B682"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatbotActiveIcon);

/***/ }),

/***/ "./components/icons/ChatbotTabIcon.js":
/*!********************************************!*\
  !*** ./components/icons/ChatbotTabIcon.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const ChatbotTabIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    width: 21,
    height: 24,
    viewBox: "0 0 21 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",
    stroke: "#344054",
    strokeWidth: "1.5"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M6.2 14c2.3 2 5.3 2 7.8 0",
    stroke: "#344054",
    strokeWidth: "1.6"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",
    stroke: "#344054",
    strokeWidth: "1.5"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    clipRule: "evenodd",
    d: "M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",
    fill: "#344054"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", {
    cx: "10.1",
    cy: "1.5",
    r: "1.5",
    fill: "#344054"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatbotTabIcon);

/***/ }),

/***/ "./components/icons/Expand.js":
/*!************************************!*\
  !*** ./components/icons/Expand.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const Expand = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#16CA9E",
    d: "M23.995.901c-.001-.015-.004-.029-.006-.043L23.98.805l-.013-.05-.011-.045-.017-.048-.016-.045-.02-.043-.022-.045-.024-.04-.026-.044L23.8.402l-.027-.036a.993.993 0 0 0-.065-.073h-.001a.999.999 0 0 0-.073-.066L23.598.2l-.043-.032c-.014-.01-.029-.017-.043-.026l-.04-.024-.046-.022-.043-.02-.045-.016-.048-.017-.045-.012-.05-.012-.053-.008L23.1.005A1.022 1.022 0 0 0 23 0h-7a1 1 0 1 0 0 2h4.586l-7.293 7.293a1 1 0 0 0 1.414 1.414L22 3.414V8a1 1 0 1 0 2 0V1c0-.033-.002-.066-.005-.099ZM9.293 13.293 2 20.586V16a1 1 0 1 0-2 0v7c0 .033.002.066.005.099l.006.043.008.053.013.05.011.045.017.048.016.045.02.043.022.045.024.04.026.044.032.043.027.036c.02.025.042.05.065.072v.001h.001c.024.024.048.046.073.066l.036.027c.014.01.028.022.043.031.014.01.029.018.043.027l.04.024.046.022.043.02.045.016.048.017.045.011c.017.005.033.01.05.013l.053.008.043.006C.934 23.998.967 24 1 24h7a1 1 0 1 0 0-2H3.414l7.293-7.293a1 1 0 1 0-1.414-1.414Z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Expand);

/***/ }),

/***/ "./components/icons/FileDelete.js":
/*!****************************************!*\
  !*** ./components/icons/FileDelete.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const FileDelete = ({
  className,
  onClick
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    className: className,
    xmlns: "http://www.w3.org/2000/svg",
    width: 20,
    height: 25,
    fill: "none",
    onClick: onClick
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    stroke: "#667085",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 1.667,
    d: "M2.5 5h1.667m0 0H17.5M4.167 5v11.667a1.667 1.667 0 0 0 1.666 1.666h8.334a1.667 1.667 0 0 0 1.666-1.666V5H4.167Zm2.5 0V3.333a1.667 1.667 0 0 1 1.666-1.666h3.334a1.667 1.667 0 0 1 1.666 1.666V5m-5 4.167v5m3.334-5v5"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FileDelete);

/***/ }),

/***/ "./components/icons/FileIcon.js":
/*!**************************************!*\
  !*** ./components/icons/FileIcon.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const FileIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 16,
    height: 25,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    stroke: "#01BAB4",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 1.333,
    d: "M8.666 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V6M8.666 1.333 13.333 6M8.666 1.333V6h4.667"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FileIcon);

/***/ }),

/***/ "./components/icons/Happy.js":
/*!***********************************!*\
  !*** ./components/icons/Happy.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const Happy = ({
  clickHandler
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "ia-reaction ia-happy",
    onClick: clickHandler
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    xmlSpace: "preserve",
    className: "betterdocs-emo betterdocs-happy-icon",
    viewBox: "0 0 20 20",
    "data-reaction": "happy"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm3.3 9.9c-2.6 0-5-1.6-5.9-4.1l1.2-.5c.7 1.9 2.6 3.2 4.6 3.2s3.9-1.3 4.6-3.2l1.2.5c-.7 2.4-3.1 4.1-5.7 4.1z"
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Happy);

/***/ }),

/***/ "./components/icons/HomeIcon.js":
/*!**************************************!*\
  !*** ./components/icons/HomeIcon.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const HomeIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#000",
    d: "m23.4 10.392-.002-.002L13.608.6a2.194 2.194 0 0 0-1.562-.647c-.59 0-1.145.23-1.563.647L.698 10.385a2.212 2.212 0 0 0-.006 3.13 2.197 2.197 0 0 0 1.535.648h.39v7.204a2.589 2.589 0 0 0 2.586 2.586h3.83a.703.703 0 0 0 .703-.703v-5.648c0-.651.53-1.18 1.18-1.18h2.26c.65 0 1.18.529 1.18 1.18v5.648c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.586-2.586v-7.204h.362c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261.001-3.123Zm-.996 2.13a.798.798 0 0 1-.568.235h-1.065a.703.703 0 0 0-.703.703v7.907c0 .65-.529 1.18-1.18 1.18h-3.127v-4.945a2.589 2.589 0 0 0-2.586-2.586h-2.259a2.59 2.59 0 0 0-2.586 2.586v4.945H5.203c-.65 0-1.18-.53-1.18-1.18V13.46a.703.703 0 0 0-.703-.703H2.273a.797.797 0 0 1-.586-.236.804.804 0 0 1 0-1.136h.001l9.79-9.79a.797.797 0 0 1 .568-.236c.214 0 .416.084.568.236l9.787 9.787a.805.805 0 0 1 .003 1.14Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeIcon);

/***/ }),

/***/ "./components/icons/HomeIconActive.js":
/*!********************************************!*\
  !*** ./components/icons/HomeIconActive.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const HomeIconActive = ({
  url,
  active
}) => {
  if (url) {
    return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: TAB_MESSAGE_ICON?.url,
      width: "24",
      height: "24"
    });
  }
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#16CA9E",
    d: "m23.353 10.439-.002-.002-9.79-9.79A2.194 2.194 0 0 0 12 0c-.59 0-1.145.23-1.563.647L.652 10.432l-.01.01a2.212 2.212 0 0 0 .004 3.12 2.197 2.197 0 0 0 1.534.648h.39v7.204A2.589 2.589 0 0 0 5.156 24h3.83a.703.703 0 0 0 .704-.703v-5.649c0-.65.529-1.18 1.18-1.18h2.259c.65 0 1.18.53 1.18 1.18v5.649c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.587-2.586V14.21h.361c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261 0-3.123Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomeIconActive);

/***/ }),

/***/ "./components/icons/LauncherIcon.js":
/*!******************************************!*\
  !*** ./components/icons/LauncherIcon.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const LauncherIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    className: "betterdocs-launch-icon",
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("mask", {
    id: "a",
    width: 24,
    height: 24,
    x: 0,
    y: 0,
    maskUnits: "userSpaceOnUse",
    style: {
      maskType: "luminance"
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M24 0H0v24h24V0Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    mask: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("mask", {
    id: "b",
    width: 24,
    height: 24,
    x: 0,
    y: 0,
    maskUnits: "userSpaceOnUse",
    style: {
      maskType: "luminance"
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0V0Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    mask: "url(#b)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    stroke: "#fff",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeMiterlimit: 10,
    strokeWidth: 1.5,
    d: "M12 .938C5.89.938.937 5.89.937 12c0 2.15.615 4.158 1.677 5.856L.938 23.062l5.206-1.676A11.01 11.01 0 0 0 12 23.063c6.11 0 11.063-4.953 11.063-11.063S18.11.938 12 .938Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LauncherIcon);

/***/ }),

/***/ "./components/icons/LauncherIconClose.js":
/*!***********************************************!*\
  !*** ./components/icons/LauncherIconClose.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const LauncherIconClose = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 15,
    height: 15,
    className: "betterdocs-launch-icon betterdocs-launch-icon-close"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M14.793.207a.758.758 0 0 0-.227-.156.774.774 0 0 0-.27-.059.774.774 0 0 0-.269.059.758.758 0 0 0-.226.156L7.5 6.504 1.2.207A.682.682 0 0 0 .702 0a.682.682 0 0 0-.496.207A.682.682 0 0 0 0 .703c0 .188.07.363.207.496L6.504 7.5.207 13.8a.682.682 0 0 0-.207.497c0 .187.07.363.207.496A.682.682 0 0 0 .703 15c.188 0 .363-.07.496-.207L7.5 8.496l6.3 6.297a.682.682 0 0 0 .497.207c.187 0 .363-.07.496-.207a.682.682 0 0 0 .207-.496.682.682 0 0 0-.207-.496L8.496 7.5l6.297-6.3a.758.758 0 0 0 .156-.227.769.769 0 0 0 .051-.27.769.769 0 0 0-.05-.27.758.758 0 0 0-.157-.226Zm0 0",
    style: {
      stroke: "none",
      fillRule: "nonzero",
      fill: "#fff",
      fillOpacity: 1
    }
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LauncherIconClose);

/***/ }),

/***/ "./components/icons/ListArrow.js":
/*!***************************************!*\
  !*** ./components/icons/ListArrow.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const ListArrow = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 8,
    height: 16,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#00B682",
    d: "M.94.97a.81.81 0 0 0-.514.173.991.991 0 0 0-.332.479 1.126 1.126 0 0 0-.025.608c.048.2.15.38.291.512l5.401 5.242-5.401 5.24a.97.97 0 0 0-.24.304 1.127 1.127 0 0 0-.08.795c.034.132.094.254.173.358.079.105.176.19.286.25a.79.79 0 0 0 .706.03.882.882 0 0 0 .301-.223L7.69 8.744a.997.997 0 0 0 .229-.342 1.113 1.113 0 0 0 0-.838.998.998 0 0 0-.229-.342l-6.184-6A.828.828 0 0 0 .941.97Z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ListArrow);

/***/ }),

/***/ "./components/icons/LogoFooter.js":
/*!****************************************!*\
  !*** ./components/icons/LogoFooter.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const LogoFooter = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 67,
    height: 14,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#00B682",
    d: "M10.871 5.14H7.293a.634.634 0 1 1 0-1.268h3.578a.634.634 0 1 1 0 1.268ZM9.429 7.611H5.853a.634.634 0 1 1 0-1.268h3.576a.634.634 0 1 1 0 1.268ZM7.97 10.125H4.392a.634.634 0 1 1 0-1.268H7.97a.634.634 0 1 1 0 1.268Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#00B682",
    d: "M13.511 6.577a.685.685 0 0 0-.083-.13l.955-1.645c.52-.894.523-1.964.007-2.861A2.83 2.83 0 0 0 11.91.506H7.59c-1.012 0-1.96.546-2.473 1.424L.898 9.198a2.829 2.829 0 0 0-.007 2.861 2.83 2.83 0 0 0 2.48 1.435h7.402A3.79 3.79 0 0 0 14 11.711a3.79 3.79 0 0 0 .209-3.68l-.698-1.454Zm-9.049 5.576H3.37c-.55 0-1.042-.285-1.317-.762a1.5 1.5 0 0 1 .004-1.518l4.221-7.268a1.526 1.526 0 0 1 1.314-.758h4.32c.55 0 1.042.285 1.316.762a1.5 1.5 0 0 1-.004 1.518l-4.223 7.27c-.27.465-.77.755-1.31.755H4.463Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#16342F",
    d: "M22.413 6.935c.27-.136.476-.325.615-.569.139-.243.208-.518.208-.822 0-.237-.042-.457-.127-.66a1.361 1.361 0 0 0-.396-.528 1.967 1.967 0 0 0-.68-.35 3.358 3.358 0 0 0-.98-.128h-2.396a.104.104 0 0 0-.103.104v6.203c0 .048.033.09.079.1.258.059.548.107.87.144.356.041.733.06 1.133.06.508 0 .942-.044 1.305-.136.362-.091.66-.222.89-.39.23-.17.4-.378.507-.625.108-.247.162-.523.162-.828 0-.386-.094-.712-.284-.98a1.667 1.667 0 0 0-.803-.594v-.001Zm-2.61-1.926c0-.058.047-.105.104-.105h.952c.393-.006.678.065.858.214.18.149.27.352.27.61 0 .27-.09.481-.27.634-.18.152-.462.228-.848.228h-.96a.105.105 0 0 1-.105-.104V5.01l-.002-.001ZM21.89 9.24c-.24.162-.628.244-1.163.244-.196 0-.365-.005-.507-.016a2.392 2.392 0 0 1-.335-.047.104.104 0 0 1-.082-.102V7.668c0-.058.047-.104.104-.104h1.054c.44 0 .765.084.975.254.21.17.315.402.315.7 0 .32-.12.559-.361.722ZM50.56 4.878a3.204 3.204 0 0 0-1.117-.66 4.331 4.331 0 0 0-1.457-.233h-1.812a.103.103 0 0 0-.103.103v6.304c0 .057.047.103.103.103h1.812c.535 0 1.02-.078 1.458-.233a3.168 3.168 0 0 0 1.117-.665c.308-.288.547-.631.716-1.031.17-.4.254-.843.254-1.33 0-.487-.084-.931-.254-1.33-.17-.4-.408-.741-.716-1.026v-.002Zm-.446 3.26a1.87 1.87 0 0 1-.458.68c-.2.19-.445.338-.736.443-.29.104-.626.157-1.005.157h-.49a.103.103 0 0 1-.104-.104V5.166c0-.058.046-.104.104-.104h.49c.38 0 .715.053 1.005.157.291.105.537.254.736.447.2.194.352.425.458.696.104.272.157.569.157.894 0 .326-.053.62-.157.884v-.002ZM56.561 6.294a2.439 2.439 0 0 0-.796-.502 2.668 2.668 0 0 0-.98-.178c-.352 0-.67.06-.976.183a2.407 2.407 0 0 0-1.334 1.29c-.133.298-.2.62-.2.965 0 .366.065.698.195.995.13.298.308.555.537.772.23.217.497.384.802.502.305.119.63.178.975.178s.67-.06.971-.183c.303-.122.568-.29.797-.508a2.406 2.406 0 0 0 .742-1.758c0-.352-.065-.687-.194-.985a2.324 2.324 0 0 0-.538-.772v.001Zm-.588 2.285a1.287 1.287 0 0 1-.269.427 1.243 1.243 0 0 1-.92.386 1.233 1.233 0 0 1-.92-.386 1.303 1.303 0 0 1-.271-.427 1.453 1.453 0 0 1-.095-.528c0-.19.032-.366.095-.528.063-.163.153-.305.27-.427a1.233 1.233 0 0 1 .92-.386 1.233 1.233 0 0 1 .92.386c.117.122.207.264.27.427.063.162.095.338.095.528s-.032.366-.095.528ZM61.419 9.3a2.42 2.42 0 0 1-.717.092c-.21 0-.402-.034-.578-.102a1.41 1.41 0 0 1-.458-.28 1.247 1.247 0 0 1-.3-.426 1.34 1.34 0 0 1-.107-.533c0-.19.036-.377.107-.544.071-.165.17-.308.3-.426a1.41 1.41 0 0 1 .463-.28c.179-.068.377-.102.594-.102.264 0 .492.032.685.097.07.024.135.048.196.075a.103.103 0 0 0 .145-.094v-.899a.102.102 0 0 0-.067-.096 2.773 2.773 0 0 0-.455-.118 3.744 3.744 0 0 0-.645-.05c-.358 0-.694.063-1.005.188-.311.125-.58.298-.808.518-.227.22-.406.477-.538.772a2.329 2.329 0 0 0-.199.96 2.413 2.413 0 0 0 .712 1.732c.223.22.49.394.802.523.312.128.657.193 1.036.193.27 0 .496-.021.675-.066.15-.037.29-.077.423-.12a.102.102 0 0 0 .07-.098V9.33a.103.103 0 0 0-.142-.094 2.348 2.348 0 0 1-.188.067l-.001-.001ZM45.173 5.614h-.172c-.366 0-.675.086-.924.259-.251.173-.427.388-.529.644l-.062-.79a.103.103 0 0 0-.112-.094l-.914.083a.103.103 0 0 0-.094.109l.085 1.25v3.314c0 .058.046.104.103.104h1.012a.104.104 0 0 0 .104-.104V8.112c0-.4.108-.725.324-.976.217-.25.522-.376.914-.376.084 0 .166.004.242.013a.102.102 0 0 0 .114-.102v-.99s-.004-.069-.092-.069l.001.002ZM41.066 9.17c-.108.06-.223.111-.345.151a2.505 2.505 0 0 1-.406.097 3.183 3.183 0 0 1-.498.036c-.467 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.262.216.477.284.217.068.468.102.752.102.636-.006 1.128-.146 1.478-.416.348-.27.523-.654.523-1.148 0-.434-.157-.793-.472-1.076-.315-.285-.777-.427-1.386-.427-.373 0-.718.065-1.036.193a2.525 2.525 0 0 0-.828.534 2.393 2.393 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.126.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.654.162 1.026.162.406 0 .751-.042 1.036-.127.257-.076.463-.163.62-.258a.1.1 0 0 0 .048-.088v-.802c0-.08-.086-.13-.155-.089a1.6 1.6 0 0 1-.057.033V9.17ZM38.73 6.96c.257-.22.558-.33.904-.33.203 0 .365.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.346.233c-.159.06-.369.105-.63.131a7.16 7.16 0 0 1-.96.021c.055-.385.21-.689.468-.909l.001.001ZM65.946 8.078c-.237-.234-.64-.411-1.209-.534-.223-.047-.406-.09-.548-.127a1.58 1.58 0 0 1-.33-.117.448.448 0 0 1-.163-.128.267.267 0 0 1-.045-.157c0-.278.285-.416.853-.416.257 0 .513.03.767.091.17.041.337.1.5.175.067.03.144-.02.144-.095v-.834a.105.105 0 0 0-.063-.097 2.745 2.745 0 0 0-.586-.165 4.468 4.468 0 0 0-.762-.061c-.304 0-.58.034-.827.102a2.06 2.06 0 0 0-.64.29 1.28 1.28 0 0 0-.412.457c-.094.18-.142.377-.142.594 0 .386.123.693.366.919.244.227.63.395 1.158.502.23.042.418.082.563.123.146.04.26.084.345.131a.443.443 0 0 1 .173.152c.03.055.045.119.045.194 0 .29-.308.436-.924.436-.304 0-.612-.047-.919-.141a2.746 2.746 0 0 1-.581-.257.102.102 0 0 0-.155.09v.844c0 .039.022.076.057.094.24.12.488.208.745.265.277.06.586.091.924.091.61 0 1.099-.13 1.468-.39.369-.261.554-.618.554-1.072 0-.406-.119-.727-.356-.96ZM35.923 9.342a1.554 1.554 0 0 1-.549.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.522a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.103h-1.522V4.35a.104.104 0 0 0-.116-.103l-1.002.126a.103.103 0 0 0-.09.102l-.011 4.267c0 .589.16 1.028.477 1.32.319.291.74.437 1.27.437a2.6 2.6 0 0 0 .654-.07c.179-.048.327-.106.442-.174V9.39a.102.102 0 0 0-.145-.093l-.109.046h.001ZM32.17 9.342a1.553 1.553 0 0 1-.55.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.518a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.102h-1.518V4.35a.104.104 0 0 0-.116-.103l-1.002.127a.103.103 0 0 0-.09.101l-.01 4.267c0 .589.158 1.028.476 1.32.319.291.741.437 1.27.437a2.6 2.6 0 0 0 .654-.07 1.9 1.9 0 0 0 .382-.14.102.102 0 0 0 .054-.092v-.805a.103.103 0 0 0-.146-.093 2.609 2.609 0 0 1-.103.043l.002-.001ZM28.33 9.17a1.93 1.93 0 0 1-.345.151 2.504 2.504 0 0 1-.406.097c-.15.024-.315.036-.498.036-.468 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.261.216.477.284.217.068.468.102.751.102.637-.006 1.129-.146 1.478-.416s.523-.654.523-1.148c0-.434-.157-.793-.471-1.076-.315-.285-.777-.427-1.387-.427-.372 0-.717.065-1.036.193a2.524 2.524 0 0 0-.827.534 2.391 2.391 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.125.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.653.162 1.025.162.407 0 .752-.042 1.037-.127.258-.077.465-.164.622-.26a.102.102 0 0 0 .048-.087v-.803c0-.08-.087-.13-.154-.089-.02.013-.04.024-.06.035V9.17Zm-2.336-2.21c.257-.22.558-.33.903-.33.204 0 .366.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.345.233c-.16.06-.369.105-.63.131-.261.027-.58.034-.96.021.055-.385.21-.689.468-.909v.001Z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LogoFooter);

/***/ }),

/***/ "./components/icons/Magnifier.js":
/*!***************************************!*\
  !*** ./components/icons/Magnifier.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);

const Magnifier = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: "16",
    height: "16",
    viewBox: "0 0 16 16"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M7.29289 1.2876C3.98708 1.2876 1.29419 3.98048 1.29419 7.28631C1.29419 10.5921 3.98708 13.2902 7.29289 13.2902C8.7049 13.2902 10.0035 12.7954 11.0299 11.9738L13.5286 14.4712C13.6547 14.5921 13.8231 14.6588 13.9977 14.657C14.1724 14.6552 14.3394 14.5851 14.463 14.4617C14.5866 14.3382 14.657 14.1713 14.659 13.9967C14.661 13.822 14.5946 13.6535 14.4739 13.5272L11.9752 11.0285C12.7975 10.0006 13.2929 8.69995 13.2929 7.28631C13.2929 3.98048 10.5987 1.2876 7.29289 1.2876ZM7.29289 2.62095C9.87811 2.62095 11.9583 4.70108 11.9583 7.28631C11.9583 9.87153 9.87811 11.9569 7.29289 11.9569C4.70766 11.9569 2.62752 9.87153 2.62752 7.28631C2.62752 4.70108 4.70766 2.62095 7.29289 2.62095Z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Magnifier);

/***/ }),

/***/ "./components/icons/Neutral.js":
/*!*************************************!*\
  !*** ./components/icons/Neutral.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const Neutral = ({
  clickHandler
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "ia-reaction ia-neutral",
    onClick: clickHandler
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    xmlSpace: "preserve",
    className: "betterdocs-emo betterdocs-neutral-icon",
    viewBox: "0 0 20 20",
    "data-reaction": "normal"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zM6.6 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.8.1-1.5-.6-1.5-1.4 0-.9.7-1.6 1.5-1.6zm7.7 8H5.7c-.3 0-.6-.3-.6-.6s.3-.6.6-.6h8.5c.3 0 .6.3.6.6.1.3-.2.6-.5.6zm-1-4.9c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5c.1.8-.6 1.5-1.5 1.5z"
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Neutral);

/***/ }),

/***/ "./components/icons/ResourcesActiveIcon.js":
/*!*************************************************!*\
  !*** ./components/icons/ResourcesActiveIcon.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const ResourcesActiveIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    fill: "#16CA9E",
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M24 16.242a7.782 7.782 0 0 0-4.268-6.929c-.079 5.71-4.709 10.34-10.419 10.42A7.782 7.782 0 0 0 16.243 24a7.73 7.73 0 0 0 3.947-1.078l3.776 1.044-1.045-3.776A7.73 7.73 0 0 0 24 16.242Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M18.328 9.164C18.328 4.111 14.218 0 9.164 0 4.111 0 0 4.11 0 9.164c0 1.647.438 3.25 1.27 4.658L.035 18.294l4.472-1.237a9.135 9.135 0 0 0 4.658 1.271c5.053 0 9.164-4.11 9.164-9.164ZM7.758 7.031H6.352A2.816 2.816 0 0 1 9.164 4.22a2.816 2.816 0 0 1 2.813 2.812 2.82 2.82 0 0 1-.915 2.076L9.867 10.2v1.097H8.461V9.58l1.652-1.512a1.408 1.408 0 0 0-.948-2.444c-.776 0-1.407.63-1.407 1.406Zm.703 5.672h1.406v1.406H8.461v-1.406Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResourcesActiveIcon);

/***/ }),

/***/ "./components/icons/ResourcesIcon.js":
/*!*******************************************!*\
  !*** ./components/icons/ResourcesIcon.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const ResourcesIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("mask", {
    id: "b",
    width: 24,
    height: 24,
    x: 0,
    y: 0,
    maskUnits: "userSpaceOnUse",
    style: {
      maskType: "luminance"
    }
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0V0Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    fill: "#202223",
    mask: "url(#b)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    d: "M16.242 22.547a6.307 6.307 0 0 1-6.183-5.07l-1.472.292a7.807 7.807 0 0 0 11.61 5.203l3.837 1.061-1.062-3.837A7.807 7.807 0 0 0 17.77 8.587l-.292 1.472a6.307 6.307 0 0 1 4.056 9.613l-.184.283.533 1.927-1.927-.533-.283.184a6.271 6.271 0 0 1-3.43 1.014Z",
    clipRule: "evenodd"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    d: "M-.047 9.164A9.211 9.211 0 1 1 4.5 17.108L-.033 18.36 1.22 13.83A9.172 9.172 0 0 1-.047 9.164Zm9.211-7.71A7.711 7.711 0 0 0 2.662 13.31l.18.281-.724 2.618 2.618-.724.281.18A7.71 7.71 0 1 0 9.164 1.453Z",
    clipRule: "evenodd"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M9.867 14.11H8.461v-1.407h1.406v1.406Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fillRule: "evenodd",
    d: "M9.914 10.22v1.077h-1.5V9.56l1.667-1.525a1.36 1.36 0 1 0-2.276-1.003h-1.5a2.86 2.86 0 1 1 4.789 2.11l-1.18 1.079Z",
    clipRule: "evenodd"
  }))), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResourcesIcon);

/***/ }),

/***/ "./components/icons/Sad.js":
/*!*********************************!*\
  !*** ./components/icons/Sad.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const Sad = ({
  clickHandler
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "ia-reaction ia-sad",
    onClick: clickHandler
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    xmlSpace: "preserve",
    className: "betterdocs-emo betterdocs-sad-icon",
    viewBox: "0 0 20 20",
    "data-reaction": "sad"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm7.5 8.6c-.2 0-.4-.1-.5-.2-.9-1.1-2.2-1.8-3.7-1.8s-2.8.7-3.7 1.7c-.2.2-.3.3-.5.3-.6 0-.9-.7-.5-1.1 1.2-1.3 2.9-2.2 4.7-2.2 1.8 0 3.6.8 4.7 2.2.4.4.1 1.1-.5 1.1z",
    className: "st-path"
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sad);

/***/ }),

/***/ "./components/icons/SearchIconClose.js":
/*!*********************************************!*\
  !*** ./components/icons/SearchIconClose.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const SearchIconClose = ({
  onClick
}) => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: "1em",
    height: "1em",
    fill: "currentColor",
    stroke: "currentColor",
    strokeWidth: 0,
    viewBox: "0 0 24 24",
    onClick: onClick
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    stroke: "none",
    d: "M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SearchIconClose);

/***/ }),

/***/ "./components/icons/SendMessageIcon.js":
/*!*********************************************!*\
  !*** ./components/icons/SendMessageIcon.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const SendMessageIcon = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    fill: "#000",
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M2.768 18.719a4.668 4.668 0 0 1-2.296-.603.723.723 0 0 1-.288-1.013c.725-1.16.987-2.576.655-3.908C.495 11.815-.003 10.651 0 9.19.013 4.06 4.282-.098 9.406.002c4.949.1 9.027 4.26 9.027 9.21 0 6.465-6.775 11.004-12.757 8.508-.824.647-1.86.999-2.908.999Zm-.975-1.579c1.127.35 2.394.07 3.263-.77a.713.713 0 0 1 .803-.13c5.121 2.449 11.149-1.39 11.149-7.028 0-4.208-3.424-7.7-7.631-7.785-4.336-.086-7.94 3.426-7.951 7.766-.003 1.388.538 2.498.834 3.814a6.362 6.362 0 0 1-.467 4.133Z"
  }), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    d: "M21.232 24a4.734 4.734 0 0 1-2.908-1c-3.181 1.328-6.965.724-9.573-1.529a.713.713 0 0 1 .931-1.079c2.314 1.998 5.702 2.447 8.459 1.13a.713.713 0 0 1 .803.13 3.305 3.305 0 0 0 3.263.77 6.335 6.335 0 0 1-.248-4.892c.41-.968.618-1.996.615-3.056-.004-1.87-.626-3.599-1.798-5.001a.713.713 0 1 1 1.094-.914A9.26 9.26 0 0 1 24 14.47a9.15 9.15 0 0 1-.719 3.594c-.503 1.459-.272 3.02.535 4.32a.723.723 0 0 1-.288 1.013 4.67 4.67 0 0 1-2.296.603ZM9.217 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM5.061 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM13.373 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255Z"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h24v24H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SendMessageIcon);

/***/ }),

/***/ "./components/icons/TabIcon.js":
/*!*************************************!*\
  !*** ./components/icons/TabIcon.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../BDSettings/Constants */ "./components/BDSettings/Constants.js");
/* harmony import */ var _HomeIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HomeIcon */ "./components/icons/HomeIcon.js");
/* harmony import */ var _HomeIconActive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HomeIconActive */ "./components/icons/HomeIconActive.js");
/* harmony import */ var _SendMessageIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SendMessageIcon */ "./components/icons/SendMessageIcon.js");
/* harmony import */ var _ResourcesIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ResourcesIcon */ "./components/icons/ResourcesIcon.js");
/* harmony import */ var _ResourcesActiveIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ResourcesActiveIcon */ "./components/icons/ResourcesActiveIcon.js");
/* harmony import */ var _ChatbotTabIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ChatbotTabIcon */ "./components/icons/ChatbotTabIcon.js");
/* harmony import */ var _ChatbotActiveIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ChatbotActiveIcon */ "./components/icons/ChatbotActiveIcon.js");










const TabIcon = ({
  icon,
  active
}) => {
  if (_BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__.TAB_ICONS?.[icon] != undefined) {
    return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
      src: _BDSettings_Constants__WEBPACK_IMPORTED_MODULE_2__.TAB_ICONS[icon],
      width: "24",
      height: "24"
    });
  }
  const defaultIcons = {
    home: {
      default: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_HomeIcon__WEBPACK_IMPORTED_MODULE_3__["default"], null),
      active: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_HomeIconActive__WEBPACK_IMPORTED_MODULE_4__["default"], null)
    },
    feedback_tab: {
      default: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_SendMessageIcon__WEBPACK_IMPORTED_MODULE_5__["default"], null),
      active: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_SendMessageIcon__WEBPACK_IMPORTED_MODULE_5__["default"], null)
    },
    resources: {
      default: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ResourcesIcon__WEBPACK_IMPORTED_MODULE_6__["default"], null),
      active: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ResourcesActiveIcon__WEBPACK_IMPORTED_MODULE_7__["default"], null)
    },
    chatbot: {
      default: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ChatbotTabIcon__WEBPACK_IMPORTED_MODULE_8__["default"], null),
      active: (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_ChatbotActiveIcon__WEBPACK_IMPORTED_MODULE_9__["default"], null)
    }
  };
  if (active) {
    return defaultIcons?.[icon]?.active;
  }
  return defaultIcons?.[icon].default;
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TabIcon);

/***/ }),

/***/ "./components/icons/Upload.js":
/*!************************************!*\
  !*** ./components/icons/Upload.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);


const Upload = () => {
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", {
    xmlns: "http://www.w3.org/2000/svg",
    width: 20,
    height: 20,
    fill: "none"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("g", {
    clipPath: "url(#a)"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    stroke: "#475467",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 1.667,
    d: "M13.334 13.333 10 10m0 0-3.333 3.333M10 10v7.5m6.992-2.175A4.167 4.167 0 0 0 15 7.5h-1.05A6.667 6.667 0 1 0 2.5 13.583"
  })), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", {
    id: "a"
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("path", {
    fill: "#fff",
    d: "M0 0h20v20H0z"
  }))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Upload);

/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _extends)
/* harmony export */ });
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}


/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _inheritsLoose)
/* harmony export */ });
/* harmony import */ var _setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setPrototypeOf.js */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js");

function _inheritsLoose(t, o) {
  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, (0,_setPrototypeOf_js__WEBPACK_IMPORTED_MODULE_0__["default"])(t, o);
}


/***/ }),

/***/ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _setPrototypeOf)
/* harmony export */ });
function _setPrototypeOf(t, e) {
  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {
    return t.__proto__ = e, t;
  }, _setPrototypeOf(t, e);
}


/***/ }),

/***/ "./node_modules/.pnpm/react-content-loader@6.2.1_react@18.3.1/node_modules/react-content-loader/dist/react-content-loader.es.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-content-loader@6.2.1_react@18.3.1/node_modules/react-content-loader/dist/react-content-loader.es.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BulletList: () => (/* binding */ ReactContentLoaderBulletList),
/* harmony export */   Code: () => (/* binding */ ReactContentLoaderCode),
/* harmony export */   Facebook: () => (/* binding */ ReactContentLoaderFacebook),
/* harmony export */   Instagram: () => (/* binding */ ReactContentLoaderInstagram),
/* harmony export */   List: () => (/* binding */ ReactContentLoaderListStyle),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);


/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function __rest(s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
}

var uid = (function () {
    return Math.random()
        .toString(36)
        .substring(6);
});

var SVG = function (_a) {
    var _b = _a.animate, animate = _b === void 0 ? true : _b, animateBegin = _a.animateBegin, _c = _a.backgroundColor, backgroundColor = _c === void 0 ? '#f5f6f7' : _c, _d = _a.backgroundOpacity, backgroundOpacity = _d === void 0 ? 1 : _d, _e = _a.baseUrl, baseUrl = _e === void 0 ? '' : _e, children = _a.children, _f = _a.foregroundColor, foregroundColor = _f === void 0 ? '#eee' : _f, _g = _a.foregroundOpacity, foregroundOpacity = _g === void 0 ? 1 : _g, _h = _a.gradientRatio, gradientRatio = _h === void 0 ? 2 : _h, _j = _a.gradientDirection, gradientDirection = _j === void 0 ? 'left-right' : _j, uniqueKey = _a.uniqueKey, _k = _a.interval, interval = _k === void 0 ? 0.25 : _k, _l = _a.rtl, rtl = _l === void 0 ? false : _l, _m = _a.speed, speed = _m === void 0 ? 1.2 : _m, _o = _a.style, style = _o === void 0 ? {} : _o, _p = _a.title, title = _p === void 0 ? 'Loading...' : _p, _q = _a.beforeMask, beforeMask = _q === void 0 ? null : _q, props = __rest(_a, ["animate", "animateBegin", "backgroundColor", "backgroundOpacity", "baseUrl", "children", "foregroundColor", "foregroundOpacity", "gradientRatio", "gradientDirection", "uniqueKey", "interval", "rtl", "speed", "style", "title", "beforeMask"]);
    var fixedId = uniqueKey || uid();
    var idClip = fixedId + "-diff";
    var idGradient = fixedId + "-animated-diff";
    var idAria = fixedId + "-aria";
    var rtlStyle = rtl ? { transform: 'scaleX(-1)' } : null;
    var keyTimes = "0; " + interval + "; 1";
    var dur = speed + "s";
    var gradientTransform = gradientDirection === 'top-bottom' ? 'rotate(90)' : undefined;
    return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("svg", __assign({ "aria-labelledby": idAria, role: "img", style: __assign(__assign({}, style), rtlStyle) }, props),
        title ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("title", { id: idAria }, title) : null,
        beforeMask && (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(beforeMask) ? beforeMask : null,
        (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { role: "presentation", x: "0", y: "0", width: "100%", height: "100%", clipPath: "url(" + baseUrl + "#" + idClip + ")", style: { fill: "url(" + baseUrl + "#" + idGradient + ")" } }),
        (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("defs", null,
            (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("clipPath", { id: idClip }, children),
            (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("linearGradient", { id: idGradient, gradientTransform: gradientTransform },
                (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("stop", { offset: "0%", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("animate", { attributeName: "offset", values: -gradientRatio + "; " + -gradientRatio + "; 1", keyTimes: keyTimes, dur: dur, repeatCount: "indefinite", begin: animateBegin }))),
                (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("stop", { offset: "50%", stopColor: foregroundColor, stopOpacity: foregroundOpacity }, animate && ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("animate", { attributeName: "offset", values: -gradientRatio / 2 + "; " + -gradientRatio / 2 + "; " + (1 +
                        gradientRatio / 2), keyTimes: keyTimes, dur: dur, repeatCount: "indefinite", begin: animateBegin }))),
                (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("stop", { offset: "100%", stopColor: backgroundColor, stopOpacity: backgroundOpacity }, animate && ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("animate", { attributeName: "offset", values: "0; 0; " + (1 + gradientRatio), keyTimes: keyTimes, dur: dur, repeatCount: "indefinite", begin: animateBegin })))))));
};

var ContentLoader = function (props) {
    return props.children ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SVG, __assign({}, props)) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ReactContentLoaderFacebook, __assign({}, props));
};

var ReactContentLoaderFacebook = function (props) { return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ContentLoader, __assign({ viewBox: "0 0 476 124" }, props),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "48", y: "8", width: "88", height: "6", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "48", y: "26", width: "52", height: "6", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "56", width: "410", height: "6", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "72", width: "380", height: "6", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "88", width: "178", height: "6", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "20", cy: "20", r: "20" }))); };

var ReactContentLoaderInstagram = function (props) { return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ContentLoader, __assign({ viewBox: "0 0 400 460" }, props),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "31", cy: "31", r: "15" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "58", y: "18", rx: "2", ry: "2", width: "140", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "58", y: "34", rx: "2", ry: "2", width: "140", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "60", rx: "2", ry: "2", width: "400", height: "400" }))); };

var ReactContentLoaderCode = function (props) { return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ContentLoader, __assign({ viewBox: "0 0 340 84" }, props),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "0", width: "67", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "76", y: "0", width: "140", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "127", y: "48", width: "53", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "187", y: "48", width: "72", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "18", y: "48", width: "100", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "71", width: "37", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "18", y: "23", width: "140", height: "11", rx: "3" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "166", y: "23", width: "173", height: "11", rx: "3" }))); };

var ReactContentLoaderListStyle = function (props) { return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ContentLoader, __assign({ viewBox: "0 0 400 110" }, props),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "0", rx: "3", ry: "3", width: "250", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "20", y: "20", rx: "3", ry: "3", width: "220", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "20", y: "40", rx: "3", ry: "3", width: "170", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "0", y: "60", rx: "3", ry: "3", width: "250", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "20", y: "80", rx: "3", ry: "3", width: "200", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "20", y: "100", rx: "3", ry: "3", width: "80", height: "10" }))); };

var ReactContentLoaderBulletList = function (props) { return ((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ContentLoader, __assign({ viewBox: "0 0 245 125" }, props),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "10", cy: "20", r: "8" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "25", y: "15", rx: "5", ry: "5", width: "220", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "10", cy: "50", r: "8" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "25", y: "45", rx: "5", ry: "5", width: "220", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "10", cy: "80", r: "8" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "25", y: "75", rx: "5", ry: "5", width: "220", height: "10" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("circle", { cx: "10", cy: "110", r: "8" }),
    (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("rect", { x: "25", y: "105", rx: "5", ry: "5", width: "220", height: "10" }))); };

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContentLoader);

//# sourceMappingURL=react-content-loader.es.js.map


/***/ }),

/***/ "./node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var m = __webpack_require__(/*! react-dom */ "react-dom");
if (false) {} else {
  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
  exports.createRoot = function(c, o) {
    i.usingClientEntryPoint = true;
    try {
      return m.createRoot(c, o);
    } finally {
      i.usingClientEntryPoint = false;
    }
  };
  exports.hydrateRoot = function(c, h, o) {
    i.usingClientEntryPoint = true;
    try {
      return m.hydrateRoot(c, h, o);
    } finally {
      i.usingClientEntryPoint = false;
    }
  };
}


/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FocusManager: () => (/* binding */ FocusManager),
/* harmony export */   focusManager: () => (/* binding */ focusManager)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");



var FocusManager = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(FocusManager, _Subscribable);

  function FocusManager() {
    var _this;

    _this = _Subscribable.call(this) || this;

    _this.setup = function (onFocus) {
      var _window;

      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {
        var listener = function listener() {
          return onFocus();
        }; // Listen to visibillitychange and focus


        window.addEventListener('visibilitychange', listener, false);
        window.addEventListener('focus', listener, false);
        return function () {
          // Be sure to unsubscribe if a new handler is set
          window.removeEventListener('visibilitychange', listener);
          window.removeEventListener('focus', listener);
        };
      }
    };

    return _this;
  }

  var _proto = FocusManager.prototype;

  _proto.onSubscribe = function onSubscribe() {
    if (!this.cleanup) {
      this.setEventListener(this.setup);
    }
  };

  _proto.onUnsubscribe = function onUnsubscribe() {
    if (!this.hasListeners()) {
      var _this$cleanup;

      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);
      this.cleanup = undefined;
    }
  };

  _proto.setEventListener = function setEventListener(setup) {
    var _this$cleanup2,
        _this2 = this;

    this.setup = setup;
    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);
    this.cleanup = setup(function (focused) {
      if (typeof focused === 'boolean') {
        _this2.setFocused(focused);
      } else {
        _this2.onFocus();
      }
    });
  };

  _proto.setFocused = function setFocused(focused) {
    this.focused = focused;

    if (focused) {
      this.onFocus();
    }
  };

  _proto.onFocus = function onFocus() {
    this.listeners.forEach(function (listener) {
      listener();
    });
  };

  _proto.isFocused = function isFocused() {
    if (typeof this.focused === 'boolean') {
      return this.focused;
    } // document global can be unavailable in react native


    if (typeof document === 'undefined') {
      return true;
    }

    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);
  };

  return FocusManager;
}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);
var focusManager = new FocusManager();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/hydration.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/hydration.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   dehydrate: () => (/* binding */ dehydrate),
/* harmony export */   hydrate: () => (/* binding */ hydrate)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");


// TYPES
// FUNCTIONS
function dehydrateMutation(mutation) {
  return {
    mutationKey: mutation.options.mutationKey,
    state: mutation.state
  };
} // Most config is not dehydrated but instead meant to configure again when
// consuming the de/rehydrated data, typically with useQuery on the client.
// Sometimes it might make sense to prefetch data on the server and include
// in the html-payload, but not consume it on the initial render.


function dehydrateQuery(query) {
  return {
    state: query.state,
    queryKey: query.queryKey,
    queryHash: query.queryHash
  };
}

function defaultShouldDehydrateMutation(mutation) {
  return mutation.state.isPaused;
}

function defaultShouldDehydrateQuery(query) {
  return query.state.status === 'success';
}

function dehydrate(client, options) {
  var _options, _options2;

  options = options || {};
  var mutations = [];
  var queries = [];

  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {
    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;
    client.getMutationCache().getAll().forEach(function (mutation) {
      if (shouldDehydrateMutation(mutation)) {
        mutations.push(dehydrateMutation(mutation));
      }
    });
  }

  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {
    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;
    client.getQueryCache().getAll().forEach(function (query) {
      if (shouldDehydrateQuery(query)) {
        queries.push(dehydrateQuery(query));
      }
    });
  }

  return {
    mutations: mutations,
    queries: queries
  };
}
function hydrate(client, dehydratedState, options) {
  if (typeof dehydratedState !== 'object' || dehydratedState === null) {
    return;
  }

  var mutationCache = client.getMutationCache();
  var queryCache = client.getQueryCache();
  var mutations = dehydratedState.mutations || [];
  var queries = dehydratedState.queries || [];
  mutations.forEach(function (dehydratedMutation) {
    var _options$defaultOptio;

    mutationCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {
      mutationKey: dehydratedMutation.mutationKey
    }), dehydratedMutation.state);
  });
  queries.forEach(function (dehydratedQuery) {
    var _options$defaultOptio2;

    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data

    if (query) {
      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {
        query.setState(dehydratedQuery.state);
      }

      return;
    } // Restore query


    queryCache.build(client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {
      queryKey: dehydratedQuery.queryKey,
      queryHash: dehydratedQuery.queryHash
    }), dehydratedQuery.state);
  });
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/index.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/index.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.CancelledError),
/* harmony export */   InfiniteQueryObserver: () => (/* reexport safe */ _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__.InfiniteQueryObserver),
/* harmony export */   MutationCache: () => (/* reexport safe */ _mutationCache__WEBPACK_IMPORTED_MODULE_6__.MutationCache),
/* harmony export */   MutationObserver: () => (/* reexport safe */ _mutationObserver__WEBPACK_IMPORTED_MODULE_7__.MutationObserver),
/* harmony export */   QueriesObserver: () => (/* reexport safe */ _queriesObserver__WEBPACK_IMPORTED_MODULE_4__.QueriesObserver),
/* harmony export */   QueryCache: () => (/* reexport safe */ _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache),
/* harmony export */   QueryClient: () => (/* reexport safe */ _queryClient__WEBPACK_IMPORTED_MODULE_2__.QueryClient),
/* harmony export */   QueryObserver: () => (/* reexport safe */ _queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver),
/* harmony export */   dehydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.dehydrate),
/* harmony export */   focusManager: () => (/* reexport safe */ _focusManager__WEBPACK_IMPORTED_MODULE_10__.focusManager),
/* harmony export */   hashQueryKey: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.hashQueryKey),
/* harmony export */   hydrate: () => (/* reexport safe */ _hydration__WEBPACK_IMPORTED_MODULE_13__.hydrate),
/* harmony export */   isCancelledError: () => (/* reexport safe */ _retryer__WEBPACK_IMPORTED_MODULE_0__.isCancelledError),
/* harmony export */   isError: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_12__.isError),
/* harmony export */   notifyManager: () => (/* reexport safe */ _notifyManager__WEBPACK_IMPORTED_MODULE_9__.notifyManager),
/* harmony export */   onlineManager: () => (/* reexport safe */ _onlineManager__WEBPACK_IMPORTED_MODULE_11__.onlineManager),
/* harmony export */   setLogger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_8__.setLogger)
/* harmony export */ });
/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryer */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js");
/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryCache.js");
/* harmony import */ var _queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queryClient */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryClient.js");
/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js");
/* harmony import */ var _queriesObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queriesObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queriesObserver.js");
/* harmony import */ var _infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./infiniteQueryObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryObserver.js");
/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mutationCache */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationCache.js");
/* harmony import */ var _mutationObserver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mutationObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationObserver.js");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./logger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./focusManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js");
/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./onlineManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/onlineManager.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _hydration__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hydration */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/hydration.js");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/types.js");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_14__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_14__) if(["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_14__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);














 // Types



/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryBehavior.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryBehavior.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getNextPageParam: () => (/* binding */ getNextPageParam),
/* harmony export */   getPreviousPageParam: () => (/* binding */ getPreviousPageParam),
/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),
/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),
/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)
/* harmony export */ });
/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");


function infiniteQueryBehavior() {
  return {
    onFetch: function onFetch(context) {
      context.fetchFn = function () {
        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;

        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;
        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;
        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;
        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';
        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';
        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];
        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];
        var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getAbortController)();
        var abortSignal = abortController == null ? void 0 : abortController.signal;
        var newPageParams = oldPageParams;
        var cancelled = false; // Get query function

        var queryFn = context.options.queryFn || function () {
          return Promise.reject('Missing queryFn');
        };

        var buildNewPages = function buildNewPages(pages, param, page, previous) {
          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);
          return previous ? [page].concat(pages) : [].concat(pages, [page]);
        }; // Create function to fetch a page


        var fetchPage = function fetchPage(pages, manual, param, previous) {
          if (cancelled) {
            return Promise.reject('Cancelled');
          }

          if (typeof param === 'undefined' && !manual && pages.length) {
            return Promise.resolve(pages);
          }

          var queryFnContext = {
            queryKey: context.queryKey,
            signal: abortSignal,
            pageParam: param,
            meta: context.meta
          };
          var queryFnResult = queryFn(queryFnContext);
          var promise = Promise.resolve(queryFnResult).then(function (page) {
            return buildNewPages(pages, param, page, previous);
          });

          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(queryFnResult)) {
            var promiseAsAny = promise;
            promiseAsAny.cancel = queryFnResult.cancel;
          }

          return promise;
        };

        var promise; // Fetch first page?

        if (!oldPages.length) {
          promise = fetchPage([]);
        } // Fetch next page?
        else if (isFetchingNextPage) {
            var manual = typeof pageParam !== 'undefined';
            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);
            promise = fetchPage(oldPages, manual, param);
          } // Fetch previous page?
          else if (isFetchingPreviousPage) {
              var _manual = typeof pageParam !== 'undefined';

              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);

              promise = fetchPage(oldPages, _manual, _param, true);
            } // Refetch pages
            else {
                (function () {
                  newPageParams = [];
                  var manual = typeof context.options.getNextPageParam === 'undefined';
                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page

                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages

                  var _loop = function _loop(i) {
                    promise = promise.then(function (pages) {
                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;

                      if (shouldFetchNextPage) {
                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);

                        return fetchPage(pages, manual, _param2);
                      }

                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));
                    });
                  };

                  for (var i = 1; i < oldPages.length; i++) {
                    _loop(i);
                  }
                })();
              }

        var finalPromise = promise.then(function (pages) {
          return {
            pages: pages,
            pageParams: newPageParams
          };
        });
        var finalPromiseAsAny = finalPromise;

        finalPromiseAsAny.cancel = function () {
          cancelled = true;
          abortController == null ? void 0 : abortController.abort();

          if ((0,_retryer__WEBPACK_IMPORTED_MODULE_1__.isCancelable)(promise)) {
            promise.cancel();
          }
        };

        return finalPromise;
      };
    }
  };
}
function getNextPageParam(options, pages) {
  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);
}
function getPreviousPageParam(options, pages) {
  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);
}
/**
 * Checks if there is a next page.
 * Returns `undefined` if it cannot be determined.
 */

function hasNextPage(options, pages) {
  if (options.getNextPageParam && Array.isArray(pages)) {
    var nextPageParam = getNextPageParam(options, pages);
    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;
  }
}
/**
 * Checks if there is a previous page.
 * Returns `undefined` if it cannot be determined.
 */

function hasPreviousPage(options, pages) {
  if (options.getPreviousPageParam && Array.isArray(pages)) {
    var previousPageParam = getPreviousPageParam(options, pages);
    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;
  }
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryObserver.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryObserver.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   InfiniteQueryObserver: () => (/* binding */ InfiniteQueryObserver)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queryObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js");
/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./infiniteQueryBehavior */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryBehavior.js");




var InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(InfiniteQueryObserver, _QueryObserver);

  // Type override
  // Type override
  // Type override
  // eslint-disable-next-line @typescript-eslint/no-useless-constructor
  function InfiniteQueryObserver(client, options) {
    return _QueryObserver.call(this, client, options) || this;
  }

  var _proto = InfiniteQueryObserver.prototype;

  _proto.bindMethods = function bindMethods() {
    _QueryObserver.prototype.bindMethods.call(this);

    this.fetchNextPage = this.fetchNextPage.bind(this);
    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);
  };

  _proto.setOptions = function setOptions(options, notifyOptions) {
    _QueryObserver.prototype.setOptions.call(this, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, options, {
      behavior: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)()
    }), notifyOptions);
  };

  _proto.getOptimisticResult = function getOptimisticResult(options) {
    options.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.infiniteQueryBehavior)();
    return _QueryObserver.prototype.getOptimisticResult.call(this, options);
  };

  _proto.fetchNextPage = function fetchNextPage(options) {
    var _options$cancelRefetc;

    return this.fetch({
      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)
      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,
      throwOnError: options == null ? void 0 : options.throwOnError,
      meta: {
        fetchMore: {
          direction: 'forward',
          pageParam: options == null ? void 0 : options.pageParam
        }
      }
    });
  };

  _proto.fetchPreviousPage = function fetchPreviousPage(options) {
    var _options$cancelRefetc2;

    return this.fetch({
      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)
      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,
      throwOnError: options == null ? void 0 : options.throwOnError,
      meta: {
        fetchMore: {
          direction: 'backward',
          pageParam: options == null ? void 0 : options.pageParam
        }
      }
    });
  };

  _proto.createResult = function createResult(query, options) {
    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;

    var state = query.state;

    var result = _QueryObserver.prototype.createResult.call(this, query, options);

    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, result, {
      fetchNextPage: this.fetchNextPage,
      fetchPreviousPage: this.fetchPreviousPage,
      hasNextPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasNextPage)(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),
      hasPreviousPage: (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_2__.hasPreviousPage)(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),
      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',
      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'
    });
  };

  return InfiniteQueryObserver;
}(_queryObserver__WEBPACK_IMPORTED_MODULE_3__.QueryObserver);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getLogger: () => (/* binding */ getLogger),
/* harmony export */   setLogger: () => (/* binding */ setLogger)
/* harmony export */ });
// TYPES
// FUNCTIONS
var logger = console;
function getLogger() {
  return logger;
}
function setLogger(newLogger) {
  logger = newLogger;
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutation.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutation.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Mutation: () => (/* binding */ Mutation),
/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./logger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./retryer */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");




 // TYPES

// CLASS
var Mutation = /*#__PURE__*/function () {
  function Mutation(config) {
    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, config.defaultOptions, config.options);
    this.mutationId = config.mutationId;
    this.mutationCache = config.mutationCache;
    this.observers = [];
    this.state = config.state || getDefaultState();
    this.meta = config.meta;
  }

  var _proto = Mutation.prototype;

  _proto.setState = function setState(state) {
    this.dispatch({
      type: 'setState',
      state: state
    });
  };

  _proto.addObserver = function addObserver(observer) {
    if (this.observers.indexOf(observer) === -1) {
      this.observers.push(observer);
    }
  };

  _proto.removeObserver = function removeObserver(observer) {
    this.observers = this.observers.filter(function (x) {
      return x !== observer;
    });
  };

  _proto.cancel = function cancel() {
    if (this.retryer) {
      this.retryer.cancel();
      return this.retryer.promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop);
    }

    return Promise.resolve();
  };

  _proto.continue = function _continue() {
    if (this.retryer) {
      this.retryer.continue();
      return this.retryer.promise;
    }

    return this.execute();
  };

  _proto.execute = function execute() {
    var _this = this;

    var data;
    var restored = this.state.status === 'loading';
    var promise = Promise.resolve();

    if (!restored) {
      this.dispatch({
        type: 'loading',
        variables: this.options.variables
      });
      promise = promise.then(function () {
        // Notify cache callback
        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);
      }).then(function () {
        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);
      }).then(function (context) {
        if (context !== _this.state.context) {
          _this.dispatch({
            type: 'loading',
            context: context,
            variables: _this.state.variables
          });
        }
      });
    }

    return promise.then(function () {
      return _this.executeMutation();
    }).then(function (result) {
      data = result; // Notify cache callback

      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);
    }).then(function () {
      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);
    }).then(function () {
      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);
    }).then(function () {
      _this.dispatch({
        type: 'success',
        data: data
      });

      return data;
    }).catch(function (error) {
      // Notify cache callback
      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error

      (0,_logger__WEBPACK_IMPORTED_MODULE_2__.getLogger)().error(error);
      return Promise.resolve().then(function () {
        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);
      }).then(function () {
        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);
      }).then(function () {
        _this.dispatch({
          type: 'error',
          error: error
        });

        throw error;
      });
    });
  };

  _proto.executeMutation = function executeMutation() {
    var _this2 = this,
        _this$options$retry;

    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_3__.Retryer({
      fn: function fn() {
        if (!_this2.options.mutationFn) {
          return Promise.reject('No mutationFn found');
        }

        return _this2.options.mutationFn(_this2.state.variables);
      },
      onFail: function onFail() {
        _this2.dispatch({
          type: 'failed'
        });
      },
      onPause: function onPause() {
        _this2.dispatch({
          type: 'pause'
        });
      },
      onContinue: function onContinue() {
        _this2.dispatch({
          type: 'continue'
        });
      },
      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,
      retryDelay: this.options.retryDelay
    });
    return this.retryer.promise;
  };

  _proto.dispatch = function dispatch(action) {
    var _this3 = this;

    this.state = reducer(this.state, action);
    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {
      _this3.observers.forEach(function (observer) {
        observer.onMutationUpdate(action);
      });

      _this3.mutationCache.notify(_this3);
    });
  };

  return Mutation;
}();
function getDefaultState() {
  return {
    context: undefined,
    data: undefined,
    error: null,
    failureCount: 0,
    isPaused: false,
    status: 'idle',
    variables: undefined
  };
}

function reducer(state, action) {
  switch (action.type) {
    case 'failed':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        failureCount: state.failureCount + 1
      });

    case 'pause':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        isPaused: true
      });

    case 'continue':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        isPaused: false
      });

    case 'loading':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        context: action.context,
        data: undefined,
        error: null,
        isPaused: false,
        status: 'loading',
        variables: action.variables
      });

    case 'success':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        data: action.data,
        error: null,
        status: 'success',
        isPaused: false
      });

    case 'error':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
        data: undefined,
        error: action.error,
        failureCount: state.failureCount + 1,
        isPaused: false,
        status: 'error'
      });

    case 'setState':
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, action.state);

    default:
      return state;
  }
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationCache.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationCache.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MutationCache: () => (/* binding */ MutationCache)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutation.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");




 // TYPES

// CLASS
var MutationCache = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(MutationCache, _Subscribable);

  function MutationCache(config) {
    var _this;

    _this = _Subscribable.call(this) || this;
    _this.config = config || {};
    _this.mutations = [];
    _this.mutationId = 0;
    return _this;
  }

  var _proto = MutationCache.prototype;

  _proto.build = function build(client, options, state) {
    var mutation = new _mutation__WEBPACK_IMPORTED_MODULE_1__.Mutation({
      mutationCache: this,
      mutationId: ++this.mutationId,
      options: client.defaultMutationOptions(options),
      state: state,
      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,
      meta: options.meta
    });
    this.add(mutation);
    return mutation;
  };

  _proto.add = function add(mutation) {
    this.mutations.push(mutation);
    this.notify(mutation);
  };

  _proto.remove = function remove(mutation) {
    this.mutations = this.mutations.filter(function (x) {
      return x !== mutation;
    });
    mutation.cancel();
    this.notify(mutation);
  };

  _proto.clear = function clear() {
    var _this2 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {
      _this2.mutations.forEach(function (mutation) {
        _this2.remove(mutation);
      });
    });
  };

  _proto.getAll = function getAll() {
    return this.mutations;
  };

  _proto.find = function find(filters) {
    if (typeof filters.exact === 'undefined') {
      filters.exact = true;
    }

    return this.mutations.find(function (mutation) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);
    });
  };

  _proto.findAll = function findAll(filters) {
    return this.mutations.filter(function (mutation) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation);
    });
  };

  _proto.notify = function notify(mutation) {
    var _this3 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {
      _this3.listeners.forEach(function (listener) {
        listener(mutation);
      });
    });
  };

  _proto.onFocus = function onFocus() {
    this.resumePausedMutations();
  };

  _proto.onOnline = function onOnline() {
    this.resumePausedMutations();
  };

  _proto.resumePausedMutations = function resumePausedMutations() {
    var pausedMutations = this.mutations.filter(function (x) {
      return x.state.isPaused;
    });
    return _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {
      return pausedMutations.reduce(function (promise, mutation) {
        return promise.then(function () {
          return mutation.continue().catch(_utils__WEBPACK_IMPORTED_MODULE_3__.noop);
        });
      }, Promise.resolve());
    });
  };

  return MutationCache;
}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationObserver.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationObserver.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutation.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");





// CLASS
var MutationObserver = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(MutationObserver, _Subscribable);

  function MutationObserver(client, options) {
    var _this;

    _this = _Subscribable.call(this) || this;
    _this.client = client;

    _this.setOptions(options);

    _this.bindMethods();

    _this.updateResult();

    return _this;
  }

  var _proto = MutationObserver.prototype;

  _proto.bindMethods = function bindMethods() {
    this.mutate = this.mutate.bind(this);
    this.reset = this.reset.bind(this);
  };

  _proto.setOptions = function setOptions(options) {
    this.options = this.client.defaultMutationOptions(options);
  };

  _proto.onUnsubscribe = function onUnsubscribe() {
    if (!this.listeners.length) {
      var _this$currentMutation;

      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);
    }
  };

  _proto.onMutationUpdate = function onMutationUpdate(action) {
    this.updateResult(); // Determine which callbacks to trigger

    var notifyOptions = {
      listeners: true
    };

    if (action.type === 'success') {
      notifyOptions.onSuccess = true;
    } else if (action.type === 'error') {
      notifyOptions.onError = true;
    }

    this.notify(notifyOptions);
  };

  _proto.getCurrentResult = function getCurrentResult() {
    return this.currentResult;
  };

  _proto.reset = function reset() {
    this.currentMutation = undefined;
    this.updateResult();
    this.notify({
      listeners: true
    });
  };

  _proto.mutate = function mutate(variables, options) {
    this.mutateOptions = options;

    if (this.currentMutation) {
      this.currentMutation.removeObserver(this);
    }

    this.currentMutation = this.client.getMutationCache().build(this.client, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, this.options, {
      variables: typeof variables !== 'undefined' ? variables : this.options.variables
    }));
    this.currentMutation.addObserver(this);
    return this.currentMutation.execute();
  };

  _proto.updateResult = function updateResult() {
    var state = this.currentMutation ? this.currentMutation.state : (0,_mutation__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();

    var result = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
      isLoading: state.status === 'loading',
      isSuccess: state.status === 'success',
      isError: state.status === 'error',
      isIdle: state.status === 'idle',
      mutate: this.mutate,
      reset: this.reset
    });

    this.currentResult = result;
  };

  _proto.notify = function notify(options) {
    var _this2 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {
      // First trigger the mutate callbacks
      if (_this2.mutateOptions) {
        if (options.onSuccess) {
          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);
          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);
        } else if (options.onError) {
          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);
          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);
        }
      } // Then trigger the listeners


      if (options.listeners) {
        _this2.listeners.forEach(function (listener) {
          listener(_this2.currentResult);
        });
      }
    });
  };

  return MutationObserver;
}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotifyManager: () => (/* binding */ NotifyManager),
/* harmony export */   notifyManager: () => (/* binding */ notifyManager)
/* harmony export */ });
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
 // TYPES

// CLASS
var NotifyManager = /*#__PURE__*/function () {
  function NotifyManager() {
    this.queue = [];
    this.transactions = 0;

    this.notifyFn = function (callback) {
      callback();
    };

    this.batchNotifyFn = function (callback) {
      callback();
    };
  }

  var _proto = NotifyManager.prototype;

  _proto.batch = function batch(callback) {
    var result;
    this.transactions++;

    try {
      result = callback();
    } finally {
      this.transactions--;

      if (!this.transactions) {
        this.flush();
      }
    }

    return result;
  };

  _proto.schedule = function schedule(callback) {
    var _this = this;

    if (this.transactions) {
      this.queue.push(callback);
    } else {
      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {
        _this.notifyFn(callback);
      });
    }
  }
  /**
   * All calls to the wrapped function will be batched.
   */
  ;

  _proto.batchCalls = function batchCalls(callback) {
    var _this2 = this;

    return function () {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }

      _this2.schedule(function () {
        callback.apply(void 0, args);
      });
    };
  };

  _proto.flush = function flush() {
    var _this3 = this;

    var queue = this.queue;
    this.queue = [];

    if (queue.length) {
      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.scheduleMicrotask)(function () {
        _this3.batchNotifyFn(function () {
          queue.forEach(function (callback) {
            _this3.notifyFn(callback);
          });
        });
      });
    }
  }
  /**
   * Use this method to set a custom notify function.
   * This can be used to for example wrap notifications with `React.act` while running tests.
   */
  ;

  _proto.setNotifyFunction = function setNotifyFunction(fn) {
    this.notifyFn = fn;
  }
  /**
   * Use this method to set a custom function to batch notifications together into a single tick.
   * By default React Query will use the batch function provided by ReactDOM or React Native.
   */
  ;

  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {
    this.batchNotifyFn = fn;
  };

  return NotifyManager;
}(); // SINGLETON

var notifyManager = new NotifyManager();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/onlineManager.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/onlineManager.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),
/* harmony export */   onlineManager: () => (/* binding */ onlineManager)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");



var OnlineManager = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(OnlineManager, _Subscribable);

  function OnlineManager() {
    var _this;

    _this = _Subscribable.call(this) || this;

    _this.setup = function (onOnline) {
      var _window;

      if (!_utils__WEBPACK_IMPORTED_MODULE_1__.isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {
        var listener = function listener() {
          return onOnline();
        }; // Listen to online


        window.addEventListener('online', listener, false);
        window.addEventListener('offline', listener, false);
        return function () {
          // Be sure to unsubscribe if a new handler is set
          window.removeEventListener('online', listener);
          window.removeEventListener('offline', listener);
        };
      }
    };

    return _this;
  }

  var _proto = OnlineManager.prototype;

  _proto.onSubscribe = function onSubscribe() {
    if (!this.cleanup) {
      this.setEventListener(this.setup);
    }
  };

  _proto.onUnsubscribe = function onUnsubscribe() {
    if (!this.hasListeners()) {
      var _this$cleanup;

      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);
      this.cleanup = undefined;
    }
  };

  _proto.setEventListener = function setEventListener(setup) {
    var _this$cleanup2,
        _this2 = this;

    this.setup = setup;
    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);
    this.cleanup = setup(function (online) {
      if (typeof online === 'boolean') {
        _this2.setOnline(online);
      } else {
        _this2.onOnline();
      }
    });
  };

  _proto.setOnline = function setOnline(online) {
    this.online = online;

    if (online) {
      this.onOnline();
    }
  };

  _proto.onOnline = function onOnline() {
    this.listeners.forEach(function (listener) {
      listener();
    });
  };

  _proto.isOnline = function isOnline() {
    if (typeof this.online === 'boolean') {
      return this.online;
    }

    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {
      return true;
    }

    return navigator.onLine;
  };

  return OnlineManager;
}(_subscribable__WEBPACK_IMPORTED_MODULE_2__.Subscribable);
var onlineManager = new OnlineManager();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queriesObserver.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queriesObserver.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueriesObserver: () => (/* binding */ QueriesObserver)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _queryObserver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");





var QueriesObserver = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(QueriesObserver, _Subscribable);

  function QueriesObserver(client, queries) {
    var _this;

    _this = _Subscribable.call(this) || this;
    _this.client = client;
    _this.queries = [];
    _this.result = [];
    _this.observers = [];
    _this.observersMap = {};

    if (queries) {
      _this.setQueries(queries);
    }

    return _this;
  }

  var _proto = QueriesObserver.prototype;

  _proto.onSubscribe = function onSubscribe() {
    var _this2 = this;

    if (this.listeners.length === 1) {
      this.observers.forEach(function (observer) {
        observer.subscribe(function (result) {
          _this2.onUpdate(observer, result);
        });
      });
    }
  };

  _proto.onUnsubscribe = function onUnsubscribe() {
    if (!this.listeners.length) {
      this.destroy();
    }
  };

  _proto.destroy = function destroy() {
    this.listeners = [];
    this.observers.forEach(function (observer) {
      observer.destroy();
    });
  };

  _proto.setQueries = function setQueries(queries, notifyOptions) {
    this.queries = queries;
    this.updateObservers(notifyOptions);
  };

  _proto.getCurrentResult = function getCurrentResult() {
    return this.result;
  };

  _proto.getOptimisticResult = function getOptimisticResult(queries) {
    return this.findMatchingObservers(queries).map(function (match) {
      return match.observer.getOptimisticResult(match.defaultedQueryOptions);
    });
  };

  _proto.findMatchingObservers = function findMatchingObservers(queries) {
    var _this3 = this;

    var prevObservers = this.observers;
    var defaultedQueryOptions = queries.map(function (options) {
      return _this3.client.defaultQueryObserverOptions(options);
    });
    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {
      var match = prevObservers.find(function (observer) {
        return observer.options.queryHash === defaultedOptions.queryHash;
      });

      if (match != null) {
        return [{
          defaultedQueryOptions: defaultedOptions,
          observer: match
        }];
      }

      return [];
    });
    var matchedQueryHashes = matchingObservers.map(function (match) {
      return match.defaultedQueryOptions.queryHash;
    });
    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {
      return !matchedQueryHashes.includes(defaultedOptions.queryHash);
    });
    var unmatchedObservers = prevObservers.filter(function (prevObserver) {
      return !matchingObservers.some(function (match) {
        return match.observer === prevObserver;
      });
    });
    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {
      if (options.keepPreviousData) {
        // return previous data from one of the observers that no longer match
        var previouslyUsedObserver = unmatchedObservers[index];

        if (previouslyUsedObserver !== undefined) {
          return {
            defaultedQueryOptions: options,
            observer: previouslyUsedObserver
          };
        }
      }

      return {
        defaultedQueryOptions: options,
        observer: _this3.getObserver(options)
      };
    });

    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {
      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);
    };

    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);
  };

  _proto.getObserver = function getObserver(options) {
    var defaultedOptions = this.client.defaultQueryObserverOptions(options);
    var currentObserver = this.observersMap[defaultedOptions.queryHash];
    return currentObserver != null ? currentObserver : new _queryObserver__WEBPACK_IMPORTED_MODULE_1__.QueryObserver(this.client, defaultedOptions);
  };

  _proto.updateObservers = function updateObservers(notifyOptions) {
    var _this4 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {
      var prevObservers = _this4.observers;

      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes


      newObserverMatches.forEach(function (match) {
        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);
      });
      var newObservers = newObserverMatches.map(function (match) {
        return match.observer;
      });
      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {
        return [observer.options.queryHash, observer];
      }));
      var newResult = newObservers.map(function (observer) {
        return observer.getCurrentResult();
      });
      var hasIndexChange = newObservers.some(function (observer, index) {
        return observer !== prevObservers[index];
      });

      if (prevObservers.length === newObservers.length && !hasIndexChange) {
        return;
      }

      _this4.observers = newObservers;
      _this4.observersMap = newObserversMap;
      _this4.result = newResult;

      if (!_this4.hasListeners()) {
        return;
      }

      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(prevObservers, newObservers).forEach(function (observer) {
        observer.destroy();
      });
      (0,_utils__WEBPACK_IMPORTED_MODULE_3__.difference)(newObservers, prevObservers).forEach(function (observer) {
        observer.subscribe(function (result) {
          _this4.onUpdate(observer, result);
        });
      });

      _this4.notify();
    });
  };

  _proto.onUpdate = function onUpdate(observer, result) {
    var index = this.observers.indexOf(observer);

    if (index !== -1) {
      this.result = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.replaceAt)(this.result, index, result);
      this.notify();
    }
  };

  _proto.notify = function notify() {
    var _this5 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(function () {
      _this5.listeners.forEach(function (listener) {
        listener(_this5.result);
      });
    });
  };

  return QueriesObserver;
}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/query.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/query.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Query: () => (/* binding */ Query)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js");
/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js");




 // TYPES

// CLASS
var Query = /*#__PURE__*/function () {
  function Query(config) {
    this.abortSignalConsumed = false;
    this.hadObservers = false;
    this.defaultOptions = config.defaultOptions;
    this.setOptions(config.options);
    this.observers = [];
    this.cache = config.cache;
    this.queryKey = config.queryKey;
    this.queryHash = config.queryHash;
    this.initialState = config.state || this.getDefaultState(this.options);
    this.state = this.initialState;
    this.meta = config.meta;
    this.scheduleGc();
  }

  var _proto = Query.prototype;

  _proto.setOptions = function setOptions(options) {
    var _this$options$cacheTi;

    this.options = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, this.defaultOptions, options);
    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set

    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);
  };

  _proto.setDefaultOptions = function setDefaultOptions(options) {
    this.defaultOptions = options;
  };

  _proto.scheduleGc = function scheduleGc() {
    var _this = this;

    this.clearGcTimeout();

    if ((0,_utils__WEBPACK_IMPORTED_MODULE_1__.isValidTimeout)(this.cacheTime)) {
      this.gcTimeout = setTimeout(function () {
        _this.optionalRemove();
      }, this.cacheTime);
    }
  };

  _proto.clearGcTimeout = function clearGcTimeout() {
    if (this.gcTimeout) {
      clearTimeout(this.gcTimeout);
      this.gcTimeout = undefined;
    }
  };

  _proto.optionalRemove = function optionalRemove() {
    if (!this.observers.length) {
      if (this.state.isFetching) {
        if (this.hadObservers) {
          this.scheduleGc();
        }
      } else {
        this.cache.remove(this);
      }
    }
  };

  _proto.setData = function setData(updater, options) {
    var _this$options$isDataE, _this$options;

    var prevData = this.state.data; // Get the new data

    var data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.functionalUpdate)(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`

    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {
      data = prevData;
    } else if (this.options.structuralSharing !== false) {
      // Structurally share data between prev and new data if needed
      data = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.replaceEqualDeep)(prevData, data);
    } // Set data and mark it as cached


    this.dispatch({
      data: data,
      type: 'success',
      dataUpdatedAt: options == null ? void 0 : options.updatedAt
    });
    return data;
  };

  _proto.setState = function setState(state, setStateOptions) {
    this.dispatch({
      type: 'setState',
      state: state,
      setStateOptions: setStateOptions
    });
  };

  _proto.cancel = function cancel(options) {
    var _this$retryer;

    var promise = this.promise;
    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);
    return promise ? promise.then(_utils__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();
  };

  _proto.destroy = function destroy() {
    this.clearGcTimeout();
    this.cancel({
      silent: true
    });
  };

  _proto.reset = function reset() {
    this.destroy();
    this.setState(this.initialState);
  };

  _proto.isActive = function isActive() {
    return this.observers.some(function (observer) {
      return observer.options.enabled !== false;
    });
  };

  _proto.isFetching = function isFetching() {
    return this.state.isFetching;
  };

  _proto.isStale = function isStale() {
    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {
      return observer.getCurrentResult().isStale;
    });
  };

  _proto.isStaleByTime = function isStaleByTime(staleTime) {
    if (staleTime === void 0) {
      staleTime = 0;
    }

    return this.state.isInvalidated || !this.state.dataUpdatedAt || !(0,_utils__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);
  };

  _proto.onFocus = function onFocus() {
    var _this$retryer2;

    var observer = this.observers.find(function (x) {
      return x.shouldFetchOnWindowFocus();
    });

    if (observer) {
      observer.refetch();
    } // Continue fetch if currently paused


    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();
  };

  _proto.onOnline = function onOnline() {
    var _this$retryer3;

    var observer = this.observers.find(function (x) {
      return x.shouldFetchOnReconnect();
    });

    if (observer) {
      observer.refetch();
    } // Continue fetch if currently paused


    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();
  };

  _proto.addObserver = function addObserver(observer) {
    if (this.observers.indexOf(observer) === -1) {
      this.observers.push(observer);
      this.hadObservers = true; // Stop the query from being garbage collected

      this.clearGcTimeout();
      this.cache.notify({
        type: 'observerAdded',
        query: this,
        observer: observer
      });
    }
  };

  _proto.removeObserver = function removeObserver(observer) {
    if (this.observers.indexOf(observer) !== -1) {
      this.observers = this.observers.filter(function (x) {
        return x !== observer;
      });

      if (!this.observers.length) {
        // If the transport layer does not support cancellation
        // we'll let the query continue so the result can be cached
        if (this.retryer) {
          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {
            this.retryer.cancel({
              revert: true
            });
          } else {
            this.retryer.cancelRetry();
          }
        }

        if (this.cacheTime) {
          this.scheduleGc();
        } else {
          this.cache.remove(this);
        }
      }

      this.cache.notify({
        type: 'observerRemoved',
        query: this,
        observer: observer
      });
    }
  };

  _proto.getObserversCount = function getObserversCount() {
    return this.observers.length;
  };

  _proto.invalidate = function invalidate() {
    if (!this.state.isInvalidated) {
      this.dispatch({
        type: 'invalidate'
      });
    }
  };

  _proto.fetch = function fetch(options, fetchOptions) {
    var _this2 = this,
        _this$options$behavio,
        _context$fetchOptions,
        _abortController$abor;

    if (this.state.isFetching) {
      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {
        // Silently cancel current fetch if the user wants to cancel refetches
        this.cancel({
          silent: true
        });
      } else if (this.promise) {
        var _this$retryer4;

        // make sure that retries that were potentially cancelled due to unmounts can continue
        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching

        return this.promise;
      }
    } // Update config if passed, otherwise the config from the last execution is used


    if (options) {
      this.setOptions(options);
    } // Use the options from the first observer with a query function if no function is found.
    // This can happen when the query is hydrated or created with setQueryData.


    if (!this.options.queryFn) {
      var observer = this.observers.find(function (x) {
        return x.options.queryFn;
      });

      if (observer) {
        this.setOptions(observer.options);
      }
    }

    var queryKey = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.ensureQueryKeyArray)(this.queryKey);
    var abortController = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAbortController)(); // Create query function context

    var queryFnContext = {
      queryKey: queryKey,
      pageParam: undefined,
      meta: this.meta
    };
    Object.defineProperty(queryFnContext, 'signal', {
      enumerable: true,
      get: function get() {
        if (abortController) {
          _this2.abortSignalConsumed = true;
          return abortController.signal;
        }

        return undefined;
      }
    }); // Create fetch function

    var fetchFn = function fetchFn() {
      if (!_this2.options.queryFn) {
        return Promise.reject('Missing queryFn');
      }

      _this2.abortSignalConsumed = false;
      return _this2.options.queryFn(queryFnContext);
    }; // Trigger behavior hook


    var context = {
      fetchOptions: fetchOptions,
      options: this.options,
      queryKey: queryKey,
      state: this.state,
      fetchFn: fetchFn,
      meta: this.meta
    };

    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {
      var _this$options$behavio2;

      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);
    } // Store state in case the current fetch needs to be reverted


    this.revertState = this.state; // Set to fetching state if not already in it

    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {
      var _context$fetchOptions2;

      this.dispatch({
        type: 'fetch',
        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta
      });
    } // Try to fetch the data


    this.retryer = new _retryer__WEBPACK_IMPORTED_MODULE_2__.Retryer({
      fn: context.fetchFn,
      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),
      onSuccess: function onSuccess(data) {
        _this2.setData(data); // Notify cache callback


        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0

        if (_this2.cacheTime === 0) {
          _this2.optionalRemove();
        }
      },
      onError: function onError(error) {
        // Optimistically update state if needed
        if (!((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {
          _this2.dispatch({
            type: 'error',
            error: error
          });
        }

        if (!(0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {
          // Notify cache callback
          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error

          (0,_logger__WEBPACK_IMPORTED_MODULE_3__.getLogger)().error(error);
        } // Remove query after fetching if cache time is 0


        if (_this2.cacheTime === 0) {
          _this2.optionalRemove();
        }
      },
      onFail: function onFail() {
        _this2.dispatch({
          type: 'failed'
        });
      },
      onPause: function onPause() {
        _this2.dispatch({
          type: 'pause'
        });
      },
      onContinue: function onContinue() {
        _this2.dispatch({
          type: 'continue'
        });
      },
      retry: context.options.retry,
      retryDelay: context.options.retryDelay
    });
    this.promise = this.retryer.promise;
    return this.promise;
  };

  _proto.dispatch = function dispatch(action) {
    var _this3 = this;

    this.state = this.reducer(this.state, action);
    _notifyManager__WEBPACK_IMPORTED_MODULE_4__.notifyManager.batch(function () {
      _this3.observers.forEach(function (observer) {
        observer.onQueryUpdate(action);
      });

      _this3.cache.notify({
        query: _this3,
        type: 'queryUpdated',
        action: action
      });
    });
  };

  _proto.getDefaultState = function getDefaultState(options) {
    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;
    var hasInitialData = typeof options.initialData !== 'undefined';
    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;
    var hasData = typeof data !== 'undefined';
    return {
      data: data,
      dataUpdateCount: 0,
      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,
      error: null,
      errorUpdateCount: 0,
      errorUpdatedAt: 0,
      fetchFailureCount: 0,
      fetchMeta: null,
      isFetching: false,
      isInvalidated: false,
      isPaused: false,
      status: hasData ? 'success' : 'idle'
    };
  };

  _proto.reducer = function reducer(state, action) {
    var _action$meta, _action$dataUpdatedAt;

    switch (action.type) {
      case 'failed':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          fetchFailureCount: state.fetchFailureCount + 1
        });

      case 'pause':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          isPaused: true
        });

      case 'continue':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          isPaused: false
        });

      case 'fetch':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          fetchFailureCount: 0,
          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,
          isFetching: true,
          isPaused: false
        }, !state.dataUpdatedAt && {
          error: null,
          status: 'loading'
        });

      case 'success':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          data: action.data,
          dataUpdateCount: state.dataUpdateCount + 1,
          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),
          error: null,
          fetchFailureCount: 0,
          isFetching: false,
          isInvalidated: false,
          isPaused: false,
          status: 'success'
        });

      case 'error':
        var error = action.error;

        if ((0,_retryer__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.revertState) {
          return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, this.revertState);
        }

        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          error: error,
          errorUpdateCount: state.errorUpdateCount + 1,
          errorUpdatedAt: Date.now(),
          fetchFailureCount: state.fetchFailureCount + 1,
          isFetching: false,
          isPaused: false,
          status: 'error'
        });

      case 'invalidate':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, {
          isInvalidated: true
        });

      case 'setState':
        return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, state, action.state);

      default:
        return state;
    }
  };

  return Query;
}();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryCache.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryCache.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryCache: () => (/* binding */ QueryCache)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/query.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");





// CLASS
var QueryCache = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_0__["default"])(QueryCache, _Subscribable);

  function QueryCache(config) {
    var _this;

    _this = _Subscribable.call(this) || this;
    _this.config = config || {};
    _this.queries = [];
    _this.queriesMap = {};
    return _this;
  }

  var _proto = QueryCache.prototype;

  _proto.build = function build(client, options, state) {
    var _options$queryHash;

    var queryKey = options.queryKey;
    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : (0,_utils__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);
    var query = this.get(queryHash);

    if (!query) {
      query = new _query__WEBPACK_IMPORTED_MODULE_2__.Query({
        cache: this,
        queryKey: queryKey,
        queryHash: queryHash,
        options: client.defaultQueryOptions(options),
        state: state,
        defaultOptions: client.getQueryDefaults(queryKey),
        meta: options.meta
      });
      this.add(query);
    }

    return query;
  };

  _proto.add = function add(query) {
    if (!this.queriesMap[query.queryHash]) {
      this.queriesMap[query.queryHash] = query;
      this.queries.push(query);
      this.notify({
        type: 'queryAdded',
        query: query
      });
    }
  };

  _proto.remove = function remove(query) {
    var queryInMap = this.queriesMap[query.queryHash];

    if (queryInMap) {
      query.destroy();
      this.queries = this.queries.filter(function (x) {
        return x !== query;
      });

      if (queryInMap === query) {
        delete this.queriesMap[query.queryHash];
      }

      this.notify({
        type: 'queryRemoved',
        query: query
      });
    }
  };

  _proto.clear = function clear() {
    var _this2 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {
      _this2.queries.forEach(function (query) {
        _this2.remove(query);
      });
    });
  };

  _proto.get = function get(queryHash) {
    return this.queriesMap[queryHash];
  };

  _proto.getAll = function getAll() {
    return this.queries;
  };

  _proto.find = function find(arg1, arg2) {
    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),
        filters = _parseFilterArgs[0];

    if (typeof filters.exact === 'undefined') {
      filters.exact = true;
    }

    return this.queries.find(function (query) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);
    });
  };

  _proto.findAll = function findAll(arg1, arg2) {
    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.parseFilterArgs)(arg1, arg2),
        filters = _parseFilterArgs2[0];

    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query);
    }) : this.queries;
  };

  _proto.notify = function notify(event) {
    var _this3 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {
      _this3.listeners.forEach(function (listener) {
        listener(event);
      });
    });
  };

  _proto.onFocus = function onFocus() {
    var _this4 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {
      _this4.queries.forEach(function (query) {
        query.onFocus();
      });
    });
  };

  _proto.onOnline = function onOnline() {
    var _this5 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(function () {
      _this5.queries.forEach(function (query) {
        query.onOnline();
      });
    });
  };

  return QueryCache;
}(_subscribable__WEBPACK_IMPORTED_MODULE_4__.Subscribable);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryClient.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryClient.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryClient: () => (/* binding */ QueryClient)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _queryCache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queryCache */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryCache.js");
/* harmony import */ var _mutationCache__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutationCache */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationCache.js");
/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js");
/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onlineManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/onlineManager.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infiniteQueryBehavior */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryBehavior.js");








// CLASS
var QueryClient = /*#__PURE__*/function () {
  function QueryClient(config) {
    if (config === void 0) {
      config = {};
    }

    this.queryCache = config.queryCache || new _queryCache__WEBPACK_IMPORTED_MODULE_1__.QueryCache();
    this.mutationCache = config.mutationCache || new _mutationCache__WEBPACK_IMPORTED_MODULE_2__.MutationCache();
    this.defaultOptions = config.defaultOptions || {};
    this.queryDefaults = [];
    this.mutationDefaults = [];
  }

  var _proto = QueryClient.prototype;

  _proto.mount = function mount() {
    var _this = this;

    this.unsubscribeFocus = _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.subscribe(function () {
      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {
        _this.mutationCache.onFocus();

        _this.queryCache.onFocus();
      }
    });
    this.unsubscribeOnline = _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.subscribe(function () {
      if (_focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused() && _onlineManager__WEBPACK_IMPORTED_MODULE_4__.onlineManager.isOnline()) {
        _this.mutationCache.onOnline();

        _this.queryCache.onOnline();
      }
    });
  };

  _proto.unmount = function unmount() {
    var _this$unsubscribeFocu, _this$unsubscribeOnli;

    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);
    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);
  };

  _proto.isFetching = function isFetching(arg1, arg2) {
    var _parseFilterArgs = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),
        filters = _parseFilterArgs[0];

    filters.fetching = true;
    return this.queryCache.findAll(filters).length;
  };

  _proto.isMutating = function isMutating(filters) {
    return this.mutationCache.findAll((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, filters, {
      fetching: true
    })).length;
  };

  _proto.getQueryData = function getQueryData(queryKey, filters) {
    var _this$queryCache$find;

    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;
  };

  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {
    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {
      var queryKey = _ref.queryKey,
          state = _ref.state;
      var data = state.data;
      return [queryKey, data];
    });
  };

  _proto.setQueryData = function setQueryData(queryKey, updater, options) {
    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(queryKey);
    var defaultedOptions = this.defaultQueryOptions(parsedOptions);
    return this.queryCache.build(this, defaultedOptions).setData(updater, options);
  };

  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {
    var _this2 = this;

    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {
        var queryKey = _ref2.queryKey;
        return [queryKey, _this2.setQueryData(queryKey, updater, options)];
      });
    });
  };

  _proto.getQueryState = function getQueryState(queryKey, filters) {
    var _this$queryCache$find2;

    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;
  };

  _proto.removeQueries = function removeQueries(arg1, arg2) {
    var _parseFilterArgs2 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2),
        filters = _parseFilterArgs2[0];

    var queryCache = this.queryCache;
    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      queryCache.findAll(filters).forEach(function (query) {
        queryCache.remove(query);
      });
    });
  };

  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {
    var _this3 = this;

    var _parseFilterArgs3 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),
        filters = _parseFilterArgs3[0],
        options = _parseFilterArgs3[1];

    var queryCache = this.queryCache;

    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, filters, {
      active: true
    });

    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      queryCache.findAll(filters).forEach(function (query) {
        query.reset();
      });
      return _this3.refetchQueries(refetchFilters, options);
    });
  };

  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {
    var _this4 = this;

    var _parseFilterArgs4 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),
        filters = _parseFilterArgs4[0],
        _parseFilterArgs4$ = _parseFilterArgs4[1],
        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;

    if (typeof cancelOptions.revert === 'undefined') {
      cancelOptions.revert = true;
    }

    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      return _this4.queryCache.findAll(filters).map(function (query) {
        return query.cancel(cancelOptions);
      });
    });
    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);
  };

  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {
    var _ref3,
        _filters$refetchActiv,
        _filters$refetchInact,
        _this5 = this;

    var _parseFilterArgs5 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),
        filters = _parseFilterArgs5[0],
        options = _parseFilterArgs5[1];

    var refetchFilters = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, filters, {
      // if filters.refetchActive is not provided and filters.active is explicitly false,
      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries
      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,
      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false
    });

    return _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      _this5.queryCache.findAll(filters).forEach(function (query) {
        query.invalidate();
      });

      return _this5.refetchQueries(refetchFilters, options);
    });
  };

  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {
    var _this6 = this;

    var _parseFilterArgs6 = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseFilterArgs)(arg1, arg2, arg3),
        filters = _parseFilterArgs6[0],
        options = _parseFilterArgs6[1];

    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      return _this6.queryCache.findAll(filters).map(function (query) {
        return query.fetch(undefined, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, options, {
          meta: {
            refetchPage: filters == null ? void 0 : filters.refetchPage
          }
        }));
      });
    });
    var promise = Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);

    if (!(options == null ? void 0 : options.throwOnError)) {
      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);
    }

    return promise;
  };

  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {
    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);
    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652

    if (typeof defaultedOptions.retry === 'undefined') {
      defaultedOptions.retry = false;
    }

    var query = this.queryCache.build(this, defaultedOptions);
    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);
  };

  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {
    return this.fetchQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);
  };

  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {
    var parsedOptions = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.parseQueryArgs)(arg1, arg2, arg3);
    parsedOptions.behavior = (0,_infiniteQueryBehavior__WEBPACK_IMPORTED_MODULE_7__.infiniteQueryBehavior)();
    return this.fetchQuery(parsedOptions);
  };

  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {
    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);
  };

  _proto.cancelMutations = function cancelMutations() {
    var _this7 = this;

    var promises = _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      return _this7.mutationCache.getAll().map(function (mutation) {
        return mutation.cancel();
      });
    });
    return Promise.all(promises).then(_utils__WEBPACK_IMPORTED_MODULE_5__.noop).catch(_utils__WEBPACK_IMPORTED_MODULE_5__.noop);
  };

  _proto.resumePausedMutations = function resumePausedMutations() {
    return this.getMutationCache().resumePausedMutations();
  };

  _proto.executeMutation = function executeMutation(options) {
    return this.mutationCache.build(this, options).execute();
  };

  _proto.getQueryCache = function getQueryCache() {
    return this.queryCache;
  };

  _proto.getMutationCache = function getMutationCache() {
    return this.mutationCache;
  };

  _proto.getDefaultOptions = function getDefaultOptions() {
    return this.defaultOptions;
  };

  _proto.setDefaultOptions = function setDefaultOptions(options) {
    this.defaultOptions = options;
  };

  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {
    var result = this.queryDefaults.find(function (x) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(queryKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.queryKey);
    });

    if (result) {
      result.defaultOptions = options;
    } else {
      this.queryDefaults.push({
        queryKey: queryKey,
        defaultOptions: options
      });
    }
  };

  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {
    var _this$queryDefaults$f;

    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(queryKey, x.queryKey);
    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;
  };

  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {
    var result = this.mutationDefaults.find(function (x) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(mutationKey) === (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKey)(x.mutationKey);
    });

    if (result) {
      result.defaultOptions = options;
    } else {
      this.mutationDefaults.push({
        mutationKey: mutationKey,
        defaultOptions: options
      });
    }
  };

  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {
    var _this$mutationDefault;

    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {
      return (0,_utils__WEBPACK_IMPORTED_MODULE_5__.partialMatchKey)(mutationKey, x.mutationKey);
    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;
  };

  _proto.defaultQueryOptions = function defaultQueryOptions(options) {
    if (options == null ? void 0 : options._defaulted) {
      return options;
    }

    var defaultedOptions = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {
      _defaulted: true
    });

    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {
      defaultedOptions.queryHash = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.hashQueryKeyByOptions)(defaultedOptions.queryKey, defaultedOptions);
    }

    return defaultedOptions;
  };

  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {
    return this.defaultQueryOptions(options);
  };

  _proto.defaultMutationOptions = function defaultMutationOptions(options) {
    if (options == null ? void 0 : options._defaulted) {
      return options;
    }

    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {
      _defaulted: true
    });
  };

  _proto.clear = function clear() {
    this.queryCache.clear();
    this.mutationCache.clear();
  };

  return QueryClient;
}();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/inheritsLoose */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/inheritsLoose.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _notifyManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js");
/* harmony import */ var _subscribable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribable */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js");
/* harmony import */ var _retryer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryer */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js");








var QueryObserver = /*#__PURE__*/function (_Subscribable) {
  (0,_babel_runtime_helpers_esm_inheritsLoose__WEBPACK_IMPORTED_MODULE_1__["default"])(QueryObserver, _Subscribable);

  function QueryObserver(client, options) {
    var _this;

    _this = _Subscribable.call(this) || this;
    _this.client = client;
    _this.options = options;
    _this.trackedProps = [];
    _this.selectError = null;

    _this.bindMethods();

    _this.setOptions(options);

    return _this;
  }

  var _proto = QueryObserver.prototype;

  _proto.bindMethods = function bindMethods() {
    this.remove = this.remove.bind(this);
    this.refetch = this.refetch.bind(this);
  };

  _proto.onSubscribe = function onSubscribe() {
    if (this.listeners.length === 1) {
      this.currentQuery.addObserver(this);

      if (shouldFetchOnMount(this.currentQuery, this.options)) {
        this.executeFetch();
      }

      this.updateTimers();
    }
  };

  _proto.onUnsubscribe = function onUnsubscribe() {
    if (!this.listeners.length) {
      this.destroy();
    }
  };

  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {
    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);
  };

  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {
    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);
  };

  _proto.destroy = function destroy() {
    this.listeners = [];
    this.clearTimers();
    this.currentQuery.removeObserver(this);
  };

  _proto.setOptions = function setOptions(options, notifyOptions) {
    var prevOptions = this.options;
    var prevQuery = this.currentQuery;
    this.options = this.client.defaultQueryObserverOptions(options);

    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {
      throw new Error('Expected enabled to be a boolean');
    } // Keep previous query key if the user does not supply one


    if (!this.options.queryKey) {
      this.options.queryKey = prevOptions.queryKey;
    }

    this.updateQuery();
    var mounted = this.hasListeners(); // Fetch if there are subscribers

    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {
      this.executeFetch();
    } // Update result


    this.updateResult(notifyOptions); // Update stale interval if needed

    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {
      this.updateStaleTimeout();
    }

    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed

    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {
      this.updateRefetchInterval(nextRefetchInterval);
    }
  };

  _proto.getOptimisticResult = function getOptimisticResult(options) {
    var defaultedOptions = this.client.defaultQueryObserverOptions(options);
    var query = this.client.getQueryCache().build(this.client, defaultedOptions);
    return this.createResult(query, defaultedOptions);
  };

  _proto.getCurrentResult = function getCurrentResult() {
    return this.currentResult;
  };

  _proto.trackResult = function trackResult(result, defaultedOptions) {
    var _this2 = this;

    var trackedResult = {};

    var trackProp = function trackProp(key) {
      if (!_this2.trackedProps.includes(key)) {
        _this2.trackedProps.push(key);
      }
    };

    Object.keys(result).forEach(function (key) {
      Object.defineProperty(trackedResult, key, {
        configurable: false,
        enumerable: true,
        get: function get() {
          trackProp(key);
          return result[key];
        }
      });
    });

    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {
      trackProp('error');
    }

    return trackedResult;
  };

  _proto.getNextResult = function getNextResult(options) {
    var _this3 = this;

    return new Promise(function (resolve, reject) {
      var unsubscribe = _this3.subscribe(function (result) {
        if (!result.isFetching) {
          unsubscribe();

          if (result.isError && (options == null ? void 0 : options.throwOnError)) {
            reject(result.error);
          } else {
            resolve(result);
          }
        }
      });
    });
  };

  _proto.getCurrentQuery = function getCurrentQuery() {
    return this.currentQuery;
  };

  _proto.remove = function remove() {
    this.client.getQueryCache().remove(this.currentQuery);
  };

  _proto.refetch = function refetch(options) {
    return this.fetch((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, options, {
      meta: {
        refetchPage: options == null ? void 0 : options.refetchPage
      }
    }));
  };

  _proto.fetchOptimistic = function fetchOptimistic(options) {
    var _this4 = this;

    var defaultedOptions = this.client.defaultQueryObserverOptions(options);
    var query = this.client.getQueryCache().build(this.client, defaultedOptions);
    return query.fetch().then(function () {
      return _this4.createResult(query, defaultedOptions);
    });
  };

  _proto.fetch = function fetch(fetchOptions) {
    var _this5 = this;

    return this.executeFetch(fetchOptions).then(function () {
      _this5.updateResult();

      return _this5.currentResult;
    });
  };

  _proto.executeFetch = function executeFetch(fetchOptions) {
    // Make sure we reference the latest query as the current one might have been removed
    this.updateQuery(); // Fetch

    var promise = this.currentQuery.fetch(this.options, fetchOptions);

    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {
      promise = promise.catch(_utils__WEBPACK_IMPORTED_MODULE_2__.noop);
    }

    return promise;
  };

  _proto.updateStaleTimeout = function updateStaleTimeout() {
    var _this6 = this;

    this.clearStaleTimeout();

    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.currentResult.isStale || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.options.staleTime)) {
      return;
    }

    var time = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.
    // To mitigate this issue we always add 1 ms to the timeout.

    var timeout = time + 1;
    this.staleTimeoutId = setTimeout(function () {
      if (!_this6.currentResult.isStale) {
        _this6.updateResult();
      }
    }, timeout);
  };

  _proto.computeRefetchInterval = function computeRefetchInterval() {
    var _this$options$refetch;

    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;
  };

  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {
    var _this7 = this;

    this.clearRefetchInterval();
    this.currentRefetchInterval = nextInterval;

    if (_utils__WEBPACK_IMPORTED_MODULE_2__.isServer || this.options.enabled === false || !(0,_utils__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {
      return;
    }

    this.refetchIntervalId = setInterval(function () {
      if (_this7.options.refetchIntervalInBackground || _focusManager__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {
        _this7.executeFetch();
      }
    }, this.currentRefetchInterval);
  };

  _proto.updateTimers = function updateTimers() {
    this.updateStaleTimeout();
    this.updateRefetchInterval(this.computeRefetchInterval());
  };

  _proto.clearTimers = function clearTimers() {
    this.clearStaleTimeout();
    this.clearRefetchInterval();
  };

  _proto.clearStaleTimeout = function clearStaleTimeout() {
    if (this.staleTimeoutId) {
      clearTimeout(this.staleTimeoutId);
      this.staleTimeoutId = undefined;
    }
  };

  _proto.clearRefetchInterval = function clearRefetchInterval() {
    if (this.refetchIntervalId) {
      clearInterval(this.refetchIntervalId);
      this.refetchIntervalId = undefined;
    }
  };

  _proto.createResult = function createResult(query, options) {
    var prevQuery = this.currentQuery;
    var prevOptions = this.options;
    var prevResult = this.currentResult;
    var prevResultState = this.currentResultState;
    var prevResultOptions = this.currentResultOptions;
    var queryChange = query !== prevQuery;
    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;
    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;
    var state = query.state;
    var dataUpdatedAt = state.dataUpdatedAt,
        error = state.error,
        errorUpdatedAt = state.errorUpdatedAt,
        isFetching = state.isFetching,
        status = state.status;
    var isPreviousData = false;
    var isPlaceholderData = false;
    var data; // Optimistically set result in fetching state if needed

    if (options.optimisticResults) {
      var mounted = this.hasListeners();
      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);
      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);

      if (fetchOnMount || fetchOptionally) {
        isFetching = true;

        if (!dataUpdatedAt) {
          status = 'loading';
        }
      }
    } // Keep previous data if needed


    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {
      data = prevQueryResult.data;
      dataUpdatedAt = prevQueryResult.dataUpdatedAt;
      status = prevQueryResult.status;
      isPreviousData = true;
    } // Select data if needed
    else if (options.select && typeof state.data !== 'undefined') {
        // Memoize select result
        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {
          data = this.selectResult;
        } else {
          try {
            this.selectFn = options.select;
            data = options.select(state.data);

            if (options.structuralSharing !== false) {
              data = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, data);
            }

            this.selectResult = data;
            this.selectError = null;
          } catch (selectError) {
            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);
            this.selectError = selectError;
          }
        }
      } // Use query data
      else {
          data = state.data;
        } // Show placeholder data if needed


    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {
      var placeholderData; // Memoize placeholder data

      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {
        placeholderData = prevResult.data;
      } else {
        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;

        if (options.select && typeof placeholderData !== 'undefined') {
          try {
            placeholderData = options.select(placeholderData);

            if (options.structuralSharing !== false) {
              placeholderData = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.replaceEqualDeep)(prevResult == null ? void 0 : prevResult.data, placeholderData);
            }

            this.selectError = null;
          } catch (selectError) {
            (0,_logger__WEBPACK_IMPORTED_MODULE_4__.getLogger)().error(selectError);
            this.selectError = selectError;
          }
        }
      }

      if (typeof placeholderData !== 'undefined') {
        status = 'success';
        data = placeholderData;
        isPlaceholderData = true;
      }
    }

    if (this.selectError) {
      error = this.selectError;
      data = this.selectResult;
      errorUpdatedAt = Date.now();
      status = 'error';
    }

    var result = {
      status: status,
      isLoading: status === 'loading',
      isSuccess: status === 'success',
      isError: status === 'error',
      isIdle: status === 'idle',
      data: data,
      dataUpdatedAt: dataUpdatedAt,
      error: error,
      errorUpdatedAt: errorUpdatedAt,
      failureCount: state.fetchFailureCount,
      errorUpdateCount: state.errorUpdateCount,
      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,
      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,
      isFetching: isFetching,
      isRefetching: isFetching && status !== 'loading',
      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,
      isPlaceholderData: isPlaceholderData,
      isPreviousData: isPreviousData,
      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,
      isStale: isStale(query, options),
      refetch: this.refetch,
      remove: this.remove
    };
    return result;
  };

  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {
    if (!prevResult) {
      return true;
    }

    var _this$options = this.options,
        notifyOnChangeProps = _this$options.notifyOnChangeProps,
        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;

    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {
      return true;
    }

    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {
      return true;
    }

    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;
    return Object.keys(result).some(function (key) {
      var typedKey = key;
      var changed = result[typedKey] !== prevResult[typedKey];
      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {
        return x === key;
      });
      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {
        return x === key;
      });
      return changed && !isExcluded && (!includedProps || isIncluded);
    });
  };

  _proto.updateResult = function updateResult(notifyOptions) {
    var prevResult = this.currentResult;
    this.currentResult = this.createResult(this.currentQuery, this.options);
    this.currentResultState = this.currentQuery.state;
    this.currentResultOptions = this.options; // Only notify if something has changed

    if ((0,_utils__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.currentResult, prevResult)) {
      return;
    } // Determine which callbacks to trigger


    var defaultNotifyOptions = {
      cache: true
    };

    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {
      defaultNotifyOptions.listeners = true;
    }

    this.notify((0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, defaultNotifyOptions, notifyOptions));
  };

  _proto.updateQuery = function updateQuery() {
    var query = this.client.getQueryCache().build(this.client, this.options);

    if (query === this.currentQuery) {
      return;
    }

    var prevQuery = this.currentQuery;
    this.currentQuery = query;
    this.currentQueryInitialState = query.state;
    this.previousQueryResult = this.currentResult;

    if (this.hasListeners()) {
      prevQuery == null ? void 0 : prevQuery.removeObserver(this);
      query.addObserver(this);
    }
  };

  _proto.onQueryUpdate = function onQueryUpdate(action) {
    var notifyOptions = {};

    if (action.type === 'success') {
      notifyOptions.onSuccess = true;
    } else if (action.type === 'error' && !(0,_retryer__WEBPACK_IMPORTED_MODULE_5__.isCancelledError)(action.error)) {
      notifyOptions.onError = true;
    }

    this.updateResult(notifyOptions);

    if (this.hasListeners()) {
      this.updateTimers();
    }
  };

  _proto.notify = function notify(notifyOptions) {
    var _this8 = this;

    _notifyManager__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batch(function () {
      // First trigger the configuration callbacks
      if (notifyOptions.onSuccess) {
        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);
        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);
      } else if (notifyOptions.onError) {
        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);
        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);
      } // Then trigger the listeners


      if (notifyOptions.listeners) {
        _this8.listeners.forEach(function (listener) {
          listener(_this8.currentResult);
        });
      } // Then the cache listeners


      if (notifyOptions.cache) {
        _this8.client.getQueryCache().notify({
          query: _this8.currentQuery,
          type: 'observerResultsUpdated'
        });
      }
    });
  };

  return QueryObserver;
}(_subscribable__WEBPACK_IMPORTED_MODULE_7__.Subscribable);

function shouldLoadOnMount(query, options) {
  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);
}

function shouldFetchOnMount(query, options) {
  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);
}

function shouldFetchOn(query, options, field) {
  if (options.enabled !== false) {
    var value = typeof field === 'function' ? field(query) : field;
    return value === 'always' || value !== false && isStale(query, options);
  }

  return false;
}

function shouldFetchOptionally(query, prevQuery, options, prevOptions) {
  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);
}

function isStale(query, options) {
  return query.isStaleByTime(options.staleTime);
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/retryer.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CancelledError: () => (/* binding */ CancelledError),
/* harmony export */   Retryer: () => (/* binding */ Retryer),
/* harmony export */   isCancelable: () => (/* binding */ isCancelable),
/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)
/* harmony export */ });
/* harmony import */ var _focusManager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./focusManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/focusManager.js");
/* harmony import */ var _onlineManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./onlineManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/onlineManager.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");




function defaultRetryDelay(failureCount) {
  return Math.min(1000 * Math.pow(2, failureCount), 30000);
}

function isCancelable(value) {
  return typeof (value == null ? void 0 : value.cancel) === 'function';
}
var CancelledError = function CancelledError(options) {
  this.revert = options == null ? void 0 : options.revert;
  this.silent = options == null ? void 0 : options.silent;
};
function isCancelledError(value) {
  return value instanceof CancelledError;
} // CLASS

var Retryer = function Retryer(config) {
  var _this = this;

  var cancelRetry = false;
  var cancelFn;
  var continueFn;
  var promiseResolve;
  var promiseReject;
  this.abort = config.abort;

  this.cancel = function (cancelOptions) {
    return cancelFn == null ? void 0 : cancelFn(cancelOptions);
  };

  this.cancelRetry = function () {
    cancelRetry = true;
  };

  this.continueRetry = function () {
    cancelRetry = false;
  };

  this.continue = function () {
    return continueFn == null ? void 0 : continueFn();
  };

  this.failureCount = 0;
  this.isPaused = false;
  this.isResolved = false;
  this.isTransportCancelable = false;
  this.promise = new Promise(function (outerResolve, outerReject) {
    promiseResolve = outerResolve;
    promiseReject = outerReject;
  });

  var resolve = function resolve(value) {
    if (!_this.isResolved) {
      _this.isResolved = true;
      config.onSuccess == null ? void 0 : config.onSuccess(value);
      continueFn == null ? void 0 : continueFn();
      promiseResolve(value);
    }
  };

  var reject = function reject(value) {
    if (!_this.isResolved) {
      _this.isResolved = true;
      config.onError == null ? void 0 : config.onError(value);
      continueFn == null ? void 0 : continueFn();
      promiseReject(value);
    }
  };

  var pause = function pause() {
    return new Promise(function (continueResolve) {
      continueFn = continueResolve;
      _this.isPaused = true;
      config.onPause == null ? void 0 : config.onPause();
    }).then(function () {
      continueFn = undefined;
      _this.isPaused = false;
      config.onContinue == null ? void 0 : config.onContinue();
    });
  }; // Create loop function


  var run = function run() {
    // Do nothing if already resolved
    if (_this.isResolved) {
      return;
    }

    var promiseOrValue; // Execute query

    try {
      promiseOrValue = config.fn();
    } catch (error) {
      promiseOrValue = Promise.reject(error);
    } // Create callback to cancel this fetch


    cancelFn = function cancelFn(cancelOptions) {
      if (!_this.isResolved) {
        reject(new CancelledError(cancelOptions));
        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported

        if (isCancelable(promiseOrValue)) {
          try {
            promiseOrValue.cancel();
          } catch (_unused) {}
        }
      }
    }; // Check if the transport layer support cancellation


    _this.isTransportCancelable = isCancelable(promiseOrValue);
    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {
      var _config$retry, _config$retryDelay;

      // Stop if the fetch is already resolved
      if (_this.isResolved) {
        return;
      } // Do we need to retry the request?


      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;
      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;
      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;
      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);

      if (cancelRetry || !shouldRetry) {
        // We are done if the query does not need to be retried
        reject(error);
        return;
      }

      _this.failureCount++; // Notify on fail

      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay

      (0,_utils__WEBPACK_IMPORTED_MODULE_0__.sleep)(delay) // Pause if the document is not visible or when the device is offline
      .then(function () {
        if (!_focusManager__WEBPACK_IMPORTED_MODULE_1__.focusManager.isFocused() || !_onlineManager__WEBPACK_IMPORTED_MODULE_2__.onlineManager.isOnline()) {
          return pause();
        }
      }).then(function () {
        if (cancelRetry) {
          reject(error);
        } else {
          run();
        }
      });
    });
  }; // Start loop


  run();
};

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/subscribable.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Subscribable: () => (/* binding */ Subscribable)
/* harmony export */ });
var Subscribable = /*#__PURE__*/function () {
  function Subscribable() {
    this.listeners = [];
  }

  var _proto = Subscribable.prototype;

  _proto.subscribe = function subscribe(listener) {
    var _this = this;

    var callback = listener || function () {
      return undefined;
    };

    this.listeners.push(callback);
    this.onSubscribe();
    return function () {
      _this.listeners = _this.listeners.filter(function (x) {
        return x !== callback;
      });

      _this.onUnsubscribe();
    };
  };

  _proto.hasListeners = function hasListeners() {
    return this.listeners.length > 0;
  };

  _proto.onSubscribe = function onSubscribe() {// Do nothing
  };

  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing
  };

  return Subscribable;
}();

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/types.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/types.js ***!
  \***********************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   difference: () => (/* binding */ difference),
/* harmony export */   ensureQueryKeyArray: () => (/* binding */ ensureQueryKeyArray),
/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),
/* harmony export */   getAbortController: () => (/* binding */ getAbortController),
/* harmony export */   hashQueryKey: () => (/* binding */ hashQueryKey),
/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),
/* harmony export */   isError: () => (/* binding */ isError),
/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),
/* harmony export */   isQueryKey: () => (/* binding */ isQueryKey),
/* harmony export */   isServer: () => (/* binding */ isServer),
/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),
/* harmony export */   mapQueryStatusFilter: () => (/* binding */ mapQueryStatusFilter),
/* harmony export */   matchMutation: () => (/* binding */ matchMutation),
/* harmony export */   matchQuery: () => (/* binding */ matchQuery),
/* harmony export */   noop: () => (/* binding */ noop),
/* harmony export */   parseFilterArgs: () => (/* binding */ parseFilterArgs),
/* harmony export */   parseMutationArgs: () => (/* binding */ parseMutationArgs),
/* harmony export */   parseMutationFilterArgs: () => (/* binding */ parseMutationFilterArgs),
/* harmony export */   parseQueryArgs: () => (/* binding */ parseQueryArgs),
/* harmony export */   partialDeepEqual: () => (/* binding */ partialDeepEqual),
/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),
/* harmony export */   replaceAt: () => (/* binding */ replaceAt),
/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),
/* harmony export */   scheduleMicrotask: () => (/* binding */ scheduleMicrotask),
/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),
/* harmony export */   sleep: () => (/* binding */ sleep),
/* harmony export */   stableValueHash: () => (/* binding */ stableValueHash),
/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");

// TYPES
// UTILS
var isServer = typeof window === 'undefined';
function noop() {
  return undefined;
}
function functionalUpdate(updater, input) {
  return typeof updater === 'function' ? updater(input) : updater;
}
function isValidTimeout(value) {
  return typeof value === 'number' && value >= 0 && value !== Infinity;
}
function ensureQueryKeyArray(value) {
  return Array.isArray(value) ? value : [value];
}
function difference(array1, array2) {
  return array1.filter(function (x) {
    return array2.indexOf(x) === -1;
  });
}
function replaceAt(array, index, value) {
  var copy = array.slice(0);
  copy[index] = value;
  return copy;
}
function timeUntilStale(updatedAt, staleTime) {
  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);
}
function parseQueryArgs(arg1, arg2, arg3) {
  if (!isQueryKey(arg1)) {
    return arg1;
  }

  if (typeof arg2 === 'function') {
    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg3, {
      queryKey: arg1,
      queryFn: arg2
    });
  }

  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg2, {
    queryKey: arg1
  });
}
function parseMutationArgs(arg1, arg2, arg3) {
  if (isQueryKey(arg1)) {
    if (typeof arg2 === 'function') {
      return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg3, {
        mutationKey: arg1,
        mutationFn: arg2
      });
    }

    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg2, {
      mutationKey: arg1
    });
  }

  if (typeof arg1 === 'function') {
    return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg2, {
      mutationFn: arg1
    });
  }

  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg1);
}
function parseFilterArgs(arg1, arg2, arg3) {
  return isQueryKey(arg1) ? [(0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg2, {
    queryKey: arg1
  }), arg3] : [arg1 || {}, arg2];
}
function parseMutationFilterArgs(arg1, arg2) {
  return isQueryKey(arg1) ? (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, arg2, {
    mutationKey: arg1
  }) : arg1;
}
function mapQueryStatusFilter(active, inactive) {
  if (active === true && inactive === true || active == null && inactive == null) {
    return 'all';
  } else if (active === false && inactive === false) {
    return 'none';
  } else {
    // At this point, active|inactive can only be true|false or false|true
    // so, when only one value is provided, the missing one has to be the negated value
    var isActive = active != null ? active : !inactive;
    return isActive ? 'active' : 'inactive';
  }
}
function matchQuery(filters, query) {
  var active = filters.active,
      exact = filters.exact,
      fetching = filters.fetching,
      inactive = filters.inactive,
      predicate = filters.predicate,
      queryKey = filters.queryKey,
      stale = filters.stale;

  if (isQueryKey(queryKey)) {
    if (exact) {
      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {
        return false;
      }
    } else if (!partialMatchKey(query.queryKey, queryKey)) {
      return false;
    }
  }

  var queryStatusFilter = mapQueryStatusFilter(active, inactive);

  if (queryStatusFilter === 'none') {
    return false;
  } else if (queryStatusFilter !== 'all') {
    var isActive = query.isActive();

    if (queryStatusFilter === 'active' && !isActive) {
      return false;
    }

    if (queryStatusFilter === 'inactive' && isActive) {
      return false;
    }
  }

  if (typeof stale === 'boolean' && query.isStale() !== stale) {
    return false;
  }

  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {
    return false;
  }

  if (predicate && !predicate(query)) {
    return false;
  }

  return true;
}
function matchMutation(filters, mutation) {
  var exact = filters.exact,
      fetching = filters.fetching,
      predicate = filters.predicate,
      mutationKey = filters.mutationKey;

  if (isQueryKey(mutationKey)) {
    if (!mutation.options.mutationKey) {
      return false;
    }

    if (exact) {
      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {
        return false;
      }
    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {
      return false;
    }
  }

  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {
    return false;
  }

  if (predicate && !predicate(mutation)) {
    return false;
  }

  return true;
}
function hashQueryKeyByOptions(queryKey, options) {
  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;
  return hashFn(queryKey);
}
/**
 * Default query keys hash function.
 */

function hashQueryKey(queryKey) {
  var asArray = ensureQueryKeyArray(queryKey);
  return stableValueHash(asArray);
}
/**
 * Hashes the value into a stable hash.
 */

function stableValueHash(value) {
  return JSON.stringify(value, function (_, val) {
    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {
      result[key] = val[key];
      return result;
    }, {}) : val;
  });
}
/**
 * Checks if key `b` partially matches with key `a`.
 */

function partialMatchKey(a, b) {
  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));
}
/**
 * Checks if `b` partially matches with `a`.
 */

function partialDeepEqual(a, b) {
  if (a === b) {
    return true;
  }

  if (typeof a !== typeof b) {
    return false;
  }

  if (a && b && typeof a === 'object' && typeof b === 'object') {
    return !Object.keys(b).some(function (key) {
      return !partialDeepEqual(a[key], b[key]);
    });
  }

  return false;
}
/**
 * This function returns `a` if `b` is deeply equal.
 * If not, it will replace any deeply equal children of `b` with those of `a`.
 * This can be used for structural sharing between JSON values for example.
 */

function replaceEqualDeep(a, b) {
  if (a === b) {
    return a;
  }

  var array = Array.isArray(a) && Array.isArray(b);

  if (array || isPlainObject(a) && isPlainObject(b)) {
    var aSize = array ? a.length : Object.keys(a).length;
    var bItems = array ? b : Object.keys(b);
    var bSize = bItems.length;
    var copy = array ? [] : {};
    var equalItems = 0;

    for (var i = 0; i < bSize; i++) {
      var key = array ? i : bItems[i];
      copy[key] = replaceEqualDeep(a[key], b[key]);

      if (copy[key] === a[key]) {
        equalItems++;
      }
    }

    return aSize === bSize && equalItems === aSize ? a : copy;
  }

  return b;
}
/**
 * Shallow compare objects. Only works with objects that always have the same properties.
 */

function shallowEqualObjects(a, b) {
  if (a && !b || b && !a) {
    return false;
  }

  for (var key in a) {
    if (a[key] !== b[key]) {
      return false;
    }
  }

  return true;
} // Copied from: https://github.com/jonschlinkert/is-plain-object

function isPlainObject(o) {
  if (!hasObjectPrototype(o)) {
    return false;
  } // If has modified constructor


  var ctor = o.constructor;

  if (typeof ctor === 'undefined') {
    return true;
  } // If has modified prototype


  var prot = ctor.prototype;

  if (!hasObjectPrototype(prot)) {
    return false;
  } // If constructor does not have an Object-specific method


  if (!prot.hasOwnProperty('isPrototypeOf')) {
    return false;
  } // Most likely a plain Object


  return true;
}

function hasObjectPrototype(o) {
  return Object.prototype.toString.call(o) === '[object Object]';
}

function isQueryKey(value) {
  return typeof value === 'string' || Array.isArray(value);
}
function isError(value) {
  return value instanceof Error;
}
function sleep(timeout) {
  return new Promise(function (resolve) {
    setTimeout(resolve, timeout);
  });
}
/**
 * Schedules a microtask.
 * This can be useful to schedule state updates after rendering.
 */

function scheduleMicrotask(callback) {
  Promise.resolve().then(callback).catch(function (error) {
    return setTimeout(function () {
      throw error;
    });
  });
}
function getAbortController() {
  if (typeof AbortController === 'function') {
    return new AbortController();
  }
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/index.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _core__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _core__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);
/* harmony import */ var _react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./react */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/index.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _react__WEBPACK_IMPORTED_MODULE_1__) if(["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _react__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);



/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/Hydrate.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/Hydrate.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hydrate: () => (/* binding */ Hydrate),
/* harmony export */   useHydrate: () => (/* binding */ useHydrate)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/hydration.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");



function useHydrate(state, options) {
  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();
  var optionsRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(options);
  optionsRef.current = options; // Running hydrate again with the same queries is safe,
  // it wont overwrite or initialize existing queries,
  // relying on useMemo here is only a performance optimization.
  // hydrate can and should be run *during* render here for SSR to work properly

  react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {
    if (state) {
      (0,_core__WEBPACK_IMPORTED_MODULE_2__.hydrate)(queryClient, state, optionsRef.current);
    }
  }, [queryClient, state]);
}
var Hydrate = function Hydrate(_ref) {
  var children = _ref.children,
      options = _ref.options,
      state = _ref.state;
  useHydrate(state, options);
  return children;
};

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),
/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var defaultContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(undefined);
var QueryClientSharingContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(false); // if contextSharing is on, we share the first and at least one
// instance of the context across the window
// to ensure that if React Query is used across
// different bundles or microfrontends they will
// all use the same **instance** of context, regardless
// of module scoping.

function getQueryClientContext(contextSharing) {
  if (contextSharing && typeof window !== 'undefined') {
    if (!window.ReactQueryClientContext) {
      window.ReactQueryClientContext = defaultContext;
    }

    return window.ReactQueryClientContext;
  }

  return defaultContext;
}

var useQueryClient = function useQueryClient() {
  var queryClient = react__WEBPACK_IMPORTED_MODULE_0___default().useContext(getQueryClientContext(react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryClientSharingContext)));

  if (!queryClient) {
    throw new Error('No QueryClient set, use QueryClientProvider to set one');
  }

  return queryClient;
};
var QueryClientProvider = function QueryClientProvider(_ref) {
  var client = _ref.client,
      _ref$contextSharing = _ref.contextSharing,
      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,
      children = _ref.children;
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    client.mount();
    return function () {
      client.unmount();
    };
  }, [client]);
  var Context = getQueryClientContext(contextSharing);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryClientSharingContext.Provider, {
    value: contextSharing
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {
    value: client
  }, children));
};

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryErrorResetBoundary.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryErrorResetBoundary.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),
/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
 // CONTEXT

function createValue() {
  var _isReset = false;
  return {
    clearReset: function clearReset() {
      _isReset = false;
    },
    reset: function reset() {
      _isReset = true;
    },
    isReset: function isReset() {
      return _isReset;
    }
  };
}

var QueryErrorResetBoundaryContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(createValue()); // HOOK

var useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {
  return react__WEBPACK_IMPORTED_MODULE_0___default().useContext(QueryErrorResetBoundaryContext);
}; // COMPONENT

var QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {
  var children = _ref.children;
  var value = react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(function () {
    return createValue();
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(QueryErrorResetBoundaryContext.Provider, {
    value: value
  }, typeof children === 'function' ? children(value) : children);
};

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.Hydrate),
/* harmony export */   QueryClientProvider: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider),
/* harmony export */   QueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.QueryErrorResetBoundary),
/* harmony export */   useHydrate: () => (/* reexport safe */ _Hydrate__WEBPACK_IMPORTED_MODULE_10__.useHydrate),
/* harmony export */   useInfiniteQuery: () => (/* reexport safe */ _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__.useInfiniteQuery),
/* harmony export */   useIsFetching: () => (/* reexport safe */ _useIsFetching__WEBPACK_IMPORTED_MODULE_4__.useIsFetching),
/* harmony export */   useIsMutating: () => (/* reexport safe */ _useIsMutating__WEBPACK_IMPORTED_MODULE_5__.useIsMutating),
/* harmony export */   useMutation: () => (/* reexport safe */ _useMutation__WEBPACK_IMPORTED_MODULE_6__.useMutation),
/* harmony export */   useQueries: () => (/* reexport safe */ _useQueries__WEBPACK_IMPORTED_MODULE_8__.useQueries),
/* harmony export */   useQuery: () => (/* reexport safe */ _useQuery__WEBPACK_IMPORTED_MODULE_7__.useQuery),
/* harmony export */   useQueryClient: () => (/* reexport safe */ _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient),
/* harmony export */   useQueryErrorResetBoundary: () => (/* reexport safe */ _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)
/* harmony export */ });
/* harmony import */ var _setBatchUpdatesFn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./setBatchUpdatesFn */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setBatchUpdatesFn.js");
/* harmony import */ var _setLogger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./setLogger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setLogger.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");
/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryErrorResetBoundary.js");
/* harmony import */ var _useIsFetching__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useIsFetching */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsFetching.js");
/* harmony import */ var _useIsMutating__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./useIsMutating */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsMutating.js");
/* harmony import */ var _useMutation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./useMutation */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useMutation.js");
/* harmony import */ var _useQuery__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useQuery */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQuery.js");
/* harmony import */ var _useQueries__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useQueries */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQueries.js");
/* harmony import */ var _useInfiniteQuery__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./useInfiniteQuery */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useInfiniteQuery.js");
/* harmony import */ var _Hydrate__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Hydrate */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/Hydrate.js");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./types */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/types.js");
/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_11__);
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_11__) if(["default","QueryClientProvider","useQueryClient","QueryErrorResetBoundary","useQueryErrorResetBoundary","useIsFetching","useIsMutating","useMutation","useQuery","useQueries","useInfiniteQuery","useHydrate","Hydrate"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_11__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);
// Side effects










 // Types



/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/logger.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/logger.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   logger: () => (/* binding */ logger)
/* harmony export */ });
var logger = console;

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/reactBatchedUpdates.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/reactBatchedUpdates.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   unstable_batchedUpdates: () => (/* binding */ unstable_batchedUpdates)
/* harmony export */ });
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ "react-dom");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);

var unstable_batchedUpdates = (react_dom__WEBPACK_IMPORTED_MODULE_0___default().unstable_batchedUpdates);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setBatchUpdatesFn.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setBatchUpdatesFn.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reactBatchedUpdates */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/reactBatchedUpdates.js");


_core__WEBPACK_IMPORTED_MODULE_0__.notifyManager.setBatchNotifyFunction(_reactBatchedUpdates__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setLogger.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/setLogger.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/logger.js");
/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/logger.js");


(0,_core__WEBPACK_IMPORTED_MODULE_0__.setLogger)(_logger__WEBPACK_IMPORTED_MODULE_1__.logger);

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/types.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/types.js ***!
  \************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useBaseQuery.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useBaseQuery.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryErrorResetBoundary */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryErrorResetBoundary.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/utils.js");





function useBaseQuery(options, Observer) {
  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),
      forceUpdate = _React$useState[1];

  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();
  var errorResetBoundary = (0,_QueryErrorResetBoundary__WEBPACK_IMPORTED_MODULE_2__.useQueryErrorResetBoundary)();
  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options

  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders

  if (defaultedOptions.onError) {
    defaultedOptions.onError = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onError);
  }

  if (defaultedOptions.onSuccess) {
    defaultedOptions.onSuccess = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSuccess);
  }

  if (defaultedOptions.onSettled) {
    defaultedOptions.onSettled = _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(defaultedOptions.onSettled);
  }

  if (defaultedOptions.suspense) {
    // Always set stale time when using suspense to prevent
    // fetching again when directly mounting after suspending
    if (typeof defaultedOptions.staleTime !== 'number') {
      defaultedOptions.staleTime = 1000;
    } // Set cache time to 1 if the option has been set to 0
    // when using suspense to prevent infinite loop of fetches


    if (defaultedOptions.cacheTime === 0) {
      defaultedOptions.cacheTime = 1;
    }
  }

  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {
    // Prevent retrying failed query if the error boundary has not been reset yet
    if (!errorResetBoundary.isReset()) {
      defaultedOptions.retryOnMount = false;
    }
  }

  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {
    return new Observer(queryClient, defaultedOptions);
  }),
      observer = _React$useState2[0];

  var result = observer.getOptimisticResult(defaultedOptions);
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    mountedRef.current = true;
    errorResetBoundary.clearReset();
    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {
      if (mountedRef.current) {
        forceUpdate(function (x) {
          return x + 1;
        });
      }
    })); // Update result to make sure we did not miss any query updates
    // between creating the observer and subscribing to it.

    observer.updateResult();
    return function () {
      mountedRef.current = false;
      unsubscribe();
    };
  }, [errorResetBoundary, observer]);
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    // Do not notify on updates because of changes in the options because
    // these changes should already be reflected in the optimistic result.
    observer.setOptions(defaultedOptions, {
      listeners: false
    });
  }, [defaultedOptions, observer]); // Handle suspense

  if (defaultedOptions.suspense && result.isLoading) {
    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {
      var data = _ref.data;
      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);
      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);
    }).catch(function (error) {
      errorResetBoundary.clearReset();
      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);
      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);
    });
  } // Handle error boundary


  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && (0,_utils__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {
    throw result.error;
  } // Handle result property usage tracking


  if (defaultedOptions.notifyOnChangeProps === 'tracked') {
    result = observer.trackResult(result, defaultedOptions);
  }

  return result;
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useInfiniteQuery.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useInfiniteQuery.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)
/* harmony export */ });
/* harmony import */ var _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/infiniteQueryObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/infiniteQueryObserver.js");
/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useBaseQuery.js");


 // HOOK

function useInfiniteQuery(arg1, arg2, arg3) {
  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);
  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(options, _core_infiniteQueryObserver__WEBPACK_IMPORTED_MODULE_2__.InfiniteQueryObserver);
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsFetching.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsFetching.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useIsFetching: () => (/* binding */ useIsFetching)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");





var checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {
  var newIsFetching = queryClient.isFetching(filters);

  if (isFetching !== newIsFetching) {
    setIsFetching(newIsFetching);
  }
};

function useIsFetching(arg1, arg2) {
  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);
  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();

  var _parseFilterArgs = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseFilterArgs)(arg1, arg2),
      filters = _parseFilterArgs[0];

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isFetching(filters)),
      isFetching = _React$useState[0],
      setIsFetching = _React$useState[1];

  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);
  filtersRef.current = filters;
  var isFetchingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isFetching);
  isFetchingRef.current = isFetching;
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    mountedRef.current = true;
    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);
    var unsubscribe = queryClient.getQueryCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {
      if (mountedRef.current) {
        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);
      }
    }));
    return function () {
      mountedRef.current = false;
      unsubscribe();
    };
  }, [queryClient]);
  return isFetching;
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsMutating.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useIsMutating.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useIsMutating: () => (/* binding */ useIsMutating)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core/utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");




function useIsMutating(arg1, arg2) {
  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);
  var filters = (0,_core_utils__WEBPACK_IMPORTED_MODULE_1__.parseMutationFilterArgs)(arg1, arg2);
  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(queryClient.isMutating(filters)),
      isMutating = _React$useState[0],
      setIsMutating = _React$useState[1];

  var filtersRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(filters);
  filtersRef.current = filters;
  var isMutatingRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(isMutating);
  isMutatingRef.current = isMutating;
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    mountedRef.current = true;
    var unsubscribe = queryClient.getMutationCache().subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {
      if (mountedRef.current) {
        var newIsMutating = queryClient.isMutating(filtersRef.current);

        if (isMutatingRef.current !== newIsMutating) {
          setIsMutating(newIsMutating);
        }
      }
    }));
    return function () {
      mountedRef.current = false;
      unsubscribe();
    };
  }, [queryClient]);
  return isMutating;
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useMutation.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useMutation.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useMutation: () => (/* binding */ useMutation)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ "./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../core/notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../core/mutationObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/mutationObserver.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/utils.js");






 // HOOK

function useMutation(arg1, arg2, arg3) {
  var mountedRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(false);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_1___default().useState(0),
      forceUpdate = _React$useState[1];

  var options = (0,_core_utils__WEBPACK_IMPORTED_MODULE_2__.parseMutationArgs)(arg1, arg2, arg3);
  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();
  var obsRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef();

  if (!obsRef.current) {
    obsRef.current = new _core_mutationObserver__WEBPACK_IMPORTED_MODULE_4__.MutationObserver(queryClient, options);
  } else {
    obsRef.current.setOptions(options);
  }

  var currentResult = obsRef.current.getCurrentResult();
  react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(function () {
    mountedRef.current = true;
    var unsubscribe = obsRef.current.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batchCalls(function () {
      if (mountedRef.current) {
        forceUpdate(function (x) {
          return x + 1;
        });
      }
    }));
    return function () {
      mountedRef.current = false;
      unsubscribe();
    };
  }, []);
  var mutate = react__WEBPACK_IMPORTED_MODULE_1___default().useCallback(function (variables, mutateOptions) {
    obsRef.current.mutate(variables, mutateOptions).catch(_core_utils__WEBPACK_IMPORTED_MODULE_2__.noop);
  }, []);

  if (currentResult.error && (0,_utils__WEBPACK_IMPORTED_MODULE_6__.shouldThrowError)(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {
    throw currentResult.error;
  }

  return (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, currentResult, {
    mutate: mutate,
    mutateAsync: currentResult.mutate
  });
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQueries.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQueries.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useQueries: () => (/* binding */ useQueries)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _core_notifyManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../core/notifyManager */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/notifyManager.js");
/* harmony import */ var _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core/queriesObserver */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queriesObserver.js");
/* harmony import */ var _QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/QueryClientProvider.js");




function useQueries(queries) {
  var mountedRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(false);

  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0___default().useState(0),
      forceUpdate = _React$useState[1];

  var queryClient = (0,_QueryClientProvider__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)();
  var defaultedQueries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {
    return queries.map(function (options) {
      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options

      defaultedOptions.optimisticResults = true;
      return defaultedOptions;
    });
  }, [queries, queryClient]);

  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0___default().useState(function () {
    return new _core_queriesObserver__WEBPACK_IMPORTED_MODULE_2__.QueriesObserver(queryClient, defaultedQueries);
  }),
      observer = _React$useState2[0];

  var result = observer.getOptimisticResult(defaultedQueries);
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    mountedRef.current = true;
    var unsubscribe = observer.subscribe(_core_notifyManager__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(function () {
      if (mountedRef.current) {
        forceUpdate(function (x) {
          return x + 1;
        });
      }
    }));
    return function () {
      mountedRef.current = false;
      unsubscribe();
    };
  }, [observer]);
  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(function () {
    // Do not notify on updates because of changes in the options because
    // these changes should already be reflected in the optimistic result.
    observer.setQueries(defaultedQueries, {
      listeners: false
    });
  }, [defaultedQueries, observer]);
  return result;
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQuery.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useQuery.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   useQuery: () => (/* binding */ useQuery)
/* harmony export */ });
/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../core */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/queryObserver.js");
/* harmony import */ var _core_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/utils */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/core/utils.js");
/* harmony import */ var _useBaseQuery__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useBaseQuery */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/useBaseQuery.js");


 // HOOK

function useQuery(arg1, arg2, arg3) {
  var parsedOptions = (0,_core_utils__WEBPACK_IMPORTED_MODULE_0__.parseQueryArgs)(arg1, arg2, arg3);
  return (0,_useBaseQuery__WEBPACK_IMPORTED_MODULE_1__.useBaseQuery)(parsedOptions, _core__WEBPACK_IMPORTED_MODULE_2__.QueryObserver);
}

/***/ }),

/***/ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/utils.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/react/utils.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)
/* harmony export */ });
function shouldThrowError(suspense, _useErrorBoundary, params) {
  // Allow useErrorBoundary function to override throwing behavior on a per-error basis
  if (typeof _useErrorBoundary === 'function') {
    return _useErrorBoundary.apply(void 0, params);
  } // Allow useErrorBoundary to override suspense's throwing behavior


  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors

  return !!suspense;
}

/***/ }),

/***/ "@wordpress/api-fetch":
/*!**********************************!*\
  !*** external ["wp","apiFetch"] ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["apiFetch"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/hooks":
/*!*******************************!*\
  !*** external ["wp","hooks"] ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["hooks"];

/***/ }),

/***/ "@wordpress/html-entities":
/*!**************************************!*\
  !*** external ["wp","htmlEntities"] ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["htmlEntities"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["i18n"];

/***/ }),

/***/ "@wordpress/url":
/*!*****************************!*\
  !*** external ["wp","url"] ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = window["wp"]["url"];

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = window["React"];

/***/ }),

/***/ "react-dom":
/*!***************************!*\
  !*** external "ReactDOM" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = window["ReactDOM"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/publicPath */
/******/ 	(() => {
/******/ 		var scriptUrl;
/******/ 		if (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + "";
/******/ 		var document = __webpack_require__.g.document;
/******/ 		if (!scriptUrl && document) {
/******/ 			if (document.currentScript && document.currentScript.tagName.toUpperCase() === 'SCRIPT')
/******/ 				scriptUrl = document.currentScript.src;
/******/ 			if (!scriptUrl) {
/******/ 				var scripts = document.getElementsByTagName("script");
/******/ 				if(scripts.length) {
/******/ 					var i = scripts.length - 1;
/******/ 					while (i > -1 && (!scriptUrl || !/^http(s?):/.test(scriptUrl))) scriptUrl = scripts[i--].src;
/******/ 				}
/******/ 			}
/******/ 		}
/******/ 		// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration
/******/ 		// or pass an empty string ("") and set the __webpack_public_path__ variable from your code to use your own logic.
/******/ 		if (!scriptUrl) throw new Error("Automatic publicPath is not supported in this browser");
/******/ 		scriptUrl = scriptUrl.replace(/^blob:/, "").replace(/#.*$/, "").replace(/\?.*$/, "").replace(/\/[^\/]+$/, "/");
/******/ 		__webpack_require__.p = scriptUrl + "../";
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be in strict mode.
(() => {
"use strict";
/*!**************************!*\
  !*** ./InstantAnswer.js ***!
  \**************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _components_Launcher_Launcher__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Launcher/Launcher */ "./components/Launcher/Launcher.js");
/* harmony import */ var _components_Launcher_Preview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Launcher/Preview */ "./components/Launcher/Preview.js");
/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ "./node_modules/.pnpm/react-query@3.39.3_react-dom@18.3.1_react@18.3.1/node_modules/react-query/es/index.js");
/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-dom/client */ "./node_modules/.pnpm/react-dom@18.3.1_react@18.3.1/node_modules/react-dom/client.js");






const container = document.getElementById("betterdocs-ia");
const root = (0,react_dom_client__WEBPACK_IMPORTED_MODULE_5__.createRoot)(container);
const BetterDocsIA = () => {
  const [toggleState, setToggleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const queryClient = new react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClient();
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    let eventFn = event => {
      if (event?.key == 'Escape') {
        if (toggleState) {
          setToggleState(false);
        }
      }
    };
    document.addEventListener('keydown', eventFn, false);
    return () => {
      document.removeEventListener('keydown', eventFn, false);
    };
  }, [toggleState]);
  return (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {
    client: queryClient
  }, (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "betterdocs-ia-root"
  }, toggleState && (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Launcher_Preview__WEBPACK_IMPORTED_MODULE_3__["default"], null), (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(_components_Launcher_Launcher__WEBPACK_IMPORTED_MODULE_2__["default"], {
    toggleState: toggleState,
    setToggleState: setToggleState
  })));
};
root.render((0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createElement)(BetterDocsIA, null));
})();

/******/ })()
;
//# sourceMappingURL=instant-answer.js.map