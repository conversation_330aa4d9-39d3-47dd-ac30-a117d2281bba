(()=>{var e={100:(e,t,n)=>{"use strict";n.d(t,{dd:()=>o,eJ:()=>c,wm:()=>u});var r=n(814),a=n(543),i=n(932);function l(e){return Math.min(1e3*Math.pow(2,e),3e4)}function o(e){return"function"==typeof(null==e?void 0:e.cancel)}var s=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function u(e){return e instanceof s}var c=function(e){var t,n,u,c,d=this,f=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){f=!0},this.continueRetry=function(){f=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(e,t){u=e,c=t}));var h=function(t){d.isResolved||(d.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==n||n(),u(t))},p=function(t){d.isResolved||(d.isResolved=!0,null==e.onError||e.onError(t),null==n||n(),c(t))};!function u(){if(!d.isResolved){var c;try{c=e.fn()}catch(e){c=Promise.reject(e)}t=function(e){if(!d.isResolved&&(p(new s(e)),null==d.abort||d.abort(),o(c)))try{c.cancel()}catch(e){}},d.isTransportCancelable=o(c),Promise.resolve(c).then(h).catch((function(t){var o,s;if(!d.isResolved){var c=null!=(o=e.retry)?o:3,h=null!=(s=e.retryDelay)?s:l,m="function"==typeof h?h(d.failureCount,t):h,g=!0===c||"number"==typeof c&&d.failureCount<c||"function"==typeof c&&c(d.failureCount,t);!f&&g?(d.failureCount++,null==e.onFail||e.onFail(d.failureCount,t),(0,i.yy)(m).then((function(){if(!r.m.isFocused()||!a.t.isOnline())return new Promise((function(t){n=t,d.isPaused=!0,null==e.onPause||e.onPause()})).then((function(){n=void 0,d.isPaused=!1,null==e.onContinue||e.onContinue()}))})).then((function(){f?p(t):u()}))):p(t)}}))}}()}},143:(e,t,n)=>{"use strict";!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(481)},185:(e,t,n)=>{"use strict";n.d(t,{j:()=>a});var r=n(932),a=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):(0,r.G6)((function(){t.notifyFn(e)}))},t.batchCalls=function(e){var t=this;return function(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];t.schedule((function(){e.apply(void 0,r)}))}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&(0,r.G6)((function(){e.batchNotifyFn((function(){t.forEach((function(t){e.notifyFn(t)}))}))}))},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}())},295:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r(e,t)}n.d(t,{A:()=>a})},329:(e,t,n)=>{"use strict";n.d(t,{B:()=>i,t:()=>a});var r=console;function a(){return r}function i(e){r=e}},352:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,n=e||function(){};return this.listeners.push(n),this.onSubscribe(),function(){t.listeners=t.listeners.filter((function(e){return e!==n})),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}()},425:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},481:(e,t,n)=>{"use strict";var r=n(758),a=n(896);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,o={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(o[e]=t,e=0;e<t.length;e++)l.add(t[e])}var c=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,a,i,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),E=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),T=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),z=Symbol.for("react.suspense"),A=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),O=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var M=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var P=Symbol.iterator;function L(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=P&&e[P]||e["@@iterator"])?e:null}var D,I=Object.assign;function F(e){if(void 0===D)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var q=!1;function U(e,t){if(!e||q)return"";q=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(e){var r=e}Reflect.construct(e,[],t)}else{try{t.call()}catch(e){r=e}e.call(t.prototype)}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),i=r.stack.split("\n"),l=a.length-1,o=i.length-1;1<=l&&0<=o&&a[l]!==i[o];)o--;for(;1<=l&&0<=o;l--,o--)if(a[l]!==i[o]){if(1!==l||1!==o)do{if(l--,0>--o||a[l]!==i[o]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=l&&0<=o);break}}}finally{q=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function H(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return U(e.type,!1);case 11:return U(e.type.render,!1);case 1:return U(e.type,!0);default:return""}}function $(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case C:return"Fragment";case E:return"Portal";case S:return"Profiler";case x:return"StrictMode";case z:return"Suspense";case A:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case N:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:$(e.type)||"Memo";case O:t=e._payload,e=e._init;try{return $(e(t))}catch(e){}}return null}function j(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $(t);case 8:return t===x?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function Q(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function B(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function V(e){e._valueTracker||(e._valueTracker=function(e){var t=B(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Z(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=B(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function W(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=Q(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Y(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function X(e,t){Y(e,t);var n=Q(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,Q(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&W(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Q(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Q(n)}}function ie(e,t){var n=Q(t.value),r=Q(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(he).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]}))}));var ve=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(i(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ee=null,Ce=null,xe=null;function Se(e){if(e=wa(e)){if("function"!=typeof Ee)throw Error(i(280));var t=e.stateNode;t&&(t=Ea(t),Ee(e.stateNode,e.type,t))}}function _e(e){Ce?xe?xe.push(e):xe=[e]:Ce=e}function Te(){if(Ce){var e=Ce,t=xe;if(xe=Ce=null,Se(e),t)for(e=0;e<t.length;e++)Se(t[e])}}function Ne(e,t){return e(t)}function ze(){}var Ae=!1;function Re(e,t,n){if(Ae)return e(t,n);Ae=!0;try{return Ne(e,t,n)}finally{Ae=!1,(null!==Ce||null!==xe)&&(ze(),Te())}}function Oe(e,t){var n=e.stateNode;if(null===n)return null;var r=Ea(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(i(231,t,typeof n));return n}var Me=!1;if(c)try{var Pe={};Object.defineProperty(Pe,"passive",{get:function(){Me=!0}}),window.addEventListener("test",Pe,Pe),window.removeEventListener("test",Pe,Pe)}catch(ce){Me=!1}function Le(e,t,n,r,a,i,l,o,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(e){this.onError(e)}}var De=!1,Ie=null,Fe=!1,qe=null,Ue={onError:function(e){De=!0,Ie=e}};function He(e,t,n,r,a,i,l,o,s){De=!1,Ie=null,Le.apply(Ue,arguments)}function $e(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function je(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function Qe(e){if($e(e)!==e)throw Error(i(188))}function Be(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=$e(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return Qe(a),e;if(l===r)return Qe(a),t;l=l.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=l;else{for(var o=!1,s=a.child;s;){if(s===n){o=!0,n=a,r=l;break}if(s===r){o=!0,r=a,n=l;break}s=s.sibling}if(!o){for(s=l.child;s;){if(s===n){o=!0,n=l,r=a;break}if(s===r){o=!0,r=l,n=a;break}s=s.sibling}if(!o)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?Ve(e):null}function Ve(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ve(e);if(null!==t)return t;e=e.sibling}return null}var Ze=a.unstable_scheduleCallback,We=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Ge=a.unstable_requestPaint,Ye=a.unstable_now,Xe=a.unstable_getCurrentPriorityLevel,Je=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,it=null,lt=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ot(e)/st|0)|0},ot=Math.log,st=Math.LN2,ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,i=e.pingedLanes,l=268435455&n;if(0!==l){var o=l&~a;0!==o?r=dt(o):0!=(i&=l)&&(r=dt(i))}else 0!=(l=n&~a)?r=dt(l):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&!(t&a)&&((a=r&-r)>=(i=t&-t)||16===a&&4194240&i))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-lt(t)),r|=e[n],t&=~a;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return!(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var kt,Et,Ct,xt,St,_t=!1,Tt=[],Nt=null,zt=null,At=null,Rt=new Map,Ot=new Map,Mt=[],Pt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lt(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":zt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":Rt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ot.delete(t.pointerId)}}function Dt(e,t,n,r,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[a]},null!==t&&null!==(t=wa(t))&&Et(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function It(e){var t=ba(e.target);if(null!==t){var n=$e(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=je(n)))return e.blockedOn=t,void St(e.priority,(function(){Ct(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wa(n))&&Et(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function qt(e,t,n){Ft(e)&&n.delete(t)}function Ut(){_t=!1,null!==Nt&&Ft(Nt)&&(Nt=null),null!==zt&&Ft(zt)&&(zt=null),null!==At&&Ft(At)&&(At=null),Rt.forEach(qt),Ot.forEach(qt)}function Ht(e,t){e.blockedOn===t&&(e.blockedOn=null,_t||(_t=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ut)))}function $t(e){function t(t){return Ht(t,e)}if(0<Tt.length){Ht(Tt[0],e);for(var n=1;n<Tt.length;n++){var r=Tt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Nt&&Ht(Nt,e),null!==zt&&Ht(zt,e),null!==At&&Ht(At,e),Rt.forEach(t),Ot.forEach(t),n=0;n<Mt.length;n++)(r=Mt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Mt.length&&null===(n=Mt[0]).blockedOn;)It(n),null===n.blockedOn&&Mt.shift()}var jt=w.ReactCurrentBatchConfig,Qt=!0;function Bt(e,t,n,r){var a=bt,i=jt.transition;jt.transition=null;try{bt=1,Zt(e,t,n,r)}finally{bt=a,jt.transition=i}}function Vt(e,t,n,r){var a=bt,i=jt.transition;jt.transition=null;try{bt=4,Zt(e,t,n,r)}finally{bt=a,jt.transition=i}}function Zt(e,t,n,r){if(Qt){var a=Kt(e,t,n,r);if(null===a)Br(e,t,r,Wt,n),Lt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Nt=Dt(Nt,e,t,n,r,a),!0;case"dragenter":return zt=Dt(zt,e,t,n,r,a),!0;case"mouseover":return At=Dt(At,e,t,n,r,a),!0;case"pointerover":var i=a.pointerId;return Rt.set(i,Dt(Rt.get(i)||null,e,t,n,r,a)),!0;case"gotpointercapture":return i=a.pointerId,Ot.set(i,Dt(Ot.get(i)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Lt(e,r),4&t&&-1<Pt.indexOf(e)){for(;null!==a;){var i=wa(a);if(null!==i&&kt(i),null===(i=Kt(e,t,n,r))&&Br(e,t,r,Wt,n),i===a)break;a=i}null!==a&&r.stopPropagation()}else Br(e,t,r,null,n)}}var Wt=null;function Kt(e,t,n,r){if(Wt=null,null!==(e=ba(e=ke(r))))if(null===(t=$e(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=je(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Wt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Yt=null,Xt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Xt,r=n.length,a="value"in Yt?Yt.value:Yt.textContent,i=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[i-t];t++);return Jt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,i){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=I({},un,{view:0,detail:0}),fn=an(dn),hn=I({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),pn=an(hn),mn=an(I({},hn,{dataTransfer:0})),gn=an(I({},dn,{relatedTarget:0})),vn=an(I({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=I({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),wn=an(I({},un,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},En={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Cn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Cn[e])&&!!t[e]}function Sn(){return xn}var Tn=I({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?En[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Nn=an(Tn),zn=an(I({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),An=an(I({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sn})),Rn=an(I({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=I({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mn=an(On),Pn=[9,13,27,32],Ln=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var In=c&&"TextEvent"in window&&!Dn,Fn=c&&(!Ln||Dn&&8<Dn&&11>=Dn),qn=String.fromCharCode(32),Un=!1;function Hn(e,t){switch(e){case"keyup":return-1!==Pn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $n(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var jn=!1,Qn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Bn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Qn[e.type]:"textarea"===t}function Vn(e,t,n,r){_e(r),0<(t=Zr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Zn=null,Wn=null;function Kn(e){qr(e,0)}function Gn(e){if(Z(ka(e)))return e}function Yn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Jn;if(c){var er="oninput"in document;if(!er){var tr=document.createElement("div");tr.setAttribute("oninput","return;"),er="function"==typeof tr.oninput}Jn=er}else Jn=!1;Xn=Jn&&(!document.documentMode||9<document.documentMode)}function nr(){Zn&&(Zn.detachEvent("onpropertychange",rr),Wn=Zn=null)}function rr(e){if("value"===e.propertyName&&Gn(Wn)){var t=[];Vn(t,Wn,e,ke(e)),Re(Kn,t)}}function ar(e,t,n){"focusin"===e?(nr(),Wn=n,(Zn=t).attachEvent("onpropertychange",rr)):"focusout"===e&&nr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Wn)}function lr(e,t){if("click"===e)return Gn(t)}function or(e,t){if("input"===e||"change"===e)return Gn(t)}var sr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function ur(e,t){if(sr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function hr(){for(var e=window,t=W();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=W((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=hr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,i=Math.min(r.start,a);r=void 0===r.end?i:Math.min(r.end,a),!e.extend&&i>r&&(a=r,r=i,i=a),a=dr(n,i);var l=dr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,yr=null,br=null,wr=!1;function kr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;wr||null==vr||vr!==W(r)||(r="selectionStart"in(r=vr)&&pr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&ur(br,r)||(br=r,0<(r=Zr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function Er(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cr={animationend:Er("Animation","AnimationEnd"),animationiteration:Er("Animation","AnimationIteration"),animationstart:Er("Animation","AnimationStart"),transitionend:Er("Transition","TransitionEnd")},xr={},Sr={};function _r(e){if(xr[e])return xr[e];if(!Cr[e])return e;var t,n=Cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Sr)return xr[e]=n[t];return e}c&&(Sr=document.createElement("div").style,"AnimationEvent"in window||(delete Cr.animationend.animation,delete Cr.animationiteration.animation,delete Cr.animationstart.animation),"TransitionEvent"in window||delete Cr.transitionend.transition);var Tr=_r("animationend"),Nr=_r("animationiteration"),zr=_r("animationstart"),Ar=_r("transitionend"),Rr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mr(e,t){Rr.set(e,t),s(t,[e])}for(var Pr=0;Pr<Or.length;Pr++){var Lr=Or[Pr];Mr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Mr(Tr,"onAnimationEnd"),Mr(Nr,"onAnimationIteration"),Mr(zr,"onAnimationStart"),Mr("dblclick","onDoubleClick"),Mr("focusin","onFocus"),Mr("focusout","onBlur"),Mr(Ar,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ir=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,o,s,u){if(He.apply(this,arguments),De){if(!De)throw Error(i(198));var c=Ie;De=!1,Ie=null,Fe||(Fe=!0,qe=c)}}(r,t,void 0,e),e.currentTarget=null}function qr(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var o=r[l],s=o.instance,u=o.currentTarget;if(o=o.listener,s!==i&&a.isPropagationStopped())break e;Fr(a,o,u),i=s}else for(l=0;l<r.length;l++){if(s=(o=r[l]).instance,u=o.currentTarget,o=o.listener,s!==i&&a.isPropagationStopped())break e;Fr(a,o,u),i=s}}}if(Fe)throw e=qe,Fe=!1,qe=null,e}function Ur(e,t){var n=t[ga];void 0===n&&(n=t[ga]=new Set);var r=e+"__bubble";n.has(r)||(Qr(t,e,2,!1),n.add(r))}function Hr(e,t,n){var r=0;t&&(r|=4),Qr(n,e,r,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function jr(e){if(!e[$r]){e[$r]=!0,l.forEach((function(t){"selectionchange"!==t&&(Ir.has(t)||Hr(t,!1,e),Hr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Hr("selectionchange",!1,t))}}function Qr(e,t,n,r){switch(Gt(t)){case 1:var a=Bt;break;case 4:a=Vt;break;default:a=Zt}n=a.bind(null,t,n,e),a=void 0,!Me||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Br(e,t,n,r,a){var i=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var o=r.stateNode.containerInfo;if(o===a||8===o.nodeType&&o.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;l=l.return}for(;null!==o;){if(null===(l=ba(o)))return;if(5===(s=l.tag)||6===s){r=i=l;continue e}o=o.parentNode}}r=r.return}Re((function(){var r=i,a=ke(n),l=[];e:{var o=Rr.get(e);if(void 0!==o){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=Nn;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=An;break;case Tr:case Nr:case zr:s=vn;break;case Ar:s=Rn;break;case"scroll":s=fn;break;case"wheel":s=Mn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=zn}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==o?o+"Capture":null:o;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&null!=(m=Oe(p,f))&&c.push(Vr(p,m,h))),d)break;p=p.return}0<c.length&&(o=new s(o,u,null,n,a),l.push({event:o,listeners:c}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!ba(u)&&!u[ma])&&(s||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ba(u):null)&&(u!==(d=$e(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=zn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==s?o:ka(s),h=null==u?o:ka(u),(o=new c(m,p+"leave",s,n,a)).target=d,o.relatedTarget=h,m=null,ba(a)===r&&((c=new c(f,p+"enter",u,n,a)).target=h,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,p=0,h=c=s;h;h=Wr(h))p++;for(h=0,m=f;m;m=Wr(m))h++;for(;0<p-h;)c=Wr(c),p--;for(;0<h-p;)f=Wr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Wr(c),f=Wr(f)}c=null}else c=null;null!==s&&Kr(l,o,s,c,!1),null!==u&&null!==d&&Kr(l,d,u,c,!0)}if("select"===(s=(o=r?ka(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===s&&"file"===o.type)var g=Yn;else if(Bn(o))if(Xn)g=or;else{g=ir;var v=ar}else(s=o.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(g=lr);switch(g&&(g=g(e,r))?Vn(l,g,n,a):(v&&v(e,o,r),"focusout"===e&&(v=o._wrapperState)&&v.controlled&&"number"===o.type&&ee(o,"number",o.value)),v=r?ka(r):window,e){case"focusin":(Bn(v)||"true"===v.contentEditable)&&(vr=v,yr=r,br=null);break;case"focusout":br=yr=vr=null;break;case"mousedown":wr=!0;break;case"contextmenu":case"mouseup":case"dragend":wr=!1,kr(l,n,a);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":kr(l,n,a)}var y;if(Ln)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else jn?Hn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(jn||"onCompositionStart"!==b?"onCompositionEnd"===b&&jn&&(y=en()):(Xt="value"in(Yt=a)?Yt.value:Yt.textContent,jn=!0)),0<(v=Zr(r,b)).length&&(b=new wn(b,e,null,n,a),l.push({event:b,listeners:v}),(y||null!==(y=$n(n)))&&(b.data=y))),(y=In?function(e,t){switch(e){case"compositionend":return $n(t);case"keypress":return 32!==t.which?null:(Un=!0,qn);case"textInput":return(e=t.data)===qn&&Un?null:e;default:return null}}(e,n):function(e,t){if(jn)return"compositionend"===e||!Ln&&Hn(e,t)?(e=en(),Jt=Xt=Yt=null,jn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(r=Zr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=y)}qr(l,t)}))}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Zr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Oe(e,n))&&r.unshift(Vr(e,i,a)),null!=(i=Oe(e,t))&&r.push(Vr(e,i,a))),e=e.return}return r}function Wr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var i=t._reactName,l=[];null!==n&&n!==r;){var o=n,s=o.alternate,u=o.stateNode;if(null!==s&&s===r)break;5===o.tag&&null!==u&&(o=u,a?null!=(s=Oe(n,i))&&l.unshift(Vr(n,s,o)):a||null!=(s=Oe(n,i))&&l.push(Vr(n,s,o))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Gr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Xr(e){return("string"==typeof e?e:""+e).replace(Gr,"\n").replace(Yr,"")}function Jr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(i(425))}function ea(){}var ta=null,na=null;function ra(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var aa="function"==typeof setTimeout?setTimeout:void 0,ia="function"==typeof clearTimeout?clearTimeout:void 0,la="function"==typeof Promise?Promise:void 0,oa="function"==typeof queueMicrotask?queueMicrotask:void 0!==la?function(e){return la.resolve(null).then(e).catch(sa)}:aa;function sa(e){setTimeout((function(){throw e}))}function ua(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void $t(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);$t(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function da(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fa=Math.random().toString(36).slice(2),ha="__reactFiber$"+fa,pa="__reactProps$"+fa,ma="__reactContainer$"+fa,ga="__reactEvents$"+fa,va="__reactListeners$"+fa,ya="__reactHandles$"+fa;function ba(e){var t=e[ha];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ma]||n[ha]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=da(e);null!==e;){if(n=e[ha])return n;e=da(e)}return t}n=(e=n).parentNode}return null}function wa(e){return!(e=e[ha]||e[ma])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ka(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function Ea(e){return e[pa]||null}var Ca=[],xa=-1;function Sa(e){return{current:e}}function _a(e){0>xa||(e.current=Ca[xa],Ca[xa]=null,xa--)}function Ta(e,t){xa++,Ca[xa]=e.current,e.current=t}var Na={},za=Sa(Na),Aa=Sa(!1),Ra=Na;function Oa(e,t){var n=e.type.contextTypes;if(!n)return Na;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in n)i[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ma(e){return null!=e.childContextTypes}function Pa(){_a(Aa),_a(za)}function La(e,t,n){if(za.current!==Na)throw Error(i(168));Ta(za,t),Ta(Aa,n)}function Da(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!=typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(i(108,j(e)||"Unknown",a));return I({},n,r)}function Ia(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Na,Ra=za.current,Ta(za,e),Ta(Aa,Aa.current),!0}function Fa(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Da(e,t,Ra),r.__reactInternalMemoizedMergedChildContext=e,_a(Aa),_a(za),Ta(za,e)):_a(Aa),Ta(Aa,n)}var qa=null,Ua=!1,Ha=!1;function $a(e){null===qa?qa=[e]:qa.push(e)}function ja(){if(!Ha&&null!==qa){Ha=!0;var e=0,t=bt;try{var n=qa;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}qa=null,Ua=!1}catch(t){throw null!==qa&&(qa=qa.slice(e+1)),Ze(Je,ja),t}finally{bt=t,Ha=!1}}return null}var Qa=[],Ba=0,Va=null,Za=0,Wa=[],Ka=0,Ga=null,Ya=1,Xa="";function Ja(e,t){Qa[Ba++]=Za,Qa[Ba++]=Va,Va=e,Za=t}function ei(e,t,n){Wa[Ka++]=Ya,Wa[Ka++]=Xa,Wa[Ka++]=Ga,Ga=e;var r=Ya;e=Xa;var a=32-lt(r)-1;r&=~(1<<a),n+=1;var i=32-lt(t)+a;if(30<i){var l=a-a%5;i=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Ya=1<<32-lt(t)+a|n<<a|r,Xa=i+e}else Ya=1<<i|n<<a|r,Xa=e}function ti(e){null!==e.return&&(Ja(e,1),ei(e,1,0))}function ni(e){for(;e===Va;)Va=Qa[--Ba],Qa[Ba]=null,Za=Qa[--Ba],Qa[Ba]=null;for(;e===Ga;)Ga=Wa[--Ka],Wa[Ka]=null,Xa=Wa[--Ka],Wa[Ka]=null,Ya=Wa[--Ka],Wa[Ka]=null}var ri=null,ai=null,ii=!1,li=null;function oi(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ri=e,ai=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ri=e,ai=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ya,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ri=e,ai=null,!0);default:return!1}}function ui(e){return!(!(1&e.mode)||128&e.flags)}function ci(e){if(ii){var t=ai;if(t){var n=t;if(!si(e,t)){if(ui(e))throw Error(i(418));t=ca(n.nextSibling);var r=ri;t&&si(e,t)?oi(r,n):(e.flags=-4097&e.flags|2,ii=!1,ri=e)}}else{if(ui(e))throw Error(i(418));e.flags=-4097&e.flags|2,ii=!1,ri=e}}}function di(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ri=e}function fi(e){if(e!==ri)return!1;if(!ii)return di(e),ii=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ra(e.type,e.memoizedProps)),t&&(t=ai)){if(ui(e))throw hi(),Error(i(418));for(;t;)oi(e,t),t=ca(t.nextSibling)}if(di(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ai=ca(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ai=null}}else ai=ri?ca(e.stateNode.nextSibling):null;return!0}function hi(){for(var e=ai;e;)e=ca(e.nextSibling)}function pi(){ai=ri=null,ii=!1}function mi(e){null===li?li=[e]:li.push(e)}var gi=w.ReactCurrentBatchConfig;function vi(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!=typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bi(e){return(0,e._init)(e._payload)}function wi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Pu(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var i=n.type;return i===C?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===O&&bi(i)===t.type)?((r=a(t,n.props)).ref=vi(e,t,n),r.return=e,r):((r=Lu(n.type,n.key,n.props,null,e.mode,r)).ref=vi(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Du(n,e.mode,r,i)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Lu(t.type,t.key,t.props,null,e.mode,n)).ref=vi(e,null,t),n.return=e,n;case E:return(t=qu(t,e.mode,n)).return=e,t;case O:return f(e,(0,t._init)(t._payload),n)}if(te(t)||L(t))return(t=Du(t,e.mode,n,null)).return=e,t;yi(e,t)}return null}function h(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===a?u(e,t,n,r):null;case E:return n.key===a?c(e,t,n,r):null;case O:return h(e,t,(a=n._init)(n._payload),r)}if(te(n)||L(n))return null!==a?null:d(e,t,n,r,null);yi(e,n)}return null}function p(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case E:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case O:return p(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||L(r))return d(t,e=e.get(n)||null,r,a,null);yi(t,r)}return null}function m(a,i,o,s){for(var u=null,c=null,d=i,m=i=0,g=null;null!==d&&m<o.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=h(a,d,o[m],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),i=l(v,i,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===o.length)return n(a,d),ii&&Ja(a,m),u;if(null===d){for(;m<o.length;m++)null!==(d=f(a,o[m],s))&&(i=l(d,i,m),null===c?u=d:c.sibling=d,c=d);return ii&&Ja(a,m),u}for(d=r(a,d);m<o.length;m++)null!==(g=p(d,a,m,o[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=l(g,i,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),ii&&Ja(a,m),u}function g(a,o,s,u){var c=L(s);if("function"!=typeof c)throw Error(i(150));if(null==(s=c.call(s)))throw Error(i(151));for(var d=c=null,m=o,g=o=0,v=null,y=s.next();null!==m&&!y.done;g++,y=s.next()){m.index>g?(v=m,m=null):v=m.sibling;var b=h(a,m,y.value,u);if(null===b){null===m&&(m=v);break}e&&m&&null===b.alternate&&t(a,m),o=l(b,o,g),null===d?c=b:d.sibling=b,d=b,m=v}if(y.done)return n(a,m),ii&&Ja(a,g),c;if(null===m){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,u))&&(o=l(y,o,g),null===d?c=y:d.sibling=y,d=y);return ii&&Ja(a,g),c}for(m=r(a,m);!y.done;g++,y=s.next())null!==(y=p(m,a,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),o=l(y,o,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(a,e)})),ii&&Ja(a,g),c}return function e(r,i,l,s){if("object"==typeof l&&null!==l&&l.type===C&&null===l.key&&(l=l.props.children),"object"==typeof l&&null!==l){switch(l.$$typeof){case k:e:{for(var u=l.key,c=i;null!==c;){if(c.key===u){if((u=l.type)===C){if(7===c.tag){n(r,c.sibling),(i=a(c,l.props.children)).return=r,r=i;break e}}else if(c.elementType===u||"object"==typeof u&&null!==u&&u.$$typeof===O&&bi(u)===c.type){n(r,c.sibling),(i=a(c,l.props)).ref=vi(r,c,l),i.return=r,r=i;break e}n(r,c);break}t(r,c),c=c.sibling}l.type===C?((i=Du(l.props.children,r.mode,s,l.key)).return=r,r=i):((s=Lu(l.type,l.key,l.props,null,r.mode,s)).ref=vi(r,i,l),s.return=r,r=s)}return o(r);case E:e:{for(c=l.key;null!==i;){if(i.key===c){if(4===i.tag&&i.stateNode.containerInfo===l.containerInfo&&i.stateNode.implementation===l.implementation){n(r,i.sibling),(i=a(i,l.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=qu(l,r.mode,s)).return=r,r=i}return o(r);case O:return e(r,i,(c=l._init)(l._payload),s)}if(te(l))return m(r,i,l,s);if(L(l))return g(r,i,l,s);yi(r,l)}return"string"==typeof l&&""!==l||"number"==typeof l?(l=""+l,null!==i&&6===i.tag?(n(r,i.sibling),(i=a(i,l)).return=r,r=i):(n(r,i),(i=Fu(l,r.mode,s)).return=r,r=i),o(r)):n(r,i)}}var ki=wi(!0),Ei=wi(!1),Ci=Sa(null),xi=null,Si=null,_i=null;function Ti(){_i=Si=xi=null}function Ni(e){var t=Ci.current;_a(Ci),e._currentValue=t}function zi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ai(e,t){xi=e,_i=Si=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(!!(e.lanes&t)&&(wo=!0),e.firstContext=null)}function Ri(e){var t=e._currentValue;if(_i!==e)if(e={context:e,memoizedValue:t,next:null},null===Si){if(null===xi)throw Error(i(308));Si=e,xi.dependencies={lanes:0,firstContext:e}}else Si=Si.next=e;return t}var Oi=null;function Mi(e){null===Oi?Oi=[e]:Oi.push(e)}function Pi(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Mi(t)):(n.next=a.next,a.next=n),t.interleaved=n,Li(e,r)}function Li(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Di=!1;function Ii(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ui(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&zs){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Li(e,n)}return null===(a=r.interleaved)?(t.next=t,Mi(r)):(t.next=a.next,a.next=t),r.interleaved=t,Li(e,n)}function Hi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function $i(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?a=i=l:i=i.next=l,n=n.next}while(null!==n);null===i?a=i=t:i=i.next=t}else a=i=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ji(e,t,n,r){var a=e.updateQueue;Di=!1;var i=a.firstBaseUpdate,l=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,u=s.next;s.next=null,null===l?i=u:l.next=u,l=s;var c=e.alternate;null!==c&&(o=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===o?c.firstBaseUpdate=u:o.next=u,c.lastBaseUpdate=s)}if(null!==i){var d=a.baseState;for(l=0,c=u=s=null,o=i;;){var f=o.lane,h=o.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var p=e,m=o;switch(f=t,h=n,m.tag){case 1:if("function"==typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(f="function"==typeof(p=m.payload)?p.call(h,d,f):p))break e;d=I({},d,f);break e;case 2:Di=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[o]:f.push(o))}else h={eventTime:h,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===c?(u=c=h,s=d):c=c.next=h,l|=f;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(f=o).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);Is|=l,e.lanes=l,e.memoizedState=d}}function Qi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!=typeof a)throw Error(i(191,a));a.call(r)}}}var Bi={},Vi=Sa(Bi),Zi=Sa(Bi),Wi=Sa(Bi);function Ki(e){if(e===Bi)throw Error(i(174));return e}function Gi(e,t){switch(Ta(Wi,t),Ta(Zi,e),Ta(Vi,Bi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}_a(Vi),Ta(Vi,t)}function Yi(){_a(Vi),_a(Zi),_a(Wi)}function Xi(e){Ki(Wi.current);var t=Ki(Vi.current),n=se(t,e.type);t!==n&&(Ta(Zi,e),Ta(Vi,n))}function Ji(e){Zi.current===e&&(_a(Vi),_a(Zi))}var el=Sa(0);function tl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function rl(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var al=w.ReactCurrentDispatcher,il=w.ReactCurrentBatchConfig,ll=0,ol=null,sl=null,ul=null,cl=!1,dl=!1,fl=0,hl=0;function pl(){throw Error(i(321))}function ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function gl(e,t,n,r,a,l){if(ll=l,ol=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,al.current=null===e||null===e.memoizedState?Jl:eo,e=n(r,a),dl){l=0;do{if(dl=!1,fl=0,25<=l)throw Error(i(301));l+=1,ul=sl=null,t.updateQueue=null,al.current=to,e=n(r,a)}while(dl)}if(al.current=Xl,t=null!==sl&&null!==sl.next,ll=0,ul=sl=ol=null,cl=!1,t)throw Error(i(300));return e}function vl(){var e=0!==fl;return fl=0,e}function yl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ul?ol.memoizedState=ul=e:ul=ul.next=e,ul}function bl(){if(null===sl){var e=ol.alternate;e=null!==e?e.memoizedState:null}else e=sl.next;var t=null===ul?ol.memoizedState:ul.next;if(null!==t)ul=t,sl=e;else{if(null===e)throw Error(i(310));e={memoizedState:(sl=e).memoizedState,baseState:sl.baseState,baseQueue:sl.baseQueue,queue:sl.queue,next:null},null===ul?ol.memoizedState=ul=e:ul=ul.next=e}return ul}function wl(e,t){return"function"==typeof t?t(e):t}function kl(e){var t=bl(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=sl,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var o=a.next;a.next=l.next,l.next=o}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var s=o=null,u=null,c=l;do{var d=c.lane;if((ll&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,o=r):u=u.next=f,ol.lanes|=d,Is|=d}c=c.next}while(null!==c&&c!==l);null===u?o=r:u.next=s,sr(r,t.memoizedState)||(wo=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,ol.lanes|=l,Is|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function El(e){var t=bl(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{l=e(l,o.action),o=o.next}while(o!==a);sr(l,t.memoizedState)||(wo=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Cl(){}function xl(e,t){var n=ol,r=bl(),a=t(),l=!sr(r.memoizedState,a);if(l&&(r.memoizedState=a,wo=!0),r=r.queue,Dl(Tl.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==ul&&1&ul.memoizedState.tag){if(n.flags|=2048,Rl(9,_l.bind(null,n,r,a,t),void 0,null),null===As)throw Error(i(349));30&ll||Sl(n,t,a)}return a}function Sl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ol.updateQueue)?(t={lastEffect:null,stores:null},ol.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function _l(e,t,n,r){t.value=n,t.getSnapshot=r,Nl(t)&&zl(e)}function Tl(e,t,n){return n((function(){Nl(t)&&zl(e)}))}function Nl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(e){return!0}}function zl(e){var t=Li(e,1);null!==t&&ru(t,e,1,-1)}function Al(e){var t=yl();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wl,lastRenderedState:e},t.queue=e,e=e.dispatch=Wl.bind(null,ol,e),[t.memoizedState,e]}function Rl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ol.updateQueue)?(t={lastEffect:null,stores:null},ol.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ol(){return bl().memoizedState}function Ml(e,t,n,r){var a=yl();ol.flags|=e,a.memoizedState=Rl(1|t,n,void 0,void 0===r?null:r)}function Pl(e,t,n,r){var a=bl();r=void 0===r?null:r;var i=void 0;if(null!==sl){var l=sl.memoizedState;if(i=l.destroy,null!==r&&ml(r,l.deps))return void(a.memoizedState=Rl(t,n,i,r))}ol.flags|=e,a.memoizedState=Rl(1|t,n,i,r)}function Ll(e,t){return Ml(8390656,8,e,t)}function Dl(e,t){return Pl(2048,8,e,t)}function Il(e,t){return Pl(4,2,e,t)}function Fl(e,t){return Pl(4,4,e,t)}function ql(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ul(e,t,n){return n=null!=n?n.concat([e]):null,Pl(4,4,ql.bind(null,t,e),n)}function Hl(){}function $l(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function jl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ql(e,t,n){return 21&ll?(sr(n,t)||(n=mt(),ol.lanes|=n,Is|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,wo=!0),e.memoizedState=n)}function Bl(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=il.transition;il.transition={};try{e(!1),t()}finally{bt=n,il.transition=r}}function Vl(){return bl().memoizedState}function Zl(e,t,n){var r=nu(e);n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Kl(e)?Gl(t,n):null!==(n=Pi(e,t,n,r))&&(ru(n,e,r,tu()),Yl(n,t,r))}function Wl(e,t,n){var r=nu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Kl(e))Gl(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var l=t.lastRenderedState,o=i(l,n);if(a.hasEagerState=!0,a.eagerState=o,sr(o,l)){var s=t.interleaved;return null===s?(a.next=a,Mi(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(e){}null!==(n=Pi(e,t,a,r))&&(ru(n,e,r,a=tu()),Yl(n,t,r))}}function Kl(e){var t=e.alternate;return e===ol||null!==t&&t===ol}function Gl(e,t){dl=cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yl(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Xl={readContext:Ri,useCallback:pl,useContext:pl,useEffect:pl,useImperativeHandle:pl,useInsertionEffect:pl,useLayoutEffect:pl,useMemo:pl,useReducer:pl,useRef:pl,useState:pl,useDebugValue:pl,useDeferredValue:pl,useTransition:pl,useMutableSource:pl,useSyncExternalStore:pl,useId:pl,unstable_isNewReconciler:!1},Jl={readContext:Ri,useCallback:function(e,t){return yl().memoizedState=[e,void 0===t?null:t],e},useContext:Ri,useEffect:Ll,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Ml(4194308,4,ql.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ml(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ml(4,2,e,t)},useMemo:function(e,t){var n=yl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yl();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Zl.bind(null,ol,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yl().memoizedState=e},useState:Al,useDebugValue:Hl,useDeferredValue:function(e){return yl().memoizedState=e},useTransition:function(){var e=Al(!1),t=e[0];return e=Bl.bind(null,e[1]),yl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ol,a=yl();if(ii){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===As)throw Error(i(349));30&ll||Sl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,Ll(Tl.bind(null,r,l,e),[e]),r.flags|=2048,Rl(9,_l.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=yl(),t=As.identifierPrefix;if(ii){var n=Xa;t=":"+t+"R"+(n=(Ya&~(1<<32-lt(Ya)-1)).toString(32)+n),0<(n=fl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=hl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},eo={readContext:Ri,useCallback:$l,useContext:Ri,useEffect:Dl,useImperativeHandle:Ul,useInsertionEffect:Il,useLayoutEffect:Fl,useMemo:jl,useReducer:kl,useRef:Ol,useState:function(){return kl(wl)},useDebugValue:Hl,useDeferredValue:function(e){return Ql(bl(),sl.memoizedState,e)},useTransition:function(){return[kl(wl)[0],bl().memoizedState]},useMutableSource:Cl,useSyncExternalStore:xl,useId:Vl,unstable_isNewReconciler:!1},to={readContext:Ri,useCallback:$l,useContext:Ri,useEffect:Dl,useImperativeHandle:Ul,useInsertionEffect:Il,useLayoutEffect:Fl,useMemo:jl,useReducer:El,useRef:Ol,useState:function(){return El(wl)},useDebugValue:Hl,useDeferredValue:function(e){var t=bl();return null===sl?t.memoizedState=e:Ql(t,sl.memoizedState,e)},useTransition:function(){return[El(wl)[0],bl().memoizedState]},useMutableSource:Cl,useSyncExternalStore:xl,useId:Vl,unstable_isNewReconciler:!1};function no(e,t){if(e&&e.defaultProps){for(var n in t=I({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ro(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:I({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ao={isMounted:function(e){return!!(e=e._reactInternals)&&$e(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=tu(),a=nu(e),i=qi(r,a);i.payload=t,null!=n&&(i.callback=n),null!==(t=Ui(e,i,a))&&(ru(t,e,a,r),Hi(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=tu(),a=nu(e),i=qi(r,a);i.tag=1,i.payload=t,null!=n&&(i.callback=n),null!==(t=Ui(e,i,a))&&(ru(t,e,a,r),Hi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=tu(),r=nu(e),a=qi(n,r);a.tag=2,null!=t&&(a.callback=t),null!==(t=Ui(e,a,r))&&(ru(t,e,r,n),Hi(t,e,r))}};function io(e,t,n,r,a,i,l){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,l):!(t.prototype&&t.prototype.isPureReactComponent&&ur(n,r)&&ur(a,i))}function lo(e,t,n){var r=!1,a=Na,i=t.contextType;return"object"==typeof i&&null!==i?i=Ri(i):(a=Ma(t)?Ra:za.current,i=(r=null!=(r=t.contextTypes))?Oa(e,a):Na),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ao,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function oo(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ao.enqueueReplaceState(t,t.state,null)}function so(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ii(e);var i=t.contextType;"object"==typeof i&&null!==i?a.context=Ri(i):(i=Ma(t)?Ra:za.current,a.context=Oa(e,i)),a.state=e.memoizedState,"function"==typeof(i=t.getDerivedStateFromProps)&&(ro(e,t,i,n),a.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof a.getSnapshotBeforeUpdate||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||(t=a.state,"function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ao.enqueueReplaceState(a,a.state,null),ji(e,n,a,r),a.state=e.memoizedState),"function"==typeof a.componentDidMount&&(e.flags|=4194308)}function uo(e,t){try{var n="",r=t;do{n+=H(r),r=r.return}while(r);var a=n}catch(e){a="\nError generating stack: "+e.message+"\n"+e.stack}return{value:e,source:t,stack:a,digest:null}}function co(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function fo(e,t){try{console.error(t.value)}catch(e){setTimeout((function(){throw e}))}}var ho="function"==typeof WeakMap?WeakMap:Map;function po(e,t,n){(n=qi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Bs||(Bs=!0,Vs=r),fo(0,t)},n}function mo(e,t,n){(n=qi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){fo(0,t)}}var i=e.stateNode;return null!==i&&"function"==typeof i.componentDidCatch&&(n.callback=function(){fo(0,t),"function"!=typeof r&&(null===Zs?Zs=new Set([this]):Zs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function go(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ho;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=_u.bind(null,e,t,n),t.then(e,e))}function vo(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yo(e,t,n,r,a){return 1&e.mode?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=qi(-1,1)).tag=2,Ui(n,t,1))),n.lanes|=1),e)}var bo=w.ReactCurrentOwner,wo=!1;function ko(e,t,n,r){t.child=null===e?Ei(t,null,n,r):ki(t,e.child,n,r)}function Eo(e,t,n,r,a){n=n.render;var i=t.ref;return Ai(t,a),r=gl(e,t,n,r,i,a),n=vl(),null===e||wo?(ii&&n&&ti(t),t.flags|=1,ko(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Bo(e,t,a))}function Co(e,t,n,r,a){if(null===e){var i=n.type;return"function"!=typeof i||Mu(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Lu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,xo(e,t,i,r,a))}if(i=e.child,!(e.lanes&a)){var l=i.memoizedProps;if((n=null!==(n=n.compare)?n:ur)(l,r)&&e.ref===t.ref)return Bo(e,t,a)}return t.flags|=1,(e=Pu(i,r)).ref=t.ref,e.return=t,t.child=e}function xo(e,t,n,r,a){if(null!==e){var i=e.memoizedProps;if(ur(i,r)&&e.ref===t.ref){if(wo=!1,t.pendingProps=r=i,!(e.lanes&a))return t.lanes=e.lanes,Bo(e,t,a);131072&e.flags&&(wo=!0)}}return To(e,t,n,r,a)}function So(e,t,n){var r=t.pendingProps,a=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ta(Ps,Ms),Ms|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,Ta(Ps,Ms),Ms|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ta(Ps,Ms),Ms|=n;else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,Ta(Ps,Ms),Ms|=r;return ko(e,t,a,n),t.child}function _o(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function To(e,t,n,r,a){var i=Ma(n)?Ra:za.current;return i=Oa(t,i),Ai(t,a),n=gl(e,t,n,r,i,a),r=vl(),null===e||wo?(ii&&r&&ti(t),t.flags|=1,ko(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Bo(e,t,a))}function No(e,t,n,r,a){if(Ma(n)){var i=!0;Ia(t)}else i=!1;if(Ai(t,a),null===t.stateNode)Qo(e,t),lo(t,n,r),so(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,o=t.memoizedProps;l.props=o;var s=l.context,u=n.contextType;u="object"==typeof u&&null!==u?Ri(u):Oa(t,u=Ma(n)?Ra:za.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof l.getSnapshotBeforeUpdate;d||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==r||s!==u)&&oo(t,l,r,u),Di=!1;var f=t.memoizedState;l.state=f,ji(t,r,l,a),s=t.memoizedState,o!==r||f!==s||Aa.current||Di?("function"==typeof c&&(ro(t,n,c,r),s=t.memoizedState),(o=Di||io(t,n,o,r,f,s,u))?(d||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=u,r=o):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Fi(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:no(t.type,o),l.props=u,d=t.pendingProps,f=l.context,s="object"==typeof(s=n.contextType)&&null!==s?Ri(s):Oa(t,s=Ma(n)?Ra:za.current);var h=n.getDerivedStateFromProps;(c="function"==typeof h||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(o!==d||f!==s)&&oo(t,l,r,s),Di=!1,f=t.memoizedState,l.state=f,ji(t,r,l,a);var p=t.memoizedState;o!==d||f!==p||Aa.current||Di?("function"==typeof h&&(ro(t,n,h,r),p=t.memoizedState),(u=Di||io(t,n,u,r,f,p,s)||!1)?(c||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,p,s),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),l.props=r,l.state=p,l.context=s,r=u):("function"!=typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return zo(e,t,n,r,i,a)}function zo(e,t,n,r,a,i){_o(e,t);var l=!!(128&t.flags);if(!r&&!l)return a&&Fa(t,n,!1),Bo(e,t,i);r=t.stateNode,bo.current=t;var o=l&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=ki(t,e.child,null,i),t.child=ki(t,null,o,i)):ko(e,t,o,i),t.memoizedState=r.state,a&&Fa(t,n,!0),t.child}function Ao(e){var t=e.stateNode;t.pendingContext?La(0,t.pendingContext,t.pendingContext!==t.context):t.context&&La(0,t.context,!1),Gi(e,t.containerInfo)}function Ro(e,t,n,r,a){return pi(),mi(a),t.flags|=256,ko(e,t,n,r),t.child}var Oo,Mo,Po,Lo,Do={dehydrated:null,treeContext:null,retryLane:0};function Io(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fo(e,t,n){var r,a=t.pendingProps,l=el.current,o=!1,s=!!(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&!!(2&l)),r?(o=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),Ta(el,1&l),null===e)return ci(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=a.children,e=a.fallback,o?(a=t.mode,o=t.child,s={mode:"hidden",children:s},1&a||null===o?o=Iu(s,a,0,null):(o.childLanes=0,o.pendingProps=s),e=Du(e,a,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Io(n),t.memoizedState=Do,e):qo(t,s));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,o){if(n)return 256&t.flags?(t.flags&=-257,Uo(e,t,o,r=co(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Iu({mode:"visible",children:r.children},a,0,null),(l=Du(l,a,o,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,1&t.mode&&ki(t,e.child,null,o),t.child.memoizedState=Io(o),t.memoizedState=Do,l);if(!(1&t.mode))return Uo(e,t,o,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Uo(e,t,o,r=co(l=Error(i(419)),r,void 0))}if(s=!!(o&e.childLanes),wo||s){if(null!==(r=As)){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=a&(r.suspendedLanes|o)?0:a)&&a!==l.retryLane&&(l.retryLane=a,Li(e,a),ru(r,e,a,-1))}return gu(),Uo(e,t,o,r=co(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Nu.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ai=ca(a.nextSibling),ri=t,ii=!0,li=null,null!==e&&(Wa[Ka++]=Ya,Wa[Ka++]=Xa,Wa[Ka++]=Ga,Ya=e.id,Xa=e.overflow,Ga=t),(t=qo(t,r.children)).flags|=4096,t)}(e,t,s,a,r,l,n);if(o){o=a.fallback,s=t.mode,r=(l=e.child).sibling;var u={mode:"hidden",children:a.children};return 1&s||t.child===l?(a=Pu(l,u)).subtreeFlags=14680064&l.subtreeFlags:((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null),null!==r?o=Pu(r,o):(o=Du(o,s,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,s=null===(s=e.child.memoizedState)?Io(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Do,a}return e=(o=e.child).sibling,a=Pu(o,{mode:"visible",children:a.children}),!(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function qo(e,t){return(t=Iu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Uo(e,t,n,r){return null!==r&&mi(r),ki(t,e.child,null,n),(e=qo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ho(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),zi(e.return,t,n)}function $o(e,t,n,r,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=a)}function jo(e,t,n){var r=t.pendingProps,a=r.revealOrder,i=r.tail;if(ko(e,t,r.children,n),2&(r=el.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ho(e,n,t);else if(19===e.tag)Ho(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ta(el,r),1&t.mode)switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===tl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$o(t,!1,a,n,i);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===tl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$o(t,!0,n,null,i);break;case"together":$o(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Qo(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Bo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Is|=t.lanes,!(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Pu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Pu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Vo(e,t){if(!ii)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Zo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wo(e,t,n){var r=t.pendingProps;switch(ni(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Zo(t),null;case 1:case 17:return Ma(t.type)&&Pa(),Zo(t),null;case 3:return r=t.stateNode,Yi(),_a(Aa),_a(za),rl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==li&&(ou(li),li=null))),Mo(e,t),Zo(t),null;case 5:Ji(t);var a=Ki(Wi.current);if(n=t.type,null!==e&&null!=t.stateNode)Po(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return Zo(t),null}if(e=Ki(Vi.current),fi(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[ha]=t,r[pa]=l,e=!!(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(a=0;a<Dr.length;a++)Ur(Dr[a],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":G(r,l),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Ur("invalid",r);break;case"textarea":ae(r,l),Ur("invalid",r)}for(var s in ye(n,l),a=null,l)if(l.hasOwnProperty(s)){var u=l[s];"children"===s?"string"==typeof u?r.textContent!==u&&(!0!==l.suppressHydrationWarning&&Jr(r.textContent,u,e),a=["children",u]):"number"==typeof u&&r.textContent!==""+u&&(!0!==l.suppressHydrationWarning&&Jr(r.textContent,u,e),a=["children",""+u]):o.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Ur("scroll",r)}switch(n){case"input":V(r),J(r,l,!0);break;case"textarea":V(r),le(r);break;case"select":case"option":break;default:"function"==typeof l.onClick&&(r.onclick=ea)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=oe(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[ha]=t,e[pa]=r,Oo(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),a=r;break;case"iframe":case"object":case"embed":Ur("load",e),a=r;break;case"video":case"audio":for(a=0;a<Dr.length;a++)Ur(Dr[a],e);a=r;break;case"source":Ur("error",e),a=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),a=r;break;case"details":Ur("toggle",e),a=r;break;case"input":G(e,r),a=K(e,r),Ur("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=I({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ur("invalid",e)}for(l in ye(n,a),u=a)if(u.hasOwnProperty(l)){var c=u[l];"style"===l?ge(e,c):"dangerouslySetInnerHTML"===l?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===l?"string"==typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"==typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(o.hasOwnProperty(l)?null!=c&&"onScroll"===l&&Ur("scroll",e):null!=c&&b(e,l,c,s))}switch(n){case"input":V(e),J(e,r,!1);break;case"textarea":V(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+Q(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=ea)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Zo(t),null;case 6:if(e&&null!=t.stateNode)Lo(e,t,e.memoizedProps,r);else{if("string"!=typeof r&&null===t.stateNode)throw Error(i(166));if(n=Ki(Wi.current),Ki(Vi.current),fi(t)){if(r=t.stateNode,n=t.memoizedProps,r[ha]=t,(l=r.nodeValue!==n)&&null!==(e=ri))switch(e.tag){case 3:Jr(r.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,!!(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[ha]=t,t.stateNode=r}return Zo(t),null;case 13:if(_a(el),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ii&&null!==ai&&1&t.mode&&!(128&t.flags))hi(),pi(),t.flags|=98560,l=!1;else if(l=fi(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(i(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(i(317));l[ha]=t}else pi(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Zo(t),l=!1}else null!==li&&(ou(li),li=null),l=!0;if(!l)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((r=null!==r)!=(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,1&t.mode&&(null===e||1&el.current?0===Ls&&(Ls=3):gu())),null!==t.updateQueue&&(t.flags|=4),Zo(t),null);case 4:return Yi(),Mo(e,t),null===e&&jr(t.stateNode.containerInfo),Zo(t),null;case 10:return Ni(t.type._context),Zo(t),null;case 19:if(_a(el),null===(l=t.memoizedState))return Zo(t),null;if(r=!!(128&t.flags),null===(s=l.rendering))if(r)Vo(l,!1);else{if(0!==Ls||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=tl(e))){for(t.flags|=128,Vo(l,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ta(el,1&el.current|2),t.child}e=e.sibling}null!==l.tail&&Ye()>js&&(t.flags|=128,r=!0,Vo(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=tl(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Vo(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ii)return Zo(t),null}else 2*Ye()-l.renderingStartTime>js&&1073741824!==n&&(t.flags|=128,r=!0,Vo(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=l.last)?n.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Ye(),t.sibling=null,n=el.current,Ta(el,r?1&n|2:1&n),t):(Zo(t),null);case 22:case 23:return fu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&1&t.mode?!!(1073741824&Ms)&&(Zo(t),6&t.subtreeFlags&&(t.flags|=8192)):Zo(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Ko(e,t){switch(ni(t),t.tag){case 1:return Ma(t.type)&&Pa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Yi(),_a(Aa),_a(za),rl(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ji(t),null;case 13:if(_a(el),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));pi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return _a(el),null;case 4:return Yi(),null;case 10:return Ni(t.type._context),null;case 22:case 23:return fu(),null;default:return null}}Oo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Mo=function(){},Po=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Ki(Vi.current);var i,l=null;switch(n){case"input":a=K(e,a),r=K(e,r),l=[];break;case"select":a=I({},a,{value:void 0}),r=I({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!=typeof a.onClick&&"function"==typeof r.onClick&&(e.onclick=ea)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(i in s)s.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(o.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(i in s)!s.hasOwnProperty(i)||u&&u.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in u)u.hasOwnProperty(i)&&s[i]!==u[i]&&(n||(n={}),n[i]=u[i])}else n||(l||(l=[]),l.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(l=l||[]).push(c,u)):"children"===c?"string"!=typeof u&&"number"!=typeof u||(l=l||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(o.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ur("scroll",e),l||s===u||(l=[])):(l=l||[]).push(c,u))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}},Lo=function(e,t,n,r){n!==r&&(t.flags|=4)};var Go=!1,Yo=!1,Xo="function"==typeof WeakSet?WeakSet:Set,Jo=null;function es(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(n){Su(e,t,n)}else n.current=null}function ts(e,t,n){try{n()}catch(n){Su(e,t,n)}}var ns=!1;function rs(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&ts(t,n,i)}a=a.next}while(a!==r)}}function as(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function is(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&(delete t[ha],delete t[pa],delete t[ga],delete t[va],delete t[ya]),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function ss(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=ea));else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var ds=null,fs=!1;function hs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(it&&"function"==typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,n)}catch(e){}switch(n.tag){case 5:Yo||es(n,t);case 6:var r=ds,a=fs;ds=null,hs(e,t,n),fs=a,null!==(ds=r)&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ds.removeChild(n.stateNode));break;case 18:null!==ds&&(fs?(e=ds,n=n.stateNode,8===e.nodeType?ua(e.parentNode,n):1===e.nodeType&&ua(e,n),$t(e)):ua(ds,n.stateNode));break;case 4:r=ds,a=fs,ds=n.stateNode.containerInfo,fs=!0,hs(e,t,n),ds=r,fs=a;break;case 0:case 11:case 14:case 15:if(!Yo&&null!==(r=n.updateQueue)&&null!==(r=r.lastEffect)){a=r=r.next;do{var i=a,l=i.destroy;i=i.tag,void 0!==l&&(2&i||4&i)&&ts(n,t,l),a=a.next}while(a!==r)}hs(e,t,n);break;case 1:if(!Yo&&(es(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(e){Su(n,t,e)}hs(e,t,n);break;case 21:hs(e,t,n);break;case 22:1&n.mode?(Yo=(r=Yo)||null!==n.memoizedState,hs(e,t,n),Yo=r):hs(e,t,n);break;default:hs(e,t,n)}}function ms(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xo),t.forEach((function(t){var r=zu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function gs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 5:ds=s.stateNode,fs=!1;break e;case 3:case 4:ds=s.stateNode.containerInfo,fs=!0;break e}s=s.return}if(null===ds)throw Error(i(160));ps(l,o,a),ds=null,fs=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(e){Su(a,t,e)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vs(t,e),t=t.sibling}function vs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gs(t,e),ys(e),4&r){try{rs(3,e,e.return),as(3,e)}catch(t){Su(e,e.return,t)}try{rs(5,e,e.return)}catch(t){Su(e,e.return,t)}}break;case 1:gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return);break;case 5:if(gs(t,e),ys(e),512&r&&null!==n&&es(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(t){Su(e,e.return,t)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,o=null!==n?n.memoizedProps:l,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===l.type&&null!=l.name&&Y(a,l),be(s,o);var c=be(s,l);for(o=0;o<u.length;o+=2){var d=u[o],f=u[o+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":X(a,l);break;case"textarea":ie(a,l);break;case"select":var h=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var p=l.value;null!=p?ne(a,!!l.multiple,p,!1):h!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[pa]=l}catch(t){Su(e,e.return,t)}}break;case 6:if(gs(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(t){Su(e,e.return,t)}}break;case 3:if(gs(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{$t(t.containerInfo)}catch(t){Su(e,e.return,t)}break;case 4:default:gs(t,e),ys(e);break;case 13:gs(t,e),ys(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||($s=Ye())),4&r&&ms(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Yo=(c=Yo)||d,gs(t,e),Yo=c):gs(t,e),ys(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&1&e.mode)for(Jo=e,d=e.child;null!==d;){for(f=Jo=d;null!==Jo;){switch(p=(h=Jo).child,h.tag){case 0:case 11:case 14:case 15:rs(4,h,h.return);break;case 1:es(h,h.return);var m=h.stateNode;if("function"==typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(e){Su(r,n,e)}}break;case 5:es(h,h.return);break;case 22:if(null!==h.memoizedState){Es(f);continue}}null!==p?(p.return=h,Jo=p):Es(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"==typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=f.stateNode,o=null!=(u=f.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",o))}catch(t){Su(e,e.return,t)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(t){Su(e,e.return,t)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:gs(t,e),ys(e),4&r&&ms(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),cs(e,ss(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;us(e,ss(e),l);break;default:throw Error(i(161))}}catch(t){Su(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,n){Jo=e,ws(e,t,n)}function ws(e,t,n){for(var r=!!(1&e.mode);null!==Jo;){var a=Jo,i=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||Go;if(!l){var o=a.alternate,s=null!==o&&null!==o.memoizedState||Yo;o=Go;var u=Yo;if(Go=l,(Yo=s)&&!u)for(Jo=a;null!==Jo;)s=(l=Jo).child,22===l.tag&&null!==l.memoizedState?Cs(a):null!==s?(s.return=l,Jo=s):Cs(a);for(;null!==i;)Jo=i,ws(i,t,n),i=i.sibling;Jo=a,Go=o,Yo=u}ks(e)}else 8772&a.subtreeFlags&&null!==i?(i.return=a,Jo=i):ks(e)}}function ks(e){for(;null!==Jo;){var t=Jo;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Yo||as(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Yo)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:no(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Qi(t,l,r);break;case 3:var o=t.updateQueue;if(null!==o){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Qi(t,o,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&$t(f)}}}break;default:throw Error(i(163))}Yo||512&t.flags&&is(t)}catch(e){Su(t,t.return,e)}}if(t===e){Jo=null;break}if(null!==(n=t.sibling)){n.return=t.return,Jo=n;break}Jo=t.return}}function Es(e){for(;null!==Jo;){var t=Jo;if(t===e){Jo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Jo=n;break}Jo=t.return}}function Cs(e){for(;null!==Jo;){var t=Jo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{as(4,t)}catch(e){Su(t,n,e)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(e){Su(t,a,e)}}var i=t.return;try{is(t)}catch(e){Su(t,i,e)}break;case 5:var l=t.return;try{is(t)}catch(e){Su(t,l,e)}}}catch(e){Su(t,t.return,e)}if(t===e){Jo=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Jo=o;break}Jo=t.return}}var xs,Ss=Math.ceil,_s=w.ReactCurrentDispatcher,Ts=w.ReactCurrentOwner,Ns=w.ReactCurrentBatchConfig,zs=0,As=null,Rs=null,Os=0,Ms=0,Ps=Sa(0),Ls=0,Ds=null,Is=0,Fs=0,qs=0,Us=null,Hs=null,$s=0,js=1/0,Qs=null,Bs=!1,Vs=null,Zs=null,Ws=!1,Ks=null,Gs=0,Ys=0,Xs=null,Js=-1,eu=0;function tu(){return 6&zs?Ye():-1!==Js?Js:Js=Ye()}function nu(e){return 1&e.mode?2&zs&&0!==Os?Os&-Os:null!==gi.transition?(0===eu&&(eu=mt()),eu):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Gt(e.type):1}function ru(e,t,n,r){if(50<Ys)throw Ys=0,Xs=null,Error(i(185));vt(e,n,r),2&zs&&e===As||(e===As&&(!(2&zs)&&(Fs|=n),4===Ls&&su(e,Os)),au(e,r),1===n&&0===zs&&!(1&t.mode)&&(js=Ye()+500,Ua&&ja()))}function au(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-lt(i),o=1<<l,s=a[l];-1===s?o&n&&!(o&r)||(a[l]=ht(o,t)):s<=t&&(e.expiredLanes|=o),i&=~o}}(e,t);var r=ft(e,e===As?Os:0);if(0===r)null!==n&&We(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&We(n),1===t)0===e.tag?function(e){Ua=!0,$a(e)}(uu.bind(null,e)):$a(uu.bind(null,e)),oa((function(){!(6&zs)&&ja()})),n=null;else{switch(wt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Au(n,iu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function iu(e,t){if(Js=-1,eu=0,6&zs)throw Error(i(327));var n=e.callbackNode;if(Cu()&&e.callbackNode!==n)return null;var r=ft(e,e===As?Os:0);if(0===r)return null;if(30&r||r&e.expiredLanes||t)t=vu(e,r);else{t=r;var a=zs;zs|=2;var l=mu();for(As===e&&Os===t||(Qs=null,js=Ye()+500,hu(e,t));;)try{bu();break}catch(t){pu(e,t)}Ti(),_s.current=l,zs=a,null!==Rs?t=0:(As=null,Os=0,t=Ls)}if(0!==t){if(2===t&&0!==(a=pt(e))&&(r=a,t=lu(e,a)),1===t)throw n=Ds,hu(e,0),su(e,r),au(e,Ye()),n;if(6===t)su(e,r);else{if(a=e.current.alternate,!(30&r||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],i=a.getSnapshot;a=a.value;try{if(!sr(i(),a))return!1}catch(e){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)||(t=vu(e,r),2===t&&(l=pt(e),0!==l&&(r=l,t=lu(e,l))),1!==t)))throw n=Ds,hu(e,0),su(e,r),au(e,Ye()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:Eu(e,Hs,Qs);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=$s+500-Ye())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){tu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=aa(Eu.bind(null,e,Hs,Qs),t);break}Eu(e,Hs,Qs);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var o=31-lt(r);l=1<<o,(o=t[o])>a&&(a=o),r&=~l}if(r=a,10<(r=(120>(r=Ye()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ss(r/1960))-r)){e.timeoutHandle=aa(Eu.bind(null,e,Hs,Qs),r);break}Eu(e,Hs,Qs);break;default:throw Error(i(329))}}}return au(e,Ye()),e.callbackNode===n?iu.bind(null,e):null}function lu(e,t){var n=Us;return e.current.memoizedState.isDehydrated&&(hu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Hs,Hs=n,null!==t&&ou(t)),e}function ou(e){null===Hs?Hs=e:Hs.push.apply(Hs,e)}function su(e,t){for(t&=~qs,t&=~Fs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function uu(e){if(6&zs)throw Error(i(327));Cu();var t=ft(e,0);if(!(1&t))return au(e,Ye()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=lu(e,r))}if(1===n)throw n=Ds,hu(e,0),su(e,t),au(e,Ye()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Eu(e,Hs,Qs),au(e,Ye()),null}function cu(e,t){var n=zs;zs|=1;try{return e(t)}finally{0===(zs=n)&&(js=Ye()+500,Ua&&ja())}}function du(e){null!==Ks&&0===Ks.tag&&!(6&zs)&&Cu();var t=zs;zs|=1;var n=Ns.transition,r=bt;try{if(Ns.transition=null,bt=1,e)return e()}finally{bt=r,Ns.transition=n,!(6&(zs=t))&&ja()}}function fu(){Ms=Ps.current,_a(Ps)}function hu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ia(n)),null!==Rs)for(n=Rs.return;null!==n;){var r=n;switch(ni(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Pa();break;case 3:Yi(),_a(Aa),_a(za),rl();break;case 5:Ji(r);break;case 4:Yi();break;case 13:case 19:_a(el);break;case 10:Ni(r.type._context);break;case 22:case 23:fu()}n=n.return}if(As=e,Rs=e=Pu(e.current,null),Os=Ms=t,Ls=0,Ds=null,qs=Fs=Is=0,Hs=Us=null,null!==Oi){for(t=0;t<Oi.length;t++)if(null!==(r=(n=Oi[t]).interleaved)){n.interleaved=null;var a=r.next,i=n.pending;if(null!==i){var l=i.next;i.next=a,r.next=l}n.pending=r}Oi=null}return e}function pu(e,t){for(;;){var n=Rs;try{if(Ti(),al.current=Xl,cl){for(var r=ol.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cl=!1}if(ll=0,ul=sl=ol=null,dl=!1,fl=0,Ts.current=null,null===n||null===n.return){Ls=1,Ds=t,Rs=null;break}e:{var l=e,o=n.return,s=n,u=t;if(t=Os,s.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var c=u,d=s,f=d.tag;if(!(1&d.mode||0!==f&&11!==f&&15!==f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=vo(o);if(null!==p){p.flags&=-257,yo(p,o,s,0,t),1&p.mode&&go(l,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(!(1&t)){go(l,c,t),gu();break e}u=Error(i(426))}else if(ii&&1&s.mode){var v=vo(o);if(null!==v){!(65536&v.flags)&&(v.flags|=256),yo(v,o,s,0,t),mi(uo(u,s));break e}}l=u=uo(u,s),4!==Ls&&(Ls=2),null===Us?Us=[l]:Us.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,$i(l,po(0,u,t));break e;case 1:s=u;var y=l.type,b=l.stateNode;if(!(128&l.flags||"function"!=typeof y.getDerivedStateFromError&&(null===b||"function"!=typeof b.componentDidCatch||null!==Zs&&Zs.has(b)))){l.flags|=65536,t&=-t,l.lanes|=t,$i(l,mo(l,s,t));break e}}l=l.return}while(null!==l)}ku(n)}catch(e){t=e,Rs===n&&null!==n&&(Rs=n=n.return);continue}break}}function mu(){var e=_s.current;return _s.current=Xl,null===e?Xl:e}function gu(){0!==Ls&&3!==Ls&&2!==Ls||(Ls=4),null===As||!(268435455&Is)&&!(268435455&Fs)||su(As,Os)}function vu(e,t){var n=zs;zs|=2;var r=mu();for(As===e&&Os===t||(Qs=null,hu(e,t));;)try{yu();break}catch(t){pu(e,t)}if(Ti(),zs=n,_s.current=r,null!==Rs)throw Error(i(261));return As=null,Os=0,Ls}function yu(){for(;null!==Rs;)wu(Rs)}function bu(){for(;null!==Rs&&!Ke();)wu(Rs)}function wu(e){var t=xs(e.alternate,e,Ms);e.memoizedProps=e.pendingProps,null===t?ku(e):Rs=t,Ts.current=null}function ku(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ko(n,t)))return n.flags&=32767,void(Rs=n);if(null===e)return Ls=6,void(Rs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Wo(n,t,Ms)))return void(Rs=n);if(null!==(t=t.sibling))return void(Rs=t);Rs=t=e}while(null!==t);0===Ls&&(Ls=5)}function Eu(e,t,n){var r=bt,a=Ns.transition;try{Ns.transition=null,bt=1,function(e,t,n,r){do{Cu()}while(null!==Ks);if(6&zs)throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-lt(n),i=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~i}}(e,l),e===As&&(Rs=As=null,Os=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Ws||(Ws=!0,Au(tt,(function(){return Cu(),null}))),l=!!(15990&n.flags),15990&n.subtreeFlags||l){l=Ns.transition,Ns.transition=null;var o=bt;bt=1;var s=zs;zs|=4,Ts.current=null,function(e,t){if(ta=Qt,pr(e=hr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(e){n=null;break e}var o=0,s=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(s=o+a),f!==l||0!==r&&3!==f.nodeType||(u=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===a&&(s=o),h===l&&++d===r&&(u=o),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(na={focusedElem:e,selectionRange:n},Qt=!1,Jo=t;null!==Jo;)if(e=(t=Jo).child,1028&t.subtreeFlags&&null!==e)e.return=t,Jo=e;else for(;null!==Jo;){t=Jo;try{var m=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:no(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(i(163))}}catch(e){Su(t,t.return,e)}if(null!==(e=t.sibling)){e.return=t.return,Jo=e;break}Jo=t.return}m=ns,ns=!1}(e,n),vs(n,e),mr(na),Qt=!!ta,na=ta=null,e.current=n,bs(n,e,a),Ge(),zs=s,bt=o,Ns.transition=l}else e.current=n;if(Ws&&(Ws=!1,Ks=e,Gs=a),0===(l=e.pendingLanes)&&(Zs=null),function(e){if(it&&"function"==typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,!(128&~e.current.flags))}catch(e){}}(n.stateNode),au(e,Ye()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)r((a=t[n]).value,{componentStack:a.stack,digest:a.digest});if(Bs)throw Bs=!1,e=Vs,Vs=null,e;!!(1&Gs)&&0!==e.tag&&Cu(),1&(l=e.pendingLanes)?e===Xs?Ys++:(Ys=0,Xs=e):Ys=0,ja()}(e,t,n,r)}finally{Ns.transition=a,bt=r}return null}function Cu(){if(null!==Ks){var e=wt(Gs),t=Ns.transition,n=bt;try{if(Ns.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Gs=0,6&zs)throw Error(i(331));var a=zs;for(zs|=4,Jo=e.current;null!==Jo;){var l=Jo,o=l.child;if(16&Jo.flags){var s=l.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Jo=c;null!==Jo;){var d=Jo;switch(d.tag){case 0:case 11:case 15:rs(8,d,l)}var f=d.child;if(null!==f)f.return=d,Jo=f;else for(;null!==Jo;){var h=(d=Jo).sibling,p=d.return;if(ls(d),d===c){Jo=null;break}if(null!==h){h.return=p,Jo=h;break}Jo=p}}}var m=l.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Jo=l}}if(2064&l.subtreeFlags&&null!==o)o.return=l,Jo=o;else e:for(;null!==Jo;){if(2048&(l=Jo).flags)switch(l.tag){case 0:case 11:case 15:rs(9,l,l.return)}var y=l.sibling;if(null!==y){y.return=l.return,Jo=y;break e}Jo=l.return}}var b=e.current;for(Jo=b;null!==Jo;){var w=(o=Jo).child;if(2064&o.subtreeFlags&&null!==w)w.return=o,Jo=w;else e:for(o=b;null!==Jo;){if(2048&(s=Jo).flags)try{switch(s.tag){case 0:case 11:case 15:as(9,s)}}catch(e){Su(s,s.return,e)}if(s===o){Jo=null;break e}var k=s.sibling;if(null!==k){k.return=s.return,Jo=k;break e}Jo=s.return}}if(zs=a,ja(),it&&"function"==typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(e){}r=!0}return r}finally{bt=n,Ns.transition=t}}return!1}function xu(e,t,n){e=Ui(e,t=po(0,t=uo(n,t),1),1),t=tu(),null!==e&&(vt(e,1,t),au(e,t))}function Su(e,t,n){if(3===e.tag)xu(e,e,n);else for(;null!==t;){if(3===t.tag){xu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Zs||!Zs.has(r))){t=Ui(t,e=mo(t,e=uo(n,e),1),1),e=tu(),null!==t&&(vt(t,1,e),au(t,e));break}}t=t.return}}function _u(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=tu(),e.pingedLanes|=e.suspendedLanes&n,As===e&&(Os&n)===n&&(4===Ls||3===Ls&&(130023424&Os)===Os&&500>Ye()-$s?hu(e,0):qs|=n),au(e,t)}function Tu(e,t){0===t&&(1&e.mode?(t=ct,!(130023424&(ct<<=1))&&(ct=4194304)):t=1);var n=tu();null!==(e=Li(e,t))&&(vt(e,t,n),au(e,n))}function Nu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Tu(e,n)}function zu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),Tu(e,n)}function Au(e,t){return Ze(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,r){return new Ru(e,t,n,r)}function Mu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Pu(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lu(e,t,n,r,a,l){var o=2;if(r=e,"function"==typeof e)Mu(e)&&(o=1);else if("string"==typeof e)o=5;else e:switch(e){case C:return Du(n.children,a,l,t);case x:o=8,a|=8;break;case S:return(e=Ou(12,n,t,2|a)).elementType=S,e.lanes=l,e;case z:return(e=Ou(13,n,t,a)).elementType=z,e.lanes=l,e;case A:return(e=Ou(19,n,t,a)).elementType=A,e.lanes=l,e;case M:return Iu(n,a,l,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case _:o=10;break e;case T:o=9;break e;case N:o=11;break e;case R:o=14;break e;case O:o=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Ou(o,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Du(e,t,n,r){return(e=Ou(7,e,r,t)).lanes=n,e}function Iu(e,t,n,r){return(e=Ou(22,e,r,t)).elementType=M,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function qu(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Hu(e,t,n,r,a,i,l,o,s){return e=new Uu(e,t,n,o,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Ou(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ii(i),e}function $u(e){if(!e)return Na;e:{if($e(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ma(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Ma(n))return Da(e,n,t)}return t}function ju(e,t,n,r,a,i,l,o,s){return(e=Hu(n,r,!0,e,0,i,0,o,s)).context=$u(null),n=e.current,(i=qi(r=tu(),a=nu(n))).callback=null!=t?t:null,Ui(n,i,a),e.current.lanes=a,vt(e,a,r),au(e,r),e}function Qu(e,t,n,r){var a=t.current,i=tu(),l=nu(a);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=qi(i,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ui(a,t,l))&&(ru(e,a,l,i),Hi(e,a,l)),l}function Bu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Vu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Zu(e,t){Vu(e,t),(e=e.alternate)&&Vu(e,t)}xs=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Aa.current)wo=!0;else{if(!(e.lanes&n||128&t.flags))return wo=!1,function(e,t,n){switch(t.tag){case 3:Ao(t),pi();break;case 5:Xi(t);break;case 1:Ma(t.type)&&Ia(t);break;case 4:Gi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ta(Ci,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ta(el,1&el.current),t.flags|=128,null):n&t.child.childLanes?Fo(e,t,n):(Ta(el,1&el.current),null!==(e=Bo(e,t,n))?e.sibling:null);Ta(el,1&el.current);break;case 19:if(r=!!(n&t.childLanes),128&e.flags){if(r)return jo(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ta(el,el.current),r)break;return null;case 22:case 23:return t.lanes=0,So(e,t,n)}return Bo(e,t,n)}(e,t,n);wo=!!(131072&e.flags)}else wo=!1,ii&&1048576&t.flags&&ei(t,Za,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Qo(e,t),e=t.pendingProps;var a=Oa(t,za.current);Ai(t,n),a=gl(null,t,r,e,a,n);var l=vl();return t.flags|=1,"object"==typeof a&&null!==a&&"function"==typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ma(r)?(l=!0,Ia(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ii(t),a.updater=ao,t.stateNode=a,a._reactInternals=t,so(t,r,e,n),t=zo(null,t,r,!0,l,n)):(t.tag=0,ii&&l&&ti(t),ko(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Qo(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"==typeof e)return Mu(e)?1:0;if(null!=e){if((e=e.$$typeof)===N)return 11;if(e===R)return 14}return 2}(r),e=no(r,e),a){case 0:t=To(null,t,r,e,n);break e;case 1:t=No(null,t,r,e,n);break e;case 11:t=Eo(null,t,r,e,n);break e;case 14:t=Co(null,t,r,no(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,To(e,t,r,a=t.elementType===r?a:no(r,a),n);case 1:return r=t.type,a=t.pendingProps,No(e,t,r,a=t.elementType===r?a:no(r,a),n);case 3:e:{if(Ao(t),null===e)throw Error(i(387));r=t.pendingProps,a=(l=t.memoizedState).element,Fi(e,t),ji(t,r,null,n);var o=t.memoizedState;if(r=o.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ro(e,t,r,n,a=uo(Error(i(423)),t));break e}if(r!==a){t=Ro(e,t,r,n,a=uo(Error(i(424)),t));break e}for(ai=ca(t.stateNode.containerInfo.firstChild),ri=t,ii=!0,li=null,n=Ei(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pi(),r===a){t=Bo(e,t,n);break e}ko(e,t,r,n)}t=t.child}return t;case 5:return Xi(t),null===e&&ci(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,o=a.children,ra(r,a)?o=null:null!==l&&ra(r,l)&&(t.flags|=32),_o(e,t),ko(e,t,o,n),t.child;case 6:return null===e&&ci(t),null;case 13:return Fo(e,t,n);case 4:return Gi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ki(t,null,r,n):ko(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,Eo(e,t,r,a=t.elementType===r?a:no(r,a),n);case 7:return ko(e,t,t.pendingProps,n),t.child;case 8:case 12:return ko(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,o=a.value,Ta(Ci,r._currentValue),r._currentValue=o,null!==l)if(sr(l.value,o)){if(l.children===a.children&&!Aa.current){t=Bo(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){o=l.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===l.tag){(u=qi(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),zi(l.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===l.tag)o=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(o=l.return))throw Error(i(341));o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),zi(o,n,t),o=l.sibling}else o=l.child;if(null!==o)o.return=l;else for(o=l;null!==o;){if(o===t){o=null;break}if(null!==(l=o.sibling)){l.return=o.return,o=l;break}o=o.return}l=o}ko(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ai(t,n),r=r(a=Ri(a)),t.flags|=1,ko(e,t,r,n),t.child;case 14:return a=no(r=t.type,t.pendingProps),Co(e,t,r,a=no(r.type,a),n);case 15:return xo(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:no(r,a),Qo(e,t),t.tag=1,Ma(r)?(e=!0,Ia(t)):e=!1,Ai(t,n),lo(t,r,a),so(t,r,a,n),zo(null,t,r,!0,e,n);case 19:return jo(e,t,n);case 22:return So(e,t,n)}throw Error(i(156,t.tag))};var Wu="function"==typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Ju(){}function ec(e,t,n,r,a){var i=n._reactRootContainer;if(i){var l=i;if("function"==typeof a){var o=a;a=function(){var e=Bu(l);o.call(e)}}Qu(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"==typeof r){var i=r;r=function(){var e=Bu(l);i.call(e)}}var l=ju(t,r,e,0,null,!1,0,"",Ju);return e._reactRootContainer=l,e[ma]=l.current,jr(8===e.nodeType?e.parentNode:e),du(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"==typeof r){var o=r;r=function(){var e=Bu(s);o.call(e)}}var s=Hu(e,0,!1,null,0,!1,0,"",Ju);return e._reactRootContainer=s,e[ma]=s.current,jr(8===e.nodeType?e.parentNode:e),du((function(){Qu(t,s,n,r)})),s}(n,t,e,a,r);return Bu(l)}Gu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Qu(e,t,null,null)},Gu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;du((function(){Qu(null,e,null,null)})),t[ma]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=xt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Mt.length&&0!==t&&t<Mt[n].priority;n++);Mt.splice(n,0,e),0===n&&It(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),au(t,Ye()),!(6&zs)&&(js=Ye()+500,ja()))}break;case 13:du((function(){var t=Li(e,1);if(null!==t){var n=tu();ru(t,e,1,n)}})),Zu(e,1)}},Et=function(e){if(13===e.tag){var t=Li(e,134217728);null!==t&&ru(t,e,134217728,tu()),Zu(e,134217728)}},Ct=function(e){if(13===e.tag){var t=nu(e),n=Li(e,t);null!==n&&ru(n,e,t,tu()),Zu(e,t)}},xt=function(){return bt},St=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Ee=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=Ea(r);if(!a)throw Error(i(90));Z(r),X(r,a)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ne=cu,ze=du;var tc={usingClientEntryPoint:!1,Events:[wa,ka,Ea,_e,Te,cu]},nc={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},rc={bundleType:nc.bundleType,version:nc.version,rendererPackageName:nc.rendererPackageName,rendererConfig:nc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Be(e))?null:e.stateNode},findFiberByHostInstance:nc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ac=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ac.isDisabled&&ac.supportsFiber)try{at=ac.inject(rc),it=ac}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yu(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:E,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yu(e))throw Error(i(299));var n=!1,r="",a=Wu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Hu(e,1,!1,null,0,n,0,r,a),e[ma]=t.current,jr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return null===(e=Be(t))?null:e.stateNode},t.flushSync=function(e){return du(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(i(200));return ec(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yu(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",o=Wu;if(null!=n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(o=n.onRecoverableError)),t=ju(t,null,e,1,null!=n?n:null,a,0,l,o),e[ma]=t.current,jr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Gu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(i(200));return ec(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(i(40));return!!e._reactRootContainer&&(du((function(){ec(null,null,e,!1,(function(){e._reactRootContainer=null,e[ma]=null}))})),!0)},t.unstable_batchedUpdates=cu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return ec(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},543:(e,t,n)=>{"use strict";n.d(t,{t:()=>l});var r=n(295),a=n(352),i=n(932),l=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setOnline(e):n.onOnline()}))},n.setOnline=function(e){this.online=e,e&&this.onOnline()},n.onOnline=function(){this.listeners.forEach((function(e){e()}))},n.isOnline=function(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},t}(a.Q))},576:(e,t,n)=>{"use strict";var r=n(143);t.H=r.createRoot,r.hydrateRoot},592:(e,t,n)=>{"use strict";n.d(t,{QueryClientProvider:()=>f,useQuery:()=>T});var r=n(185),a=n(143).unstable_batchedUpdates;r.j.setBatchNotifyFunction(a);var i=n(329),l=console;(0,i.B)(l);var o=n(758),s=o.createContext(void 0),u=o.createContext(!1);function c(e){return e&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=s),window.ReactQueryClientContext):s}var d=function(){var e=o.useContext(c(o.useContext(u)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},f=function(e){var t=e.client,n=e.contextSharing,r=void 0!==n&&n,a=e.children;o.useEffect((function(){return t.mount(),function(){t.unmount()}}),[t]);var i=c(r);return o.createElement(u.Provider,{value:r},o.createElement(i.Provider,{value:t},a))},h=n(425),p=n(295),m=n(932),g=n(814),v=n(352),y=n(100),b=function(e){function t(t,n){var r;return(r=e.call(this)||this).client=t,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,p.A)(t,e);var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),w(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return k(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return k(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(e,t){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var a=this.hasListeners();a&&E(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(t),!a||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var i=this.computeRefetchInterval();!a||this.currentQuery===r&&this.options.enabled===n.enabled&&i===this.currentRefetchInterval||this.updateRefetchInterval(i)},n.getOptimisticResult=function(e){var t=this.client.defaultQueryObserverOptions(e),n=this.client.getQueryCache().build(this.client,t);return this.createResult(n,t)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(e,t){var n=this,r={},a=function(e){n.trackedProps.includes(e)||n.trackedProps.push(e)};return Object.keys(e).forEach((function(t){Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:function(){return a(t),e[t]}})})),(t.useErrorBoundary||t.suspense)&&a("error"),r},n.getNextResult=function(e){var t=this;return new Promise((function(n,r){var a=t.subscribe((function(t){t.isFetching||(a(),t.isError&&(null==e?void 0:e.throwOnError)?r(t.error):n(t))}))}))},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(e){return this.fetch((0,h.A)({},e,{meta:{refetchPage:null==e?void 0:e.refetchPage}}))},n.fetchOptimistic=function(e){var t=this,n=this.client.defaultQueryObserverOptions(e),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return t.createResult(r,n)}))},n.fetch=function(e){var t=this;return this.executeFetch(e).then((function(){return t.updateResult(),t.currentResult}))},n.executeFetch=function(e){this.updateQuery();var t=this.currentQuery.fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(m.lQ)),t},n.updateStaleTimeout=function(){var e=this;if(this.clearStaleTimeout(),!m.S$&&!this.currentResult.isStale&&(0,m.gn)(this.options.staleTime)){var t=(0,m.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){e.currentResult.isStale||e.updateResult()}),t)}},n.computeRefetchInterval=function(){var e;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e},n.updateRefetchInterval=function(e){var t=this;this.clearRefetchInterval(),this.currentRefetchInterval=e,!m.S$&&!1!==this.options.enabled&&(0,m.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(t.options.refetchIntervalInBackground||g.m.isFocused())&&t.executeFetch()}),this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(e,t){var n,r=this.currentQuery,a=this.options,l=this.currentResult,o=this.currentResultState,s=this.currentResultOptions,u=e!==r,c=u?e.state:this.currentQueryInitialState,d=u?this.currentResult:this.previousQueryResult,f=e.state,h=f.dataUpdatedAt,p=f.error,g=f.errorUpdatedAt,v=f.isFetching,y=f.status,b=!1,k=!1;if(t.optimisticResults){var x=this.hasListeners(),S=!x&&w(e,t),_=x&&E(e,r,t,a);(S||_)&&(v=!0,h||(y="loading"))}if(t.keepPreviousData&&!f.dataUpdateCount&&(null==d?void 0:d.isSuccess)&&"error"!==y)n=d.data,h=d.dataUpdatedAt,y=d.status,b=!0;else if(t.select&&void 0!==f.data)if(l&&f.data===(null==o?void 0:o.data)&&t.select===this.selectFn)n=this.selectResult;else try{this.selectFn=t.select,n=t.select(f.data),!1!==t.structuralSharing&&(n=(0,m.BH)(null==l?void 0:l.data,n)),this.selectResult=n,this.selectError=null}catch(e){(0,i.t)().error(e),this.selectError=e}else n=f.data;if(void 0!==t.placeholderData&&void 0===n&&("loading"===y||"idle"===y)){var T;if((null==l?void 0:l.isPlaceholderData)&&t.placeholderData===(null==s?void 0:s.placeholderData))T=l.data;else if(T="function"==typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&void 0!==T)try{T=t.select(T),!1!==t.structuralSharing&&(T=(0,m.BH)(null==l?void 0:l.data,T)),this.selectError=null}catch(e){(0,i.t)().error(e),this.selectError=e}void 0!==T&&(y="success",n=T,k=!0)}return this.selectError&&(p=this.selectError,n=this.selectResult,g=Date.now(),y="error"),{status:y,isLoading:"loading"===y,isSuccess:"success"===y,isError:"error"===y,isIdle:"idle"===y,data:n,dataUpdatedAt:h,error:p,errorUpdatedAt:g,failureCount:f.fetchFailureCount,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:v,isRefetching:v&&"loading"!==y,isLoadingError:"error"===y&&0===f.dataUpdatedAt,isPlaceholderData:k,isPreviousData:b,isRefetchError:"error"===y&&0!==f.dataUpdatedAt,isStale:C(e,t),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(e,t){if(!t)return!0;var n=this.options,r=n.notifyOnChangeProps,a=n.notifyOnChangePropsExclusions;if(!r&&!a)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var i="tracked"===r?this.trackedProps:r;return Object.keys(e).some((function(n){var r=n,l=e[r]!==t[r],o=null==i?void 0:i.some((function(e){return e===n})),s=null==a?void 0:a.some((function(e){return e===n}));return l&&!s&&(!i||o)}))},n.updateResult=function(e){var t=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,m.f8)(this.currentResult,t)){var n={cache:!0};!1!==(null==e?void 0:e.listeners)&&this.shouldNotifyListeners(this.currentResult,t)&&(n.listeners=!0),this.notify((0,h.A)({},n,e))}},n.updateQuery=function(){var e=this.client.getQueryCache().build(this.client,this.options);if(e!==this.currentQuery){var t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}},n.onQueryUpdate=function(e){var t={};"success"===e.type?t.onSuccess=!0:"error"!==e.type||(0,y.wm)(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()},n.notify=function(e){var t=this;r.j.batch((function(){e.onSuccess?(null==t.options.onSuccess||t.options.onSuccess(t.currentResult.data),null==t.options.onSettled||t.options.onSettled(t.currentResult.data,null)):e.onError&&(null==t.options.onError||t.options.onError(t.currentResult.error),null==t.options.onSettled||t.options.onSettled(void 0,t.currentResult.error)),e.listeners&&t.listeners.forEach((function(e){e(t.currentResult)})),e.cache&&t.client.getQueryCache().notify({query:t.currentQuery,type:"observerResultsUpdated"})}))},t}(v.Q);function w(e,t){return function(e,t){return!(!1===t.enabled||e.state.dataUpdatedAt||"error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&k(e,t,t.refetchOnMount)}function k(e,t,n){if(!1!==t.enabled){var r="function"==typeof n?n(e):n;return"always"===r||!1!==r&&C(e,t)}return!1}function E(e,t,n,r){return!1!==n.enabled&&(e!==t||!1===r.enabled)&&(!n.suspense||"error"!==e.state.status)&&C(e,n)}function C(e,t){return e.isStaleByTime(t.staleTime)}var x,S=o.createContext((x=!1,{clearReset:function(){x=!1},reset:function(){x=!0},isReset:function(){return x}})),_=function(){return o.useContext(S)};function T(e,t,n){return function(e,t){var n=o.useRef(!1),a=o.useState(0)[1],i=d(),l=_(),s=i.defaultQueryObserverOptions(e);s.optimisticResults=!0,s.onError&&(s.onError=r.j.batchCalls(s.onError)),s.onSuccess&&(s.onSuccess=r.j.batchCalls(s.onSuccess)),s.onSettled&&(s.onSettled=r.j.batchCalls(s.onSettled)),s.suspense&&("number"!=typeof s.staleTime&&(s.staleTime=1e3),0===s.cacheTime&&(s.cacheTime=1)),(s.suspense||s.useErrorBoundary)&&(l.isReset()||(s.retryOnMount=!1));var u,c,f,h=o.useState((function(){return new t(i,s)}))[0],p=h.getOptimisticResult(s);if(o.useEffect((function(){n.current=!0,l.clearReset();var e=h.subscribe(r.j.batchCalls((function(){n.current&&a((function(e){return e+1}))})));return h.updateResult(),function(){n.current=!1,e()}}),[l,h]),o.useEffect((function(){h.setOptions(s,{listeners:!1})}),[s,h]),s.suspense&&p.isLoading)throw h.fetchOptimistic(s).then((function(e){var t=e.data;null==s.onSuccess||s.onSuccess(t),null==s.onSettled||s.onSettled(t,null)})).catch((function(e){l.clearReset(),null==s.onError||s.onError(e),null==s.onSettled||s.onSettled(void 0,e)}));if(p.isError&&!l.isReset()&&!p.isFetching&&(u=s.suspense,c=s.useErrorBoundary,f=[p.error,h.getCurrentQuery()],"function"==typeof c?c.apply(void 0,f):"boolean"==typeof c?c:u))throw p.error;return"tracked"===s.notifyOnChangeProps&&(p=h.trackResult(p,s)),p}((0,m.vh)(e,t,n),b)}},633:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.E});var r=n(672),a=n(640);n.o(a,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return a.QueryClientProvider}}),n.o(a,"useQuery")&&n.d(t,{useQuery:function(){return a.useQuery}})},640:()=>{},672:(e,t,n)=>{"use strict";n.d(t,{E:()=>v});var r=n(425),a=n(932),i=n(295),l=n(185),o=n(329),s=n(100),u=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=(0,r.A)({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),(0,a.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){e.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var n,r,i=this.state.data,l=(0,a.Zw)(e,i);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,i,l))?l=i:!1!==this.options.structuralSharing&&(l=(0,a.BH)(i,l)),this.dispatch({data:l,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),l},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(a.lQ).catch(a.lQ):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some((function(e){return!1!==e.options.enabled}))},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(e){return e.getCurrentResult().isStale}))},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,a.j3)(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnWindowFocus()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnReconnect()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter((function(t){return t!==e})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var n,r,i,l=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return null==(u=this.retryer)||u.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var c=this.observers.find((function(e){return e.options.queryFn}));c&&this.setOptions(c.options)}var d=(0,a.HN)(this.queryKey),f=(0,a.jY)(),h={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(f)return l.abortSignalConsumed=!0,f.signal}});var p,m,g={fetchOptions:t,options:this.options,queryKey:d,state:this.state,fetchFn:function(){return l.options.queryFn?(l.abortSignalConsumed=!1,l.options.queryFn(h)):Promise.reject("Missing queryFn")},meta:this.meta};return(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(p=this.options.behavior)||p.onFetch(g)),this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=g.fetchOptions)?void 0:r.meta)||this.dispatch({type:"fetch",meta:null==(m=g.fetchOptions)?void 0:m.meta}),this.retryer=new s.eJ({fn:g.fetchFn,abort:null==f||null==(i=f.abort)?void 0:i.bind(f),onSuccess:function(e){l.setData(e),null==l.cache.config.onSuccess||l.cache.config.onSuccess(e,l),0===l.cacheTime&&l.optionalRemove()},onError:function(e){(0,s.wm)(e)&&e.silent||l.dispatch({type:"error",error:e}),(0,s.wm)(e)||(null==l.cache.config.onError||l.cache.config.onError(e,l),(0,o.t)().error(e)),0===l.cacheTime&&l.optionalRemove()},onFail:function(){l.dispatch({type:"failed"})},onPause:function(){l.dispatch({type:"pause"})},onContinue:function(){l.dispatch({type:"continue"})},retry:g.options.retry,retryDelay:g.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),l.j.batch((function(){t.observers.forEach((function(t){t.onQueryUpdate(e)})),t.cache.notify({query:t,type:"queryUpdated",action:e})}))},t.getDefaultState=function(e){var t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==e.initialData?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,r=void 0!==t;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function(e,t){var n,a;switch(t.type){case"failed":return(0,r.A)({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"fetch":return(0,r.A)({},e,{fetchFailureCount:0,fetchMeta:null!=(n=t.meta)?n:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.A)({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(a=t.dataUpdatedAt)?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var i=t.error;return(0,s.wm)(i)&&i.revert&&this.revertState?(0,r.A)({},this.revertState):(0,r.A)({},e,{error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.A)({},e,{isInvalidated:!0});case"setState":return(0,r.A)({},e,t.state);default:return e}},e}(),c=n(352),d=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.queries=[],n.queriesMap={},n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r,i=t.queryKey,l=null!=(r=t.queryHash)?r:(0,a.F$)(i,t),o=this.get(l);return o||(o=new u({cache:this,queryKey:i,queryHash:l,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(i),meta:t.meta}),this.add(o)),o},n.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},n.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter((function(t){return t!==e})),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},n.clear=function(){var e=this;l.j.batch((function(){e.queries.forEach((function(t){e.remove(t)}))}))},n.get=function(e){return this.queriesMap[e]},n.getAll=function(){return this.queries},n.find=function(e,t){var n=(0,a.b_)(e,t)[0];return void 0===n.exact&&(n.exact=!0),this.queries.find((function(e){return(0,a.MK)(n,e)}))},n.findAll=function(e,t){var n=(0,a.b_)(e,t)[0];return Object.keys(n).length>0?this.queries.filter((function(e){return(0,a.MK)(n,e)})):this.queries},n.notify=function(e){var t=this;l.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){var e=this;l.j.batch((function(){e.queries.forEach((function(e){e.onFocus()}))}))},n.onOnline=function(){var e=this;l.j.batch((function(){e.queries.forEach((function(e){e.onOnline()}))}))},t}(c.Q),f=function(){function e(e){this.options=(0,r.A)({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter((function(t){return t!==e}))},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(a.lQ).catch(a.lQ)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)})).then((function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)})).then((function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})}))),r.then((function(){return t.executeMutation()})).then((function(n){e=n,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)})).then((function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)})).then((function(){return t.dispatch({type:"success",data:e}),e})).catch((function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),(0,o.t)().error(e),Promise.resolve().then((function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)})).then((function(){throw t.dispatch({type:"error",error:e}),e}))}))},t.executeMutation=function(){var e,t=this;return this.retryer=new s.eJ({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return(0,r.A)({},e,{failureCount:e.failureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"loading":return(0,r.A)({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return(0,r.A)({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.A)({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.A)({},e,t.state);default:return e}}(this.state,e),l.j.batch((function(){t.observers.forEach((function(t){t.onMutationUpdate(e)})),t.mutationCache.notify(t)}))},e}(),h=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.mutations=[],n.mutationId=0,n}(0,i.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r=new f({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(r),r},n.add=function(e){this.mutations.push(e),this.notify(e)},n.remove=function(e){this.mutations=this.mutations.filter((function(t){return t!==e})),e.cancel(),this.notify(e)},n.clear=function(){var e=this;l.j.batch((function(){e.mutations.forEach((function(t){e.remove(t)}))}))},n.getAll=function(){return this.mutations},n.find=function(e){return void 0===e.exact&&(e.exact=!0),this.mutations.find((function(t){return(0,a.nJ)(e,t)}))},n.findAll=function(e){return this.mutations.filter((function(t){return(0,a.nJ)(e,t)}))},n.notify=function(e){var t=this;l.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var e=this.mutations.filter((function(e){return e.state.isPaused}));return l.j.batch((function(){return e.reduce((function(e,t){return e.then((function(){return t.continue().catch(a.lQ)}))}),Promise.resolve())}))},t}(c.Q),p=n(814),m=n(543);function g(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}var v=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new d,this.mutationCache=e.mutationCache||new h,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=p.m.subscribe((function(){p.m.isFocused()&&m.t.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())})),this.unsubscribeOnline=m.t.subscribe((function(){p.m.isFocused()&&m.t.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())}))},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var n=(0,a.b_)(e,t)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function(e){return this.mutationCache.findAll((0,r.A)({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map((function(e){return[e.queryKey,e.state.data]}))},t.setQueryData=function(e,t,n){var r=(0,a.vh)(e),i=this.defaultQueryOptions(r);return this.queryCache.build(this,i).setData(t,n)},t.setQueriesData=function(e,t,n){var r=this;return l.j.batch((function(){return r.getQueryCache().findAll(e).map((function(e){var a=e.queryKey;return[a,r.setQueryData(a,t,n)]}))}))},t.getQueryState=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state},t.removeQueries=function(e,t){var n=(0,a.b_)(e,t)[0],r=this.queryCache;l.j.batch((function(){r.findAll(n).forEach((function(e){r.remove(e)}))}))},t.resetQueries=function(e,t,n){var i=this,o=(0,a.b_)(e,t,n),s=o[0],u=o[1],c=this.queryCache,d=(0,r.A)({},s,{active:!0});return l.j.batch((function(){return c.findAll(s).forEach((function(e){e.reset()})),i.refetchQueries(d,u)}))},t.cancelQueries=function(e,t,n){var r=this,i=(0,a.b_)(e,t,n),o=i[0],s=i[1],u=void 0===s?{}:s;void 0===u.revert&&(u.revert=!0);var c=l.j.batch((function(){return r.queryCache.findAll(o).map((function(e){return e.cancel(u)}))}));return Promise.all(c).then(a.lQ).catch(a.lQ)},t.invalidateQueries=function(e,t,n){var i,o,s,u=this,c=(0,a.b_)(e,t,n),d=c[0],f=c[1],h=(0,r.A)({},d,{active:null==(i=null!=(o=d.refetchActive)?o:d.active)||i,inactive:null!=(s=d.refetchInactive)&&s});return l.j.batch((function(){return u.queryCache.findAll(d).forEach((function(e){e.invalidate()})),u.refetchQueries(h,f)}))},t.refetchQueries=function(e,t,n){var i=this,o=(0,a.b_)(e,t,n),s=o[0],u=o[1],c=l.j.batch((function(){return i.queryCache.findAll(s).map((function(e){return e.fetch(void 0,(0,r.A)({},u,{meta:{refetchPage:null==s?void 0:s.refetchPage}}))}))})),d=Promise.all(c).then(a.lQ);return(null==u?void 0:u.throwOnError)||(d=d.catch(a.lQ)),d},t.fetchQuery=function(e,t,n){var r=(0,a.vh)(e,t,n),i=this.defaultQueryOptions(r);void 0===i.retry&&(i.retry=!1);var l=this.queryCache.build(this,i);return l.isStaleByTime(i.staleTime)?l.fetch(i):Promise.resolve(l.state.data)},t.prefetchQuery=function(e,t,n){return this.fetchQuery(e,t,n).then(a.lQ).catch(a.lQ)},t.fetchInfiniteQuery=function(e,t,n){var r=(0,a.vh)(e,t,n);return r.behavior={onFetch:function(e){e.fetchFn=function(){var t,n,r,i,l,o,u,c,d,f=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,h=null==(r=e.fetchOptions)||null==(i=r.meta)?void 0:i.fetchMore,p=null==h?void 0:h.pageParam,m="forward"===(null==h?void 0:h.direction),v="backward"===(null==h?void 0:h.direction),y=(null==(l=e.state.data)?void 0:l.pages)||[],b=(null==(o=e.state.data)?void 0:o.pageParams)||[],w=(0,a.jY)(),k=null==w?void 0:w.signal,E=b,C=!1,x=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},S=function(e,t,n,r){return E=r?[t].concat(E):[].concat(E,[t]),r?[n].concat(e):[].concat(e,[n])},_=function(t,n,r,a){if(C)return Promise.reject("Cancelled");if(void 0===r&&!n&&t.length)return Promise.resolve(t);var i={queryKey:e.queryKey,signal:k,pageParam:r,meta:e.meta},l=x(i),o=Promise.resolve(l).then((function(e){return S(t,r,e,a)}));return(0,s.dd)(l)&&(o.cancel=l.cancel),o};if(y.length)if(m){var T=void 0!==p,N=T?p:g(e.options,y);u=_(y,T,N)}else if(v){var z=void 0!==p,A=z?p:(c=e.options,d=y,null==c.getPreviousPageParam?void 0:c.getPreviousPageParam(d[0],d));u=_(y,z,A,!0)}else!function(){E=[];var t=void 0===e.options.getNextPageParam,n=!f||!y[0]||f(y[0],0,y);u=n?_([],t,b[0]):Promise.resolve(S([],b[0],y[0]));for(var r=function(n){u=u.then((function(r){if(!f||!y[n]||f(y[n],n,y)){var a=t?b[n]:g(e.options,r);return _(r,t,a)}return Promise.resolve(S(r,b[n],y[n]))}))},a=1;a<y.length;a++)r(a)}();else u=_([]);var R=u.then((function(e){return{pages:e,pageParams:E}}));return R.cancel=function(){C=!0,null==w||w.abort(),(0,s.dd)(u)&&u.cancel()},R}}},this.fetchQuery(r)},t.prefetchInfiniteQuery=function(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(a.lQ).catch(a.lQ)},t.cancelMutations=function(){var e=this,t=l.j.batch((function(){return e.mutationCache.getAll().map((function(e){return e.cancel()}))}));return Promise.all(t).then(a.lQ).catch(a.lQ)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var n=this.queryDefaults.find((function(t){return(0,a.Od)(e)===(0,a.Od)(t.queryKey)}));n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find((function(t){return(0,a.Cp)(e,t.queryKey)})))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var n=this.mutationDefaults.find((function(t){return(0,a.Od)(e)===(0,a.Od)(t.mutationKey)}));n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find((function(t){return(0,a.Cp)(e,t.mutationKey)})))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=(0,r.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=(0,a.F$)(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:(0,r.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}()},713:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator,p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,m(w,v.prototype),w.isPureReactComponent=!0;var k=Array.isArray,E=Object.prototype.hasOwnProperty,C={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,i={},l=null,o=null;if(null!=t)for(a in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(l=""+t.key),t)E.call(t,a)&&!x.hasOwnProperty(a)&&(i[a]=t[a]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===i[a]&&(i[a]=s[a]);return{$$typeof:n,type:e,key:l,ref:o,props:i,_owner:C.current}}function _(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function N(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function z(e,t,a,i,l){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s=!1;if(null===e)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return l=l(s=e),e=""===i?"."+N(s,0):i,k(l)?(a="",null!=e&&(a=e.replace(T,"$&/")+"/"),z(l,t,a,"",(function(e){return e}))):null!=l&&(_(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(T,"$&/")+"/")+e)),t.push(l)),1;if(s=0,i=""===i?".":i+":",k(e))for(var u=0;u<e.length;u++){var c=i+N(o=e[u],u);s+=z(o,t,a,c,l)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"==typeof c)for(e=c.call(e),u=0;!(o=e.next()).done;)s+=z(o=o.value,t,a,c=i+N(o,u++),l);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function A(e,t,n){if(null==e)return e;var r=[],a=0;return z(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},M={transition:null},P={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:M,ReactCurrentOwner:C};function L(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:A,forEach:function(e,t,n){A(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return A(e,(function(){t++})),t},toArray:function(e){return A(e,(function(e){return e}))||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=i,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=P,t.act=L,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),i=e.key,l=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,o=C.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)E.call(t,u)&&!x.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:i,ref:l,props:a,_owner:o}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=L,t.useCallback=function(e,t){return O.current.useCallback(e,t)},t.useContext=function(e){return O.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return O.current.useDeferredValue(e)},t.useEffect=function(e,t){return O.current.useEffect(e,t)},t.useId=function(){return O.current.useId()},t.useImperativeHandle=function(e,t,n){return O.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return O.current.useMemo(e,t)},t.useReducer=function(e,t,n){return O.current.useReducer(e,t,n)},t.useRef=function(e){return O.current.useRef(e)},t.useState=function(e){return O.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return O.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return O.current.useTransition()},t.version="18.3.1"},731:(e,t,n)=>{var r;!function(){"use strict";var a={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function i(e){return function(e,t){var n,r,l,o,s,u,c,d,f,h=1,p=e.length,m="";for(r=0;r<p;r++)if("string"==typeof e[r])m+=e[r];else if("object"==typeof e[r]){if((o=e[r]).keys)for(n=t[h],l=0;l<o.keys.length;l++){if(null==n)throw new Error(i('[sprintf] Cannot access property "%s" of undefined value "%s"',o.keys[l],o.keys[l-1]));n=n[o.keys[l]]}else n=o.param_no?t[o.param_no]:t[h++];if(a.not_type.test(o.type)&&a.not_primitive.test(o.type)&&n instanceof Function&&(n=n()),a.numeric_arg.test(o.type)&&"number"!=typeof n&&isNaN(n))throw new TypeError(i("[sprintf] expecting number but found %T",n));switch(a.number.test(o.type)&&(d=n>=0),o.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,o.width?parseInt(o.width):0);break;case"e":n=o.precision?parseFloat(n).toExponential(o.precision):parseFloat(n).toExponential();break;case"f":n=o.precision?parseFloat(n).toFixed(o.precision):parseFloat(n);break;case"g":n=o.precision?String(Number(n.toPrecision(o.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=o.precision?n.substring(0,o.precision):n;break;case"t":n=String(!!n),n=o.precision?n.substring(0,o.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=o.precision?n.substring(0,o.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=o.precision?n.substring(0,o.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}a.json.test(o.type)?m+=n:(!a.number.test(o.type)||d&&!o.sign?f="":(f=d?"+":"-",n=n.toString().replace(a.sign,"")),u=o.pad_char?"0"===o.pad_char?"0":o.pad_char.charAt(1):" ",c=o.width-(f+n).length,s=o.width&&c>0?u.repeat(c):"",m+=o.align?f+n+s:"0"===u?f+s+n:s+f+n)}return m}(function(e){if(o[e])return o[e];for(var t,n=e,r=[],i=0;n;){if(null!==(t=a.text.exec(n)))r.push(t[0]);else if(null!==(t=a.modulo.exec(n)))r.push("%");else{if(null===(t=a.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){i|=1;var l=[],s=t[2],u=[];if(null===(u=a.key.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(l.push(u[1]);""!==(s=s.substring(u[0].length));)if(null!==(u=a.key_access.exec(s)))l.push(u[1]);else{if(null===(u=a.index_access.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");l.push(u[1])}t[2]=l}else i|=2;if(3===i)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}n=n.substring(t[0].length)}return o[e]=r}(e),arguments)}function l(e,t){return i.apply(null,[e].concat(t||[]))}var o=Object.create(null);t.sprintf=i,t.vsprintf=l,"undefined"!=typeof window&&(window.sprintf=i,window.vsprintf=l,void 0===(r=function(){return{sprintf:i,vsprintf:l}}.call(t,n,t,e))||(e.exports=r))}()},733:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<i(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var o=2*(r+1)-1,s=e[o],u=o+1,c=e[u];if(0>i(s,n))u<a&&0>i(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[o]=n,r=o);else{if(!(u<a&&0>i(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,g=!1,v="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function k(e){if(g=!1,w(e),!m)if(null!==r(u))m=!0,M(E);else{var t=r(c);null!==t&&P(k,t.startTime-e)}}function E(e,n){m=!1,g&&(g=!1,y(_),_=-1),p=!0;var i=h;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!z());){var l=f.callback;if("function"==typeof l){f.callback=null,h=f.priorityLevel;var o=l(f.expirationTime<=n);n=t.unstable_now(),"function"==typeof o?f.callback=o:f===r(u)&&a(u),w(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&P(k,d.startTime-n),s=!1}return s}finally{f=null,h=i,p=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var C,x=!1,S=null,_=-1,T=5,N=-1;function z(){return!(t.unstable_now()-N<T)}function A(){if(null!==S){var e=t.unstable_now();N=e;var n=!0;try{n=S(!0,e)}finally{n?C():(x=!1,S=null)}}else x=!1}if("function"==typeof b)C=function(){b(A)};else if("undefined"!=typeof MessageChannel){var R=new MessageChannel,O=R.port2;R.port1.onmessage=A,C=function(){O.postMessage(null)}}else C=function(){v(A,0)};function M(e){S=e,x||(x=!0,C())}function P(e,n){_=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,M(E))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,a,i){var l=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?l+i:l,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:i,expirationTime:o=i+o,sortIndex:-1},i>l?(e.sortIndex=i,n(c,e),null===r(u)&&e===r(c)&&(g?(y(_),_=-1):g=!0,P(k,i-l))):(e.sortIndex=o,n(u,e),m||p||(m=!0,M(E))),e},t.unstable_shouldYield=z,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},758:(e,t,n)=>{"use strict";e.exports=n(713)},803:(e,t,n)=>{"use strict";n.d(t,{ip:()=>p,U2:()=>m,W5:()=>C,se:()=>h});const r=function(e){return"string"!=typeof e||""===e?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(e)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)},a=function(e){return"string"!=typeof e||""===e?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(e)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(e)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)},i=function(e,t){return function(n,i,l,o=10){const s=e[t];if(!a(n))return;if(!r(i))return;if("function"!=typeof l)return void console.error("The hook callback must be a function.");if("number"!=typeof o)return void console.error("If specified, the hook priority must be a number.");const u={callback:l,priority:o,namespace:i};if(s[n]){const e=s[n].handlers;let t;for(t=e.length;t>0&&!(o>=e[t-1].priority);t--);t===e.length?e[t]=u:e.splice(t,0,u),s.__current.forEach((e=>{e.name===n&&e.currentIndex>=t&&e.currentIndex++}))}else s[n]={handlers:[u],runs:0};"hookAdded"!==n&&e.doAction("hookAdded",n,i,l,o)}},l=function(e,t,n=!1){return function(i,l){const o=e[t];if(!a(i))return;if(!n&&!r(l))return;if(!o[i])return 0;let s=0;if(n)s=o[i].handlers.length,o[i]={runs:o[i].runs,handlers:[]};else{const e=o[i].handlers;for(let t=e.length-1;t>=0;t--)e[t].namespace===l&&(e.splice(t,1),s++,o.__current.forEach((e=>{e.name===i&&e.currentIndex>=t&&e.currentIndex--})))}return"hookRemoved"!==i&&e.doAction("hookRemoved",i,l),s}},o=function(e,t){return function(n,r){const a=e[t];return void 0!==r?n in a&&a[n].handlers.some((e=>e.namespace===r)):n in a}},s=function(e,t,n=!1){return function(r,...a){const i=e[t];i[r]||(i[r]={handlers:[],runs:0}),i[r].runs++;const l=i[r].handlers;if(!l||!l.length)return n?a[0]:void 0;const o={name:r,currentIndex:0};for(i.__current.push(o);o.currentIndex<l.length;){const e=l[o.currentIndex].callback.apply(null,a);n&&(a[0]=e),o.currentIndex++}return i.__current.pop(),n?a[0]:void 0}},u=function(e,t){return function(){var n;const r=e[t];return null!==(n=r.__current[r.__current.length-1]?.name)&&void 0!==n?n:null}},c=function(e,t){return function(n){const r=e[t];return void 0===n?void 0!==r.__current[0]:!!r.__current[0]&&n===r.__current[0].name}},d=function(e,t){return function(n){const r=e[t];if(a(n))return r[n]&&r[n].runs?r[n].runs:0}};class f{constructor(){this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=i(this,"actions"),this.addFilter=i(this,"filters"),this.removeAction=l(this,"actions"),this.removeFilter=l(this,"filters"),this.hasAction=o(this,"actions"),this.hasFilter=o(this,"filters"),this.removeAllActions=l(this,"actions",!0),this.removeAllFilters=l(this,"filters",!0),this.doAction=s(this,"actions"),this.applyFilters=s(this,"filters",!0),this.currentAction=u(this,"actions"),this.currentFilter=u(this,"filters"),this.doingAction=c(this,"actions"),this.doingFilter=c(this,"filters"),this.didAction=d(this,"actions"),this.didFilter=d(this,"filters")}}const h=new f,{addAction:p,addFilter:m,removeAction:g,removeFilter:v,hasAction:y,hasFilter:b,removeAllActions:w,removeAllFilters:k,doAction:E,applyFilters:C,currentAction:x,currentFilter:S,doingAction:_,doingFilter:T,didAction:N,didFilter:z,actions:A,filters:R}=h},814:(e,t,n)=>{"use strict";n.d(t,{m:()=>l});var r=n(295),a=n(352),i=n(932),l=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"==typeof e?n.setFocused(e):n.onFocus()}))},n.setFocused=function(e){this.focused=e,e&&this.onFocus()},n.onFocus=function(){this.listeners.forEach((function(e){e()}))},n.isFocused=function(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(a.Q))},896:(e,t,n)=>{"use strict";e.exports=n(733)},932:(e,t,n)=>{"use strict";n.d(t,{BH:()=>y,Cp:()=>g,F$:()=>p,G6:()=>x,HN:()=>s,MK:()=>f,Od:()=>m,S$:()=>a,Zw:()=>l,b_:()=>d,f8:()=>b,gn:()=>o,j3:()=>u,jY:()=>S,lQ:()=>i,nJ:()=>h,vh:()=>c,yy:()=>C});var r=n(425),a="undefined"==typeof window;function i(){}function l(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function s(e){return Array.isArray(e)?e:[e]}function u(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t,n){return E(e)?"function"==typeof t?(0,r.A)({},n,{queryKey:e,queryFn:t}):(0,r.A)({},t,{queryKey:e}):e}function d(e,t,n){return E(e)?[(0,r.A)({},t,{queryKey:e}),n]:[e||{},t]}function f(e,t){var n=e.active,r=e.exact,a=e.fetching,i=e.inactive,l=e.predicate,o=e.queryKey,s=e.stale;if(E(o))if(r){if(t.queryHash!==p(o,t.options))return!1}else if(!g(t.queryKey,o))return!1;var u=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(n,i);if("none"===u)return!1;if("all"!==u){var c=t.isActive();if("active"===u&&!c)return!1;if("inactive"===u&&c)return!1}return!("boolean"==typeof s&&t.isStale()!==s||"boolean"==typeof a&&t.isFetching()!==a||l&&!l(t))}function h(e,t){var n=e.exact,r=e.fetching,a=e.predicate,i=e.mutationKey;if(E(i)){if(!t.options.mutationKey)return!1;if(n){if(m(t.options.mutationKey)!==m(i))return!1}else if(!g(t.options.mutationKey,i))return!1}return!("boolean"==typeof r&&"loading"===t.state.status!==r||a&&!a(t))}function p(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){var t;return t=s(e),JSON.stringify(t,(function(e,t){return w(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}))}function g(e,t){return v(s(e),s(t))}function v(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((function(n){return!v(e[n],t[n])}))}function y(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||w(e)&&w(t)){for(var r=n?e.length:Object.keys(e).length,a=n?t:Object.keys(t),i=a.length,l=n?[]:{},o=0,s=0;s<i;s++){var u=n?s:a[s];l[u]=y(e[u],t[u]),l[u]===e[u]&&o++}return r===i&&o===r?e:l}return t}function b(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function w(e){if(!k(e))return!1;var t=e.constructor;if(void 0===t)return!0;var n=t.prototype;return!!k(n)&&!!n.hasOwnProperty("isPrototypeOf")}function k(e){return"[object Object]"===Object.prototype.toString.call(e)}function E(e){return"string"==typeof e||Array.isArray(e)}function C(e){return new Promise((function(t){setTimeout(t,e)}))}function x(e){Promise.resolve().then(e).catch((function(e){return setTimeout((function(){throw e}))}))}function S(){if("function"==typeof AbortController)return new AbortController}},957:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.QueryClient,QueryClientProvider:()=>a.QueryClientProvider,useQuery:()=>a.useQuery});var r=n(633);n.o(r,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return r.QueryClientProvider}}),n.o(r,"useQuery")&&n.d(t,{useQuery:function(){return r.useQuery}});var a=n(592)},968:(e,t,n)=>{"use strict";n.d(t,{__:()=>__,_n:()=>_n,nv:()=>l});var r=n(731),a=n.n(r);const i=function(e,t){var n,r,a=0;function i(){var i,l,o=n,s=arguments.length;e:for(;o;){if(o.args.length===arguments.length){for(l=0;l<s;l++)if(o.args[l]!==arguments[l]){o=o.next;continue e}return o!==n&&(o===r&&(r=o.prev),o.prev.next=o.next,o.next&&(o.next.prev=o.prev),o.next=n,o.prev=null,n.prev=o,n=o),o.val}o=o.next}for(i=new Array(s),l=0;l<s;l++)i[l]=arguments[l];return o={args:i,val:e.apply(null,i)},n?(n.prev=o,o.next=n):r=o,a===t.maxSize?(r=r.prev).next=null:a++,n=o,o.val}return t=t||{},i.clear=function(){n=null,r=null,a=0},i}(console.error);function l(e,...t){try{return a().sprintf(e,...t)}catch(t){return t instanceof Error&&i("sprintf error: \n\n"+t.toString()),e}}var o,s,u,c;o={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},s=["(","?"],u={")":["("],":":["?","?:"]},c=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var d={"!":function(e){return!e},"*":function(e,t){return e*t},"/":function(e,t){return e/t},"%":function(e,t){return e%t},"+":function(e,t){return e+t},"-":function(e,t){return e-t},"<":function(e,t){return e<t},"<=":function(e,t){return e<=t},">":function(e,t){return e>t},">=":function(e,t){return e>=t},"==":function(e,t){return e===t},"!=":function(e,t){return e!==t},"&&":function(e,t){return e&&t},"||":function(e,t){return e||t},"?:":function(e,t,n){if(e)throw t;return n}};var f={contextDelimiter:"",onMissingKey:null};function h(e,t){var n;for(n in this.data=e,this.pluralForms={},this.options={},f)this.options[n]=void 0!==t&&n in t?t[n]:f[n]}h.prototype.getPluralForm=function(e,t){var n,r,a,i,l=this.pluralForms[e];return l||("function"!=typeof(a=(n=this.data[e][""])["Plural-Forms"]||n["plural-forms"]||n.plural_forms)&&(r=function(e){var t,n,r;for(t=e.split(";"),n=0;n<t.length;n++)if(0===(r=t[n].trim()).indexOf("plural="))return r.substr(7)}(n["Plural-Forms"]||n["plural-forms"]||n.plural_forms),i=function(e){var t=function(e){for(var t,n,r,a,i=[],l=[];t=e.match(c);){for(n=t[0],(r=e.substr(0,t.index).trim())&&i.push(r);a=l.pop();){if(u[n]){if(u[n][0]===a){n=u[n][1]||n;break}}else if(s.indexOf(a)>=0||o[a]<o[n]){l.push(a);break}i.push(a)}u[n]||l.push(n),e=e.substr(t.index+n.length)}return(e=e.trim())&&i.push(e),i.concat(l.reverse())}(e);return function(e){return function(e,t){var n,r,a,i,l,o,s=[];for(n=0;n<e.length;n++){if(l=e[n],i=d[l]){for(r=i.length,a=Array(r);r--;)a[r]=s.pop();try{o=i.apply(null,a)}catch(e){return e}}else o=t.hasOwnProperty(l)?t[l]:+l;s.push(o)}return s[0]}(t,e)}}(r),a=function(e){return+i({n:e})}),l=this.pluralForms[e]=a),l(t)},h.prototype.dcnpgettext=function(e,t,n,r,a){var i,l,o;return i=void 0===a?0:this.getPluralForm(e,a),l=n,t&&(l=t+this.options.contextDelimiter+n),(o=this.data[e][l])&&o[i]?o[i]:(this.options.onMissingKey&&this.options.onMissingKey(n,e),0===i?n:r)};const p={plural_forms:e=>1===e?0:1},m=/^i18n\.(n?gettext|has_translation)(_|$)/,g=((e,t,n)=>{const r=new h({}),a=new Set,i=()=>{a.forEach((e=>e()))},l=(e,t="default")=>{r.data[t]={...r.data[t],...e},r.data[t][""]={...p,...r.data[t]?.[""]},delete r.pluralForms[t]},o=(e,t)=>{l(e,t),i()},s=(e="default",t,n,a,i)=>(r.data[e]||l(void 0,e),r.dcnpgettext(e,t,n,a,i)),u=(e="default")=>e,_x=(e,t,r)=>{let a=s(r,t,e);return n?(a=n.applyFilters("i18n.gettext_with_context",a,e,t,r),n.applyFilters("i18n.gettext_with_context_"+u(r),a,e,t,r)):a};if(n){const e=e=>{m.test(e)&&i()};n.addAction("hookAdded","core/i18n",e),n.addAction("hookRemoved","core/i18n",e)}return{getLocaleData:(e="default")=>r.data[e],setLocaleData:o,addLocaleData:(e,t="default")=>{r.data[t]={...r.data[t],...e,"":{...p,...r.data[t]?.[""],...e?.[""]}},delete r.pluralForms[t],i()},resetLocaleData:(e,t)=>{r.data={},r.pluralForms={},o(e,t)},subscribe:e=>(a.add(e),()=>a.delete(e)),__:(e,t)=>{let r=s(t,void 0,e);return n?(r=n.applyFilters("i18n.gettext",r,e,t),n.applyFilters("i18n.gettext_"+u(t),r,e,t)):r},_x,_n:(e,t,r,a)=>{let i=s(a,void 0,e,t,r);return n?(i=n.applyFilters("i18n.ngettext",i,e,t,r,a),n.applyFilters("i18n.ngettext_"+u(a),i,e,t,r,a)):i},_nx:(e,t,r,a,i)=>{let l=s(i,a,e,t,r);return n?(l=n.applyFilters("i18n.ngettext_with_context",l,e,t,r,a,i),n.applyFilters("i18n.ngettext_with_context_"+u(i),l,e,t,r,a,i)):l},isRTL:()=>"rtl"===_x("ltr","text direction"),hasTranslation:(e,t,a)=>{const i=t?t+""+e:e;let l=!!r.data?.[null!=a?a:"default"]?.[i];return n&&(l=n.applyFilters("i18n.has_translation",l,e,t,a),l=n.applyFilters("i18n.has_translation_"+u(a),l,e,t,a)),l}}})(0,0,n(803).se),__=(g.getLocaleData.bind(g),g.setLocaleData.bind(g),g.resetLocaleData.bind(g),g.subscribe.bind(g),g.__.bind(g)),_n=(g._x.bind(g),g._n.bind(g));g._nx.bind(g),g.isRTL.bind(g),g.hasTranslation.bind(g)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var t=n.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var a=r.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=r[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e+"../"})(),(()=>{"use strict";var e=n(758);const t=()=>(0,e.createElement)("svg",{className:"betterdocs-launch-icon",xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("mask",{id:"a",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M24 0H0v24h24V0Z"}),(0,e.createElement)("path",{fill:"#fff",d:"M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"})),(0,e.createElement)("g",{mask:"url(#a)"},(0,e.createElement)("mask",{id:"b",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0V0Z"})),(0,e.createElement)("g",{mask:"url(#b)"},(0,e.createElement)("path",{stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:10,strokeWidth:1.5,d:"M12 .938C5.89.938.937 5.89.937 12c0 2.15.615 4.158 1.677 5.856L.938 23.062l5.206-1.676A11.01 11.01 0 0 0 12 23.063c6.11 0 11.063-4.953 11.063-11.063S18.11.938 12 .938Z"}),(0,e.createElement)("path",{fill:"#fff",d:"M13.172 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.344 0ZM17.86 12a1.172 1.172 0 1 1-2.344 0 1.172 1.172 0 0 1 2.343 0ZM8.484 12a1.172 1.172 0 1 1-2.343 0 1.172 1.172 0 0 1 2.343 0Z"})))),r=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:15,height:15,className:"betterdocs-launch-icon betterdocs-launch-icon-close"},(0,e.createElement)("path",{d:"M14.793.207a.758.758 0 0 0-.227-.156.774.774 0 0 0-.27-.059.774.774 0 0 0-.269.059.758.758 0 0 0-.226.156L7.5 6.504 1.2.207A.682.682 0 0 0 .702 0a.682.682 0 0 0-.496.207A.682.682 0 0 0 0 .703c0 .188.07.363.207.496L6.504 7.5.207 13.8a.682.682 0 0 0-.207.497c0 .187.07.363.207.496A.682.682 0 0 0 .703 15c.188 0 .363-.07.496-.207L7.5 8.496l6.3 6.297a.682.682 0 0 0 .497.207c.187 0 .363-.07.496-.207a.682.682 0 0 0 .207-.496.682.682 0 0 0-.207-.496L8.496 7.5l6.297-6.3a.758.758 0 0 0 .156-.227.769.769 0 0 0 .051-.27.769.769 0 0 0-.05-.27.758.758 0 0 0-.157-.226Zm0 0",style:{stroke:"none",fillRule:"nonzero",fill:"#fff",fillOpacity:1}}));var a=n(803);const i=betterdocs,l=i?.BASE_URL,o=(i.IA_NONCE,Boolean(i.HOME_DOCS_SWITCH)),s=Boolean(i.HOME_FAQ_SWITCH),u=i.CHAT,c=Boolean(i.TAB_AI_CHATBOT),d=i.FAQ,f=i.HOME_FAQ,h=i.SEARCH,p=i.SEARCH_LETTER_LIMIT,m=i.ASKFORM,g=m.FILE_UPLOAD_SWITCH,v=i.THANKS,y=i.DOC_CATEGORY,b=y["doc-title"],w=d["faq-title"],k=d["faq-switch"],E=d.faq_content_type,C=d["faq-terms"],x=d["faq-list"],S=d["faq-terms-order"],_=d["faq-terms-order-by"],T=d["faq-list-orderby"],N=d["faq-list-order"],z=y["doc-category-switch"],A=y["doc-terms"],R=y["doc-terms-order"],O=y["doc-terms-order-by"],M=y["doc-subcategory-switch"],P=i?.URL,L=i?.HOME_CONTENT,D=i?.HOME_CONTENT_DOC_CATEGORY_TITLE,I=i?.HOME_CONTENT_DOCS_TITLE,F="docs"==L?I:D,q="docs"==L?"post_type":"taxonomy",U=i?.FEEDBACK?.DISPLAY,H=i?.FEEDBACK?.URL,$=i?.FEEDBACK?.SUCCESS,j=i?.FEEDBACK?.TEXT,Q=i.HOME_TITLE,B=i.HOME_SUBTITLE,V=i.RESOURCES_TITLE,Z=i.RESOURCES_TAB_TITLE,W=(i.HEADER_ICON,i.HEADER_LOGO),K=i.TAB_HOME_ICON,G=i.TAB_MESSAGE_ICON,Y=i.TAB_RESOURCE_ICON,X=i.LAUNCHER?.open_icon,J=i.LAUNCHER?.close_icon,ee=i.BRANDING?.show,te=i.HOME_TAB_TITLE,ne={home:K?.url,feedback_tab:G?.url,resources:Y?.url},re=({toogle:n})=>{const[i,l]=(0,e.useState)(void 0),[o,s]=(0,e.useState)(void 0),[u,c]=(0,e.useState)(!1);return(0,e.useEffect)((()=>{(0,a.ip)("openIconPreviewAction","instant_answer",(e=>{const{img_url:t,preview:n}=e;l(t),c(n)})),(0,a.ip)("closeIconPreviewAction","instant_answer",(e=>{const{img_url:t,preview:n}=e;s(t),c(n)}))}),[]),!n&&u?null!=i?(0,e.createElement)("img",{src:i,height:25,width:25}):(0,e.createElement)(t,null):n&&u?null!=o?(0,e.createElement)("img",{src:o,height:25,width:25}):(0,e.createElement)(r,null):n&&!u?null!=J?(0,e.createElement)("img",{src:J,height:25,width:25}):(0,e.createElement)(r,null):n||u?void 0:null!=X?(0,e.createElement)("img",{src:X,height:25,width:25}):(0,e.createElement)(t,null)},ae=({toggleState:t,setToggleState:n})=>(0,e.createElement)("div",{className:"betterdocs-ia-launcher-wrapper"},(0,e.createElement)("button",{className:"betterdocs-ia-launcher",onClick:()=>n(!t)},(0,e.createElement)(re,{toogle:t}))),ie=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("mask",{id:"b",width:24,height:24,x:0,y:0,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0V0Z"})),(0,e.createElement)("g",{fill:"#202223",mask:"url(#b)"},(0,e.createElement)("path",{fillRule:"evenodd",d:"M16.242 22.547a6.307 6.307 0 0 1-6.183-5.07l-1.472.292a7.807 7.807 0 0 0 11.61 5.203l3.837 1.061-1.062-3.837A7.807 7.807 0 0 0 17.77 8.587l-.292 1.472a6.307 6.307 0 0 1 4.056 9.613l-.184.283.533 1.927-1.927-.533-.283.184a6.271 6.271 0 0 1-3.43 1.014Z",clipRule:"evenodd"}),(0,e.createElement)("path",{fillRule:"evenodd",d:"M-.047 9.164A9.211 9.211 0 1 1 4.5 17.108L-.033 18.36 1.22 13.83A9.172 9.172 0 0 1-.047 9.164Zm9.211-7.71A7.711 7.711 0 0 0 2.662 13.31l.18.281-.724 2.618 2.618-.724.281.18A7.71 7.71 0 1 0 9.164 1.453Z",clipRule:"evenodd"}),(0,e.createElement)("path",{d:"M9.867 14.11H8.461v-1.407h1.406v1.406Z"}),(0,e.createElement)("path",{fillRule:"evenodd",d:"M9.914 10.22v1.077h-1.5V9.56l1.667-1.525a1.36 1.36 0 1 0-2.276-1.003h-1.5a2.86 2.86 0 1 1 4.789 2.11l-1.18 1.079Z",clipRule:"evenodd"}))),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),le=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{fill:"#16CA9E",clipPath:"url(#a)"},(0,e.createElement)("path",{d:"M24 16.242a7.782 7.782 0 0 0-4.268-6.929c-.079 5.71-4.709 10.34-10.419 10.42A7.782 7.782 0 0 0 16.243 24a7.73 7.73 0 0 0 3.947-1.078l3.776 1.044-1.045-3.776A7.73 7.73 0 0 0 24 16.242Z"}),(0,e.createElement)("path",{d:"M18.328 9.164C18.328 4.111 14.218 0 9.164 0 4.111 0 0 4.11 0 9.164c0 1.647.438 3.25 1.27 4.658L.035 18.294l4.472-1.237a9.135 9.135 0 0 0 4.658 1.271c5.053 0 9.164-4.11 9.164-9.164ZM7.758 7.031H6.352A2.816 2.816 0 0 1 9.164 4.22a2.816 2.816 0 0 1 2.813 2.812 2.82 2.82 0 0 1-.915 2.076L9.867 10.2v1.097H8.461V9.58l1.652-1.512a1.408 1.408 0 0 0-.948-2.444c-.776 0-1.407.63-1.407 1.406Zm.703 5.672h1.406v1.406H8.461v-1.406Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),oe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{fill:"#000",d:"m23.4 10.392-.002-.002L13.608.6a2.194 2.194 0 0 0-1.562-.647c-.59 0-1.145.23-1.563.647L.698 10.385a2.212 2.212 0 0 0-.006 3.13 2.197 2.197 0 0 0 1.535.648h.39v7.204a2.589 2.589 0 0 0 2.586 2.586h3.83a.703.703 0 0 0 .703-.703v-5.648c0-.651.53-1.18 1.18-1.18h2.26c.65 0 1.18.529 1.18 1.18v5.648c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.586-2.586v-7.204h.362c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261.001-3.123Zm-.996 2.13a.798.798 0 0 1-.568.235h-1.065a.703.703 0 0 0-.703.703v7.907c0 .65-.529 1.18-1.18 1.18h-3.127v-4.945a2.589 2.589 0 0 0-2.586-2.586h-2.259a2.59 2.59 0 0 0-2.586 2.586v4.945H5.203c-.65 0-1.18-.53-1.18-1.18V13.46a.703.703 0 0 0-.703-.703H2.273a.797.797 0 0 1-.586-.236.804.804 0 0 1 0-1.136h.001l9.79-9.79a.797.797 0 0 1 .568-.236c.214 0 .416.084.568.236l9.787 9.787a.805.805 0 0 1 .003 1.14Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),se=({url:t,active:n})=>t?(0,e.createElement)("img",{src:TAB_MESSAGE_ICON?.url,width:"24",height:"24"}):(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{fill:"#16CA9E",d:"m23.353 10.439-.002-.002-9.79-9.79A2.194 2.194 0 0 0 12 0c-.59 0-1.145.23-1.563.647L.652 10.432l-.01.01a2.212 2.212 0 0 0 .004 3.12 2.197 2.197 0 0 0 1.534.648h.39v7.204A2.589 2.589 0 0 0 5.156 24h3.83a.703.703 0 0 0 .704-.703v-5.649c0-.65.529-1.18 1.18-1.18h2.259c.65 0 1.18.53 1.18 1.18v5.649c0 .388.314.703.702.703h3.83a2.589 2.589 0 0 0 2.587-2.586V14.21h.361c.59 0 1.145-.23 1.563-.648.86-.86.86-2.261 0-3.123Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),ue=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("g",{fill:"#000",clipPath:"url(#a)"},(0,e.createElement)("path",{d:"M2.768 18.719a4.668 4.668 0 0 1-2.296-.603.723.723 0 0 1-.288-1.013c.725-1.16.987-2.576.655-3.908C.495 11.815-.003 10.651 0 9.19.013 4.06 4.282-.098 9.406.002c4.949.1 9.027 4.26 9.027 9.21 0 6.465-6.775 11.004-12.757 8.508-.824.647-1.86.999-2.908.999Zm-.975-1.579c1.127.35 2.394.07 3.263-.77a.713.713 0 0 1 .803-.13c5.121 2.449 11.149-1.39 11.149-7.028 0-4.208-3.424-7.7-7.631-7.785-4.336-.086-7.94 3.426-7.951 7.766-.003 1.388.538 2.498.834 3.814a6.362 6.362 0 0 1-.467 4.133Z"}),(0,e.createElement)("path",{d:"M21.232 24a4.734 4.734 0 0 1-2.908-1c-3.181 1.328-6.965.724-9.573-1.529a.713.713 0 0 1 .931-1.079c2.314 1.998 5.702 2.447 8.459 1.13a.713.713 0 0 1 .803.13 3.305 3.305 0 0 0 3.263.77 6.335 6.335 0 0 1-.248-4.892c.41-.968.618-1.996.615-3.056-.004-1.87-.626-3.599-1.798-5.001a.713.713 0 1 1 1.094-.914A9.26 9.26 0 0 1 24 14.47a9.15 9.15 0 0 1-.719 3.594c-.503 1.459-.272 3.02.535 4.32a.723.723 0 0 1-.288 1.013 4.67 4.67 0 0 1-2.296.603ZM9.217 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM5.061 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255ZM13.373 10.375a1.128 1.128 0 1 0 0-2.255 1.128 1.128 0 0 0 0 2.255Z"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"})))),ce=()=>(0,e.createElement)("svg",{width:21,height:24,viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{d:"M6.2 14c2.3 2 5.3 2 7.8 0",stroke:"#344054",strokeWidth:"1.6"}),(0,e.createElement)("path",{d:"M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#344054"}),(0,e.createElement)("circle",{cx:"10.1",cy:"1.5",r:"1.5",fill:"#344054"})),de=()=>(0,e.createElement)("svg",{width:22,height:24,viewBox:"0 0 22 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"a",fill:"#fff"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"})),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",fill:"#00B682"}),(0,e.createElement)("path",{d:"m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",mask:"url(#a)"}),(0,e.createElement)("path",{d:"M11 7V1",stroke:"#00B682",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#00B682"}),(0,e.createElement)("circle",{cx:11,cy:"1.5",r:"1.5",fill:"#00B682"})),fe=({icon:t,active:n})=>{if(null!=ne?.[t])return(0,e.createElement)("img",{src:ne[t],width:"24",height:"24"});const r={home:{default:(0,e.createElement)(oe,null),active:(0,e.createElement)(se,null)},feedback_tab:{default:(0,e.createElement)(ue,null),active:(0,e.createElement)(ue,null)},resources:{default:(0,e.createElement)(ie,null),active:(0,e.createElement)(le,null)},chatbot:{default:(0,e.createElement)(ce,null),active:(0,e.createElement)(de,null)}};return n?r?.[t]?.active:r?.[t].default},he=(0,e.createContext)(),pe=()=>{const{Tabs:t,setTab:n,activeTabClass:r,setactiveTabClass:a}=(0,e.useContext)(he);return(0,e.createElement)("ul",{className:"betterdocs-ia-tabs"},t.filter((e=>1==e?.showTab&&"tab"==e?.type)).map((i=>(0,e.createElement)("li",{className:r==i?.id?i?.class+" active":i?.class,key:Math.random(),onClick:()=>(e=>{let r=t.find((t=>t?.id==e));"feedback_tab"!=e&&a(e),n(r?.id)})(i?.id)},i?.icon,(0,e.createElement)("p",null,i?.title)))))},me=()=>{const{selectedTab:t,Tabs:n}=(0,e.useContext)(he),r=n?.find((e=>e.id==t))?.component,a=n?.find((e=>e.id==t))?.require_components,i="home"==t?" home-content":"resources"==t?" resources-content":"";return(0,e.createElement)("div",{className:`betterdocs-ia-main-content${i}`},r&&(0,e.createElement)(r,a))};var ge=n(968);function ve(e){try{return decodeURIComponent(e)}catch(t){return e}}function ye(e){return(function(e){let t;try{t=new URL(e,"http://example.com").search.substring(1)}catch(e){}if(t)return t}(e)||"").replace(/\+/g,"%20").split("&").reduce(((e,t)=>{const[n,r=""]=t.split("=").filter(Boolean).map(ve);return n&&function(e,t,n){const r=t.length,a=r-1;for(let i=0;i<r;i++){let r=t[i];!r&&Array.isArray(e)&&(r=e.length.toString()),r=["__proto__","constructor","prototype"].includes(r)?r.toUpperCase():r;const l=!isNaN(Number(t[i+1]));e[r]=i===a?n:e[r]||(l?[]:{}),Array.isArray(e[r])&&!l&&(e[r]={...e[r]}),e=e[r]}}(e,n.replace(/\]/g,"").split("["),r),e}),Object.create(null))}function be(e){let t="";const n=Object.entries(e);let r;for(;r=n.shift();){let[e,a]=r;if(Array.isArray(a)||a&&a.constructor===Object){const t=Object.entries(a).reverse();for(const[r,a]of t)n.unshift([`${e}[${r}]`,a])}else void 0!==a&&(null===a&&(a=""),t+="&"+[e,a].map(encodeURIComponent).join("="))}return t.substr(1)}function we(e="",t){if(!t||!Object.keys(t).length)return e;let n=e;const r=e.indexOf("?");return-1!==r&&(t=Object.assign(ye(e),t),n=n.substr(0,r)),n+"?"+be(t)}const ke=({queryKey:e})=>{const[t,n]=e,{parent:r,taxonomy:a,terms:i,terms_per_page:o,term_id:s,post_type:u,post_id:c,post_ids:d,posts_order:f,posts_orderby:h,posts_per_page:p,terms_orderby:m,terms_order:g}=n;let v="wp/v2/",y={},b={hide_empty:!0};return null!=a?(v+=a,i?.length>0&&(b.include=i.join(",")),null!=r&&0==r&&(b.parent=0),null!=m&&(b.orderby=m),null!=g&&(b.order=g),null!=o&&(b.per_page=o),null!=s&&"doc_category"==a&&(v=v.replace(a,""),v+="docs",b[a]=s),null!=s&&"betterdocs_faq_category"==a&&(v=v.replace(a,""),v+="betterdocs_faq",b[a]=s),fetch(`${l}${we(v,b)}`,{method:"GET"}).then((e=>e.json()))):null!=u?(v+=u+"/",d?.length>0&&(y.include=d.join(",")),null!=c&&(v+=c),null!=f&&(y.order=f),null!=h&&(y.orderby=h),null!=p&&(y.per_page=p),fetch(`${l}${we(v,y)}`,{method:"GET"}).then((e=>e.json()))):void 0},Ee=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("path",{fill:"#fff",d:"M15.675 21.3 6.9 12.525a.762.762 0 0 1-.175-.25.734.734 0 0 1-.05-.275c0-.1.017-.192.05-.275a.762.762 0 0 1 .175-.25L15.675 2.7a.948.948 0 0 1 .7-.275c.283 0 .517.092.7.275.2.2.3.437.3.712 0 .275-.1.513-.3.713L9.2 12l7.875 7.875c.217.217.317.458.3.725a1.011 1.011 0 0 1-.3.675c-.2.2-.438.3-.712.3a.933.933 0 0 1-.688-.275Z"})),Ce=({layout:t})=>{const{Tabs:n,selectedTab:r,setNavigationHistory:a,setTab:i,setactiveTabClass:l,mainWrapperStyleRef:d}=(0,e.useContext)(he);let f="single-doc"==t?"content-icon-back":"header__back header__button";const{current:h}=d,{style:p}=h,m=()=>{const e=n.filter((e=>e.showTab&&"tab"===e.type));return e.length>0?e[0].id:null};return(0,e.createElement)("div",{className:f,onClick:()=>{if("resources"===r){const e=(()=>{const e={home:o||s,resources:z||k,chatbot:c,feedback_tab:u?.show},t=["home","resources","chatbot","feedback_tab"].filter((t=>"resources"!==t&&e[t]));return t.length>0?t[0]:m()})();if(e)return i(e),l(e),a([{id:e}]),void(""!=p?.width&&""!=p?.height&&Object.assign(p,{width:"",height:""}))}a((e=>{let t=[...e];if(t?.length>0){if(t.pop(),t?.length<1)if(o||s)t.push({id:"home"});else if(z||k)t.push({id:"resources"});else if(c)t.push({id:"chatbot"});else if(u?.show)t.push({id:"feedback_tab"});else{const e=m();e&&t.push({id:e})}let e=t[t.length-1];return i(e?.id),"home"!==e?.id&&"resources"!==e?.id&&"chatbot"!==e?.id&&"feedback_tab"!==e?.id||l(e?.id),t}return t})),""!=p?.width&&""!=p?.height&&Object.assign(p,{width:"",height:""})}},(0,e.createElement)(Ee,null))},xe=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16"},(0,e.createElement)("path",{d:"M7.29289 1.2876C3.98708 1.2876 1.29419 3.98048 1.29419 7.28631C1.29419 10.5921 3.98708 13.2902 7.29289 13.2902C8.7049 13.2902 10.0035 12.7954 11.0299 11.9738L13.5286 14.4712C13.6547 14.5921 13.8231 14.6588 13.9977 14.657C14.1724 14.6552 14.3394 14.5851 14.463 14.4617C14.5866 14.3382 14.657 14.1713 14.659 13.9967C14.661 13.822 14.5946 13.6535 14.4739 13.5272L11.9752 11.0285C12.7975 10.0006 13.2929 8.69995 13.2929 7.28631C13.2929 3.98048 10.5987 1.2876 7.29289 1.2876ZM7.29289 2.62095C9.87811 2.62095 11.9583 4.70108 11.9583 7.28631C11.9583 9.87153 9.87811 11.9569 7.29289 11.9569C4.70766 11.9569 2.62752 9.87153 2.62752 7.28631C2.62752 4.70108 4.70766 2.62095 7.29289 2.62095Z"})),Se=({onClick:t})=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",fill:"currentColor",stroke:"currentColor",strokeWidth:0,viewBox:"0 0 24 24",onClick:t},(0,e.createElement)("path",{stroke:"none",d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),_e=({searchCallback:t,searchKeyword:n})=>(0,e.createElement)("label",{className:"betterdocs-ia-search"},(0,e.createElement)("input",{className:"betterdocs-ia-search-field",onChange:e=>t(e?.target?.value),name:"betterdocs-ia-search",placeholder:h?.SEARCH_PLACEHOLDER,value:n}),(0,e.createElement)("div",{className:"betterdocs-ia-search-icon"},n?.length>0?(0,e.createElement)(Se,{onClick:()=>t("")}):(0,e.createElement)(xe,null))),Te=()=>(0,e.createElement)("svg",{width:"190",viewBox:"0 0 366 85",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M63.063 32.6198H44.0562C42.1943 32.6198 40.6873 31.1128 40.6873 29.2509C40.6873 27.389 42.1943 25.882 44.0562 25.882H63.063C64.9249 25.882 66.4319 27.389 66.4319 29.2509C66.4319 31.1128 64.9249 32.6198 63.063 32.6198Z",fill:"white"}),(0,e.createElement)("path",{d:"M55.4024 45.7461H36.4065C34.5446 45.7461 33.0376 44.2391 33.0376 42.3772C33.0376 40.5152 34.5446 39.0082 36.4065 39.0082H55.4024C57.2643 39.0082 58.7713 40.5152 58.7713 42.3772C58.7713 44.2391 57.2643 45.7461 55.4024 45.7461Z",fill:"white"}),(0,e.createElement)("path",{d:"M47.6545 59.1016H28.6476C26.7857 59.1016 25.2787 57.5946 25.2787 55.7327C25.2787 53.8708 26.7857 52.3638 28.6476 52.3638H47.6545C49.5164 52.3638 51.0234 53.8708 51.0234 55.7327C51.0234 57.5946 49.5164 59.1016 47.6545 59.1016Z",fill:"white"}),(0,e.createElement)("path",{d:"M77.0901 40.2531C76.97 40.002 76.8171 39.7727 76.6479 39.5597L81.7204 30.8235C84.4832 26.0731 84.4996 20.3891 81.7586 15.6224C79.0121 10.8502 74.0925 8 68.5832 8H45.6342C40.2559 8 35.2217 10.8993 32.497 15.5678L10.0831 54.1765C7.32026 58.9269 7.30388 64.6109 10.0449 69.3776C12.7913 74.1498 17.7109 77 23.2202 77H62.5443C69.5497 77 75.9599 73.4618 79.6892 67.5266C83.4185 61.5969 83.8334 54.2857 80.7976 47.9738L77.0901 40.2531ZM29.0189 69.8745H23.2148C20.2936 69.8745 17.6782 68.362 16.2203 65.8285C14.7679 63.3005 14.7734 60.2865 16.2422 57.7639L38.667 19.1496C40.114 16.6653 42.7894 15.1255 45.6451 15.1255H68.5941C71.5153 15.1255 74.1307 16.638 75.5886 19.1715C77.041 21.6995 77.0355 24.7135 75.5668 27.2361L53.131 65.8558C51.7004 68.3293 49.0359 69.869 46.1747 69.869H29.0189V69.8745Z",fill:"white"}),(0,e.createElement)("path",{d:"M124.384 42.1533C125.82 41.4325 126.913 40.4279 127.65 39.1338C128.387 37.8397 128.758 36.3819 128.758 34.7657C128.758 33.5044 128.534 32.3359 128.081 31.2603C127.633 30.1791 126.929 29.2455 125.979 28.4537C125.023 27.662 123.822 27.045 122.364 26.5918C120.906 26.1441 119.17 25.9148 117.155 25.9148H104.433C104.127 25.9148 103.882 26.1605 103.882 26.4662V59.4183C103.882 59.6749 104.056 59.8988 104.302 59.9534C105.672 60.2646 107.212 60.5212 108.927 60.7178C110.816 60.9362 112.82 61.0399 114.944 61.0399C117.641 61.0399 119.951 60.7997 121.878 60.3137C123.8 59.8278 125.378 59.1343 126.601 58.2389C127.824 57.3379 128.725 56.235 129.299 54.9191C129.872 53.6087 130.161 52.1399 130.161 50.5237C130.161 48.4707 129.659 46.7398 128.649 45.3147C127.639 43.895 126.219 42.8412 124.384 42.1587V42.1533ZM110.516 31.9209C110.516 31.6152 110.767 31.364 111.073 31.364H116.129C118.214 31.3312 119.732 31.708 120.688 32.4997C121.638 33.2914 122.118 34.3725 122.118 35.7376C122.118 37.1791 121.643 38.2984 120.688 39.112C119.732 39.9201 118.231 40.3241 116.183 40.3241H111.078C110.772 40.3241 110.521 40.0729 110.521 39.7672V31.9264L110.516 31.9209ZM121.605 54.4004C120.328 55.2631 118.269 55.6944 115.43 55.6944C114.387 55.6944 113.486 55.6671 112.732 55.6125C112.126 55.5689 111.531 55.487 110.952 55.3614C110.701 55.3068 110.521 55.0774 110.521 54.8208V46.0464C110.521 45.7406 110.767 45.4949 111.073 45.4949H116.669C119.006 45.4949 120.732 45.9426 121.851 46.8435C122.965 47.7445 123.522 48.9839 123.522 50.5674C123.522 52.26 122.883 53.5322 121.605 54.4004Z",fill:"white"}),(0,e.createElement)("path",{d:"M273.916 31.2275C272.278 29.715 270.302 28.5466 267.981 27.7221C265.66 26.8976 263.078 26.4826 260.239 26.4826H250.612C250.312 26.4826 250.066 26.7283 250.066 27.0287V60.5213C250.066 60.8216 250.312 61.0673 250.612 61.0673H260.239C263.078 61.0673 265.66 60.6523 267.981 59.8278C270.302 59.0033 272.278 57.8239 273.916 56.2951C275.554 54.7662 276.821 52.9426 277.722 50.8186C278.623 48.6946 279.071 46.3412 279.071 43.7531C279.071 41.165 278.623 38.8062 277.722 36.6877C276.821 34.5637 275.554 32.7509 273.916 31.2384V31.2275ZM271.541 48.5471C270.984 49.9504 270.176 51.1571 269.111 52.1617C268.052 53.1719 266.747 53.9527 265.202 54.5096C263.657 55.0666 261.877 55.345 259.862 55.345H257.257C256.952 55.345 256.706 55.0993 256.706 54.7935V32.7564C256.706 32.4506 256.952 32.2049 257.257 32.2049H259.862C261.877 32.2049 263.657 32.4833 265.202 33.0403C266.747 33.5972 268.052 34.3889 269.111 35.4155C270.171 36.442 270.979 37.6705 271.541 39.112C272.098 40.5535 272.376 42.1314 272.376 43.8623C272.376 45.5932 272.098 47.1548 271.541 48.558V48.5471Z",fill:"white"}),(0,e.createElement)("path",{d:"M305.798 38.7516C304.581 37.5995 303.172 36.7095 301.567 36.0816C299.961 35.4537 298.225 35.137 296.358 35.137C294.49 35.137 292.797 35.4591 291.176 36.1089C289.554 36.7586 288.14 37.665 286.944 38.8335C285.743 40.002 284.793 41.3779 284.089 42.9614C283.384 44.5448 283.029 46.2538 283.029 48.0885C283.029 50.0323 283.373 51.7959 284.061 53.3739C284.749 54.9573 285.699 56.3224 286.917 57.4745C288.135 58.6266 289.554 59.5166 291.176 60.1445C292.797 60.7724 294.528 61.0891 296.358 61.0891C298.187 61.0891 299.912 60.7669 301.517 60.1172C303.123 59.4674 304.531 58.572 305.749 57.4199C306.967 56.2678 307.928 54.8918 308.632 53.292C309.336 51.6922 309.691 49.9558 309.691 48.083C309.691 46.2102 309.347 44.4301 308.659 42.8467C307.971 41.2633 307.021 39.8982 305.804 38.7461L305.798 38.7516ZM302.675 50.8895C302.342 51.7522 301.861 52.5057 301.244 53.1555C300.627 53.8052 299.901 54.3076 299.077 54.6679C298.247 55.0283 297.34 55.2085 296.352 55.2085C295.364 55.2085 294.457 55.0283 293.627 54.6679C292.798 54.3076 292.077 53.8052 291.46 53.1555C290.843 52.5057 290.368 51.7522 290.029 50.8895C289.696 50.0268 289.527 49.0931 289.527 48.083C289.527 47.0729 289.696 46.1392 290.029 45.2765C290.362 44.4138 290.843 43.6603 291.46 43.0105C292.077 42.3608 292.798 41.8584 293.627 41.498C294.457 41.1377 295.364 40.9575 296.352 40.9575C297.34 40.9575 298.247 41.1377 299.077 41.498C299.907 41.8584 300.627 42.3608 301.244 43.0105C301.861 43.6603 302.336 44.4138 302.675 45.2765C303.008 46.1392 303.177 47.0729 303.177 48.083C303.177 49.0931 303.008 50.0268 302.675 50.8895Z",fill:"white"}),(0,e.createElement)("path",{d:"M331.603 54.7226C330.576 55.0447 329.31 55.2085 327.797 55.2085C326.683 55.2085 325.657 55.0283 324.723 54.668C323.789 54.3076 322.976 53.8162 322.293 53.1828C321.611 52.5549 321.081 51.7959 320.699 50.9168C320.322 50.0377 320.131 49.0931 320.131 48.083C320.131 47.0729 320.322 46.0791 320.699 45.1946C321.076 44.3155 321.605 43.5565 322.293 42.9286C322.976 42.3007 323.795 41.8038 324.75 41.4434C325.7 41.0831 326.754 40.9029 327.906 40.9029C329.31 40.9029 330.522 41.0722 331.548 41.4161C331.92 41.5417 332.264 41.6728 332.586 41.8147C332.952 41.9731 333.356 41.7165 333.356 41.3179V36.5402C333.356 36.3109 333.214 36.1034 333.001 36.027C332.264 35.7649 331.461 35.5574 330.582 35.4045C329.555 35.2243 328.414 35.137 327.158 35.137C325.253 35.137 323.473 35.47 321.818 36.1362C320.164 36.8023 318.733 37.7196 317.527 38.8881C316.32 40.0566 315.37 41.4216 314.665 42.9887C313.967 44.5557 313.612 46.2539 313.612 48.0885C313.612 49.9231 313.945 51.5775 314.611 53.161C315.277 54.7444 316.205 56.1204 317.39 57.2888C318.575 58.4573 320 59.3855 321.654 60.0681C323.309 60.7506 325.144 61.0946 327.158 61.0946C328.594 61.0946 329.79 60.9799 330.746 60.7451C331.537 60.5486 332.285 60.3356 332.99 60.1063C333.214 60.0353 333.361 59.8224 333.361 59.5876V54.8754C333.361 54.4878 332.968 54.2257 332.608 54.3731C332.296 54.4987 331.963 54.6188 331.608 54.728L331.603 54.7226Z",fill:"white"}),(0,e.createElement)("path",{d:"M245.299 35.137H244.382C242.438 35.137 240.8 35.5956 239.473 36.5129C238.141 37.4303 237.207 38.5714 236.667 39.9365L236.334 35.7376C236.312 35.4373 236.044 35.208 235.739 35.2353L230.884 35.6775C230.59 35.7048 230.371 35.9615 230.388 36.2563L230.835 42.9013V60.5049C230.835 60.8106 231.081 61.0564 231.387 61.0564H236.76C237.065 61.0564 237.311 60.8106 237.311 60.5049V48.4052C237.311 46.2812 237.884 44.5558 239.036 43.2235C240.189 41.8912 241.805 41.2251 243.891 41.2251C244.338 41.2251 244.77 41.2469 245.174 41.296C245.501 41.3343 245.78 41.0776 245.78 40.75V35.4919C245.78 35.4919 245.758 35.1261 245.294 35.1261L245.299 35.137Z",fill:"white"}),(0,e.createElement)("path",{d:"M223.481 54.0236C222.907 54.3458 222.296 54.6188 221.646 54.8317C220.996 55.0501 220.281 55.2194 219.489 55.345C218.698 55.4706 217.819 55.5361 216.847 55.5361C214.362 55.5361 212.424 54.9682 211.021 53.838C209.617 52.7023 208.809 51.2226 208.591 49.388C209.132 50.0377 209.978 50.5401 211.124 50.9004C212.277 51.2608 213.609 51.441 215.116 51.441C218.496 51.4082 221.111 50.6656 222.968 49.2296C224.819 47.7936 225.747 45.757 225.747 43.1306C225.747 40.8264 224.911 38.9209 223.241 37.4138C221.57 35.9014 219.113 35.1479 215.875 35.1479C213.898 35.1479 212.064 35.4919 210.371 36.1744C208.678 36.8569 207.215 37.8015 205.976 39.0082C204.736 40.2149 203.77 41.6345 203.087 43.2726C202.405 44.9106 202.061 46.6961 202.061 48.6399C202.061 50.5837 202.394 52.2709 203.06 53.8216C203.726 55.3668 204.654 56.6827 205.839 57.7584C207.024 58.8395 208.449 59.664 210.103 60.2427C211.758 60.8161 213.576 61.1054 215.553 61.1054C217.709 61.1054 219.544 60.8816 221.056 60.4284C222.422 60.0243 223.519 59.5657 224.354 59.0579C224.518 58.9596 224.606 58.7849 224.606 58.5938V54.3294C224.606 53.909 224.147 53.6414 223.781 53.8598C223.683 53.9199 223.579 53.9799 223.481 54.0345V54.0236ZM211.07 42.2898C212.435 41.1213 214.035 40.5371 215.869 40.5371C216.95 40.5371 217.813 40.7773 218.457 41.2633C219.107 41.7492 219.429 42.4426 219.429 43.3381C219.429 43.9879 219.238 44.5612 218.862 45.0635C218.485 45.5659 217.873 45.9808 217.027 46.303C216.181 46.6251 215.067 46.8599 213.68 47.0019C212.293 47.1438 210.595 47.1821 208.58 47.1111C208.869 45.0635 209.694 43.4528 211.064 42.2843L211.07 42.2898Z",fill:"white"}),(0,e.createElement)("path",{d:"M355.655 48.225C354.393 46.9855 352.253 46.0409 349.234 45.3912C348.049 45.14 347.077 44.9161 346.318 44.7141C345.564 44.5175 344.975 44.3101 344.565 44.0917C344.15 43.8732 343.866 43.6494 343.702 43.4146C343.539 43.1798 343.462 42.9013 343.462 42.5792C343.462 41.105 344.975 40.3678 347.994 40.3678C349.359 40.3678 350.719 40.5316 352.067 40.8538C352.974 41.0722 353.858 41.3834 354.721 41.782C355.081 41.9458 355.491 41.6783 355.491 41.2797V36.846C355.491 36.6222 355.36 36.4201 355.158 36.3328C354.202 35.9396 353.165 35.6448 352.04 35.4537C350.779 35.2353 349.43 35.1315 347.994 35.1315C346.378 35.1315 344.909 35.3117 343.599 35.6721C342.283 36.0325 341.153 36.5457 340.197 37.2119C339.242 37.878 338.515 38.6861 338.013 39.6416C337.511 40.5972 337.26 41.6455 337.26 42.7976C337.26 44.8506 337.909 46.4777 339.203 47.679C340.497 48.8857 342.55 49.7757 345.351 50.349C346.575 50.5674 347.574 50.7803 348.344 50.9987C349.119 51.2171 349.731 51.4465 350.178 51.6976C350.626 51.9488 350.932 52.2218 351.096 52.5057C351.259 52.7951 351.336 53.1337 351.336 53.5323C351.336 55.0775 349.698 55.8528 346.427 55.8528C344.811 55.8528 343.178 55.6017 341.546 55.0993C340.465 54.7662 339.433 54.3076 338.455 53.7343C338.089 53.5213 337.636 53.7834 337.636 54.2093V58.6976C337.636 58.905 337.751 59.1016 337.937 59.1944C339.209 59.8333 340.53 60.3028 341.895 60.6031C343.369 60.9253 345.007 61.0891 346.804 61.0891C350.042 61.0891 352.641 60.3957 354.601 59.0142C356.561 57.6274 357.544 55.7327 357.544 53.3193C357.544 51.1625 356.916 49.459 355.655 48.2195V48.225Z",fill:"white"}),(0,e.createElement)("path",{d:"M196.158 54.941C195.405 55.2304 194.433 55.3723 193.242 55.3723C192.052 55.3723 190.987 55.0229 190.141 54.3185C189.295 53.6196 188.874 52.402 188.874 50.6766V41.1814H196.961C197.261 41.1814 197.507 40.9357 197.507 40.6354V36.2236C197.507 35.9232 197.261 35.6775 196.961 35.6775H188.874V28.421C188.874 28.0934 188.585 27.8368 188.257 27.875L182.934 28.5466C182.661 28.5793 182.453 28.8141 182.453 29.0871L182.399 51.7522C182.399 54.8809 183.245 57.2179 184.932 58.7685C186.625 60.3138 188.869 61.0891 191.675 61.0891C193.04 61.0891 194.203 60.9635 195.154 60.7124C196.104 60.4612 196.89 60.1554 197.501 59.7951V59.7732V55.1921C197.501 54.7935 197.092 54.5315 196.732 54.6953C196.551 54.7772 196.36 54.8591 196.153 54.941H196.158Z",fill:"white"}),(0,e.createElement)("path",{d:"M176.212 54.941C175.459 55.2304 174.487 55.3723 173.297 55.3723C172.106 55.3723 171.041 55.0229 170.195 54.3185C169.349 53.6196 168.928 52.402 168.928 50.6766V41.1814H176.993C177.293 41.1814 177.539 40.9357 177.539 40.6354V36.229C177.539 35.9287 177.293 35.683 176.993 35.683H168.928V28.4264C168.928 28.0988 168.639 27.8422 168.311 27.8804L162.988 28.552C162.715 28.5848 162.507 28.8196 162.507 29.0926L162.453 51.7577C162.453 54.8864 163.299 57.2233 164.986 58.774C166.679 60.3192 168.923 61.0946 171.729 61.0946C173.094 61.0946 174.257 60.969 175.208 60.7178C176.005 60.5103 176.682 60.2592 177.239 59.9752C177.419 59.8824 177.523 59.6913 177.523 59.4893V55.214C177.523 54.8154 177.113 54.5478 176.747 54.7171C176.578 54.7935 176.392 54.87 176.201 54.9464L176.212 54.941Z",fill:"white"}),(0,e.createElement)("path",{d:"M155.819 54.0236C155.245 54.3458 154.634 54.6188 153.984 54.8317C153.334 55.0501 152.619 55.2194 151.827 55.345C151.035 55.4706 150.156 55.5361 149.184 55.5361C146.7 55.5361 144.762 54.9682 143.358 53.838C141.955 52.7023 141.147 51.2226 140.929 49.388C141.469 50.0377 142.316 50.5401 143.462 50.9004C144.614 51.2608 145.947 51.441 147.454 51.441C150.833 51.4082 153.449 50.6656 155.305 49.2296C157.156 47.7936 158.085 45.757 158.085 43.1306C158.085 40.8264 157.249 38.9209 155.578 37.4138C153.908 35.9014 151.45 35.1479 148.213 35.1479C146.236 35.1479 144.401 35.4919 142.709 36.1744C141.016 36.8569 139.553 37.8015 138.313 39.0082C137.074 40.2149 136.107 41.6345 135.425 43.2726C134.742 44.9106 134.398 46.6961 134.398 48.6399C134.398 50.5837 134.731 52.2709 135.398 53.8216C136.064 55.3668 136.992 56.6827 138.177 57.7584C139.362 58.8395 140.787 59.664 142.441 60.2427C144.096 60.8161 145.914 61.1054 147.89 61.1054C150.047 61.1054 151.882 60.8816 153.394 60.4284C154.765 60.0189 155.868 59.5602 156.703 59.0524C156.861 58.9541 156.954 58.7794 156.954 58.5883V54.3185C156.954 53.898 156.496 53.6305 156.135 53.8489C156.032 53.9144 155.928 53.9745 155.824 54.0345L155.819 54.0236ZM143.408 42.2898C144.773 41.1213 146.372 40.5371 148.207 40.5371C149.288 40.5371 150.151 40.7773 150.795 41.2633C151.445 41.7492 151.767 42.4426 151.767 43.3381C151.767 43.9879 151.576 44.5612 151.199 45.0635C150.823 45.5659 150.211 45.9808 149.365 46.303C148.518 46.6251 147.404 46.8599 146.018 47.0019C144.631 47.1438 142.933 47.1821 140.918 47.1111C141.207 45.0635 142.032 43.4528 143.402 42.2843L143.408 42.2898Z",fill:"white"})),Ne=({headingContent:t,headingLayout:n,headingSubContent:r,enableLogo:a,enableSearch:i,extraClass:l,enableBackButton:o,searchCallback:s,searchKeyword:u})=>{let c=null!=l?" "+l:"",d=t&&"home-layout"==n?(0,e.createElement)("h1",{className:"betterdocs-title"},t):!t||"resources-list"!=n&&"resources-category"!=n?"":(0,e.createElement)("h2",null,t),f=a?null==W||Array.isArray(W)?(0,e.createElement)("span",{className:"betterdocs-logo"},(0,e.createElement)(Te,null)):(0,e.createElement)("img",{src:W?.url,width:100,height:20}):"",h=r?(0,e.createElement)("p",{className:"betterdocs-info"},r):"",p=i?(0,e.createElement)(_e,{searchCallback:s,searchKeyword:u}):"",m=o?(0,e.createElement)(Ce,null):"";return(0,e.createElement)("div",{className:`betterdocs-ia-common-header${c}`},(0,e.createElement)("div",{className:"betterdocs-ia-header-group"},f,m,d,h),p)},ze=()=>(0,e.createElement)("div",{className:"generic-loader"},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:40,height:40,preserveAspectRatio:"xMidYMid",style:{background:"0 0",shapeRendering:"auto"},viewBox:"0 0 100 100"},(0,e.createElement)("circle",{cx:50,cy:50,r:26,fill:"none",stroke:"#16ca9e",strokeDasharray:"122.52211349000194 42.840704496667314",strokeWidth:10},(0,e.createElement)("animateTransform",{attributeName:"transform",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"}))));var Ae=n(957);const Re=(e,t)=>{let n,r,a=e.path;return"string"==typeof e.namespace&&"string"==typeof e.endpoint&&(n=e.namespace.replace(/^\/|\/$/g,""),r=e.endpoint.replace(/^\//,""),a=r?n+"/"+r:n),delete e.namespace,delete e.endpoint,t({...e,path:a})};function Oe(e){const t=e.split("?"),n=t[1],r=t[0];return n?r+"?"+n.split("&").map((e=>e.split("="))).map((e=>e.map(decodeURIComponent))).sort(((e,t)=>e[0].localeCompare(t[0]))).map((e=>e.map(encodeURIComponent))).map((e=>e.join("="))).join("&"):r}function Me(e,t){return Promise.resolve(t?e.body:new window.Response(JSON.stringify(e.body),{status:200,statusText:"OK",headers:e.headers}))}const Pe=({path:e,url:t,...n},r)=>({...n,url:t&&we(t,r),path:e&&we(e,r)}),Le=e=>e.json?e.json():Promise.reject(e),De=e=>{const{next:t}=(e=>{if(!e)return{};const t=e.match(/<([^>]+)>; rel="next"/);return t?{next:t[1]}:{}})(e.headers.get("link"));return t},Ie=async(e,t)=>{if(!1===e.parse)return t(e);if(!(e=>{const t=!!e.path&&-1!==e.path.indexOf("per_page=-1"),n=!!e.url&&-1!==e.url.indexOf("per_page=-1");return t||n})(e))return t(e);const n=await Ye({...Pe(e,{per_page:100}),parse:!1}),r=await Le(n);if(!Array.isArray(r))return r;let a=De(n);if(!a)return r;let i=[].concat(r);for(;a;){const t=await Ye({...e,path:void 0,url:a,parse:!1}),n=await Le(t);i=i.concat(n),a=De(t)}return i},Fe=new Set(["PATCH","PUT","DELETE"]),qe="GET";function Ue(e,t){return ye(e)[t]}function He(e,t){return void 0!==Ue(e,t)}const $e=(e,t=!0)=>Promise.resolve(((e,t=!0)=>t?204===e.status?null:e.json?e.json():Promise.reject(e):e)(e,t)).catch((e=>je(e,t)));function je(e,t=!0){if(!t)throw e;return(e=>{const t={code:"invalid_json",message:(0,ge.__)("The response is not a valid JSON response.")};if(!e||!e.json)throw t;return e.json().catch((()=>{throw t}))})(e).then((e=>{const t={code:"unknown_error",message:(0,ge.__)("An unknown error occurred.")};throw e||t}))}function Qe(e,...t){const n=e.indexOf("?");if(-1===n)return e;const r=ye(e),a=e.substr(0,n);t.forEach((e=>delete r[e]));const i=be(r);return i?a+"?"+i:a}const Be={Accept:"application/json, */*;q=0.1"},Ve={credentials:"include"},Ze=[(e,t)=>("string"!=typeof e.url||He(e.url,"_locale")||(e.url=we(e.url,{_locale:"user"})),"string"!=typeof e.path||He(e.path,"_locale")||(e.path=we(e.path,{_locale:"user"})),t(e)),Re,(e,t)=>{const{method:n=qe}=e;return Fe.has(n.toUpperCase())&&(e={...e,headers:{...e.headers,"X-HTTP-Method-Override":n,"Content-Type":"application/json"},method:"POST"}),t(e)},Ie],We=e=>{if(e.status>=200&&e.status<300)return e;throw e};let Ke=e=>{const{url:t,path:n,data:r,parse:a=!0,...i}=e;let{body:l,headers:o}=e;return o={...Be,...o},r&&(l=JSON.stringify(r),o["Content-Type"]="application/json"),window.fetch(t||n||window.location.href,{...Ve,...i,body:l,headers:o}).then((e=>Promise.resolve(e).then(We).catch((e=>je(e,a))).then((e=>$e(e,a)))),(e=>{if(e&&"AbortError"===e.name)throw e;throw{code:"fetch_error",message:(0,ge.__)("You are probably offline.")}}))};function Ge(e){return Ze.reduceRight(((e,t)=>n=>t(n,e)),Ke)(e).catch((t=>"rest_cookie_invalid_nonce"!==t.code?Promise.reject(t):window.fetch(Ge.nonceEndpoint).then(We).then((e=>e.text())).then((t=>(Ge.nonceMiddleware.nonce=t,Ge(e))))))}Ge.use=function(e){Ze.unshift(e)},Ge.setFetchHandler=function(e){Ke=e},Ge.createNonceMiddleware=function(e){const t=(e,n)=>{const{headers:r={}}=e;for(const a in r)if("x-wp-nonce"===a.toLowerCase()&&r[a]===t.nonce)return n(e);return n({...e,headers:{...r,"X-WP-Nonce":t.nonce}})};return t.nonce=e,t},Ge.createPreloadingMiddleware=function(e){const t=Object.fromEntries(Object.entries(e).map((([e,t])=>[Oe(e),t])));return(e,n)=>{const{parse:r=!0}=e;let a=e.path;if(!a&&e.url){const{rest_route:t,...n}=ye(e.url);"string"==typeof t&&(a=we(t,n))}if("string"!=typeof a)return n(e);const i=e.method||"GET",l=Oe(a);if("GET"===i&&t[l]){const e=t[l];return delete t[l],Me(e,!!r)}if("OPTIONS"===i&&t[i]&&t[i][l]){const e=t[i][l];return delete t[i][l],Me(e,!!r)}return n(e)}},Ge.createRootURLMiddleware=e=>(t,n)=>Re(t,(t=>{let r,a=t.url,i=t.path;return"string"==typeof i&&(r=e,-1!==e.indexOf("?")&&(i=i.replace("?","&")),i=i.replace(/^\//,""),"string"==typeof r&&-1!==r.indexOf("?")&&(i=i.replace("?","&")),a=r+i),n({...t,url:a})})),Ge.fetchAllMiddleware=Ie,Ge.mediaUploadMiddleware=(e,t)=>{if(!function(e){const t=!!e.method&&"POST"===e.method;return(!!e.path&&-1!==e.path.indexOf("/wp/v2/media")||!!e.url&&-1!==e.url.indexOf("/wp/v2/media"))&&t}(e))return t(e);let n=0;const r=e=>(n++,t({path:`/wp/v2/media/${e}/post-process`,method:"POST",data:{action:"create-image-subsizes"},parse:!1}).catch((()=>n<5?r(e):(t({path:`/wp/v2/media/${e}?force=true`,method:"DELETE"}),Promise.reject()))));return t({...e,parse:!1}).catch((t=>{const n=t.headers.get("x-wp-upload-attachment-id");return t.status>=500&&t.status<600&&n?r(n).catch((()=>!1!==e.parse?Promise.reject({code:"post_process",message:(0,ge.__)("Media upload failed. If this is a photo or a large image, please scale it down and try again.")}):Promise.reject(t))):je(t,e.parse)})).then((t=>$e(t,e.parse)))},Ge.createThemePreviewMiddleware=e=>(t,n)=>{if("string"==typeof t.url){const n=Ue(t.url,"wp_theme_preview");void 0===n?t.url=we(t.url,{wp_theme_preview:e}):""===n&&(t.url=Qe(t.url,"wp_theme_preview"))}if("string"==typeof t.path){const n=Ue(t.path,"wp_theme_preview");void 0===n?t.path=we(t.path,{wp_theme_preview:e}):""===n&&(t.path=Qe(t.path,"wp_theme_preview"))}return n(t)};const Ye=Ge;function Xe(e,t){const n=`wp/v2/${e}`,r={search:t};return"docs"==e&&(r.orderby="relevance"),Ye({url:`${l}${we(n,r)}`})}function Je(e,t){const n={s:e};t&&(n.no_result=1);try{return Ye({url:`${l}${we("betterdocs/v1/search-insert",n)}`})}catch(e){return console.error("Please update BetterDocs free version: ",e),!1}}const et=(t,n,r)=>{const a=((t,n=500)=>{const[r,a]=(0,e.useState)(t);return(0,e.useEffect)((()=>{let e=setTimeout((()=>{a(t)}),n);return()=>{clearTimeout(e)}}),[t,n]),r})(t,300),[i,l]=(0,e.useState)([]),[o,s]=(0,e.useState)(!1),[u,c]=(0,e.useState)(!1);return(0,e.useEffect)((()=>{0!==a.length?(s(!0),async function(){var e,i,o;switch(!0){case n&&r:c(!0),e=Xe("docs",a),i=Xe("betterdocs_faq",a),o=await Promise.all([e,i]);let[s,u]=o;a.length>=p&&(s.length>0?await Je(t,0):await Je(t,1)),l([...s,...u]),c(!1);break;case n:c(!0),e=await Xe("docs",a),a.length>=p&&(e.length?Je(a,0):Je(a,1)),l([...e]),c(!1);break;case r:c(!0),i=await Xe("betterdocs_faq",a),a.length>=p&&(i.length?Je(a,0):Je(a,1)),l([...i]),c(!1)}}()):s(!1)}),[a]),[o,u,i]},tt=({DocList:t})=>{const[n,r]=(0,e.useState)(""),{termInfo:a}=(0,e.useContext)(he),{term_id:i,taxonomy:l,term_name:o}=a,[s,u,c]=et(n,z,k),{data:d,isLoading:f,isSuccess:p}=(0,Ae.useQuery)({queryKey:["taxDocs",{taxonomy:l,term_id:i,terms_per_page:100}],queryFn:ke,staleTime:1/0});return(0,e.createElement)(e.Fragment,null,(0,e.createElement)(Ne,{enableBackButton:!0,headingContent:V,headingLayout:"resources-list",enableSearch:!0,extraClass:"list-page-header",searchCallback:r,searchKeyword:n}),f&&!s&&(0,e.createElement)(ze,null),p&&!s&&(0,e.createElement)(t,{docList:d,headingContent:o,contentType:"post_type"}),u&&s&&(0,e.createElement)(ze,null),!u&&s&&(0,e.createElement)(t,{docList:c,contentType:"post_type",noContent:h?.OOPS,noSubContent:h?.NOT_FOUND}))},nt=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:67,height:14,fill:"none"},(0,e.createElement)("path",{fill:"#00B682",d:"M10.871 5.14H7.293a.634.634 0 1 1 0-1.268h3.578a.634.634 0 1 1 0 1.268ZM9.429 7.611H5.853a.634.634 0 1 1 0-1.268h3.576a.634.634 0 1 1 0 1.268ZM7.97 10.125H4.392a.634.634 0 1 1 0-1.268H7.97a.634.634 0 1 1 0 1.268Z"}),(0,e.createElement)("path",{fill:"#00B682",d:"M13.511 6.577a.685.685 0 0 0-.083-.13l.955-1.645c.52-.894.523-1.964.007-2.861A2.83 2.83 0 0 0 11.91.506H7.59c-1.012 0-1.96.546-2.473 1.424L.898 9.198a2.829 2.829 0 0 0-.007 2.861 2.83 2.83 0 0 0 2.48 1.435h7.402A3.79 3.79 0 0 0 14 11.711a3.79 3.79 0 0 0 .209-3.68l-.698-1.454Zm-9.049 5.576H3.37c-.55 0-1.042-.285-1.317-.762a1.5 1.5 0 0 1 .004-1.518l4.221-7.268a1.526 1.526 0 0 1 1.314-.758h4.32c.55 0 1.042.285 1.316.762a1.5 1.5 0 0 1-.004 1.518l-4.223 7.27c-.27.465-.77.755-1.31.755H4.463Z"}),(0,e.createElement)("path",{fill:"#16342F",d:"M22.413 6.935c.27-.136.476-.325.615-.569.139-.243.208-.518.208-.822 0-.237-.042-.457-.127-.66a1.361 1.361 0 0 0-.396-.528 1.967 1.967 0 0 0-.68-.35 3.358 3.358 0 0 0-.98-.128h-2.396a.104.104 0 0 0-.103.104v6.203c0 .048.033.09.079.1.258.059.548.107.87.144.356.041.733.06 1.133.06.508 0 .942-.044 1.305-.136.362-.091.66-.222.89-.39.23-.17.4-.378.507-.625.108-.247.162-.523.162-.828 0-.386-.094-.712-.284-.98a1.667 1.667 0 0 0-.803-.594v-.001Zm-2.61-1.926c0-.058.047-.105.104-.105h.952c.393-.006.678.065.858.214.18.149.27.352.27.61 0 .27-.09.481-.27.634-.18.152-.462.228-.848.228h-.96a.105.105 0 0 1-.105-.104V5.01l-.002-.001ZM21.89 9.24c-.24.162-.628.244-1.163.244-.196 0-.365-.005-.507-.016a2.392 2.392 0 0 1-.335-.047.104.104 0 0 1-.082-.102V7.668c0-.058.047-.104.104-.104h1.054c.44 0 .765.084.975.254.21.17.315.402.315.7 0 .32-.12.559-.361.722ZM50.56 4.878a3.204 3.204 0 0 0-1.117-.66 4.331 4.331 0 0 0-1.457-.233h-1.812a.103.103 0 0 0-.103.103v6.304c0 .057.047.103.103.103h1.812c.535 0 1.02-.078 1.458-.233a3.168 3.168 0 0 0 1.117-.665c.308-.288.547-.631.716-1.031.17-.4.254-.843.254-1.33 0-.487-.084-.931-.254-1.33-.17-.4-.408-.741-.716-1.026v-.002Zm-.446 3.26a1.87 1.87 0 0 1-.458.68c-.2.19-.445.338-.736.443-.29.104-.626.157-1.005.157h-.49a.103.103 0 0 1-.104-.104V5.166c0-.058.046-.104.104-.104h.49c.38 0 .715.053 1.005.157.291.105.537.254.736.447.2.194.352.425.458.696.104.272.157.569.157.894 0 .326-.053.62-.157.884v-.002ZM56.561 6.294a2.439 2.439 0 0 0-.796-.502 2.668 2.668 0 0 0-.98-.178c-.352 0-.67.06-.976.183a2.407 2.407 0 0 0-1.334 1.29c-.133.298-.2.62-.2.965 0 .366.065.698.195.995.13.298.308.555.537.772.23.217.497.384.802.502.305.119.63.178.975.178s.67-.06.971-.183c.303-.122.568-.29.797-.508a2.406 2.406 0 0 0 .742-1.758c0-.352-.065-.687-.194-.985a2.324 2.324 0 0 0-.538-.772v.001Zm-.588 2.285a1.287 1.287 0 0 1-.269.427 1.243 1.243 0 0 1-.92.386 1.233 1.233 0 0 1-.92-.386 1.303 1.303 0 0 1-.271-.427 1.453 1.453 0 0 1-.095-.528c0-.19.032-.366.095-.528.063-.163.153-.305.27-.427a1.233 1.233 0 0 1 .92-.386 1.233 1.233 0 0 1 .92.386c.117.122.207.264.27.427.063.162.095.338.095.528s-.032.366-.095.528ZM61.419 9.3a2.42 2.42 0 0 1-.717.092c-.21 0-.402-.034-.578-.102a1.41 1.41 0 0 1-.458-.28 1.247 1.247 0 0 1-.3-.426 1.34 1.34 0 0 1-.107-.533c0-.19.036-.377.107-.544.071-.165.17-.308.3-.426a1.41 1.41 0 0 1 .463-.28c.179-.068.377-.102.594-.102.264 0 .492.032.685.097.07.024.135.048.196.075a.103.103 0 0 0 .145-.094v-.899a.102.102 0 0 0-.067-.096 2.773 2.773 0 0 0-.455-.118 3.744 3.744 0 0 0-.645-.05c-.358 0-.694.063-1.005.188-.311.125-.58.298-.808.518-.227.22-.406.477-.538.772a2.329 2.329 0 0 0-.199.96 2.413 2.413 0 0 0 .712 1.732c.223.22.49.394.802.523.312.128.657.193 1.036.193.27 0 .496-.021.675-.066.15-.037.29-.077.423-.12a.102.102 0 0 0 .07-.098V9.33a.103.103 0 0 0-.142-.094 2.348 2.348 0 0 1-.188.067l-.001-.001ZM45.173 5.614h-.172c-.366 0-.675.086-.924.259-.251.173-.427.388-.529.644l-.062-.79a.103.103 0 0 0-.112-.094l-.914.083a.103.103 0 0 0-.094.109l.085 1.25v3.314c0 .058.046.104.103.104h1.012a.104.104 0 0 0 .104-.104V8.112c0-.4.108-.725.324-.976.217-.25.522-.376.914-.376.084 0 .166.004.242.013a.102.102 0 0 0 .114-.102v-.99s-.004-.069-.092-.069l.001.002ZM41.066 9.17c-.108.06-.223.111-.345.151a2.505 2.505 0 0 1-.406.097 3.183 3.183 0 0 1-.498.036c-.467 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.262.216.477.284.217.068.468.102.752.102.636-.006 1.128-.146 1.478-.416.348-.27.523-.654.523-1.148 0-.434-.157-.793-.472-1.076-.315-.285-.777-.427-1.386-.427-.373 0-.718.065-1.036.193a2.525 2.525 0 0 0-.828.534 2.393 2.393 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.126.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.654.162 1.026.162.406 0 .751-.042 1.036-.127.257-.076.463-.163.62-.258a.1.1 0 0 0 .048-.088v-.802c0-.08-.086-.13-.155-.089a1.6 1.6 0 0 1-.057.033V9.17ZM38.73 6.96c.257-.22.558-.33.904-.33.203 0 .365.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.346.233c-.159.06-.369.105-.63.131a7.16 7.16 0 0 1-.96.021c.055-.385.21-.689.468-.909l.001.001ZM65.946 8.078c-.237-.234-.64-.411-1.209-.534-.223-.047-.406-.09-.548-.127a1.58 1.58 0 0 1-.33-.117.448.448 0 0 1-.163-.128.267.267 0 0 1-.045-.157c0-.278.285-.416.853-.416.257 0 .513.03.767.091.17.041.337.1.5.175.067.03.144-.02.144-.095v-.834a.105.105 0 0 0-.063-.097 2.745 2.745 0 0 0-.586-.165 4.468 4.468 0 0 0-.762-.061c-.304 0-.58.034-.827.102a2.06 2.06 0 0 0-.64.29 1.28 1.28 0 0 0-.412.457c-.094.18-.142.377-.142.594 0 .386.123.693.366.919.244.227.63.395 1.158.502.23.042.418.082.563.123.146.04.26.084.345.131a.443.443 0 0 1 .173.152c.03.055.045.119.045.194 0 .29-.308.436-.924.436-.304 0-.612-.047-.919-.141a2.746 2.746 0 0 1-.581-.257.102.102 0 0 0-.155.09v.844c0 .039.022.076.057.094.24.12.488.208.745.265.277.06.586.091.924.091.61 0 1.099-.13 1.468-.39.369-.261.554-.618.554-1.072 0-.406-.119-.727-.356-.96ZM35.923 9.342a1.554 1.554 0 0 1-.549.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.522a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.103h-1.522V4.35a.104.104 0 0 0-.116-.103l-1.002.126a.103.103 0 0 0-.09.102l-.011 4.267c0 .589.16 1.028.477 1.32.319.291.74.437 1.27.437a2.6 2.6 0 0 0 .654-.07c.179-.048.327-.106.442-.174V9.39a.102.102 0 0 0-.145-.093l-.109.046h.001ZM32.17 9.342a1.553 1.553 0 0 1-.55.081.893.893 0 0 1-.584-.198c-.159-.132-.238-.361-.238-.686V6.752h1.518a.103.103 0 0 0 .103-.103v-.83a.103.103 0 0 0-.103-.102h-1.518V4.35a.104.104 0 0 0-.116-.103l-1.002.127a.103.103 0 0 0-.09.101l-.01 4.267c0 .589.158 1.028.476 1.32.319.291.741.437 1.27.437a2.6 2.6 0 0 0 .654-.07 1.9 1.9 0 0 0 .382-.14.102.102 0 0 0 .054-.092v-.805a.103.103 0 0 0-.146-.093 2.609 2.609 0 0 1-.103.043l.002-.001ZM28.33 9.17a1.93 1.93 0 0 1-.345.151 2.504 2.504 0 0 1-.406.097c-.15.024-.315.036-.498.036-.468 0-.832-.107-1.096-.32a1.221 1.221 0 0 1-.458-.837c.102.122.261.216.477.284.217.068.468.102.751.102.637-.006 1.129-.146 1.478-.416s.523-.654.523-1.148c0-.434-.157-.793-.471-1.076-.315-.285-.777-.427-1.387-.427-.372 0-.717.065-1.036.193a2.524 2.524 0 0 0-.827.534 2.391 2.391 0 0 0-.544.802 2.598 2.598 0 0 0-.193 1.01c0 .367.063.684.188.976.125.291.3.539.523.741a2.3 2.3 0 0 0 .803.468c.311.108.653.162 1.025.162.407 0 .752-.042 1.037-.127.258-.077.465-.164.622-.26a.102.102 0 0 0 .048-.087v-.803c0-.08-.087-.13-.154-.089-.02.013-.04.024-.06.035V9.17Zm-2.336-2.21c.257-.22.558-.33.903-.33.204 0 .366.046.487.137a.459.459 0 0 1 .183.39c0 .123-.036.231-.107.326a.754.754 0 0 1-.345.233c-.16.06-.369.105-.63.131-.261.027-.58.034-.96.021.055-.385.21-.689.468-.909v.001Z"})),rt=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,fill:"none"},(0,e.createElement)("path",{fill:"#16CA9E",d:"M23.995.901c-.001-.015-.004-.029-.006-.043L23.98.805l-.013-.05-.011-.045-.017-.048-.016-.045-.02-.043-.022-.045-.024-.04-.026-.044L23.8.402l-.027-.036a.993.993 0 0 0-.065-.073h-.001a.999.999 0 0 0-.073-.066L23.598.2l-.043-.032c-.014-.01-.029-.017-.043-.026l-.04-.024-.046-.022-.043-.02-.045-.016-.048-.017-.045-.012-.05-.012-.053-.008L23.1.005A1.022 1.022 0 0 0 23 0h-7a1 1 0 1 0 0 2h4.586l-7.293 7.293a1 1 0 0 0 1.414 1.414L22 3.414V8a1 1 0 1 0 2 0V1c0-.033-.002-.066-.005-.099ZM9.293 13.293 2 20.586V16a1 1 0 1 0-2 0v7c0 .033.002.066.005.099l.006.043.008.053.013.05.011.045.017.048.016.045.02.043.022.045.024.04.026.044.032.043.027.036c.02.025.042.05.065.072v.001h.001c.024.024.048.046.073.066l.036.027c.014.01.028.022.043.031.014.01.029.018.043.027l.04.024.046.022.043.02.045.016.048.017.045.011c.017.005.033.01.05.013l.053.008.043.006C.934 23.998.967 24 1 24h7a1 1 0 1 0 0-2H3.414l7.293-7.293a1 1 0 1 0-1.414-1.414Z"})),at=()=>{const{mainWrapperStyleRef:t}=(0,e.useContext)(he),{current:n}=t;return(0,e.createElement)("span",{className:"content-icon-expand",onClick:()=>{let{style:e}=n,{width:t,height:r}=e;""===t&&""===r?Object.assign(e,{width:"686px",height:"800px"}):Object.assign(e,{width:"",height:""})}},(0,e.createElement)(rt,null))},it=({scrollData:t,scrollTitle:n,contentTitle:r})=>{const{current:a}=r,i=null!=a?.offsetHeight?a?.offsetHeight:0;return(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-header"+(t>i?" on-scroll":"")},(0,e.createElement)(Ce,{layout:"single-doc"}),t>i&&(0,e.createElement)("h2",null,n),(0,e.createElement)(at,null))},lt=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-neutral",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-neutral-icon",viewBox:"0 0 20 20","data-reaction":"normal"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zM6.6 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.8.1-1.5-.6-1.5-1.4 0-.9.7-1.6 1.5-1.6zm7.7 8H5.7c-.3 0-.6-.3-.6-.6s.3-.6.6-.6h8.5c.3 0 .6.3.6.6.1.3-.2.6-.5.6zm-1-4.9c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5c.1.8-.6 1.5-1.5 1.5z"}))),ot=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-sad",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-sad-icon",viewBox:"0 0 20 20","data-reaction":"sad"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm7.5 8.6c-.2 0-.4-.1-.5-.2-.9-1.1-2.2-1.8-3.7-1.8s-2.8.7-3.7 1.7c-.2.2-.3.3-.5.3-.6 0-.9-.7-.5-1.1 1.2-1.3 2.9-2.2 4.7-2.2 1.8 0 3.6.8 4.7 2.2.4.4.1 1.1-.5 1.1z",className:"st-path"}))),st=({clickHandler:t})=>(0,e.createElement)("div",{className:"ia-reaction ia-happy",onClick:t},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",xmlSpace:"preserve",className:"betterdocs-emo betterdocs-happy-icon",viewBox:"0 0 20 20","data-reaction":"happy"},(0,e.createElement)("path",{d:"M10 0C4.5 0 0 4.5 0 10s4.5 10 10 10 10-4.5 10-10S15.5 0 10 0zm3.3 6.4c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-6.6 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5c-.9.1-1.6-.6-1.6-1.4 0-.9.7-1.6 1.6-1.6zm3.3 9.9c-2.6 0-5-1.6-5.9-4.1l1.2-.5c.7 1.9 2.6 3.2 4.6 3.2s3.9-1.3 4.6-3.2l1.2.5c-.7 2.4-3.1 4.1-5.7 4.1z"}))),ut=({sadOnclick:t,neutralOnclick:n,happyOnClick:r,reactionText:a})=>{let i=j==a?"":" success";return(0,e.createElement)("div",{className:`betterdocs-ia-footer-feedback${i}`},(0,e.createElement)("p",null,a),j==a&&(0,e.createElement)("div",{className:"betterdocs-ia-reaction-group"},(0,e.createElement)(st,{clickHandler:r}),(0,e.createElement)(ot,{clickHandler:t}),(0,e.createElement)(lt,{clickHandler:n})))},ct=()=>{const{singleDocInfo:t,feedbackText:n,setFeedbackText:r}=(0,e.useContext)(he),{doc_id:a,post_type:i}=t,[l,o]=(0,e.useState)(0),[s,u]=(0,e.useState)(""),c=(0,e.useRef)(null),{data:d,isLoading:f}=(0,Ae.useQuery)({queryKey:["docContent",{post_type:i,post_id:a}],queryFn:ke,staleTime:1/0});function h(e,t){fetch(`${H}/${e}?feelings=${t}`,{method:"POST"}).then((e=>e.json())).then((e=>{r(e?$:"Oops! Something went wrong with your reaction."),setTimeout((()=>{r(j)}),3e3)}))}return(0,e.createElement)(e.Fragment,null,f?(0,e.createElement)(ze,null):(0,e.createElement)("div",{className:"betterdocs-ia-single-docs-wrapper",onScroll:e=>{o(e?.target?.scrollTop),u(d?.title?.rendered)}},(0,e.createElement)(it,{scrollData:l,scrollTitle:s,contentTitle:c}),(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-content"},(0,e.createElement)("h4",{className:"doc-title",dangerouslySetInnerHTML:{__html:d?.title?.rendered},ref:c}),(0,e.createElement)("div",{dangerouslySetInnerHTML:{__html:d?.content?.rendered}})),(0,e.createElement)("div",{className:"betterdocs-ia-singleDoc-footer"},U&&"betterdocs_faq"!=i&&(0,e.createElement)(ut,{sadOnclick:e=>{h(a,"sad")},neutralOnclick:e=>{h(a,"normal")},happyOnClick:e=>{h(a,"happy")},reactionText:n}),ee&&(0,e.createElement)("div",{className:"betterdocs-ia-footer-group"},(0,e.createElement)("h5",null,(0,ge.__)("Powered by","betterdocs-pro")),(0,e.createElement)("span",{className:"logoFooter"},(0,e.createElement)("a",{href:"https://betterdocs.co/",target:"_blank"},(0,e.createElement)(nt,null)))))))},dt=()=>(0,e.createElement)(ct,null),ft=n.p+"images/nodoc.c8afc2a7.png",ht=({content:t,subContent:n})=>{const{setTab:r}=(0,e.useContext)(he);return(0,e.createElement)("div",{className:"betterdocs-ia-no-doc"},(0,e.createElement)("img",{src:ft,alt:"nodoc"}),t&&(0,e.createElement)("h4",null,t),n&&(0,e.createElement)("p",null,n),(0,e.createElement)("button",{onClick:()=>{r("feedback_form")}},(0,ge.__)("Contact Us","betterdocs-pro")))},pt=({DocList:t})=>{const[n,r]=(0,e.useState)(""),[a,i,l]=et(n,z,k);let o,s,u=[{querykey:"docDefault",queryParams:{taxonomy:"doc_category",terms_per_page:5,terms_order:R,terms_orderby:O,parent:1==M?1:0},queryFn:ke,queryRenderCondition:z&&0==A.length},{querykey:"docPerPage",queryParams:{taxonomy:"doc_category",terms_per_page:100,terms_order:R,terms_orderby:O,parent:1==M?1:0},queryFn:ke,queryRenderCondition:z&&A.includes("all")},{querykey:"docTerms",queryParams:{taxonomy:"doc_category",terms:A,terms_order:R,terms_orderby:O},queryFn:ke,queryRenderCondition:z&&!A.includes("all")&&A.length>0}],c=[{querykey:"faqDefault",queryParams:{post_type:"betterdocs_faq",posts_per_page:5,posts_order:N,posts_orderby:T},queryFn:ke,queryRenderCondition:k&&"faq-list"===E&&0==x.length},{querykey:"faqAll",queryParams:{post_type:"betterdocs_faq",posts_per_page:100,posts_order:N,posts_orderby:T},queryFn:ke,queryRenderCondition:k&&"faq-list"===E&&x.includes("all")},{querykey:"faqSpecificList",queryParams:{post_type:"betterdocs_faq",post_ids:x,posts_order:N,posts_orderby:T},queryFn:ke,queryRenderCondition:k&&"faq-list"===E&&!x.includes("all")},{queryKey:"faqListPerPage",queryParams:{taxonomy:"betterdocs_faq_category",terms_per_page:5,terms_order:S,terms_orderby:_},queryFn:ke,queryRenderCondition:k&&"faq-group"===E&&0==C.length},{queryKey:"faqListPerPage",queryParams:{taxonomy:"betterdocs_faq_category",terms_per_page:100,terms_order:S,terms_orderby:_},queryFn:ke,queryRenderCondition:k&&"faq-group"===E&&C.includes("all")},{queryKey:"faqSpecficTerms",queryParams:{taxonomy:"betterdocs_faq_category",terms:C,terms_order:S,terms_orderby:_},queryFn:ke,queryRenderCondition:k&&"faq-group"===E&&!C.includes("all")}];for(const e of u)if(e?.queryRenderCondition){s=(0,Ae.useQuery)({queryKey:[e?.querykey,e?.queryParams],queryFn:e?.queryFn,staleTime:1/0,enabled:z});break}for(const e of c)if(e?.queryRenderCondition){o=(0,Ae.useQuery)({queryKey:[e?.querykey,e?.queryParams],queryFn:e?.queryFn,staleTime:1/0,enabled:k});break}return(0,e.createElement)("div",{className:"betterdocs-ia-tab-faq-content"},(0,e.createElement)(Ne,{headingLayout:"resources-category",headingContent:V,enableSearch:!0,extraClass:"resources-page-header",searchCallback:r,searchKeyword:n,enableBackButton:!0}),a&&i&&(0,e.createElement)(ze,null),a&&!i&&(0,e.createElement)(t,{docList:l,contentType:"post_type",borderRadius:!0,noContent:h?.OOPS,noSubContent:h?.NOT_FOUND}),!z&&k&&o?.isLoading&&!a&&(0,e.createElement)(ze,null),!k&&z&&s?.isLoading&&!a&&(0,e.createElement)(ze,null),z&&k&&s?.isLoading&&o?.isLoading&&!a&&(0,e.createElement)(ze,null),z&&!a&&s?.isSuccess&&s?.data?.length>0&&(0,e.createElement)(t,{docList:s?.data,headingContent:b,contentType:"taxonomy",borderRadius:!0}),k&&o?.isSuccess&&!a&&o?.data?.length>0&&(0,e.createElement)(t,{docList:o?.data,headingContent:w,contentType:"faq-list"==E?"post_type":"taxonomy",borderRadius:!0}),z&&k&&0==o?.data?.length&&0==s?.data?.length&&!a&&(0,e.createElement)(ht,{content:(0,ge.__)("No Faq & Doc Categories Available","betterdocs-pro"),subContent:(0,ge.__)("There are no categories available","betterdocs-pro")}),z&&!k&&0==s?.data?.length&&!a&&(0,e.createElement)(ht,{content:(0,ge.__)("No Doc Categories Available","betterdocs-pro"),subContent:(0,ge.__)("There are no categories available","betterdocs-pro")}),!z&&k&&0==o?.data?.length&&!a&&(0,e.createElement)(ht,{content:(0,ge.__)("No FAQ Available","betterdocs-pro"),subContent:(0,ge.__)("There are no FAQs available","betterdocs-pro")}),!z&&!k&&!a&&(0,e.createElement)(ht,{content:(0,ge.__)("No Content Available","betterdocs-pro"),subContent:(0,ge.__)("Please enable Doc Categories or FAQ in settings","betterdocs-pro")}))},mt=({DocList:t})=>{const[n,r]=(0,e.useState)(""),[a,i,l]=et(n,o,!1),[u,c,d]=et(n,!1,s),{data:p,isLoading:m,isSuccess:g}=(0,Ae.useQuery)({queryKey:["homeContent"],queryFn:()=>fetch(P,{method:"GET"}).then((e=>e.json())),staleTime:1/0,enabled:o}),{data:v,isLoading:y,isSuccess:b}=(0,Ae.useQuery)({queryKey:["homeFaqContent"],queryFn:()=>fetch(f?.FAQ_URL,{method:"GET"}).then((e=>e.json())),staleTime:1/0,enabled:s});return(0,e.createElement)("div",{className:"betterdocs-ia-tab-home-content"},(0,e.createElement)(Ne,{headingContent:Q,headingSubContent:B,headingLayout:"home-layout",enableLogo:!1,enableSearch:!0,extraClass:"home-page-header",searchCallback:r,searchKeyword:n}),(0,e.createElement)("div",{className:"betterdocs-ia-tab-main-content"},(0,e.createElement)("div",{className:"betterdocs-ia-home-content-list"},(o&&m||s&&y)&&!a&&(0,e.createElement)(ze,null),o&&g&&!a&&p?.length>0&&(0,e.createElement)(t,{docList:p,headingContent:F,borderRadius:!0,contentType:q,noContent:"post_type"==q?(0,ge.__)("No Docs Available","betterdocs-pro"):(0,ge.__)("No Doc Categories Found","betterdocs-pro")}),s&&b&&!a&&v?.length>0&&(0,e.createElement)(t,{docList:v,headingContent:(0,ge.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"faq-list"==f.HOME_FAQ_CONTENT_TYPE?"post_type":"taxonomy",noContent:(0,ge.__)("No FAQ Available","betterdocs-pro")}),!a&&(o&&s&&g&&b&&0==p?.length&&0==v?.length||o&&!s&&g&&0==p?.length||!o&&s&&b&&0==v?.length)&&(0,e.createElement)(ht,{content:o&&s?"post_type"==q?(0,ge.__)("No Docs & FAQ Available","betterdocs-pro"):(0,ge.__)("No Doc Categories & FAQ Found","betterdocs-pro"):o?"post_type"==q?(0,ge.__)("No Docs Available","betterdocs-pro"):(0,ge.__)("No Doc Categories Found","betterdocs-pro"):(0,ge.__)("No FAQ Available","betterdocs-pro")}),(a&&i||u&&c)&&(0,e.createElement)(ze,null),o&&s&&a&&u&&!i&&!c&&l?.length>0&&d?.length>0&&(0,e.createElement)(e.Fragment,null,(0,e.createElement)(t,{docList:l,headingContent:(0,ge.__)(F,"betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:h?.OOPS,noSubContent:h?.NOT_FOUND}),(0,e.createElement)(t,{docList:d,headingContent:(0,ge.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:h?.OOPS,noSubContent:h?.NOT_FOUND})),o&&a&&!i&&(s&&u&&!c&&l?.length>0&&0==d?.length||!s&&l?.length>0)&&(0,e.createElement)(t,{docList:l,headingContent:(0,ge.__)(I,"betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:h?.OOPS,noSubContent:h?.NOT_FOUND}),s&&u&&!c&&(o&&a&&!i&&0==l?.length&&d?.length>0||!o&&d?.length>0)&&(0,e.createElement)(t,{docList:d,headingContent:(0,ge.__)("FAQ","betterdocs-pro"),borderRadius:!0,contentType:"post_type",noContent:h?.OOPS,noSubContent:h?.NOT_FOUND}),(o&&a&&!i&&0==l?.length||!o)&&(s&&u&&!c&&0==d?.length||!s)&&(a||u)&&(0,e.createElement)(ht,{content:h?.OOPS,subContent:h?.NOT_FOUND}))))};let gt;function vt(e){if("string"!=typeof e||-1===e.indexOf("&"))return e;void 0===gt&&(gt=document.implementation&&document.implementation.createHTMLDocument?document.implementation.createHTMLDocument("").createElement("textarea"):document.createElement("textarea")),gt.innerHTML=e;const t=gt.textContent;return gt.innerHTML="",t}const yt=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:8,height:16,fill:"none"},(0,e.createElement)("path",{fill:"#00B682",d:"M.94.97a.81.81 0 0 0-.514.173.991.991 0 0 0-.332.479 1.126 1.126 0 0 0-.025.608c.048.2.15.38.291.512l5.401 5.242-5.401 5.24a.97.97 0 0 0-.24.304 1.127 1.127 0 0 0-.08.795c.034.132.094.254.173.358.079.105.176.19.286.25a.79.79 0 0 0 .706.03.882.882 0 0 0 .301-.223L7.69 8.744a.997.997 0 0 0 .229-.342 1.113 1.113 0 0 0 0-.838.998.998 0 0 0-.229-.342l-6.184-6A.828.828 0 0 0 .941.97Z"})),bt=(0,e.memo)((({docList:t,headingContent:n,borderRadius:r,contentType:a,noContent:i,noSubContent:l})=>{const{setTab:o,setSingleDocInfo:s,setTermInfo:u}=(0,e.useContext)(he);return(0,e.createElement)(e.Fragment,null,t?.length>0?(0,e.createElement)("div",{className:"betterdocs-ia-docs"+(r?" radius-layout":"")},n&&(0,e.createElement)("div",{className:"betterdocs-ia-docs-heading"},(0,e.createElement)("h4",{className:"doc-title"},(0,ge.__)(vt(n),"betterdocs-pro"))),t?.map((t=>(0,e.createElement)("div",{className:"betterdocs-ia-docs-content",onClick:()=>{return e=t,"post_type"==(n=a)&&(o("single_doc_view"),s({doc_id:e?.id,post_type:e.hasOwnProperty("doc_category")?"docs":"betterdocs_faq"})),void("taxonomy"==n&&(o("doc_list"),u({term_name:e?.name,term_id:e?.id,taxonomy:e?.taxonomy})));var e,n},key:Math?.random()},(0,e.createElement)("div",{className:"content-item"},(0,e.createElement)("h4",{dangerouslySetInnerHTML:{__html:"taxonomy"==a?vt(t?.name):t?.title?.rendered}}),"taxonomy"!=a&&(0,e.createElement)("p",null,vt(t?.excerpt?.rendered?.replace(/<[^>]+>/g,"")?.replace("[&hellip;]",""))?.split(" ")?.slice(0,10)?.join(" "))),(0,e.createElement)("div",{className:"content-icon"},(0,e.createElement)(yt,null)))))):(0,e.createElement)(ht,{content:i,subContent:l}))})),wt=n.p+"images/messageLoader.d2dc7c6a.gif",kt=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{stroke:"#475467",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.667,d:"M13.334 13.333 10 10m0 0-3.333 3.333M10 10v7.5m6.992-2.175A4.167 4.167 0 0 0 15 7.5h-1.05A6.667 6.667 0 1 0 2.5 13.583"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h20v20H0z"})))),Et=({className:t,onClick:n})=>(0,e.createElement)("svg",{className:t,xmlns:"http://www.w3.org/2000/svg",width:20,height:25,fill:"none",onClick:n},(0,e.createElement)("path",{stroke:"#667085",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.667,d:"M2.5 5h1.667m0 0H17.5M4.167 5v11.667a1.667 1.667 0 0 0 1.666 1.666h8.334a1.667 1.667 0 0 0 1.666-1.666V5H4.167Zm2.5 0V3.333a1.667 1.667 0 0 1 1.666-1.666h3.334a1.667 1.667 0 0 1 1.666 1.666V5m-5 4.167v5m3.334-5v5"})),Ct=()=>(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:25,fill:"none"},(0,e.createElement)("path",{stroke:"#01BAB4",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.333,d:"M8.666 1.333H4a1.333 1.333 0 0 0-1.333 1.334v10.666A1.333 1.333 0 0 0 4 14.667h8a1.333 1.333 0 0 0 1.333-1.334V6M8.666 1.333 13.333 6M8.666 1.333V6h4.667"})),xt=(0,e.forwardRef)((({fileName:t,fileSize:n,fileID:r,fileDatas:a,formState:i},l)=>(0,e.createElement)(e.Fragment,null,(0,e.createElement)("div",{className:"betterdocs-ia-progress-wrapper",ref:l},(0,e.createElement)("div",{className:"progress-content-wrapper"},(0,e.createElement)("div",{className:"progress-icon-details"},(0,e.createElement)(Ct,null),(0,e.createElement)("div",{className:"progress-details"},(0,e.createElement)("h5",{className:"brand-info-title"},t),(0,e.createElement)("span",{className:"brand-info-sub"},n+"mb"))),(0,e.createElement)(Et,{className:"progress-remove-icon",onClick:()=>{let e=a.filter(((e,t)=>t!=r));i((t=>({...t,fileData:[...e]})))}})))))),St=n.p+"images/msg-receive.fe68435f.png",_t=()=>(0,e.createElement)("div",{className:"betterdocs-ia-msg-receive"},(0,e.createElement)("img",{src:St,alt:"receiveImg"}),(0,e.createElement)("h4",null,(0,ge.__)(v?.title)),(0,e.createElement)("p",null,(0,ge.__)(v?.text))),Tt=({enableBackButton:t,heading:n,subHeading:r})=>{let a=t?(0,e.createElement)(Ce,null):"";return(0,e.createElement)("div",{className:"message__header betterdocs-ia-common-header"},(0,e.createElement)("div",{className:"betterdocs-ia-header-group"},a,(0,e.createElement)("div",{className:"header__content"},n?(0,e.createElement)("h4",null,n):null,r?(0,e.createElement)("p",null,r):null)))},Nt=()=>{const{feedbackFormInfo:t,setFeedbackFormInfo:n,validation:r,setValidation:a,messageStatus:i,setMessageStatus:l,buttonStatus:o,setButtonStatus:s}=(0,e.useContext)(he),c=(0,e.useRef)(),d=(0,e.useRef)(),{current:f}=c,{name:h,subject:p,message:v,email:y,fileData:b}=t,{message:w,fieldType:k}=r;function E(e){let t,n,r=["png","gif","pdf","jpeg","jpg"];for(let i of e)if(n=i?.name?.split(".")[0],t=i?.name?.split(".")[i?.name?.split(".")?.length-1].toLowerCase(),!r.includes(t))return a((e=>({...e,message:`Only ${r.join(", ")} are allowed`,fieldType:"file"}))),!1;return!0}function C(e){let t;for(let n of e)if(t=Math.ceil(n?.size/1048576),t>5)return a((e=>({...e,message:"Max file size limit 5 mb",fieldType:"file"}))),!1;return!0}function x(e){const{payload:t,type:r}=e,{target:a}=t,{value:i}=a;switch(r){case"setEmail":n((e=>({...e,email:i})));break;case"setName":n((e=>({...e,name:i})));break;case"setSubject":n((e=>({...e,subject:i})));break;case"setMessage":n((e=>({...e,message:i})));break;case"setFileData":n((e=>({...e,fileData:t})))}}return(0,e.useEffect)((()=>{d?.current?.scrollIntoView({behavior:"smooth",block:"end",inline:"nearest"})}),[b.length]),(0,e.createElement)("div",{className:"betterdocs-ia-tab-message-container"},(0,e.createElement)(Tt,{enableBackButton:!0,heading:(0,ge.__)(u?.subtitle),subHeading:(0,ge.__)(u?.subtitle_two)}),"idle"==i?(0,e.createElement)("div",{className:"betterdocs-ia-feedback-form"},(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-email-group ${"email"==k&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,m?.EMAIL),(0,e.createElement)("input",{className:"ia-input",name:"ia-email",onChange:e=>x({payload:e,type:"setEmail"})}),"email"==k&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,w))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-name-group ${"name"==k&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,m?.NAME),(0,e.createElement)("input",{className:"ia-input",name:"ia-name",onChange:e=>x({payload:e,type:"setName"})}),"name"==k&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,w))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-subject-group ${"subject"==k&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,m?.SUBJECT),(0,e.createElement)("input",{className:"ia-input",name:"ia-subject",onChange:e=>x({payload:e,type:"setSubject"})}),"subject"==k&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,w))),(0,e.createElement)("div",{className:`betterdocs-ia-group betterdocs-ia-message-group ${"message"==k&&"betterdocs-ia-warning-group"}`},(0,e.createElement)("p",null,m?.TEXTAREA),(0,e.createElement)("textarea",{className:"ia-message",name:"ia-message",onChange:e=>x({payload:e,type:"setMessage"})}),"message"==k&&(0,e.createElement)("span",{className:"warning-text"},(0,e.createElement)("p",null,w))),g&&(0,e.createElement)("div",{className:"betterdocs-ia-attachments-group",onDragOver:function(e){e.preventDefault()},onDragLeave:function(e){e.preventDefault()},onDrop:function(e){e.preventDefault();const{files:t}=e.dataTransfer;E(t)&&C(t)&&(a((e=>({...e,message:"",fieldType:""}))),n((e=>{const{fileData:n}=e;return n.push(...t),{...e,fileData:[...n]}})))}},(0,e.createElement)("button",{onClick:e=>{f?.click()}},(0,e.createElement)(kt,null)),(0,e.createElement)("p",null,(0,ge.__)("Click To Upload Or Drag and Drop","betterdocs-pro")),(0,e.createElement)("p",null,m?.ATTACHMENT),(0,e.createElement)("input",{type:"file",name:"ia-upload-file",multiple:!0,accept:"image/png, image/gif, image/jpeg, image/jpg, .pdf",style:{display:"none"},ref:c,onChange:function(e){const{files:t}=e.target;E(t)&&C(t)&&(""!=w&&a((e=>({...e,message:"",fieldType:""}))),n((e=>{const{fileData:n}=e;return n.push(...t),{...e,fileData:[...n]}})))}})),"file"==k&&(0,e.createElement)("div",{className:"betterdocs-ia-group"+("message"===k?" betterdocs-ia-warning-group":"")},(0,e.createElement)("span",{className:"warning-text file-warning"},(0,e.createElement)("p",null,w))),(0,e.createElement)("div",{className:"betterdocs-ia-submit"},(0,e.createElement)("button",{type:"submit",onClick:()=>function(){let e=/^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/;switch(!0){case 0===y.length:return void a((e=>({...e,message:`${m?.EMAIL} Field Must Not Be Empty`,fieldType:"email"})));case!e.test(y):return void a((e=>({...e,message:"Email Address Invalid",fieldType:"email"})));case 0===h.length:return void a((e=>({...e,message:`${m?.NAME} Field Must Not Be Empty`,fieldType:"name"})));case 0===p.length:return void a((e=>({...e,message:`${m?.SUBJECT} Field Must Not Be Empty`,fieldType:"subject"})));case 0===v.length:return void a((e=>({...e,message:"Text Area Field Must Not Be Empty",fieldType:"message"})))}a((e=>({...e,message:"",fieldType:""}))),s("submitting"),function(){let e=new FormData;e.append("name",h),e.append("email",y),e.append("subject",p),e.append("message",v);for(let t=0;t<b.length;t++)e.append("file[]",b[t]);fetch(betterdocs?.ASK_URL,{method:"POST",body:e}).then((e=>e.json())).then((e=>{e&&(l("sent"),setTimeout((()=>{l("idle"),s("idle"),n({email:"",name:"",subject:"",message:"",fileData:[]})}),5e3))})).catch((e=>{console.error(e)})),a((e=>({...e,message:"",fieldType:""})))}()}()},"submitting"==o?(0,e.createElement)("img",{src:wt,className:"submit-loader",height:20,width:25}):"",(0,e.createElement)("span",null,"idle"==o?m?.SEND:m?.SENDING)))):(0,e.createElement)(_t,null),b.length>0&&"idle"==i?b.map(((t,r)=>{let a=t?.name,i=Math.ceil(t?.size/1048576);return(0,e.createElement)(xt,{fileName:a,fileSize:i,key:r,fileID:r,fileDatas:b,formState:n,ref:d})})):"")},zt=()=>{const[t,n]=(0,e.useState)(void 0),[r,i]=(0,e.useState)({taxonomy:"doc_category"}),l=(0,e.useRef)(null),[d,f]=(0,e.useState)([]),[h,p]=(0,e.useState)({}),[m,g]=(0,e.useState)({email:"",name:"",subject:"",message:"",fileData:[]}),[v,y]=(0,e.useState)({message:"",fieldType:""}),[b,w]=(0,e.useState)("home"),[E,C]=(0,e.useState)("idle"),[x,S]=(0,e.useState)("idle"),[_,T]=(0,e.useState)(j),N=[{id:"home",class:"betterdocs-ia-home",type:"tab",title:null!=te&&""!=te?te:(0,ge.__)("Home","betterdocs"),default:!0,icon:(0,e.createElement)(fe,{active:"home"===b,icon:"home"}),component:mt,require_components:{DocList:bt},showTab:o||s,showTabInComponent:!0},...c?(0,a.W5)("tab_chatbot_preview",[],b):[],{id:"feedback_tab",class:"betterdocs-ia-message",type:"tab",title:""!=u?.label&&null!=u?.label?u?.label:(0,ge.__)("Message","betterdocs"),default:!1,icon:(0,e.createElement)(fe,{active:"feedback_tab"===b,icon:"feedback_tab"}),component:Nt,showTab:u?.show,showTabInComponent:!1},{id:"resources",class:"betterdocs-ia-faq",type:"tab",title:""!=Z&&null!=Z?Z:(0,ge.__)("Resources","betterdocs"),default:!1,icon:(0,e.createElement)(fe,{active:"resources"===b,icon:"resources"}),component:pt,require_components:{DocList:bt},showTab:z||k,showTabInComponent:!0},{id:"feedback_form",class:"",type:"page",title:"",default:!1,icon:"",component:Nt,showTab:!1},{id:"single_doc_view",class:"",type:"page",title:"",default:!1,icon:"",component:dt,showTab:!1},{id:"doc_list",class:"",type:"page",title:"",default:!1,icon:"",require_components:{DocList:bt},component:tt,showTab:!0,showTabInComponent:!0}],A=()=>{const e=N?.find((e=>e?.default&&e?.showTab));if(e)n(e.id);else{const e=N?.find((e=>e?.showTab));e&&n(e.id)}};(0,e.useEffect)((()=>{A()}),[]),(0,e.useEffect)((()=>{A()}),[o,s,z,k]),(0,e.useEffect)((()=>{d.includes(t)||f((e=>{if("home"===t)return[{id:"home"}];if("resources"===t)return[{id:"resources"}];if("chatbot"===t)return[{id:"chatbot"}];if("resources"!=t&&!d.includes(t)||"home"!=t&&!d.includes(t)){let n=[...e];return n[n.length-1]?.id!=t&&n.push({id:t}),n}}))}),[t]);const R=N?.find((e=>e?.id==t));return(0,e.createElement)("div",{className:"betterdocs-ia-main-wrapper",ref:l},(0,e.createElement)(he.Provider,{value:{Tabs:N,selectedTab:t,setTab:n,termInfo:r,setTermInfo:i,singleDocInfo:h,setSingleDocInfo:p,mainWrapperStyleRef:l,setNavigationHistory:f,navigationHistory:d,feedbackFormInfo:m,setFeedbackFormInfo:g,validation:v,setValidation:y,activeTabClass:b,setactiveTabClass:w,messageStatus:E,setMessageStatus:C,buttonStatus:x,setButtonStatus:S,feedbackText:_,setFeedbackText:T}},(0,e.createElement)(e.Fragment,null,(0,e.createElement)(me,null),R?.showTab&&R?.showTabInComponent&&N.filter((e=>e?.showTab&&"tab"===e?.type)).length>1?(0,e.createElement)(pe,null):"")))};var At=n(576);const Rt=document.getElementById("betterdocs-ia");(0,At.H)(Rt).render((0,e.createElement)((()=>{const[t,n]=(0,e.useState)(!1),r=new Ae.QueryClient;return(0,e.useEffect)((()=>{let e=e=>{"Escape"==e?.key&&t&&n(!1)};return document.addEventListener("keydown",e,!1),()=>{document.removeEventListener("keydown",e,!1)}}),[t]),(0,e.createElement)(Ae.QueryClientProvider,{client:r},(0,e.createElement)("div",{className:"betterdocs-ia-root"},t&&(0,e.createElement)(zt,null),(0,e.createElement)(ae,{toggleState:t,setToggleState:n})))}),null))})(),(()=>{"use strict";var e=n(758),t=n(803),r=n(968);const{entries:a,setPrototypeOf:i,isFrozen:l,getPrototypeOf:o,getOwnPropertyDescriptor:s}=Object;let{freeze:u,seal:c,create:d}=Object,{apply:f,construct:h}="undefined"!=typeof Reflect&&Reflect;u||(u=function(e){return e}),c||(c=function(e){return e}),f||(f=function(e,t,n){return e.apply(t,n)}),h||(h=function(e,t){return new e(...t)});const p=z(Array.prototype.forEach),m=z(Array.prototype.lastIndexOf),g=z(Array.prototype.pop),v=z(Array.prototype.push),y=z(Array.prototype.splice),b=z(String.prototype.toLowerCase),w=z(String.prototype.toString),k=z(String.prototype.match),E=z(String.prototype.replace),C=z(String.prototype.indexOf),x=z(String.prototype.trim),S=z(Object.prototype.hasOwnProperty),_=z(RegExp.prototype.test),T=(N=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(N,t)});var N;function z(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return f(e,t,r)}}function A(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b;i&&i(e,null);let r=t.length;for(;r--;){let a=t[r];if("string"==typeof a){const e=n(a);e!==a&&(l(t)||(t[r]=e),a=e)}e[a]=!0}return e}function R(e){for(let t=0;t<e.length;t++)S(e,t)||(e[t]=null);return e}function O(e){const t=d(null);for(const[n,r]of a(e))S(e,n)&&(Array.isArray(r)?t[n]=R(r):r&&"object"==typeof r&&r.constructor===Object?t[n]=O(r):t[n]=r);return t}function M(e,t){for(;null!==e;){const n=s(e,t);if(n){if(n.get)return z(n.get);if("function"==typeof n.value)return z(n.value)}e=o(e)}return function(){return null}}const P=u(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),L=u(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),D=u(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),I=u(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),F=u(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),q=u(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=u(["#text"]),H=u(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),$=u(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),j=u(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Q=u(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=c(/\{\{[\w\W]*|[\w\W]*\}\}/gm),V=c(/<%[\w\W]*|[\w\W]*%>/gm),Z=c(/\$\{[\w\W]*/gm),W=c(/^data-[\-\w.\u00B7-\uFFFF]+$/),K=c(/^aria-[\-\w]+$/),G=c(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Y=c(/^(?:\w+script|data):/i),X=c(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=c(/^html$/i),ee=c(/^[a-z][.\w]*(-[.\w]+)+$/i);var te=Object.freeze({__proto__:null,ARIA_ATTR:K,ATTR_WHITESPACE:X,CUSTOM_ELEMENT:ee,DATA_ATTR:W,DOCTYPE_NAME:J,ERB_EXPR:V,IS_ALLOWED_URI:G,IS_SCRIPT_OR_DATA:Y,MUSTACHE_EXPR:B,TMPLIT_EXPR:Z});const ne=function(){return"undefined"==typeof window?null:window};var re=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ne();const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)return n.isSupported=!1,n;let{document:r}=t;const i=r,l=i.currentScript,{DocumentFragment:o,HTMLTemplateElement:s,Node:c,Element:f,NodeFilter:h,NamedNodeMap:N=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:z,DOMParser:R,trustedTypes:B}=t,V=f.prototype,Z=M(V,"cloneNode"),W=M(V,"remove"),K=M(V,"nextSibling"),Y=M(V,"childNodes"),X=M(V,"parentNode");if("function"==typeof s){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let ee,re="";const{implementation:ae,createNodeIterator:ie,createDocumentFragment:le,getElementsByTagName:oe}=r,{importNode:se}=i;let ue={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof a&&"function"==typeof X&&ae&&void 0!==ae.createHTMLDocument;const{MUSTACHE_EXPR:ce,ERB_EXPR:de,TMPLIT_EXPR:fe,DATA_ATTR:he,ARIA_ATTR:pe,IS_SCRIPT_OR_DATA:me,ATTR_WHITESPACE:ge,CUSTOM_ELEMENT:ve}=te;let{IS_ALLOWED_URI:ye}=te,be=null;const we=A({},[...P,...L,...D,...F,...U]);let ke=null;const Ee=A({},[...H,...$,...j,...Q]);let Ce=Object.seal(d(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),xe=null,Se=null,_e=!0,Te=!0,Ne=!1,ze=!0,Ae=!1,Re=!0,Oe=!1,Me=!1,Pe=!1,Le=!1,De=!1,Ie=!1,Fe=!0,qe=!1,Ue=!0,He=!1,$e={},je=null;const Qe=A({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Be=null;const Ve=A({},["audio","video","img","source","image","track"]);let Ze=null;const We=A({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Ke="http://www.w3.org/1998/Math/MathML",Ge="http://www.w3.org/2000/svg",Ye="http://www.w3.org/1999/xhtml";let Xe=Ye,Je=!1,et=null;const tt=A({},[Ke,Ge,Ye],w);let nt=A({},["mi","mo","mn","ms","mtext"]),rt=A({},["annotation-xml"]);const at=A({},["title","style","font","a","script"]);let it=null;const lt=["application/xhtml+xml","text/html"];let ot=null,st=null;const ut=r.createElement("form"),ct=function(e){return e instanceof RegExp||e instanceof Function},dt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!st||st!==e){if(e&&"object"==typeof e||(e={}),e=O(e),it=-1===lt.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,ot="application/xhtml+xml"===it?w:b,be=S(e,"ALLOWED_TAGS")?A({},e.ALLOWED_TAGS,ot):we,ke=S(e,"ALLOWED_ATTR")?A({},e.ALLOWED_ATTR,ot):Ee,et=S(e,"ALLOWED_NAMESPACES")?A({},e.ALLOWED_NAMESPACES,w):tt,Ze=S(e,"ADD_URI_SAFE_ATTR")?A(O(We),e.ADD_URI_SAFE_ATTR,ot):We,Be=S(e,"ADD_DATA_URI_TAGS")?A(O(Ve),e.ADD_DATA_URI_TAGS,ot):Ve,je=S(e,"FORBID_CONTENTS")?A({},e.FORBID_CONTENTS,ot):Qe,xe=S(e,"FORBID_TAGS")?A({},e.FORBID_TAGS,ot):O({}),Se=S(e,"FORBID_ATTR")?A({},e.FORBID_ATTR,ot):O({}),$e=!!S(e,"USE_PROFILES")&&e.USE_PROFILES,_e=!1!==e.ALLOW_ARIA_ATTR,Te=!1!==e.ALLOW_DATA_ATTR,Ne=e.ALLOW_UNKNOWN_PROTOCOLS||!1,ze=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ae=e.SAFE_FOR_TEMPLATES||!1,Re=!1!==e.SAFE_FOR_XML,Oe=e.WHOLE_DOCUMENT||!1,Le=e.RETURN_DOM||!1,De=e.RETURN_DOM_FRAGMENT||!1,Ie=e.RETURN_TRUSTED_TYPE||!1,Pe=e.FORCE_BODY||!1,Fe=!1!==e.SANITIZE_DOM,qe=e.SANITIZE_NAMED_PROPS||!1,Ue=!1!==e.KEEP_CONTENT,He=e.IN_PLACE||!1,ye=e.ALLOWED_URI_REGEXP||G,Xe=e.NAMESPACE||Ye,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,rt=e.HTML_INTEGRATION_POINTS||rt,Ce=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ct(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ce.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ct(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ce.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ce.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ae&&(Te=!1),De&&(Le=!0),$e&&(be=A({},U),ke=[],!0===$e.html&&(A(be,P),A(ke,H)),!0===$e.svg&&(A(be,L),A(ke,$),A(ke,Q)),!0===$e.svgFilters&&(A(be,D),A(ke,$),A(ke,Q)),!0===$e.mathMl&&(A(be,F),A(ke,j),A(ke,Q))),e.ADD_TAGS&&(be===we&&(be=O(be)),A(be,e.ADD_TAGS,ot)),e.ADD_ATTR&&(ke===Ee&&(ke=O(ke)),A(ke,e.ADD_ATTR,ot)),e.ADD_URI_SAFE_ATTR&&A(Ze,e.ADD_URI_SAFE_ATTR,ot),e.FORBID_CONTENTS&&(je===Qe&&(je=O(je)),A(je,e.FORBID_CONTENTS,ot)),Ue&&(be["#text"]=!0),Oe&&A(be,["html","head","body"]),be.table&&(A(be,["tbody"]),delete xe.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw T('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ee=e.TRUSTED_TYPES_POLICY,re=ee.createHTML("")}else void 0===ee&&(ee=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}}(B,l)),null!==ee&&"string"==typeof re&&(re=ee.createHTML(""));u&&u(e),st=e}},ft=A({},[...L,...D,...I]),ht=A({},[...F,...q]),pt=function(e){v(n.removed,{element:e});try{X(e).removeChild(e)}catch(t){W(e)}},mt=function(e,t){try{v(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){v(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Le||De)try{pt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){let t=null,n=null;if(Pe)e="<remove></remove>"+e;else{const t=k(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===it&&Xe===Ye&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=ee?ee.createHTML(e):e;if(Xe===Ye)try{t=(new R).parseFromString(a,it)}catch(e){}if(!t||!t.documentElement){t=ae.createDocument(Xe,"template",null);try{t.documentElement.innerHTML=Je?re:a}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),Xe===Ye?oe.call(t,Oe?"html":"body")[0]:Oe?t.documentElement:i},vt=function(e){return ie.call(e.ownerDocument||e,e,h.SHOW_ELEMENT|h.SHOW_COMMENT|h.SHOW_TEXT|h.SHOW_PROCESSING_INSTRUCTION|h.SHOW_CDATA_SECTION,null)},yt=function(e){return e instanceof z&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof N)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},bt=function(e){return"function"==typeof c&&e instanceof c};function wt(e,t,r){p(e,(e=>{e.call(n,t,r,st)}))}const kt=function(e){let t=null;if(wt(ue.beforeSanitizeElements,e,null),yt(e))return pt(e),!0;const r=ot(e.nodeName);if(wt(ue.uponSanitizeElement,e,{tagName:r,allowedTags:be}),Re&&e.hasChildNodes()&&!bt(e.firstElementChild)&&_(/<[/\w!]/g,e.innerHTML)&&_(/<[/\w!]/g,e.textContent))return pt(e),!0;if(7===e.nodeType)return pt(e),!0;if(Re&&8===e.nodeType&&_(/<[/\w]/g,e.data))return pt(e),!0;if(!be[r]||xe[r]){if(!xe[r]&&Ct(r)){if(Ce.tagNameCheck instanceof RegExp&&_(Ce.tagNameCheck,r))return!1;if(Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(r))return!1}if(Ue&&!je[r]){const t=X(e)||e.parentNode,n=Y(e)||e.childNodes;if(n&&t)for(let r=n.length-1;r>=0;--r){const a=Z(n[r],!0);a.__removalCount=(e.__removalCount||0)+1,t.insertBefore(a,K(e))}}return pt(e),!0}return e instanceof f&&!function(e){let t=X(e);t&&t.tagName||(t={namespaceURI:Xe,tagName:"template"});const n=b(e.tagName),r=b(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===Ge?t.namespaceURI===Ye?"svg"===n:t.namespaceURI===Ke?"svg"===n&&("annotation-xml"===r||nt[r]):Boolean(ft[n]):e.namespaceURI===Ke?t.namespaceURI===Ye?"math"===n:t.namespaceURI===Ge?"math"===n&&rt[r]:Boolean(ht[n]):e.namespaceURI===Ye?!(t.namespaceURI===Ge&&!rt[r])&&!(t.namespaceURI===Ke&&!nt[r])&&!ht[n]&&(at[n]||!ft[n]):!("application/xhtml+xml"!==it||!et[e.namespaceURI]))}(e)?(pt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!_(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ae&&3===e.nodeType&&(t=e.textContent,p([ce,de,fe],(e=>{t=E(t,e," ")})),e.textContent!==t&&(v(n.removed,{element:e.cloneNode()}),e.textContent=t)),wt(ue.afterSanitizeElements,e,null),!1):(pt(e),!0)},Et=function(e,t,n){if(Fe&&("id"===t||"name"===t)&&(n in r||n in ut))return!1;if(Te&&!Se[t]&&_(he,t));else if(_e&&_(pe,t));else if(!ke[t]||Se[t]){if(!(Ct(e)&&(Ce.tagNameCheck instanceof RegExp&&_(Ce.tagNameCheck,e)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(e))&&(Ce.attributeNameCheck instanceof RegExp&&_(Ce.attributeNameCheck,t)||Ce.attributeNameCheck instanceof Function&&Ce.attributeNameCheck(t))||"is"===t&&Ce.allowCustomizedBuiltInElements&&(Ce.tagNameCheck instanceof RegExp&&_(Ce.tagNameCheck,n)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(n))))return!1}else if(Ze[t]);else if(_(ye,E(n,ge,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==C(n,"data:")||!Be[e])if(Ne&&!_(me,E(n,ge,"")));else if(n)return!1;return!0},Ct=function(e){return"annotation-xml"!==e&&k(e,ve)},xt=function(e){wt(ue.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||yt(e))return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:ke,forceKeepAttr:void 0};let a=t.length;for(;a--;){const i=t[a],{name:l,namespaceURI:o,value:s}=i,u=ot(l),c=s;let d="value"===l?c:x(c);if(r.attrName=u,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,wt(ue.uponSanitizeAttribute,e,r),d=r.attrValue,!qe||"id"!==u&&"name"!==u||(mt(l,e),d="user-content-"+d),Re&&_(/((--!?|])>)|<\/(style|title)/i,d)){mt(l,e);continue}if(r.forceKeepAttr)continue;if(!r.keepAttr){mt(l,e);continue}if(!ze&&_(/\/>/i,d)){mt(l,e);continue}Ae&&p([ce,de,fe],(e=>{d=E(d,e," ")}));const f=ot(e.nodeName);if(Et(f,u,d)){if(ee&&"object"==typeof B&&"function"==typeof B.getAttributeType)if(o);else switch(B.getAttributeType(f,u)){case"TrustedHTML":d=ee.createHTML(d);break;case"TrustedScriptURL":d=ee.createScriptURL(d)}if(d!==c)try{o?e.setAttributeNS(o,l,d):e.setAttribute(l,d),yt(e)?pt(e):g(n.removed)}catch(t){mt(l,e)}}else mt(l,e)}wt(ue.afterSanitizeAttributes,e,null)},St=function e(t){let n=null;const r=vt(t);for(wt(ue.beforeSanitizeShadowDOM,t,null);n=r.nextNode();)wt(ue.uponSanitizeShadowNode,n,null),kt(n),xt(n),n.content instanceof o&&e(n.content);wt(ue.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,a=null,l=null,s=null;if(Je=!e,Je&&(e="\x3c!--\x3e"),"string"!=typeof e&&!bt(e)){if("function"!=typeof e.toString)throw T("toString is not a function");if("string"!=typeof(e=e.toString()))throw T("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Me||dt(t),n.removed=[],"string"==typeof e&&(He=!1),He){if(e.nodeName){const t=ot(e.nodeName);if(!be[t]||xe[t])throw T("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof c)r=gt("\x3c!----\x3e"),a=r.ownerDocument.importNode(e,!0),1===a.nodeType&&"BODY"===a.nodeName||"HTML"===a.nodeName?r=a:r.appendChild(a);else{if(!Le&&!Ae&&!Oe&&-1===e.indexOf("<"))return ee&&Ie?ee.createHTML(e):e;if(r=gt(e),!r)return Le?null:Ie?re:""}r&&Pe&&pt(r.firstChild);const u=vt(He?e:r);for(;l=u.nextNode();)kt(l),xt(l),l.content instanceof o&&St(l.content);if(He)return e;if(Le){if(De)for(s=le.call(r.ownerDocument);r.firstChild;)s.appendChild(r.firstChild);else s=r;return(ke.shadowroot||ke.shadowrootmode)&&(s=se.call(i,s,!0)),s}let d=Oe?r.outerHTML:r.innerHTML;return Oe&&be["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&_(J,r.ownerDocument.doctype.name)&&(d="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+d),Ae&&p([ce,de,fe],(e=>{d=E(d,e," ")})),ee&&Ie?ee.createHTML(d):d},n.setConfig=function(){dt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Me=!0},n.clearConfig=function(){st=null,Me=!1},n.isValidAttribute=function(e,t,n){st||dt({});const r=ot(e),a=ot(t);return Et(r,a,n)},n.addHook=function(e,t){"function"==typeof t&&v(ue[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=m(ue[e],t);return-1===n?void 0:y(ue[e],n,1)[0]}return g(ue[e])},n.removeHooks=function(e){ue[e]=[]},n.removeAllHooks=function(){ue={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const ae=()=>(0,e.createElement)("svg",{width:38,height:46,viewBox:"0 0 38 46",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"a",fill:"#fff"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z"})),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.605 10.874h16.79c3.517 0 5.276 0 6.37 1.093 1.092 1.093 1.092 2.852 1.092 6.37v11.006c0 3.518 0 5.276-1.093 6.37-1.093 1.092-2.852 1.092-6.37 1.092h-2.73l-9.955 8.582v-8.582h-4.104c-3.518 0-5.277 0-6.37-1.093s-1.092-2.852-1.092-6.37V18.337c0-3.517 0-5.276 1.092-6.37 1.093-1.092 2.852-1.092 6.37-1.092m.14 16.984c4.875 4.239 11.278 4.187 16.463.04l-1.865-2.332c-4.143 3.315-8.932 3.263-12.64.04zm1.073-5.605v-2.798h2.798v2.798zm11.567-2.798v2.798h2.798v-2.798z",fill:"#00B682",className:"chatbotpath"}),(0,e.createElement)("path",{d:"m33.764 11.967 1.979-1.979zm0 23.745 1.979 1.979zm-9.1 1.093v-2.798h-1.04l-.788.679zm-9.955 8.582h-2.798v6.107l4.625-3.988zm0-8.582h2.798v-2.798H14.71zM4.235 35.712l-1.978 1.979zm22.973-7.815 1.748 2.185 2.185-1.748-1.748-2.185zm-16.463-.039-2.111-1.836-1.837 2.112L8.91 29.97zm14.598-2.292 2.185-1.748-1.748-2.185-2.185 1.748zm-12.64.04 1.837-2.112-2.111-1.836-1.837 2.111zm-.885-6.151v-2.798H9.019v2.798zm0 2.798H9.019v2.798h2.799zm2.798-2.798h2.799v-2.798h-2.799zm0 2.798v2.798h2.799v-2.798zm8.769 0h-2.799v2.798h2.799zm0-2.798v-2.798h-2.799v2.798zm2.798 2.798v2.798h2.799v-2.798zm0-2.798h2.799v-2.798h-2.799zm1.212-11.38h-16.79v5.597h16.79zm8.348 1.913c-1.153-1.153-2.555-1.572-3.882-1.75-1.245-.168-2.787-.162-4.466-.162v5.596c1.838 0 2.935.006 3.72.112.361.048.542.106.623.139l.038.018.01.005h-.001q-.002-.003 0 0zm1.912 8.348c0-1.68.006-3.22-.161-4.467-.179-1.326-.598-2.728-1.751-3.88l-3.958 3.957q.003.001 0 0v-.002l.005.01.018.04c.033.08.09.26.139.621.106.786.112 1.883.112 3.721zm0 11.007V18.336H32.06v11.007zm-1.912 8.348c1.153-1.153 1.572-2.555 1.75-3.881.168-1.246.162-2.788.162-4.467H32.06c0 1.838-.006 2.935-.112 3.721-.049.36-.106.54-.14.622l-.021.047q0-.002 0 0zm-8.349 1.912c1.68 0 3.222.006 4.468-.161 1.326-.178 2.728-.598 3.88-1.751l-3.957-3.958q-.002.003 0 .001l.002-.001-.01.006-.04.017c-.08.034-.261.09-.621.14-.787.105-1.883.11-3.721.11zm-2.73 0h2.73v-5.596h-2.73zm-8.128 7.903 9.955-8.581-3.655-4.24-9.954 8.582zm-4.625-10.7v8.58h5.596v-8.58zm-1.306 2.797h4.104v-5.596h-4.104zm-8.348-1.912c1.153 1.153 2.555 1.573 3.881 1.75 1.246.168 2.787.162 4.467.162v-5.596c-1.838 0-2.935-.006-3.721-.112-.36-.048-.541-.105-.622-.139l-.039-.017-.01-.006.002.001q0 .002 0 0zM.344 29.343c0 1.68-.006 3.22.162 4.467.178 1.326.598 2.728 1.75 3.881l3.958-3.958q-.002-.002 0 0l.001.002-.006-.01-.017-.039c-.034-.081-.09-.262-.14-.622-.105-.786-.111-1.883-.111-3.721zm0-11.007v11.007h5.597V18.336zm1.913-8.348C1.104 11.141.684 12.543.506 13.87c-.168 1.246-.162 2.788-.162 4.467h5.597c0-1.838.006-2.935.112-3.72.048-.361.105-.542.139-.623l.017-.038.006-.01-.001.001q-.002.001 0 0zm8.348-1.912c-1.68 0-3.221-.006-4.467.161-1.326.179-2.728.598-3.881 1.751l3.957 3.958q.003-.003 0 0h-.001l.01-.005.039-.018c.081-.033.261-.09.622-.139.786-.106 1.883-.112 3.721-.112zm14.854 17.636c-4.21 3.368-9.1 3.32-12.877.034L8.909 29.97c5.97 5.192 13.887 5.04 20.047.112zm-2.301 1.603 1.864 2.33 4.37-3.496-1.864-2.33zm-12.29.402c2.313 2.012 5.075 3.117 7.99 3.119 2.905.002 5.74-1.09 8.233-3.084l-3.496-4.37c-1.65 1.32-3.28 1.858-4.733 1.857-1.442 0-2.928-.533-4.322-1.745zm1.989 1.977 1.959-2.252-4.224-3.673-1.958 2.253zm-3.838-10.24v2.799h5.597v-2.798zm5.597-2.797h-2.798v5.596h2.798zm2.799 5.596v-2.798h-5.597v2.798zm-5.597 2.798h2.798v-5.596h-2.798zm14.365-2.798v-2.798h-5.597v2.798zm0-2.799h-2.798v5.597h2.798zm-2.798 0v2.799h5.596v-2.798zm0 2.8h2.798v-5.597h-2.798z",fill:"#00B682",className:"chatbotpath",mask:"url(#a)"}),(0,e.createElement)("path",{d:"M19 13.673V2.48",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"2.798"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.142 25.239v-5.783H.157v5.783zm31.716-5.783v5.783h2.985v-5.783z",fill:"#00B682",className:"chatbotpath"}),(0,e.createElement)("circle",{cx:19,cy:"3.411",r:"2.798",fill:"#00B682",className:"chatbotpath"})),ie=()=>(0,e.createElement)("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("g",{clipPath:"url(#a)"},(0,e.createElement)("path",{d:"M9.666 11.66H8.11v-1.556h1.556zm6.225 0h-1.556v-1.556h1.556zm6.224-1.556v3.112H20.56v3.89q0 .488-.182.912-.183.426-.499.742a2.34 2.34 0 0 1-1.653.68h-3.21l-5.349 4.572V19.44h-3.89a2.34 2.34 0 0 1-1.653-.68 2.3 2.3 0 0 1-.499-.743 2.3 2.3 0 0 1-.182-.912v-3.89H1.886v-3.112h1.556V7.77q0-.486.182-.912.183-.425.499-.742a2.34 2.34 0 0 1 1.653-.68h5.446V2.895a1.5 1.5 0 0 1-.559-.572 1.66 1.66 0 0 1-.219-.778q0-.327.122-.607a1.6 1.6 0 0 1 .328-.487q.207-.207.499-.34.291-.134.607-.122.33 0 .608.122.28.12.487.328.205.207.34.499t.122.607a1.55 1.55 0 0 1-.779 1.35v2.54h5.447q.486 0 .912.183.424.183.741.498a2.34 2.34 0 0 1 .681 1.654v2.334zM19.003 7.77a.75.75 0 0 0-.231-.547.75.75 0 0 0-.547-.231H5.776a.75.75 0 0 0-.547.23.75.75 0 0 0-.231.548v9.337q0 .316.23.547.232.23.548.23h5.446v2.748l3.21-2.748h3.793q.316 0 .547-.23a.75.75 0 0 0 .23-.547zM8.657 13.386q.67.67 1.532 1.022.864.352 1.811.364.949 0 1.812-.352a4.5 4.5 0 0 0 1.532-1.034l1.094 1.107A6.22 6.22 0 0 1 12 16.328a6.2 6.2 0 0 1-2.395-.474 6.4 6.4 0 0 1-2.042-1.361z",fill:"#00B682",className:"chatbotpath"})),(0,e.createElement)("defs",null,(0,e.createElement)("clipPath",{id:"a"},(0,e.createElement)("path",{fill:"#fff",d:"M0 0h24v24H0z"}))));let le={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function oe(e){le=e}const se=/[&<>"']/,ue=new RegExp(se.source,"g"),ce=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,de=new RegExp(ce.source,"g"),fe={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},he=e=>fe[e];function pe(e,t){if(t){if(se.test(e))return e.replace(ue,he)}else if(ce.test(e))return e.replace(de,he);return e}const me=/(^|[^\[])\^/g;function ge(e,t){let n="string"==typeof e?e:e.source;t=t||"";const r={replace:(e,t)=>{let a="string"==typeof t?t:t.source;return a=a.replace(me,"$1"),n=n.replace(e,a),r},getRegex:()=>new RegExp(n,t)};return r}function ve(e){try{e=encodeURI(e).replace(/%25/g,"%")}catch{return null}return e}const ye={exec:()=>null};function be(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let r=!1,a=t;for(;--a>=0&&"\\"===n[a];)r=!r;return r?"|":" |"})).split(/ \|/);let r=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(/\\\|/g,"|");return n}function we(e,t,n){const r=e.length;if(0===r)return"";let a=0;for(;a<r;){const i=e.charAt(r-a-1);if(i!==t||n){if(i===t||!n)break;a++}else a++}return e.slice(0,r-a)}function ke(e,t,n,r){const a=t.href,i=t.title?pe(t.title):null,l=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){r.state.inLink=!0;const e={type:"link",raw:n,href:a,title:i,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,e}return{type:"image",raw:n,href:a,title:i,text:pe(l)}}class Ee{options;rules;lexer;constructor(e){this.options=e||le}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^(?: {1,4}| {0,3}\t)/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:we(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const r=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=r.length?e.slice(r.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=we(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:we(t[0],"\n")}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){let e=we(t[0],"\n").split("\n"),n="",r="";const a=[];for(;e.length>0;){let t=!1;const i=[];let l;for(l=0;l<e.length;l++)if(/^ {0,3}>/.test(e[l]))i.push(e[l]),t=!0;else{if(t)break;i.push(e[l])}e=e.slice(l);const o=i.join("\n"),s=o.replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,"\n    $1").replace(/^ {0,3}>[ \t]?/gm,"");n=n?`${n}\n${o}`:o,r=r?`${r}\n${s}`:s;const u=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(s,a,!0),this.lexer.state.top=u,0===e.length)break;const c=a[a.length-1];if("code"===c?.type)break;if("blockquote"===c?.type){const t=c,i=t.raw+"\n"+e.join("\n"),l=this.blockquote(i);a[a.length-1]=l,n=n.substring(0,n.length-t.raw.length)+l.raw,r=r.substring(0,r.length-t.text.length)+l.text;break}if("list"!==c?.type);else{const t=c,i=t.raw+"\n"+e.join("\n"),l=this.list(i);a[a.length-1]=l,n=n.substring(0,n.length-c.raw.length)+l.raw,r=r.substring(0,r.length-t.raw.length)+l.raw,e=i.substring(a[a.length-1].raw.length).split("\n")}}return{type:"blockquote",raw:n,tokens:a,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,a={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const i=new RegExp(`^( {0,3}${n})((?:[\t ][^\\n]*)?(?:\\n|$))`);let l=!1;for(;e;){let n=!1,r="",o="";if(!(t=i.exec(e)))break;if(this.rules.block.hr.test(e))break;r=t[0],e=e.substring(r.length);let s=t[2].split("\n",1)[0].replace(/^\t+/,(e=>" ".repeat(3*e.length))),u=e.split("\n",1)[0],c=!s.trim(),d=0;if(this.options.pedantic?(d=2,o=s.trimStart()):c?d=t[1].length+1:(d=t[2].search(/[^ ]/),d=d>4?1:d,o=s.slice(d),d+=t[1].length),c&&/^[ \t]*$/.test(u)&&(r+=u+"\n",e=e.substring(u.length+1),n=!0),!n){const t=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),n=new RegExp(`^ {0,${Math.min(3,d-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),a=new RegExp(`^ {0,${Math.min(3,d-1)}}(?:\`\`\`|~~~)`),i=new RegExp(`^ {0,${Math.min(3,d-1)}}#`),l=new RegExp(`^ {0,${Math.min(3,d-1)}}<(?:[a-z].*>|!--)`,"i");for(;e;){const f=e.split("\n",1)[0];let h;if(u=f,this.options.pedantic?(u=u.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  "),h=u):h=u.replace(/\t/g,"    "),a.test(u))break;if(i.test(u))break;if(l.test(u))break;if(t.test(u))break;if(n.test(u))break;if(h.search(/[^ ]/)>=d||!u.trim())o+="\n"+h.slice(d);else{if(c)break;if(s.replace(/\t/g,"    ").search(/[^ ]/)>=4)break;if(a.test(s))break;if(i.test(s))break;if(n.test(s))break;o+="\n"+u}c||u.trim()||(c=!0),r+=f+"\n",e=e.substring(f.length+1),s=h.slice(d)}}a.loose||(l?a.loose=!0:/\n[ \t]*\n[ \t]*$/.test(r)&&(l=!0));let f,h=null;this.options.gfm&&(h=/^\[[ xX]\] /.exec(o),h&&(f="[ ] "!==h[0],o=o.replace(/^\[[ xX]\] +/,""))),a.items.push({type:"list_item",raw:r,task:!!h,checked:f,loose:!1,text:o,tokens:[]}),a.raw+=r}a.items[a.items.length-1].raw=a.items[a.items.length-1].raw.trimEnd(),a.items[a.items.length-1].text=a.items[a.items.length-1].text.trimEnd(),a.raw=a.raw.trimEnd();for(let e=0;e<a.items.length;e++)if(this.lexer.state.top=!1,a.items[e].tokens=this.lexer.blockTokens(a.items[e].text,[]),!a.loose){const t=a.items[e].tokens.filter((e=>"space"===e.type)),n=t.length>0&&t.some((e=>/\n.*\n/.test(e.raw)));a.loose=n}if(a.loose)for(let e=0;e<a.items.length;e++)a.items[e].loose=!0;return a}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const e=t[1].toLowerCase().replace(/\s+/g," "),n=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){const t=this.rules.block.table.exec(e);if(!t)return;if(!/[:|]/.test(t[2]))return;const n=be(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),a=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[],i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const e of r)/^ *-+: *$/.test(e)?i.align.push("right"):/^ *:-+: *$/.test(e)?i.align.push("center"):/^ *:-+ *$/.test(e)?i.align.push("left"):i.align.push(null);for(let e=0;e<n.length;e++)i.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:i.align[e]});for(const e of a)i.rows.push(be(e,i.header.length).map(((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:i.align[t]}))));return i}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:pe(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=we(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),ke(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const e=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!e){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return ke(n,e,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&(!r[3]||!n.match(/[\p{L}\p{N}]/u))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){const n=[...r[0]].length-1;let a,i,l=n,o=0;const s="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=s.exec(t));){if(a=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!a)continue;if(i=[...a].length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&n%3&&!((n+i)%3)){o+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+o);const t=[...r[0]][0].length,s=e.slice(0,n+r.index+t+i);if(Math.min(n,i)%2){const e=s.slice(1,-1);return{type:"em",raw:s,text:e,tokens:this.lexer.inlineTokens(e)}}const u=s.slice(2,-2);return{type:"strong",raw:s,text:u,tokens:this.lexer.inlineTokens(u)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),r=/^ /.test(e)&&/ $/.test(e);return n&&r&&(e=e.substring(1,e.length-1)),e=pe(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let e,n;return"@"===t[2]?(e=pe(t[1]),n="mailto:"+e):(e=pe(t[1]),n=e),{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])e=pe(t[0]),n="mailto:"+e;else{let r;do{r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(r!==t[0]);e=pe(t[0]),n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let e;return e=this.lexer.state.inRawBlock?t[0]:pe(t[0]),{type:"text",raw:t[0],text:e}}}}const Ce=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,xe=/(?:[*+-]|\d{1,9}[.)])/,Se=ge(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,xe).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),_e=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Te=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ne=ge(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Te).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),ze=ge(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,xe).getRegex(),Ae="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Re=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Oe=ge("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",Re).replace("tag",Ae).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Me=ge(_e).replace("hr",Ce).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ae).getRegex(),Pe={blockquote:ge(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Me).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:Ne,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:Ce,html:Oe,lheading:Se,list:ze,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:Me,table:ye,text:/^[^\n]+/},Le=ge("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Ce).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ae).getRegex(),De={...Pe,table:Le,paragraph:ge(_e).replace("hr",Ce).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Le).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ae).getRegex()},Ie={...Pe,html:ge("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",Re).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:ye,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ge(_e).replace("hr",Ce).replace("heading"," *#{1,6} *[^\n]").replace("lheading",Se).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Fe=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,qe=/^( {2,}|\\)\n(?!\s*$)/,Ue="\\p{P}\\p{S}",He=ge(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Ue).getRegex(),$e=ge(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Ue).getRegex(),je=ge("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Ue).getRegex(),Qe=ge("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Ue).getRegex(),Be=ge(/\\([punct])/,"gu").replace(/punct/g,Ue).getRegex(),Ve=ge(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ze=ge(Re).replace("(?:--\x3e|$)","--\x3e").getRegex(),We=ge("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ze).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Ke=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Ge=ge(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Ke).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ye=ge(/^!?\[(label)\]\[(ref)\]/).replace("label",Ke).replace("ref",Te).getRegex(),Xe=ge(/^!?\[(ref)\](?:\[\])?/).replace("ref",Te).getRegex(),Je={_backpedal:ye,anyPunctuation:Be,autolink:Ve,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:qe,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:ye,emStrongLDelim:$e,emStrongRDelimAst:je,emStrongRDelimUnd:Qe,escape:Fe,link:Ge,nolink:Xe,punctuation:He,reflink:Ye,reflinkSearch:ge("reflink|nolink(?!\\()","g").replace("reflink",Ye).replace("nolink",Xe).getRegex(),tag:We,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:ye},et={...Je,link:ge(/^!?\[(label)\]\((.*?)\)/).replace("label",Ke).getRegex(),reflink:ge(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Ke).getRegex()},tt={...Je,escape:ge(Fe).replace("])","~|])").getRegex(),url:ge(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},nt={...tt,br:ge(qe).replace("{2,}","*").getRegex(),text:ge(tt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},rt={normal:Pe,gfm:De,pedantic:Ie},at={normal:Je,gfm:tt,breaks:nt,pedantic:et};class it{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||le,this.options.tokenizer=this.options.tokenizer||new Ee,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:rt.normal,inline:at.normal};this.options.pedantic?(t.block=rt.pedantic,t.inline=at.pedantic):this.options.gfm&&(t.block=rt.gfm,this.options.breaks?t.inline=at.breaks:t.inline=at.gfm),this.tokenizer.rules=t}static get rules(){return{block:rt,inline:at}}static lex(e,t){return new it(t).lex(e)}static lexInline(e,t){return new it(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){let r,a,i;for(this.options.pedantic&&(e=e.replace(/\t/g,"    ").replace(/^ +$/gm,""));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))))if(r=this.tokenizer.space(e))e=e.substring(r.raw.length),1===r.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(r);else if(r=this.tokenizer.code(e))e=e.substring(r.raw.length),a=t[t.length-1],!a||"paragraph"!==a.type&&"text"!==a.type?t.push(r):(a.raw+="\n"+r.raw,a.text+="\n"+r.text,this.inlineQueue[this.inlineQueue.length-1].src=a.text);else if(r=this.tokenizer.fences(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.heading(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.hr(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.blockquote(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.list(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.html(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.def(e))e=e.substring(r.raw.length),a=t[t.length-1],!a||"paragraph"!==a.type&&"text"!==a.type?this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title}):(a.raw+="\n"+r.raw,a.text+="\n"+r.raw,this.inlineQueue[this.inlineQueue.length-1].src=a.text);else if(r=this.tokenizer.table(e))e=e.substring(r.raw.length),t.push(r);else if(r=this.tokenizer.lheading(e))e=e.substring(r.raw.length),t.push(r);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startBlock.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(i=e.substring(0,t+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i)))a=t[t.length-1],n&&"paragraph"===a?.type?(a.raw+="\n"+r.raw,a.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=a.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);else if(r=this.tokenizer.text(e))e=e.substring(r.raw.length),a=t[t.length-1],a&&"text"===a.type?(a.raw+="\n"+r.raw,a.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=a.text):t.push(r);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,a,i,l,o,s=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(i=this.tokenizer.rules.inline.reflinkSearch.exec(s));)e.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(s=s.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(i=this.tokenizer.rules.inline.blockSkip.exec(s));)s=s.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+s.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(i=this.tokenizer.rules.inline.anyPunctuation.exec(s));)s=s.slice(0,i.index)+"++"+s.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(o=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((r=>!!(n=r.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&"text"===n.type&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,s,o))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(a=e,this.options.extensions&&this.options.extensions.startInline){let t=1/0;const n=e.slice(1);let r;this.options.extensions.startInline.forEach((e=>{r=e.call({lexer:this},n),"number"==typeof r&&r>=0&&(t=Math.min(t,r))})),t<1/0&&t>=0&&(a=e.substring(0,t+1))}if(n=this.tokenizer.inlineText(a))e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(o=n.raw.slice(-1)),l=!0,r=t[t.length-1],r&&"text"===r.type?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw new Error(t)}}else e=e.substring(n.raw.length),t.push(n);return t}}class lt{options;parser;constructor(e){this.options=e||le}space(e){return""}code({text:e,lang:t,escaped:n}){const r=(t||"").match(/^\S*/)?.[0],a=e.replace(/\n$/,"")+"\n";return r?'<pre><code class="language-'+pe(r)+'">'+(n?a:pe(a,!0))+"</code></pre>\n":"<pre><code>"+(n?a:pe(a,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){const t=e.ordered,n=e.start;let r="";for(let t=0;t<e.items.length;t++){const n=e.items[t];r+=this.listitem(n)}const a=t?"ol":"ul";return"<"+a+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+a+">\n"}listitem(e){let t="";if(e.task){const n=this.checkbox({checked:!!e.checked});e.loose?e.tokens.length>0&&"paragraph"===e.tokens[0].type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+e.tokens[0].tokens[0].text)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" "}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){const a=e.rows[t];n="";for(let e=0;e<a.length;e++)n+=this.tablecell(a[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){const t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${e}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){const r=this.parser.parseInline(n),a=ve(e);if(null===a)return r;let i='<a href="'+(e=a)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+r+"</a>",i}image({href:e,title:t,text:n}){const r=ve(e);if(null===r)return n;let a=`<img src="${e=r}" alt="${n}"`;return t&&(a+=` title="${t}"`),a+=">",a}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):e.text}}class ot{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}}class st{options;renderer;textRenderer;constructor(e){this.options=e||le,this.options.renderer=this.options.renderer||new lt,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ot}static parse(e,t){return new st(t).parse(e)}static parseInline(e,t){return new st(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const a=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const e=a,t=this.options.extensions.renderers[e.type].call({parser:this},e);if(!1!==t||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(e.type)){n+=t||"";continue}}const i=a;switch(i.type){case"space":n+=this.renderer.space(i);continue;case"hr":n+=this.renderer.hr(i);continue;case"heading":n+=this.renderer.heading(i);continue;case"code":n+=this.renderer.code(i);continue;case"table":n+=this.renderer.table(i);continue;case"blockquote":n+=this.renderer.blockquote(i);continue;case"list":n+=this.renderer.list(i);continue;case"html":n+=this.renderer.html(i);continue;case"paragraph":n+=this.renderer.paragraph(i);continue;case"text":{let a=i,l=this.renderer.text(a);for(;r+1<e.length&&"text"===e[r+1].type;)a=e[++r],l+="\n"+this.renderer.text(a);n+=t?this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l}]}):l;continue}default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const a=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const e=this.options.extensions.renderers[a.type].call({parser:this},a);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){n+=e||"";continue}}const i=a;switch(i.type){case"escape":case"text":n+=t.text(i);break;case"html":n+=t.html(i);break;case"link":n+=t.link(i);break;case"image":n+=t.image(i);break;case"strong":n+=t.strong(i);break;case"em":n+=t.em(i);break;case"codespan":n+=t.codespan(i);break;case"br":n+=t.br(i);break;case"del":n+=t.del(i);break;default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw new Error(e)}}}return n}}class ut{options;block;constructor(e){this.options=e||le}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?it.lex:it.lexInline}provideParser(){return this.block?st.parse:st.parseInline}}const ct=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=st;Renderer=lt;TextRenderer=ot;Lexer=it;Tokenizer=Ee;Hooks=ut;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(const r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{const e=r;for(const r of e.header)n=n.concat(this.walkTokens(r.tokens,t));for(const r of e.rows)for(const e of r)n=n.concat(this.walkTokens(e.tokens,t));break}case"list":{const e=r;n=n.concat(this.walkTokens(e.items,t));break}default:{const e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach((r=>{const a=e[r].flat(1/0);n=n.concat(this.walkTokens(a,t))})):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach((e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach((e=>{if(!e.name)throw new Error("extension name required");if("renderer"in e){const n=t.renderers[e.name];t.renderers[e.name]=n?function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");const n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)})),n.extensions=t),e.renderer){const t=this.defaults.renderer||new lt(this.defaults);for(const n in e.renderer){if(!(n in t))throw new Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;const r=n,a=e.renderer[r],i=t[r];t[r]=(...e)=>{let n=a.apply(t,e);return!1===n&&(n=i.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){const t=this.defaults.tokenizer||new Ee(this.defaults);for(const n in e.tokenizer){if(!(n in t))throw new Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;const r=n,a=e.tokenizer[r],i=t[r];t[r]=(...e)=>{let n=a.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){const t=this.defaults.hooks||new ut;for(const n in e.hooks){if(!(n in t))throw new Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;const r=n,a=e.hooks[r],i=t[r];ut.passThroughHooks.has(n)?t[r]=e=>{if(this.defaults.async)return Promise.resolve(a.call(t,e)).then((e=>i.call(t,e)));const n=a.call(t,e);return i.call(t,n)}:t[r]=(...e)=>{let n=a.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){const t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}})),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return it.lex(e,t??this.defaults)}parser(e,t){return st.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{const r={...n},a={...this.defaults,...r},i=this.onError(!!a.silent,!!a.async);if(!0===this.defaults.async&&!1===r.async)return i(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return i(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));a.hooks&&(a.hooks.options=a,a.hooks.block=e);const l=a.hooks?a.hooks.provideLexer():e?it.lex:it.lexInline,o=a.hooks?a.hooks.provideParser():e?st.parse:st.parseInline;if(a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(t):t).then((e=>l(e,a))).then((e=>a.hooks?a.hooks.processAllTokens(e):e)).then((e=>a.walkTokens?Promise.all(this.walkTokens(e,a.walkTokens)).then((()=>e)):e)).then((e=>o(e,a))).then((e=>a.hooks?a.hooks.postprocess(e):e)).catch(i);try{a.hooks&&(t=a.hooks.preprocess(t));let e=l(t,a);a.hooks&&(e=a.hooks.processAllTokens(e)),a.walkTokens&&this.walkTokens(e,a.walkTokens);let n=o(e,a);return a.hooks&&(n=a.hooks.postprocess(n)),n}catch(e){return i(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){const e="<p>An error occurred:</p><pre>"+pe(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function dt(e,t){return ct.parse(e,t)}dt.options=dt.setOptions=function(e){return ct.setOptions(e),dt.defaults=ct.defaults,oe(dt.defaults),dt},dt.getDefaults=function(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}},dt.defaults=le,dt.use=function(...e){return ct.use(...e),dt.defaults=ct.defaults,oe(dt.defaults),dt},dt.walkTokens=function(e,t){return ct.walkTokens(e,t)},dt.parseInline=ct.parseInline,dt.Parser=st,dt.parser=st.parse,dt.Renderer=lt,dt.TextRenderer=ot,dt.Lexer=it,dt.lexer=it.lex,dt.Tokenizer=Ee,dt.Hooks=ut,dt.parse=dt,dt.options,dt.setOptions,dt.use,dt.walkTokens,dt.parseInline,st.parse,it.lex;const ft=()=>{const e=navigator.userAgent;let t="Unknown Browser",n="",r="";return e.includes("Edg/")?(t="Edge",n=e.match(/Edg\/(\d+)/)?.[1]||""):e.includes("Chrome/")&&e.includes("Safari/")?(t="Chrome",n=e.match(/Chrome\/(\d+)/)?.[1]||""):e.includes("Safari/")&&!e.includes("Chrome/")?(t="Safari",n=e.match(/Version\/(\d+)/)?.[1]||""):e.includes("Firefox/")?(t="Mozila Firefox",n=e.match(/Firefox\/(\d+)/)?.[1]||""):(e.includes("MSIE")||e.includes("Trident/"))&&(t="Internet Explorer",n=e.match(/(?:MSIE |rv:)(\d+)/)?.[1]||""),e.includes("Macintosh")?r="Mac OS":e.includes("Windows")?r="Windows":e.includes("Linux")?r="Linux":e.includes("Android")?r="Android":(e.includes("iPhone")||e.includes("iPad"))&&(r="iOS"),`${t} ${n} on ${r}`},ht=(e,t,n={})=>{"number"==typeof n&&(n={expiresDays:n});const r={expiresDays:7,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax",...n};let a=`${e}=${encodeURIComponent(t)}`;if(r.expiresDays){const e=new Date;e.setTime(e.getTime()+24*r.expiresDays*60*60*1e3),a+=`; expires=${e.toUTCString()}`}a+=`; path=${r.path}`,r.secure&&(a+="; Secure"),r.sameSite&&(a+=`; SameSite=${r.sameSite}`),document.cookie=a},pt=e=>{const t=document.cookie.split("; ");for(let n of t){const[t,r]=n.split("=");if(t===e)return decodeURIComponent(r)}return null},mt=(()=>{const e={text:(e,t=1e3)=>"string"!=typeof e?"":e.replace(/[<>"'`;\\/]/g,"").slice(0,t),email:e=>{const t=e.toLowerCase().trim();return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?t:""},sessionId:e=>e.replace(/[^a-zA-Z0-9_-]/g,"").slice(0,64)};return{sanitize:(t,n)=>e[t]?e[t](n):""}})(),gt=()=>(0,e.createElement)("svg",{width:8,height:12,viewBox:"0 0 8 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M5.1 6 1.2 2.1a.95.95 0 0 1-.275-.7q0-.426.275-.7a.95.95 0 0 1 .7-.275q.424 0 .7.275l4.6 4.6q.15.15.212.325.063.174.063.375 0 .2-.063.375A.9.9 0 0 1 7.2 6.7l-4.6 4.6a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275.95.95 0 0 1-.275-.7q0-.426.275-.7z",fill:"#1C1B1F"})),vt=()=>(0,e.createElement)("svg",{width:18,height:16,viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"m16.865 8.925-15.4 6.5a.99.99 0 0 1-.95-.087q-.45-.288-.45-.838v-13q0-.55.45-.837a.99.99 0 0 1 .95-.088l15.4 6.5q.624.276.625.925 0 .65-.625.925M2.064 13l11.85-5-11.85-5v3.5l6 1.5-6 1.5z",fill:"#fff"})),yt=async(e,t)=>{try{const n=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/update-online-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e})});if(!n.ok){const e=await n.json();throw new Error(e.message||"Online status update failed")}return n.json()}catch(e){throw console.error("Error updating online status:",e),e}},bt=({timestamp:t,timezone:n})=>{const[a,i]=(0,e.useState)(""),l=(0,e.useCallback)((e=>{if(!e)return(0,r.__)("Just now","betterdocs-ai-chatbot");let t;try{if(t="string"==typeof e&&e.includes(" ")?new Date(e.replace(" ","T")+"Z"):e instanceof Date?e:new Date(e),isNaN(t?.getTime()))return(0,r.__)("Just now","betterdocs-ai-chatbot");const a=new Intl.DateTimeFormat("en-US",{timeZone:n||"UTC",hour:"numeric",minute:"numeric",hour12:!0}),i=new Date,l=new Date(i.toLocaleString("en-US",{timeZone:n||"UTC"})),o=new Date(t.toLocaleString("en-US",{timeZone:n||"UTC"})),s=(l.getTime()-o.getTime())/1e3,u=a.format(t);if(s<60)return(0,r.__)("Just now","betterdocs-ai-chatbot");if(s<3600){const e=Math.floor(s/60);return(0,r.nv)((0,r._n)("%d minute ago","%d minutes ago",e,"betterdocs-ai-chatbot"),e)}if(s<86400){const e=Math.floor(s/3600);return(0,r.nv)((0,r._n)("%d hour ago","%d hours ago",e,"betterdocs-ai-chatbot"),e)}const c=new Date(l);if(c.setDate(c.getDate()-1),o.getDate()===c.getDate()&&o.getMonth()===c.getMonth()&&o.getFullYear()===c.getFullYear())return(0,r.nv)((0,r.__)("Yesterday (%s)","betterdocs-ai-chatbot"),u);const d=new Intl.DateTimeFormat("en-US",{timeZone:n||"UTC",day:"2-digit",month:"short",year:"numeric"});return(0,r.nv)("%s (%s)",d.format(t),u)}catch(e){return console.error("Error calculating time ago:",e),(0,r.__)("Just now","betterdocs-ai-chatbot")}}),[n]);return(0,e.useEffect)((()=>{i(l(t));const e=setInterval((()=>{i(l(t))}),6e4);return()=>clearInterval(e)}),[t,l]),(0,e.createElement)("span",{className:"text-gray-500"},a)},wt=()=>(0,e.createElement)("div",{className:"generic-loader"},(0,e.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:40,height:40,preserveAspectRatio:"xMidYMid",style:{background:"0 0",shapeRendering:"auto"},viewBox:"0 0 100 100"},(0,e.createElement)("circle",{cx:50,cy:50,r:26,fill:"none",stroke:"#16ca9e",strokeDasharray:"122.52211349000194 42.840704496667314",strokeWidth:10},(0,e.createElement)("animateTransform",{attributeName:"transform",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"})))),kt=()=>{const[t,n]=(0,e.useState)(null),[a,i]=(0,e.useState)(""),[l,o]=(0,e.useState)([]),[s,u]=(0,e.useState)(!1),[c,d]=(0,e.useState)(!1),[f,h]=(0,e.useState)(""),[p,m]=(0,e.useState)(!0),[g,v]=(0,e.useState)(""),[y,b]=(0,e.useState)(pt("userEmail")||""),[w]=(0,e.useState)(betterdocsAIChatbot?.welcome_message),[k,E]=(0,e.useState)(null),[C,x]=(0,e.useState)(!1),[S,_]=(0,e.useState)(!1),[T,N]=(0,e.useState)(null),[z,A]=(0,e.useState)(!1),[R,O]=(0,e.useState)(!1),M=(0,e.useRef)(null),P=(0,e.useRef)(null),[L,D]=(0,e.useState)(null),[I,F]=(0,e.useState)({city:"",country:""}),[q,U]=(0,e.useState)("offline"),H=(new Date).toISOString().slice(0,19).replace("T"," "),$=()=>(new Date).toISOString().slice(0,19).replace("T"," ");(0,e.useEffect)((()=>{const e=(()=>{let e=pt("chatbot_session_id");return e&&(/^session_[a-f0-9]{14}\.[a-f0-9]{8}$/.test(e)||/^session_[a-z0-9]{9,}\d+$/.test(e))||(e=(()=>{try{const e=Date.now()/1e3,t=Math.floor(e%1*1e6);return`session_${Math.floor(e).toString(16)}${t}.${crypto.getRandomValues(new Uint8Array(4)).reduce(((e,t)=>e+t.toString(16).padStart(2,"0")),"")}`}catch(e){return`session_${Math.random().toString(36).substring(2,11)}${Date.now()}`}})(),ht("chatbot_session_id",e,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"})),e})();n(e)}),[]),(0,e.useEffect)((()=>{localStorage.setItem("userStatus","online");const e=()=>{localStorage.setItem("userStatus","online")};return window.addEventListener("mousemove",e),window.addEventListener("keydown",e),()=>{window.removeEventListener("mousemove",e),window.removeEventListener("keydown",e),localStorage.removeItem("userStatus")}}),[]),(0,e.useEffect)((()=>{(async()=>{try{const e=await fetch("https://ipinfo.io/json");if(!e.ok)throw new Error("Network response was not ok");const t=await(e?.json());F(t)}catch(e){D(e.message)}})()}),[]);const j=async()=>{if(""===a.trim())return;const e=a?.trim(),n=$();if(o((t=>[...t,{text:e,type:navigator?.onLine?"sent":"failed",timestamp:n}])),i(""),h(""),M.current&&setTimeout((()=>{M.current.scrollTop=M.current.scrollHeight}),50),navigator.onLine){d(!0);try{const n=(betterdocsAIChatbot?.locale||"en_US").split("_")[0],r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/query-post`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,email:y,session_id:t,lang:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"API request failed")}const a=await r.json();if(!a?.success)throw new Error(a.message||"Request failed");{const e=a?.data?.conversation||[],t=e[e.length-1],n=t?.text||"No response content available";u(!0),Q(n)}}catch(e){d(!1),o((t=>[...t,{text:e.message,type:"received",timestamp:$()}]))}}},Q=e=>{let t=0,n="";h("");const r=$();d(!0),E(r);const a=()=>{if(t<e?.length)n+=e?.charAt(t),h(n),t++,setTimeout(a,10);else{const e=(i=n,i?(dt.setOptions({gfm:!0,breaks:!0,headerIds:!1}),{__html:dt(i)}):"").__html;h(e),d(!1),o((t=>[...t,{text:e,type:"received",timestamp:r}]))}var i};a()},B=e=>{const t=new Set;let n=null;return e.map((e=>{if(!e.timestamp){const t=$();return{...e,timestamp:t}}if(t.has(e.timestamp)||e.timestamp===n){let r;r=e.timestamp.includes("T")&&e.timestamp.includes("Z")?new Date(e.timestamp):new Date(e.timestamp.replace(" ","T")+"Z"),r.setMilliseconds(r.getMilliseconds()+1);const a=r.toISOString().slice(0,19).replace("T"," ");return n=a,t.add(a),{...e,timestamp:a}}return n=e.timestamp,t.add(e.timestamp),e}))},V=async()=>{if(""===g?.trim()||!K(g?.trim()))return _(!0),void setTimeout((()=>_(!1)),3e3);ht("userEmail",g,{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),ht("showEmailField","false",{expiresDays:30,path:"/",secure:"https:"===window.location.protocol,sameSite:"Lax"}),b(g),x(!0),setTimeout((()=>x(!1)),3e3),m(!1)};(0,e.useEffect)((()=>{y&&(async()=>{if(y&&t){O(!0);try{const e=await(async(e,t)=>{try{const n=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/get-current-user-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:t,user_email:e,filter_term:void 0})});if(!n.ok){const e=await n.json();throw new Error(e.message||"Failed to fetch conversation")}return n.json()}catch(e){throw console.error("Error fetching conversation:",e),e}})(y,t);e?.conversation&&(o(e.conversation),yt(y,t))}catch(e){o([{text:w,type:"received",timestamp:$()}])}finally{O(!1)}}})()}),[y,t,w]),(0,e.useEffect)((()=>{const e=$(),t=y?pt(y):null;if(y&&t){const e=JSON.parse(t);o(B(e?.conversation||[]))}else y||o([{text:w,type:"received",timestamp:e}])}),[y]);const Z=(0,e.useRef)(0),W=(0,e.useRef)(null);(0,e.useEffect)((()=>()=>{W.current&&clearTimeout(W.current)}),[]),(0,e.useEffect)((()=>{y&&l&&0!==l.length&&t&&(W.current&&clearTimeout(W.current),W.current=setTimeout((()=>{const e=Date.now();if(e-Z.current<2e3)return;Z.current=e;const n=B(l),r={email:y,main_information:{location:`${I?.city}, ${I?.country}`,country:I?.country,time:$(),timezone:I?.timezone,language:navigator?.language},device_information:{IP_address:I?.ip,browser:ft()},isBlocked:!1,conversation:n.map((e=>({text:e?.text,type:e?.type,timestamp:e?.timestamp||$()})))};(async(e,t,n)=>{try{const r=await fetch(`${betterdocsAIChatbot.rest_url}betterdocs-pro/v1/save-conversation`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,conversation:t,session_id:n})});if(!r.ok){const e=await r.json();throw new Error(e.message||"Failed to save conversation")}return r.json()}catch(e){throw console.error("Error saving conversation:",e),e}})(y,r,t).then((e=>{e?.success&&yt(y,t)})).catch((e=>{console.error("Error saving conversation:",e)}))}),500))}),[I,l,y,t]),(0,e.useEffect)((()=>{if(P?.current){const e=()=>A(!0),t=()=>A(!1),n=P?.current;return n.addEventListener("focus",e),n.addEventListener("blur",t),()=>{n.removeEventListener("focus",e),n.removeEventListener("blur",t)}}}),[]),(0,e.useEffect)((()=>{M.current&&(M.current.scrollTop=M?.current?.scrollHeight)}),[l,c,f,S,C]);const K=e=>e?!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||(N("Please enter a valid email"),!1):(N("Email is required"),!1);return(0,e.createElement)("div",{className:"chat-container"},(0,e.createElement)("div",{className:"betterdocs-chatbot-header betterdocs-ia-common-header"},(0,e.createElement)("h2",null,(0,r.__)("Chatbot","betterdocs-ai-chatbot"))),(0,e.createElement)("div",{className:"chat-content-wrapper"},(0,e.createElement)("div",{className:"chat-body",ref:M},(0,e.createElement)("div",{className:"top-content"},(0,e.createElement)("div",{className:"chat-icon"},(0,e.createElement)(ae,null)),betterdocsAIChatbot?.title&&(0,e.createElement)("h3",{className:"heading-title"},betterdocsAIChatbot.title),betterdocsAIChatbot?.subtitle&&(0,e.createElement)("p",{className:"chat-description"},betterdocsAIChatbot.subtitle)),R&&(0,e.createElement)(wt,null),!R&&l&&l?.map(((t,n)=>(0,e.createElement)("div",{key:n,className:`message ${t?.type}`},"sent"===t?.type||"failed"===t?.type?(0,e.createElement)("div",{className:"query"},t?.text,"failed"==t?.type&&(0,e.createElement)("span",{className:"status"},(0,r.__)("Sending Failed","betterdocs-ai-chatbot"))):"received"===t?.type?(0,e.createElement)("div",{className:"message-content"},(0,e.createElement)("div",{className:"avatar"},(0,e.createElement)(ie,null)),(0,e.createElement)("div",{className:"message-text"},(0,e.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:re.sanitize(dt(t?.text))}}),(0,e.createElement)("span",{className:"message-received-time"},t.timestamp&&(0,e.createElement)(bt,{timestamp:t?.timestamp,timezone:I?.timezone,isNewMesage:s,setNewMessage:u})))):(0,e.createElement)(e.Fragment,null)))),c&&f&&(0,e.createElement)("div",{className:"message typing received message-active"},(0,e.createElement)("div",{className:"message-content"},(0,e.createElement)("div",{className:"avatar"},(0,e.createElement)(ie,null)),(0,e.createElement)("div",{className:"message-text"},(0,e.createElement)("div",{className:"text",dangerouslySetInnerHTML:{__html:re?.sanitize(f)}}),(0,e.createElement)("span",{className:"message-received-time"},(0,e.createElement)(bt,{timestamp:k||H,timezone:I?.timezone,isNewMesage:s,setNewMessage:u}))))),c&&!f&&(0,e.createElement)("div",{className:"message typing received message-active"},(0,e.createElement)("div",{className:"message-content"},(0,e.createElement)("div",{className:"avatar"},(0,e.createElement)(ie,null)),(0,e.createElement)("div",{className:"text thinking-dots"},(0,r.__)("Thinking","betterdocs-ai-chatbot"),(0,e.createElement)("span",{className:"dots"})))),p&&"false"!==pt("showEmailField")&&(0,e.createElement)("div",{className:"message received email-field-wrapper "+(z?"focused":"")},(0,e.createElement)("div",{className:"message-content"},(0,e.createElement)("div",{className:"avatar"},(0,e.createElement)(ie,null)),(0,e.createElement)("div",{className:"text"},(0,r.__)("Enter email to keep conversation alive.","betterdocs-ai-chatbot"))),(0,e.createElement)("div",{className:"email-field-container"},(0,e.createElement)("div",{className:"email-field"},(0,e.createElement)("input",{ref:P,type:"email",id:"email",value:g,onChange:e=>{v(e?.target?.value)},onKeyDown:e=>{"Enter"===e?.key&&V()},placeholder:(0,r.__)("Enter your email","betterdocs-ai-chatbot"),required:!0}),(0,e.createElement)("div",{className:"email-icon",onClick:V},(0,e.createElement)(gt,null))),S&&(0,e.createElement)("div",{className:"error-message-container"},T)))),C&&(0,e.createElement)("div",{className:"thankyou-message-container"},(0,r.__)("Thanks! We should reply in a moment.","betterdocs-ai-chatbot")),(0,e.createElement)("div",{className:"chat-footer"},(0,e.createElement)("div",{className:"message-input"+(p&&"false"!==pt("showEmailField")?" disabled":"")},(0,e.createElement)("input",{placeholder:(0,r.__)("Type a message...","betterdocs-ai-chatbot"),value:a,onChange:e=>{const t=e.target.value,n=mt.sanitize("text",t);i(n)},onKeyDown:e=>{c||"Enter"!==e?.key||e?.shiftKey||(e.preventDefault(),j())},rows:"2"}),(0,e.createElement)("button",{onClick:j,style:c?{pointerEvents:"none",opacity:".6"}:{},disabled:c},(0,e.createElement)(vt,null))))))},Et=()=>(0,e.createElement)("svg",{width:22,height:24,viewBox:"0 0 22 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("mask",{id:"a",fill:"#fff"},(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z"})),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 5.5h9c1.886 0 2.829 0 3.415.586S19.5 7.614 19.5 9.5v5.9c0 1.886 0 2.828-.585 3.414-.586.586-1.529.586-3.415.586h-1.464L8.7 24v-4.6H6.5c-1.885 0-2.828 0-3.414-.586S2.5 17.286 2.5 15.4V9.5c0-1.886 0-2.828.586-3.414S4.615 5.5 6.5 5.5m.075 9.104c2.613 2.272 6.046 2.244 8.825.02l-1-1.249c-2.22 1.777-4.788 1.75-6.775.021zM7.15 11.6v-1.5h1.5v1.5zm6.2-1.5v1.5h1.5v-1.5z",fill:"#00B682",className:"chatbotpath"}),(0,e.createElement)("path",{d:"m18.915 6.086-1.061 1.06zm0 12.728-1.061-1.06zm-4.879.586v-1.5h-.557l-.422.364zM8.7 24H7.2v3.274l2.48-2.138zm0-4.6h1.5v-1.5H8.7zm-5.614-.586 1.06-1.06zm12.314-4.19.937 1.172 1.172-.937-.938-1.171zm-8.825-.02-1.131-.985-.985 1.132 1.132.985zm7.826-1.229 1.17-.937-.936-1.171-1.171.937zm-6.776.021.985-1.132-1.132-.984-.985 1.132zM7.15 10.1V8.6h-1.5v1.5zm0 1.5h-1.5v1.5h1.5zm1.5-1.5h1.5V8.6h-1.5zm0 1.5v1.5h1.5v-1.5zm4.7 0h-1.5v1.5h1.5zm0-1.5V8.6h-1.5v1.5zm1.5 1.5v1.5h1.5v-1.5zm0-1.5h1.5V8.6h-1.5zM15.5 4h-9v3h9zm4.475 1.025c-.618-.618-1.37-.843-2.08-.938C17.227 3.997 16.4 4 15.5 4v3c.986 0 1.574.003 1.995.06a1.4 1.4 0 0 1 .354.084l.006.003h-.001q-.002-.001 0 0zM21 9.5c0-.9.004-1.727-.086-2.394-.096-.711-.32-1.463-.939-2.08l-2.121 2.12q0 .002 0 0l.003.005.01.02c.017.044.048.141.073.334.057.422.06 1.01.06 1.995zm0 5.9V9.5h-3v5.9zm-1.025 4.475c.618-.618.843-1.37.939-2.08.09-.668.086-1.495.086-2.395h-3c0 .985-.003 1.573-.06 1.995a1.4 1.4 0 0 1-.083.354l-.003.005q0-.002 0 0zM15.5 20.9c.9 0 1.727.003 2.395-.087.71-.095 1.462-.32 2.08-.938l-2.121-2.121q-.002 0 0 0l-.005.002-.02.01c-.044.018-.14.048-.334.074-.421.057-1.01.06-1.995.06zm-1.464 0H15.5v-3h-1.464zM9.68 25.136l5.336-4.6-1.96-2.272-5.335 4.6zM7.2 19.4V24h3v-4.6zm-.7 1.5h2.2v-3H6.5zm-4.475-1.025c.619.618 1.37.843 2.081.938.668.09 1.494.087 2.394.087v-3c-.985 0-1.573-.003-1.994-.06a1.4 1.4 0 0 1-.354-.084l-.006-.003h.001q0 .002 0 0zM1 15.4c0 .9-.003 1.727.087 2.394.096.711.32 1.463.938 2.08l2.122-2.12q-.002-.002 0 0l-.003-.005-.01-.02a1.4 1.4 0 0 1-.074-.334C4.004 16.973 4 16.385 4 15.4zm0-5.9v5.9h3V9.5zm1.025-4.475c-.618.618-.842 1.37-.938 2.08C.997 7.774 1 8.6 1 9.5h3c0-.985.004-1.573.06-1.995a1.4 1.4 0 0 1 .084-.354l.003-.005q-.002.002 0 0zM6.5 4c-.9 0-1.726-.003-2.394.087-.711.095-1.462.32-2.08.938l2.12 2.121h.001-.001l.006-.002.02-.01c.044-.018.14-.048.334-.074C4.927 7.003 5.516 7 6.5 7zm7.963 9.453c-2.257 1.806-4.878 1.78-6.903.019l-1.97 2.264c3.2 2.783 7.444 2.701 10.746.06zm-1.234.86 1 1.249 2.342-1.874-1-1.25zm-6.588.215c1.24 1.079 2.72 1.67 4.283 1.672 1.558.001 3.077-.584 4.414-1.653l-1.874-2.343c-.885.708-1.759.997-2.537.996-.774 0-1.57-.286-2.317-.936zm1.066 1.06 1.05-1.207-2.264-1.969-1.05 1.207zM5.65 10.1v1.5h3v-1.5zm3-1.5h-1.5v3h1.5zm1.5 3v-1.5h-3v1.5zm-3 1.5h1.5v-3h-1.5zm7.7-1.5v-1.5h-3v1.5zm0-1.5h-1.5v3h1.5zm-1.5 0v1.5h3v-1.5zm0 1.5h1.5v-3h-1.5z",mask:"url(#a)"}),(0,e.createElement)("path",{d:"M11 7V1",stroke:"#00B682",className:"chatbotpathstroke",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.5 13.2v-3.1H.9v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#00B682",className:"chatbotpath"}),(0,e.createElement)("circle",{cx:11,cy:"1.5",r:"1.5",fill:"#00B682",className:"chatbotpath"})),Ct=()=>(0,e.createElement)("svg",{width:21,height:24,viewBox:"0 0 21 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,e.createElement)("path",{d:"M8.55 19.4v-.75H5.6c-.964 0-1.612-.002-2.095-.066-.461-.063-.659-.17-.789-.3l-.53.53.53-.53c-.13-.13-.237-.328-.3-.79-.064-.482-.066-1.13-.066-2.094V9.5c0-.964.002-1.612.067-2.095.062-.461.169-.659.3-.789s.327-.237.788-.3c.483-.064 1.131-.066 2.095-.066h9c.964 0 1.612.002 2.095.067.461.062.659.169.789.3l.53-.531-.53.53c.13.13.237.328.3.79.064.482.066 1.13.066 2.094v5.9c0 .964-.002 1.612-.067 2.095-.062.461-.169.659-.3.789s-.327.237-.788.3c-.483.064-1.131.066-2.095.066h-1.743l-.21.182-4.097 3.531z",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{d:"M6.2 14c2.3 2 5.3 2 7.8 0",stroke:"#344054",strokeWidth:"1.6"}),(0,e.createElement)("path",{d:"M7 10.1v1.5m6.2-1.5v1.5M10.1 7V1",stroke:"#344054",strokeWidth:"1.5"}),(0,e.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.6 13.2v-3.1H0v3.1zm17-3.1v3.1h1.6v-3.1z",fill:"#344054"}),(0,e.createElement)("circle",{cx:"10.1",cy:"1.5",r:"1.5",fill:"#344054"}));(0,t.U2)("tab_chatbot_preview","betterdocs/chattab-preview",((t,n)=>(t.push({id:"chatbot",class:"betterdocs-ia-chatbot",type:"tab",title:(0,r.__)("Chatbot","betterdocs-ai-chatbot"),default:!1,icon:betterdocsAIChatbot?.ai_chatbot_icon?.url?(0,e.createElement)("img",{src:betterdocsAIChatbot.ai_chatbot_icon.url,width:"24",height:"24",style:{opacity:"chatbot"===n?1:.7,filter:"chatbot"===n?"brightness(1.1)":"none"}}):"chatbot"===n?(0,e.createElement)(Et,null):(0,e.createElement)(Ct,null),component:kt,showTab:!0,showTabInComponent:!0}),t)))})()})();