{"AAInfo": "This is a CodeKit 3 project config file. EDITING THIS FILE IS A POOR LIFE DECISION. Doing so may cause CodeKit to crash and/or corrupt your project. Several critical values in this file are 64-bit integers, which JavaScript JSON parsers do not support because JavaScript cannot handle 64-bit integers. These values will be corrupted if the file is parsed with JavaScript. This file is not backwards-compatible with CodeKit 1 or 2. For details, see https://codekitapp.com/", "buildSteps": [{"name": "Process All Remaining Files and Folders", "stepType": 1, "uuidString": "FDF09C14-788E-4A4D-A218-DA6A56580A0D"}], "creatorBuild": "34576", "files": {"/.gitignore": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/.giti<PERSON>re", "oF": 0}, "/admin/admin-init.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/admin/admin-init.php", "oF": 0}, "/admin/img/shortpixel.png": {"ft": 32768, "iS": 3373, "oA": 0, "oAP": "/admin/img/shortpixel.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/admin/plugin-action-meta-links.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/admin/plugin-action-meta-links.php", "oF": 0}, "/admin/svgs-settings-page-help.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/admin/svgs-settings-page-help.php", "oF": 0}, "/admin/svgs-settings-page.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/admin/svgs-settings-page.php", "oF": 0}, "/composer.json": {"ft": 524288, "oA": 1, "oAP": "/composer-min.json", "oF": 0, "oO": 0, "oS": 1}, "/composer.lock": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/composer.lock", "oF": 0}, "/css/jquery.dropdown-min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/css/jquery.dropdown-min-min.css", "oF": 0, "pg": 0}, "/css/svgs-admin-edit-post.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin-edit-post-min.css", "oF": 0, "pg": 0}, "/css/svgs-admin-simple-mode.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin-simple-mode-min.css", "oF": 0, "pg": 0}, "/css/svgs-admin.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin-min.css", "oF": 0, "pg": 0}, "/css/svgs-attachment.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/css/svgs-attachment-min.css", "oF": 0, "pg": 0}, "/functions/attachment.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/attachment.php", "oF": 0}, "/functions/attribute-control.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/attribute-control.php", "oF": 0}, "/functions/enqueue.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/enqueue.php", "oF": 0}, "/functions/featured-image.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/featured-image.php", "oF": 0}, "/functions/localization.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/localization.php", "oF": 0}, "/functions/meta-cleanup.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/meta-cleanup.php", "oF": 0}, "/functions/mime-types.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/mime-types.php", "oF": 0}, "/functions/thumbnail-display.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/functions/thumbnail-display.php", "oF": 0}, "/includes/svg-attributes.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/includes/svg-attributes.php", "oF": 0}, "/includes/svg-tags.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/includes/svg-tags.php", "oF": 0}, "/integrations/wp-all-import.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/integrations/wp-all-import.php", "oF": 0}, "/js/gutenberg-filters.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/gutenberg-filters-min.js", "oF": 2, "sC": 3, "tS": 0}, "/js/jquery.dropdown.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/jquery.dropdown-min.js", "oF": 2, "sC": 3, "tS": 0}, "/js/min/gutenberg-filters-min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/gutenberg-filters-min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/js/min/jquery.dropdown-min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/jquery.dropdown-min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/js/min/svgs-inline-min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/svgs-inline-min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/js/min/svgs-inline-vanilla-min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/svgs-inline-vanilla-min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/js/svgs-inline-vanilla.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/svgs-inline-vanilla-min.js", "oF": 2, "sC": 3, "tS": 0}, "/js/svgs-inline.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/js/min/svgs-inline-min.js", "oF": 2, "sC": 3, "tS": 0}, "/languages/svgsupport-es_ES.mo": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/languages/svgsupport-es_ES.mo", "oF": 0}, "/languages/svgsupport-es_ES.po": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/languages/svgsupport-es_ES.po", "oF": 0}, "/languages/svgsupport-sr_RS.mo": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/languages/svgsupport-sr_RS.mo", "oF": 0}, "/languages/svgsupport-sr_RS.po": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/languages/svgsupport-sr_RS.po", "oF": 0}, "/languages/svgsupport.pot": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/languages/svgsupport.pot", "oF": 0}, "/README.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 1, "oAP": "/README.html", "oF": 1, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/readme.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/readme.txt", "oF": 0}, "/scss/jquery.dropdown.scss": {"aP": 1, "bl": 1, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/css/jquery.dropdown-min.css", "oF": 2, "oS": 3, "pg": 0, "sct": 1}, "/scss/svgs-admin-edit-post.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin-edit-post.css", "oF": 2, "oS": 3, "pg": 0, "sct": 1}, "/scss/svgs-admin-simple-mode.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin-simple-mode.css", "oF": 2, "oS": 3, "pg": 0, "sct": 1}, "/scss/svgs-admin.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/css/svgs-admin.css", "oF": 2, "oS": 3, "pg": 0, "sct": 1}, "/scss/svgs-attachment.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/css/svgs-attachment.css", "oF": 2, "oS": 3, "pg": 0, "sct": 1}, "/svg-support.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/svg-support.php", "oF": 0}, "/svg-support.png": {"ft": 32768, "iS": 38215, "oA": 0, "oAP": "/svg-support.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/uninstall.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/uninstall.php", "oF": 0}, "/vendor/autoload.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/autoload.php", "oF": 0}, "/vendor/composer/autoload_classmap.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/autoload_classmap.php", "oF": 0}, "/vendor/composer/autoload_namespaces.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/autoload_namespaces.php", "oF": 0}, "/vendor/composer/autoload_psr4.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/autoload_psr4.php", "oF": 0}, "/vendor/composer/autoload_real.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/autoload_real.php", "oF": 0}, "/vendor/composer/autoload_static.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/autoload_static.php", "oF": 0}, "/vendor/composer/ClassLoader.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/ClassLoader.php", "oF": 0}, "/vendor/composer/installed.json": {"ft": 524288, "oA": 1, "oAP": "/vendor/composer/installed-min.json", "oF": 0, "oO": 0, "oS": 1}, "/vendor/composer/installed.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/installed.php", "oF": 0}, "/vendor/composer/InstalledVersions.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/InstalledVersions.php", "oF": 0}, "/vendor/composer/LICENSE": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/LICENSE", "oF": 0}, "/vendor/composer/platform_check.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/composer/platform_check.php", "oF": 0}, "/vendor/cure53/dompurify/.babelrc": {"cB": 0, "ft": 8192, "hM": 0, "oA": 1, "oAP": "/vendor/cure53/dompurify/.babelrc", "oF": 0}, "/vendor/cure53/dompurify/.editorconfig": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.editorconfig", "oF": 0}, "/vendor/cure53/dompurify/.github/dependabot.yml": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.github/dependabot.yml", "oF": 0}, "/vendor/cure53/dompurify/.github/FUNDING.yml": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.github/FUNDING.yml", "oF": 0}, "/vendor/cure53/dompurify/.github/ISSUE_TEMPLATE.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/cure53/dompurify/.github/ISSUE_TEMPLATE.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/cure53/dompurify/.github/PULL_REQUEST_TEMPLATE.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/cure53/dompurify/.github/PULL_REQUEST_TEMPLATE.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/cure53/dompurify/.github/workflows/build-and-test.yml": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.github/workflows/build-and-test.yml", "oF": 0}, "/vendor/cure53/dompurify/.github/workflows/codeql-analysis.yml": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.github/workflows/codeql-analysis.yml", "oF": 0}, "/vendor/cure53/dompurify/.gitignore": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.gitignore", "oF": 0}, "/vendor/cure53/dompurify/.nvmrc": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.nvmrc", "oF": 0}, "/vendor/cure53/dompurify/.prettierignore": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.prettierignore", "oF": 0}, "/vendor/cure53/dompurify/.prettierrc": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.prettierrc", "oF": 0}, "/vendor/cure53/dompurify/.settings/.gitignore": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/.settings/.gitignore", "oF": 0}, "/vendor/cure53/dompurify/bower.json": {"ft": 524288, "oA": 1, "oAP": "/vendor/cure53/dompurify/bower-min.json", "oF": 0, "oO": 0, "oS": 1}, "/vendor/cure53/dompurify/demos/advanced-config-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/advanced-config-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/basic-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/basic-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/config-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/config-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-link-proxy-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-link-proxy-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-mentaljs-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-mentaljs-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-node-removal-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-node-removal-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-node-removal2-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-node-removal2-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-proxy-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-proxy-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-removal-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-removal-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-sanitize-css-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-sanitize-css-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-scheme-allowlist.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-scheme-allowlist.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-svg-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-svg-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/hooks-target-blank-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/hooks-target-blank-demo.html", "oF": 0}, "/vendor/cure53/dompurify/demos/lib/Mental.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/demos/lib/Mental-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/demos/README.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/cure53/dompurify/demos/README.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/cure53/dompurify/demos/trusted-types-demo.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/demos/trusted-types-demo.html", "oF": 0}, "/vendor/cure53/dompurify/dist/purify.cjs.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/dist/purify.cjs-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/dist/purify.cjs.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/dist/purify.cjs.js.map", "oF": 0}, "/vendor/cure53/dompurify/dist/purify.es.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/dist/purify.es-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/dist/purify.es.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/dist/purify.es.js.map", "oF": 0}, "/vendor/cure53/dompurify/dist/purify.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/dist/purify-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/dist/purify.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/dist/purify.js.map", "oF": 0}, "/vendor/cure53/dompurify/dist/purify.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/dist/purify.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/dist/purify.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/dist/purify.min.js.map", "oF": 0}, "/vendor/cure53/dompurify/LICENSE": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/LICENSE", "oF": 0}, "/vendor/cure53/dompurify/package-lock.json": {"ft": 524288, "oA": 1, "oAP": "/vendor/cure53/dompurify/package-lock-min.json", "oF": 0, "oO": 0, "oS": 1}, "/vendor/cure53/dompurify/package.json": {"ft": 524288, "oA": 1, "oAP": "/vendor/cure53/dompurify/package-min.json", "oF": 0, "oO": 0, "oS": 1}, "/vendor/cure53/dompurify/README.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/cure53/dompurify/README.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/cure53/dompurify/rollup.config.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/rollup.config-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/scripts/commit-amend-build.sh": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/scripts/commit-amend-build.sh", "oF": 0}, "/vendor/cure53/dompurify/scripts/server.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/scripts/server-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/SECURITY.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/cure53/dompurify/SECURITY.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/cure53/dompurify/src/attrs.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/src/attrs-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/src/license_header": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/src/license_header", "oF": 0}, "/vendor/cure53/dompurify/src/purify.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/src/purify-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/src/regexp.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/src/regexp-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/src/tags.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/src/tags-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/src/utils.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/src/utils-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/bootstrap-test-suite.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/bootstrap-test-suite-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/config/setup.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/config/setup-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/fixtures/expect.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/test/fixtures/expect-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/jsdom-node-runner.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/jsdom-node-runner-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/jsdom-node.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/jsdom-node-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/karma.conf.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/karma.conf-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/karma.custom-launchers.config.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/karma.custom-launchers.config-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/purify.min.spec.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/purify.min.spec-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/purify.spec.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/cure53/dompurify/test/purify.spec-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/test/test-suite.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/vendor/cure53/dompurify/test/test-suite-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/cure53/dompurify/website/index.html": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/cure53/dompurify/website/index.html", "oF": 0}, "/vendor/DOMPurify/DOMPurify.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/vendor/DOMPurify/DOMPurify.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/vendor/enshrined/svg-sanitize/composer.json": {"ft": 524288, "oA": 1, "oAP": "/vendor/enshrined/svg-sanitize/composer-min.json", "oF": 0, "oO": 0, "oS": 1}, "/vendor/enshrined/svg-sanitize/LICENSE": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/LICENSE", "oF": 0}, "/vendor/enshrined/svg-sanitize/README.md": {"cB": 0, "cS": 0, "eF": 1, "eL": 1, "ema": 1, "eSQ": 1, "ft": 4096, "hM": 0, "oA": 0, "oAP": "/vendor/enshrined/svg-sanitize/README.html", "oF": 0, "oFM": 0, "oS": 0, "pHT": 0, "pME": 1, "rFN": 0, "uCM": 0}, "/vendor/enshrined/svg-sanitize/src/data/AllowedAttributes.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/data/AllowedAttributes.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/data/AllowedTags.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/data/AllowedTags.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/data/AttributeInterface.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/data/AttributeInterface.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/data/TagInterface.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/data/TagInterface.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/data/XPath.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/data/XPath.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/ElementReference/Resolver.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/ElementReference/Resolver.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/ElementReference/Subject.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/ElementReference/Subject.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/ElementReference/Usage.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/ElementReference/Usage.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/Exceptions/NestingException.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/Exceptions/NestingException.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/Helper.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/Helper.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/Sanitizer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/Sanitizer.php", "oF": 0}, "/vendor/enshrined/svg-sanitize/src/svg-scanner.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 2, "oAP": "/vendor/enshrined/svg-sanitize/src/svg-scanner.php", "oF": 0}}, "hooks": [], "manualImportLinks": {}, "projectAttributes": {"creationDate": 507572464, "displayValue": "svg-support", "displayValueWasSetByUser": 0, "iconImageName": "/svg-support.png", "iconImageWasSetByUser": 1}, "projectSettings": {"abortBuildOnError": 1, "allowInjectionReloads": 1, "alwaysUseExternalServer": 0, "animateCSSInjections": 1, "autoBuildNewItems": 1, "autoprefixerEnableIEGrid": 0, "babel7PresetType": 1, "babelAllowRCFiles": 0, "babelAuxiliaryCommentAfter": "", "babelAuxiliaryCommentBefore": "", "babelConfigType": 0, "babelCustomPluginsList": "", "babelCustomPresetsList": "", "babelExcludeString": "/\\/node_modules\\//, /\\/core-js\\//, /\\/bower_components\\//", "babelInsertModuleIDs": 0, "babelModuleID": "", "babelNoComments": 0, "babelPlugins": {"arrow-functions": {"active": 0}, "async-generator-functions": {"active": 0}, "async-to-generator": {"active": 0}, "block-scoped-functions": {"active": 0}, "block-scoping": {"active": 0}, "class-properties": {"active": 0}, "classes": {"active": 0}, "computed-properties": {"active": 0}, "decorators": {"active": 0}, "destructuring": {"active": 0}, "do-expressions": {"active": 0}, "dotall-regex": {"active": 0}, "duplicate-keys": {"active": 0}, "exponentiation-operator": {"active": 0}, "export-default-from": {"active": 0}, "export-namespace-from": {"active": 0}, "external-helpers": {"active": 0}, "flow-strip-types": {"active": 0}, "for-of": {"active": 0}, "function-bind": {"active": 0}, "function-name": {"active": 0}, "function-sent": {"active": 0}, "inline-consecutive-adds": {"active": 0}, "inline-environment-variables": {"active": 0}, "instanceof": {"active": 0}, "jscript": {"active": 0}, "literals": {"active": 0}, "logical-assignment-operators": {"active": 0}, "member-expression-literals": {"active": 0}, "merge-sibling-variables": {"active": 0}, "minify-booleans": {"active": 0}, "minify-builtins": {"active": 0}, "minify-constant-folding": {"active": 0}, "minify-dead-code-elimination": {"active": 0}, "minify-flip-comparisons": {"active": 0}, "minify-guarded-expressions": {"active": 0}, "minify-infinity": {"active": 0}, "minify-mangle-names": {"active": 0}, "minify-numeric-literals": {"active": 0}, "minify-simplify": {"active": 0}, "minify-type-constructors": {"active": 0}, "modules-amd": {"active": 0}, "modules-commonjs": {"active": 0}, "modules-systemjs": {"active": 0}, "modules-umd": {"active": 0}, "named-capturing-groups-regex": {"active": 0}, "new-target": {"active": 0}, "node-env-inline": {"active": 0}, "nullish-coalescing-operator": {"active": 0}, "numeric-separator": {"active": 0}, "object-assign": {"active": 0}, "object-rest-spread": {"active": 0}, "object-set-prototype-of-to-assign": {"active": 0}, "object-super": {"active": 0}, "optional-catch-binding": {"active": 0}, "optional-chaining": {"active": 0}, "parameters": {"active": 0}, "partial-application": {"active": 0}, "pipeline-operator": {"active": 0}, "private-methods": {"active": 0}, "property-literals": {"active": 0}, "property-mutators": {"active": 0}, "proto-to-assign": {"active": 0}, "react-constant-elements": {"active": 0}, "react-display-name": {"active": 0}, "react-inline-elements": {"active": 0}, "react-jsx": {"active": 0}, "react-jsx-compat": {"active": 0}, "react-jsx-self": {"active": 0}, "react-jsx-source": {"active": 0}, "regenerator": {"active": 0}, "regexp-constructors": {"active": 0}, "remove-console": {"active": 0}, "remove-debugger": {"active": 0}, "remove-undefined": {"active": 0}, "reserved-words": {"active": 0}, "runtime": {"active": 0}, "shorthand-properties": {"active": 0}, "simplify-comparison-operators": {"active": 0}, "spread": {"active": 0}, "sticky-regex": {"active": 0}, "strict-mode": {"active": 0}, "template-literals": {"active": 0}, "throw-expressions": {"active": 0}, "typeof-symbol": {"active": 0}, "undefined-to-void": {"active": 0}, "unicode-property-regex": {"active": 0}, "unicode-regex": {"active": 0}}, "babelRetainLines": 0, "babelUseBuiltInsType": 0, "bowerAbbreviatedPath": "bower_components", "bowerForceLatestOnConflict": 1, "bowerTargetDependencyListType": 1, "bowerUseExactVersion": 0, "browserRefreshDelay": 0, "browserslistString": ">0.2%, last 2 versions, Firefox ESR, not dead", "buildEnvironment": 0, "buildFolderActive": 0, "buildFolderName": "build", "cleanBuild": 1, "cssoForceMediaMerge": 0, "cssoRestructure": 1, "environmentVariableEntries": ["NODE_ENV:::production"], "esLintConfigFileHandlingType": 0, "esLintECMAVersion": 7, "esLintEnvironmentsMask": 1, "esLintRules": {"accessor-pairs": {"active": 0, "optionString": "{'setWithoutGet': true, 'getWithoutSet': false}"}, "array-bracket-newline": {"active": 0, "optionString": "{'multiline': true, 'minItems': null}"}, "array-bracket-spacing": {"active": 0, "optionString": "'never', {'singleValue': false, 'objectsInArrays': false, 'arraysInArrays': false}"}, "array-callback-return": {"active": 0, "optionString": ""}, "array-element-newline": {"active": 0, "optionString": "'always'"}, "arrow-body-style": {"active": 0, "optionString": "'as-needed', {'requireReturnForObjectLiteral': false}"}, "arrow-parens": {"active": 0, "optionString": "'always'"}, "arrow-spacing": {"active": 0, "optionString": "{'before': true, 'after': true}"}, "block-scoped-var": {"active": 0}, "block-spacing": {"active": 0, "optionString": "'always'"}, "brace-style": {"active": 0, "optionString": "'1tbs', {'allowSingleLine': true}"}, "camelcase": {"active": 0, "optionString": "{'properties': 'always'}"}, "capitalized-comments": {"active": 0, "optionString": "'always', {'ignoreInlineComments': false, 'ignoreConsecutiveComments': false}"}, "class-methods-use-this": {"active": 0, "optionString": "{'exceptMethods': []}"}, "comma-dangle": {"active": 1, "optionString": "'never'"}, "comma-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "comma-style": {"active": 0, "optionString": "'last'"}, "complexity": {"active": 0, "optionString": "20"}, "computed-property-spacing": {"active": 0, "optionString": "'never'"}, "consistent-return": {"active": 0, "optionString": "{'treatUndefinedAsUnspecified': false}"}, "consistent-this": {"active": 0, "optionString": "'that'"}, "constructor-super": {"active": 1}, "curly": {"active": 0, "optionString": "'all'"}, "default-case": {"active": 0}, "default-case-last": {"active": 0}, "default-param-last": {"active": 0}, "dot-location": {"active": 0, "optionString": "'object'"}, "dot-notation": {"active": 0, "optionString": "{'allowKeywords': false}"}, "eol-last": {"active": 0, "optionString": "'always'"}, "eqeqeq": {"active": 0, "optionString": "'always', {'null': 'always'}"}, "for-direction": {"active": 0}, "func-call-spacing": {"active": 0, "optionString": "'never'"}, "func-name-matching": {"active": 0, "optionString": "'always', {'includeCommonJSModuleExports': false}"}, "func-names": {"active": 0, "optionString": "'always'"}, "func-style": {"active": 0, "optionString": "'expression'"}, "function-call-argument-newline": {"active": 0, "optionString": "'always'"}, "function-paren-newline": {"active": 0, "optionString": "'multiline'"}, "generator-star-spacing": {"active": 0, "optionString": "{'before': true, 'after': false}"}, "getter-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "grouped-accessor-pairs": {"active": 0, "optionString": "'anyOrder'"}, "guard-for-in": {"active": 0}, "id-denylist": {"active": 0, "optionString": "'data', 'err', 'e', 'cb', 'callback'"}, "id-length": {"active": 0, "optionString": "{'min': 2, 'max': 1000, 'properties': 'always', 'exceptions': ['x', 'i', 'y']}"}, "id-match": {"active": 0, "optionString": "'^[a-z]+([A-Z][a-z]+)*$', {'properties': false, 'onlyDeclarations': true}"}, "implicit-arrow-linebreak": {"active": 0, "optionString": "'beside'"}, "indent": {"active": 0, "optionString": "4, {'SwitchCase': 0, 'VariableDeclarator': 1, 'outerIIFEBody': 1, }"}, "init-declarations": {"active": 0, "optionString": "'always',  {'ignoreForLoopInit': true}"}, "jsx-quotes": {"active": 0, "optionString": "'prefer-double'"}, "key-spacing": {"active": 0, "optionString": "{'singleLine': {'beforeColon': false, 'afterColon': true, 'mode':'strict'}, 'multiLine': {'beforeColon': false, 'afterColon': true, 'align': 'value', 'mode':'minimum'}}"}, "keyword-spacing": {"active": 0, "optionString": "{'before': true, 'after': true, 'overrides': {}}"}, "line-comment-position": {"active": 0, "optionString": "{'position': 'above'}"}, "linebreak-style": {"active": 0, "optionString": "'unix'"}, "lines-around-comment": {"active": 0, "optionString": "{'beforeBlockComment': true}"}, "lines-between-class-members": {"active": 0, "optionString": "'always', {exceptAfterSingleLine: false}"}, "logical-assignment-operators": {"active": 0, "optionString": "'always',  {'enforceForIfStatements': false}"}, "max-classes-per-file": {"active": 0, "optionString": "1"}, "max-depth": {"active": 0, "optionString": "{'max': 4}"}, "max-len": {"active": 0, "optionString": "{'code': 80, 'comments': 80, 'tabWidth': 4, 'ignoreUrls': true, 'ignoreStrings': true, 'ignoreTemplateLiterals': true, 'ignoreRegExpLiterals': true}"}, "max-lines": {"active": 0, "optionString": "{'max': 300, 'skipBlankLines': true, 'skipComments': true}"}, "max-lines-per-function": {"active": 0, "optionString": "{'max': 50, 'skipBlankLines': true, 'skipComments': true, 'IIFEs': false}"}, "max-nested-callbacks": {"active": 0, "optionString": "{'max': 10}"}, "max-params": {"active": 0, "optionString": "{'max': 4}"}, "max-statements": {"active": 0, "optionString": "{'max': 10}, {'ignoreTopLevelFunctions': true}"}, "max-statements-per-line": {"active": 0, "optionString": "{'max': 1}"}, "multiline-comment-style": {"active": 0, "optionString": "'starred-block'"}, "multiline-ternary": {"active": 0, "optionString": "'always'"}, "new-cap": {"active": 0, "optionString": "{'newIsCap': true, 'capIsNew': true, 'newIsCapExceptions': [], 'capIsNewExceptions': ['Array', 'Boolean', 'Date', 'Error', 'Function', 'Number', 'Object', 'RegExp', 'String', 'Symbol'], 'properties': true}"}, "new-parens": {"active": 0, "optionString": ""}, "newline-per-chained-call": {"active": 0, "optionString": "{'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>': 2}"}, "no-alert": {"active": 0}, "no-array-constructor": {"active": 0}, "no-async-promise-executor": {"active": 0}, "no-await-in-loop": {"active": 0}, "no-bitwise": {"active": 0, "optionString": "{'allow': ['~'], 'int32Hint': true}"}, "no-caller": {"active": 0}, "no-case-declarations": {"active": 1}, "no-class-assign": {"active": 1}, "no-compare-neg-zero": {"active": 0}, "no-cond-assign": {"active": 1, "optionString": "'except-parens'"}, "no-confusing-arrow": {"active": 0, "optionString": "{'allowParens': false}"}, "no-console": {"active": 1, "optionString": "{'allow': ['warn', 'error']}"}, "no-const-assign": {"active": 1}, "no-constant-binary-expression": {"active": 0}, "no-constant-condition": {"active": 1, "optionString": "{'checkLoops': true}"}, "no-constructor-return": {"active": 0}, "no-continue": {"active": 0}, "no-control-regex": {"active": 1}, "no-debugger": {"active": 1}, "no-delete-var": {"active": 1}, "no-div-regex": {"active": 0}, "no-dupe-args": {"active": 1}, "no-dupe-class-members": {"active": 1}, "no-dupe-else-if": {"active": 1}, "no-dupe-keys": {"active": 1}, "no-duplicate-case": {"active": 1}, "no-duplicate-imports": {"active": 0, "optionString": "{'includeExports': false}"}, "no-else-return": {"active": 0}, "no-empty": {"active": 1, "optionString": "{'allowEmptyCatch': false}"}, "no-empty-character-class": {"active": 1}, "no-empty-function": {"active": 0, "optionString": "{'allow': []}"}, "no-empty-pattern": {"active": 1}, "no-empty-static-block": {"active": 1}, "no-eq-null": {"active": 0}, "no-eval": {"active": 0, "optionString": "{'allowIndirect': false}"}, "no-ex-assign": {"active": 1}, "no-extend-native": {"active": 0, "optionString": "{'exceptions': []}"}, "no-extra-bind": {"active": 0}, "no-extra-boolean-cast": {"active": 1}, "no-extra-label": {"active": 0}, "no-extra-parens": {"active": 1, "optionString": "'all', {'conditionalAssign': false, 'returnAssign': false, 'nestedBinaryExpressions': false}"}, "no-extra-semi": {"active": 1}, "no-fallthrough": {"active": 1, "optionString": "{'allowEmptyCase': false}"}, "no-floating-decimal": {"active": 0}, "no-func-assign": {"active": 1}, "no-global-assign": {"active": 0, "optionString": "{'exceptions': []}"}, "no-implicit-coercion": {"active": 0, "optionString": "{'boolean': true, 'number': true, 'string': true, 'allow': []}"}, "no-implicit-globals": {"active": 0}, "no-implied-eval": {"active": 0}, "no-import-assign": {"active": 1}, "no-inline-comments": {"active": 0}, "no-inner-declarations": {"active": 1, "optionString": "'functions'"}, "no-invalid-regexp": {"active": 1, "optionString": "{'allowConstructorFlags': ['u', 'y']}"}, "no-invalid-this": {"active": 0, "optionString": ""}, "no-irregular-whitespace": {"active": 1, "optionString": "{'skipStrings': true, 'skipComments': false, 'skipRegExps': true, 'skipTemplates': true}"}, "no-iterator": {"active": 0}, "no-label-var": {"active": 0}, "no-labels": {"active": 0, "optionString": "{'allowLoop': false, 'allowSwitch': false}"}, "no-lone-blocks": {"active": 0}, "no-lonely-if": {"active": 0}, "no-loop-func": {"active": 0}, "no-loss-of-precision": {"active": 0}, "no-magic-numbers": {"active": 0, "optionString": "{'ignore': [], 'ignoreArrayIndexes': true, 'enforceConst': false, 'detectObjects': false}"}, "no-misleading-character-class": {"active": 0}, "no-mixed-operators": {"active": 0, "optionString": "{'groups': [['+', '-', '*', '/', '%', '**'], ['&', '|', '^', '~', '<<', '>>', '>>>'], ['==', '!=', '===', '!==', '>', '>=', '<', '<='], ['&&', '||'], ['in', 'instanceof']], 'allowSamePrecedence': true}"}, "no-mixed-spaces-and-tabs": {"active": 0, "optionString": ""}, "no-multi-assign": {"active": 0, "optionString": "{'ignoreNonDeclaration': false}"}, "no-multi-spaces": {"active": 0, "optionString": "{'exceptions': {'Property': true, 'BinaryExpression': false, 'VariableDeclarator': false, 'ImportDeclaration': false}}"}, "no-multi-str": {"active": 0}, "no-multiple-empty-lines": {"active": 0, "optionString": "{'max': 2, 'maxBOF': 2, 'maxEOF': 2}"}, "no-negated-condition": {"active": 0}, "no-nested-ternary": {"active": 0}, "no-new": {"active": 0}, "no-new-func": {"active": 0}, "no-new-native-nonconstructor": {"active": 1}, "no-new-symbol": {"active": 1}, "no-new-wrappers": {"active": 0}, "no-nonoctal-decimal-escape": {"active": 0}, "no-obj-calls": {"active": 1}, "no-object-constructor": {"active": 0}, "no-octal": {"active": 1}, "no-octal-escape": {"active": 0}, "no-param-reassign": {"active": 0, "optionString": "{'props': false}"}, "no-plusplus": {"active": 0, "optionString": "{'allowForLoopAfterthoughts': false}"}, "no-promise-executor-return": {"active": 0}, "no-proto": {"active": 0}, "no-prototype-builtins": {"active": 0}, "no-redeclare": {"active": 1, "optionString": "{'builtinGlobals': false}"}, "no-regex-spaces": {"active": 1}, "no-restricted-exports": {"active": 0, "optionString": "{'restrictedNamedExports': []}"}, "no-restricted-globals": {"active": 0, "optionString": "'event', 'fdescribe'"}, "no-restricted-imports": {"active": 0, "optionString": ""}, "no-restricted-properties": {"active": 0, "optionString": "[{'object': 'disallowedObjectName', 'property': 'disallowedPropertyName'}, {'object': 'disallowedObjectName', 'property': 'anotherDisallowedPropertyName', 'message': 'Please use allowedObjectName.allowedPropertyName.'}]"}, "no-restricted-syntax": {"active": 0, "optionString": "'FunctionExpression', 'WithStatement'"}, "no-return-assign": {"active": 0, "optionString": "'except-parens'"}, "no-script-url": {"active": 0}, "no-self-assign": {"active": 1, "optionString": "{'props': false}"}, "no-self-compare": {"active": 1}, "no-sequences": {"active": 0, "optionString": "{'allowInParentheses': true}"}, "no-setter-return": {"active": 1}, "no-shadow": {"active": 0, "optionString": "{'builtinGlobals': false, 'hoist': 'functions', 'allow': []}"}, "no-shadow-restricted-names": {"active": 0}, "no-sparse-arrays": {"active": 1}, "no-tabs": {"active": 0, "optionString": ""}, "no-template-curly-in-string": {"active": 1}, "no-ternary": {"active": 0}, "no-this-before-super": {"active": 1}, "no-throw-literal": {"active": 0}, "no-trailing-spaces": {"active": 0, "optionString": "{'skipBlankLines': false}"}, "no-undef": {"active": 1, "optionString": "{'typeof': false}"}, "no-undef-init": {"active": 0}, "no-undefined": {"active": 0}, "no-underscore-dangle": {"active": 0, "optionString": "{'allow': [], 'allowAfterThis': false, 'allowAfterSuper': false}"}, "no-unexpected-multiline": {"active": 1}, "no-unmodified-loop-condition": {"active": 0}, "no-unneeded-ternary": {"active": 0, "optionString": "{'defaultAssignment': true}"}, "no-unreachable": {"active": 1}, "no-unreachable-loop": {"active": 0, "optionString": "{'ignore': []}"}, "no-unsafe-finally": {"active": 1}, "no-unsafe-negation": {"active": 0, "optionString": ""}, "no-unsafe-optional-chaining": {"active": 0, "optionString": "{'disallowArithmeticOperators': false}"}, "no-unused-expressions": {"active": 0, "optionString": "{'allowShortCircuit': false, 'allowTernary': false}"}, "no-unused-labels": {"active": 1}, "no-unused-private-class-members": {"active": 0}, "no-unused-vars": {"active": 1, "optionString": "{'vars': 'all', 'args': 'after-used', 'caughtErrors': 'none'}"}, "no-use-before-define": {"active": 0, "optionString": "{'functions': true, 'classes': true}"}, "no-useless-backreference": {"active": 0}, "no-useless-call": {"active": 0}, "no-useless-catch": {"active": 0}, "no-useless-computed-key": {"active": 1, "optionString": ""}, "no-useless-concat": {"active": 0}, "no-useless-constructor": {"active": 0}, "no-useless-escape": {"active": 0}, "no-useless-rename": {"active": 0, "optionString": "{'ignoreDestructuring': false, 'ignoreImport': false, 'ignoreExport': false}"}, "no-useless-return": {"active": 0}, "no-var": {"active": 0}, "no-void": {"active": 0, "optionString": ""}, "no-warning-comments": {"active": 0, "optionString": "{'terms': ['todo', 'fixme', 'xxx'], 'location': 'start'}"}, "no-whitespace-before-property": {"active": 0}, "no-with": {"active": 0}, "nonblock-statement-body-position": {"active": 0, "optionString": "'beside'"}, "object-curly-newline": {"active": 0, "optionString": "{'ObjectExpression': {'multiline': true}, 'ObjectPattern': {'multiline': true}}"}, "object-curly-spacing": {"active": 0, "optionString": "'never'"}, "object-property-newline": {"active": 0, "optionString": "{'allowMultiplePropertiesPerLine': true}"}, "object-shorthand": {"active": 0, "optionString": "'always', {'avoidQuotes': false, 'ignoreConstructors': false}"}, "one-var": {"active": 0, "optionString": "'always'"}, "one-var-declaration-per-line": {"active": 0, "optionString": "'always'"}, "operator-assignment": {"active": 0, "optionString": "'always'"}, "operator-linebreak": {"active": 0, "optionString": "'after', {'overrides': {'?': 'after', '+=': 'none'}}"}, "padded-blocks": {"active": 0, "optionString": "{'blocks': 'always', 'switches': 'always', 'classes': 'always'}"}, "padding-line-between-statements": {"active": 0, "optionString": "{blankLine: 'always', prev:'*', next:'return'}"}, "prefer-arrow-callback": {"active": 0}, "prefer-const": {"active": 0, "optionString": "{'destructuring': 'any', 'ignoreReadBeforeAssign': false}"}, "prefer-destructuring": {"active": 0, "optionString": "{'array': true, 'object': true}, {'enforceForRenamedProperties': false}"}, "prefer-exponentiation-operator": {"active": 0}, "prefer-named-capture-group": {"active": 0}, "prefer-numeric-literals": {"active": 0}, "prefer-object-has-own": {"active": 0}, "prefer-object-spread": {"active": 0}, "prefer-promise-reject-errors": {"active": 0, "optionString": "{'allowEmptyReject': false}"}, "prefer-regex-literals": {"active": 0}, "prefer-rest-params": {"active": 0}, "prefer-spread": {"active": 0}, "prefer-template": {"active": 0}, "quote-props": {"active": 0, "optionString": "'always'"}, "quotes": {"active": 0, "optionString": "'double', {'avoidEscape': true, 'allowTemplateLiterals': true}"}, "radix": {"active": 0, "optionString": "'always'"}, "require-atomic-updates": {"active": 0, "optionString": "{'allowProperties': false}"}, "require-await": {"active": 0}, "require-unicode-regexp": {"active": 0}, "require-yield": {"active": 0}, "rest-spread-spacing": {"active": 0, "optionString": "'never'"}, "semi": {"active": 0, "optionString": "'always', {'omitLastInOneLineBlock': false}"}, "semi-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "semi-style": {"active": 0, "optionString": "'last'"}, "sort-imports": {"active": 0, "optionString": "{'ignoreCase': false, 'ignoreMemberSort': true, 'memberSyntaxSortOrder': ['none', 'all', 'multiple', 'single']}"}, "sort-keys": {"active": 0, "optionString": "'asc', {'caseSensitive': true, 'natural': false}"}, "sort-vars": {"active": 0, "optionString": "{'ignoreCase': false}"}, "space-before-blocks": {"active": 0, "optionString": "{'functions': 'always', 'keywords': 'always', 'classes': 'always'}"}, "space-before-function-paren": {"active": 0, "optionString": "{'anonymous': 'always', 'named': 'never'}"}, "space-in-parens": {"active": 0, "optionString": "'never', {'exceptions': []}"}, "space-infix-ops": {"active": 0, "optionString": "{'int32Hint': false}"}, "space-unary-ops": {"active": 0, "optionString": "{'words': true, 'nonwords': false, 'overrides': {}}"}, "spaced-comment": {"active": 0, "optionString": "'always', {'line': {'markers': ['/'], 'exceptions': ['-', '+']}, 'block': {'markers': ['!'], 'exceptions': ['*'], 'balanced': false}}"}, "strict": {"active": 0, "optionString": "'safe'"}, "switch-colon-spacing": {"active": 0, "optionString": "{'after': true, 'before': false}"}, "symbol-description": {"active": 0}, "template-curly-spacing": {"active": 0, "optionString": "'never'"}, "template-tag-spacing": {"active": 0, "optionString": "'never'"}, "unicode-bom": {"active": 0, "optionString": "'never'"}, "use-isnan": {"active": 1, "optionString": ""}, "valid-typeof": {"active": 1, "optionString": "{'requireStringLiterals': true}"}, "vars-on-top": {"active": 0}, "wrap-iife": {"active": 0, "optionString": "'outside'"}, "wrap-regex": {"active": 0}, "yield-star-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "yoda": {"active": 0, "optionString": "'never', {'except<PERSON><PERSON><PERSON>': false, 'onlyEquality': false}"}}, "esLintSourceType": 0, "externalServerAddress": "http://localhost:8888", "gitIgnoreBuildFolder": 1, "hideConfigFile": 0, "jsCheckerReservedNamesString": "", "languageDefaultsCOFFEE": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "minifierStyle": 1, "outputStyle": 0, "sourceMapStyle": 0, "transpilerStyle": 1}, "languageDefaultsCSS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.css", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "combineImports": 0, "cssoStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 1, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsGIF": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.gif", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsHAML": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "escapeHTMLCharacters": 0, "htmlMinifierStyle": 0, "noEscapeInAttributes": 0, "outputFormat": 2, "shouldRunCacheBuster": 0, "useCDATA": 0, "useDoubleQuotes": 0, "useUnixNewlines": 0}, "languageDefaultsJPG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.jpg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "outputFormat": 0, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsJS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.js", "autoOutputPathRelativePath": "/min", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "bundleFormat": 0, "minifierStyle": 1, "sourceMapStyle": 0, "syntaxCheckerStyle": 3, "transpilerStyle": 0}, "languageDefaultsJSON": {"autoOutputAction": 1, "autoOutputPathFilenamePattern": "*-min.json", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "orderOutput": 0, "outputStyle": 1}, "languageDefaultsKIT": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "kit", "autoOutputPathReplace2": "html", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsLESS": {"allowInsecureImports": 0, "autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "less", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "enableJavascript": 0, "mathStyle": 0, "outputStyle": 0, "purgeCSSStyle": 0, "rewriteURLStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 1, "strictImports": 0, "strictUnits": 0}, "languageDefaultsMARKDOWN": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "criticStyle": 0, "enableFootnotes": 1, "enableLabels": 1, "enableSmartQuotes": 1, "htmlMinifierStyle": 0, "maskEmailAddresses": 1, "outputFormat": 0, "outputStyle": 0, "parseMetadata": 1, "processHTML": 0, "randomFootnoteNumbers": 0, "shouldRunCacheBuster": 0, "useCompatibilityMode": 0}, "languageDefaultsOTHER": {"autoOutputAction": 2, "autoOutputPathFilenamePattern": "*.*", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsPNG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.png", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "optimizerType": 1, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsPUG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileDebug": 1, "htmlMinifierStyle": 0, "outputStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSASS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "sass", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "compilerType": 1, "cssoStyle": 0, "decimalPrecision": 10, "emitCharset": 1, "outputStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSLIM": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileOnly": 0, "htmlMinifierStyle": 0, "logicless": 0, "outputFormat": 0, "outputStyle": 1, "railsCompatible": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSTYLUS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "stylus", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "debugStyle": 0, "importCSS": 0, "outputStyle": 0, "purgeCSSStyle": 0, "resolveRelativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSVG": {"autoOutputAction": 2, "autoOutputPathFilenamePattern": "*.svg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "pluginMask": 3758088159}, "languageDefaultsTS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "/js", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createDeclarationFile": 0, "jsxMode": 0, "minifierStyle": 0, "moduleDetectionType": 0, "moduleResolutionType": 0, "moduleType": 2, "removeComments": 0, "sourceMapStyle": 0, "targetECMAVersion": 2018}, "languageDefaultsUserDefined": [], "npmAbbreviatedPath": "", "npmCreatePackageLock": 1, "npmInstallOptionalDependencies": 0, "npmSaveExactVersion": 0, "npmTargetDependencyListType": 1, "overrideExternalServerCSS": 0, "previewPathAddition": "", "purgeCSS": {"blocklistEntries": [], "contentEntries": ["**/*.html", "**/*.htm", "**/*.shtml", "**/*.xhtml", "**/*.php", "**/*.js", "**/*.ts", "**/*.coffee", "**/*.erb", "**/*.pug", "**/*.jade", "**/*.slim", "**/*.haml", "**/*.md", "**/*.kit"], "removeFontFace": 0, "removeKeyframes": 0, "removeVariables": 0, "safelistEntries": [], "skippedEntries": ["node_modules/**"]}, "rollupContext": "", "rollupExternalEntries": [], "rollupReplacementEntries": ["process.env.NODE_ENV:::$NODE_ENV", "ENVIRONMENT:::$NODE_ENV"], "rollupTreeshakingEnabled": 1, "rollupTreeshakingModuleSideEffects": 1, "skippedFoldersString": "log, _logs, logs, _cache, cache, /storage/framework/sessions, node_modules", "sourceFolderName": "source", "susyVersion": 3, "tsAllowArbitraryExtensions": 0, "tsAllowImportingTSExtensions": 0, "tsAllowSyntheticDefaultImports": 0, "tsAllowUMDGlobalAccess": 0, "tsAllowUnreachableCode": 0, "tsAllowUnusedLabels": 0, "tsAlwaysStrict": 0, "tsDownlevelIteration": 0, "tsEmitBOM": 0, "tsEmitDecoratorMetadata": 0, "tsESModuleInterop": 0, "tsExactOptionalPropertyTypes": 0, "tsExperimentalDecorators": 0, "tsForceConsistentCasingInFileNames": 0, "tsImportHelpers": 0, "tsIsolatedModules": 0, "tsJSXFactory": "React.createElement", "tsNoEmitHelpers": 0, "tsNoFallthroughCasesInSwitch": 0, "tsNoImplicitAny": 0, "tsNoImplicitOverride": 0, "tsNoImplicitReturns": 0, "tsNoImplicitThis": 0, "tsNoLib": 0, "tsNoPropertyAccessFromIndexSignature": 0, "tsNoResolve": 0, "tsNoUncheckedIndexAccess": 0, "tsNoUnusedLocals": 0, "tsNoUnusedParameters": 0, "tsPreserveConstEnums": 0, "tsPreserveSymlinks": 0, "tsResolveJsonModule": 0, "tsSkipDefaultLibCheck": 0, "tsSkipLibCheck": 0, "tsStrictBindCallApply": 0, "tsStrictFunctionTypes": 0, "tsStrictNullChecks": 0, "tsStrictPropertyInitialization": 0, "tsStripInternal": 0, "tsUseDefineForClassFields": 0, "tsUseUnknownInCatchVariables": 0, "tsVerbatimModuleSyntax": 0, "uglifyDefinesString": "", "uglifyFlags2": {"arguments": {"active": 1, "flagValue": -1}, "arrows": {"active": 1, "flagValue": -1}, "ascii_only": {"active": 0, "flagValue": -1}, "booleans": {"active": 1, "flagValue": -1}, "booleans_as_integers": {"active": 0, "flagValue": -1}, "braces": {"active": 0, "flagValue": -1}, "collapse_vars": {"active": 1, "flagValue": -1}, "comments": {"active": 0, "flagValue": 1}, "comparisons": {"active": 1, "flagValue": -1}, "computed_props": {"active": 1, "flagValue": -1}, "conditionals": {"active": 1, "flagValue": -1}, "dead_code": {"active": 0, "flagValue": -1}, "directives": {"active": 1, "flagValue": -1}, "drop_console": {"active": 0, "flagValue": -1}, "drop_debugger": {"active": 1, "flagValue": -1}, "ecma": {"active": 1, "flagValue": 5}, "eval": {"active": 0, "flagValue": -1}, "evaluate": {"active": 1, "flagValue": -1}, "expression": {"active": 0, "flagValue": -1}, "hoist_funs": {"active": 1, "flagValue": -1}, "hoist_props": {"active": 1, "flagValue": -1}, "hoist_vars": {"active": 0, "flagValue": -1}, "ie8": {"active": 0, "flagValue": -1}, "if_return": {"active": 1, "flagValue": -1}, "indent_level": {"active": 0, "flagValue": 4}, "indent_start": {"active": 0, "flagValue": 0}, "inline": {"active": 1, "flagValue": 3}, "inline_script": {"active": 1, "flagValue": -1}, "join_vars": {"active": 1, "flagValue": -1}, "keep_classnames": {"active": 0, "flagValue": -1}, "keep_fargs": {"active": 0, "flagValue": -1}, "keep_fnames": {"active": 0, "flagValue": -1}, "keep_infinity": {"active": 0, "flagValue": -1}, "keep_numbers": {"active": 0, "flagValue": -1}, "keep_quoted_props": {"active": 0, "flagValue": -1}, "loops": {"active": 1, "flagValue": -1}, "max_line_len": {"active": 1, "flagValue": 32000}, "module": {"active": 0, "flagValue": -1}, "negate_iife": {"active": 1, "flagValue": -1}, "passes": {"active": 1, "flagValue": 1}, "preserve_annotations": {"active": 0, "flagValue": -1}, "properties": {"active": 1, "flagValue": -1}, "pure_getters": {"active": 0, "flagValue": -1}, "quote_keys": {"active": 0, "flagValue": -1}, "quote_style": {"active": 1, "flagValue": 0}, "reduce_funcs": {"active": 1, "flagValue": -1}, "reduce_vars": {"active": 1, "flagValue": -1}, "safari10": {"active": 0, "flagValue": -1}, "semicolons": {"active": 1, "flagValue": -1}, "sequences": {"active": 1, "flagValue": -1}, "shebang": {"active": 1, "flagValue": -1}, "side_effects": {"active": 1, "flagValue": -1}, "switches": {"active": 1, "flagValue": -1}, "toplevel": {"active": 0, "flagValue": -1}, "typeofs": {"active": 1, "flagValue": -1}, "unsafe": {"active": 0, "flagValue": -1}, "unsafe_arrows": {"active": 0, "flagValue": -1}, "unsafe_comps": {"active": 0, "flagValue": -1}, "unsafe_Function": {"active": 0, "flagValue": -1}, "unsafe_math": {"active": 0, "flagValue": -1}, "unsafe_methods": {"active": 0, "flagValue": -1}, "unsafe_proto": {"active": 0, "flagValue": -1}, "unsafe_regexp": {"active": 0, "flagValue": -1}, "unsafe_undefined": {"active": 0, "flagValue": -1}, "unused": {"active": 0, "flagValue": -1}, "warnings": {"active": 0, "flagValue": -1}, "webkit": {"active": 0, "flagValue": -1}, "wrap_func_args": {"active": 1, "flagValue": -1}, "wrap_iife": {"active": 0, "flagValue": -1}}, "uglifyMangleNames": 1, "uglifyReservedNamesString": "$", "webpPresets": {}, "websiteRelativeRoot": ""}, "settingsFileVersion": "3"}