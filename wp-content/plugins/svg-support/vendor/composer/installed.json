{"packages": [{"name": "cure53/dompurify", "version": "2.5.8", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://github.com/cure53/DOMPurify/archive/refs/tags/2.5.8.zip"}, "type": "library", "installation-source": "dist", "install-path": "../cure53/dompurify"}, {"name": "enshrined/svg-sanitize", "version": "0.21.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "reference": "5e477468fac5c5ce933dce53af3e8e4e58dcccc9", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^6.5 || ^8.5"}, "time": "2025-01-13T09:32:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.21.0"}, "install-path": "../enshrined/svg-sanitize"}], "dev": true, "dev-package-names": []}