<?php return array(
    'root' => array(
        'name' => 'benbodhi/svg-support',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'c8c61134935cfdadc2860ee4224523ae8ca156a5',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'benbodhi/svg-support' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'c8c61134935cfdadc2860ee4224523ae8ca156a5',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cure53/dompurify' => array(
            'pretty_version' => '2.5.8',
            'version' => '2.5.8.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../cure53/dompurify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'enshrined/svg-sanitize' => array(
            'pretty_version' => '0.21.0',
            'version' => '0.21.0.0',
            'reference' => '5e477468fac5c5ce933dce53af3e8e4e58dcccc9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../enshrined/svg-sanitize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
