"use strict";function CheckBoxCustom(e){const[t,n]=useState(e.meta.inline_featured_image),{meta:o,updateInlineFeaturedSvg:a}=e;return createElement(wp.components.CheckboxControl,{label:"Render this SVG inline (Advanced)",checked:t,onChange:e=>{n(e),a(e,o)},__nextHasNoMarginBottom:!0})}function wrapPostFeaturedImage(e){return function(t){return createElement(Fragment,{},"",createElement(e,t),createElement(composedCheckBox))}}const{createElement:createElement,Fragment:Fragment,useState:useState}=wp.element,{withSelect:withSelect,withDispatch:withDispatch}=wp.data,{compose:compose}=wp.compose,composedCheckBox=compose([withSelect((e=>{const t=undefined,n=undefined;return{meta:{...e("core/editor").getCurrentPostAttribute("meta"),...e("core/editor").getEditedPostAttribute("meta")}}})),withDispatch((e=>({updateInlineFeaturedSvg(t,n){n={...n,inline_featured_image:t},e("core/editor").editPost({meta:n})}})))])(CheckBoxCustom);wp.hooks.addFilter("editor.PostFeaturedImage","bodhi-svgs-featured-image/render-inline-image-checkbox",wrapPostFeaturedImage);