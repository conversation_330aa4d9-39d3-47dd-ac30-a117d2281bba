jQuery(document).ready((function($){function t(t){const s=t.hasClass(i),r=t.parent().hasClass(i),n=t.closest("."+i).length>0;if(("true"===ForceInlineSVGActive||s||n)&&(!svgSettings.skipNested||s||r||!n)){var a=t.attr("id"),o=t.attr("class"),c=t.attr("src");c.endsWith("svg")&&$.get(c,(function(i){var s=$(i).find("svg"),r=s.attr("id");void 0===a?void 0===r?(a="svg-replaced-"+e,s=s.attr("id",a)):a=r:s=s.attr("id",a),void 0!==o&&(s=s.attr("class",o+" replaced-svg svg-replaced-"+e)),s=s.removeAttr("xmlns:a"),"on"===frontSanitizationEnabled&&""!=s[0].outerHTML&&(s=DOMPurify.sanitize(s[0].outerHTML)),t.replaceWith(s),e++,$(document).trigger("svg.loaded",[a])}),"xml").fail((function(){}))}}let e=0,i;(bodhisvgsInlineSupport=function(){"true"===ForceInlineSVGActive&&jQuery("img").each((function(){void 0!==jQuery(this).attr("src")&&!1!==jQuery(this).attr("src")&&jQuery(this).attr("src").match(/\.(svg)/)&&(jQuery(this).hasClass(cssTarget.ForceInlineSVG)||jQuery(this).addClass(cssTarget.ForceInlineSVG))})),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var i=this.toString();("number"!=typeof e||!isFinite(e)||Math.floor(e)!==e||e>i.length)&&(e=i.length),e-=t.length;var s=i.lastIndexOf(t,e);return-1!==s&&s===e}),String.prototype.endsWith=function(t){var e=this.length-t.length;return e>=0&&this.lastIndexOf(t)===e},i="true"===ForceInlineSVGActive?"img."!==cssTarget.Bodhi?cssTarget.ForceInlineSVG:"style-svg":"img."!==cssTarget.Bodhi?cssTarget.Bodhi:"style-svg","string"==typeof i&&(i=i.replace("img.",""),$("."+i).each((function(e){void 0!==$(this).attr("src")&&!1!==$(this).attr("src")?t($(this)):$(this).find("img").each((function(e){void 0!==$(this).attr("src")&&!1!==$(this).attr("src")&&t($(this))}))})))})()}));