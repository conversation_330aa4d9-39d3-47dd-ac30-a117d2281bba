document.addEventListener("DOMContentLoaded",(function(e){function t(e){if("IMG"!==e.nodeName)return;const t=e.classList.contains(i),n=e.parentElement.classList.contains(i),r=null!==e.closest("."+i);if(("true"===ForceInlineSVGActive||t||r)&&(!svgSettings.skipNested||t||n||!r)){var o=e.id,a=e.className,c=e.src;if(c.endsWith("svg")){var l=new XMLHttpRequest;l.onreadystatechange=function(){if(4===l.readyState&&200===l.status){var t=l.responseText;let c;const d=undefined;var n=(new DOMParser).parseFromString(t,"text/html").getElementsByTagName("svg")[0],i=n.id;if(void 0===o||""===o?void 0===i||""===i?(o="svg-replaced-"+s,n.setAttribute("id",o)):o=i:n.setAttribute("id",o),void 0!==a&&""!==a&&n.setAttribute("class",a+" replaced-svg svg-replaced-"+s),n.removeAttribute("xmlns:a"),"on"===frontSanitizationEnabled&&""!==n.outerHTML){var r=DOMPurify.sanitize(n.outerHTML);e.outerHTML=r}else e.replaceWith(n);s++}else 4===l.readyState&&l.status},l.open("GET",c,!0),l.send(null)}}}function n(e){if(e.childNodes.length>0)for(var s=0;s<e.childNodes.length;s++){var i;if("IMG"===e.childNodes[s].nodeName)t(e.childNodes[s]);else n(e.childNodes[s])}}let s=0,i;(bodhisvgsInlineSupport=function(){if("true"===ForceInlineSVGActive)for(var e=document.getElementsByTagName("img"),s=0;s<e.length;s++)void 0!==e[s].src&&e[s].src.match(/\.(svg)/)&&(e[s].classList.contains(cssTarget.ForceInlineSVG)||e[s].classList.add(cssTarget.ForceInlineSVG));String.prototype.endsWith||(String.prototype.endsWith=function(e,t){var n=this.toString();("number"!=typeof t||!isFinite(t)||Math.floor(t)!==t||t>n.length)&&(t=n.length),t-=e.length;var s=n.lastIndexOf(e,t);return-1!==s&&s===t}),String.prototype.endsWith=function(e){var t=this.length-e.length;return t>=0&&this.lastIndexOf(e)===t},i="true"===ForceInlineSVGActive?"img."!==cssTarget.Bodhi?cssTarget.ForceInlineSVG:"style-svg":"img."!==cssTarget.Bodhi?cssTarget.Bodhi:"style-svg","string"==typeof i&&(i=i.replace("img.",""),document.querySelectorAll("."+i).forEach((function(e){"IMG"===e.nodeName?t(e):n(e)})))})()}));