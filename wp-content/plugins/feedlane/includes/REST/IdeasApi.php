<?php

namespace WPDeveloper\Feedlane\REST;

use WP_REST_Server;
use WP_REST_Request;
use WP_REST_Response;
use WP_Error;
use WPDeveloper\Feedlane\Utils\Helper;
use WPDeveloper\Feedlane\Database\DBSchema;

/**
 * Ideas REST API Class
 */
class IdeasApi {

    /**
     * Constructor
     */
    public function __construct() {
        // Register routes immediately instead of hooking into rest_api_init again
        $this->register_routes();
    }

    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Submit idea
        register_rest_route( 'feedlane/v1', '/ideas', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [ $this, 'submit_idea' ],
                'permission_callback' => [ $this, 'submit_idea_permissions' ],
                'args' => $this->get_submit_idea_args(),
            ],
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [ $this, 'get_ideas' ],
                'permission_callback' => '__return_true',
                'args' => $this->get_ideas_args(),
            ],
        ] );

        // Vote on idea
        register_rest_route( 'feedlane/v1', '/ideas/(?P<id>\d+)/vote', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [ $this, 'vote_idea' ],
                'permission_callback' => [ $this, 'vote_permissions' ],
                'args' => [
                    'id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                ],
            ],
        ] );

        // Get single idea
        register_rest_route( 'feedlane/v1', '/ideas/(?P<id>\d+)', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [ $this, 'get_idea' ],
                'permission_callback' => '__return_true',
                'args' => [
                    'id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                ],
            ],
        ] );
    }

    /**
     * Submit new idea
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function submit_idea( WP_REST_Request $request ) {
        $title = $request->get_param( 'title' );
        $details = $request->get_param( 'details' );
        $category = $request->get_param( 'category' );
        $email = $request->get_param( 'email' );
        $first_name = $request->get_param( 'first_name' );
        $last_name = $request->get_param( 'last_name' );
        $image = $request->get_file_params()['image'] ?? null;

        // For guest users, require email and at least first name
        if ( ! is_user_logged_in() ) {
            if ( empty( $email ) ) {
                return new WP_Error( 'missing_email', __( 'Email is required for guest submissions.', 'feedlane' ), [ 'status' => 400 ] );
            }
            if ( empty( $first_name ) ) {
                return new WP_Error( 'missing_name', __( 'First name is required for guest submissions.', 'feedlane' ), [ 'status' => 400 ] );
            }
        }

        // Create the post
        $post_data = [
            'post_title' => $title,
            'post_content' => $details,
            'post_type' => 'feedlane_ideas',
            'post_status' => 'pending', // Requires admin approval
            'meta_input' => [
                'submitter_email' => $email,
                'submitter_first_name' => $first_name,
                'submitter_last_name' => $last_name,
                'submitter_ip' => Helper::get_user_ip(),
                'submission_date' => current_time( 'mysql' ),
            ],
        ];

        // Add author if user is logged in
        if ( is_user_logged_in() ) {
            $post_data['post_author'] = get_current_user_id();
        }

        $post_id = wp_insert_post( $post_data );

        if ( is_wp_error( $post_id ) ) {
            Helper::log_error( 'Failed to create idea post', [
                'error' => $post_id->get_error_message(),
                'title' => $title,
            ] );

            return new WP_Error( 'post_creation_failed', __( 'Failed to submit idea.', 'feedlane' ), [ 'status' => 500 ] );
        }

        // Set category
        if ( $category ) {
            wp_set_object_terms( $post_id, $category, 'idea_category' );
        }

        // Handle image upload
        if ( $image && ! empty( $image['tmp_name'] ) ) {
            $attachment_id = $this->handle_image_upload( $image, $post_id );
            if ( $attachment_id && ! is_wp_error( $attachment_id ) ) {
                set_post_thumbnail( $post_id, $attachment_id );
            }
        }

        // Track analytics
        $this->track_idea_submission( $post_id );

        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Idea submitted successfully and is pending approval.', 'feedlane' ),
            'idea_id' => $post_id,
        ], 201 );
    }

    /**
     * Get ideas list
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response
     */
    public function get_ideas( WP_REST_Request $request ) {
        $page = $request->get_param( 'page' ) ?: 1;
        $per_page = $request->get_param( 'per_page' ) ?: 10;
        $category = $request->get_param( 'category' );
        $status = $request->get_param( 'status' );
        $orderby = $request->get_param( 'orderby' ) ?: 'votes';

        $args = [
            'post_type' => 'feedlane_ideas',
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page,
        ];

        // Filter by category
        if ( $category ) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'idea_category',
                    'field' => 'slug',
                    'terms' => $category,
                ],
            ];
        }

        // Filter by roadmap status
        if ( $status ) {
            if ( ! isset( $args['tax_query'] ) ) {
                $args['tax_query'] = [];
            }
            $args['tax_query'][] = [
                'taxonomy' => 'roadmap_status',
                'field' => 'slug',
                'terms' => $status,
            ];
        }

        // Order by votes
        if ( $orderby === 'votes' ) {
            $args['meta_key'] = '_vote_count';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
        }

        $query = new \WP_Query( $args );
        $ideas = [];

        foreach ( $query->posts as $post ) {
            $ideas[] = $this->format_idea( $post );
        }

        return new WP_REST_Response( [
            'ideas' => $ideas,
            'pagination' => [
                'total' => $query->found_posts,
                'page' => (int) $page,
                'per_page' => (int) $per_page,
                'total_pages' => $query->max_num_pages,
            ],
        ] );
    }

    /**
     * Get single idea
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function get_idea( WP_REST_Request $request ) {
        $id = $request->get_param( 'id' );

        $post = get_post( $id );
        if ( ! $post || $post->post_type !== 'feedlane_ideas' || $post->post_status !== 'publish' ) {
            return new WP_Error( 'idea_not_found', __( 'Idea not found.', 'feedlane' ), [ 'status' => 404 ] );
        }

        // Track view
        $this->track_idea_view( $id );

        return new WP_REST_Response( [
            'idea' => $this->format_idea( $post, true ),
        ] );
    }

    /**
     * Vote on idea
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function vote_idea( WP_REST_Request $request ) {
        global $wpdb;

        $id = $request->get_param( 'id' );
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();

        // Verify idea exists
        $post = get_post( $id );
        if ( ! $post || $post->post_type !== 'feedlane_ideas' || $post->post_status !== 'publish' ) {
            return new WP_Error( 'idea_not_found', __( 'Idea not found.', 'feedlane' ), [ 'status' => 404 ] );
        }

        // Check if user has already voted
        if ( DBSchema::has_user_voted( $id, $user_id, $user_ip ) ) {
            return new WP_Error( 'already_voted', __( 'You have already voted on this idea.', 'feedlane' ), [ 'status' => 400 ] );
        }

        // Add vote
        $table = $wpdb->prefix . 'feedlane_votes';
        $result = $wpdb->insert(
            $table,
            [
                'post_id' => $id,
                'user_id' => $user_id ?: null,
                'user_ip' => $user_ip,
                'vote_type' => 'up',
                'created_at' => current_time( 'mysql' ),
            ],
            [ '%d', '%d', '%s', '%s', '%s' ]
        );

        if ( $result === false ) {
            Helper::log_error( 'Failed to insert vote', [
                'post_id' => $id,
                'user_id' => $user_id,
                'error' => $wpdb->last_error,
            ] );

            return new WP_Error( 'vote_failed', __( 'Failed to vote.', 'feedlane' ), [ 'status' => 500 ] );
        }

        // Update vote count meta
        $vote_count = DBSchema::get_vote_count( $id );
        update_post_meta( $id, '_vote_count', $vote_count );

        // Track analytics
        $this->track_vote( $id, $user_id, $user_ip );

        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Vote recorded successfully.', 'feedlane' ),
            'vote_count' => $vote_count,
        ] );
    }

    /**
     * Format idea data
     *
     * @param \WP_Post $post
     * @param bool $detailed
     * @return array
     */
    private function format_idea( $post, $detailed = false ) {
        $vote_count = get_post_meta( $post->ID, '_vote_count', true ) ?: 0;
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();
        $has_voted = DBSchema::has_user_voted( $post->ID, $user_id, $user_ip );

        $categories = wp_get_post_terms( $post->ID, 'idea_category' );
        $status = wp_get_post_terms( $post->ID, 'roadmap_status' );

        $data = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'excerpt' => $detailed ? null : Helper::truncate( $post->post_content, 150 ),
            'content' => $detailed ? $post->post_content : null,
            'details' => $post->post_content, // Add details field for frontend
            'vote_count' => (int) $vote_count,
            'has_voted' => $has_voted,
            'category' => ! empty( $categories ) ? $categories[0]->slug : null, // Single category for frontend
            'categories' => array_map( function( $term ) {
                return [
                    'slug' => $term->slug,
                    'name' => $term->name,
                ];
            }, $categories ),
            'status' => ! empty( $status ) ? $status[0]->slug : null, // Single status slug for frontend
            'status_object' => ! empty( $status ) ? [
                'slug' => $status[0]->slug,
                'name' => $status[0]->name,
            ] : null,
            'comment_count' => (int) get_comments_number( $post->ID ),
            'created_at' => $post->post_date,
            'time_ago' => Helper::time_ago( $post->post_date ),
        ];

        if ( has_post_thumbnail( $post->ID ) ) {
            $data['image'] = wp_get_attachment_image_url( get_post_thumbnail_id( $post->ID ), 'medium' );
        }

        return $data;
    }

    /**
     * Handle image upload
     *
     * @param array $file
     * @param int $post_id
     * @return int|WP_Error
     */
    private function handle_image_upload( $file, $post_id ) {
        if ( ! function_exists( 'wp_handle_upload' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/file.php' );
        }

        $upload = wp_handle_upload( $file, [ 'test_form' => false ] );

        if ( isset( $upload['error'] ) ) {
            return new WP_Error( 'upload_error', $upload['error'] );
        }

        $attachment = [
            'post_mime_type' => $upload['type'],
            'post_title' => sanitize_file_name( $file['name'] ),
            'post_content' => '',
            'post_status' => 'inherit',
            'post_parent' => $post_id,
        ];

        $attachment_id = wp_insert_attachment( $attachment, $upload['file'], $post_id );

        if ( ! function_exists( 'wp_generate_attachment_metadata' ) ) {
            require_once( ABSPATH . 'wp-admin/includes/image.php' );
        }

        $attachment_data = wp_generate_attachment_metadata( $attachment_id, $upload['file'] );
        wp_update_attachment_metadata( $attachment_id, $attachment_data );

        return $attachment_id;
    }

    /**
     * Check permissions for submitting ideas
     */
    public function submit_idea_permissions( WP_REST_Request $request ) {
        $nonce = $request->get_header( 'X-WP-Nonce' );
        if ( ! Helper::verify_nonce( $nonce ) ) {
            return new WP_Error( 'invalid_nonce', __( 'Invalid nonce.', 'feedlane' ), [ 'status' => 403 ] );
        }

        return Helper::can_submit_feedback();
    }

    /**
     * Check permissions for voting
     */
    public function vote_permissions( WP_REST_Request $request ) {
        $nonce = $request->get_header( 'X-WP-Nonce' );
        if ( ! Helper::verify_nonce( $nonce ) ) {
            return new WP_Error( 'invalid_nonce', __( 'Invalid nonce.', 'feedlane' ), [ 'status' => 403 ] );
        }

        return Helper::can_vote();
    }

    /**
     * Get submit idea arguments
     */
    private function get_submit_idea_args() {
        return [
            'title' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'details' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_textarea' ],
            ],
            'category' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'email' => [
                'required' => false, // Only required for guest users
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_email' ],
                'validate_callback' => function( $value ) {
                    // Only validate email format if provided
                    return empty( $value ) || is_email( $value );
                },
            ],
            'first_name' => [
                'required' => false, // Only required for guest users
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'last_name' => [
                'required' => false, // Only required for guest users
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
        ];
    }

    /**
     * Get ideas arguments
     */
    private function get_ideas_args() {
        return [
            'page' => [
                'type' => 'integer',
                'default' => 1,
                'sanitize_callback' => 'absint',
            ],
            'per_page' => [
                'type' => 'integer',
                'default' => 10,
                'sanitize_callback' => 'absint',
            ],
            'category' => [
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'status' => [
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'orderby' => [
                'type' => 'string',
                'default' => 'votes',
                'enum' => [ 'votes', 'date', 'title' ],
            ],
        ];
    }

    /**
     * Track analytics events
     */
    private function track_idea_submission( $post_id ) {
        global $wpdb;
        $table = $wpdb->prefix . 'feedlane_analytics';
        $wpdb->insert( $table, [
            'post_id' => $post_id,
            'event_type' => 'idea_submitted',
            'user_id' => get_current_user_id() ?: null,
            'user_ip' => Helper::get_user_ip(),
            'created_at' => current_time( 'mysql' ),
        ], [ '%d', '%s', '%d', '%s', '%s' ] );
    }

    private function track_idea_view( $post_id ) {
        global $wpdb;
        $table = $wpdb->prefix . 'feedlane_analytics';
        $wpdb->insert( $table, [
            'post_id' => $post_id,
            'event_type' => 'idea_viewed',
            'user_id' => get_current_user_id() ?: null,
            'user_ip' => Helper::get_user_ip(),
            'created_at' => current_time( 'mysql' ),
        ], [ '%d', '%s', '%d', '%s', '%s' ] );
    }

    private function track_vote( $post_id, $user_id, $user_ip ) {
        global $wpdb;
        $table = $wpdb->prefix . 'feedlane_analytics';
        $wpdb->insert( $table, [
            'post_id' => $post_id,
            'event_type' => 'idea_voted',
            'user_id' => $user_id ?: null,
            'user_ip' => $user_ip,
            'created_at' => current_time( 'mysql' ),
        ], [ '%d', '%s', '%d', '%s', '%s' ] );
    }
}
