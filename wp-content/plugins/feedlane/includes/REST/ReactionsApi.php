<?php

namespace WPDeveloper\Feedlane\REST;

use WP_REST_Server;
use WP_REST_Request;
use WP_REST_Response;
use WP_Error;
use WPDeveloper\Feedlane\Utils\Helper;
use WPDeveloper\Feedlane\Database\DBSchema;

/**
 * Reactions REST API Class
 */
class ReactionsApi {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'register_routes' ] );
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Add reaction
        register_rest_route( 'feedlane/v1', '/reactions', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [ $this, 'add_reaction' ],
                'permission_callback' => [ $this, 'reaction_permissions' ],
                'args' => $this->get_reaction_args(),
            ],
        ] );
        
        // Get reactions for a post
        register_rest_route( 'feedlane/v1', '/reactions/(?P<post_id>\d+)', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [ $this, 'get_reactions' ],
                'permission_callback' => '__return_true',
                'args' => [
                    'post_id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                ],
            ],
        ] );
        
        // Remove reaction
        register_rest_route( 'feedlane/v1', '/reactions/(?P<post_id>\d+)/(?P<reaction_type>[a-zA-Z]+)', [
            [
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => [ $this, 'remove_reaction' ],
                'permission_callback' => [ $this, 'reaction_permissions' ],
                'args' => [
                    'post_id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                    'reaction_type' => [
                        'required' => true,
                        'type' => 'string',
                        'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
                    ],
                ],
            ],
        ] );
    }
    
    /**
     * Add reaction to a post
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function add_reaction( WP_REST_Request $request ) {
        global $wpdb;
        
        $post_id = $request->get_param( 'post_id' );
        $reaction_type = $request->get_param( 'reaction_type' );
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();
        
        // Verify post exists
        $post = get_post( $post_id );
        if ( ! $post || ! in_array( $post->post_type, [ 'feedlane_posts', 'feedlane_ideas' ] ) ) {
            return new WP_Error( 'invalid_post', __( 'Invalid post ID.', 'feedlane' ), [ 'status' => 404 ] );
        }
        
        // Verify reaction type is valid
        $allowed_reactions = Helper::get_reaction_types();
        if ( ! array_key_exists( $reaction_type, $allowed_reactions ) ) {
            return new WP_Error( 'invalid_reaction', __( 'Invalid reaction type.', 'feedlane' ), [ 'status' => 400 ] );
        }
        
        // Check if user has already reacted with this type
        if ( DBSchema::has_user_reacted( $post_id, $reaction_type, $user_id, $user_ip ) ) {
            return new WP_Error( 'already_reacted', __( 'You have already reacted with this type.', 'feedlane' ), [ 'status' => 400 ] );
        }
        
        // Remove any existing reactions from this user for this post
        $this->remove_user_reactions( $post_id, $user_id, $user_ip );
        
        // Add new reaction
        $table = $wpdb->prefix . 'feedlane_reactions';
        $result = $wpdb->insert(
            $table,
            [
                'post_id' => $post_id,
                'user_id' => $user_id ?: null,
                'user_ip' => $user_ip,
                'reaction_type' => $reaction_type,
                'created_at' => current_time( 'mysql' ),
            ],
            [ '%d', '%d', '%s', '%s', '%s' ]
        );
        
        if ( $result === false ) {
            return new WP_Error( 'reaction_failed', __( 'Failed to add reaction.', 'feedlane' ), [ 'status' => 500 ] );
        }
        
        // Get updated reaction counts
        $reaction_counts = DBSchema::get_reaction_counts( $post_id );
        
        // Track analytics
        $this->track_reaction( $post_id, $reaction_type, $user_id, $user_ip );
        
        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Reaction added successfully.', 'feedlane' ),
            'reaction_counts' => $reaction_counts,
            'user_reaction' => $reaction_type,
        ] );
    }
    
    /**
     * Get reactions for a post
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function get_reactions( WP_REST_Request $request ) {
        $post_id = $request->get_param( 'post_id' );
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();
        
        // Verify post exists
        $post = get_post( $post_id );
        if ( ! $post || ! in_array( $post->post_type, [ 'feedlane_posts', 'feedlane_ideas' ] ) ) {
            return new WP_Error( 'invalid_post', __( 'Invalid post ID.', 'feedlane' ), [ 'status' => 404 ] );
        }
        
        // Get reaction counts
        $reaction_counts = DBSchema::get_reaction_counts( $post_id );
        
        // Get user's current reaction
        $user_reaction = $this->get_user_reaction( $post_id, $user_id, $user_ip );
        
        // Get available reaction types
        $reaction_types = Helper::get_reaction_types();
        
        return new WP_REST_Response( [
            'reaction_counts' => $reaction_counts,
            'user_reaction' => $user_reaction,
            'available_reactions' => $reaction_types,
        ] );
    }
    
    /**
     * Remove reaction from a post
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function remove_reaction( WP_REST_Request $request ) {
        global $wpdb;
        
        $post_id = $request->get_param( 'post_id' );
        $reaction_type = $request->get_param( 'reaction_type' );
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();
        
        // Verify post exists
        $post = get_post( $post_id );
        if ( ! $post || ! in_array( $post->post_type, [ 'feedlane_posts', 'feedlane_ideas' ] ) ) {
            return new WP_Error( 'invalid_post', __( 'Invalid post ID.', 'feedlane' ), [ 'status' => 404 ] );
        }
        
        // Remove reaction
        $table = $wpdb->prefix . 'feedlane_reactions';
        
        if ( $user_id ) {
            $result = $wpdb->delete(
                $table,
                [
                    'post_id' => $post_id,
                    'reaction_type' => $reaction_type,
                    'user_id' => $user_id,
                ],
                [ '%d', '%s', '%d' ]
            );
        } else {
            $result = $wpdb->delete(
                $table,
                [
                    'post_id' => $post_id,
                    'reaction_type' => $reaction_type,
                    'user_ip' => $user_ip,
                ],
                [ '%d', '%s', '%s' ]
            );
        }
        
        if ( $result === false ) {            
            return new WP_Error( 'remove_reaction_failed', __( 'Failed to remove reaction.', 'feedlane' ), [ 'status' => 500 ] );
        }
        
        // Get updated reaction counts
        $reaction_counts = DBSchema::get_reaction_counts( $post_id );
        
        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Reaction removed successfully.', 'feedlane' ),
            'reaction_counts' => $reaction_counts,
            'user_reaction' => null,
        ] );
    }
    
    /**
     * Check permissions for reactions
     *
     * @param WP_REST_Request $request
     * @return bool|WP_Error
     */
    public function reaction_permissions( WP_REST_Request $request ) {
        // Verify nonce
        $nonce = $request->get_header( 'X-WP-Nonce' );
        if ( ! Helper::verify_nonce( $nonce ) ) {
            return new WP_Error( 'invalid_nonce', __( 'Invalid nonce.', 'feedlane' ), [ 'status' => 403 ] );
        }
        
        // Check if user can react
        if ( ! Helper::can_vote() ) {
            return new WP_Error( 'insufficient_permissions', __( 'You do not have permission to react.', 'feedlane' ), [ 'status' => 403 ] );
        }
        
        return true;
    }
    
    /**
     * Get reaction arguments
     *
     * @return array
     */
    private function get_reaction_args() {
        return [
            'post_id' => [
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'reaction_type' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
                'validate_callback' => function( $param ) {
                    $allowed_reactions = Helper::get_reaction_types();
                    return array_key_exists( $param, $allowed_reactions );
                },
            ],
        ];
    }
    
    /**
     * Remove all reactions from a user for a specific post
     *
     * @param int $post_id
     * @param int|null $user_id
     * @param string $user_ip
     */
    private function remove_user_reactions( $post_id, $user_id, $user_ip ) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'feedlane_reactions';
        
        if ( $user_id ) {
            $wpdb->delete(
                $table,
                [
                    'post_id' => $post_id,
                    'user_id' => $user_id,
                ],
                [ '%d', '%d' ]
            );
        } else {
            $wpdb->delete(
                $table,
                [
                    'post_id' => $post_id,
                    'user_ip' => $user_ip,
                ],
                [ '%d', '%s' ]
            );
        }
    }
    
    /**
     * Get user's current reaction for a post
     *
     * @param int $post_id
     * @param int|null $user_id
     * @param string $user_ip
     * @return string|null
     */
    private function get_user_reaction( $post_id, $user_id, $user_ip ) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'feedlane_reactions';
        
        if ( $user_id ) {
            $reaction = $wpdb->get_var( $wpdb->prepare(
                "SELECT reaction_type FROM $table WHERE post_id = %d AND user_id = %d",
                $post_id, $user_id
            ) );
        } else {
            $reaction = $wpdb->get_var( $wpdb->prepare(
                "SELECT reaction_type FROM $table WHERE post_id = %d AND user_ip = %s",
                $post_id, $user_ip
            ) );
        }
        
        return $reaction;
    }
    
    /**
     * Track reaction for analytics
     *
     * @param int $post_id
     * @param string $reaction_type
     * @param int|null $user_id
     * @param string $user_ip
     */
    private function track_reaction( $post_id, $reaction_type, $user_id, $user_ip ) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'feedlane_analytics';
        $wpdb->insert(
            $table,
            [
                'post_id' => $post_id,
                'event_type' => 'reaction_added',
                'user_id' => $user_id,
                'user_ip' => $user_ip,
                'metadata' => wp_json_encode( [ 'reaction_type' => $reaction_type ] ),
                'created_at' => current_time( 'mysql' ),
            ],
            [ '%d', '%s', '%d', '%s', '%s', '%s' ]
        );
    }
}
