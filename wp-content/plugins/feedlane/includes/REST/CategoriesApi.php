<?php

namespace WPDeveloper\Feedlane\REST;

use WP_REST_Server;
use WP_REST_Request;
use WP_REST_Response;
use WP_Error;
use WPDeveloper\Feedlane\Utils\Helper;

/**
 * Categories API Class
 */
class CategoriesApi {

    /**
     * Constructor
     */
    public function __construct() {
        // Register routes immediately instead of hooking into rest_api_init again
        $this->register_routes();
    }

    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Get categories
        register_rest_route( 'feedlane/v1', '/categories', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [ $this, 'get_categories' ],
                'permission_callback' => '__return_true',
                'args' => $this->get_categories_args(),
            ],
        ] );

        // Create category
        register_rest_route( 'feedlane/v1', '/categories', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [ $this, 'create_category' ],
                'permission_callback' => [ $this, 'manage_categories_permissions' ],
                'args' => $this->get_create_category_args(),
            ],
        ] );

        // Update category
        register_rest_route( 'feedlane/v1', '/categories/(?P<id>\d+)', [
            [
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => [ $this, 'update_category' ],
                'permission_callback' => [ $this, 'manage_categories_permissions' ],
                'args' => $this->get_update_category_args(),
            ],
        ] );

        // Delete category
        register_rest_route( 'feedlane/v1', '/categories/(?P<id>\d+)', [
            [
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => [ $this, 'delete_category' ],
                'permission_callback' => [ $this, 'manage_categories_permissions' ],
                'args' => [
                    'id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                ],
            ],
        ] );
    }

    /**
     * Get categories
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response
     */
    public function get_categories( WP_REST_Request $request ) {
        $per_page = $request->get_param( 'per_page' ) ?: 100;
        $page = $request->get_param( 'page' ) ?: 1;

        $terms = get_terms( [
            'taxonomy' => 'idea_category',
            'hide_empty' => false,
            'number' => $per_page,
            'offset' => ( $page - 1 ) * $per_page,
        ] );

        if ( is_wp_error( $terms ) ) {
            return new WP_Error( 'categories_fetch_failed', __( 'Failed to fetch categories.', 'feedlane' ), [ 'status' => 500 ] );
        }

        $categories = array_map( [ $this, 'format_category' ], $terms );

        return new WP_REST_Response( [
            'categories' => $categories,
            'pagination' => [
                'page' => $page,
                'per_page' => $per_page,
                'total' => wp_count_terms( 'idea_category' ),
            ],
        ] );
    }

    /**
     * Create category
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function create_category( WP_REST_Request $request ) {
        $name = sanitize_text_field( $request->get_param( 'name' ) );
        $description = sanitize_textarea_field( $request->get_param( 'description' ) );
        $color = sanitize_hex_color( $request->get_param( 'color' ) ) ?: '#10B981';

        if ( empty( $name ) ) {
            return new WP_Error( 'missing_name', __( 'Category name is required.', 'feedlane' ), [ 'status' => 400 ] );
        }

        // Check if category already exists
        if ( term_exists( $name, 'idea_category' ) ) {
            return new WP_Error( 'category_exists', __( 'A category with this name already exists.', 'feedlane' ), [ 'status' => 400 ] );
        }

        $result = wp_insert_term( $name, 'idea_category', [
            'description' => $description,
        ] );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( 'category_creation_failed', __( 'Failed to create category.', 'feedlane' ), [ 'status' => 500 ] );
        }

        $term_id = $result['term_id'];

        // Save color as term meta
        update_term_meta( $term_id, 'color', $color );

        $term = get_term( $term_id, 'idea_category' );

        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Category created successfully.', 'feedlane' ),
            'category' => $this->format_category( $term ),
        ], 201 );
    }

    /**
     * Update category
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function update_category( WP_REST_Request $request ) {
        $term_id = $request->get_param( 'id' );
        $name = sanitize_text_field( $request->get_param( 'name' ) );
        $description = sanitize_textarea_field( $request->get_param( 'description' ) );
        $color = sanitize_hex_color( $request->get_param( 'color' ) ) ?: '#10B981';

        if ( empty( $name ) ) {
            return new WP_Error( 'missing_name', __( 'Category name is required.', 'feedlane' ), [ 'status' => 400 ] );
        }

        // Check if term exists
        $term = get_term( $term_id, 'idea_category' );
        if ( is_wp_error( $term ) || ! $term ) {
            return new WP_Error( 'category_not_found', __( 'Category not found.', 'feedlane' ), [ 'status' => 404 ] );
        }

        // Check if another category with the same name exists (excluding current)
        $existing = get_term_by( 'name', $name, 'idea_category' );
        if ( $existing && $existing->term_id !== $term_id ) {
            return new WP_Error( 'category_exists', __( 'A category with this name already exists.', 'feedlane' ), [ 'status' => 400 ] );
        }

        $result = wp_update_term( $term_id, 'idea_category', [
            'name' => $name,
            'description' => $description,
        ] );

        if ( is_wp_error( $result ) ) {
            return new WP_Error( 'category_update_failed', __( 'Failed to update category.', 'feedlane' ), [ 'status' => 500 ] );
        }

        // Update color meta
        update_term_meta( $term_id, 'color', $color );

        $updated_term = get_term( $term_id, 'idea_category' );

        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Category updated successfully.', 'feedlane' ),
            'category' => $this->format_category( $updated_term ),
        ] );
    }

    /**
     * Delete category
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function delete_category( WP_REST_Request $request ) {
        $term_id = $request->get_param( 'id' );

        // Check if term exists
        $term = get_term( $term_id, 'idea_category' );
        if ( is_wp_error( $term ) || ! $term ) {
            return new WP_Error( 'category_not_found', __( 'Category not found.', 'feedlane' ), [ 'status' => 404 ] );
        }

        // Check if category has ideas assigned
        $ideas_count = get_posts( [
            'post_type' => 'feedlane_ideas',
            'post_status' => 'any',
            'tax_query' => [
                [
                    'taxonomy' => 'idea_category',
                    'field' => 'term_id',
                    'terms' => $term_id,
                ],
            ],
            'fields' => 'ids',
            'posts_per_page' => 1,
        ] );

        if ( ! empty( $ideas_count ) ) {
            return new WP_Error( 'category_has_ideas', __( 'Cannot delete category that has ideas assigned to it.', 'feedlane' ), [ 'status' => 400 ] );
        }

        $result = wp_delete_term( $term_id, 'idea_category' );

        if ( is_wp_error( $result ) || ! $result ) {
            return new WP_Error( 'category_deletion_failed', __( 'Failed to delete category.', 'feedlane' ), [ 'status' => 500 ] );
        }

        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Category deleted successfully.', 'feedlane' ),
        ] );
    }

    /**
     * Format category data
     *
     * @param \WP_Term $term
     * @return array
     */
    private function format_category( $term ) {
        $color = get_term_meta( $term->term_id, 'color', true ) ?: '#10B981';

        return [
            'id' => $term->term_id,
            'name' => $term->name,
            'slug' => $term->slug,
            'description' => $term->description,
            'count' => $term->count,
            'color' => $color,
            'meta' => [
                'color' => $color,
            ],
        ];
    }

    /**
     * Get categories args
     */
    private function get_categories_args() {
        return [
            'page' => [
                'type' => 'integer',
                'default' => 1,
                'minimum' => 1,
                'sanitize_callback' => 'absint',
            ],
            'per_page' => [
                'type' => 'integer',
                'default' => 100,
                'minimum' => 1,
                'maximum' => 100,
                'sanitize_callback' => 'absint',
            ],
        ];
    }

    /**
     * Get create category args
     */
    private function get_create_category_args() {
        return [
            'name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'color' => [
                'type' => 'string',
                'default' => '#10B981',
                'sanitize_callback' => 'sanitize_hex_color',
            ],
        ];
    }

    /**
     * Get update category args
     */
    private function get_update_category_args() {
        return [
            'id' => [
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'name' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ],
            'description' => [
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ],
            'color' => [
                'type' => 'string',
                'default' => '#10B981',
                'sanitize_callback' => 'sanitize_hex_color',
            ],
        ];
    }

    /**
     * Check permissions for managing categories
     */
    public function manage_categories_permissions( WP_REST_Request $request ) {
        $nonce = $request->get_header( 'X-WP-Nonce' );
        if ( ! Helper::verify_nonce( $nonce ) ) {
            return new WP_Error( 'invalid_nonce', __( 'Invalid nonce.', 'feedlane' ), [ 'status' => 403 ] );
        }

        return current_user_can( 'manage_options' );
    }
}
