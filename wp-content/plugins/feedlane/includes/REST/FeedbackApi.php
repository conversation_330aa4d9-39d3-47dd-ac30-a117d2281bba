<?php

namespace WPDeveloper\Feedlane\REST;

use WP_REST_Server;
use WP_REST_Request;
use WP_REST_Response;
use WP_Error;
use WPDeveloper\Feedlane\Utils\Helper;

/**
 * Feedback REST API Class
 */
class FeedbackApi {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'rest_api_init', [ $this, 'register_routes' ] );
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        register_rest_route( 'feedlane/v1', '/feedback', [
            [
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => [ $this, 'submit_feedback' ],
                'permission_callback' => [ $this, 'submit_feedback_permissions' ],
                'args' => $this->get_submit_feedback_args(),
            ],
        ] );
        
        register_rest_route( 'feedlane/v1', '/feedback/(?P<post_id>\d+)', [
            [
                'methods' => WP_REST_Server::READABLE,
                'callback' => [ $this, 'get_feedback' ],
                'permission_callback' => '__return_true',
                'args' => [
                    'post_id' => [
                        'required' => true,
                        'type' => 'integer',
                        'sanitize_callback' => 'absint',
                    ],
                ],
            ],
        ] );
    }
    
    /**
     * Submit feedback
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function submit_feedback( WP_REST_Request $request ) {
        global $wpdb;
        
        $post_id = $request->get_param( 'post_id' );
        $feedback_text = $request->get_param( 'feedback_text' );
        $user_name = $request->get_param( 'user_name' );
        $user_email = $request->get_param( 'user_email' );
        
        // Verify post exists
        $post = get_post( $post_id );
        if ( ! $post || ! in_array( $post->post_type, [ 'feedlane_posts', 'feedlane_ideas' ] ) ) {
            return new WP_Error( 'invalid_post', __( 'Invalid post ID.', 'feedlane' ), [ 'status' => 404 ] );
        }
        
        // Get user info
        $user_id = get_current_user_id();
        $user_ip = Helper::get_user_ip();
        
        // If user is not logged in, require name and email
        if ( ! $user_id && ( empty( $user_name ) || empty( $user_email ) ) ) {
            return new WP_Error( 'missing_user_info', __( 'Name and email are required for guest submissions.', 'feedlane' ), [ 'status' => 400 ] );
        }
        
        // Insert feedback
        $table = $wpdb->prefix . 'feedlane_feedback';
        $result = $wpdb->insert(
            $table,
            [
                'post_id' => $post_id,
                'user_id' => $user_id ?: null,
                'user_name' => $user_name,
                'user_email' => $user_email,
                'user_ip' => $user_ip,
                'feedback_text' => $feedback_text,
                'status' => 'published',
                'created_at' => current_time( 'mysql' ),
            ],
            [
                '%d',
                '%d',
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
                '%s',
            ]
        );
        
        if ( $result === false ) {            
            return new WP_Error( 'database_error', __( 'Failed to submit feedback.', 'feedlane' ), [ 'status' => 500 ] );
        }
        
        // Track analytics
        $this->track_feedback_submission( $post_id, $user_id, $user_ip );
        
        return new WP_REST_Response( [
            'success' => true,
            'message' => __( 'Feedback submitted successfully.', 'feedlane' ),
            'feedback_id' => $wpdb->insert_id,
        ], 201 );
    }
    
    /**
     * Get feedback for a post
     *
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function get_feedback( WP_REST_Request $request ) {
        global $wpdb;
        
        $post_id = $request->get_param( 'post_id' );
        $page = $request->get_param( 'page' ) ?: 1;
        $per_page = $request->get_param( 'per_page' ) ?: 10;
        $offset = ( $page - 1 ) * $per_page;
        
        // Verify post exists
        $post = get_post( $post_id );
        if ( ! $post || ! in_array( $post->post_type, [ 'feedlane_posts', 'feedlane_ideas' ] ) ) {
            return new WP_Error( 'invalid_post', __( 'Invalid post ID.', 'feedlane' ), [ 'status' => 404 ] );
        }
        
        $table = $wpdb->prefix . 'feedlane_feedback';
        
        // Get feedback
        $feedback = $wpdb->get_results( $wpdb->prepare(
            "SELECT f.*, u.display_name as user_display_name 
             FROM $table f 
             LEFT JOIN {$wpdb->users} u ON f.user_id = u.ID 
             WHERE f.post_id = %d AND f.status = 'published' 
             ORDER BY f.created_at DESC 
             LIMIT %d OFFSET %d",
            $post_id, $per_page, $offset
        ) );
        
        // Get total count
        $total = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE post_id = %d AND status = 'published'",
            $post_id
        ) );
        
        // Format feedback
        $formatted_feedback = [];
        foreach ( $feedback as $item ) {
            $formatted_feedback[] = [
                'id' => (int) $item->id,
                'feedback_text' => $item->feedback_text,
                'author' => [
                    'name' => $item->user_display_name ?: $item->user_name,
                    'email' => $item->user_email,
                    'is_registered' => ! empty( $item->user_id ),
                ],
                'created_at' => $item->created_at,
                'time_ago' => Helper::time_ago( $item->created_at ),
            ];
        }
        
        return new WP_REST_Response( [
            'feedback' => $formatted_feedback,
            'pagination' => [
                'total' => (int) $total,
                'page' => (int) $page,
                'per_page' => (int) $per_page,
                'total_pages' => ceil( $total / $per_page ),
            ],
        ] );
    }
    
    /**
     * Check permissions for submitting feedback
     *
     * @param WP_REST_Request $request
     * @return bool|WP_Error
     */
    public function submit_feedback_permissions( WP_REST_Request $request ) {
        // Verify nonce
        $nonce = $request->get_header( 'X-WP-Nonce' );
        if ( ! Helper::verify_nonce( $nonce ) ) {
            return new WP_Error( 'invalid_nonce', __( 'Invalid nonce.', 'feedlane' ), [ 'status' => 403 ] );
        }
        
        // Check if user can submit feedback
        if ( ! Helper::can_submit_feedback() ) {
            return new WP_Error( 'insufficient_permissions', __( 'You do not have permission to submit feedback.', 'feedlane' ), [ 'status' => 403 ] );
        }
        
        return true;
    }
    
    /**
     * Get submit feedback arguments
     *
     * @return array
     */
    private function get_submit_feedback_args() {
        return [
            'post_id' => [
                'required' => true,
                'type' => 'integer',
                'sanitize_callback' => 'absint',
            ],
            'feedback_text' => [
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_textarea' ],
                'validate_callback' => function( $param ) {
                    return ! empty( trim( $param ) );
                },
            ],
            'user_name' => [
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_text' ],
            ],
            'user_email' => [
                'required' => false,
                'type' => 'string',
                'sanitize_callback' => [ Helper::class, 'sanitize_email' ],
                'validate_callback' => function( $param ) {
                    return empty( $param ) || is_email( $param );
                },
            ],
        ];
    }
    
    /**
     * Track feedback submission for analytics
     *
     * @param int $post_id
     * @param int|null $user_id
     * @param string $user_ip
     */
    private function track_feedback_submission( $post_id, $user_id, $user_ip ) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'feedlane_analytics';
        $wpdb->insert(
            $table,
            [
                'post_id' => $post_id,
                'event_type' => 'feedback_submitted',
                'user_id' => $user_id,
                'user_ip' => $user_ip,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => current_time( 'mysql' ),
            ],
            [ '%d', '%s', '%d', '%s', '%s', '%s' ]
        );
    }
}
