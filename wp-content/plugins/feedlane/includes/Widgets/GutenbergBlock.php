<?php

namespace WPDeveloper\Feedlane\Widgets;

/**
 * Gutenberg Block Class
 */
class GutenbergBlock {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', [ $this, 'register_block' ] );
        add_action( 'enqueue_block_editor_assets', [ $this, 'enqueue_block_editor_assets' ] );
    }

    /**
     * Register Gutenberg block
     */
    public function register_block() {

        register_block_type( 'feedlane/sidebar', [
            'attributes' => [
                'position' => [
                    'type' => 'string',
                    'default' => 'left',
                ],
                'height' => [
                    'type' => 'string',
                    'default' => '600px',
                ],
                'primaryColor' => [
                    'type' => 'string',
                    'default' => '#0ea5e9',
                ],
            ],
            'render_callback' => [ $this, 'render_block' ],
        ] );

    }

    /**
     * Enqueue block editor assets
     */
    public function enqueue_block_editor_assets() {
        // Check if block script exists
        $block_script = FEEDLANE_ASSETS_DIR_PATH . 'js/feedlane-block-editor.min.js';

        if ( file_exists( $block_script ) ) {
            // Register block script
            wp_enqueue_script(
                'feedlane-block-editor',
                FEEDLANE_ASSETS_DIR_URL . 'js/feedlane-block-editor.min.js',
                [ 'wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n' ],
                FEEDLANE_VERSION,
                true
            );
        } else {
            // Fallback: register inline script
            wp_enqueue_script( 'wp-blocks' );
            wp_enqueue_script( 'wp-element' );
            wp_enqueue_script( 'wp-block-editor' );
            wp_enqueue_script( 'wp-components' );
            wp_enqueue_script( 'wp-i18n' );

            // Register a custom script to hold our block
            wp_register_script(
                'feedlane-block-inline',
                '',
                [ 'wp-blocks', 'wp-element', 'wp-block-editor', 'wp-components', 'wp-i18n' ],
                FEEDLANE_VERSION,
                true
            );
            wp_enqueue_script( 'feedlane-block-inline' );
            wp_add_inline_script( 'feedlane-block-inline', $this->get_inline_block_script() );
        }

        // Register block styles
        wp_enqueue_style(
            'feedlane-block-editor',
            FEEDLANE_ASSETS_DIR_URL . 'css/feedlane-sidebar.min.css',
            [],
            FEEDLANE_VERSION
        );

        // Localize script
        $script_handle = file_exists( FEEDLANE_ASSETS_DIR_PATH . 'js/feedlane-block-editor.min.js' )
            ? 'feedlane-block-editor'
            : 'feedlane-block-inline';

        wp_localize_script( $script_handle, 'feedlaneBlock', [
            'title' => __( 'Feedlane Sidebar', 'feedlane' ),
            'description' => __( 'Add a feedback sidebar with newsfeed, ideas, and roadmap tabs.', 'feedlane' ),
            'category' => 'widgets',
            'icon' => 'feedback',
        ] );
    }

    /**
     * Render block
     */
    public function render_block( $attributes ) {
        // Extract attributes
        $position = $attributes['position'] ?? 'left';
        $height = $attributes['height'] ?? '600px';
        $primary_color = $attributes['primaryColor'] ?? '#0ea5e9';

        // Generate unique ID
        $unique_id = 'feedlane-gutenberg-' . wp_generate_uuid4();

        // Enqueue assets
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js' );
        feedlane()->assets->enqueue( 'feedlane-sidebar', 'css/feedlane-sidebar.min.css' );

        // Localize script data
        feedlane()->assets->localize(
            'feedlane-sidebar',
            'feedlaneGutenberg',
            [
                'instance_id' => $unique_id,
                'position' => $position,
                'height' => $height,
                'primary_color' => $primary_color,
            ]
        );

        ob_start();
        ?>
        <div id="<?php echo esc_attr( $unique_id ); ?>"
             class="feedlane-gutenberg-block"
             data-position="<?php echo esc_attr( $position ); ?>"
             data-height="<?php echo esc_attr( $height ); ?>"
             style="--feedlane-primary-color: <?php echo esc_attr( $primary_color ); ?>">
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Get inline block script
     */
    private function get_inline_block_script() {
        return "
        (function() {
            const { registerBlockType } = wp.blocks;
            const { InspectorControls } = wp.blockEditor;
            const { PanelBody, SelectControl, TextControl } = wp.components;
            const { __ } = wp.i18n;
            const { createElement: el } = wp.element;

            registerBlockType('feedlane/sidebar', {
                title: __('Feedlane Sidebar', 'feedlane'),
                description: __('Add a feedback sidebar with newsfeed, ideas, and roadmap tabs.', 'feedlane'),
                category: 'widgets',
                icon: 'feedback',
                attributes: {
                    position: {
                        type: 'string',
                        default: 'left',
                    },
                    height: {
                        type: 'string',
                        default: '600px',
                    },
                    primaryColor: {
                        type: 'string',
                        default: '#0ea5e9',
                    },
                },

                edit: function(props) {
                    const { attributes, setAttributes } = props;
                    const { position, height, primaryColor } = attributes;

                    return [
                        el(InspectorControls, { key: 'inspector' },
                            el(PanelBody, {
                                title: __('Settings', 'feedlane'),
                                initialOpen: true,
                            },
                                el(SelectControl, {
                                    label: __('Position', 'feedlane'),
                                    value: position,
                                    options: [
                                        { label: __('Left', 'feedlane'), value: 'left' },
                                        { label: __('Right', 'feedlane'), value: 'right' },
                                    ],
                                    onChange: function(value) {
                                        setAttributes({ position: value });
                                    },
                                }),
                                el(TextControl, {
                                    label: __('Height', 'feedlane'),
                                    value: height,
                                    onChange: function(value) {
                                        setAttributes({ height: value });
                                    },
                                    help: __('Set the height (e.g., 600px, 100vh)', 'feedlane'),
                                }),
                                el(TextControl, {
                                    label: __('Primary Color', 'feedlane'),
                                    value: primaryColor,
                                    type: 'color',
                                    onChange: function(value) {
                                        setAttributes({ primaryColor: value });
                                    },
                                })
                            )
                        ),

                        el('div', {
                            key: 'preview',
                            className: 'feedlane-block-preview',
                            style: {
                                border: '2px dashed #ddd',
                                padding: '40px 20px',
                                textAlign: 'center',
                                background: '#f9f9f9',
                                borderRadius: '8px',
                                '--feedlane-primary-color': primaryColor
                            }
                        },
                            el('div', {
                                style: {
                                    fontSize: '48px',
                                    color: '#666',
                                    marginBottom: '16px'
                                }
                            }, '💬'),
                            el('h4', {
                                style: {
                                    margin: '0 0 12px 0',
                                    color: '#333'
                                }
                            }, __('Feedlane Sidebar', 'feedlane')),
                            el('p', {
                                style: {
                                    margin: '4px 0',
                                    color: '#666',
                                    fontSize: '14px'
                                }
                            }, __('Position:', 'feedlane') + ' ' + position),
                            el('p', {
                                style: {
                                    margin: '4px 0',
                                    color: '#666',
                                    fontSize: '14px'
                                }
                            }, __('Height:', 'feedlane') + ' ' + height),
                            el('div', {
                                style: {
                                    width: '20px',
                                    height: '20px',
                                    backgroundColor: primaryColor,
                                    borderRadius: '50%',
                                    margin: '8px auto 0',
                                    border: '2px solid #fff',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                                }
                            })
                        )
                    ];
                },

                save: function() {
                    // Return null since this is a dynamic block
                    return null;
                },
            });
        })();
        ";
    }
}
