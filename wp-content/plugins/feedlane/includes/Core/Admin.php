<?php

namespace WPDeveloper\Feedlane\Core;

/**
 * Admin Class
 */
class Admin {

    /**
     * Constructor
     */
    public function __construct() {


        add_action( 'admin_menu', [ $this, 'add_admin_menu' ] );
        add_action( 'admin_init', [ $this, 'register_settings' ] );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {


        // Main menu page
        add_menu_page(
            __( 'Feedlane', 'feedlane' ),
            __( 'Feedlane', 'feedlane' ),
            'manage_options',
            'feedlane',
            [ $this, 'dashboard_page' ],
            'dashicons-feedback',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'feedlane',
            __( 'Dashboard', 'feedlane' ),
            __( 'Dashboard', 'feedlane' ),
            'manage_options',
            'feedlane',
            [ $this, 'dashboard_page' ]
        );

        // Settings submenu
        add_submenu_page(
            'feedlane',
            __( 'Settings', 'feedlane' ),
            __( 'Settings', 'feedlane' ),
            'manage_options',
            'feedlane-settings',
            [ $this, 'settings_page' ]
        );

        // Analytics submenu
        add_submenu_page(
            'feedlane',
            __( 'Analytics', 'feedlane' ),
            __( 'Analytics', 'feedlane' ),
            'manage_options',
            'feedlane-analytics',
            [ $this, 'analytics_page' ]
        );

        // Ideas Management submenu
        add_submenu_page(
            'feedlane',
            __( 'Ideas Management', 'feedlane' ),
            __( 'Ideas Management', 'feedlane' ),
            'manage_options',
            'feedlane-ideas',
            [ $this, 'ideas_page' ]
        );

        // Categories Management submenu
        add_submenu_page(
            'feedlane',
            __( 'Categories', 'feedlane' ),
            __( 'Categories', 'feedlane' ),
            'manage_options',
            'feedlane-categories',
            [ $this, 'categories_page' ]
        );
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        // General settings
        register_setting( 'feedlane_general', 'feedlane_enable_newsfeed' );
        register_setting( 'feedlane_general', 'feedlane_enable_ideas' );
        register_setting( 'feedlane_general', 'feedlane_enable_roadmap' );
        register_setting( 'feedlane_general', 'feedlane_enable_guest_submissions' );
        register_setting( 'feedlane_general', 'feedlane_sidebar_position' );
        register_setting( 'feedlane_general', 'feedlane_primary_color' );
        register_setting( 'feedlane_general', 'feedlane_enable_floating_sidebar' );

        // Firebase settings
        register_setting( 'feedlane_firebase', 'feedlane_firebase_config' );
        register_setting( 'feedlane_firebase', 'feedlane_firebase_webhook_secret' );

        // Add settings sections
        add_settings_section(
            'feedlane_general_section',
            __( 'General Settings', 'feedlane' ),
            [ $this, 'general_section_callback' ],
            'feedlane_general'
        );

        add_settings_section(
            'feedlane_firebase_section',
            __( 'Firebase Configuration', 'feedlane' ),
            [ $this, 'firebase_section_callback' ],
            'feedlane_firebase'
        );

        // Add settings fields
        $this->add_general_fields();
        $this->add_firebase_fields();
    }

    /**
     * Add general settings fields
     */
    private function add_general_fields() {
        add_settings_field(
            'feedlane_enable_newsfeed',
            __( 'Enable Newsfeed Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_newsfeed',
                'description' => __( 'Enable the newsfeed tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_ideas',
            __( 'Enable Ideas Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_ideas',
                'description' => __( 'Enable the ideas submission tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_roadmap',
            __( 'Enable Roadmap Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_roadmap',
                'description' => __( 'Enable the roadmap tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_guest_submissions',
            __( 'Enable Guest Submissions', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_guest_submissions',
                'description' => __( 'Allow non-logged-in users to submit feedback and ideas.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_sidebar_position',
            __( 'Sidebar Position', 'feedlane' ),
            [ $this, 'select_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_sidebar_position',
                'options' => [
                    'left' => __( 'Left', 'feedlane' ),
                    'right' => __( 'Right', 'feedlane' ),
                ],
                'description' => __( 'Choose which side of the screen the sidebar appears on.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_primary_color',
            __( 'Primary Color', 'feedlane' ),
            [ $this, 'color_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_primary_color',
                'description' => __( 'Choose the primary color for the sidebar and buttons.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_floating_sidebar',
            __( 'Enable Floating Sidebar', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_floating_sidebar',
                'description' => __( 'Show the floating feedback sidebar on all pages of your website.', 'feedlane' ),
            ]
        );
    }

    /**
     * Add Firebase settings fields
     */
    private function add_firebase_fields() {
        add_settings_field(
            'feedlane_firebase_config',
            __( 'Firebase Configuration', 'feedlane' ),
            [ $this, 'textarea_field_callback' ],
            'feedlane_firebase',
            'feedlane_firebase_section',
            [
                'label_for' => 'feedlane_firebase_config',
                'description' => __( 'Paste your Firebase configuration JSON here for real-time comments.', 'feedlane' ),
                'placeholder' => '{"apiKey": "...", "authDomain": "...", "projectId": "..."}',
            ]
        );

        add_settings_field(
            'feedlane_firebase_webhook_secret',
            __( 'Webhook Secret', 'feedlane' ),
            [ $this, 'text_field_callback' ],
            'feedlane_firebase',
            'feedlane_firebase_section',
            [
                'label_for' => 'feedlane_firebase_webhook_secret',
                'description' => __( 'Secret key for Firebase webhook authentication.', 'feedlane' ),
                'type' => 'password',
            ]
        );
    }

    /**
     * Dashboard page
     */
    public function dashboard_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-dashboard"></div>
        </div>
        <?php
    }

    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-settings"></div>
        </div>
        <?php
    }

    /**
     * Analytics page
     */
    public function analytics_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-analytics"></div>
        </div>
        <?php
    }

    /**
     * Ideas management page
     */
    public function ideas_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-ideas"></div>
        </div>
        <?php
    }

    /**
     * Categories management page
     */
    public function categories_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-categories"></div>
        </div>
        <?php
    }

    /**
     * Section callbacks
     */
    public function general_section_callback() {
        echo '<p>' . __( 'Configure general settings for the Feedlane plugin.', 'feedlane' ) . '</p>';
    }

    public function firebase_section_callback() {
        echo '<p>' . __( 'Configure Firebase for real-time comments functionality.', 'feedlane' ) . '</p>';
    }

    /**
     * Field callbacks
     */
    public function checkbox_field_callback( $args ) {
        $option = get_option( $args['label_for'], true );
        ?>
        <input type="checkbox" id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>" value="1"
               <?php checked( $option, 1 ); ?> />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function select_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        ?>
        <select id="<?php echo esc_attr( $args['label_for'] ); ?>"
                name="<?php echo esc_attr( $args['label_for'] ); ?>">
            <?php foreach ( $args['options'] as $value => $label ) : ?>
                <option value="<?php echo esc_attr( $value ); ?>"
                        <?php selected( $option, $value ); ?>>
                    <?php echo esc_html( $label ); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function color_field_callback( $args ) {
        $option = get_option( $args['label_for'], '#0ea5e9' );
        ?>
        <input type="color" id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>"
               value="<?php echo esc_attr( $option ); ?>" />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function text_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        $type = $args['type'] ?? 'text';
        ?>
        <input type="<?php echo esc_attr( $type ); ?>"
               id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>"
               value="<?php echo esc_attr( $option ); ?>"
               class="regular-text" />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function textarea_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        $placeholder = $args['placeholder'] ?? '';
        ?>
        <textarea id="<?php echo esc_attr( $args['label_for'] ); ?>"
                  name="<?php echo esc_attr( $args['label_for'] ); ?>"
                  rows="5" cols="50" class="large-text"
                  placeholder="<?php echo esc_attr( $placeholder ); ?>"><?php echo esc_textarea( $option ); ?></textarea>
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }
}
