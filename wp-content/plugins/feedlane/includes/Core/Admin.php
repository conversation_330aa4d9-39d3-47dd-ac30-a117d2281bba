<?php

namespace WPDeveloper\Feedlane\Core;

/**
 * Admin Class
 */
class Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'admin_menu', [ $this, 'add_admin_menu' ] );
        add_action( 'admin_init', [ $this, 'register_settings' ] );

        // AJAX handlers for settings
        add_action( 'wp_ajax_feedlane_get_settings', [ $this, 'ajax_get_settings' ] );
        add_action( 'wp_ajax_feedlane_save_settings', [ $this, 'ajax_save_settings' ] );

        // AJAX handlers for ideas management
        add_action( 'wp_ajax_feedlane_get_ideas', [ $this, 'ajax_get_ideas' ] );
        add_action( 'wp_ajax_feedlane_update_idea_status', [ $this, 'ajax_update_idea_status' ] );
        add_action( 'wp_ajax_feedlane_delete_idea', [ $this, 'ajax_delete_idea' ] );

        // AJAX handlers for categories management
        add_action( 'wp_ajax_feedlane_get_categories', [ $this, 'ajax_get_categories' ] );
        add_action( 'wp_ajax_feedlane_save_category', [ $this, 'ajax_save_category' ] );
        add_action( 'wp_ajax_feedlane_delete_category', [ $this, 'ajax_delete_category' ] );

        // AJAX handlers for status management
        add_action( 'wp_ajax_feedlane_get_status', [ $this, 'ajax_get_status' ] );
        add_action( 'wp_ajax_feedlane_save_status', [ $this, 'ajax_save_status' ] );
        add_action( 'wp_ajax_feedlane_delete_status', [ $this, 'ajax_delete_status' ] );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {


        // Main menu page
        add_menu_page(
            __( 'Feedlane', 'feedlane' ),
            __( 'Feedlane', 'feedlane' ),
            'manage_options',
            'feedlane',
            [ $this, 'dashboard_page' ],
            'dashicons-feedback',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'feedlane',
            __( 'Dashboard', 'feedlane' ),
            __( 'Dashboard', 'feedlane' ),
            'manage_options',
            'feedlane',
            [ $this, 'dashboard_page' ]
        );

        // Settings submenu
        add_submenu_page(
            'feedlane',
            __( 'Settings', 'feedlane' ),
            __( 'Settings', 'feedlane' ),
            'manage_options',
            'feedlane-settings',
            [ $this, 'settings_page' ]
        );

        // Analytics submenu
        add_submenu_page(
            'feedlane',
            __( 'Analytics', 'feedlane' ),
            __( 'Analytics', 'feedlane' ),
            'manage_options',
            'feedlane-analytics',
            [ $this, 'analytics_page' ]
        );

        // Idea Manager submenu
        add_submenu_page(
            'feedlane',
            __( 'Idea Manager', 'feedlane' ),
            __( 'Idea Manager', 'feedlane' ),
            'manage_options',
            'feedlane-ideas',
            [ $this, 'ideas_page' ]
        );

        // Categories Management submenu
        add_submenu_page(
            'feedlane',
            __( 'Categories', 'feedlane' ),
            __( 'Categories', 'feedlane' ),
            'manage_options',
            'feedlane-categories',
            [ $this, 'categories_page' ]
        );

        // Roadmap Manager submenu
        add_submenu_page(
            'feedlane',
            __( 'Roadmap Manager', 'feedlane' ),
            __( 'Roadmap Manager', 'feedlane' ),
            'manage_options',
            'feedlane-roadmap',
            [ $this, 'roadmap_page' ]
        );

        // Status Management submenu
        add_submenu_page(
            'feedlane',
            __( 'Status', 'feedlane' ),
            __( 'Status', 'feedlane' ),
            'manage_options',
            'feedlane-status',
            [ $this, 'status_page' ]
        );
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        // General settings
        register_setting( 'feedlane_general', 'feedlane_enable_newsfeed' );
        register_setting( 'feedlane_general', 'feedlane_enable_ideas' );
        register_setting( 'feedlane_general', 'feedlane_enable_roadmap' );
        register_setting( 'feedlane_general', 'feedlane_enable_guest_submissions' );
        register_setting( 'feedlane_general', 'feedlane_sidebar_position' );
        register_setting( 'feedlane_general', 'feedlane_primary_color' );
        register_setting( 'feedlane_general', 'feedlane_enable_floating_sidebar' );

        // Firebase settings
        register_setting( 'feedlane_firebase', 'feedlane_firebase_config' );
        register_setting( 'feedlane_firebase', 'feedlane_firebase_webhook_secret' );

        // Add settings sections
        add_settings_section(
            'feedlane_general_section',
            __( 'General Settings', 'feedlane' ),
            [ $this, 'general_section_callback' ],
            'feedlane_general'
        );

        add_settings_section(
            'feedlane_firebase_section',
            __( 'Firebase Configuration', 'feedlane' ),
            [ $this, 'firebase_section_callback' ],
            'feedlane_firebase'
        );

        // Add settings fields
        $this->add_general_fields();
        $this->add_firebase_fields();
    }

    /**
     * Add general settings fields
     */
    private function add_general_fields() {
        add_settings_field(
            'feedlane_enable_newsfeed',
            __( 'Enable Newsfeed Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_newsfeed',
                'description' => __( 'Enable the newsfeed tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_ideas',
            __( 'Enable Ideas Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_ideas',
                'description' => __( 'Enable the ideas submission tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_roadmap',
            __( 'Enable Roadmap Tab', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_roadmap',
                'description' => __( 'Enable the roadmap tab in the sidebar.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_guest_submissions',
            __( 'Enable Guest Submissions', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_guest_submissions',
                'description' => __( 'Allow non-logged-in users to submit feedback and ideas.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_sidebar_position',
            __( 'Sidebar Position', 'feedlane' ),
            [ $this, 'select_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_sidebar_position',
                'options' => [
                    'left' => __( 'Left', 'feedlane' ),
                    'right' => __( 'Right', 'feedlane' ),
                ],
                'description' => __( 'Choose which side of the screen the sidebar appears on.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_primary_color',
            __( 'Primary Color', 'feedlane' ),
            [ $this, 'color_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_primary_color',
                'description' => __( 'Choose the primary color for the sidebar and buttons.', 'feedlane' ),
            ]
        );

        add_settings_field(
            'feedlane_enable_floating_sidebar',
            __( 'Enable Floating Sidebar', 'feedlane' ),
            [ $this, 'checkbox_field_callback' ],
            'feedlane_general',
            'feedlane_general_section',
            [
                'label_for' => 'feedlane_enable_floating_sidebar',
                'description' => __( 'Show the floating feedback sidebar on all pages of your website.', 'feedlane' ),
            ]
        );
    }

    /**
     * Add Firebase settings fields
     */
    private function add_firebase_fields() {
        add_settings_field(
            'feedlane_firebase_config',
            __( 'Firebase Configuration', 'feedlane' ),
            [ $this, 'textarea_field_callback' ],
            'feedlane_firebase',
            'feedlane_firebase_section',
            [
                'label_for' => 'feedlane_firebase_config',
                'description' => __( 'Paste your Firebase configuration JSON here for real-time comments.', 'feedlane' ),
                'placeholder' => '{"apiKey": "...", "authDomain": "...", "projectId": "..."}',
            ]
        );

        add_settings_field(
            'feedlane_firebase_webhook_secret',
            __( 'Webhook Secret', 'feedlane' ),
            [ $this, 'text_field_callback' ],
            'feedlane_firebase',
            'feedlane_firebase_section',
            [
                'label_for' => 'feedlane_firebase_webhook_secret',
                'description' => __( 'Secret key for Firebase webhook authentication.', 'feedlane' ),
                'type' => 'password',
            ]
        );
    }

    /**
     * Dashboard page
     */
    public function dashboard_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-dashboard"></div>
        </div>
        <?php
    }

    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-settings"></div>
        </div>
        <?php
    }

    /**
     * Analytics page
     */
    public function analytics_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-analytics"></div>
        </div>
        <?php
    }

    /**
     * Ideas management page
     */
    public function ideas_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-ideas"></div>
        </div>
        <?php
    }

    /**
     * Categories management page
     */
    public function categories_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-categories"></div>
        </div>
        <?php
    }

    /**
     * Roadmap management page
     */
    public function roadmap_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-roadmap"></div>
        </div>
        <?php
    }

    /**
     * Status management page
     */
    public function status_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <div id="feedlane-admin-status"></div>
        </div>
        <?php
    }

    /**
     * Section callbacks
     */
    public function general_section_callback() {
        echo '<p>' . __( 'Configure general settings for the Feedlane plugin.', 'feedlane' ) . '</p>';
    }

    public function firebase_section_callback() {
        echo '<p>' . __( 'Configure Firebase for real-time comments functionality.', 'feedlane' ) . '</p>';
    }

    /**
     * Field callbacks
     */
    public function checkbox_field_callback( $args ) {
        $option = get_option( $args['label_for'], true );
        ?>
        <input type="checkbox" id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>" value="1"
               <?php checked( $option, 1 ); ?> />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function select_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        ?>
        <select id="<?php echo esc_attr( $args['label_for'] ); ?>"
                name="<?php echo esc_attr( $args['label_for'] ); ?>">
            <?php foreach ( $args['options'] as $value => $label ) : ?>
                <option value="<?php echo esc_attr( $value ); ?>"
                        <?php selected( $option, $value ); ?>>
                    <?php echo esc_html( $label ); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function color_field_callback( $args ) {
        $option = get_option( $args['label_for'], '#0ea5e9' );
        ?>
        <input type="color" id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>"
               value="<?php echo esc_attr( $option ); ?>" />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function text_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        $type = $args['type'] ?? 'text';
        ?>
        <input type="<?php echo esc_attr( $type ); ?>"
               id="<?php echo esc_attr( $args['label_for'] ); ?>"
               name="<?php echo esc_attr( $args['label_for'] ); ?>"
               value="<?php echo esc_attr( $option ); ?>"
               class="regular-text" />
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    public function textarea_field_callback( $args ) {
        $option = get_option( $args['label_for'], '' );
        $placeholder = $args['placeholder'] ?? '';
        ?>
        <textarea id="<?php echo esc_attr( $args['label_for'] ); ?>"
                  name="<?php echo esc_attr( $args['label_for'] ); ?>"
                  rows="5" cols="50" class="large-text"
                  placeholder="<?php echo esc_attr( $placeholder ); ?>"><?php echo esc_textarea( $option ); ?></textarea>
        <?php if ( isset( $args['description'] ) ) : ?>
            <p class="description"><?php echo esc_html( $args['description'] ); ?></p>
        <?php endif; ?>
        <?php
    }

    /**
     * AJAX handler to get settings
     */
    public function ajax_get_settings() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $settings = [
            'feedlane_enable_newsfeed' => get_option( 'feedlane_enable_newsfeed', true ),
            'feedlane_enable_ideas' => get_option( 'feedlane_enable_ideas', true ),
            'feedlane_enable_roadmap' => get_option( 'feedlane_enable_roadmap', true ),
            'feedlane_enable_guest_submissions' => get_option( 'feedlane_enable_guest_submissions', true ),
            'feedlane_enable_floating_sidebar' => get_option( 'feedlane_enable_floating_sidebar', true ),
            'feedlane_sidebar_position' => get_option( 'feedlane_sidebar_position', 'left' ),
            'feedlane_primary_color' => get_option( 'feedlane_primary_color', '#0ea5e9' ),
            'feedlane_firebase_config' => get_option( 'feedlane_firebase_config', '' ),
            'feedlane_firebase_webhook_secret' => get_option( 'feedlane_firebase_webhook_secret', '' ),
        ];

        wp_send_json_success( $settings );
    }

    /**
     * AJAX handler to save settings
     */
    public function ajax_save_settings() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $settings = [
            'feedlane_enable_newsfeed',
            'feedlane_enable_ideas',
            'feedlane_enable_roadmap',
            'feedlane_enable_guest_submissions',
            'feedlane_enable_floating_sidebar',
            'feedlane_sidebar_position',
            'feedlane_primary_color',
            'feedlane_firebase_config',
            'feedlane_firebase_webhook_secret',
        ];

        foreach ( $settings as $setting ) {
            if ( isset( $_POST[ $setting ] ) ) {
                $value = sanitize_text_field( $_POST[ $setting ] );

                // Handle checkboxes
                if ( in_array( $setting, [
                    'feedlane_enable_newsfeed',
                    'feedlane_enable_ideas',
                    'feedlane_enable_roadmap',
                    'feedlane_enable_guest_submissions',
                    'feedlane_enable_floating_sidebar'
                ] ) ) {
                    $value = $value === 'true' || $value === '1';
                }

                update_option( $setting, $value );
            }
        }

        wp_send_json_success( 'Settings saved successfully' );
    }

    /**
     * AJAX handler to get ideas
     */
    public function ajax_get_ideas() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $page = isset( $_POST['page'] ) ? intval( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? intval( $_POST['per_page'] ) : 20;
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : 'all';

        $args = [
            'post_type' => 'feedlane_ideas',
            'posts_per_page' => $per_page,
            'paged' => $page,
            'meta_query' => [],
        ];

        if ( $status !== 'all' ) {
            $args['post_status'] = $status;
        }

        $query = new \WP_Query( $args );
        $ideas = [];

        foreach ( $query->posts as $post ) {
            $categories = wp_get_post_terms( $post->ID, 'idea_category' );
            $status = wp_get_post_terms( $post->ID, 'roadmap_status' );

            $ideas[] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'content' => $post->post_content,
                'status' => $post->post_status,
                'date' => $post->post_date,
                'author' => get_the_author_meta( 'display_name', $post->post_author ),
                'categories' => array_map( function( $term ) {
                    return [
                        'id' => $term->term_id,
                        'name' => $term->name,
                        'slug' => $term->slug,
                    ];
                }, $categories ),
                'roadmap_status' => array_map( function( $term ) {
                    return [
                        'id' => $term->term_id,
                        'name' => $term->name,
                        'slug' => $term->slug,
                    ];
                }, $status ),
                'votes' => get_post_meta( $post->ID, '_feedlane_votes', true ) ?: 0,
            ];
        }

        wp_send_json_success( [
            'ideas' => $ideas,
            'total' => $query->found_posts,
            'pages' => $query->max_num_pages,
        ] );
    }

    /**
     * AJAX handler to update idea status
     */
    public function ajax_update_idea_status() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $idea_id = isset( $_POST['idea_id'] ) ? intval( $_POST['idea_id'] ) : 0;
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : '';

        if ( ! $idea_id || ! $status ) {
            wp_send_json_error( 'Invalid parameters' );
        }

        // Update the roadmap_status taxonomy term for this idea
        $result = wp_set_object_terms( $idea_id, $status, 'roadmap_status' );

        if ( ! is_wp_error( $result ) ) {
            wp_send_json_success( 'Status updated successfully' );
        } else {
            wp_send_json_error( 'Failed to update status: ' . $result->get_error_message() );
        }
    }

    /**
     * AJAX handler to delete idea
     */
    public function ajax_delete_idea() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $idea_id = isset( $_POST['idea_id'] ) ? intval( $_POST['idea_id'] ) : 0;

        if ( ! $idea_id ) {
            wp_send_json_error( 'Invalid idea ID' );
        }

        $result = wp_delete_post( $idea_id, true );

        if ( $result ) {
            wp_send_json_success( 'Idea deleted successfully' );
        } else {
            wp_send_json_error( 'Failed to delete idea' );
        }
    }

    /**
     * AJAX handler to get categories
     */
    public function ajax_get_categories() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $terms = get_terms( [
            'taxonomy' => 'idea_category',
            'hide_empty' => false,
        ] );

        $categories = [];
        foreach ( $terms as $term ) {
            $categories[] = [
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug,
                'description' => $term->description,
                'count' => $term->count,
                'color' => get_term_meta( $term->term_id, 'color', true ) ?: '#10B981',
            ];
        }

        wp_send_json_success( $categories );
    }

    /**
     * AJAX handler to save category
     */
    public function ajax_save_category() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $category_id = isset( $_POST['category_id'] ) ? intval( $_POST['category_id'] ) : 0;
        $name = isset( $_POST['name'] ) ? sanitize_text_field( $_POST['name'] ) : '';
        $slug = isset( $_POST['slug'] ) ? sanitize_title( $_POST['slug'] ) : '';
        $description = isset( $_POST['description'] ) ? sanitize_textarea_field( $_POST['description'] ) : '';
        $color = isset( $_POST['color'] ) ? sanitize_hex_color( $_POST['color'] ) : '#10B981';

        if ( ! $name ) {
            wp_send_json_error( 'Category name is required' );
        }

        $args = [
            'name' => $name,
            'slug' => $slug ?: sanitize_title( $name ),
            'description' => $description,
        ];

        if ( $category_id ) {
            // Update existing category
            $result = wp_update_term( $category_id, 'idea_category', $args );
        } else {
            // Create new category
            $result = wp_insert_term( $name, 'idea_category', $args );
        }

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( $result->get_error_message() );
        }

        $term_id = $category_id ?: $result['term_id'];
        update_term_meta( $term_id, 'color', $color );

        wp_send_json_success( 'Category saved successfully' );
    }

    /**
     * AJAX handler to delete category
     */
    public function ajax_delete_category() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $category_id = isset( $_POST['category_id'] ) ? intval( $_POST['category_id'] ) : 0;

        if ( ! $category_id ) {
            wp_send_json_error( 'Invalid category ID' );
        }

        $result = wp_delete_term( $category_id, 'idea_category' );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( $result->get_error_message() );
        }

        wp_send_json_success( 'Category deleted successfully' );
    }

    /**
     * AJAX handler to get all status
     */
    public function ajax_get_status() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $terms = get_terms( [
            'taxonomy' => 'roadmap_status',
            'hide_empty' => false,
        ] );

        $status = [];
        foreach ( $terms as $term ) {
            $status[] = [
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug,
                'description' => $term->description,
                'count' => $term->count,
                'color' => get_term_meta( $term->term_id, 'color', true ) ?: '#6B7280',
            ];
        }

        wp_send_json_success( $status );
    }



    /**
     * AJAX handler to save status
     */
    public function ajax_save_status() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $status_id = isset( $_POST['status_id'] ) ? intval( $_POST['status_id'] ) : 0;
        $name = isset( $_POST['name'] ) ? sanitize_text_field( $_POST['name'] ) : '';
        $slug = isset( $_POST['slug'] ) ? sanitize_title( $_POST['slug'] ) : '';
        $description = isset( $_POST['description'] ) ? sanitize_textarea_field( $_POST['description'] ) : '';
        $color = isset( $_POST['color'] ) ? sanitize_hex_color( $_POST['color'] ) : '#6B7280';

        if ( ! $name ) {
            wp_send_json_error( 'Status name is required' );
        }

        $args = [
            'name' => $name,
            'slug' => $slug ?: sanitize_title( $name ),
            'description' => $description,
        ];

        if ( $status_id ) {
            // Update existing status
            $result = wp_update_term( $status_id, 'roadmap_status', $args );
        } else {
            // Create new status
            $result = wp_insert_term( $name, 'roadmap_status', $args );
        }

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( $result->get_error_message() );
        }

        $term_id = $status_id ?: $result['term_id'];
        update_term_meta( $term_id, 'color', $color );

        wp_send_json_success( 'Status saved successfully' );
    }

    /**
     * AJAX handler to delete status
     */
    public function ajax_delete_status() {
        check_ajax_referer( 'feedlane_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Unauthorized' );
        }

        $status_id = isset( $_POST['status_id'] ) ? intval( $_POST['status_id'] ) : 0;

        if ( ! $status_id ) {
            wp_send_json_error( 'Invalid status ID' );
        }

        $result = wp_delete_term( $status_id, 'roadmap_status' );

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( $result->get_error_message() );
        }

        wp_send_json_success( 'Status deleted successfully' );
    }
}
