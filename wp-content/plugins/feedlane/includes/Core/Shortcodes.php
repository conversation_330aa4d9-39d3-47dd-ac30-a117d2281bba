<?php

namespace WPDeveloper\Feedlane\Core;

/**
 * Shortcodes Class
 */
class Shortcodes {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', [ $this, 'register_shortcodes' ] );
    }

    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode( 'feedlane_sidebar', [ $this, 'sidebar_shortcode' ] );
        add_shortcode( 'feedlane_newsfeed', [ $this, 'newsfeed_shortcode' ] );
        add_shortcode( 'feedlane_ideas', [ $this, 'ideas_shortcode' ] );
        add_shortcode( 'feedlane_roadmap', [ $this, 'roadmap_shortcode' ] );

        // Add a simple test shortcode
        add_shortcode( 'feedlane_test', [ $this, 'test_shortcode' ] );

    }

    /**
     * Sidebar shortcode
     *
     * Usage: [feedlane_sidebar position="left" height="600px"]
     *
     * @param array $atts
     * @return string
     */
    public function sidebar_shortcode( $atts ) {
        $atts = shortcode_atts( [
            'position' => get_option( 'feedlane_sidebar_position', 'left' ),
            'height' => '600px',
            'primary_color' => get_option( 'feedlane_primary_color', '#0ea5e9' ),
        ], $atts, 'feedlane_sidebar' );

        // Enqueue assets
        $this->enqueue_sidebar_assets();

        // Generate unique ID
        $unique_id = 'feedlane-sidebar-' . wp_generate_uuid4();

        // Localize additional data for this instance
        wp_localize_script( 'feedlane-sidebar', 'feedlaneShortcode', [
            'instance_id' => $unique_id,
            'position' => sanitize_text_field( $atts['position'] ),
            'height' => sanitize_text_field( $atts['height'] ),
            'primary_color' => sanitize_hex_color( $atts['primary_color'] ),
        ] );

        ob_start();
        ?>
        <div id="<?php echo esc_attr( $unique_id ); ?>"
             class="feedlane-sidebar-container"
             data-position="<?php echo esc_attr( $atts['position'] ); ?>"
             data-height="<?php echo esc_attr( $atts['height'] ); ?>"
             style="--feedlane-primary-color: <?php echo esc_attr( $atts['primary_color'] ); ?>">
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Newsfeed shortcode
     *
     * Usage: [feedlane_newsfeed limit="5" show_reactions="true"]
     *
     * @param array $atts
     * @return string
     */
    public function newsfeed_shortcode( $atts ) {
        $atts = shortcode_atts( [
            'limit' => 5,
            'show_reactions' => 'true',
            'show_feedback_form' => 'true',
        ], $atts, 'feedlane_newsfeed' );

        // Get newsfeed posts
        $posts = get_posts( [
            'post_type' => 'feedlane_posts',
            'post_status' => 'publish',
            'numberposts' => intval( $atts['limit'] ),
            'orderby' => 'date',
            'order' => 'DESC',
        ] );

        if ( empty( $posts ) ) {
            return '<p>' . __( 'No newsfeed posts found.', 'feedlane' ) . '</p>';
        }

        // Enqueue assets
        $this->enqueue_sidebar_assets();

        ob_start();
        ?>
        <div class="feedlane-newsfeed-shortcode">
            <?php foreach ( $posts as $post ) : ?>
                <div class="feedlane-post" data-post-id="<?php echo esc_attr( $post->ID ); ?>">
                    <h3 class="feedlane-post-title"><?php echo esc_html( $post->post_title ); ?></h3>

                    <?php if ( has_post_thumbnail( $post->ID ) ) : ?>
                        <div class="feedlane-post-image">
                            <?php echo get_the_post_thumbnail( $post->ID, 'medium' ); ?>
                        </div>
                    <?php endif; ?>

                    <div class="feedlane-post-content">
                        <?php echo wp_kses_post( $post->post_content ); ?>
                    </div>

                    <div class="feedlane-post-meta">
                        <span class="feedlane-post-date">
                            <?php echo esc_html( get_the_date( '', $post ) ); ?>
                        </span>
                    </div>

                    <?php if ( $atts['show_reactions'] === 'true' ) : ?>
                        <div class="feedlane-reactions" data-post-id="<?php echo esc_attr( $post->ID ); ?>">
                            <!-- Reactions will be loaded by React -->
                        </div>
                    <?php endif; ?>

                    <?php if ( $atts['show_feedback_form'] === 'true' ) : ?>
                        <div class="feedlane-feedback-form" data-post-id="<?php echo esc_attr( $post->ID ); ?>">
                            <!-- Feedback form will be loaded by React -->
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Ideas shortcode
     *
     * Usage: [feedlane_ideas limit="10" category="feature-request" show_form="true"]
     *
     * @param array $atts
     * @return string
     */
    public function ideas_shortcode( $atts ) {
        $atts = shortcode_atts( [
            'limit' => 10,
            'category' => '',
            'status' => '',
            'show_form' => 'true',
            'orderby' => 'votes',
        ], $atts, 'feedlane_ideas' );

        // Build query args
        $args = [
            'post_type' => 'feedlane_ideas',
            'post_status' => 'publish',
            'numberposts' => intval( $atts['limit'] ),
        ];

        // Add taxonomy filters
        $tax_query = [];

        if ( ! empty( $atts['category'] ) ) {
            $tax_query[] = [
                'taxonomy' => 'idea_category',
                'field' => 'slug',
                'terms' => sanitize_text_field( $atts['category'] ),
            ];
        }

        if ( ! empty( $atts['status'] ) ) {
            $tax_query[] = [
                'taxonomy' => 'roadmap_status',
                'field' => 'slug',
                'terms' => sanitize_text_field( $atts['status'] ),
            ];
        }

        if ( ! empty( $tax_query ) ) {
            $args['tax_query'] = $tax_query;
        }

        // Handle ordering
        if ( $atts['orderby'] === 'votes' ) {
            $args['meta_key'] = '_vote_count';
            $args['orderby'] = 'meta_value_num';
            $args['order'] = 'DESC';
        }

        $ideas = get_posts( $args );

        // Enqueue assets
        $this->enqueue_sidebar_assets();

        ob_start();
        ?>
        <div class="feedlane-ideas-shortcode">
            <?php if ( $atts['show_form'] === 'true' ) : ?>
                <div class="feedlane-idea-form">
                    <!-- Idea submission form will be loaded by React -->
                </div>
            <?php endif; ?>

            <div class="feedlane-ideas-list">
                <?php if ( empty( $ideas ) ) : ?>
                    <p><?php _e( 'No ideas found.', 'feedlane' ); ?></p>
                <?php else : ?>
                    <?php foreach ( $ideas as $idea ) : ?>
                        <div class="feedlane-idea" data-idea-id="<?php echo esc_attr( $idea->ID ); ?>">
                            <h3 class="feedlane-idea-title"><?php echo esc_html( $idea->post_title ); ?></h3>

                            <div class="feedlane-idea-excerpt">
                                <?php echo esc_html( wp_trim_words( $idea->post_content, 30 ) ); ?>
                            </div>

                            <div class="feedlane-idea-meta">
                                <?php
                                $vote_count = get_post_meta( $idea->ID, '_vote_count', true ) ?: 0;
                                $categories = wp_get_post_terms( $idea->ID, 'idea_category' );
                                $statuses = wp_get_post_terms( $idea->ID, 'roadmap_status' );
                                ?>

                                <span class="feedlane-vote-count">
                                    <?php printf( _n( '%d vote', '%d votes', $vote_count, 'feedlane' ), $vote_count ); ?>
                                </span>

                                <?php if ( ! empty( $categories ) ) : ?>
                                    <span class="feedlane-category">
                                        <?php echo esc_html( $categories[0]->name ); ?>
                                    </span>
                                <?php endif; ?>

                                <?php if ( ! empty( $statuses ) ) : ?>
                                    <span class="feedlane-status feedlane-status-<?php echo esc_attr( $statuses[0]->slug ); ?>">
                                        <?php echo esc_html( $statuses[0]->name ); ?>
                                    </span>
                                <?php endif; ?>
                            </div>

                            <div class="feedlane-idea-actions" data-idea-id="<?php echo esc_attr( $idea->ID ); ?>">
                                <!-- Vote button will be loaded by React -->
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Roadmap shortcode
     *
     * Usage: [feedlane_roadmap]
     *
     * @param array $atts
     * @return string
     */
    public function roadmap_shortcode( $atts ) {
        $atts = shortcode_atts( [], $atts, 'feedlane_roadmap' );

        // Get roadmap statuses
        $statuses = get_terms( [
            'taxonomy' => 'roadmap_status',
            'hide_empty' => false,
        ] );

        if ( is_wp_error( $statuses ) || empty( $statuses ) ) {
            return '<p>' . __( 'No roadmap statuses found.', 'feedlane' ) . '</p>';
        }

        // Enqueue assets
        $this->enqueue_sidebar_assets();

        ob_start();
        ?>
        <div class="feedlane-roadmap-shortcode">
            <?php foreach ( $statuses as $status ) : ?>
                <?php
                $ideas = get_posts( [
                    'post_type' => 'feedlane_ideas',
                    'post_status' => 'publish',
                    'numberposts' => -1,
                    'tax_query' => [
                        [
                            'taxonomy' => 'roadmap_status',
                            'field' => 'term_id',
                            'terms' => $status->term_id,
                        ],
                    ],
                    'meta_key' => '_vote_count',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC',
                ] );
                ?>

                <div class="feedlane-roadmap-section" data-status="<?php echo esc_attr( $status->slug ); ?>">
                    <h3 class="feedlane-roadmap-title">
                        <?php echo esc_html( $status->name ); ?>
                        <span class="feedlane-roadmap-count">(<?php echo count( $ideas ); ?>)</span>
                    </h3>

                    <div class="feedlane-roadmap-items">
                        <?php if ( empty( $ideas ) ) : ?>
                            <p class="feedlane-no-items"><?php _e( 'No items in this status.', 'feedlane' ); ?></p>
                        <?php else : ?>
                            <?php foreach ( $ideas as $idea ) : ?>
                                <div class="feedlane-roadmap-item" data-idea-id="<?php echo esc_attr( $idea->ID ); ?>">
                                    <h4 class="feedlane-item-title"><?php echo esc_html( $idea->post_title ); ?></h4>

                                    <div class="feedlane-item-excerpt">
                                        <?php echo esc_html( wp_trim_words( $idea->post_content, 20 ) ); ?>
                                    </div>

                                    <div class="feedlane-item-meta">
                                        <?php
                                        $vote_count = get_post_meta( $idea->ID, '_vote_count', true ) ?: 0;
                                        ?>
                                        <span class="feedlane-vote-count">
                                            <?php printf( _n( '%d vote', '%d votes', $vote_count, 'feedlane' ), $vote_count ); ?>
                                        </span>
                                    </div>

                                    <div class="feedlane-item-actions" data-idea-id="<?php echo esc_attr( $idea->ID ); ?>">
                                        <!-- Vote button will be loaded by React -->
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Test shortcode
     *
     * Usage: [feedlane_test]
     *
     * @param array $atts
     * @return string
     */
    public function test_shortcode( $atts ) {
        return '<div style="background: #f0f0f0; padding: 20px; border: 2px solid #0ea5e9; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #0ea5e9; margin: 0 0 10px 0;">🎉 Feedlane Test Shortcode Working!</h3>
            <p style="margin: 0;">This confirms that Feedlane shortcodes are working correctly.</p>
            <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                Try: <code>[feedlane_sidebar]</code>, <code>[feedlane_newsfeed]</code>, <code>[feedlane_ideas]</code>, <code>[feedlane_roadmap]</code>
            </p>
        </div>';
    }

    /**
     * Enqueue sidebar assets
     */
    private function enqueue_sidebar_assets() {
        if ( ! wp_script_is( 'feedlane-sidebar', 'enqueued' ) ) {
            feedlane()->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js' );
            feedlane()->assets->enqueue( 'feedlane-sidebar', 'css/feedlane-sidebar.min.css' );

            // Localize script data
            feedlane()->assets->localize(
                'feedlane-sidebar',
                'feedlaneData',
                [
                    'rest_url' => esc_url_raw( rest_url() ),
                    'nonce' => wp_create_nonce( 'wp_rest' ),
                    'ajax_url' => admin_url( 'admin-ajax.php' ),
                    'settings' => [
                        'enable_newsfeed' => get_option( 'feedlane_enable_newsfeed', true ),
                        'enable_ideas' => get_option( 'feedlane_enable_ideas', true ),
                        'enable_roadmap' => get_option( 'feedlane_enable_roadmap', true ),
                        'enable_guest_submissions' => get_option( 'feedlane_enable_guest_submissions', true ),
                        'sidebar_position' => get_option( 'feedlane_sidebar_position', 'left' ),
                        'primary_color' => get_option( 'feedlane_primary_color', '#0ea5e9' ),
                    ],
                    'user_id' => get_current_user_id(),
                    'is_user_logged_in' => is_user_logged_in(),
                ]
            );
        }
    }
}
