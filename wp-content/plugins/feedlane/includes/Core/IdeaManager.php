<?php

namespace WPDeveloper\Feedlane\Core;

class IdeaManager {

    public function __construct() {
        add_action('wp_ajax_feedlane_get_ideas', [$this, 'get_ideas']);
        add_action('wp_ajax_feedlane_get_categories', [$this, 'get_categories']);
        add_action('wp_ajax_feedlane_get_status', [$this, 'get_status']);
        add_action('wp_ajax_feedlane_create_idea', [$this, 'create_idea']);
        add_action('wp_ajax_feedlane_update_idea', [$this, 'update_idea']);
        add_action('wp_ajax_feedlane_delete_idea', [$this, 'delete_idea']);
        add_action('wp_ajax_feedlane_toggle_idea_visibility', [$this, 'toggle_idea_visibility']);
        add_action('wp_ajax_feedlane_add_comment', [$this, 'add_comment']);
        add_action('wp_ajax_feedlane_get_comments', [$this, 'get_comments']);
    }

    public function get_ideas() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $page = intval($_POST['page'] ?? 1);
        $search = sanitize_text_field($_POST['search'] ?? '');
        $orderby = sanitize_text_field($_POST['orderby'] ?? 'date');
        $per_page = 20;

        $args = [
            'post_type' => 'feedlane_ideas',
            'post_status' => ['publish', 'private'],
            'posts_per_page' => $per_page,
            'paged' => $page,
            'meta_query' => []
        ];

        // Handle search
        if (!empty($search)) {
            $args['s'] = $search;
        }

        // Handle ordering
        switch ($orderby) {
            case 'title':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'votes':
                $args['meta_key'] = '_feedlane_vote_count';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            case 'comments':
                $args['orderby'] = 'comment_count';
                $args['order'] = 'DESC';
                break;
            default:
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        $query = new \WP_Query($args);
        $ideas = [];

        foreach ($query->posts as $post) {
            $author = get_userdata($post->post_author);
            $categories = wp_get_post_terms($post->ID, 'idea_category');
            $status = wp_get_post_terms($post->ID, 'roadmap_status');
            $vote_count = get_post_meta($post->ID, '_feedlane_vote_count', true) ?: 0;
            $visibility = get_post_meta($post->ID, '_feedlane_visibility', true) ?: 'public';

            $ideas[] = [
                'id' => $post->ID,
                'title' => $post->post_title,
                'content' => $post->post_content,
                'excerpt' => $post->post_excerpt,
                'date' => $post->post_date,
                'author_name' => $author ? $author->display_name : 'Unknown',
                'author_id' => $post->post_author,
                'status' => !empty($status) ? $status[0]->slug : '',
                'categories' => array_map(function($cat) {
                    return [
                        'id' => $cat->term_id,
                        'name' => $cat->name,
                        'slug' => $cat->slug
                    ];
                }, $categories),
                'vote_count' => intval($vote_count),
                'comment_count' => intval($post->comment_count),
                'visibility' => $visibility
            ];
        }

        wp_send_json_success([
            'ideas' => $ideas,
            'pages' => $query->max_num_pages,
            'total' => $query->found_posts
        ]);
    }

    public function get_categories() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $categories = get_terms([
            'taxonomy' => 'idea_category',
            'hide_empty' => false
        ]);

        $formatted_categories = [];
        foreach ($categories as $category) {
            $color = get_term_meta($category->term_id, '_feedlane_color', true) ?: '#6B7280';
            $formatted_categories[] = [
                'id' => $category->term_id,
                'name' => $category->name,
                'slug' => $category->slug,
                'color' => $color
            ];
        }

        wp_send_json_success($formatted_categories);
    }

    public function get_status() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $status_terms = get_terms([
            'taxonomy' => 'roadmap_status',
            'hide_empty' => false
        ]);

        $formatted_status = [];
        foreach ($status_terms as $status) {
            $color = get_term_meta($status->term_id, '_feedlane_color', true) ?: '#6B7280';
            $formatted_status[] = [
                'id' => $status->term_id,
                'name' => $status->name,
                'slug' => $status->slug,
                'color' => $color
            ];
        }

        wp_send_json_success($formatted_status);
    }

    public function create_idea() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $title = sanitize_text_field($_POST['title']);
        $content = wp_kses_post($_POST['content']);
        $status = sanitize_text_field($_POST['status'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $visibility = sanitize_text_field($_POST['visibility'] ?? 'public');
        $email = sanitize_email($_POST['email'] ?? '');
        $first_name = sanitize_text_field($_POST['first_name'] ?? '');
        $last_name = sanitize_text_field($_POST['last_name'] ?? '');

        if (empty($title) || empty($content)) {
            wp_send_json_error('Title and content are required');
            return;
        }

        // Create post
        $post_data = [
            'post_title' => $title,
            'post_content' => $content,
            'post_type' => 'feedlane_ideas',
            'post_status' => $visibility === 'private' ? 'private' : 'publish',
            'post_author' => get_current_user_id()
        ];

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create idea');
            return;
        }

        // Set status - always set, even if empty to clear existing
        $status_term_ids = [];
        if (!empty($status)) {
            $status_term = get_term_by('slug', $status, 'roadmap_status');
            if ($status_term && !is_wp_error($status_term)) {
                $status_term_ids = [$status_term->term_id];
            }
        }
        wp_set_post_terms($post_id, $status_term_ids, 'roadmap_status');

        // Set category - always set, even if empty to clear existing
        $category_term_ids = [];
        if (!empty($category)) {
            $category_term = get_term_by('slug', $category, 'idea_category');
            if ($category_term && !is_wp_error($category_term)) {
                $category_term_ids = [$category_term->term_id];
            }
        }
        wp_set_post_terms($post_id, $category_term_ids, 'idea_category');

        // Set visibility meta
        update_post_meta($post_id, '_feedlane_visibility', $visibility);

        // Store additional user info if provided
        if (!empty($email)) {
            update_post_meta($post_id, '_feedlane_user_email', $email);
        }
        if (!empty($first_name)) {
            update_post_meta($post_id, '_feedlane_user_first_name', $first_name);
        }
        if (!empty($last_name)) {
            update_post_meta($post_id, '_feedlane_user_last_name', $last_name);
        }

        wp_send_json_success([
            'id' => $post_id,
            'message' => 'Idea created successfully'
        ]);
    }

    public function update_idea() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $idea_id   = intval($_POST['idea_id']);
        $title     = sanitize_text_field($_POST['title']);
        $content   = wp_kses_post($_POST['content']);
        $status    = sanitize_text_field($_POST['status'] ?? '');
        $category  = sanitize_text_field($_POST['categories'] ?? '');
        $visibility = sanitize_text_field($_POST['visibility'] ?? 'public');

        if (empty($title) || empty($content)) {
            wp_send_json_error('Title and content are required');
            return;
        }

        // Update post
        $post_data = [
            'ID'           => $idea_id,
            'post_title'   => $title,
            'post_content' => $content,
            'post_status'  => $visibility === 'private' ? 'private' : 'publish'
        ];

        $result = wp_update_post($post_data);

        if (is_wp_error($result)) {
            wp_send_json_error('Failed to update idea');
            return;
        }

        // Update roadmap status taxonomy - Convert slug to term ID
        $status_term_ids = [];
        if (!empty($status)) {
            $status_term = get_term_by('slug', $status, 'roadmap_status');
            if ($status_term && !is_wp_error($status_term)) {
                $status_term_ids = [$status_term->term_id];
            }
        }
        wp_set_post_terms($idea_id, $status_term_ids, 'roadmap_status');

        // Convert category slug to term ID for wp_set_post_terms
        $category_term_ids = [];
        if (!empty($category)) {
            $term = get_term_by('slug', $category, 'idea_category');
            if ($term && !is_wp_error($term)) {
                $category_term_ids = [$term->term_id];
            }
        }

        wp_set_post_terms($idea_id, $category_term_ids, 'idea_category');

        // Update visibility meta
        update_post_meta($idea_id, '_feedlane_visibility', $visibility);

        wp_send_json_success('Idea updated successfully');
    }

    public function delete_idea() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $idea_id = intval($_POST['idea_id']);

        if (empty($idea_id)) {
            wp_send_json_error('Invalid idea ID');
            return;
        }

        $result = wp_delete_post($idea_id, true);

        if (!$result) {
            wp_send_json_error('Failed to delete idea');
            return;
        }

        wp_send_json_success('Idea deleted successfully');
    }

    public function toggle_idea_visibility() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $idea_id = intval($_POST['idea_id']);
        $visibility = sanitize_text_field($_POST['visibility']);

        if (empty($idea_id) || !in_array($visibility, ['public', 'private'])) {
            wp_send_json_error('Invalid parameters');
            return;
        }

        // Update post status
        $post_status = $visibility === 'private' ? 'private' : 'publish';
        wp_update_post([
            'ID' => $idea_id,
            'post_status' => $post_status
        ]);

        // Update visibility meta
        update_post_meta($idea_id, '_feedlane_visibility', $visibility);

        wp_send_json_success('Visibility updated successfully');
    }

    public function add_comment() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $post_id = intval($_POST['post_id']);
        $content = wp_kses_post($_POST['content']);

        if (empty($post_id) || empty($content)) {
            wp_send_json_error('Post ID and content are required');
            return;
        }

        // Create comment
        $comment_data = [
            'comment_post_ID' => $post_id,
            'comment_content' => $content,
            'comment_approved' => 1,
            'comment_author' => wp_get_current_user()->display_name,
            'comment_author_email' => wp_get_current_user()->user_email,
            'user_id' => get_current_user_id()
        ];

        $comment_id = wp_insert_comment($comment_data);

        if (!$comment_id) {
            wp_send_json_error('Failed to add comment');
            return;
        }

        // Get the created comment
        $comment = get_comment($comment_id);

        wp_send_json_success([
            'id' => $comment->comment_ID,
            'content' => ['rendered' => wpautop($comment->comment_content)],
            'author_name' => $comment->comment_author,
            'date' => $comment->comment_date
        ]);
    }

    public function get_comments() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'feedlane_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $post_id = intval($_POST['post_id']);

        if (empty($post_id)) {
            wp_send_json_error('Post ID is required');
            return;
        }

        // Get comments
        $comments = get_comments([
            'post_id' => $post_id,
            'status' => 'approve',
            'orderby' => 'comment_date',
            'order' => 'ASC'
        ]);

        $formatted_comments = [];
        foreach ($comments as $comment) {
            $formatted_comments[] = [
                'id' => $comment->comment_ID,
                'content' => ['rendered' => wpautop($comment->comment_content)],
                'author_name' => $comment->comment_author,
                'date' => $comment->comment_date
            ];
        }

        wp_send_json_success($formatted_comments);
    }
}
