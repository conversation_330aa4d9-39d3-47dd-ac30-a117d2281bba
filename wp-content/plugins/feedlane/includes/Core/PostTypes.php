<?php

namespace WPDeveloper\Feedlane\Core;

/**
 * Custom Post Types Class
 */
class PostTypes {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', [ $this, 'register_post_types' ] );

        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane PostTypes::__construct() called' );
        }
    }

    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane PostTypes::register_post_types() called' );
        }

        $this->register_feedlane_posts();
        $this->register_feedlane_ideas();

        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane post types registered: feedlane_posts, feedlane_ideas' );
        }
    }

    /**
     * Register feedlane_posts post type
     */
    private function register_feedlane_posts() {
        $labels = [
            'name'                  => _x( 'Newsfeed Posts', 'Post type general name', 'feedlane' ),
            'singular_name'         => _x( 'Newsfeed Post', 'Post type singular name', 'feedlane' ),
            'menu_name'             => _x( 'Newsfeed', 'Admin Menu text', 'feedlane' ),
            'name_admin_bar'        => _x( 'Newsfeed Post', 'Add New on Toolbar', 'feedlane' ),
            'add_new'               => __( 'Add New', 'feedlane' ),
            'add_new_item'          => __( 'Add New Newsfeed Post', 'feedlane' ),
            'new_item'              => __( 'New Newsfeed Post', 'feedlane' ),
            'edit_item'             => __( 'Edit Newsfeed Post', 'feedlane' ),
            'view_item'             => __( 'View Newsfeed Post', 'feedlane' ),
            'all_items'             => __( 'All Newsfeed Posts', 'feedlane' ),
            'search_items'          => __( 'Search Newsfeed Posts', 'feedlane' ),
            'parent_item_colon'     => __( 'Parent Newsfeed Posts:', 'feedlane' ),
            'not_found'             => __( 'No newsfeed posts found.', 'feedlane' ),
            'not_found_in_trash'    => __( 'No newsfeed posts found in Trash.', 'feedlane' ),
            'featured_image'        => _x( 'Newsfeed Post Cover Image', 'Overrides the "Featured Image" phrase', 'feedlane' ),
            'set_featured_image'    => _x( 'Set cover image', 'Overrides the "Set featured image" phrase', 'feedlane' ),
            'remove_featured_image' => _x( 'Remove cover image', 'Overrides the "Remove featured image" phrase', 'feedlane' ),
            'use_featured_image'    => _x( 'Use as cover image', 'Overrides the "Use as featured image" phrase', 'feedlane' ),
            'archives'              => _x( 'Newsfeed Post archives', 'The post type archive label', 'feedlane' ),
            'insert_into_item'      => _x( 'Insert into newsfeed post', 'Overrides the "Insert into post" phrase', 'feedlane' ),
            'uploaded_to_this_item' => _x( 'Uploaded to this newsfeed post', 'Overrides the "Uploaded to this post" phrase', 'feedlane' ),
            'filter_items_list'     => _x( 'Filter newsfeed posts list', 'Screen reader text for the filter links', 'feedlane' ),
            'items_list_navigation' => _x( 'Newsfeed posts list navigation', 'Screen reader text for the pagination', 'feedlane' ),
            'items_list'            => _x( 'Newsfeed posts list', 'Screen reader text for the items list', 'feedlane' ),
        ];

        $args = [
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'feedlane',
            'query_var'          => true,
            'rewrite'            => [ 'slug' => 'feedlane-posts' ],
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'menu_icon'          => 'dashicons-megaphone',
            'supports'           => [ 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ],
            'show_in_rest'       => true,
            'rest_base'          => 'feedlane-posts',
        ];

        register_post_type( 'feedlane_posts', $args );
    }

    /**
     * Register feedlane_ideas post type
     */
    private function register_feedlane_ideas() {
        $labels = [
            'name'                  => _x( 'Ideas', 'Post type general name', 'feedlane' ),
            'singular_name'         => _x( 'Idea', 'Post type singular name', 'feedlane' ),
            'menu_name'             => _x( 'Ideas', 'Admin Menu text', 'feedlane' ),
            'name_admin_bar'        => _x( 'Idea', 'Add New on Toolbar', 'feedlane' ),
            'add_new'               => __( 'Add New', 'feedlane' ),
            'add_new_item'          => __( 'Add New Idea', 'feedlane' ),
            'new_item'              => __( 'New Idea', 'feedlane' ),
            'edit_item'             => __( 'Edit Idea', 'feedlane' ),
            'view_item'             => __( 'View Idea', 'feedlane' ),
            'all_items'             => __( 'All Ideas', 'feedlane' ),
            'search_items'          => __( 'Search Ideas', 'feedlane' ),
            'parent_item_colon'     => __( 'Parent Ideas:', 'feedlane' ),
            'not_found'             => __( 'No ideas found.', 'feedlane' ),
            'not_found_in_trash'    => __( 'No ideas found in Trash.', 'feedlane' ),
            'featured_image'        => _x( 'Idea Cover Image', 'Overrides the "Featured Image" phrase', 'feedlane' ),
            'set_featured_image'    => _x( 'Set cover image', 'Overrides the "Set featured image" phrase', 'feedlane' ),
            'remove_featured_image' => _x( 'Remove cover image', 'Overrides the "Remove featured image" phrase', 'feedlane' ),
            'use_featured_image'    => _x( 'Use as cover image', 'Overrides the "Use as featured image" phrase', 'feedlane' ),
            'archives'              => _x( 'Idea archives', 'The post type archive label', 'feedlane' ),
            'insert_into_item'      => _x( 'Insert into idea', 'Overrides the "Insert into post" phrase', 'feedlane' ),
            'uploaded_to_this_item' => _x( 'Uploaded to this idea', 'Overrides the "Uploaded to this post" phrase', 'feedlane' ),
            'filter_items_list'     => _x( 'Filter ideas list', 'Screen reader text for the filter links', 'feedlane' ),
            'items_list_navigation' => _x( 'Ideas list navigation', 'Screen reader text for the pagination', 'feedlane' ),
            'items_list'            => _x( 'Ideas list', 'Screen reader text for the items list', 'feedlane' ),
        ];

        $args = [
            'labels'             => $labels,
            'public'             => false,
            'publicly_queryable' => false,
            'show_ui'            => true,
            'show_in_menu'       => 'feedlane',
            'query_var'          => true,
            'rewrite'            => [ 'slug' => 'feedlane-ideas' ],
            'capability_type'    => 'post',
            'has_archive'        => false,
            'hierarchical'       => false,
            'menu_position'      => null,
            'menu_icon'          => 'dashicons-lightbulb',
            'supports'           => [ 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields' ],
            'show_in_rest'       => true,
            'rest_base'          => 'feedlane-ideas',
        ];

        register_post_type( 'feedlane_ideas', $args );
    }
}
