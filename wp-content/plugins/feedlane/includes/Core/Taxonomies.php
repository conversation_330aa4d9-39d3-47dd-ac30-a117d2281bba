<?php

namespace WPDeveloper\Feedlane\Core;

/**
 * Taxonomies Class
 */
class Taxonomies {

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'init', [ $this, 'register_taxonomies' ] );

        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane Taxonomies::__construct() called' );
        }
    }

    /**
     * Register custom taxonomies
     */
    public function register_taxonomies() {
        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane Taxonomies::register_taxonomies() called' );
        }

        $this->register_idea_category();
        $this->register_roadmap_status();

        // Debug
        if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
            error_log( 'Feedlane taxonomies registered: idea_category, roadmap_status' );
        }
    }

    /**
     * Register idea category taxonomy
     */
    private function register_idea_category() {
        $labels = [
            'name'                       => _x( 'Idea Categories', 'Taxonomy General Name', 'feedlane' ),
            'singular_name'              => _x( 'Idea Category', 'Taxonomy Singular Name', 'feedlane' ),
            'menu_name'                  => __( 'Categories', 'feedlane' ),
            'all_items'                  => __( 'All Categories', 'feedlane' ),
            'parent_item'                => __( 'Parent Category', 'feedlane' ),
            'parent_item_colon'          => __( 'Parent Category:', 'feedlane' ),
            'new_item_name'              => __( 'New Category Name', 'feedlane' ),
            'add_new_item'               => __( 'Add New Category', 'feedlane' ),
            'edit_item'                  => __( 'Edit Category', 'feedlane' ),
            'update_item'                => __( 'Update Category', 'feedlane' ),
            'view_item'                  => __( 'View Category', 'feedlane' ),
            'separate_items_with_commas' => __( 'Separate categories with commas', 'feedlane' ),
            'add_or_remove_items'        => __( 'Add or remove categories', 'feedlane' ),
            'choose_from_most_used'      => __( 'Choose from the most used', 'feedlane' ),
            'popular_items'              => __( 'Popular Categories', 'feedlane' ),
            'search_items'               => __( 'Search Categories', 'feedlane' ),
            'not_found'                  => __( 'Not Found', 'feedlane' ),
            'no_terms'                   => __( 'No categories', 'feedlane' ),
            'items_list'                 => __( 'Categories list', 'feedlane' ),
            'items_list_navigation'      => __( 'Categories list navigation', 'feedlane' ),
        ];

        $args = [
            'labels'                     => $labels,
            'hierarchical'               => true,
            'public'                     => false,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => false,
            'show_tagcloud'              => false,
            'show_in_rest'               => true,
            'rest_base'                  => 'idea-categories',
        ];

        register_taxonomy( 'idea_category', [ 'feedlane_ideas' ], $args );

        // Add default categories
        $this->add_default_idea_categories();
    }

    /**
     * Register roadmap status taxonomy
     */
    private function register_roadmap_status() {
        $labels = [
            'name'                       => _x( 'Roadmap Status', 'Taxonomy General Name', 'feedlane' ),
            'singular_name'              => _x( 'Status', 'Taxonomy Singular Name', 'feedlane' ),
            'menu_name'                  => __( 'Roadmap Status', 'feedlane' ),
            'all_items'                  => __( 'All Statuses', 'feedlane' ),
            'parent_item'                => __( 'Parent Status', 'feedlane' ),
            'parent_item_colon'          => __( 'Parent Status:', 'feedlane' ),
            'new_item_name'              => __( 'New Status Name', 'feedlane' ),
            'add_new_item'               => __( 'Add New Status', 'feedlane' ),
            'edit_item'                  => __( 'Edit Status', 'feedlane' ),
            'update_item'                => __( 'Update Status', 'feedlane' ),
            'view_item'                  => __( 'View Status', 'feedlane' ),
            'separate_items_with_commas' => __( 'Separate statuses with commas', 'feedlane' ),
            'add_or_remove_items'        => __( 'Add or remove statuses', 'feedlane' ),
            'choose_from_most_used'      => __( 'Choose from the most used', 'feedlane' ),
            'popular_items'              => __( 'Popular Statuses', 'feedlane' ),
            'search_items'               => __( 'Search Statuses', 'feedlane' ),
            'not_found'                  => __( 'Not Found', 'feedlane' ),
            'no_terms'                   => __( 'No statuses', 'feedlane' ),
            'items_list'                 => __( 'Statuses list', 'feedlane' ),
            'items_list_navigation'      => __( 'Statuses list navigation', 'feedlane' ),
        ];

        $args = [
            'labels'                     => $labels,
            'hierarchical'               => false,
            'public'                     => false,
            'show_ui'                    => true,
            'show_admin_column'          => true,
            'show_in_nav_menus'          => false,
            'show_tagcloud'              => false,
            'show_in_rest'               => true,
            'rest_base'                  => 'roadmap-status',
        ];

        register_taxonomy( 'roadmap_status', [ 'feedlane_ideas' ], $args );

        // Add default statuses
        $this->add_default_roadmap_statuses();
    }

    /**
     * Add default idea categories
     */
    private function add_default_idea_categories() {
        $categories = [
            'improvement' => __( 'Improvement', 'feedlane' ),
            'feature-request' => __( 'Feature Request', 'feedlane' ),
            'bug' => __( 'Bug', 'feedlane' ),
            'changelog' => __( 'Changelog', 'feedlane' ),
            'feedback' => __( 'Feedback', 'feedlane' ),
        ];

        foreach ( $categories as $slug => $name ) {
            if ( ! term_exists( $slug, 'idea_category' ) ) {
                wp_insert_term( $name, 'idea_category', [
                    'slug' => $slug,
                ] );
            }
        }
    }

    /**
     * Add default roadmap statuses
     */
    private function add_default_roadmap_statuses() {
        $statuses = [
            'under-review' => __( 'Under Review', 'feedlane' ),
            'planned' => __( 'Planned', 'feedlane' ),
            'in-progress' => __( 'In Progress', 'feedlane' ),
            'completed' => __( 'Completed', 'feedlane' ),
        ];

        foreach ( $statuses as $slug => $name ) {
            if ( ! term_exists( $slug, 'roadmap_status' ) ) {
                wp_insert_term( $name, 'roadmap_status', [
                    'slug' => $slug,
                ] );
            }
        }
    }
}
