<?php

namespace WPD<PERSON>loper\Feedlane;

use WPDeveloper\Feedlane\Core\Admin;
use WPDeveloper\Feedlane\Core\Shortcodes;
use WPDeveloper\Feedlane\Database\DBSchema;
use WPDeveloper\Feedlane\Utils\Enqueue;
use W<PERSON><PERSON>lop<PERSON>\Feedlane\Widgets\ElementorWidget;
use WPD<PERSON>loper\Feedlane\Widgets\GutenbergBlock;

/**
 * Main Plugin Class
 */
final class Plugin {

    /**
     * Plugin instance
     *
     * @var Plugin|null
     */
    private static $_instance = null;

    /**
     * Plugin version
     *
     * @var string
     */
    private $version = '1.0.0';

    /**
     * Database version
     *
     * @var string
     */
    private $db_version = '1.0.0';

    /**
     * Asset enqueue handler
     *
     * @var Enqueue
     */
    public $assets = null;

    /**
     * Create a plugin instance
     *
     * @return Plugin|null
     */
    public static function get_instance() {
        if ( self::$_instance === null ) {
            self::$_instance = new self();
            do_action( 'feedlane_loaded' );
        }

        return self::$_instance;
    }

    /**
     * Plugin constructor
     */
    public function __construct() {
        if ( defined( 'FEEDLANE_VERSION' ) ) {
            $this->version = FEEDLANE_VERSION;
        }

        $this->define_constants();
        $this->init_hooks();
    }

    /**
     * Define plugin constants
     */
    private function define_constants() {
        $this->define( 'FEEDLANE_DB_VERSION', $this->db_version );
        $this->define( 'FEEDLANE_ABSPATH', dirname( FEEDLANE_FILE ) . '/' );
        $this->define( 'FEEDLANE_ABSURL', plugin_dir_url( FEEDLANE_FILE ) );
    }

    /**
     * Define constant if not already set
     *
     * @param string $name Constant name
     * @param mixed $value Constant value
     */
    private function define( string $name, $value ) {
        if ( ! defined( $name ) ) {
            define( $name, $value );
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation hook
        register_activation_hook( FEEDLANE_FILE, [ $this, 'on_plugin_activation' ] );

        // Deactivation hook
        register_deactivation_hook( FEEDLANE_FILE, [ $this, 'on_plugin_deactivation' ] );

        // Initialize plugin (check if init has already fired)
        if ( did_action( 'init' ) ) {
            // Init has already fired, initialize immediately
            $this->init();
        } else {
            // Init hasn't fired yet, hook into it
            add_action( 'init', [ $this, 'init' ], 1 );
        }

        // Load text domain
        add_action( 'init', [ $this, 'load_textdomain' ] );

        // Initialize REST API
        add_action( 'rest_api_init', [ $this, 'init_rest_api' ] );

        // Enqueue scripts
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_public_scripts' ] );
        add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_admin_scripts' ] );



        // Initialize Elementor widget
        add_action( 'elementor/widgets/register', [ $this, 'init_elementor_widget' ] );

        // Initialize Gutenberg block
        add_action( 'init', [ $this, 'init_gutenberg_block' ] );
    }

    /**
     * Plugin activation callback
     */
    public function on_plugin_activation() {
        // Create database tables
        DBSchema::create_tables();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Set default options
        $this->set_default_options();
    }

    /**
     * Plugin deactivation callback
     */
    public function on_plugin_deactivation() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = [
            'feedlane_enable_newsfeed' => true,
            'feedlane_enable_ideas' => true,
            'feedlane_enable_roadmap' => true,
            'feedlane_enable_guest_submissions' => true,
            'feedlane_sidebar_position' => 'left',
            'feedlane_primary_color' => '#0ea5e9',
            'feedlane_enable_floating_sidebar' => true,
            'feedlane_firebase_config' => '',
        ];

        foreach ( $defaults as $option => $value ) {
            if ( get_option( $option ) === false ) {
                add_option( $option, $value );
            }
        }
    }

    /**
     * Initialize plugin
     */
    public function init() {


        // Register post types directly
        $this->register_post_types();

        // Register taxonomies directly
        $this->register_taxonomies();

        // Initialize core classes
        new Shortcodes();

        // Initialize assets
        $this->assets = new Enqueue();

        // Initialize admin (only in admin)
        if ( is_admin() ) {
            new Admin();

        }

        // Initialize frontend (only on frontend)
        if ( ! is_admin() ) {
            add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_frontend_assets' ] );
            add_action( 'wp_footer', [ $this, 'add_floating_sidebar' ] );
        }
    }

    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'feedlane',
            false,
            dirname( FEEDLANE_PLUGIN_BASENAME ) . '/languages'
        );
    }

    /**
     * Initialize REST API endpoints
     */
    public function init_rest_api() {


        $api_classes = [
            'FeedbackApi',
            'IdeasApi',
            'CommentsApi',
            'ReactionsApi',
            'CategoriesApi',
        ];

        foreach ( $api_classes as $class ) {
            $classname = "\\WPDeveloper\\Feedlane\\REST\\{$class}";
            if ( class_exists( $classname ) ) {
                new $classname();
            }
        }
    }

    /**
     * Enqueue public scripts
     */
    public function enqueue_public_scripts() {
        // Enqueue sidebar assets
        $this->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js' );
        $this->assets->enqueue( 'feedlane-sidebar', 'css/feedlane-sidebar.min.css' );

        // Localize script data
        $this->assets->localize(
            'feedlane-sidebar',
            'feedlaneData',
            [
                'rest_url' => esc_url_raw( rest_url() ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'settings' => $this->get_frontend_settings(),
                'user_id' => get_current_user_id(),
                'is_user_logged_in' => is_user_logged_in(),
            ]
        );
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts( $hook ) {
        // Only load on Feedlane admin pages
        if ( strpos( $hook, 'feedlane' ) === false ) {
            return;
        }

        // Get auto-generated dependencies from webpack
        $asset_file = FEEDLANE_ASSETS_DIR_PATH . 'js/feedlane-admin.min.asset.php';
        $asset = file_exists( $asset_file ) ? include $asset_file : [ 'dependencies' => [ 'react', 'react-dom' ], 'version' => FEEDLANE_VERSION ];

        // Enqueue admin scripts with proper dependencies and version
        $this->assets->enqueue( 'feedlane-admin', 'js/feedlane-admin.min.js', $asset['dependencies'], true, $asset['version'] );
        $this->assets->enqueue( 'feedlane-admin-css', 'css/feedlane-admin.min.css' );

        $this->assets->localize(
            'feedlane-admin',
            'feedlaneAdmin',
            [
                'rest_url' => esc_url_raw( rest_url() ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'ajax_url' => admin_url( 'admin-ajax.php' ),
            ]
        );
    }



    /**
     * Initialize Elementor widget
     */
    public function init_elementor_widget( $widgets_manager ) {
        if ( class_exists( '\Elementor\Plugin' ) ) {
            $widgets_manager->register( new ElementorWidget() );
        }
    }

    /**
     * Initialize Gutenberg block
     */
    public function init_gutenberg_block() {
        new GutenbergBlock();
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only load if floating sidebar is enabled
        $enable_floating = get_option( 'feedlane_enable_floating_sidebar', true );

        if ( ! $enable_floating ) {
            return;
        }

        // Enqueue sidebar assets
        $this->assets->enqueue( 'feedlane-sidebar', 'js/feedlane-sidebar.min.js', [ 'wp-element' ] );
        $this->assets->enqueue( 'feedlane-sidebar-css', 'css/feedlane-sidebar.min.css' );

        // Localize script data
        $this->assets->localize(
            'feedlane-sidebar',
            'feedlaneData',
            [
                'rest_url' => esc_url_raw( rest_url() ),
                'nonce' => wp_create_nonce( 'wp_rest' ),
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'siteName' => get_bloginfo( 'name' ),
                'settings' => [
                    'enable_newsfeed' => get_option( 'feedlane_enable_newsfeed', true ),
                    'enable_ideas' => get_option( 'feedlane_enable_ideas', true ),
                    'enable_roadmap' => get_option( 'feedlane_enable_roadmap', true ),
                    'enable_guest_submissions' => get_option( 'feedlane_enable_guest_submissions', true ),
                    'sidebar_position' => get_option( 'feedlane_sidebar_position', 'right' ),
                    'primary_color' => get_option( 'feedlane_primary_color', '#4F46E5' ),
                ],
                'categories' => $this->get_categories(),
                'user_id' => get_current_user_id(),
                'is_user_logged_in' => is_user_logged_in(),
                'currentUser' => is_user_logged_in() ? [
                    'id' => get_current_user_id(),
                    'name' => wp_get_current_user()->display_name,
                    'email' => wp_get_current_user()->user_email,
                ] : null,
            ]
        );
    }

    /**
     * Add floating sidebar to footer
     */
    public function add_floating_sidebar() {
        // Only add if floating sidebar is enabled
        $enable_floating = get_option( 'feedlane_enable_floating_sidebar', true );

        if ( ! $enable_floating ) {
            return;
        }

        // Check if any tabs are enabled
        $enable_newsfeed = get_option( 'feedlane_enable_newsfeed', true );
        $enable_ideas = get_option( 'feedlane_enable_ideas', true );
        $enable_roadmap = get_option( 'feedlane_enable_roadmap', true );

        if ( ! $enable_newsfeed && ! $enable_ideas && ! $enable_roadmap ) {
            return;
        }

        echo '<div id="feedlane-floating-sidebar-root"></div>';
    }

    /**
     * Register post types
     */
    private function register_post_types() {


        // Register feedlane_posts
        register_post_type( 'feedlane_posts', [
            'labels' => [
                'name' => __( 'Newsfeed Posts', 'feedlane' ),
                'singular_name' => __( 'Newsfeed Post', 'feedlane' ),
                'add_new' => __( 'Add New', 'feedlane' ),
                'add_new_item' => __( 'Add New Post', 'feedlane' ),
                'edit_item' => __( 'Edit Post', 'feedlane' ),
                'new_item' => __( 'New Post', 'feedlane' ),
                'view_item' => __( 'View Post', 'feedlane' ),
                'search_items' => __( 'Search Posts', 'feedlane' ),
                'not_found' => __( 'No posts found', 'feedlane' ),
                'not_found_in_trash' => __( 'No posts found in Trash', 'feedlane' ),
                'menu_name' => __( 'Newsfeed', 'feedlane' ),
            ],
            'public' => true,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => 'feedlane',
            'menu_position' => 25,
            'menu_icon' => 'dashicons-megaphone',
            'supports' => [ 'title', 'editor', 'thumbnail', 'excerpt' ],
            'has_archive' => false,
            'rewrite' => false,
            'show_in_rest' => true,
            'rest_base' => 'feedlane-posts',
        ] );

        // Register feedlane_ideas
        register_post_type( 'feedlane_ideas', [
            'labels' => [
                'name' => __( 'Ideas', 'feedlane' ),
                'singular_name' => __( 'Idea', 'feedlane' ),
                'add_new' => __( 'Add New', 'feedlane' ),
                'add_new_item' => __( 'Add New Idea', 'feedlane' ),
                'edit_item' => __( 'Edit Idea', 'feedlane' ),
                'new_item' => __( 'New Idea', 'feedlane' ),
                'view_item' => __( 'View Idea', 'feedlane' ),
                'search_items' => __( 'Search Ideas', 'feedlane' ),
                'not_found' => __( 'No ideas found', 'feedlane' ),
                'not_found_in_trash' => __( 'No ideas found in Trash', 'feedlane' ),
                'menu_name' => __( 'Ideas', 'feedlane' ),
            ],
            'public' => true,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => 'feedlane',
            'menu_position' => 26,
            'menu_icon' => 'dashicons-lightbulb',
            'supports' => [ 'title', 'editor', 'thumbnail', 'comments' ],
            'has_archive' => false,
            'rewrite' => false,
            'show_in_rest' => true,
            'rest_base' => 'feedlane-ideas',
        ] );


    }

    /**
     * Register taxonomies
     */
    private function register_taxonomies() {


        // Register idea_category taxonomy
        register_taxonomy( 'idea_category', [ 'feedlane_ideas' ], [
            'labels' => [
                'name' => __( 'Idea Categories', 'feedlane' ),
                'singular_name' => __( 'Idea Category', 'feedlane' ),
                'menu_name' => __( 'Categories', 'feedlane' ),
                'all_items' => __( 'All Categories', 'feedlane' ),
                'edit_item' => __( 'Edit Category', 'feedlane' ),
                'view_item' => __( 'View Category', 'feedlane' ),
                'update_item' => __( 'Update Category', 'feedlane' ),
                'add_new_item' => __( 'Add New Category', 'feedlane' ),
                'new_item_name' => __( 'New Category Name', 'feedlane' ),
                'search_items' => __( 'Search Categories', 'feedlane' ),
                'not_found' => __( 'No categories found', 'feedlane' ),
            ],
            'hierarchical' => true,
            'public' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => false,
            'show_tagcloud' => false,
            'show_in_rest' => true,
            'rest_base' => 'idea-categories',
        ] );

        // Register roadmap_status taxonomy
        register_taxonomy( 'roadmap_status', [ 'feedlane_ideas' ], [
            'labels' => [
                'name' => __( 'Roadmap Status', 'feedlane' ),
                'singular_name' => __( 'Status', 'feedlane' ),
                'menu_name' => __( 'Roadmap Status', 'feedlane' ),
                'all_items' => __( 'All Status', 'feedlane' ),
                'edit_item' => __( 'Edit Status', 'feedlane' ),
                'view_item' => __( 'View Status', 'feedlane' ),
                'update_item' => __( 'Update Status', 'feedlane' ),
                'add_new_item' => __( 'Add New Status', 'feedlane' ),
                'new_item_name' => __( 'New Status Name', 'feedlane' ),
                'search_items' => __( 'Search Status', 'feedlane' ),
                'not_found' => __( 'No status found', 'feedlane' ),
            ],
            'hierarchical' => false,
            'public' => false,
            'show_ui' => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => false,
            'show_tagcloud' => false,
            'show_in_rest' => true,
            'rest_base' => 'roadmap-status',
        ] );

        // Add default terms
        $this->add_default_terms();


    }

    /**
     * Add default taxonomy terms
     */
    private function add_default_terms() {
        // Add default idea categories
        $categories = [
            'improvement' => __( 'Improvement', 'feedlane' ),
            'feature-request' => __( 'Feature Request', 'feedlane' ),
            'bug' => __( 'Bug', 'feedlane' ),
            'changelog' => __( 'Changelog', 'feedlane' ),
            'feedback' => __( 'Feedback', 'feedlane' ),
        ];

        foreach ( $categories as $slug => $name ) {
            if ( ! term_exists( $slug, 'idea_category' ) ) {
                wp_insert_term( $name, 'idea_category', [
                    'slug' => $slug,
                ] );
            }
        }

        // Add default roadmap status
        $status = [
            'under-review' => __( 'Under Review', 'feedlane' ),
            'planned' => __( 'Planned', 'feedlane' ),
            'in-progress' => __( 'In Progress', 'feedlane' ),
            'completed' => __( 'Completed', 'feedlane' ),
        ];

        foreach ( $status as $slug => $name ) {
            if ( ! term_exists( $slug, 'roadmap_status' ) ) {
                wp_insert_term( $name, 'roadmap_status', [
                    'slug' => $slug,
                ] );
            }
        }
    }

    /**
     * Get frontend settings
     *
     * @return array
     */
    private function get_frontend_settings() {
        return [
            'enable_newsfeed' => get_option( 'feedlane_enable_newsfeed', true ),
            'enable_ideas' => get_option( 'feedlane_enable_ideas', true ),
            'enable_roadmap' => get_option( 'feedlane_enable_roadmap', true ),
            'enable_guest_submissions' => get_option( 'feedlane_enable_guest_submissions', true ),
            'sidebar_position' => get_option( 'feedlane_sidebar_position', 'right' ),
            'primary_color' => get_option( 'feedlane_primary_color', '#4F46E5' ),
        ];
    }

    /**
     * Get categories for frontend
     *
     * @return array
     */
    private function get_categories() {
        $terms = get_terms( [
            'taxonomy' => 'idea_category',
            'hide_empty' => false,
        ] );

        if ( is_wp_error( $terms ) ) {
            return [];
        }

        return array_map( function( $term ) {
            return [
                'id' => $term->term_id,
                'name' => $term->name,
                'slug' => $term->slug,
                'color' => get_term_meta( $term->term_id, 'color', true ) ?: '#10B981',
            ];
        }, $terms );
    }
}
