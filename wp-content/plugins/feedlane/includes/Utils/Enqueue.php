<?php

namespace WPDeveloper\Feedlane\Utils;

/**
 * Asset Enqueue Class
 */
class Enqueue {

    /**
     * Enqueue script or style
     *
     * @param string $handle
     * @param string $file_path
     * @param array $dependencies
     * @param bool $in_footer
     * @param string|null $version
     */
    public function enqueue( $handle, $file_path, $dependencies = [], $in_footer = true, $version = null ) {
        $file_extension = pathinfo( $file_path, PATHINFO_EXTENSION );
        $file_url = FEEDLANE_ASSETS_DIR_URL . $file_path;
        $file_full_path = FEEDLANE_ASSETS_DIR_PATH . $file_path;

        // Use provided version or fall back to file modification time
        if ( $version === null ) {
            $version = file_exists( $file_full_path ) ? filemtime( $file_full_path ) : FEEDLANE_VERSION;
        }

        if ( $file_extension === 'js' ) {
            wp_enqueue_script( $handle, $file_url, $dependencies, $version, $in_footer );
        } elseif ( $file_extension === 'css' ) {
            wp_enqueue_style( $handle, $file_url, $dependencies, $version );
        }
    }

    /**
     * Localize script data
     *
     * @param string $handle
     * @param string $object_name
     * @param array $data
     */
    public function localize( $handle, $object_name, $data ) {
        wp_localize_script( $handle, $object_name, $data );
    }

    /**
     * Register script or style
     *
     * @param string $handle
     * @param string $file_path
     * @param array $dependencies
     * @param bool $in_footer
     */
    public function register( $handle, $file_path, $dependencies = [], $in_footer = true ) {
        $file_extension = pathinfo( $file_path, PATHINFO_EXTENSION );
        $file_url = FEEDLANE_ASSETS_DIR_URL . $file_path;
        $file_full_path = FEEDLANE_ASSETS_DIR_PATH . $file_path;

        // Get file version based on modification time
        $version = file_exists( $file_full_path ) ? filemtime( $file_full_path ) : FEEDLANE_VERSION;

        if ( $file_extension === 'js' ) {
            wp_register_script( $handle, $file_url, $dependencies, $version, $in_footer );
        } elseif ( $file_extension === 'css' ) {
            wp_register_style( $handle, $file_url, $dependencies, $version );
        }
    }

    /**
     * Dequeue script or style
     *
     * @param string $handle
     * @param string $type
     */
    public function dequeue( $handle, $type = 'script' ) {
        if ( $type === 'script' ) {
            wp_dequeue_script( $handle );
        } elseif ( $type === 'style' ) {
            wp_dequeue_style( $handle );
        }
    }

    /**
     * Check if script or style is enqueued
     *
     * @param string $handle
     * @param string $type
     * @return bool
     */
    public function is_enqueued( $handle, $type = 'script' ) {
        if ( $type === 'script' ) {
            return wp_script_is( $handle, 'enqueued' );
        } elseif ( $type === 'style' ) {
            return wp_style_is( $handle, 'enqueued' );
        }

        return false;
    }

    /**
     * Add inline script
     *
     * @param string $handle
     * @param string $data
     * @param string $position
     */
    public function add_inline_script( $handle, $data, $position = 'after' ) {
        wp_add_inline_script( $handle, $data, $position );
    }

    /**
     * Add inline style
     *
     * @param string $handle
     * @param string $data
     */
    public function add_inline_style( $handle, $data ) {
        wp_add_inline_style( $handle, $data );
    }
}
