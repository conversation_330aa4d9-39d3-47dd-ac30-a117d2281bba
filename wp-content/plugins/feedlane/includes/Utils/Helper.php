<?php

namespace WPDeveloper\Feedlane\Utils;

/**
 * Helper Utility Class
 */
class Helper {
    
    /**
     * Sanitize text input
     *
     * @param string $input
     * @return string
     */
    public static function sanitize_text( $input ) {
        return sanitize_text_field( wp_unslash( $input ) );
    }
    
    /**
     * Sanitize textarea input
     *
     * @param string $input
     * @return string
     */
    public static function sanitize_textarea( $input ) {
        return sanitize_textarea_field( wp_unslash( $input ) );
    }
    
    /**
     * Sanitize email input
     *
     * @param string $input
     * @return string
     */
    public static function sanitize_email( $input ) {
        return sanitize_email( wp_unslash( $input ) );
    }
    
    /**
     * Sanitize URL input
     *
     * @param string $input
     * @return string
     */
    public static function sanitize_url( $input ) {
        return esc_url_raw( wp_unslash( $input ) );
    }
    
    /**
     * Get user IP address
     *
     * @return string
     */
    public static function get_user_ip() {
        $ip_keys = [ 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR' ];
        
        foreach ( $ip_keys as $key ) {
            if ( array_key_exists( $key, $_SERVER ) === true ) {
                foreach ( explode( ',', $_SERVER[ $key ] ) as $ip ) {
                    $ip = trim( $ip );
                    
                    if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) !== false ) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset( $_SERVER['REMOTE_ADDR'] ) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Generate unique session ID
     *
     * @return string
     */
    public static function generate_session_id() {
        return 'feedlane_' . wp_generate_uuid4();
    }
    
    /**
     * Check if user can submit feedback
     *
     * @return bool
     */
    public static function can_submit_feedback() {
        // Allow if guest submissions are enabled
        if ( get_option( 'feedlane_enable_guest_submissions', true ) ) {
            return true;
        }
        
        // Otherwise, user must be logged in
        return is_user_logged_in();
    }
    
    /**
     * Check if user can vote
     *
     * @return bool
     */
    public static function can_vote() {
        // Allow if guest submissions are enabled
        if ( get_option( 'feedlane_enable_guest_submissions', true ) ) {
            return true;
        }
        
        // Otherwise, user must be logged in
        return is_user_logged_in();
    }
    
    /**
     * Get allowed reaction types
     *
     * @return array
     */
    public static function get_reaction_types() {
        return apply_filters( 'feedlane_reaction_types', [
            'like' => '👍',
            'neutral' => '😐',
            'dislike' => '😡',
        ] );
    }
    
    /**
     * Get allowed idea categories
     *
     * @return array
     */
    public static function get_idea_categories() {
        $terms = get_terms( [
            'taxonomy' => 'idea_category',
            'hide_empty' => false,
        ] );
        
        $categories = [];
        if ( ! is_wp_error( $terms ) ) {
            foreach ( $terms as $term ) {
                $categories[ $term->slug ] = $term->name;
            }
        }
        
        return $categories;
    }
    
    /**
     * Get roadmap status
     *
     * @return array
     */
    public static function get_roadmap_status() {
        $terms = get_terms( [
            'taxonomy' => 'roadmap_status',
            'hide_empty' => false,
        ] );
        
        $status = [];
        if ( ! is_wp_error( $terms ) ) {
            foreach ( $terms as $term ) {
                $status[ $term->slug ] = $term->name;
            }
        }
        
        return $status;
    }
    
    /**
     * Format time ago
     *
     * @param string $datetime
     * @return string
     */
    public static function time_ago( $datetime ) {
        $time = time() - strtotime( $datetime );
        
        if ( $time < 60 ) {
            return __( 'just now', 'feedlane' );
        } elseif ( $time < 3600 ) {
            $minutes = floor( $time / 60 );
            return sprintf( _n( '%d minute ago', '%d minutes ago', $minutes, 'feedlane' ), $minutes );
        } elseif ( $time < 86400 ) {
            $hours = floor( $time / 3600 );
            return sprintf( _n( '%d hour ago', '%d hours ago', $hours, 'feedlane' ), $hours );
        } elseif ( $time < 2592000 ) {
            $days = floor( $time / 86400 );
            return sprintf( _n( '%d day ago', '%d days ago', $days, 'feedlane' ), $days );
        } else {
            return date( 'M j, Y', strtotime( $datetime ) );
        }
    }
    
    /**
     * Truncate text
     *
     * @param string $text
     * @param int $length
     * @param string $suffix
     * @return string
     */
    public static function truncate( $text, $length = 100, $suffix = '...' ) {
        if ( strlen( $text ) <= $length ) {
            return $text;
        }
        
        return substr( $text, 0, $length ) . $suffix;
    }
    
    /**
     * Check nonce
     *
     * @param string $nonce
     * @param string $action
     * @return bool
     */
    public static function verify_nonce( $nonce, $action = 'wp_rest' ) {
        return wp_verify_nonce( $nonce, $action );
    }
    
    /**
     * Check user capabilities
     *
     * @param string $capability
     * @return bool
     */
    public static function current_user_can( $capability ) {
        return current_user_can( $capability );
    }
}
