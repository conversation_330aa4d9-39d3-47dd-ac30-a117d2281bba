<?php

/**
 * Plugin Name:       Feedlane
 * Plugin URI:        https://feedlane.com/
 * Description:       A React-powered WordPress plugin that adds a floating feedback sidebar with Newsfeed, Ideas, and Roadmap tabs—similar to GetBeamer. It supports guest submissions, real-time comments via Firebase, and admin moderation.
 * Version:           1.0.0
 * Author:            Feedlane Team
 * Author URI:        https://feedlane.com
 * License:           GPL-3.0+
 * License URI:       http://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       feedlane
 * Domain Path:       /languages
 * Requires at least: 5.0
 * Tested up to:      6.4
 * Requires PHP:      7.4
 */

// If this file is called directly, abort.
use WPDeveloper\Feedlane\Plugin;

defined( 'ABSPATH' ) || exit;

// Plugin constants
define( 'FEEDLANE_FILE', __FILE__ );
define( 'FEEDLANE_VERSION', '1.0.0' );
define( 'FEEDLANE_ROOT_DIR_PATH', plugin_dir_path( __FILE__ ) );
define( 'FEEDLANE_ROOT_DIR_URL', plugin_dir_url( __FILE__ ) );
define( 'FEEDLANE_ASSETS_DIR_PATH', FEEDLANE_ROOT_DIR_PATH . 'assets/' );
define( 'FEEDLANE_ASSETS_DIR_URL', FEEDLANE_ROOT_DIR_URL . 'assets/' );
define( 'FEEDLANE_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

// Load composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

/**
 * Main plugin instance
 *
 * @return Plugin
 */
function feedlane() {
    return Plugin::get_instance();
}

// Initialize the plugin
feedlane();
