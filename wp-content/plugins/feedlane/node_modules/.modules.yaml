hoistPattern:
  - '*'
hoistedDependencies:
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.27.5:
    '@babel/compat-data': private
  /@babel/core/7.27.4:
    '@babel/core': private
  /@babel/eslint-parser/7.27.5(@babel/core@7.27.4)(eslint@8.57.1):
    '@babel/eslint-parser': public
  /@babel/generator/7.27.5:
    '@babel/generator': private
  /@babel/helper-annotate-as-pure/7.27.3:
    '@babel/helper-annotate-as-pure': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-create-class-features-plugin/7.27.1(@babel/core@7.27.4):
    '@babel/helper-create-class-features-plugin': private
  /@babel/helper-create-regexp-features-plugin/7.27.1(@babel/core@7.27.4):
    '@babel/helper-create-regexp-features-plugin': private
  /@babel/helper-define-polyfill-provider/0.6.4(@babel/core@7.27.4):
    '@babel/helper-define-polyfill-provider': private
  /@babel/helper-member-expression-to-functions/7.27.1:
    '@babel/helper-member-expression-to-functions': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.27.4):
    '@babel/helper-module-transforms': private
  /@babel/helper-optimise-call-expression/7.27.1:
    '@babel/helper-optimise-call-expression': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-remap-async-to-generator/7.27.1(@babel/core@7.27.4):
    '@babel/helper-remap-async-to-generator': private
  /@babel/helper-replace-supers/7.27.1(@babel/core@7.27.4):
    '@babel/helper-replace-supers': private
  /@babel/helper-skip-transparent-expression-wrappers/7.27.1:
    '@babel/helper-skip-transparent-expression-wrappers': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helper-wrap-function/7.27.1:
    '@babel/helper-wrap-function': private
  /@babel/helpers/7.27.4:
    '@babel/helpers': private
  /@babel/parser/7.27.5:
    '@babel/parser': private
  /@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  /@babel/plugin-bugfix-safari-class-field-initializer-scope/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  /@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  /@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  /@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  /@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.4):
    '@babel/plugin-proposal-private-property-in-object': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.27.4):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.27.4):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.27.4):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-import-assertions/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-syntax-import-assertions': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.27.4):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.27.4):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.27.4):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.27.4):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.27.4):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.27.4):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-syntax-unicode-sets-regex/7.18.6(@babel/core@7.27.4):
    '@babel/plugin-syntax-unicode-sets-regex': private
  /@babel/plugin-transform-arrow-functions/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-arrow-functions': private
  /@babel/plugin-transform-async-generator-functions/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-async-generator-functions': private
  /@babel/plugin-transform-async-to-generator/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-async-to-generator': private
  /@babel/plugin-transform-block-scoped-functions/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-block-scoped-functions': private
  /@babel/plugin-transform-block-scoping/7.27.5(@babel/core@7.27.4):
    '@babel/plugin-transform-block-scoping': private
  /@babel/plugin-transform-class-properties/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-class-properties': private
  /@babel/plugin-transform-class-static-block/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-class-static-block': private
  /@babel/plugin-transform-classes/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-classes': private
  /@babel/plugin-transform-computed-properties/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-computed-properties': private
  /@babel/plugin-transform-destructuring/7.27.3(@babel/core@7.27.4):
    '@babel/plugin-transform-destructuring': private
  /@babel/plugin-transform-dotall-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-dotall-regex': private
  /@babel/plugin-transform-duplicate-keys/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-duplicate-keys': private
  /@babel/plugin-transform-duplicate-named-capturing-groups-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  /@babel/plugin-transform-dynamic-import/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-dynamic-import': private
  /@babel/plugin-transform-exponentiation-operator/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-exponentiation-operator': private
  /@babel/plugin-transform-export-namespace-from/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-export-namespace-from': private
  /@babel/plugin-transform-for-of/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-for-of': private
  /@babel/plugin-transform-function-name/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-function-name': private
  /@babel/plugin-transform-json-strings/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-json-strings': private
  /@babel/plugin-transform-literals/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-literals': private
  /@babel/plugin-transform-logical-assignment-operators/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-logical-assignment-operators': private
  /@babel/plugin-transform-member-expression-literals/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-member-expression-literals': private
  /@babel/plugin-transform-modules-amd/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-modules-amd': private
  /@babel/plugin-transform-modules-commonjs/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-modules-commonjs': private
  /@babel/plugin-transform-modules-systemjs/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-modules-systemjs': private
  /@babel/plugin-transform-modules-umd/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-modules-umd': private
  /@babel/plugin-transform-named-capturing-groups-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-named-capturing-groups-regex': private
  /@babel/plugin-transform-new-target/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-new-target': private
  /@babel/plugin-transform-nullish-coalescing-operator/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-nullish-coalescing-operator': private
  /@babel/plugin-transform-numeric-separator/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-numeric-separator': private
  /@babel/plugin-transform-object-rest-spread/7.27.3(@babel/core@7.27.4):
    '@babel/plugin-transform-object-rest-spread': private
  /@babel/plugin-transform-object-super/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-object-super': private
  /@babel/plugin-transform-optional-catch-binding/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-optional-catch-binding': private
  /@babel/plugin-transform-optional-chaining/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-optional-chaining': private
  /@babel/plugin-transform-parameters/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-parameters': private
  /@babel/plugin-transform-private-methods/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-private-methods': private
  /@babel/plugin-transform-private-property-in-object/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-private-property-in-object': private
  /@babel/plugin-transform-property-literals/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-property-literals': private
  /@babel/plugin-transform-react-constant-elements/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-react-constant-elements': private
  /@babel/plugin-transform-react-display-name/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-react-display-name': private
  /@babel/plugin-transform-react-jsx-development/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-react-jsx-development': private
  /@babel/plugin-transform-react-jsx/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-react-jsx': private
  /@babel/plugin-transform-react-pure-annotations/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-react-pure-annotations': private
  /@babel/plugin-transform-regenerator/7.27.5(@babel/core@7.27.4):
    '@babel/plugin-transform-regenerator': private
  /@babel/plugin-transform-regexp-modifiers/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-regexp-modifiers': private
  /@babel/plugin-transform-reserved-words/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-reserved-words': private
  /@babel/plugin-transform-runtime/7.27.4(@babel/core@7.27.4):
    '@babel/plugin-transform-runtime': private
  /@babel/plugin-transform-shorthand-properties/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-shorthand-properties': private
  /@babel/plugin-transform-spread/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-spread': private
  /@babel/plugin-transform-sticky-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-sticky-regex': private
  /@babel/plugin-transform-template-literals/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-template-literals': private
  /@babel/plugin-transform-typeof-symbol/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-typeof-symbol': private
  /@babel/plugin-transform-typescript/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-typescript': private
  /@babel/plugin-transform-unicode-escapes/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-unicode-escapes': private
  /@babel/plugin-transform-unicode-property-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-unicode-property-regex': private
  /@babel/plugin-transform-unicode-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-unicode-regex': private
  /@babel/plugin-transform-unicode-sets-regex/7.27.1(@babel/core@7.27.4):
    '@babel/plugin-transform-unicode-sets-regex': private
  /@babel/preset-env/7.27.2(@babel/core@7.27.4):
    '@babel/preset-env': private
  /@babel/preset-modules/0.1.6-no-external-plugins(@babel/core@7.27.4):
    '@babel/preset-modules': private
  /@babel/preset-react/7.27.1(@babel/core@7.27.4):
    '@babel/preset-react': private
  /@babel/preset-typescript/7.27.1(@babel/core@7.27.4):
    '@babel/preset-typescript': private
  /@babel/runtime/7.27.4:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.27.4:
    '@babel/traverse': private
  /@babel/types/7.27.3:
    '@babel/types': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@csstools/selector-specificity/2.2.0(postcss-selector-parser@6.1.2):
    '@csstools/selector-specificity': private
  /@discoveryjs/json-ext/0.5.7:
    '@discoveryjs/json-ext': private
  /@dnd-kit/accessibility/3.1.1(react@18.3.1):
    '@dnd-kit/accessibility': private
  /@es-joy/jsdoccomment/0.36.1:
    '@es-joy/jsdoccomment': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@firebase/analytics-compat/0.2.14(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/analytics-compat': private
  /@firebase/analytics-types/0.8.2:
    '@firebase/analytics-types': private
  /@firebase/analytics/0.10.8(@firebase/app@0.10.13):
    '@firebase/analytics': private
  /@firebase/app-check-compat/0.3.15(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/app-check-compat': private
  /@firebase/app-check-interop-types/0.3.2:
    '@firebase/app-check-interop-types': private
  /@firebase/app-check-types/0.5.2:
    '@firebase/app-check-types': private
  /@firebase/app-check/0.8.8(@firebase/app@0.10.13):
    '@firebase/app-check': private
  /@firebase/app-compat/0.2.43:
    '@firebase/app-compat': private
  /@firebase/app-types/0.9.2:
    '@firebase/app-types': private
  /@firebase/app/0.10.13:
    '@firebase/app': private
  /@firebase/auth-compat/0.5.14(@firebase/app-compat@0.2.43)(@firebase/app-types@0.9.2)(@firebase/app@0.10.13):
    '@firebase/auth-compat': private
  /@firebase/auth-interop-types/0.2.3:
    '@firebase/auth-interop-types': private
  /@firebase/auth-types/0.12.2(@firebase/app-types@0.9.2)(@firebase/util@1.10.0):
    '@firebase/auth-types': private
  /@firebase/auth/1.7.9(@firebase/app@0.10.13):
    '@firebase/auth': private
  /@firebase/component/0.6.9:
    '@firebase/component': private
  /@firebase/data-connect/0.1.0(@firebase/app@0.10.13):
    '@firebase/data-connect': private
  /@firebase/database-compat/1.0.8:
    '@firebase/database-compat': private
  /@firebase/database-types/1.0.5:
    '@firebase/database-types': private
  /@firebase/database/1.0.8:
    '@firebase/database': private
  /@firebase/firestore-compat/0.3.38(@firebase/app-compat@0.2.43)(@firebase/app-types@0.9.2)(@firebase/app@0.10.13):
    '@firebase/firestore-compat': private
  /@firebase/firestore-types/3.0.2(@firebase/app-types@0.9.2)(@firebase/util@1.10.0):
    '@firebase/firestore-types': private
  /@firebase/firestore/4.7.3(@firebase/app@0.10.13):
    '@firebase/firestore': private
  /@firebase/functions-compat/0.3.14(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/functions-compat': private
  /@firebase/functions-types/0.6.2:
    '@firebase/functions-types': private
  /@firebase/functions/0.11.8(@firebase/app@0.10.13):
    '@firebase/functions': private
  /@firebase/installations-compat/0.2.9(@firebase/app-compat@0.2.43)(@firebase/app-types@0.9.2)(@firebase/app@0.10.13):
    '@firebase/installations-compat': private
  /@firebase/installations-types/0.5.2(@firebase/app-types@0.9.2):
    '@firebase/installations-types': private
  /@firebase/installations/0.6.9(@firebase/app@0.10.13):
    '@firebase/installations': private
  /@firebase/logger/0.4.2:
    '@firebase/logger': private
  /@firebase/messaging-compat/0.2.12(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/messaging-compat': private
  /@firebase/messaging-interop-types/0.2.2:
    '@firebase/messaging-interop-types': private
  /@firebase/messaging/0.12.12(@firebase/app@0.10.13):
    '@firebase/messaging': private
  /@firebase/performance-compat/0.2.9(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/performance-compat': private
  /@firebase/performance-types/0.2.2:
    '@firebase/performance-types': private
  /@firebase/performance/0.6.9(@firebase/app@0.10.13):
    '@firebase/performance': private
  /@firebase/remote-config-compat/0.2.9(@firebase/app-compat@0.2.43)(@firebase/app@0.10.13):
    '@firebase/remote-config-compat': private
  /@firebase/remote-config-types/0.3.2:
    '@firebase/remote-config-types': private
  /@firebase/remote-config/0.4.9(@firebase/app@0.10.13):
    '@firebase/remote-config': private
  /@firebase/storage-compat/0.3.12(@firebase/app-compat@0.2.43)(@firebase/app-types@0.9.2)(@firebase/app@0.10.13):
    '@firebase/storage-compat': private
  /@firebase/storage-types/0.8.2(@firebase/app-types@0.9.2)(@firebase/util@1.10.0):
    '@firebase/storage-types': private
  /@firebase/storage/0.13.2(@firebase/app@0.10.13):
    '@firebase/storage': private
  /@firebase/util/1.10.0:
    '@firebase/util': private
  /@firebase/vertexai-preview/0.0.4(@firebase/app-types@0.9.2)(@firebase/app@0.10.13):
    '@firebase/vertexai-preview': private
  /@firebase/webchannel-wrapper/1.0.1:
    '@firebase/webchannel-wrapper': private
  /@grpc/grpc-js/1.9.15:
    '@grpc/grpc-js': private
  /@grpc/proto-loader/0.7.15:
    '@grpc/proto-loader': private
  /@hapi/hoek/9.3.0:
    '@hapi/hoek': private
  /@hapi/topo/5.1.0:
    '@hapi/topo': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0:
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.8:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/set-array/1.2.1:
    '@jridgewell/set-array': private
  /@jridgewell/source-map/0.3.6:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.0:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.25:
    '@jridgewell/trace-mapping': private
  /@leichtgewicht/ip-codec/2.0.5:
    '@leichtgewicht/ip-codec': private
  /@nicolo-ribaudo/eslint-scope-5-internals/5.1.1-v1:
    '@nicolo-ribaudo/eslint-scope-5-internals': public
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@parcel/watcher-android-arm64/2.5.1:
    '@parcel/watcher-android-arm64': private
  /@parcel/watcher-darwin-arm64/2.5.1:
    '@parcel/watcher-darwin-arm64': private
  /@parcel/watcher-darwin-x64/2.5.1:
    '@parcel/watcher-darwin-x64': private
  /@parcel/watcher-freebsd-x64/2.5.1:
    '@parcel/watcher-freebsd-x64': private
  /@parcel/watcher-linux-arm-glibc/2.5.1:
    '@parcel/watcher-linux-arm-glibc': private
  /@parcel/watcher-linux-arm-musl/2.5.1:
    '@parcel/watcher-linux-arm-musl': private
  /@parcel/watcher-linux-arm64-glibc/2.5.1:
    '@parcel/watcher-linux-arm64-glibc': private
  /@parcel/watcher-linux-arm64-musl/2.5.1:
    '@parcel/watcher-linux-arm64-musl': private
  /@parcel/watcher-linux-x64-glibc/2.5.1:
    '@parcel/watcher-linux-x64-glibc': private
  /@parcel/watcher-linux-x64-musl/2.5.1:
    '@parcel/watcher-linux-x64-musl': private
  /@parcel/watcher-win32-arm64/2.5.1:
    '@parcel/watcher-win32-arm64': private
  /@parcel/watcher-win32-ia32/2.5.1:
    '@parcel/watcher-win32-ia32': private
  /@parcel/watcher-win32-x64/2.5.1:
    '@parcel/watcher-win32-x64': private
  /@parcel/watcher/2.5.1:
    '@parcel/watcher': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pmmmwh/react-refresh-webpack-plugin/0.5.16(react-refresh@0.10.0)(webpack-dev-server@4.15.2)(webpack@5.99.9):
    '@pmmmwh/react-refresh-webpack-plugin': private
  /@polka/url/1.0.0-next.29:
    '@polka/url': private
  /@protobufjs/aspromise/1.1.2:
    '@protobufjs/aspromise': private
  /@protobufjs/base64/1.1.2:
    '@protobufjs/base64': private
  /@protobufjs/codegen/2.0.4:
    '@protobufjs/codegen': private
  /@protobufjs/eventemitter/1.1.0:
    '@protobufjs/eventemitter': private
  /@protobufjs/fetch/1.1.0:
    '@protobufjs/fetch': private
  /@protobufjs/float/1.0.2:
    '@protobufjs/float': private
  /@protobufjs/inquire/1.1.0:
    '@protobufjs/inquire': private
  /@protobufjs/path/1.1.2:
    '@protobufjs/path': private
  /@protobufjs/pool/1.1.0:
    '@protobufjs/pool': private
  /@protobufjs/utf8/1.1.0:
    '@protobufjs/utf8': private
  /@remix-run/router/1.23.0:
    '@remix-run/router': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@sideway/address/4.1.5:
    '@sideway/address': private
  /@sideway/formula/3.0.1:
    '@sideway/formula': private
  /@sideway/pinpoint/2.0.0:
    '@sideway/pinpoint': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@svgr/babel-plugin-add-jsx-attribute/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-add-jsx-attribute': private
  /@svgr/babel-plugin-remove-jsx-attribute/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-remove-jsx-attribute': private
  /@svgr/babel-plugin-remove-jsx-empty-expression/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  /@svgr/babel-plugin-replace-jsx-attribute-value/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  /@svgr/babel-plugin-svg-dynamic-title/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-svg-dynamic-title': private
  /@svgr/babel-plugin-svg-em-dimensions/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-svg-em-dimensions': private
  /@svgr/babel-plugin-transform-react-native-svg/8.1.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-transform-react-native-svg': private
  /@svgr/babel-plugin-transform-svg-component/8.0.0(@babel/core@7.27.4):
    '@svgr/babel-plugin-transform-svg-component': private
  /@svgr/babel-preset/8.1.0(@babel/core@7.27.4):
    '@svgr/babel-preset': private
  /@svgr/core/8.1.0(typescript@5.8.3):
    '@svgr/core': private
  /@svgr/hast-util-to-babel-ast/8.0.0:
    '@svgr/hast-util-to-babel-ast': private
  /@svgr/plugin-jsx/8.1.0(@svgr/core@8.1.0):
    '@svgr/plugin-jsx': private
  /@svgr/plugin-svgo/8.1.0(@svgr/core@8.1.0)(typescript@5.8.3):
    '@svgr/plugin-svgo': private
  /@tannin/compile/1.1.0:
    '@tannin/compile': private
  /@tannin/evaluate/1.2.0:
    '@tannin/evaluate': private
  /@tannin/plural-forms/1.1.0:
    '@tannin/plural-forms': private
  /@tannin/postfix/1.1.0:
    '@tannin/postfix': private
  /@tanstack/query-core/5.80.5:
    '@tanstack/query-core': private
  /@tootallnate/once/2.0.0:
    '@tootallnate/once': private
  /@trysound/sax/0.2.0:
    '@trysound/sax': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/body-parser/1.19.5:
    '@types/body-parser': private
  /@types/bonjour/3.5.13:
    '@types/bonjour': private
  /@types/connect-history-api-fallback/1.5.4:
    '@types/connect-history-api-fallback': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  /@types/eslint/9.6.1:
    '@types/eslint': public
  /@types/estree/1.0.7:
    '@types/estree': private
  /@types/express-serve-static-core/5.0.6:
    '@types/express-serve-static-core': private
  /@types/express/4.17.22:
    '@types/express': private
  /@types/glob/7.2.0:
    '@types/glob': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/http-errors/2.0.4:
    '@types/http-errors': private
  /@types/http-proxy/1.17.16:
    '@types/http-proxy': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/jsdom/20.0.1:
    '@types/jsdom': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/minimatch/5.1.2:
    '@types/minimatch': private
  /@types/minimist/1.2.5:
    '@types/minimist': private
  /@types/node-forge/1.3.11:
    '@types/node-forge': private
  /@types/node/22.15.29:
    '@types/node': private
  /@types/normalize-package-data/2.4.4:
    '@types/normalize-package-data': private
  /@types/parse-json/4.0.2:
    '@types/parse-json': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/retry/0.12.0:
    '@types/retry': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.4:
    '@types/send': private
  /@types/serve-index/1.9.4:
    '@types/serve-index': private
  /@types/serve-static/1.15.7:
    '@types/serve-static': private
  /@types/sockjs/0.3.36:
    '@types/sockjs': private
  /@types/source-list-map/0.1.6:
    '@types/source-list-map': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/tapable/1.0.12:
    '@types/tapable': private
  /@types/tough-cookie/4.0.5:
    '@types/tough-cookie': private
  /@types/trusted-types/2.0.7:
    '@types/trusted-types': private
  /@types/uglify-js/3.17.5:
    '@types/uglify-js': private
  /@types/webpack-sources/3.2.3:
    '@types/webpack-sources': private
  /@types/webpack/4.41.40:
    '@types/webpack': private
  /@types/ws/8.18.1:
    '@types/ws': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@types/yauzl/2.10.3:
    '@types/yauzl': private
  /@typescript-eslint/eslint-plugin/5.62.0(@typescript-eslint/parser@5.62.0)(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/5.62.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/parser': public
  /@typescript-eslint/scope-manager/5.62.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/5.62.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/5.62.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/5.62.0(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/5.62.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/5.62.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  /@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  /@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  /@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  /@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  /@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  /@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  /@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  /@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  /@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  /@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  /@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  /@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  /@webpack-cli/configtest/1.2.0(webpack-cli@4.10.0)(webpack@5.99.9):
    '@webpack-cli/configtest': private
  /@webpack-cli/info/1.5.0(webpack-cli@4.10.0):
    '@webpack-cli/info': private
  /@webpack-cli/serve/1.7.0(webpack-cli@4.10.0)(webpack-dev-server@4.15.2):
    '@webpack-cli/serve': private
  /@wordpress/babel-plugin-import-jsx-pragma/4.41.0(@babel/core@7.27.4):
    '@wordpress/babel-plugin-import-jsx-pragma': private
  /@wordpress/babel-preset-default/7.42.0:
    '@wordpress/babel-preset-default': private
  /@wordpress/base-styles/4.49.0:
    '@wordpress/base-styles': private
  /@wordpress/browserslist-config/5.41.0:
    '@wordpress/browserslist-config': private
  /@wordpress/dependency-extraction-webpack-plugin/4.31.0(webpack@5.99.9):
    '@wordpress/dependency-extraction-webpack-plugin': private
  /@wordpress/eslint-plugin/14.12.0(@babel/core@7.27.4)(eslint@8.57.1)(jest@29.7.0)(typescript@5.8.3)(wp-prettier@2.8.5):
    '@wordpress/eslint-plugin': public
  /@wordpress/hooks/4.25.0:
    '@wordpress/hooks': private
  /@wordpress/jest-console/7.29.0(jest@29.7.0):
    '@wordpress/jest-console': private
  /@wordpress/jest-preset-default/11.29.0(@babel/core@7.27.4)(jest@29.7.0):
    '@wordpress/jest-preset-default': private
  /@wordpress/npm-package-json-lint-config/4.43.0(npm-package-json-lint@5.4.2):
    '@wordpress/npm-package-json-lint-config': private
  /@wordpress/postcss-plugins-preset/4.42.0(postcss@8.5.4):
    '@wordpress/postcss-plugins-preset': private
  /@wordpress/prettier-config/2.25.13(wp-prettier@2.8.5):
    '@wordpress/prettier-config': public
  /@wordpress/stylelint-config/21.41.0(postcss@8.5.4)(stylelint@14.16.1):
    '@wordpress/stylelint-config': private
  /@wordpress/url/3.59.0:
    '@wordpress/url': private
  /@wordpress/warning/2.58.0:
    '@wordpress/warning': private
  /@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  /@xtuc/long/4.2.2:
    '@xtuc/long': private
  /abab/2.0.6:
    abab: private
  /accepts/1.3.8:
    accepts: private
  /acorn-globals/7.0.1:
    acorn-globals: private
  /acorn-jsx/5.3.2(acorn@8.14.1):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.14.1:
    acorn: private
  /adm-zip/0.5.16:
    adm-zip: private
  /agent-base/6.0.2:
    agent-base: private
  /ajv-errors/1.0.1(ajv@6.12.6):
    ajv-errors: private
  /ajv-formats/2.1.1(ajv@8.17.1):
    ajv-formats: private
  /ajv-keywords/3.5.2(ajv@6.12.6):
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-html-community/0.0.8:
    ansi-html-community: private
  /ansi-html/0.0.9:
    ansi-html: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /ansis/4.0.0-node10:
    ansis: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /aria-query/5.3.2:
    aria-query: private
  /arr-union/3.1.0:
    arr-union: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-includes/3.1.9:
    array-includes: private
  /array-union/2.1.0:
    array-union: private
  /array-uniq/1.0.3:
    array-uniq: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /arrify/1.0.1:
    arrify: private
  /ast-types-flow/0.0.8:
    ast-types-flow: private
  /astral-regex/2.0.0:
    astral-regex: private
  /async-function/1.0.0:
    async-function: private
  /asynckit/0.4.0:
    asynckit: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axe-core/4.10.3:
    axe-core: private
  /axios/0.25.0:
    axios: private
  /axobject-query/4.1.0:
    axobject-query: private
  /babel-jest/29.7.0(@babel/core@7.27.4):
    babel-jest: private
  /babel-loader/8.4.1(@babel/core@7.27.4)(webpack@5.99.9):
    babel-loader: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-plugin-polyfill-corejs2/0.4.13(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs2: private
  /babel-plugin-polyfill-corejs3/0.11.1(@babel/core@7.27.4):
    babel-plugin-polyfill-corejs3: private
  /babel-plugin-polyfill-regenerator/0.6.4(@babel/core@7.27.4):
    babel-plugin-polyfill-regenerator: private
  /babel-preset-current-node-syntax/1.1.0(@babel/core@7.27.4):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.27.4):
    babel-preset-jest: private
  /balanced-match/2.0.0:
    balanced-match: private
  /base64-js/1.5.1:
    base64-js: private
  /batch/0.6.1:
    batch: private
  /big.js/5.2.2:
    big.js: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /bl/4.1.0:
    bl: private
  /body-parser/1.20.3:
    body-parser: private
  /bonjour-service/1.3.0:
    bonjour-service: private
  /boolbase/1.0.0:
    boolbase: private
  /brace-expansion/1.1.11:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.0:
    browserslist: private
  /bser/2.1.1:
    bser: private
  /buffer-crc32/0.2.13:
    buffer-crc32: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/5.7.1:
    buffer: private
  /bytes/3.1.2:
    bytes: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /camelcase-keys/6.2.2:
    camelcase-keys: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-api/3.0.0:
    caniuse-api: private
  /caniuse-lite/1.0.30001721:
    caniuse-lite: private
  /chalk/4.1.2:
    chalk: private
  /char-regex/1.0.2:
    char-regex: private
  /check-node-version/4.2.1:
    check-node-version: private
  /chokidar/3.6.0:
    chokidar: private
  /chownr/1.1.4:
    chownr: private
  /chrome-trace-event/1.0.4:
    chrome-trace-event: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /clean-webpack-plugin/3.0.0(webpack@5.99.9):
    clean-webpack-plugin: private
  /cliui/8.0.1:
    cliui: private
  /clone-deep/0.2.4:
    clone-deep: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /colord/2.9.3:
    colord: private
  /colorette/2.0.20:
    colorette: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/9.0.0:
    commander: private
  /comment-parser/1.3.1:
    comment-parser: private
  /commondir/1.0.1:
    commondir: private
  /compressible/2.0.18:
    compressible: private
  /compression/1.8.0:
    compression: private
  /concat-map/0.0.1:
    concat-map: private
  /connect-history-api-fallback/2.0.0:
    connect-history-api-fallback: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.7.1:
    cookie: private
  /core-js-compat/3.42.0:
    core-js-compat: private
  /core-js-pure/3.42.0:
    core-js-pure: private
  /core-js/3.42.0:
    core-js: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cosmiconfig/9.0.0(typescript@5.8.3):
    cosmiconfig: private
  /create-jest/29.7.0:
    create-jest: private
  /cross-fetch/3.1.5:
    cross-fetch: private
  /cross-spawn/5.1.0:
    cross-spawn: private
  /css-declaration-sorter/6.4.1(postcss@8.5.4):
    css-declaration-sorter: private
  /css-functions-list/3.2.3:
    css-functions-list: private
  /css-select/5.1.0:
    css-select: private
  /css-tree/2.3.1:
    css-tree: private
  /css-what/6.1.0:
    css-what: private
  /cssesc/3.0.0:
    cssesc: private
  /cssnano-preset-default/5.2.14(postcss@8.5.4):
    cssnano-preset-default: private
  /cssnano-utils/3.1.0(postcss@8.5.4):
    cssnano-utils: private
  /cssnano/5.1.15(postcss@8.5.4):
    cssnano: private
  /csso/5.0.5:
    csso: private
  /cssom/0.5.0:
    cssom: private
  /cssstyle/2.3.0:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /cwd/0.10.0:
    cwd: private
  /damerau-levenshtein/1.0.8:
    damerau-levenshtein: private
  /data-urls/3.0.2:
    data-urls: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /debounce/1.2.1:
    debounce: private
  /debug/4.4.1:
    debug: private
  /decamelize-keys/1.1.1:
    decamelize-keys: private
  /decamelize/1.2.0:
    decamelize: private
  /decimal.js/10.5.0:
    decimal.js: private
  /dedent/1.6.0:
    dedent: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /default-gateway/6.0.3:
    default-gateway: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-lazy-prop/2.0.0:
    define-lazy-prop: private
  /define-properties/1.2.1:
    define-properties: private
  /del/4.1.1:
    del: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-libc/1.0.3:
    detect-libc: private
  /detect-newline/3.1.0:
    detect-newline: private
  /detect-node/2.1.0:
    detect-node: private
  /devtools-protocol/0.0.981744:
    devtools-protocol: private
  /didyoumean/1.2.2:
    didyoumean: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dlv/1.1.3:
    dlv: private
  /dns-packet/5.6.1:
    dns-packet: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-serializer/2.0.0:
    dom-serializer: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domexception/4.0.0:
    domexception: private
  /domhandler/5.0.3:
    domhandler: private
  /domutils/3.2.2:
    domutils: private
  /dot-case/3.0.4:
    dot-case: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /duplexer/0.1.2:
    duplexer: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.165:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /emojis-list/3.0.0:
    emojis-list: private
  /encodeurl/2.0.0:
    encodeurl: private
  /encoding/0.1.13:
    encoding: private
  /end-of-stream/1.4.4:
    end-of-stream: private
  /enhanced-resolve/5.18.1:
    enhanced-resolve: private
  /entities/4.5.0:
    entities: private
  /env-paths/2.2.1:
    env-paths: private
  /envinfo/7.14.0:
    envinfo: private
  /error-ex/1.3.2:
    error-ex: private
  /error-stack-parser/2.1.4:
    error-stack-parser: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /escodegen/2.1.0:
    escodegen: private
  /eslint-config-prettier/8.10.0(eslint@8.57.1):
    eslint-config-prettier: public
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-module-utils/2.12.0(@typescript-eslint/parser@5.62.0)(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    eslint-module-utils: public
  /eslint-plugin-import/2.31.0(@typescript-eslint/parser@5.62.0)(eslint@8.57.1):
    eslint-plugin-import: public
  /eslint-plugin-jest/27.9.0(@typescript-eslint/eslint-plugin@5.62.0)(eslint@8.57.1)(jest@29.7.0)(typescript@5.8.3):
    eslint-plugin-jest: public
  /eslint-plugin-jsdoc/39.9.1(eslint@8.57.1):
    eslint-plugin-jsdoc: public
  /eslint-plugin-jsx-a11y/6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  /eslint-plugin-prettier/3.4.1(eslint-config-prettier@8.10.0)(eslint@8.57.1)(wp-prettier@2.8.5):
    eslint-plugin-prettier: public
  /eslint-plugin-react-hooks/4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/8.57.1:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /execa/5.1.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expand-tilde/1.2.2:
    expand-tilde: private
  /expect-puppeteer/4.4.0:
    expect-puppeteer: private
  /expect/29.7.0:
    expect: private
  /express/4.21.2:
    express: private
  /extract-zip/2.0.1:
    extract-zip: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fastest-levenshtein/1.0.16:
    fastest-levenshtein: private
  /fastq/1.19.1:
    fastq: private
  /faye-websocket/0.11.4:
    faye-websocket: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fd-slicer/1.1.0:
    fd-slicer: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /filename-reserved-regex/2.0.0:
    filename-reserved-regex: private
  /filenamify/4.3.0:
    filenamify: private
  /fill-range/7.1.1:
    fill-range: private
  /finalhandler/1.3.1:
    finalhandler: private
  /find-cache-dir/3.3.2:
    find-cache-dir: private
  /find-file-up/0.1.3:
    find-file-up: private
  /find-parent-dir/0.3.1:
    find-parent-dir: private
  /find-pkg/0.1.2:
    find-pkg: private
  /find-process/1.4.10:
    find-process: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flat/5.0.2:
    flat: private
  /flatted/3.3.3:
    flatted: private
  /follow-redirects/1.15.9:
    follow-redirects: private
  /for-each/0.3.5:
    for-each: private
  /for-in/1.0.2:
    for-in: private
  /for-own/0.1.5:
    for-own: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.2:
    form-data: private
  /forwarded/0.2.0:
    forwarded: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fresh/0.5.2:
    fresh: private
  /fs-constants/1.0.0:
    fs-constants: private
  /fs-exists-sync/0.1.0:
    fs-exists-sync: private
  /fs-monkey/1.0.6:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stdin/9.0.0:
    get-stdin: private
  /get-stream/5.2.0:
    get-stream: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /gettext-parser/1.4.0:
    gettext-parser: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/7.2.3:
    glob: private
  /global-modules/2.0.0:
    global-modules: private
  /global-prefix/3.0.0:
    global-prefix: private
  /globals/13.24.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /globby/13.2.2:
    globby: private
  /globjoin/0.1.4:
    globjoin: private
  /goober/2.1.16(csstype@3.1.3):
    goober: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /gzip-size/6.0.0:
    gzip-size: private
  /handle-thing/2.0.1:
    handle-thing: private
  /hard-rejection/2.1.0:
    hard-rejection: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/4.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /homedir-polyfill/1.0.3:
    homedir-polyfill: private
  /hosted-git-info/2.8.9:
    hosted-git-info: private
  /hpack.js/2.1.6:
    hpack.js: private
  /html-dom-parser/5.1.1:
    html-dom-parser: private
  /html-encoding-sniffer/3.0.0:
    html-encoding-sniffer: private
  /html-entities/2.6.0:
    html-entities: private
  /html-escaper/2.0.2:
    html-escaper: private
  /html-tags/3.3.1:
    html-tags: private
  /htmlparser2/10.0.0:
    htmlparser2: private
  /http-deceiver/1.2.7:
    http-deceiver: private
  /http-errors/2.0.0:
    http-errors: private
  /http-parser-js/0.5.10:
    http-parser-js: private
  /http-proxy-agent/5.0.0:
    http-proxy-agent: private
  /http-proxy-middleware/2.0.9(@types/express@4.17.22):
    http-proxy-middleware: private
  /http-proxy/1.18.1:
    http-proxy: private
  /https-proxy-agent/5.0.1:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.6.3:
    iconv-lite: private
  /icss-utils/5.1.0(postcss@8.5.4):
    icss-utils: private
  /idb/7.1.1:
    idb: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore-walk/4.0.1:
    ignore-walk: private
  /ignore/5.3.2:
    ignore: private
  /immutable/5.1.2:
    immutable: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-lazy/4.0.0:
    import-lazy: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/3.0.1:
    ini: private
  /inline-style-parser/0.2.4:
    inline-style-parser: private
  /internal-slot/1.1.0:
    internal-slot: private
  /interpret/2.2.0:
    interpret: private
  /ipaddr.js/2.2.0:
    ipaddr.js: private
  /irregular-plurals/3.5.0:
    irregular-plurals: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-buffer/1.1.6:
    is-buffer: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-docker/2.2.1:
    is-docker: private
  /is-extendable/0.1.1:
    is-extendable: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-path-cwd/2.2.0:
    is-path-cwd: private
  /is-path-in-cwd/2.1.0:
    is-path-in-cwd: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/3.0.0:
    is-plain-obj: private
  /is-plain-object/5.0.0:
    is-plain-object: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /is-windows/0.2.0:
    is-windows: private
  /is-wsl/2.2.0:
    is-wsl: private
  /isarray/1.0.0:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /isobject/3.0.1:
    isobject: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/5.2.1:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0:
    jest-cli: private
  /jest-config/29.7.0(@types/node@22.15.29):
    jest-config: private
  /jest-dev-server/6.2.0:
    jest-dev-server: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-jsdom/29.7.0:
    jest-environment-jsdom: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/27.5.1:
    jest-worker: private
  /jest/29.7.0:
    jest: private
  /jiti/1.21.7:
    jiti: private
  /joi/17.13.3:
    joi: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsdoc-type-pratt-parser/3.1.0:
    jsdoc-type-pratt-parser: private
  /jsdom/20.0.3:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json2php/0.0.7:
    json2php: private
  /json5/2.2.3:
    json5: private
  /jsonc-parser/3.0.0:
    jsonc-parser: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/3.2.2:
    kind-of: private
  /kleur/3.0.3:
    kleur: private
  /klona/2.0.6:
    klona: private
  /known-css-properties/0.26.0:
    known-css-properties: private
  /language-subtag-registry/0.3.23:
    language-subtag-registry: private
  /language-tags/1.0.9:
    language-tags: private
  /launch-editor/2.10.0:
    launch-editor: private
  /lazy-cache/1.0.4:
    lazy-cache: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /linkify-it/3.0.3:
    linkify-it: private
  /loader-runner/4.3.0:
    loader-runner: private
  /loader-utils/2.0.4:
    loader-utils: private
  /locate-path/5.0.0:
    locate-path: private
  /lodash.camelcase/4.3.0:
    lodash.camelcase: private
  /lodash.debounce/4.0.8:
    lodash.debounce: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.truncate/4.4.2:
    lodash.truncate: private
  /lodash.uniq/4.5.0:
    lodash.uniq: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/4.1.0:
    log-symbols: private
  /loglevel/1.9.2:
    loglevel: private
  /long/5.3.2:
    long: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lower-case/2.0.2:
    lower-case: private
  /lru-cache/4.1.5:
    lru-cache: private
  /make-dir/3.1.0:
    make-dir: private
  /makeerror/1.0.12:
    makeerror: private
  /map-obj/4.3.0:
    map-obj: private
  /map-values/1.0.1:
    map-values: private
  /markdown-it/12.3.2:
    markdown-it: private
  /markdownlint-cli/0.31.1:
    markdownlint-cli: private
  /markdownlint-rule-helpers/0.16.0:
    markdownlint-rule-helpers: private
  /markdownlint/0.25.1:
    markdownlint: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /mathml-tag-names/2.1.3:
    mathml-tag-names: private
  /mdn-data/2.0.30:
    mdn-data: private
  /mdurl/1.0.1:
    mdurl: private
  /media-typer/0.3.0:
    media-typer: private
  /memfs/3.5.3:
    memfs: private
  /memize/2.1.0:
    memize: private
  /meow/6.1.1:
    meow: private
  /merge-deep/3.0.3:
    merge-deep: private
  /merge-descriptors/1.0.3:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/1.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /min-indent/1.0.1:
    min-indent: private
  /mini-svg-data-uri/1.4.4:
    mini-svg-data-uri: private
  /minimalistic-assert/1.0.1:
    minimalistic-assert: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist-options/4.1.0:
    minimist-options: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mixin-object/2.0.1:
    mixin-object: private
  /mkdirp-classic/0.5.3:
    mkdirp-classic: private
  /mrmime/2.0.1:
    mrmime: private
  /ms/2.1.2:
    ms: private
  /multicast-dns/7.2.5:
    multicast-dns: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare-lite/1.4.0:
    natural-compare-lite: private
  /natural-compare/1.4.0:
    natural-compare: private
  /negotiator/0.6.4:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /no-case/3.0.4:
    no-case: private
  /node-addon-api/7.1.1:
    node-addon-api: private
  /node-fetch/2.6.7:
    node-fetch: private
  /node-forge/1.3.1:
    node-forge: private
  /node-int64/0.4.0:
    node-int64: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-package-data/2.5.0:
    normalize-package-data: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /normalize-url/6.1.0:
    normalize-url: private
  /npm-bundled/1.1.2:
    npm-bundled: private
  /npm-normalize-package-bin/1.0.1:
    npm-normalize-package-bin: private
  /npm-package-json-lint/5.4.2:
    npm-package-json-lint: private
  /npm-packlist/3.0.0:
    npm-packlist: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nth-check/2.1.1:
    nth-check: private
  /nwsapi/2.2.20:
    nwsapi: private
  /object-assign/4.1.1:
    object-assign: private
  /object-filter/1.0.2:
    object-filter: private
  /object-hash/3.0.0:
    object-hash: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /obuf/1.1.2:
    obuf: private
  /on-finished/2.4.1:
    on-finished: private
  /on-headers/1.0.2:
    on-headers: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /open/8.4.2:
    open: private
  /opener/1.5.2:
    opener: private
  /optionator/0.9.4:
    optionator: private
  /os-homedir/1.0.2:
    os-homedir: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/4.1.0:
    p-locate: private
  /p-map/2.1.0:
    p-map: private
  /p-retry/4.6.2:
    p-retry: private
  /p-try/2.2.0:
    p-try: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parse-passwd/1.0.0:
    parse-passwd: private
  /parse5/7.3.0:
    parse5: private
  /parseurl/1.3.3:
    parseurl: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-is-inside/1.0.2:
    path-is-inside: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/0.1.12:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pend/1.2.0:
    pend: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pify/4.0.1:
    pify: private
  /pinkie-promise/2.0.1:
    pinkie-promise: private
  /pinkie/2.0.4:
    pinkie: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /plur/4.0.0:
    plur: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-calc/8.2.4(postcss@8.5.4):
    postcss-calc: private
  /postcss-colormin/5.3.1(postcss@8.5.4):
    postcss-colormin: private
  /postcss-convert-values/5.1.3(postcss@8.5.4):
    postcss-convert-values: private
  /postcss-discard-comments/5.1.2(postcss@8.5.4):
    postcss-discard-comments: private
  /postcss-discard-duplicates/5.1.0(postcss@8.5.4):
    postcss-discard-duplicates: private
  /postcss-discard-empty/5.1.1(postcss@8.5.4):
    postcss-discard-empty: private
  /postcss-discard-overridden/5.1.0(postcss@8.5.4):
    postcss-discard-overridden: private
  /postcss-import/15.1.0(postcss@8.5.4):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.4):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.4):
    postcss-load-config: private
  /postcss-media-query-parser/0.2.3:
    postcss-media-query-parser: private
  /postcss-merge-longhand/5.1.7(postcss@8.5.4):
    postcss-merge-longhand: private
  /postcss-merge-rules/5.1.4(postcss@8.5.4):
    postcss-merge-rules: private
  /postcss-minify-font-values/5.1.0(postcss@8.5.4):
    postcss-minify-font-values: private
  /postcss-minify-gradients/5.1.1(postcss@8.5.4):
    postcss-minify-gradients: private
  /postcss-minify-params/5.1.4(postcss@8.5.4):
    postcss-minify-params: private
  /postcss-minify-selectors/5.2.1(postcss@8.5.4):
    postcss-minify-selectors: private
  /postcss-modules-extract-imports/3.1.0(postcss@8.5.4):
    postcss-modules-extract-imports: private
  /postcss-modules-local-by-default/4.2.0(postcss@8.5.4):
    postcss-modules-local-by-default: private
  /postcss-modules-scope/3.2.1(postcss@8.5.4):
    postcss-modules-scope: private
  /postcss-modules-values/4.0.0(postcss@8.5.4):
    postcss-modules-values: private
  /postcss-nested/6.2.0(postcss@8.5.4):
    postcss-nested: private
  /postcss-normalize-charset/5.1.0(postcss@8.5.4):
    postcss-normalize-charset: private
  /postcss-normalize-display-values/5.1.0(postcss@8.5.4):
    postcss-normalize-display-values: private
  /postcss-normalize-positions/5.1.1(postcss@8.5.4):
    postcss-normalize-positions: private
  /postcss-normalize-repeat-style/5.1.1(postcss@8.5.4):
    postcss-normalize-repeat-style: private
  /postcss-normalize-string/5.1.0(postcss@8.5.4):
    postcss-normalize-string: private
  /postcss-normalize-timing-functions/5.1.0(postcss@8.5.4):
    postcss-normalize-timing-functions: private
  /postcss-normalize-unicode/5.1.1(postcss@8.5.4):
    postcss-normalize-unicode: private
  /postcss-normalize-url/5.1.0(postcss@8.5.4):
    postcss-normalize-url: private
  /postcss-normalize-whitespace/5.1.1(postcss@8.5.4):
    postcss-normalize-whitespace: private
  /postcss-ordered-values/5.1.3(postcss@8.5.4):
    postcss-ordered-values: private
  /postcss-reduce-initial/5.1.2(postcss@8.5.4):
    postcss-reduce-initial: private
  /postcss-reduce-transforms/5.1.0(postcss@8.5.4):
    postcss-reduce-transforms: private
  /postcss-resolve-nested-selector/0.1.6:
    postcss-resolve-nested-selector: private
  /postcss-safe-parser/6.0.0(postcss@8.5.4):
    postcss-safe-parser: private
  /postcss-scss/4.0.9(postcss@8.5.4):
    postcss-scss: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-svgo/5.1.0(postcss@8.5.4):
    postcss-svgo: private
  /postcss-unique-selectors/5.1.1(postcss@8.5.4):
    postcss-unique-selectors: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /progress/2.0.3:
    progress: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /protobufjs/7.5.3:
    protobufjs: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /pseudomap/1.0.2:
    pseudomap: private
  /psl/1.15.0:
    psl: private
  /pump/3.0.2:
    pump: private
  /punycode/2.3.1:
    punycode: private
  /puppeteer-core/13.7.0:
    puppeteer-core: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qs/6.13.0:
    qs: private
  /querystringify/2.2.0:
    querystringify: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /quick-lru/4.0.1:
    quick-lru: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /react-is/16.13.1:
    react-is: private
  /react-property/2.0.2:
    react-property: private
  /react-refresh/0.10.0:
    react-refresh: private
  /react-router/6.30.1(react@18.3.1):
    react-router: private
  /read-cache/1.0.0:
    read-cache: private
  /read-pkg-up/7.0.1:
    read-pkg-up: private
  /read-pkg/5.2.0:
    read-pkg: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /rechoir/0.7.1:
    rechoir: private
  /redent/3.0.0:
    redent: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regenerate-unicode-properties/10.2.0:
    regenerate-unicode-properties: private
  /regenerate/1.4.2:
    regenerate: private
  /regenerator-runtime/0.14.1:
    regenerator-runtime: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /regexpu-core/6.2.0:
    regexpu-core: private
  /regjsgen/0.8.0:
    regjsgen: private
  /regjsparser/0.12.0:
    regjsparser: private
  /remove-accents/0.5.0:
    remove-accents: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /requireindex/1.2.0:
    requireindex: private
  /requires-port/1.0.0:
    requires-port: private
  /resolve-bin/0.4.3:
    resolve-bin: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-dir/0.1.1:
    resolve-dir: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/1.22.10:
    resolve: private
  /retry/0.13.1:
    retry: private
  /reusify/1.1.0:
    reusify: private
  /rimraf/3.0.2:
    rimraf: private
  /run-con/1.2.12:
    run-con: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sass-loader/12.6.0(sass@1.89.1)(webpack@5.99.9):
    sass-loader: private
  /sass/1.89.1:
    sass: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.23.2:
    scheduler: private
  /schema-utils/4.3.2:
    schema-utils: private
  /select-hose/2.0.0:
    select-hose: private
  /selfsigned/2.4.1:
    selfsigned: private
  /semver/7.7.2:
    semver: private
  /send/0.19.0:
    send: private
  /serialize-javascript/6.0.2:
    serialize-javascript: private
  /serve-index/1.9.1:
    serve-index: private
  /serve-static/1.16.2:
    serve-static: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /shallow-clone/0.1.2:
    shallow-clone: private
  /shebang-command/1.2.0:
    shebang-command: private
  /shebang-regex/1.0.0:
    shebang-regex: private
  /shell-quote/1.8.3:
    shell-quote: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/3.0.7:
    signal-exit: private
  /sirv/2.0.4:
    sirv: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/4.0.0:
    slice-ansi: private
  /snake-case/3.0.4:
    snake-case: private
  /sockjs/0.3.24:
    sockjs: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-loader/3.0.2(webpack@5.99.9):
    source-map-loader: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.7.4:
    source-map: private
  /spawnd/6.2.0:
    spawnd: private
  /spdx-correct/3.2.0:
    spdx-correct: private
  /spdx-exceptions/2.5.0:
    spdx-exceptions: private
  /spdx-expression-parse/3.0.1:
    spdx-expression-parse: private
  /spdx-license-ids/3.0.21:
    spdx-license-ids: private
  /spdy-transport/3.0.0:
    spdy-transport: private
  /spdy/4.0.2:
    spdy: private
  /sprintf-js/1.1.3:
    sprintf-js: private
  /stable/0.1.8:
    stable: private
  /stack-utils/2.0.6:
    stack-utils: private
  /stackframe/1.3.4:
    stackframe: private
  /statuses/2.0.1:
    statuses: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string.prototype.includes/2.0.1:
    string.prototype.includes: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/4.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strip-outer/1.0.1:
    strip-outer: private
  /style-search/0.1.0:
    style-search: private
  /style-to-js/1.1.16:
    style-to-js: private
  /style-to-object/1.0.8:
    style-to-object: private
  /stylehacks/5.1.1(postcss@8.5.4):
    stylehacks: private
  /stylelint-config-recommended-scss/5.0.2(postcss@8.5.4)(stylelint@14.16.1):
    stylelint-config-recommended-scss: private
  /stylelint-config-recommended/6.0.0(stylelint@14.16.1):
    stylelint-config-recommended: private
  /stylelint-scss/4.7.0(stylelint@14.16.1):
    stylelint-scss: private
  /stylelint/14.16.1:
    stylelint: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-hyperlinks/2.3.0:
    supports-hyperlinks: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /svg-parser/2.0.4:
    svg-parser: private
  /svg-tags/1.0.0:
    svg-tags: private
  /svgo/3.3.2:
    svgo: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /table/6.9.0:
    table: private
  /tannin/1.2.0:
    tannin: private
  /tapable/2.2.2:
    tapable: private
  /tar-fs/2.1.1:
    tar-fs: private
  /tar-stream/2.2.0:
    tar-stream: private
  /terser-webpack-plugin/5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  /terser/5.40.0:
    terser: private
  /test-exclude/6.0.0:
    test-exclude: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /through/2.3.8:
    through: private
  /thunky/1.1.0:
    thunky: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /totalist/3.0.1:
    totalist: private
  /tough-cookie/4.1.4:
    tough-cookie: private
  /tr46/3.0.0:
    tr46: private
  /tree-kill/1.2.2:
    tree-kill: private
  /trim-newlines/3.0.1:
    trim-newlines: private
  /trim-repeated/1.0.0:
    trim-repeated: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /tsutils/3.21.0(typescript@5.8.3):
    tsutils: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/0.8.1:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /typescript/5.8.3:
    typescript: private
  /uc.micro/1.0.6:
    uc.micro: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /unbzip2-stream/1.4.3:
    unbzip2-stream: private
  /undici-types/6.21.0:
    undici-types: private
  /undici/6.19.7:
    undici: private
  /unicode-canonical-property-names-ecmascript/2.0.1:
    unicode-canonical-property-names-ecmascript: private
  /unicode-match-property-ecmascript/2.0.0:
    unicode-match-property-ecmascript: private
  /unicode-match-property-value-ecmascript/2.2.0:
    unicode-match-property-value-ecmascript: private
  /unicode-property-aliases-ecmascript/2.1.0:
    unicode-property-aliases-ecmascript: private
  /universalify/0.2.0:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /update-browserslist-db/1.1.3(browserslist@4.25.0):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /url-parse/1.5.10:
    url-parse: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/8.3.2:
    uuid: private
  /v8-compile-cache/2.4.0:
    v8-compile-cache: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /validate-npm-package-license/3.0.4:
    validate-npm-package-license: private
  /vary/1.1.2:
    vary: private
  /w3c-xmlserializer/4.0.0:
    w3c-xmlserializer: private
  /wait-on/6.0.1:
    wait-on: private
  /walker/1.0.8:
    walker: private
  /watchpack/2.4.4:
    watchpack: private
  /wbuf/1.7.3:
    wbuf: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /webpack-bundle-analyzer/4.10.2:
    webpack-bundle-analyzer: private
  /webpack-cli/4.10.0(webpack-bundle-analyzer@4.10.2)(webpack-dev-server@4.15.2)(webpack@5.99.9):
    webpack-cli: private
  /webpack-dev-middleware/5.3.4(webpack@5.99.9):
    webpack-dev-middleware: private
  /webpack-dev-server/4.15.2(webpack-cli@4.10.0)(webpack@5.99.9):
    webpack-dev-server: private
  /webpack-merge/5.10.0:
    webpack-merge: private
  /webpack-sources/3.3.2:
    webpack-sources: private
  /webpack/5.99.9(webpack-cli@4.10.0):
    webpack: private
  /websocket-driver/0.7.4:
    websocket-driver: private
  /websocket-extensions/0.1.4:
    websocket-extensions: private
  /whatwg-encoding/2.0.0:
    whatwg-encoding: private
  /whatwg-mimetype/3.0.0:
    whatwg-mimetype: private
  /whatwg-url/11.0.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/1.3.1:
    which: private
  /wildcard/2.0.1:
    wildcard: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wp-prettier/2.8.5:
    prettier: public
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrap-ansi/8.1.0:
    wrap-ansi: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /ws/8.5.0:
    ws: private
  /xml-name-validator/4.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /y18n/5.0.8:
    y18n: private
  /yallist/2.1.2:
    yallist: private
  /yaml/1.10.2:
    yaml: private
  /yargs-parser/18.1.3:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yauzl/2.10.0:
    yauzl: private
  /yocto-queue/0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.10.4
pendingBuilds: []
prunedAt: Wed, 04 Jun 2025 21:43:57 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@parcel/watcher-android-arm64/2.5.1
  - /@parcel/watcher-darwin-x64/2.5.1
  - /@parcel/watcher-freebsd-x64/2.5.1
  - /@parcel/watcher-linux-arm-glibc/2.5.1
  - /@parcel/watcher-linux-arm-musl/2.5.1
  - /@parcel/watcher-linux-arm64-glibc/2.5.1
  - /@parcel/watcher-linux-arm64-musl/2.5.1
  - /@parcel/watcher-linux-x64-glibc/2.5.1
  - /@parcel/watcher-linux-x64-musl/2.5.1
  - /@parcel/watcher-win32-arm64/2.5.1
  - /@parcel/watcher-win32-ia32/2.5.1
  - /@parcel/watcher-win32-x64/2.5.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
