#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/i18n/tools/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/i18n/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/i18n/tools/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/i18n/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules/@wordpress/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/@wordpress+i18n@5.25.0/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@wordpress/i18n/tools/pot-to-php.js" "$@"
else
  exec node  "$basedir/../@wordpress/i18n/tools/pot-to-php.js" "$@"
fi
