{"version": 3, "file": "canUseDynamicImport.js", "names": ["result", "canUseDynamicImport", "undefined", "Function", "e"], "sources": ["../src/canUseDynamicImport.ts"], "sourcesContent": ["/* istanbul ignore file -- @preserve */\nlet result: boolean;\nfunction canUseDynamicImport(): boolean {\n  if (result === undefined) {\n    try {\n      new Function('id', 'return import(id);');\n      result = true;\n    } catch (e) {\n      result = false;\n    }\n  }\n  return result;\n}\n\nexport { canUseDynamicImport };\n"], "mappings": ";;;;;;;AAAA;AACA,IAAIA,MAAJ;;AACA,SAASC,mBAAT,GAAwC;EACtC,IAAID,MAAM,KAAKE,SAAf,EAA0B;IACxB,IAAI;MACF,IAAIC,QAAJ,CAAa,IAAb,EAAmB,oBAAnB;MACAH,MAAM,GAAG,IAAT;IACD,CAHD,CAGE,OAAOI,CAAP,EAAU;MACVJ,MAAM,GAAG,KAAT;IACD;EACF;;EACD,OAAOA,MAAP;AACD"}