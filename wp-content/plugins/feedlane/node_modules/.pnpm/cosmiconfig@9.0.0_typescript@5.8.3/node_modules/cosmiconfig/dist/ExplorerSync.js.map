{"version": 3, "file": "ExplorerSync.js", "sourceRoot": "", "sources": ["../src/ExplorerSync.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,yCAA0D;AAC1D,uDAA0E;AAC1E,mCAA2C;AAO3C,uCAAwE;AAExE;;GAEG;AACH,MAAa,YAAa,SAAQ,8BAAiC;IAC1D,IAAI,CAAC,QAAgB;QAC1B,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAElC,MAAM,IAAI,GAAG,GAAsB,EAAE;YACnC,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC;QACF,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;SAChD;QACD,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAEM,MAAM,CAAC,IAAI,GAAG,EAAE;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAClC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBAC7B,OAAO,MAAM,CAAC;aACf;SACF;QAED,IAAI,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,qCAAqC;QACrC,IAAI,YAAY,CAAC,IAAI,EAAE;YACrB,2BAA2B;YAC3B,MAAM,IAAI,KAAK,CACb,6DAA6D,IAAI,GAAG,CACrE,CAAC;SACH;QACD,IAAI,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC;QACpC,MAAM,MAAM,GAAG,GAAsB,EAAE;YACrC,qCAAqC;YACrC,IAAI,IAAA,yBAAe,EAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACpC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,CAC/C,UAAU,EACV,uCAA4B,CAC7B,EAAE;oBACD,IAAI;wBACF,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;wBACjD,IACE,MAAM,KAAK,IAAI;4BACf,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,EACxD;4BACA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;yBACtC;qBACF;oBAAC,OAAO,KAAK,EAAE;wBACd,IACE,KAAK,CAAC,IAAI,KAAK,QAAQ;4BACvB,KAAK,CAAC,IAAI,KAAK,QAAQ;4BACvB,KAAK,CAAC,IAAI,KAAK,SAAS;4BACxB,KAAK,CAAC,IAAI,KAAK,QAAQ,EACvB;4BACA,SAAS;yBACV;wBACD,MAAM,KAAK,CAAC;qBACb;iBACF;aACF;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;gBACrB,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC;gBAC/B,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBAC3D;gBACD,OAAO,MAAM,EAAE,CAAC;aACjB;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAA,iBAAO,EAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SAChD;QACD,OAAO,MAAM,EAAE,CAAC;IAClB,CAAC;IAED,kBAAkB,CAChB,QAAgB,EAChB,cAA6B,EAAE;QAE/B,MAAM,QAAQ,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC,mBAAmB,CAC7B,QAAQ,EACR,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,CACjE,CAAC;IACJ,CAAC;IAED,0BAA0B,CACxB,QAAgB,EAChB,QAAgB,EAChB,WAA0B;QAE1B,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAElE,IAAI,CAAC,aAAa,IAAI,CAAC,IAAA,cAAM,EAAC,aAAa,EAAE,SAAS,CAAC,EAAE;YACvD,OAAO,aAAa,CAAC;SACtB;QAED,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7C,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,UAAU,EAAE,GAAG,aAAa,CAAC;QAC1D,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACjE,MAAM,cAAc,GAAG,CAAC,GAAG,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClD,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QAE5D,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEjE,OAAO,MAAM,EAAE,MAAM,CAAC;QACxB,CAAC,CAAC,CAAC;QACH,OAAO,IAAA,gBAAQ,EAAC,CAAC,GAAG,eAAe,EAAE,UAAU,CAAC,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;SAC3C,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB,CAAC,QAAgB,EAAE,QAAgB;QACnD,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC1B,OAAO;SACR;QAED,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CACb,2BAA2B,IAAA,yCAAuB,EAAC,SAAS,CAAC,EAAE,CAChE,CAAC;SACH;QAED,IAAI;YACF,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAElD,IAAI,cAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,KAAK,SAAS,EAAE;gBACpD,OAAO,cAAc,CAAC;aACvB;YAED,OAAO,CACL,IAAA,2BAAiB,EACf,cAAc,EACd,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAClD,IAAI,IAAI,CACV,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC1B,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,WAAW,CAAC,IAAY;QACtB,IAAI;YACF,YAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,QAAgB;QACxB,QAAQ,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;YAClC,KAAK,MAAM,CAAC,CAAC;gBACX,uBAAuB;gBACvB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;gBAChD,OAAO;aACR;YACD,KAAK,SAAS,CAAC,CAAC;gBACd,IAAI,UAAU,GAAG,QAAQ,CAAC;gBAC1B,OAAO,IAAI,EAAE;oBACX,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC;oBAClD,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;wBAClC,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC;wBAC5D,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;4BACjC,MAAM;yBACP;qBACF;oBACD,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC3C,qCAAqC;oBACrC,IAAI,SAAS,KAAK,UAAU,EAAE;wBAC5B,wDAAwD;wBACxD,MAAM;qBACP;oBACD,UAAU,GAAG,SAAS,CAAC;iBACxB;gBACD,OAAO;aACR;YACD,KAAK,QAAQ,CAAC,CAAC;gBACb,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;aACrC;SACF;IACH,CAAC;IAED;;OAEG;IACH,0BAA0B;IACnB,QAAQ,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,0BAA0B;IACnB,UAAU,CAAC,IAAI,GAAG,EAAE;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAlND,oCAkNC"}