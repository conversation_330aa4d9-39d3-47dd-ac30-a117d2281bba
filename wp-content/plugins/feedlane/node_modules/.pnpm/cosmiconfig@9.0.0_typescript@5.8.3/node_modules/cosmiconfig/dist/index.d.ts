import { defaultLoaders, defaultLoadersSync, getDefaultSearchPlaces, getDefaultSearchPlacesSync, globalConfigSearchPlaces, globalConfigSearchPlacesSync } from './defaults';
import { CommonOptions, Config, CosmiconfigResult, Loader, LoaderResult, Loaders, LoadersSync, LoaderSync, Options, OptionsSync, PublicExplorer, PublicExplorerBase, PublicExplorerSync, SearchStrategy, Transform, TransformSync } from './types.js';
export declare function cosmiconfig(moduleName: string, options?: Readonly<Partial<Options>>): PublicExplorer;
export declare function cosmiconfigSync(moduleName: string, options?: Readonly<Partial<OptionsSync>>): PublicExplorerSync;
export { Config, CosmiconfigResult, LoaderResult, Loader, Loaders, LoaderSync, LoadersSync, Transform, TransformSync, SearchStrategy, CommonOptions, Options, OptionsSync, PublicExplorerBase, PublicExplorer, PublicExplorerSync, getDefaultSearchPlaces, getDefaultSearchPlacesSync, globalConfigSearchPlaces, globalConfigSearchPlacesSync, defaultLoaders, defaultLoadersSync, };
//# sourceMappingURL=index.d.ts.map