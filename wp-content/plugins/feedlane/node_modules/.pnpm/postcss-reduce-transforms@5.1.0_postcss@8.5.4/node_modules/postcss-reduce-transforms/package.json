{"name": "postcss-reduce-transforms", "version": "5.1.0", "description": "Reduce transform functions with PostCSS.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss-value-parser": "^4.2.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-reduce-transforms\n\n> Reduce transform functions with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-reduce-transforms) do:\n\n```\nnpm install postcss-reduce-transforms --save\n```\n\n## Example\n\nThis module will reduce transform functions where possible. For more examples,\nsee the [tests](src/__tests__/index.js).\n\n### Input\n\n```css\nh1 {\n  transform: rotate3d(0, 0, 1, 20deg);\n}\n```\n\n### Output\n\n```css\nh1 {\n  transform: rotate(20deg);\n}\n```\n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}