{"name": "stylelint-config-recommended-scss", "version": "5.0.2", "description": "The recommended shareable SCSS config for Stylelint", "keywords": ["stylelint", "stylelint-config", "recommended", "scss"], "repository": "stylelint-scss/stylelint-config-recommended-scss", "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON>", "main": "index.js", "files": ["index.js"], "scripts": {"format": "prettier . --write", "lint:formatting": "prettier . --check", "lint:js": "eslint . --ignore-path .gitignore", "lint:md": "remark . --quiet --frail --ignore-path .gitignore", "lint": "npm-run-all --parallel lint:*", "release": "np", "test": "jest", "watch": "jest --watch"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"module": true, "require": true}}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "dependencies": {"postcss-scss": "^4.0.2", "stylelint-config-recommended": "^6.0.0", "stylelint-scss": "^4.0.0"}, "devDependencies": {"@stylelint/prettier-config": "^2.0.0", "@stylelint/remark-preset": "^3.0.0", "eslint": "^8.3.0", "eslint-config-stylelint": "^15.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^25.2.4", "eslint-plugin-node": "^11.1.0", "eslint-plugin-regexp": "^1.5.0", "jest": "^27.3.1", "np": "^7.6.0", "npm-run-all": "^4.1.5", "prettier": "^2.4.1", "remark-cli": "^10.0.1", "stylelint": "^14.1.0"}, "peerDependencies": {"stylelint": "^14.0.0"}}