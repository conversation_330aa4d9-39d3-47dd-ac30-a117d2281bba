# stylelint-config-recommended-scss

[![npm version](http://img.shields.io/npm/v/stylelint-config-recommended-scss.svg)](https://www.npmjs.org/package/stylelint-config-recommended-scss)
[![Build Status](https://github.com/stylelint-scss/stylelint-config-recommended-scss/workflows/CI/badge.svg)](https://github.com/stylelint-scss/stylelint-config-recommended-scss/actions?workflow=CI)
[![contributions welcome](https://img.shields.io/badge/contributions-welcome-brightgreen.svg?style=flat)](https://egghead.io/courses/how-to-contribute-to-an-open-source-project-on-github)
[![Downloads per month](https://img.shields.io/npm/dm/stylelint-config-recommended-scss.svg)](https://npmcharts.com/compare/stylelint-config-recommended-scss)

> The recommended shareable SCSS config for stylelint.

This config:

- extends the [`stylelint-config-recommended` shared config](https://github.com/stylelint/stylelint-config-recommended) and configures its rules for SCSS
- bundles the [`stylelint-scss` plugin pack](https://github.com/stylelint-scss/stylelint-scss) and turns on its rules that check for possible errors
- bundles the [`postcss-scss` custom syntax](https://github.com/postcss/postcss-scss) and configures it

This config is extended by the [`stylelint-config-standard-scss` shared config](https://github.com/stylelint-scss/stylelint-config-standard-scss). That config is better suited to most users as it also turns on the stylistic rules in the [`stylelint-scss` plugin pack](https://github.com/stylelint-scss/stylelint-scss).

To see the rules that this config uses, please read the [config itself](/index.js).

## Installation

```shell
npm install --save-dev stylelint-config-recommended-scss
```

## Usage

Set your `stylelint` config to:

```json
{
  "extends": "stylelint-config-recommended-scss"
}
```

### Extending the config

Simply add a `"rules"` key to your config, then add your overrides and additions there.

For example, to turn off the `scss/at-if-no-null` rule:

```json
{
  "extends": "stylelint-config-recommended-scss",
  "rules": {
    "scss/at-if-no-null": null
  }
}
```

## [Changelog](CHANGELOG.md)

## [License](LICENSE)
