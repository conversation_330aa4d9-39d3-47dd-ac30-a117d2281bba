{"name": "sweetalert2", "version": "11.22.0", "repository": "sweetalert2/sweetalert2", "homepage": "https://sweetalert2.github.io/", "description": "A beautiful, responsive, customizable and accessible (WAI-ARIA) replacement for JavaScript's popup boxes, supported fork of sweetalert", "main": "dist/sweetalert2.all.js", "browser": "dist/sweetalert2.all.js", "module": "dist/sweetalert2.esm.all.js", "types": "sweetalert2.d.ts", "devDependencies": {"@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@rollup/plugin-babel": "^6.0.2", "@rollup/plugin-terser": "^0.4.0", "@sweetalert2/eslint-config": "^1.0.11", "@sweetalert2/stylelint-config": "^3.0.0", "cypress": "^14.0.0", "eslint": "^9.0.0", "eslint-plugin-cypress": "^4.0.0", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-no-unsanitized": "^4.0.1", "jquery": "^3.6.1", "playwright-webkit": "^1.37.1", "prettier": "^3.0.0", "rollup": "^4.0.0", "sass": "^1.83.0", "stylelint": "^16.0.0", "typescript": "4.8.4", "vite": "^6.0.0", "zx": "^8.0.0"}, "devDependenciesComments": {"typescript": "Do not upgrade TS, tests will verify that types work with the very first version of TS >= 4.7.4"}, "files": ["dist", "src", "themes", "sweetalert2.d.ts"], "author": "Limon Monte (https://limonte.github.io)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/zenflow)", "<PERSON>-<PERSON> <<EMAIL>> (https://github.com/toverux)", "<PERSON> (https://github.com/gverni)", "<PERSON> <<EMAIL>> (https://github.com/samturrell)", "<PERSON> (https://github.com/acupofjose)", "<PERSON> (https://github.com/birjolaxew)"], "keywords": ["sweetalert", "sweetalert2", "alert", "modal", "popup", "prompt", "confirm", "toast", "accessible"], "scripts": {"dev": "vite test/sandbox --open", "lint": "stylelint src/*.scss themes/*.css && eslint src cypress tools *.js *.ts && prettier --check src/**/*.js cypress/**/*.js *.js", "build": "zx tools/build.mjs", "test": "cypress run --headless", "check-types": "npx -p typescript@latest tsc --noEmit --lib dom,es2018 -p jsconfig.json", "cypress:open": "cypress open", "webpack-build": "webpack --config=test/webpack/webpack.config.js"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/limonte"}, "prettier": {"printWidth": 120, "semi": false, "singleQuote": true, "quoteProps": "consistent", "trailingComma": "es5"}, "bugs": "https://github.com/sweetalert2/sweetalert2/issues", "license": "MIT"}