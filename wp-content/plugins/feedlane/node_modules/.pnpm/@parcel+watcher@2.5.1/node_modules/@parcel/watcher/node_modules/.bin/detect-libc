#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules/detect-libc/bin/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules/detect-libc/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules/detect-libc/bin/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules/detect-libc/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/detect-libc@1.0.3/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../detect-libc/bin/detect-libc.js" "$@"
else
  exec node  "$basedir/../../../../detect-libc/bin/detect-libc.js" "$@"
fi
