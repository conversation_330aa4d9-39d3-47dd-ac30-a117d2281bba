#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/bin/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/bin/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/webpack@5.99.9_webpack-cli@4.10.0/node_modules:/Users/<USER>/Sites/betterdocs/wp-content/plugins/feedlane/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/bin/webpack.js" "$@"
else
  exec node  "$basedir/../../../../../webpack@5.99.9_webpack-cli@4.10.0/node_modules/webpack/bin/webpack.js" "$@"
fi
