"use strict";(globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[]).push([[828],{828:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var n=a(609),s=a(967),c=a(820),o=a(449),r=a(326),i=a(44);const l=({postId:e})=>{const t=(0,s.jE)(),{data:a,isLoading:l,error:d}=(0,c.I)({queryKey:["reactions",e],queryFn:()=>(async e=>i.A.get(`/reactions/${e}`))(e),staleTime:12e4}),m=(0,o.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>i.A.post("/reactions",{post_id:e,reaction_type:t}))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),r.Ay.success("Reaction added!")},onError:e=>{const t=e.response?.data?.message||"Failed to add reaction";r.Ay.error(t)}}),u=(0,o.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>i.A.delete(`/reactions/${e}/${t}`))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),r.Ay.success("Reaction removed!")},onError:e=>{const t=e.response?.data?.message||"Failed to remove reaction";r.Ay.error(t)}});if(l)return(0,n.createElement)("div",{className:"feedlane-reactions feedlane-reactions--loading"},(0,n.createElement)("div",{className:"feedlane-reactions__skeleton"},[1,2,3].map((e=>(0,n.createElement)("div",{key:e,className:"feedlane-reaction-button feedlane-reaction-button--skeleton"},(0,n.createElement)("div",{className:"feedlane-reaction-button__emoji"}),(0,n.createElement)("div",{className:"feedlane-reaction-button__count"}))))));if(d||!a)return null;const{reaction_counts:p={},user_reaction:y,available_reactions:_={}}=a;return(0,n.createElement)("div",{className:"feedlane-reactions"},(0,n.createElement)("div",{className:"feedlane-reactions__buttons"},Object.entries(_).map((([t,s])=>{const c=p[t]||0,o=y===t,r=m.isPending||u.isPending;return(0,n.createElement)("button",{key:t,onClick:()=>(t=>{a&&(a.user_reaction===t?u.mutate({postId:e,reactionType:t}):m.mutate({postId:e,reactionType:t}))})(t),disabled:r,className:"feedlane-reaction-button "+(o?"feedlane-reaction-button--active":""),title:`React with ${t}`},(0,n.createElement)("span",{className:"feedlane-reaction-button__emoji"},s),c>0&&(0,n.createElement)("span",{className:"feedlane-reaction-button__count"},c))}))),Object.values(p).some((e=>e>0))&&(0,n.createElement)("div",{className:"feedlane-reactions__summary"},Object.entries(p).filter((([e,t])=>t>0)).map((([e,t])=>(0,n.createElement)("span",{key:e,className:"feedlane-reactions__summary-item"},_[e]," ",t)))))}}}]);