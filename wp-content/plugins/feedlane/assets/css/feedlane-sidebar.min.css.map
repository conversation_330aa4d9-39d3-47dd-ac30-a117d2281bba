{"version": 3, "file": "css/feedlane-sidebar.min.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;;CAAA;AAAA;;;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;EAAA;AAAA;AAAA;;;;;;;;CAAA;AAAA;;EAAA;EAAA;EAAA;EAAA;KAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA;EAAA;AAAA;AAAA;;;;CAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;UAAA;AAAA;AAAA;;CAAA;AAAA;;;;;;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;AAAA;AAAA;;;;;CAAA;AAAA;;;;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;;;;CAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;;;CAAA;AAAA;;;;;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;AAAA;AAAA;;;CAAA;AAAA;;;;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;;;;;;;;;;;;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;;;EAAA;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;;CAAA;AAAA;EAAA;EAAA;AAAA;AAAA;;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;AAAA;AAAA;;CAAA;AAAA;EAAA;AAAA;AAAA;;;;CAAA;AAAA;;;;;;;;EAAA;EAAA;AAAA;AAAA;;CAAA;AAAA;;EAAA;EAAA;AAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;KAAA;UAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;UAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;UAAA;AAAA;AAAA;EAAA;KAAA;UAAA;EAAA;EAAA;UAAA;EAAA;EAAA;EAAA;EAAA;KAAA;UAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;ACAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;ACAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AHKA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAF;;AAKE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AADF;EAEE;EACA;EACA;EACA;EACA;EACA;AADF;AAII;EAAA;EACA;AADA;AAKA;EAAA;EACA;AADA;AAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EACA;EACA;AAFA;;AAQF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EACA;AADA;AAMF;EAAA;EAAA;AAAA;AAIA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAKA;EAAA;EAAA;EAAA;EACA;AADA;;AAOF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AADF;EAEE;AA/BJ;AAiCI;EACE;AA/BN;AAkCI;EACE;AAhCN;AAqCI;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAGE;AAHF;AAOE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAGE;AAHF;;AAQA;EAAA;EAAA;AAAA;;AAKA;EAAA;EAAA;AAAA;;AAAA;EAAA;IAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAKF;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AADF;EAEE;AAzDJ;AA6DI;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAKE;EAAA;EAAA;AAAA;;AAMJ;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMN;EAEI;IAAA;EAAA;EAKE;IAAA;EAAA;EAIA;IAAA;EAAA;AA9FN;AAs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wKI;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAEA;EACE;EACA;EAAA;AAjLR;AAuLI;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AApwBJ;EAAA,kBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,kBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,kBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,kBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,oBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA;AAglBC;AAhlBD;EAAA,6EAglBC;EAhlBD,iGAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,sBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,2GAglBC;EAhlBD,yGAglBC;EAhlBD;AAglBC;AAhlBD;EAAA,oBAglBC;EAhlBD;AAglBC;AAhlBD;EAAA;AAglBC;AAhlBD;EAAA;AAglBC;AAhlBD;EAAA;IAAA;EAglBC;EAhlBD;IAAA;EAglBC;EAhlBD;IAAA;EAglBC;EAhlBD;IAAA;EAglBC;AAAA,C", "sources": ["webpack://feedlane/./react-src/sidebar/scss/sidebar.scss", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/base.css", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/components.css", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/utilities.css"], "sourcesContent": ["@import 'tailwindcss/base';\n@import 'tailwindcss/components';\n@import 'tailwindcss/utilities';\n\n// CSS Variables\n:root {\n  --feedlane-primary-color: #0ea5e9;\n  --feedlane-primary-hover: #0284c7;\n  --feedlane-text-primary: #1f2937;\n  --feedlane-text-secondary: #6b7280;\n  --feedlane-bg-primary: #ffffff;\n  --feedlane-bg-secondary: #f9fafb;\n  --feedlane-border: #e5e7eb;\n  --feedlane-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --feedlane-z-index: 9999;\n}\n\n// Floating Button - Beamer Style\n.feedlane-floating-button {\n  @apply fixed z-[9999] transition-all duration-300 hover:scale-105 focus:outline-none cursor-pointer;\n  background-color: #000;\n  border-radius: 20px 20px 20px 4px;\n  padding: 12px 16px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  top: 50%;\n  transform: translateY(-50%);\n\n  &--left {\n    @apply left-0;\n    transform: translateY(-50%);\n  }\n\n  &--right {\n    @apply right-0;\n    transform: translateY(-50%);\n  }\n\n  .feedlane-floating-text {\n    @apply text-white text-sm font-medium;\n    writing-mode: vertical-rl;\n    text-orientation: mixed;\n  }\n}\n\n// Overlay\n.feedlane-overlay {\n  @apply fixed inset-0 bg-black bg-opacity-50 z-[9998] transition-opacity duration-300;\n}\n\n// Sidebar\n.feedlane-sidebar {\n  @apply fixed top-0 h-full w-96 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-in-out;\n\n  &--left {\n    @apply left-0 -translate-x-full;\n\n    &.feedlane-sidebar--open {\n      @apply translate-x-0;\n    }\n  }\n\n  &--right {\n    @apply right-0 translate-x-full;\n\n    &.feedlane-sidebar--open {\n      @apply translate-x-0;\n    }\n  }\n\n  // Header\n  &__header {\n    @apply flex items-center justify-between p-6 border-b border-gray-200;\n  }\n\n  &__title {\n    @apply text-xl font-bold text-gray-900 m-0;\n  }\n\n  &__close {\n    @apply p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300;\n  }\n\n  // Tabs\n  &__tabs {\n    @apply flex border-b border-gray-200;\n  }\n\n  &__tab {\n    @apply flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors duration-200 border-b-2 border-transparent;\n\n    &--active {\n      @apply text-gray-900 border-b-2;\n      border-bottom-color: var(--feedlane-primary-color);\n    }\n  }\n\n  &__tab-icon {\n    @apply text-lg;\n  }\n\n  &__tab-label {\n    @apply hidden sm:block;\n  }\n\n  // Content\n  &__content {\n    @apply flex-1 overflow-y-auto p-6;\n    max-height: calc(100vh - 140px);\n  }\n}\n\n// Loading States\n.feedlane-loading {\n  @apply flex flex-col items-center justify-center py-12 text-gray-500;\n\n  &__spinner {\n    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4;\n  }\n}\n\n// Error States\n.feedlane-error {\n  @apply text-center py-12;\n\n  p {\n    @apply text-gray-500 mb-4;\n  }\n}\n\n// Empty States\n.feedlane-empty {\n  @apply text-center py-12;\n\n  &__icon {\n    @apply text-4xl mb-4;\n  }\n\n  h3 {\n    @apply text-lg font-semibold text-gray-900 mb-2;\n  }\n\n  p {\n    @apply text-gray-500;\n  }\n}\n\n// Buttons\n.feedlane-button {\n  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\n\n  &--primary {\n    @apply text-white shadow-sm hover:shadow-md;\n    background-color: var(--feedlane-primary-color);\n\n    &:hover:not(:disabled) {\n      background-color: var(--feedlane-primary-hover);\n    }\n\n    &:focus {\n      --tw-ring-color: var(--feedlane-primary-color);\n    }\n  }\n\n  &--secondary {\n    @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;\n  }\n\n  &--small {\n    @apply px-3 py-1.5 text-xs;\n  }\n}\n\n// Form Elements\n.feedlane-input {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-transparent;\n\n  &:focus {\n    --tw-ring-color: var(--feedlane-primary-color);\n  }\n\n  &--small {\n    @apply px-2 py-1.5 text-sm;\n  }\n}\n\n.feedlane-textarea {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-transparent;\n\n  &:focus {\n    --tw-ring-color: var(--feedlane-primary-color);\n  }\n}\n\n.feedlane-form-row {\n  @apply flex gap-2;\n}\n\n// Spinner\n.feedlane-spinner {\n  @apply w-4 h-4 border-2 border-gray-200 border-t-current rounded-full animate-spin;\n\n  &--small {\n    @apply w-3 h-3 border;\n  }\n}\n\n// Newsfeed Styles\n.feedlane-newsfeed {\n  &__header {\n    @apply mb-6;\n\n    h3 {\n      @apply text-lg font-semibold text-gray-900 mb-1;\n    }\n\n    p {\n      @apply text-sm text-gray-500;\n    }\n  }\n\n  &__posts {\n    @apply space-y-6;\n  }\n}\n\n.feedlane-post {\n  @apply border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200;\n\n  &__header {\n    @apply mb-3;\n  }\n\n  &__title {\n    @apply text-base font-semibold text-gray-900 mb-2 leading-tight;\n  }\n\n  &__meta {\n    @apply flex items-center gap-2 text-xs text-gray-500;\n  }\n\n  &__date {\n    @apply flex items-center gap-1;\n  }\n\n  &__image {\n    @apply mb-3 rounded-lg overflow-hidden;\n\n    img {\n      @apply w-full h-auto;\n    }\n  }\n\n  &__content {\n    @apply mb-4;\n  }\n\n  &__excerpt {\n    @apply text-sm text-gray-700 leading-relaxed;\n\n    p {\n      @apply mb-2 last:mb-0;\n    }\n  }\n\n  &__read-more {\n    @apply text-xs font-medium mt-2 hover:underline;\n    color: var(--feedlane-primary-color);\n  }\n\n  &__footer {\n    @apply space-y-3;\n  }\n}\n\n// Reaction Styles\n.feedlane-reactions {\n  &__buttons {\n    @apply flex gap-2 mb-2;\n  }\n\n  &__summary {\n    @apply flex gap-3 text-xs text-gray-500;\n  }\n\n  &__summary-item {\n    @apply flex items-center gap-1;\n  }\n\n  &--loading {\n    .feedlane-reactions__skeleton {\n      @apply flex gap-2;\n    }\n  }\n}\n\n.feedlane-reaction-button {\n  @apply flex items-center gap-1 px-2 py-1 rounded-full border border-gray-200 hover:border-gray-300 transition-colors duration-200 text-sm;\n\n  &--active {\n    @apply border-blue-300 bg-blue-50;\n  }\n\n  &--skeleton {\n    @apply animate-pulse;\n\n    .feedlane-reaction-button__emoji {\n      @apply w-4 h-4 bg-gray-200 rounded;\n    }\n\n    .feedlane-reaction-button__count {\n      @apply w-3 h-3 bg-gray-200 rounded;\n    }\n  }\n\n  &__emoji {\n    @apply text-base leading-none;\n  }\n\n  &__count {\n    @apply text-xs font-medium text-gray-600;\n  }\n}\n\n// Feedback Styles\n.feedlane-feedback {\n  &__toggle {\n    @apply flex items-center justify-between mb-3;\n  }\n\n  &__toggle-button {\n    @apply flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200;\n  }\n\n  &__view-button {\n    @apply flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200;\n  }\n\n  &__form {\n    @apply space-y-3 p-3 bg-gray-50 rounded-lg;\n  }\n\n  &__guest-fields {\n    @apply space-y-2;\n  }\n\n  &__actions {\n    @apply flex justify-end gap-2;\n  }\n\n  &__list {\n    @apply mt-3 space-y-3;\n  }\n\n  &__loading {\n    @apply flex items-center justify-center gap-2 py-4 text-sm text-gray-500;\n  }\n\n  &__items {\n    @apply space-y-3;\n  }\n\n  &__item {\n    @apply p-3 bg-gray-50 rounded-lg;\n  }\n\n  &__item-header {\n    @apply flex items-center justify-between mb-2;\n  }\n\n  &__author {\n    @apply text-sm font-medium text-gray-900;\n  }\n\n  &__time {\n    @apply text-xs text-gray-500;\n  }\n\n  &__item-content {\n    @apply text-sm text-gray-700;\n  }\n\n  &__empty {\n    @apply text-center py-4;\n\n    p {\n      @apply text-sm text-gray-500;\n    }\n  }\n}\n\n// Responsive Design\n@media (max-width: 640px) {\n  .feedlane-sidebar {\n    @apply w-full;\n  }\n\n  .feedlane-floating-button {\n    &--left {\n      @apply left-4;\n    }\n\n    &--right {\n      @apply right-4;\n    }\n  }\n}\n\n// Beamer-Style Sidebar Updates\n.feedlane-sidebar {\n  &__header {\n    @apply p-0 border-b-0;\n    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);\n\n    .feedlane-sidebar__header-content {\n      @apply flex items-center justify-between p-4;\n    }\n\n    .feedlane-sidebar__title {\n      @apply text-white text-lg font-semibold;\n    }\n\n    .feedlane-sidebar__header-actions {\n      @apply flex items-center gap-2;\n    }\n\n    .feedlane-sidebar__search-btn,\n    .feedlane-sidebar__close {\n      @apply text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-lg transition-colors;\n    }\n  }\n\n  &__content {\n    @apply flex-1 overflow-y-auto p-0;\n  }\n\n  &__bottom-nav {\n    @apply flex border-t border-gray-200 bg-white;\n\n    .feedlane-sidebar__nav-item {\n      @apply flex-1 flex flex-col items-center justify-center py-3 px-2 text-xs font-medium text-gray-500 hover:text-gray-700 transition-colors;\n\n      &--active {\n        @apply text-blue-600;\n      }\n\n      .feedlane-sidebar__nav-label {\n        @apply mt-1;\n      }\n    }\n  }\n}\n\n// Subscribe Banner\n.feedlane-subscribe-banner {\n  @apply flex items-center gap-3 p-4 bg-gray-100 border-b border-gray-200;\n\n  &__icon {\n    @apply text-gray-600;\n  }\n\n  &__text {\n    @apply text-sm font-medium text-gray-700;\n  }\n}\n\n// Beamer Post Styles\n.feedlane-beamer-post {\n  @apply border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors;\n\n  &__header {\n    @apply flex items-start justify-between mb-3;\n  }\n\n  &__badges {\n    @apply flex items-center gap-2 flex-1;\n  }\n\n  &__badge {\n    @apply px-2 py-1 text-xs font-bold rounded;\n  }\n\n  &__date {\n    @apply text-xs text-gray-500;\n  }\n\n  &__share {\n    @apply text-gray-400 hover:text-gray-600 p-1 rounded transition-colors;\n  }\n\n  &__title {\n    @apply text-base font-semibold text-gray-900 mb-3 leading-tight;\n  }\n\n  &__content {\n    @apply space-y-3;\n  }\n\n  &__availability {\n    @apply text-sm text-gray-600;\n  }\n\n  &__text {\n    @apply text-sm text-gray-700 leading-relaxed;\n\n    &.expanded {\n      @apply block;\n    }\n  }\n\n  &__read-more {\n    @apply text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors;\n  }\n\n  &__video {\n    @apply mt-4;\n  }\n\n  &__video-thumbnail {\n    @apply relative bg-gray-100 rounded-lg overflow-hidden;\n\n    img {\n      @apply w-full h-auto;\n    }\n  }\n\n  &__play-btn {\n    @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 text-white hover:bg-opacity-40 transition-colors;\n  }\n\n  &__video-info {\n    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 text-white;\n  }\n\n  &__video-title {\n    @apply text-sm font-medium;\n  }\n\n  &__video-meta {\n    @apply flex items-center gap-2 mt-1 text-xs;\n  }\n\n  &__image {\n    @apply mt-4 rounded-lg overflow-hidden;\n\n    img {\n      @apply w-full h-auto;\n    }\n  }\n}\n\n// Idea Form Styles\n.feedlane-idea-form {\n  @apply p-4;\n\n  &__header {\n    @apply relative mb-6;\n\n    h3 {\n      @apply text-lg font-semibold text-gray-900 mb-2;\n    }\n\n    p {\n      @apply text-sm text-gray-600;\n    }\n  }\n\n  &__close {\n    @apply absolute top-0 right-0 text-gray-400 hover:text-gray-600 p-1 rounded transition-colors;\n  }\n\n  &__form {\n    @apply space-y-4;\n  }\n}\n\n.feedlane-form-group {\n  @apply space-y-1;\n\n  label {\n    @apply block text-xs font-bold text-gray-700 uppercase tracking-wide;\n  }\n\n  input,\n  textarea,\n  select {\n    @apply w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;\n  }\n\n  textarea {\n    @apply resize-none;\n  }\n}\n\n.feedlane-form-actions {\n  @apply flex items-center justify-between pt-4;\n}\n\n.feedlane-form-attach {\n  @apply p-2 text-gray-400 hover:text-gray-600 rounded transition-colors;\n}\n\n.feedlane-form-submit {\n  @apply px-6 py-2 bg-blue-600 text-white text-sm font-bold rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50;\n}\n\n.feedlane-form-footer {\n  @apply text-center pt-4 border-t border-gray-200;\n\n  span {\n    @apply text-xs text-gray-500;\n  }\n}\n\n// Suggest Button\n.feedlane-suggest-button {\n  @apply p-4 border-t border-gray-200;\n}\n\n.feedlane-suggest-btn {\n  @apply w-full py-3 bg-blue-600 text-white text-sm font-bold rounded-lg hover:bg-blue-700 transition-colors;\n}\n\n// Beamer Idea Styles\n.feedlane-beamer-idea {\n  @apply border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors;\n\n  &__header {\n    @apply flex gap-4;\n  }\n\n  &__vote {\n    @apply flex flex-col items-center;\n  }\n\n  &__vote-btn {\n    @apply p-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-800 transition-colors;\n\n    &.voted {\n      @apply bg-blue-100 border-blue-300 text-blue-600;\n    }\n  }\n\n  &__vote-count {\n    @apply text-sm font-medium text-gray-700 mt-1;\n  }\n\n  &__content {\n    @apply flex-1;\n  }\n\n  &__meta {\n    @apply flex items-center gap-4 mb-2;\n  }\n\n  &__category {\n    @apply text-xs font-bold;\n  }\n\n  &__comments {\n    @apply flex items-center gap-1 text-xs text-gray-500;\n  }\n\n  &__title {\n    @apply text-base font-semibold text-gray-900 mb-2;\n  }\n\n  &__status {\n    @apply flex items-center gap-2 mb-3;\n  }\n\n  &__status-indicator {\n    @apply w-2 h-2 rounded-full;\n  }\n\n  &__status-text {\n    @apply text-xs font-medium text-gray-600;\n  }\n\n  &__details {\n    @apply mb-3;\n\n    ul {\n      @apply space-y-1;\n    }\n\n    li {\n      @apply text-sm text-gray-700;\n\n      &:before {\n        content: \"• \";\n        @apply text-gray-400;\n      }\n    }\n  }\n}\n\n// Roadmap Styles\n.feedlane-roadmap {\n  &__tabs {\n    @apply flex border-b border-gray-200 bg-white;\n  }\n\n  &__tab {\n    @apply flex-1 flex items-center justify-center gap-2 py-3 px-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent transition-colors;\n\n    &.active {\n      @apply border-blue-600 text-blue-600;\n    }\n  }\n\n  &__tab-indicator {\n    @apply text-xs;\n  }\n\n  &__content {\n    @apply flex-1 overflow-y-auto;\n  }\n}\n\n.feedlane-roadmap-items {\n  @apply divide-y divide-gray-200;\n}\n\n.feedlane-beamer-roadmap-item {\n  @apply p-4 hover:bg-gray-50 transition-colors;\n\n  &__header {\n    @apply flex gap-4;\n  }\n\n  &__vote {\n    @apply flex flex-col items-center;\n  }\n\n  &__vote-btn {\n    @apply p-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-800 transition-colors;\n  }\n\n  &__vote-count {\n    @apply text-sm font-medium text-gray-700 mt-1;\n  }\n\n  &__content {\n    @apply flex-1;\n  }\n\n  &__title {\n    @apply text-base font-semibold text-gray-900 mb-2;\n  }\n\n  &__category {\n    @apply text-xs font-bold mb-3;\n  }\n\n  &__details {\n    @apply mb-3;\n\n    ul {\n      @apply space-y-1;\n    }\n\n    li {\n      @apply text-sm text-gray-700;\n\n      &:before {\n        content: \"• \";\n        @apply text-gray-400;\n      }\n    }\n  }\n\n  &__comments {\n    @apply flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700 transition-colors;\n  }\n}", "@tailwind base;\n", "@tailwind components;\n", "@tailwind utilities;\n"], "names": [], "sourceRoot": ""}