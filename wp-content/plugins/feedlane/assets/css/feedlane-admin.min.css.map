{"version": 3, "file": "css/feedlane-admin.min.css", "mappings": ";;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;CAAA;;;CAAA;;AAAA;;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;AAAA;;AAAA;;;;;;;;CAAA;;AAAA;;EAAA;EAAA;EAAA;EAAA;KAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;UAAA;AAAA;;AAAA;;CAAA;;AAAA;;;;;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;;;CAAA;;AAAA;;;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;;;;;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;;;;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;;;;;;;;;;;;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;CAAA;AAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;;;;;;;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;EAAA;AAAA;;AAAA;AAAA;EAAA;AAAA;;AAAA;EAAA;KAAA;UAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;UAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;UAAA;AAAA;;AAAA;EAAA;KAAA;UAAA;EAAA;EAAA;UAAA;EAAA;EAAA;EAAA;EAAA;KAAA;UAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;IAAA;OAAA;YAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;ACAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;ACAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;ACME;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;AAAA;;AAAA;;EAAA;IAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;IAAA;IAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AHhSN;EAAA,8BG+NC;EH/ND;AG+NC;AH/ND;EAAA,2GG+NC;EH/ND,yGG+NC;EH/ND;AG+NC;AH/ND;EAAA,oBG+NC;EH/ND;AG+NC;AH/ND;;EAAA;IAAA;EG+NC;AAAA;AH/ND;;EAAA;IAAA;EG+NC;AAAA,C", "sources": ["webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/base.css", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/components.css", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/utilities.css", "webpack://feedlane/./react-src/admin/scss/admin.scss"], "sourcesContent": ["@tailwind base;\n", "@tailwind components;\n", "@tailwind utilities;\n", "@import 'tailwindcss/base';\n@import 'tailwindcss/components';\n@import 'tailwindcss/utilities';\n\n// Admin Panel Styles\n.feedlane-admin {\n  @apply max-w-7xl mx-auto p-6;\n\n  &__header {\n    @apply mb-8;\n\n    h1 {\n      @apply text-3xl font-bold text-gray-900 mb-2;\n    }\n\n    p {\n      @apply text-lg text-gray-600;\n    }\n  }\n\n  &__content {\n    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;\n  }\n}\n\n// Loading States\n.feedlane-loading {\n  @apply flex flex-col items-center justify-center py-12 text-gray-500;\n\n  &__spinner {\n    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4;\n  }\n}\n\n// Form Styles\n.feedlane-form {\n  @apply space-y-8;\n\n  &__section {\n    @apply border-b border-gray-200 pb-8 last:border-b-0 last:pb-0;\n\n    h3 {\n      @apply text-xl font-semibold text-gray-900 mb-2;\n    }\n\n    > p {\n      @apply text-gray-600 mb-6;\n    }\n  }\n\n  &__field {\n    @apply space-y-2;\n\n    label {\n      @apply block text-sm font-medium text-gray-700;\n    }\n\n    .description {\n      @apply text-sm text-gray-500;\n    }\n  }\n\n  &__actions {\n    @apply flex justify-end pt-6 border-t border-gray-200;\n  }\n}\n\n// Enhanced Form Controls\n.feedlane-input {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n.feedlane-textarea {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n.feedlane-select {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n// Enhanced Color Picker\n.feedlane-color-picker {\n  @apply flex items-center gap-3;\n\n  .feedlane-color-input {\n    @apply w-12 h-10 border border-gray-300 rounded-lg cursor-pointer;\n\n    &::-webkit-color-swatch-wrapper {\n      @apply p-0;\n    }\n\n    &::-webkit-color-swatch {\n      @apply border-0 rounded-md;\n    }\n  }\n\n  .feedlane-color-text {\n    @apply flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n  }\n}\n\n// Enhanced Buttons\n.feedlane-btn {\n  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\n\n  &--primary {\n    @apply bg-blue-600 text-white shadow-sm hover:bg-blue-700 focus:ring-blue-500;\n  }\n\n  &--secondary {\n    @apply bg-white text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50 focus:ring-gray-500;\n  }\n\n  &--danger {\n    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500;\n  }\n\n  &--small {\n    @apply px-3 py-1.5 text-xs;\n  }\n\n  &--large {\n    @apply px-6 py-3 text-base;\n  }\n}\n\n// Spinner\n.feedlane-spinner {\n  @apply w-4 h-4 border-2 border-gray-200 border-t-current rounded-full animate-spin;\n\n  &--small {\n    @apply w-3 h-3 border;\n  }\n}\n\n// Tables\n.feedlane-table {\n  @apply w-full border-collapse bg-white shadow-sm rounded-lg overflow-hidden;\n\n  thead {\n    @apply bg-gray-50;\n\n    th {\n      @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;\n    }\n  }\n\n  tbody {\n    @apply divide-y divide-gray-200;\n\n    td {\n      @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;\n    }\n  }\n\n  &__actions {\n    @apply flex items-center gap-2;\n  }\n}\n\n// Cards\n.feedlane-card {\n  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;\n\n  &__header {\n    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;\n\n    h3 {\n      @apply text-lg font-medium text-gray-900;\n    }\n  }\n\n  &__content {\n    @apply p-6;\n  }\n\n  &__footer {\n    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;\n  }\n}\n\n// Badges\n.feedlane-badge {\n  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\n\n  &--success {\n    @apply bg-green-100 text-green-800;\n  }\n\n  &--warning {\n    @apply bg-yellow-100 text-yellow-800;\n  }\n\n  &--danger {\n    @apply bg-red-100 text-red-800;\n  }\n\n  &--info {\n    @apply bg-blue-100 text-blue-800;\n  }\n\n  &--gray {\n    @apply bg-gray-100 text-gray-800;\n  }\n}\n\n// Alerts\n.feedlane-alert {\n  @apply p-4 rounded-lg border;\n\n  &--success {\n    @apply bg-green-50 border-green-200 text-green-800;\n  }\n\n  &--warning {\n    @apply bg-yellow-50 border-yellow-200 text-yellow-800;\n  }\n\n  &--danger {\n    @apply bg-red-50 border-red-200 text-red-800;\n  }\n\n  &--info {\n    @apply bg-blue-50 border-blue-200 text-blue-800;\n  }\n}\n\n// Modal\n.feedlane-modal {\n  @apply fixed inset-0 z-50 overflow-y-auto;\n\n  &__backdrop {\n    @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity;\n  }\n\n  &__container {\n    @apply flex min-h-full items-center justify-center p-4;\n  }\n\n  &__content {\n    @apply bg-white rounded-lg shadow-xl max-w-lg w-full;\n  }\n\n  &__header {\n    @apply px-6 py-4 border-b border-gray-200;\n\n    h3 {\n      @apply text-lg font-medium text-gray-900;\n    }\n  }\n\n  &__body {\n    @apply p-6;\n  }\n\n  &__footer {\n    @apply px-6 py-4 border-t border-gray-200 flex justify-end gap-3;\n  }\n}\n\n// Pagination\n.feedlane-pagination {\n  @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;\n\n  &__info {\n    @apply text-sm text-gray-700;\n  }\n\n  &__nav {\n    @apply flex gap-2;\n  }\n\n  &__btn {\n    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;\n\n    &--active {\n      @apply bg-blue-600 text-white border-blue-600;\n    }\n  }\n}\n"], "names": [], "sourceRoot": ""}