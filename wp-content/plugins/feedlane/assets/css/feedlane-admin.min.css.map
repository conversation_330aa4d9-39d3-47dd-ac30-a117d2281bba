{"version": 3, "file": "css/feedlane-admin.min.css", "mappings": ";;;AAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;ACAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;ACOE;EACE;EACA;EACA;EACA;AAJJ;AAOE;EACE;AALJ;AASE;EACE;AAPJ;;AAaE;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;AAAA;;AAAA;EAAA;IAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EACA;AADA;AAMF;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EACA;AADA;AAIE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EACA;AADA;AAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EACA;AADA;AAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EACA;AADA;AAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;IAAA;IAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAOJ;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;IAAA;EAAA;AAAA;;AAAA;EAAA;IAAA;EAAA;AAAA;;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;AAAA;;AAMJ;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAGE;EAAA;AAHF;AAOE;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKF;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAKA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAIA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;EAAA;AAAA;;AAOJ;EAAA;EAAA;EAAA;EAAA;EACA;AADA;;AAKF;EACE;EACA;EACA;EACA;AAjGF;;AAoGA;EACE;EACA;EACA;EACA;AAjGF;;AAuGI;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMF;EACE;AAzGJ;;AA+GE;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAGE;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AFncJ;EAAA,kBEwVC;EFxVD;AEwVC;AFxVD;EAAA,kBEwVC;EFxVD;AEwVC;AFxVD;EAAA,kBEwVC;EFxVD;AEwVC;AFxVD;EAAA,kBEwVC;EFxVD;AEwVC;AFxVD;EAAA,oBEwVC;EFxVD;AEwVC;AFxVD;EAAA;AEwVC;AFxVD;EAAA,6EEwVC;EFxVD,iGEwVC;EFxVD;AEwVC;AFxVD;EAAA,sBEwVC;EFxVD;AEwVC;AFxVD;EAAA,2GEwVC;EFxVD,yGEwVC;EFxVD;AEwVC;AFxVD;EAAA,oBEwVC;EFxVD;AEwVC;AFxVD;EAAA;AEwVC;AFxVD;EAAA;AEwVC;AFxVD;EAAA;IAAA;EEwVC;AAAA,C", "sources": ["webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/components.css", "webpack://feedlane/./node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/utilities.css", "webpack://feedlane/./react-src/admin/scss/admin.scss"], "sourcesContent": ["@tailwind components;\n", "@tailwind utilities;\n", "// Only import Tailwind components and utilities, not the base reset\n@import 'tailwindcss/components';\n@import 'tailwindcss/utilities';\n\n// Scope all our styles to prevent conflicts with WordPress admin\n.feedlane-admin-wrapper {\n  // Apply Tailwind base styles only within our components\n  *, ::before, ::after {\n    box-sizing: border-box;\n    border-width: 0;\n    border-style: solid;\n    border-color: #e5e7eb;\n  }\n\n  ::before, ::after {\n    --tw-content: '';\n  }\n\n  // Ensure modals appear above WordPress admin elements\n  .feedlane-modal {\n    z-index: 999999;\n  }\n}\n\n// Admin Panel Styles\n.feedlane-admin {\n  @apply w-full p-6;\n\n  &__header {\n    @apply mb-8;\n\n    h1 {\n      @apply text-3xl font-bold text-gray-900 mb-2;\n    }\n\n    p {\n      @apply text-lg text-gray-600;\n    }\n  }\n\n  &__content {\n    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;\n  }\n}\n\n// Loading States\n.feedlane-loading {\n  @apply flex flex-col items-center justify-center py-12 text-gray-500;\n\n  &__spinner {\n    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4;\n  }\n}\n\n// Form Styles\n.feedlane-form {\n  @apply space-y-8;\n\n  &__section {\n    @apply border-b border-gray-200 pb-8 last:border-b-0 last:pb-0;\n\n    h3 {\n      @apply text-xl font-semibold text-gray-900 mb-2;\n    }\n\n    > p {\n      @apply text-gray-600 mb-6;\n    }\n  }\n\n  &__field {\n    @apply space-y-2;\n\n    label {\n      @apply block text-sm font-medium text-gray-700;\n    }\n\n    .description {\n      @apply text-sm text-gray-500;\n    }\n  }\n\n  &__actions {\n    @apply flex justify-end pt-6 border-t border-gray-200;\n  }\n}\n\n// Enhanced Form Controls\n.feedlane-input {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n.feedlane-textarea {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n.feedlane-select {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n\n  &:disabled {\n    @apply bg-gray-50 text-gray-500 cursor-not-allowed;\n  }\n}\n\n// Enhanced Color Picker\n.feedlane-color-picker {\n  @apply flex items-center gap-3;\n\n  .feedlane-color-input {\n    @apply w-12 h-10 border border-gray-300 rounded-lg cursor-pointer;\n\n    &::-webkit-color-swatch-wrapper {\n      @apply p-0;\n    }\n\n    &::-webkit-color-swatch {\n      @apply border-0 rounded-md;\n    }\n  }\n\n  .feedlane-color-text {\n    @apply flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;\n  }\n}\n\n// Enhanced Buttons\n.feedlane-btn {\n  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;\n\n  &--primary {\n    @apply bg-blue-600 text-white shadow-sm hover:bg-blue-700 focus:ring-blue-500;\n  }\n\n  &--secondary {\n    @apply bg-white text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50 focus:ring-gray-500;\n  }\n\n  &--danger {\n    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500;\n  }\n\n  &--small {\n    @apply px-3 py-1.5 text-xs;\n  }\n\n  &--large {\n    @apply px-6 py-3 text-base;\n  }\n}\n\n// Spinner\n.feedlane-spinner {\n  @apply w-4 h-4 border-2 border-gray-200 border-t-current rounded-full animate-spin;\n\n  &--small {\n    @apply w-3 h-3 border;\n  }\n}\n\n// Tables\n.feedlane-table {\n  @apply w-full border-collapse bg-white shadow-sm rounded-lg overflow-hidden min-w-max;\n\n  thead {\n    @apply bg-gray-50;\n\n    th {\n      @apply px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;\n    }\n  }\n\n  tbody {\n    @apply divide-y divide-gray-200;\n\n    td {\n      @apply px-4 py-4 text-sm text-gray-900;\n      /* Remove whitespace-nowrap to allow text wrapping */\n    }\n  }\n\n  &__actions {\n    @apply flex items-center gap-2;\n  }\n}\n\n// Cards\n.feedlane-card {\n  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto;\n\n  &__header {\n    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;\n\n    h3 {\n      @apply text-lg font-medium text-gray-900;\n    }\n  }\n\n  &__content {\n    @apply p-6;\n  }\n\n  &__footer {\n    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;\n  }\n}\n\n// Badges\n.feedlane-badge {\n  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\n\n  &--success {\n    @apply bg-green-100 text-green-800;\n  }\n\n  &--warning {\n    @apply bg-yellow-100 text-yellow-800;\n  }\n\n  &--danger {\n    @apply bg-red-100 text-red-800;\n  }\n\n  &--info {\n    @apply bg-blue-100 text-blue-800;\n  }\n\n  &--gray {\n    @apply bg-gray-100 text-gray-800;\n  }\n}\n\n// Alerts\n.feedlane-alert {\n  @apply p-4 rounded-lg border;\n\n  &--success {\n    @apply bg-green-50 border-green-200 text-green-800;\n  }\n\n  &--warning {\n    @apply bg-yellow-50 border-yellow-200 text-yellow-800;\n  }\n\n  &--danger {\n    @apply bg-red-50 border-red-200 text-red-800;\n  }\n\n  &--info {\n    @apply bg-blue-50 border-blue-200 text-blue-800;\n  }\n}\n\n// Modal\n.feedlane-modal {\n  @apply fixed inset-0 overflow-y-auto;\n  z-index: 999999; // Ensure it's above WordPress admin elements\n\n  &__backdrop {\n    @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity;\n    z-index: 999999;\n  }\n\n  &__container {\n    @apply flex min-h-full items-center justify-center p-4 relative;\n    z-index: 1000000; // Higher than backdrop\n  }\n\n  &__content {\n    @apply bg-white rounded-lg shadow-xl max-w-lg w-full relative;\n    z-index: 1000001; // Highest z-index for content\n  }\n\n  &__header {\n    @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;\n\n    h3 {\n      @apply text-lg font-medium text-gray-900;\n    }\n  }\n\n  &__close {\n    @apply ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8;\n\n    &:hover {\n      @apply bg-gray-100 text-gray-900;\n    }\n\n    &:focus {\n      @apply outline-none ring-2 ring-gray-300;\n    }\n  }\n\n  &__body {\n    @apply p-6;\n  }\n\n  &__footer {\n    @apply px-6 py-4 border-t border-gray-200 flex justify-end gap-3;\n  }\n}\n\n// Pagination\n.feedlane-pagination {\n  @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;\n\n  &__info {\n    @apply text-sm text-gray-700;\n  }\n\n  &__nav {\n    @apply flex gap-2;\n  }\n\n  &__btn {\n    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;\n\n    &--active {\n      @apply bg-blue-600 text-white border-blue-600;\n    }\n  }\n}\n\n// Roadmap Board\n.feedlane-roadmap-board {\n  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;\n}\n\n.feedlane-roadmap-column {\n  @apply bg-gray-50 rounded-lg p-4 min-h-96;\n\n  &__header {\n    @apply flex items-center gap-3 mb-4 pb-3 border-b border-gray-200;\n\n    h3 {\n      @apply text-lg font-semibold text-gray-900 flex-1;\n    }\n  }\n\n  &__indicator {\n    @apply w-3 h-3 rounded-full;\n  }\n\n  &__count {\n    @apply bg-gray-200 text-gray-700 text-xs font-medium px-2 py-1 rounded-full;\n  }\n\n  &__content {\n    @apply space-y-3;\n  }\n\n  &__empty {\n    @apply text-center py-8 text-gray-500;\n\n    p {\n      @apply text-sm;\n    }\n  }\n}\n\n.feedlane-roadmap-card {\n  @apply bg-white rounded-lg p-4 shadow-sm border border-gray-200 cursor-move transition-all duration-200 hover:shadow-md;\n\n  &:hover {\n    @apply transform -translate-y-1;\n  }\n\n  &__header {\n    @apply mb-3;\n\n    h4 {\n      @apply text-sm font-semibold text-gray-900 mb-2 line-clamp-2;\n    }\n  }\n\n  &__meta {\n    @apply flex items-center gap-3 text-xs text-gray-500;\n  }\n\n  &__votes,\n  &__comments {\n    @apply flex items-center gap-1;\n  }\n\n  &__excerpt {\n    @apply text-sm text-gray-600 mb-3 line-clamp-3;\n  }\n\n  &__category {\n    @apply flex items-center gap-2;\n\n    .feedlane-badge {\n      @apply text-xs text-white;\n    }\n  }\n}\n\n// Trello-style Roadmap Enhancements\n.feedlane-roadmap-board {\n  @apply flex gap-6 overflow-x-auto pb-6;\n  min-height: 600px;\n}\n\n// Line clamp utility for text truncation\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.line-clamp-3 {\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n// Drag and drop visual feedback\n.feedlane-roadmap-card {\n  &.dragging {\n    @apply opacity-50 rotate-3 scale-105;\n  }\n}\n\n// Column drop zone styling\n.feedlane-roadmap-column {\n  &.drag-over {\n    @apply bg-blue-50 border-2 border-blue-300 border-dashed;\n  }\n}\n\n// Enhanced modal for larger content\n.feedlane-modal__content {\n  &.max-w-2xl {\n    max-width: 42rem;\n  }\n}\n\n// Loading spinner for roadmap\n.feedlane-loading {\n  @apply flex flex-col items-center justify-center py-12;\n\n  &__spinner {\n    @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;\n  }\n}\n"], "names": [], "sourceRoot": ""}