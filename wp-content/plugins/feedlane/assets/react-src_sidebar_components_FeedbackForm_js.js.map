{"version": 3, "file": "react-src_sidebar_components_FeedbackForm_js.js", "mappings": ";;;;;;;;;;;;;;;AAAiC;;AAEjC;AACA;AACA;AACO,MAAMC,cAAc,GAAG,MAAAA,CAAOC,MAAM,EAAEC,YAAY,KAAK;EAC1D,OAAOH,+CAAS,CAACI,IAAI,CAAC,WAAW,EAAE;IAC/BC,OAAO,EAAEH,MAAM;IACf,GAAGC;EACP,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACO,MAAMG,aAAa,GAAG,MAAAA,CAAOJ,MAAM,EAAEK,MAAM,GAAG,CAAC,CAAC,KAAK;EACxD,MAAMC,aAAa,GAAG;IAClBC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACd,CAAC;EAED,MAAMC,WAAW,GAAG;IAAE,GAAGH,aAAa;IAAE,GAAGD;EAAO,CAAC;EAEnD,OAAOP,+CAAS,CAACY,GAAG,CAAC,aAAaV,MAAM,EAAE,EAAES,WAAW,CAAC;AAC5D,CAAC;;;;;;;;;;;;;;;;;;;;;;;ACxBuC;AACsC;AACO;AACjD;AAC4B;AAEhE,MAAMY,YAAY,GAAGA,CAAC;EAAErB;AAAO,CAAC,KAAK;EACjC,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGX,+CAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,+CAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,+CAAQ,CAAC;IACrCgB,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGhB,qEAAc,CAAC,CAAC;EACpC,MAAMiB,UAAU,GAAGC,MAAM,CAACC,YAAY,EAAEC,iBAAiB,IAAI,KAAK;EAClE,MAAMC,uBAAuB,GAAGH,MAAM,CAACC,YAAY,EAAEG,QAAQ,EAAEC,wBAAwB,IAAI,KAAK;;EAEhG;EACA,MAAM;IACFC,IAAI,EAAEtC,YAAY;IAClBuC,SAAS,EAAEC;EACf,CAAC,GAAG3B,+DAAQ,CAAC;IACT4B,QAAQ,EAAE,CAAC,UAAU,EAAE1C,MAAM,CAAC;IAC9B2C,OAAO,EAAEA,CAAA,KAAMvC,4DAAa,CAACJ,MAAM,CAAC;IACpC4C,OAAO,EAAEpB,YAAY;IACrBqB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG;EACxB,CAAC,CAAC;;EAEF;EACA,MAAMC,cAAc,GAAGjC,kEAAW,CAAC;IAC/BkC,UAAU,EAAGR,IAAI,IAAKxC,6DAAc,CAACC,MAAM,EAAEuC,IAAI,CAAC;IAClDS,SAAS,EAAEA,CAAA,KAAM;MACb5B,uDAAK,CAAC6B,OAAO,CAAC,kCAAkC,CAAC;MACjDtB,WAAW,CAAC;QACRC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE,EAAE;QACbC,UAAU,EAAE;MAChB,CAAC,CAAC;MACFP,aAAa,CAAC,KAAK,CAAC;;MAEpB;MACAQ,WAAW,CAACmB,iBAAiB,CAAC,CAAC,UAAU,EAAElD,MAAM,CAAC,CAAC;IACvD,CAAC;IACDmD,OAAO,EAAGC,KAAK,IAAK;MAChB,MAAMC,OAAO,GAAGD,KAAK,CAACE,QAAQ,EAAEf,IAAI,EAAEc,OAAO,IAAI,2BAA2B;MAC5EjC,uDAAK,CAACgC,KAAK,CAACC,OAAO,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAME,YAAY,GAAIC,CAAC,IAAK;IACxBA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC/B,QAAQ,CAACE,aAAa,CAAC8B,IAAI,CAAC,CAAC,EAAE;MAChCtC,uDAAK,CAACgC,KAAK,CAAC,4BAA4B,CAAC;MACzC;IACJ;;IAEA;IACA,IAAI,CAACpB,UAAU,IAAII,uBAAuB,EAAE;MACxC,IAAI,CAACV,QAAQ,CAACG,SAAS,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAChC,QAAQ,CAACI,UAAU,CAAC4B,IAAI,CAAC,CAAC,EAAE;QAC3DtC,uDAAK,CAACgC,KAAK,CAAC,6BAA6B,CAAC;QAC1C;MACJ;MAEA,IAAI,CAACO,YAAY,CAACjC,QAAQ,CAACI,UAAU,CAAC,EAAE;QACpCV,uDAAK,CAACgC,KAAK,CAAC,oCAAoC,CAAC;QACjD;MACJ;IACJ;IAEAN,cAAc,CAACc,MAAM,CAAClC,QAAQ,CAAC;EACnC,CAAC;EAED,MAAMmC,iBAAiB,GAAIL,CAAC,IAAK;IAC7B,MAAM;MAAEM,IAAI;MAAEC;IAAM,CAAC,GAAGP,CAAC,CAACQ,MAAM;IAChCrC,WAAW,CAACsC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMJ,YAAY,GAAIO,KAAK,IAAK;IAC5B,OAAO,4BAA4B,CAACC,IAAI,CAACD,KAAK,CAAC;EACnD,CAAC;EAED,MAAME,iBAAiB,GAAGpC,UAAU,IAAII,uBAAuB;EAE/D,IAAI,CAACgC,iBAAiB,EAAE;IACpB,OAAO,IAAI;EACf;EAEA,OACIC,oDAAA;IAAKC,SAAS,EAAC;EAAmB,GAE9BD,oDAAA;IAAKC,SAAS,EAAC;EAA2B,GACtCD,oDAAA;IACIE,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAACD,UAAU,CAAE;IAC1CgD,SAAS,EAAC;EAAkC,GAE5CD,oDAAA,CAACpD,2DAAe;IAACuD,IAAI,EAAE;EAAG,CAAE,CAAC,EAC7BH,oDAAA,eAAM,gBAAoB,CAAC,EAC1B/C,UAAU,GAAG+C,oDAAA,CAAClD,uDAAW;IAACqD,IAAI,EAAE;EAAG,CAAE,CAAC,GAAGH,oDAAA,CAACnD,yDAAa;IAACsD,IAAI,EAAE;EAAG,CAAE,CAChE,CAAC,EAERvE,YAAY,IAAIA,YAAY,CAACwE,UAAU,CAACC,KAAK,GAAG,CAAC,IAC9CL,oDAAA;IACIE,OAAO,EAAEA,CAAA,KAAM9C,eAAe,CAAC,CAACD,YAAY,CAAE;IAC9C8C,SAAS,EAAC;EAAgC,GAC7C,OACQ,EAACrE,YAAY,CAACwE,UAAU,CAACC,KAAK,EAAC,WACpC,EAAClD,YAAY,GAAG6C,oDAAA,CAAClD,uDAAW;IAACqD,IAAI,EAAE;EAAG,CAAE,CAAC,GAAGH,oDAAA,CAACnD,yDAAa;IAACsD,IAAI,EAAE;EAAG,CAAE,CAClE,CAEX,CAAC,EAGLlD,UAAU,IACP+C,oDAAA;IAAMM,QAAQ,EAAEpB,YAAa;IAACe,SAAS,EAAC;EAAyB,GAE5D,CAACtC,UAAU,IAAII,uBAAuB,IACnCiC,oDAAA;IAAKC,SAAS,EAAC;EAAiC,GAC5CD,oDAAA;IAAKC,SAAS,EAAC;EAAmB,GAC9BD,oDAAA;IACIO,IAAI,EAAC,MAAM;IACXd,IAAI,EAAC,WAAW;IAChBC,KAAK,EAAErC,QAAQ,CAACG,SAAU;IAC1BgD,QAAQ,EAAEhB,iBAAkB;IAC5BiB,WAAW,EAAC,WAAW;IACvBR,SAAS,EAAC,sCAAsC;IAChDS,QAAQ;EAAA,CACX,CAAC,EACFV,oDAAA;IACIO,IAAI,EAAC,OAAO;IACZd,IAAI,EAAC,YAAY;IACjBC,KAAK,EAAErC,QAAQ,CAACI,UAAW;IAC3B+C,QAAQ,EAAEhB,iBAAkB;IAC5BiB,WAAW,EAAC,YAAY;IACxBR,SAAS,EAAC,sCAAsC;IAChDS,QAAQ;EAAA,CACX,CACA,CACJ,CACR,EAGDV,oDAAA;IAAKC,SAAS,EAAC;EAA+B,GAC1CD,oDAAA;IACIP,IAAI,EAAC,eAAe;IACpBC,KAAK,EAAErC,QAAQ,CAACE,aAAc;IAC9BiD,QAAQ,EAAEhB,iBAAkB;IAC5BiB,WAAW,EAAC,0CAA0C;IACtDR,SAAS,EAAC,mBAAmB;IAC7BU,IAAI,EAAC,GAAG;IACRD,QAAQ;EAAA,CACX,CACA,CAAC,EAGNV,oDAAA;IAAKC,SAAS,EAAC;EAA4B,GACvCD,oDAAA;IACIO,IAAI,EAAC,QAAQ;IACbL,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,KAAK,CAAE;IACpC+C,SAAS,EAAC;EAAmE,GAChF,QAEO,CAAC,EACTD,oDAAA;IACIO,IAAI,EAAC,QAAQ;IACbK,QAAQ,EAAEnC,cAAc,CAACoC,SAAU;IACnCZ,SAAS,EAAC;EAAiE,GAE1ExB,cAAc,CAACoC,SAAS,GACrBb,oDAAA,CAAAc,2CAAA,QACId,oDAAA;IAAKC,SAAS,EAAC;EAA0C,CAAM,CAAC,cAElE,CAAC,GAEHD,oDAAA,CAAAc,2CAAA,QACId,oDAAA,CAACrD,kDAAM;IAACwD,IAAI,EAAE;EAAG,CAAE,CAAC,iBAEtB,CAEF,CACP,CACH,CACT,EAGAhD,YAAY,IACT6C,oDAAA;IAAKC,SAAS,EAAC;EAAyB,GACnC7B,eAAe,GACZ4B,oDAAA;IAAKC,SAAS,EAAC;EAA4B,GACvCD,oDAAA;IAAKC,SAAS,EAAC;EAAkB,CAAM,CAAC,EACxCD,oDAAA,eAAM,qBAAyB,CAC9B,CAAC,GACNpE,YAAY,IAAIA,YAAY,CAACmF,QAAQ,CAACC,MAAM,GAAG,CAAC,GAChDhB,oDAAA;IAAKC,SAAS,EAAC;EAA0B,GACpCrE,YAAY,CAACmF,QAAQ,CAACE,GAAG,CAACC,IAAI,IAC3BlB,oDAAA;IAAKmB,GAAG,EAAED,IAAI,CAACE,EAAG;IAACnB,SAAS,EAAC;EAAyB,GAClDD,oDAAA;IAAKC,SAAS,EAAC;EAAgC,GAC3CD,oDAAA;IAAMC,SAAS,EAAC;EAA2B,GACtCiB,IAAI,CAACG,MAAM,CAAC5B,IACX,CAAC,EACPO,oDAAA;IAAMC,SAAS,EAAC;EAAyB,GACpCiB,IAAI,CAACI,QACJ,CACL,CAAC,EACNtB,oDAAA;IAAKC,SAAS,EAAC;EAAiC,GAC3CiB,IAAI,CAAC3D,aACL,CACJ,CACR,CACA,CAAC,GAENyC,oDAAA;IAAKC,SAAS,EAAC;EAA0B,GACrCD,oDAAA,YAAG,uDAAwD,CAC1D,CAER,CAER,CAAC;AAEd,CAAC;AAED,iEAAehD,YAAY", "sources": ["webpack://feedlane/./react-src/sidebar/api/feedback.js", "webpack://feedlane/./react-src/sidebar/components/FeedbackForm.js"], "sourcesContent": ["import apiClient from './client';\n\n/**\n * Submit feedback for a post\n */\nexport const submitFeedback = async (postId, feedbackData) => {\n    return apiClient.post('/feedback', {\n        post_id: postId,\n        ...feedbackData,\n    });\n};\n\n/**\n * Fetch feedback for a post\n */\nexport const fetchFeedback = async (postId, params = {}) => {\n    const defaultParams = {\n        page: 1,\n        per_page: 10,\n    };\n    \n    const queryParams = { ...defaultParams, ...params };\n    \n    return apiClient.get(`/feedback/${postId}`, queryParams);\n};\n", "import React, { useState } from 'react';\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { FiSend, FiMessageCircle, FiChevronDown, FiChevronUp } from 'react-icons/fi';\nimport toast from 'react-hot-toast';\nimport { submitFeedback, fetchFeedback } from '../api/feedback';\n\nconst FeedbackForm = ({ postId }) => {\n    const [isExpanded, setIsExpanded] = useState(false);\n    const [showFeedback, setShowFeedback] = useState(false);\n    const [formData, setFormData] = useState({\n        feedback_text: '',\n        user_name: '',\n        user_email: ''\n    });\n    \n    const queryClient = useQueryClient();\n    const isLoggedIn = window.feedlaneData?.is_user_logged_in || false;\n    const guestSubmissionsEnabled = window.feedlaneData?.settings?.enable_guest_submissions || false;\n    \n    // Fetch existing feedback\n    const {\n        data: feedbackData,\n        isLoading: feedbackLoading\n    } = useQuery({\n        queryKey: ['feedback', postId],\n        queryFn: () => fetchFeedback(postId),\n        enabled: showFeedback,\n        staleTime: 2 * 60 * 1000,\n    });\n    \n    // Submit feedback mutation\n    const submitMutation = useMutation({\n        mutationFn: (data) => submitFeedback(postId, data),\n        onSuccess: () => {\n            toast.success('Feedback submitted successfully!');\n            setFormData({\n                feedback_text: '',\n                user_name: '',\n                user_email: ''\n            });\n            setIsExpanded(false);\n            \n            // Invalidate feedback query to refresh the list\n            queryClient.invalidateQueries(['feedback', postId]);\n        },\n        onError: (error) => {\n            const message = error.response?.data?.message || 'Failed to submit feedback';\n            toast.error(message);\n        },\n    });\n    \n    const handleSubmit = (e) => {\n        e.preventDefault();\n        \n        if (!formData.feedback_text.trim()) {\n            toast.error('Please enter your feedback');\n            return;\n        }\n        \n        // Validate guest user fields\n        if (!isLoggedIn && guestSubmissionsEnabled) {\n            if (!formData.user_name.trim() || !formData.user_email.trim()) {\n                toast.error('Name and email are required');\n                return;\n            }\n            \n            if (!isValidEmail(formData.user_email)) {\n                toast.error('Please enter a valid email address');\n                return;\n            }\n        }\n        \n        submitMutation.mutate(formData);\n    };\n    \n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setFormData(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n    \n    const isValidEmail = (email) => {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n    };\n    \n    const canSubmitFeedback = isLoggedIn || guestSubmissionsEnabled;\n    \n    if (!canSubmitFeedback) {\n        return null;\n    }\n    \n    return (\n        <div className=\"feedlane-feedback\">\n            {/* Toggle Button */}\n            <div className=\"feedlane-feedback__toggle\">\n                <button\n                    onClick={() => setIsExpanded(!isExpanded)}\n                    className=\"feedlane-feedback__toggle-button\"\n                >\n                    <FiMessageCircle size={16} />\n                    <span>Leave Feedback</span>\n                    {isExpanded ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}\n                </button>\n                \n                {feedbackData && feedbackData.pagination.total > 0 && (\n                    <button\n                        onClick={() => setShowFeedback(!showFeedback)}\n                        className=\"feedlane-feedback__view-button\"\n                    >\n                        View {feedbackData.pagination.total} feedback\n                        {showFeedback ? <FiChevronUp size={14} /> : <FiChevronDown size={14} />}\n                    </button>\n                )}\n            </div>\n            \n            {/* Feedback Form */}\n            {isExpanded && (\n                <form onSubmit={handleSubmit} className=\"feedlane-feedback__form\">\n                    {/* Guest user fields */}\n                    {!isLoggedIn && guestSubmissionsEnabled && (\n                        <div className=\"feedlane-feedback__guest-fields\">\n                            <div className=\"feedlane-form-row\">\n                                <input\n                                    type=\"text\"\n                                    name=\"user_name\"\n                                    value={formData.user_name}\n                                    onChange={handleInputChange}\n                                    placeholder=\"Your name\"\n                                    className=\"feedlane-input feedlane-input--small\"\n                                    required\n                                />\n                                <input\n                                    type=\"email\"\n                                    name=\"user_email\"\n                                    value={formData.user_email}\n                                    onChange={handleInputChange}\n                                    placeholder=\"Your email\"\n                                    className=\"feedlane-input feedlane-input--small\"\n                                    required\n                                />\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Feedback text */}\n                    <div className=\"feedlane-feedback__text-field\">\n                        <textarea\n                            name=\"feedback_text\"\n                            value={formData.feedback_text}\n                            onChange={handleInputChange}\n                            placeholder=\"Share your thoughts about this update...\"\n                            className=\"feedlane-textarea\"\n                            rows=\"3\"\n                            required\n                        />\n                    </div>\n                    \n                    {/* Submit button */}\n                    <div className=\"feedlane-feedback__actions\">\n                        <button\n                            type=\"button\"\n                            onClick={() => setIsExpanded(false)}\n                            className=\"feedlane-button feedlane-button--secondary feedlane-button--small\"\n                        >\n                            Cancel\n                        </button>\n                        <button\n                            type=\"submit\"\n                            disabled={submitMutation.isPending}\n                            className=\"feedlane-button feedlane-button--primary feedlane-button--small\"\n                        >\n                            {submitMutation.isPending ? (\n                                <>\n                                    <div className=\"feedlane-spinner feedlane-spinner--small\"></div>\n                                    Sending...\n                                </>\n                            ) : (\n                                <>\n                                    <FiSend size={14} />\n                                    Send Feedback\n                                </>\n                            )}\n                        </button>\n                    </div>\n                </form>\n            )}\n            \n            {/* Existing Feedback */}\n            {showFeedback && (\n                <div className=\"feedlane-feedback__list\">\n                    {feedbackLoading ? (\n                        <div className=\"feedlane-feedback__loading\">\n                            <div className=\"feedlane-spinner\"></div>\n                            <span>Loading feedback...</span>\n                        </div>\n                    ) : feedbackData && feedbackData.feedback.length > 0 ? (\n                        <div className=\"feedlane-feedback__items\">\n                            {feedbackData.feedback.map(item => (\n                                <div key={item.id} className=\"feedlane-feedback__item\">\n                                    <div className=\"feedlane-feedback__item-header\">\n                                        <span className=\"feedlane-feedback__author\">\n                                            {item.author.name}\n                                        </span>\n                                        <span className=\"feedlane-feedback__time\">\n                                            {item.time_ago}\n                                        </span>\n                                    </div>\n                                    <div className=\"feedlane-feedback__item-content\">\n                                        {item.feedback_text}\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    ) : (\n                        <div className=\"feedlane-feedback__empty\">\n                            <p>No feedback yet. Be the first to share your thoughts!</p>\n                        </div>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default FeedbackForm;\n"], "names": ["apiClient", "submitFeedback", "postId", "feedbackData", "post", "post_id", "fetchFeedback", "params", "defaultParams", "page", "per_page", "queryParams", "get", "React", "useState", "useMutation", "useQuery", "useQueryClient", "FiSend", "FiMessageCircle", "FiChevronDown", "FiChevronUp", "toast", "FeedbackForm", "isExpanded", "setIsExpanded", "showFeedback", "setShowFeedback", "formData", "setFormData", "feedback_text", "user_name", "user_email", "queryClient", "isLoggedIn", "window", "feedlaneData", "is_user_logged_in", "guestSubmissions<PERSON><PERSON><PERSON>", "settings", "enable_guest_submissions", "data", "isLoading", "feedbackLoading", "query<PERSON><PERSON>", "queryFn", "enabled", "staleTime", "submitMutation", "mutationFn", "onSuccess", "success", "invalidateQueries", "onError", "error", "message", "response", "handleSubmit", "e", "preventDefault", "trim", "isValidEmail", "mutate", "handleInputChange", "name", "value", "target", "prev", "email", "test", "canSubmitFeedback", "createElement", "className", "onClick", "size", "pagination", "total", "onSubmit", "type", "onChange", "placeholder", "required", "rows", "disabled", "isPending", "Fragment", "feedback", "length", "map", "item", "key", "id", "author", "time_ago"], "sourceRoot": ""}