{"version": 3, "file": "react-src_sidebar_components_VotingButton_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAA0B;AAC0C;AACvB;AACT;AACI;AAExC,MAAMM,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ,GAAG,KAAK;EAAEC,SAAS,GAAG;AAAE,CAAC,KAAK;EAClE,MAAMC,WAAW,GAAGR,qEAAc,CAAC,CAAC;EAEpC,MAAMS,YAAY,GAAGV,kEAAW,CAAC;IAC7BW,UAAU,EAAEA,CAAA,KAAMP,oDAAQ,CAACE,MAAM,CAAC;IAClCM,SAAS,EAAGC,IAAI,IAAK;MACjB;MACAJ,WAAW,CAACK,YAAY,CAAC,CAAC,OAAO,CAAC,EAAGC,OAAO,IAAK;QAC7C,IAAI,CAACA,OAAO,EAAE,OAAOA,OAAO;QAE5B,OAAO;UACH,GAAGA,OAAO;UACVC,KAAK,EAAED,OAAO,CAACC,KAAK,CAACC,GAAG,CAACC,IAAI,IACzBA,IAAI,CAACC,EAAE,KAAKb,MAAM,GACZ;YACE,GAAGY,IAAI;YACPE,UAAU,EAAEP,IAAI,CAACO,UAAU;YAC3BC,SAAS,EAAE;UACb,CAAC,GACDH,IACV;QACJ,CAAC;MACL,CAAC,CAAC;;MAEF;MACAT,WAAW,CAACK,YAAY,CAAC,CAAC,MAAM,EAAER,MAAM,CAAC,EAAGS,OAAO,IAAK;QACpD,IAAI,CAACA,OAAO,EAAE,OAAOA,OAAO;QAE5B,OAAO;UACH,GAAGA,OAAO;UACVK,UAAU,EAAEP,IAAI,CAACO,UAAU;UAC3BC,SAAS,EAAE;QACf,CAAC;MACL,CAAC,CAAC;MAEFlB,uDAAK,CAACmB,OAAO,CAAC,gBAAgB,CAAC;IACnC,CAAC;IACDC,OAAO,EAAGC,KAAK,IAAK;MAChB,MAAMC,OAAO,GAAGD,KAAK,CAACE,QAAQ,EAAEb,IAAI,EAAEY,OAAO,IAAI,gBAAgB;MACjEtB,uDAAK,CAACqB,KAAK,CAACC,OAAO,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAME,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIpB,QAAQ,EAAE;MACVJ,uDAAK,CAACyB,IAAI,CAAC,qCAAqC,CAAC;MACjD;IACJ;IAEAlB,YAAY,CAACmB,MAAM,CAAC,CAAC;EACzB,CAAC;EAED,OACIC,oDAAA;IACIC,OAAO,EAAEJ,UAAW;IACpBK,QAAQ,EAAEtB,YAAY,CAACuB,SAAS,IAAI1B,QAAS;IAC7C2B,SAAS,EAAE,wBAAwB3B,QAAQ,GAAG,6BAA6B,GAAG,EAAE,EAAG;IACnF4B,KAAK,EAAE5B,QAAQ,GAAG,gBAAgB,GAAG;EAAqB,GAEzDG,YAAY,CAACuB,SAAS,GACnBH,oDAAA;IAAKI,SAAS,EAAC;EAA0C,CAAM,CAAC,GAEhEJ,oDAAA,CAAC5B,uDAAW;IAACkC,IAAI,EAAE;EAAG,CAAE,CAC3B,EACDN,oDAAA;IAAMI,SAAS,EAAC;EAA6B,GACxC1B,SACC,CACF,CAAC;AAEjB,CAAC;AAED,iEAAeH,YAAY", "sources": ["webpack://feedlane/./react-src/sidebar/components/VotingButton.js"], "sourcesContent": ["import React from 'react';\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\nimport { FiChevronUp } from 'react-icons/fi';\nimport toast from 'react-hot-toast';\nimport { voteIdea } from '../api/ideas';\n\nconst VotingButton = ({ ideaId, hasVoted = false, voteCount = 0 }) => {\n    const queryClient = useQueryClient();\n    \n    const voteMutation = useMutation({\n        mutationFn: () => voteIdea(ideaId),\n        onSuccess: (data) => {\n            // Update the ideas query cache\n            queryClient.setQueryData(['ideas'], (oldData) => {\n                if (!oldData) return oldData;\n                \n                return {\n                    ...oldData,\n                    ideas: oldData.ideas.map(idea => \n                        idea.id === ideaId \n                            ? { \n                                ...idea, \n                                vote_count: data.vote_count,\n                                has_voted: true \n                              }\n                            : idea\n                    )\n                };\n            });\n            \n            // Also update individual idea cache if it exists\n            queryClient.setQueryData(['idea', ideaId], (oldData) => {\n                if (!oldData) return oldData;\n                \n                return {\n                    ...oldData,\n                    vote_count: data.vote_count,\n                    has_voted: true\n                };\n            });\n            \n            toast.success('Vote recorded!');\n        },\n        onError: (error) => {\n            const message = error.response?.data?.message || 'Failed to vote';\n            toast.error(message);\n        },\n    });\n    \n    const handleVote = () => {\n        if (hasVoted) {\n            toast.info('You have already voted on this idea');\n            return;\n        }\n        \n        voteMutation.mutate();\n    };\n    \n    return (\n        <button\n            onClick={handleVote}\n            disabled={voteMutation.isPending || hasVoted}\n            className={`feedlane-vote-button ${hasVoted ? 'feedlane-vote-button--voted' : ''}`}\n            title={hasVoted ? 'You have voted' : 'Vote for this idea'}\n        >\n            {voteMutation.isPending ? (\n                <div className=\"feedlane-spinner feedlane-spinner--small\"></div>\n            ) : (\n                <FiChevronUp size={16} />\n            )}\n            <span className=\"feedlane-vote-button__count\">\n                {voteCount}\n            </span>\n        </button>\n    );\n};\n\nexport default VotingButton;\n"], "names": ["React", "useMutation", "useQueryClient", "FiChevronUp", "toast", "voteIdea", "VotingButton", "ideaId", "hasVoted", "voteCount", "queryClient", "voteMutation", "mutationFn", "onSuccess", "data", "setQueryData", "oldData", "ideas", "map", "idea", "id", "vote_count", "has_voted", "success", "onError", "error", "message", "response", "handleVote", "info", "mutate", "createElement", "onClick", "disabled", "isPending", "className", "title", "size"], "sourceRoot": ""}