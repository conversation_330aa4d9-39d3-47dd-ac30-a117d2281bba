(()=>{"use strict";var e,t,r={44:(e,t,r)=>{r.d(t,{A:()=>n});const a=window.feedlaneData?.rest_url||"/wp-json/",s=window.feedlaneData?.nonce||"",n=new class{constructor(){this.baseURL=a,this.nonce=s}async request(e,t={}){const r=`${this.baseURL}feedlane/v1${e}`,a={headers:{"Content-Type":"application/json","X-WP-Nonce":this.nonce}},s={...a,...t,headers:{...a.headers,...t.headers}};try{const e=await fetch(r,s);if(!e.ok){const t=await e.json().catch((()=>({})));throw new Error(t.message||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw e}}async get(e,t={}){const r=new URLSearchParams(t).toString(),a=r?`${e}?${r}`:e;return this.request(a,{method:"GET"})}async post(e,t={}){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t={}){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}async postFormData(e,t){return this.request(e,{method:"POST",headers:{"X-WP-Nonce":this.nonce},body:t})}}},69:(e,t,r)=>{r.d(t,{AH:()=>m,I4:()=>g,i7:()=>b,mj:()=>v});let a={data:""},s=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||a,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,i=/\/\*[^]*?\*\/|  +/g,o=/\n+/g,l=(e,t)=>{let r="",a="",s="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?r=n+" "+i+";":a+="f"==n[1]?l(i,n):n+"{"+l(i,"k"==n[1]?"":t)+"}":"object"==typeof i?a+=l(i,t?t.replace(/([^,])+/g,(e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=l.p?l.p(n,i):n+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},c={},u=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+u(e[r]);return t}return e},d=(e,t,r,a,s)=>{let d=u(e),h=c[d]||(c[d]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(d));if(!c[h]){let t=d!==e?e:(e=>{let t,r,a=[{}];for(;t=n.exec(e.replace(i,""));)t[4]?a.shift():t[3]?(r=t[3].replace(o," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(o," ").trim();return a[0]})(e);c[h]=l(s?{["@keyframes "+h]:t}:t,r?"":"."+h)}let m=r&&c.g?c.g:null;return r&&(c.g=c[h]),((e,t,r,a)=>{a?t.data=t.data.replace(a,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[h],t,a,m),h},h=(e,t,r)=>e.reduce(((e,a,s)=>{let n=t[s];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":l(e,""):!1===e?"":e}return e+a+(null==n?"":n)}),"");function m(e){let t=this||{},r=e.call?e(t.p):e;return d(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce(((e,r)=>Object.assign(e,r&&r.call?r(t.p):r)),{}):r,s(t.target),t.g,t.o,t.k)}m.bind({g:1});let p,f,y,b=m.bind({k:1});function v(e,t,r,a){l.p=t,p=e,f=r,y=a}function g(e,t){let r=this||{};return function(){let a=arguments;function s(n,i){let o=Object.assign({},n),l=o.className||s.className;r.p=Object.assign({theme:f&&f()},o),r.o=/ *go\d+/.test(l),o.className=m.apply(r,a)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),y&&c[0]&&y(o),p(c,o)}return t?t(s):s}}},70:(e,t,r)=>{e.exports=r(462)},79:(e,t,r)=>{r.d(t,{m:()=>n});var a=r(613),s=r(383),n=new class extends a.Q{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!s.S$&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}}},99:(e,t,r)=>{r.d(t,{V:()=>i,f:()=>n});var a=r(609),s=r.n(a),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=s().createContext&&s().createContext(n)},259:(e,t,r)=>{r.d(t,{$:()=>o,s:()=>i});var a=r(474),s=r(515),n=r(483),i=class extends s.k{#a;#s;#n;constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.mutationCache,this.#a=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#a.includes(e)||(this.#a.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#a=this.#a.filter((t=>t!==e)),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#a.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#i({type:"continue"})};this.#n=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});const r="pending"===this.state.status,a=!this.#n.canStart();try{if(r)t();else{this.#i({type:"pending",variables:e,isPaused:a}),await(this.#s.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#i({type:"pending",context:t,variables:e,isPaused:a})}const s=await this.#n.start();return await(this.#s.config.onSuccess?.(s,e,this.state.context,this)),await(this.options.onSuccess?.(s,e,this.state.context)),await(this.#s.config.onSettled?.(s,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(s,null,e,this.state.context)),this.#i({type:"success",data:s}),s}catch(t){try{throw await(this.#s.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#i({type:"error",error:t})}}finally{this.#s.runNext(this)}}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch((()=>{this.#a.forEach((t=>{t.onMutationUpdate(e)})),this.#s.notify({mutation:this,type:"updated",action:e})}))}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},265:(e,t,r)=>{r.d(t,{_:()=>o});var a=r(259),s=r(474),n=r(613),i=r(383),o=class extends n.Q{#o;#l=void 0;#c;#u;constructor(e,t){super(),this.#o=e,this.setOptions(t),this.bindMethods(),this.#d()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#o.defaultMutationOptions(e),(0,i.f8)(this.options,t)||this.#o.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#c,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,i.EN)(t.mutationKey)!==(0,i.EN)(this.options.mutationKey)?this.reset():"pending"===this.#c?.state.status&&this.#c.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#c?.removeObserver(this)}onMutationUpdate(e){this.#d(),this.#h(e)}getCurrentResult(){return this.#l}reset(){this.#c?.removeObserver(this),this.#c=void 0,this.#d(),this.#h()}mutate(e,t){return this.#u=t,this.#c?.removeObserver(this),this.#c=this.#o.getMutationCache().build(this.#o,this.options),this.#c.addObserver(this),this.#c.execute(e)}#d(){const e=this.#c?.state??(0,a.$)();this.#l={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#h(e){s.jG.batch((()=>{if(this.#u&&this.hasListeners()){const t=this.#l.variables,r=this.#l.context;"success"===e?.type?(this.#u.onSuccess?.(e.data,t,r),this.#u.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#u.onError?.(e.error,t,r),this.#u.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#l)}))}))}}},272:(e,t,r)=>{r.d(t,{$1:()=>o,LJ:()=>n,wZ:()=>i});var a=r(609),s=r(383),n=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},i=e=>{a.useEffect((()=>{e.clearReset()}),[e])},o=({result:e,errorResetBoundary:t,throwOnError:r,query:a,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&a&&(n&&void 0===e.data||(0,s.GU)(r,[e.error,a]))},326:(e,t,r)=>{r.d(t,{Ay:()=>L,l$:()=>M});var a=r(609),s=r(69),n=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,i=(()=>{let e=0;return()=>(++e).toString()})(),o=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),l=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:r}=t;return l(e,{type:e.toasts.find((e=>e.id===r.id))?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map((e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+s})))}}},c=[],u={toasts:[],pausedAt:void 0},d=e=>{u=l(u,e),c.forEach((e=>{e(u)}))},h={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},m=e=>(t,r)=>{let a=((e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||i()}))(t,e,r);return d({type:2,toast:a}),a.id},p=(e,t)=>m("blank")(e,t);p.error=m("error"),p.success=m("success"),p.loading=m("loading"),p.custom=m("custom"),p.dismiss=e=>{d({type:3,toastId:e})},p.remove=e=>d({type:4,toastId:e}),p.promise=(e,t,r)=>{let a=p.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let s=t.success?n(t.success,e):void 0;return s?p.success(s,{id:a,...r,...null==r?void 0:r.success}):p.dismiss(a),e})).catch((e=>{let s=t.error?n(t.error,e):void 0;s?p.error(s,{id:a,...r,...null==r?void 0:r.error}):p.dismiss(a)})),e};var f=(e,t)=>{d({type:1,toast:{id:e,height:t}})},y=()=>{d({type:5,time:Date.now()})},b=new Map,v=s.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,g=s.i7`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,E=s.i7`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,w=(0,s.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${v} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${g} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${E} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,C=s.i7`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,_=(0,s.I4)("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${C} 1s linear infinite;
`,O=s.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,k=s.i7`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,N=(0,s.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${O} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${k} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,x=(0,s.I4)("div")`
  position: absolute;
`,S=(0,s.I4)("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,R=s.i7`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,F=(0,s.I4)("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${R} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,T=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?a.createElement(F,null,t):t:"blank"===r?null:a.createElement(S,null,a.createElement(_,{...s}),"loading"!==r&&a.createElement(x,null,"error"===r?a.createElement(w,{...s}):a.createElement(N,{...s})))},I=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Q=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,P=(0,s.I4)("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,q=(0,s.I4)("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,j=a.memo((({toast:e,position:t,style:r,children:i})=>{let l=e.height?((e,t)=>{let r=e.includes("top")?1:-1,[a,n]=o()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[I(r),Q(r)];return{animation:t?`${(0,s.i7)(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${(0,s.i7)(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},c=a.createElement(T,{toast:e}),u=a.createElement(q,{...e.ariaProps},n(e.message,e));return a.createElement(P,{className:e.className,style:{...l,...r,...e.style}},"function"==typeof i?i({icon:c,message:u}):a.createElement(a.Fragment,null,c,u))}));(0,s.mj)(a.createElement);var D=({id:e,className:t,style:r,onHeightUpdate:s,children:n})=>{let i=a.useCallback((t=>{if(t){let r=()=>{let r=t.getBoundingClientRect().height;s(e,r)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,s]);return a.createElement("div",{ref:i,className:t,style:r},n)},A=s.AH`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,M=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:i,containerStyle:l,containerClassName:m})=>{let{toasts:v,handlers:g}=(e=>{let{toasts:t,pausedAt:r}=((e={})=>{let[t,r]=(0,a.useState)(u),s=(0,a.useRef)(u);(0,a.useEffect)((()=>(s.current!==u&&r(u),c.push(r),()=>{let e=c.indexOf(r);e>-1&&c.splice(e,1)})),[]);let n=t.toasts.map((t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||h[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}}));return{...t,toasts:n}})(e);(0,a.useEffect)((()=>{if(r)return;let e=Date.now(),a=t.map((t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(r<0))return setTimeout((()=>p.dismiss(t.id)),r);t.visible&&p.dismiss(t.id)}));return()=>{a.forEach((e=>e&&clearTimeout(e)))}}),[t,r]);let s=(0,a.useCallback)((()=>{r&&d({type:6,time:Date.now()})}),[r]),n=(0,a.useCallback)(((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:n}=r||{},i=t.filter((t=>(t.position||n)===(e.position||n)&&t.height)),o=i.findIndex((t=>t.id===e.id)),l=i.filter(((e,t)=>t<o&&e.visible)).length;return i.filter((e=>e.visible)).slice(...a?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+s),0)}),[t]);return(0,a.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(b.has(e))return;let r=setTimeout((()=>{b.delete(e),d({type:4,toastId:e})}),t);b.set(e,r)})(e.id,e.removeDelay);else{let t=b.get(e.id);t&&(clearTimeout(t),b.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:f,startPause:y,endPause:s,calculateOffset:n}}})(r);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:m,onMouseEnter:g.startPause,onMouseLeave:g.endPause},v.map((r=>{let l=r.position||t,c=((e,t)=>{let r=e.includes("top"),a=r?{top:0}:{bottom:0},s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:o()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...a,...s}})(l,g.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return a.createElement(D,{id:r.id,key:r.id,onHeightUpdate:g.updateHeight,className:r.visible?A:"",style:c},"custom"===r.type?n(r.message,r):i?i(r):a.createElement(j,{toast:r,position:l}))})))},L=p},340:(e,t,r)=>{r.d(t,{$:()=>c});var a=r(79),s=r(474),n=r(702),i=r(613),o=r(443),l=r(383),c=class extends i.Q{constructor(e,t){super(),this.options=t,this.#o=e,this.#m=null,this.#p=(0,o.T)(),this.options.experimental_prefetchInRender||this.#p.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#o;#f=void 0;#y=void 0;#l=void 0;#b;#v;#p;#m;#g;#E;#w;#C;#_;#O;#k=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#f.addObserver(this),u(this.#f,this.options)?this.#N():this.updateResult(),this.#x())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#f,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#f,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#S(),this.#R(),this.#f.removeObserver(this)}setOptions(e){const t=this.options,r=this.#f;if(this.options=this.#o.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#f))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#F(),this.#f.setOptions(this.options),t._defaulted&&!(0,l.f8)(this.options,t)&&this.#o.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#f,observer:this});const a=this.hasListeners();a&&h(this.#f,r,this.options,t)&&this.#N(),this.updateResult(),!a||this.#f===r&&(0,l.Eh)(this.options.enabled,this.#f)===(0,l.Eh)(t.enabled,this.#f)&&(0,l.d2)(this.options.staleTime,this.#f)===(0,l.d2)(t.staleTime,this.#f)||this.#T();const s=this.#I();!a||this.#f===r&&(0,l.Eh)(this.options.enabled,this.#f)===(0,l.Eh)(t.enabled,this.#f)&&s===this.#O||this.#Q(s)}getOptimisticResult(e){const t=this.#o.getQueryCache().build(this.#o,e),r=this.createResult(t,e);return a=this,s=r,!(0,l.f8)(a.getCurrentResult(),s)&&(this.#l=r,this.#v=this.options,this.#b=this.#f.state),r;var a,s}getCurrentResult(){return this.#l}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#k.add(e)}getCurrentQuery(){return this.#f}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#o.defaultQueryOptions(e),r=this.#o.getQueryCache().build(this.#o,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#N({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#l)))}#N(e){this.#F();let t=this.#f.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#T(){this.#S();const e=(0,l.d2)(this.options.staleTime,this.#f);if(l.S$||this.#l.isStale||!(0,l.gn)(e))return;const t=(0,l.j3)(this.#l.dataUpdatedAt,e)+1;this.#C=setTimeout((()=>{this.#l.isStale||this.updateResult()}),t)}#I(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#f):this.options.refetchInterval)??!1}#Q(e){this.#R(),this.#O=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#f)&&(0,l.gn)(this.#O)&&0!==this.#O&&(this.#_=setInterval((()=>{(this.options.refetchIntervalInBackground||a.m.isFocused())&&this.#N()}),this.#O))}#x(){this.#T(),this.#Q(this.#I())}#S(){this.#C&&(clearTimeout(this.#C),this.#C=void 0)}#R(){this.#_&&(clearInterval(this.#_),this.#_=void 0)}createResult(e,t){const r=this.#f,a=this.options,s=this.#l,i=this.#b,c=this.#v,d=e!==r?e.state:this.#y,{state:p}=e;let f,y={...p},b=!1;if(t._optimisticResults){const s=this.hasListeners(),i=!s&&u(e,t),o=s&&h(e,r,t,a);(i||o)&&(y={...y,...(0,n.k)(p.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:v,errorUpdatedAt:g,status:E}=y;f=y.data;let w=!1;if(void 0!==t.placeholderData&&void 0===f&&"pending"===E){let e;s?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=s.data,w=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#w?.state.data,this.#w):t.placeholderData,void 0!==e&&(E="success",f=(0,l.pl)(s?.data,e,t),b=!0)}if(t.select&&void 0!==f&&!w)if(s&&f===i?.data&&t.select===this.#g)f=this.#E;else try{this.#g=t.select,f=t.select(f),f=(0,l.pl)(s?.data,f,t),this.#E=f,this.#m=null}catch(e){this.#m=e}this.#m&&(v=this.#m,f=this.#E,g=Date.now(),E="error");const C="fetching"===y.fetchStatus,_="pending"===E,O="error"===E,k=_&&C,N=void 0!==f,x={status:E,fetchStatus:y.fetchStatus,isPending:_,isSuccess:"success"===E,isError:O,isInitialLoading:k,isLoading:k,data:f,dataUpdatedAt:y.dataUpdatedAt,error:v,errorUpdatedAt:g,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>d.dataUpdateCount||y.errorUpdateCount>d.errorUpdateCount,isFetching:C,isRefetching:C&&!_,isLoadingError:O&&!N,isPaused:"paused"===y.fetchStatus,isPlaceholderData:b,isRefetchError:O&&N,isStale:m(e,t),refetch:this.refetch,promise:this.#p};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===x.status?e.reject(x.error):void 0!==x.data&&e.resolve(x.data)},a=()=>{const e=this.#p=x.promise=(0,o.T)();t(e)},s=this.#p;switch(s.status){case"pending":e.queryHash===r.queryHash&&t(s);break;case"fulfilled":"error"!==x.status&&x.data===s.value||a();break;case"rejected":"error"===x.status&&x.error===s.reason||a()}}return x}updateResult(){const e=this.#l,t=this.createResult(this.#f,this.options);this.#b=this.#f.state,this.#v=this.options,void 0!==this.#b.data&&(this.#w=this.#f),(0,l.f8)(t,e)||(this.#l=t,this.#h({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#k.size)return!0;const a=new Set(r??this.#k);return this.options.throwOnError&&a.add("error"),Object.keys(this.#l).some((t=>{const r=t;return this.#l[r]!==e[r]&&a.has(r)}))})()}))}#F(){const e=this.#o.getQueryCache().build(this.#o,this.options);if(e===this.#f)return;const t=this.#f;this.#f=e,this.#y=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#x()}#h(e){s.jG.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#l)})),this.#o.getQueryCache().notify({query:this.#f,type:"observerResultsUpdated"})}))}};function u(e,t){return function(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,l.Eh)(t.enabled,e)){const a="function"==typeof r?r(e):r;return"always"===a||!1!==a&&m(e,t)}return!1}function h(e,t,r,a){return(e!==t||!1===(0,l.Eh)(a.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&m(e,r)}function m(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}},359:(e,t,r)=>{r.d(t,{k5:()=>a.k}),r(888);var a=r(463);r(99)},383:(e,t,r)=>{r.d(t,{Cp:()=>p,EN:()=>m,Eh:()=>c,F$:()=>h,GU:()=>N,MK:()=>u,S$:()=>a,ZM:()=>k,ZZ:()=>_,Zw:()=>n,d2:()=>l,f8:()=>y,gn:()=>i,hT:()=>O,j3:()=>o,lQ:()=>s,nJ:()=>d,pl:()=>w,y9:()=>C,yy:()=>E});var a="undefined"==typeof window||"Deno"in globalThis;function s(){}function n(e,t){return"function"==typeof e?e(t):e}function i(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){const{type:r="all",exact:a,fetchStatus:s,predicate:n,queryKey:i,stale:o}=e;if(i)if(a){if(t.queryHash!==h(i,t.options))return!1}else if(!p(t.queryKey,i))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return!("boolean"==typeof o&&t.isStale()!==o||s&&s!==t.state.fetchStatus||n&&!n(t))}function d(e,t){const{exact:r,status:a,predicate:s,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(r){if(m(t.options.mutationKey)!==m(n))return!1}else if(!p(t.options.mutationKey,n))return!1}return!(a&&t.state.status!==a||s&&!s(t))}function h(e,t){return(t?.queryKeyHashFn||m)(e)}function m(e){return JSON.stringify(e,((e,t)=>v(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function p(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((r=>p(e[r],t[r])))}function f(e,t){if(e===t)return e;const r=b(e)&&b(t);if(r||v(e)&&v(t)){const a=r?e:Object.keys(e),s=a.length,n=r?t:Object.keys(t),i=n.length,o=r?[]:{};let l=0;for(let s=0;s<i;s++){const i=r?s:n[s];(!r&&a.includes(i)||r)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,l++):(o[i]=f(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&l++)}return s===i&&l===s?e:o}return t}function y(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function b(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function v(e){if(!g(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!g(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function E(e){return new Promise((t=>{setTimeout(t,e)}))}function w(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?f(e,t):t}function C(e,t,r=0){const a=[...e,t];return r&&a.length>r?a.slice(1):a}function _(e,t,r=0){const a=[t,...e];return r&&a.length>r?a.slice(0,-1):a}var O=Symbol();function k(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==O?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}function N(e,t){return"function"==typeof e?e(...t):!!e}},443:(e,t,r)=>{function a(){let e,t;const r=new Promise(((r,a)=>{e=r,t=a}));function a(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{a({status:"fulfilled",value:t}),e(t)},r.reject=e=>{a({status:"rejected",reason:e}),t(e)},r}r.d(t,{T:()=>a})},449:(e,t,r)=>{r.d(t,{n:()=>l});var a=r(609),s=r(265),n=r(474),i=r(383),o=r(967);function l(e,t){const r=(0,o.jE)(t),[l]=a.useState((()=>new s._(r,e)));a.useEffect((()=>{l.setOptions(e)}),[l,e]);const c=a.useSyncExternalStore(a.useCallback((e=>l.subscribe(n.jG.batchCalls(e))),[l]),(()=>l.getCurrentResult()),(()=>l.getCurrentResult())),u=a.useCallback(((e,t)=>{l.mutate(e,t).catch(i.lQ)}),[l]);if(c.error&&(0,i.GU)(l.options.throwOnError,[c.error]))throw c.error;return{...c,mutate:u,mutateAsync:c.mutate}}},462:(e,t,r)=>{var a=r(609),s=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,r){var a,l={},c=null,u=null;for(a in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)n.call(t,a)&&!o.hasOwnProperty(a)&&(l[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===l[a]&&(l[a]=t[a]);return{$$typeof:s,type:e,key:c,ref:u,props:l,_owner:i.current}}},463:(e,t,r)=>{r.d(t,{k:()=>c});var a=r(609),s=r.n(a),n=r(99),i=function(){return i=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var s in t=arguments[r])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},i.apply(this,arguments)},o=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(r[a[s]]=e[a[s]])}return r};function l(e){return e&&e.map((function(e,t){return s().createElement(e.tag,i({key:t},e.attr),l(e.child))}))}function c(e){return function(t){return s().createElement(u,i({attr:i({},e.attr)},t),l(e.child))}}function u(e){var t=function(t){var r,a=e.attr,n=e.size,l=e.title,c=o(e,["attr","size","title"]),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s().createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,c,{className:r,style:i(i({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),l&&s().createElement("title",null,l),e.children)};return void 0!==n.V?s().createElement(n.V.Consumer,null,(function(e){return t(e)})):t(n.f)}},471:(e,t,r)=>{r.d(t,{t:()=>d});var a=r(609),s=r(474),n=r(383),i=r(967),o=r(518),l=r(272),c=r(893),u=r(501);function d(e,t,r){const d=(0,i.jE)(r),h=(0,c.w)(),m=(0,o.h)(),p=d.defaultQueryOptions(e);d.getDefaultOptions().queries?._experimental_beforeQuery?.(p),p._optimisticResults=h?"isRestoring":"optimistic",(0,u.jv)(p),(0,l.LJ)(p,m),(0,l.wZ)(m);const f=!d.getQueryCache().get(p.queryHash),[y]=a.useState((()=>new t(d,p))),b=y.getOptimisticResult(p),v=!h&&!1!==e.subscribed;if(a.useSyncExternalStore(a.useCallback((e=>{const t=v?y.subscribe(s.jG.batchCalls(e)):n.lQ;return y.updateResult(),t}),[y,v]),(()=>y.getCurrentResult()),(()=>y.getCurrentResult())),a.useEffect((()=>{y.setOptions(p)}),[p,y]),(0,u.EU)(p,b))throw(0,u.iL)(p,y,m);if((0,l.$1)({result:b,errorResetBoundary:m,throwOnError:p.throwOnError,query:d.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw b.error;if(d.getDefaultOptions().queries?._experimental_afterQuery?.(p,b),p.experimental_prefetchInRender&&!n.S$&&(0,u.nE)(b,h)){const e=f?(0,u.iL)(p,y,m):d.getQueryCache().get(p.queryHash)?.promise;e?.catch(n.lQ).finally((()=>{y.updateResult()}))}return p.notifyOnChangeProps?b:y.trackResult(b)}},474:(e,t,r)=>{r.d(t,{jG:()=>s});var a=e=>setTimeout(e,0),s=function(){let e=[],t=0,r=e=>{e()},s=e=>{e()},n=a;const i=a=>{t?e.push(a):n((()=>{r(a)}))};return{batch:a=>{let i;t++;try{i=a()}finally{t--,t||(()=>{const t=e;e=[],t.length&&n((()=>{s((()=>{t.forEach((e=>{r(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{n=e}}}()},483:(e,t,r)=>{r.d(t,{II:()=>d,v_:()=>l,wm:()=>u});var a=r(79),s=r(860),n=r(443),i=r(383);function o(e){return Math.min(1e3*2**e,3e4)}function l(e){return"online"!==(e??"online")||s.t.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function u(e){return e instanceof c}function d(e){let t,r=!1,u=0,d=!1;const h=(0,n.T)(),m=()=>a.m.isFocused()&&("always"===e.networkMode||s.t.isOnline())&&e.canRun(),p=()=>l(e.networkMode)&&e.canRun(),f=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),h.resolve(r))},y=r=>{d||(d=!0,e.onError?.(r),t?.(),h.reject(r))},b=()=>new Promise((r=>{t=e=>{(d||m())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,d||e.onContinue?.()})),v=()=>{if(d)return;let t;const a=0===u?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(f).catch((t=>{if(d)return;const a=e.retry??(i.S$?0:3),s=e.retryDelay??o,n="function"==typeof s?s(u,t):s,l=!0===a||"number"==typeof a&&u<a||"function"==typeof a&&a(u,t);!r&&l?(u++,e.onFail?.(u,t),(0,i.yy)(n).then((()=>m()?void 0:b())).then((()=>{r?y(t):v()}))):y(t)}))};return{promise:h,cancel:t=>{d||(y(new c(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:p,start:()=>(p()?v():b().then(v),h)}}},501:(e,t,r)=>{r.d(t,{EU:()=>n,iL:()=>i,jv:()=>a,nE:()=>s});var a=e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},s=(e,t)=>e.isLoading&&e.isFetching&&!t,n=(e,t)=>e?.suspense&&t.isPending,i=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},515:(e,t,r)=>{r.d(t,{k:()=>s});var a=r(383),s=class{#P;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,a.gn)(this.gcTime)&&(this.#P=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(a.S$?1/0:3e5))}clearGcTimeout(){this.#P&&(clearTimeout(this.#P),this.#P=void 0)}}},518:(e,t,r)=>{r.d(t,{h:()=>n});var a=r(609);r(70);var s=a.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),n=()=>a.useContext(s)},559:(e,t,r)=>{r.d(t,{Gn:()=>n,Q2:()=>i,Zu:()=>s});var a=r(44);const s=async(e={})=>{const t={page:1,per_page:10,orderby:"votes",...e};return a.A.get("/ideas",t)},n=async e=>e instanceof FormData?a.A.postFormData("/ideas",e):a.A.post("/ideas",e),i=async e=>a.A.post(`/ideas/${e}/vote`)},576:(e,t,r)=>{var a=r(795);t.H=a.createRoot,a.hydrateRoot},609:e=>{e.exports=window.React},613:(e,t,r)=>{r.d(t,{Q:()=>a});var a=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},702:(e,t,r)=>{r.d(t,{X:()=>o,k:()=>l});var a=r(383),s=r(474),n=r(483),i=r(515),o=class extends i.k{#q;#j;#D;#o;#n;#A;#M;constructor(e){super(),this.#M=!1,this.#A=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#o=e.client,this.#D=this.#o.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#q=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,a=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#q,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#n?.promise}setOptions(e){this.options={...this.#A,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#D.remove(this)}setData(e,t){const r=(0,a.pl)(this.state.data,e,this.options);return this.#i({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#i({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#n?.promise;return this.#n?.cancel(e),t?t.then(a.lQ).catch(a.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#q)}isActive(){return this.observers.some((e=>!1!==(0,a.Eh)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===a.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,a.j3)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#n?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#n?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#D.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#n&&(this.#M?this.#n.cancel({revert:!0}):this.#n.cancelRetry()),this.scheduleGc()),this.#D.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#n)return this.#n.continueRetry(),this.#n.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#M=!0,r.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#o,state:this.state,fetchFn:()=>{const e=(0,a.ZM)(this.options,t),r={client:this.#o,queryKey:this.queryKey,meta:this.meta};return s(r),this.#M=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};s(i),this.options.behavior?.onFetch(i,this),this.#j=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#i({type:"fetch",meta:i.fetchOptions?.meta});const o=e=>{(0,n.wm)(e)&&e.silent||this.#i({type:"error",error:e}),(0,n.wm)(e)||(this.#D.config.onError?.(e,this),this.#D.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#n=(0,n.II)({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void o(e)}this.#D.config.onSuccess?.(e,this),this.#D.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#i({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#n.start()}#i(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,n.wm)(r)&&r.revert&&this.#j?{...this.#j,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),s.jG.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#D.notify({query:this,type:"updated",action:e})}))}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},795:e=>{e.exports=window.ReactDOM},820:(e,t,r)=>{r.d(t,{I:()=>n});var a=r(340),s=r(471);function n(e,t){return(0,s.t)(e,a.$,t)}},860:(e,t,r)=>{r.d(t,{t:()=>n});var a=r(613),s=r(383),n=new class extends a.Q{#L=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!s.S$&&window.addEventListener){const t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#L!==e&&(this.#L=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#L}}},880:(e,t,r)=>{r.d(t,{B88:()=>p,CKj:()=>d,Pum:()=>m,X6_:()=>l,aze:()=>u,fK4:()=>n,fZZ:()=>o,kGk:()=>h,mEP:()=>c,wAb:()=>i,yGN:()=>f,zd:()=>s});var a=r(359);function s(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"}},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"}}]})(e)}function n(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 12 15 18 9"}}]})(e)}function i(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"18 15 12 9 6 15"}}]})(e)}function o(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}},{tag:"circle",attr:{cx:"8.5",cy:"8.5",r:"1.5"}},{tag:"polyline",attr:{points:"21 15 16 10 5 21"}}]})(e)}function l(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"}}]})(e)}function c(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}}]})(e)}function u(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"5 3 19 12 5 21 5 3"}}]})(e)}function d(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}}]})(e)}function h(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(e)}function m(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"18",cy:"5",r:"3"}},{tag:"circle",attr:{cx:"6",cy:"12",r:"3"}},{tag:"circle",attr:{cx:"18",cy:"19",r:"3"}},{tag:"line",attr:{x1:"8.59",y1:"13.51",x2:"15.42",y2:"17.49"}},{tag:"line",attr:{x1:"15.41",y1:"6.51",x2:"8.59",y2:"10.49"}}]})(e)}function p(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(e)}function f(e){return(0,a.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}},888:(e,t,r)=>{},893:(e,t,r)=>{r.d(t,{w:()=>n});var a=r(609),s=a.createContext(!1),n=()=>a.useContext(s);s.Provider},967:(e,t,r)=>{r.d(t,{Ht:()=>o,jE:()=>i});var a=r(609),s=r(70),n=a.createContext(void 0),i=e=>{const t=a.useContext(n);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(a.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,s.jsx)(n.Provider,{value:e,children:t}))}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var n=a[e]={exports:{}};return r[e](n,n.exports,s),n.exports}s.m=r,s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var r in t)s.o(t,r)&&!s.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},s.f={},s.e=e=>Promise.all(Object.keys(s.f).reduce(((t,r)=>(s.f[r](e,t),t)),[])),s.u=e=>e+".js",s.miniCssF=e=>{},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="feedlane:",s.l=(r,a,n,i)=>{if(e[r])e[r].push(a);else{var o,l;if(void 0!==n)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+n){o=d;break}}o||(l=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,s.nc&&o.setAttribute("nonce",s.nc),o.setAttribute("data-webpack",t+n),o.src=r),e[r]=[a];var h=(t,a)=>{o.onerror=o.onload=null,clearTimeout(m);var s=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),s&&s.forEach((e=>e(a))),t)return t(a)},m=setTimeout(h.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=h.bind(null,o.onerror),o.onload=h.bind(null,o.onload),l&&document.head.appendChild(o)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;s.g.importScripts&&(e=s.g.location+"");var t=s.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var r=t.getElementsByTagName("script");if(r.length)for(var a=r.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=r[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),s.p=e+"../"})(),(()=>{var e={786:0};s.f.j=(t,r)=>{var a=s.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var n=new Promise(((r,s)=>a=e[t]=[r,s]));r.push(a[2]=n);var i=s.p+s.u(t),o=new Error;s.l(i,(r=>{if(s.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var n=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+n+": "+i+")",o.name="ChunkLoadError",o.type=n,o.request=i,a[1](o)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,n,[i,o,l]=r,c=0;if(i.some((t=>0!==e[t]))){for(a in o)s.o(o,a)&&(s.m[a]=o[a]);l&&l(s)}for(t&&t(r);c<i.length;c++)n=i[c],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0},r=globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var n=s(609),i=s(576),o=s(383),l=s(702),c=s(474),u=s(613),d=class extends u.Q{constructor(e={}){super(),this.config=e,this.#U=new Map}#U;build(e,t,r){const a=t.queryKey,s=t.queryHash??(0,o.F$)(a,t);let n=this.get(s);return n||(n=new l.X({client:e,queryKey:a,queryHash:s,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(a)}),this.add(n)),n}add(e){this.#U.has(e.queryHash)||(this.#U.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#U.get(e.queryHash);t&&(e.destroy(),t===e&&this.#U.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){c.jG.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#U.get(e)}getAll(){return[...this.#U.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,o.MK)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,o.MK)(e,t))):t}notify(e){c.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){c.jG.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){c.jG.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},h=s(259),m=class extends u.Q{constructor(e={}){super(),this.config=e,this.#$=new Set,this.#G=new Map,this.#B=0}#$;#G;#B;build(e,t,r){const a=new h.s({mutationCache:this,mutationId:++this.#B,options:e.defaultMutationOptions(t),state:r});return this.add(a),a}add(e){this.#$.add(e);const t=p(e);if("string"==typeof t){const r=this.#G.get(t);r?r.push(e):this.#G.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#$.delete(e)){const t=p(e);if("string"==typeof t){const r=this.#G.get(t);if(r)if(r.length>1){const t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#G.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=p(e);if("string"==typeof t){const r=this.#G.get(t),a=r?.find((e=>"pending"===e.state.status));return!a||a===e}return!0}runNext(e){const t=p(e);if("string"==typeof t){const r=this.#G.get(t)?.find((t=>t!==e&&t.state.isPaused));return r?.continue()??Promise.resolve()}return Promise.resolve()}clear(){c.jG.batch((()=>{this.#$.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#$.clear(),this.#G.clear()}))}getAll(){return Array.from(this.#$)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,o.nJ)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,o.nJ)(e,t)))}notify(e){c.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.jG.batch((()=>Promise.all(e.map((e=>e.continue().catch(o.lQ))))))}};function p(e){return e.options.scope?.id}var f=s(79),y=s(860);function b(e){return{onFetch:(t,r)=>{const a=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],i=t.state.data?.pageParams||[];let l={pages:[],pageParams:[]},c=0;const u=async()=>{let r=!1;const u=(0,o.ZM)(t.options,t.fetchOptions),d=async(e,a,s)=>{if(r)return Promise.reject();if(null==a&&e.pages.length)return Promise.resolve(e);const n={client:t.client,queryKey:t.queryKey,pageParam:a,direction:s?"backward":"forward",meta:t.options.meta};var i;i=n,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const l=await u(n),{maxPages:c}=t.options,d=s?o.ZZ:o.y9;return{pages:d(e.pages,l,c),pageParams:d(e.pageParams,a,c)}};if(s&&n.length){const e="backward"===s,t={pages:n,pageParams:i},r=(e?g:v)(a,t);l=await d(t,r,e)}else{const t=e??n.length;do{const e=0===c?i[0]??a.initialPageParam:v(a,l);if(c>0&&null==e)break;l=await d(l,e),c++}while(c<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=u}}}function v(e,{pages:t,pageParams:r}){const a=t.length-1;return t.length>0?e.getNextPageParam(t[a],t,r[a],r):void 0}function g(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}var E=s(967),w=s(326),C=s(880),_=s(359);function O(e){return(0,_.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"}}]})(e)}function k(e){return(0,_.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"}}]})(e)}function N(e){return(0,_.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"}}]})(e)}var x=s(820);s(44);const S=async e=>{try{const t=await fetch(`${window.feedlaneData.rest_url}wp/v2/media/${e}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(t.ok)return(await t.json()).source_url}catch(e){}return null},R=({post:e})=>{const[t,r]=(0,n.useState)(!1),a={integrations:{label:"INTEGRATIONS",color:"#8B5CF6",bg:"#F3F4F6"},features:{label:"FEATURES",color:"#10B981",bg:"#F0FDF4"},improvements:{label:"IMPROVEMENTS",color:"#F59E0B",bg:"#FFFBEB"},announcements:{label:"ANNOUNCEMENTS",color:"#EF4444",bg:"#FEF2F2"}}[e.category]||{label:"NEW",color:"#6366F1",bg:"#EEF2FF"};var s;return(0,n.createElement)("article",{className:"feedlane-beamer-post"},(0,n.createElement)("div",{className:"feedlane-beamer-post__header"},(0,n.createElement)("div",{className:"feedlane-beamer-post__badges"},(0,n.createElement)("span",{className:"feedlane-beamer-post__badge",style:{color:a.color,backgroundColor:a.bg}},a.label),(0,n.createElement)("span",{className:"feedlane-beamer-post__date"},(s=e.date,new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})))),(0,n.createElement)("button",{className:"feedlane-beamer-post__share"},(0,n.createElement)(C.Pum,{size:16}))),(0,n.createElement)("h3",{className:"feedlane-beamer-post__title"},"integrations"===e.category&&"🚀 ",e.title),(0,n.createElement)("div",{className:"feedlane-beamer-post__content"},e.availability&&(0,n.createElement)("p",{className:"feedlane-beamer-post__availability"},(0,n.createElement)("em",null,"Available for ",e.availability," customers")),(0,n.createElement)("div",{className:"feedlane-beamer-post__text "+(t?"expanded":""),dangerouslySetInnerHTML:{__html:t?e.content:e.excerpt||e.content.substring(0,200)+"..."}}),e.content.length>200&&(0,n.createElement)("button",{className:"feedlane-beamer-post__read-more",onClick:()=>r(!t)},t?"Show less":"Read more"),e.video_url&&(0,n.createElement)("div",{className:"feedlane-beamer-post__video"},(0,n.createElement)("div",{className:"feedlane-beamer-post__video-thumbnail"},e.featured_image&&(0,n.createElement)("img",{src:e.featured_image,alt:e.title,loading:"lazy"}),(0,n.createElement)("button",{className:"feedlane-beamer-post__play-btn"},(0,n.createElement)(C.aze,{size:24})),(0,n.createElement)("div",{className:"feedlane-beamer-post__video-info"},(0,n.createElement)("span",{className:"feedlane-beamer-post__video-title"},e.video_title||e.title),(0,n.createElement)("div",{className:"feedlane-beamer-post__video-meta"},(0,n.createElement)("span",null,"0"),(0,n.createElement)("span",null,"📎"),(0,n.createElement)("span",null,"✏️"))))),e.featured_image&&!e.video_url&&(0,n.createElement)("div",{className:"feedlane-beamer-post__image"},(0,n.createElement)("img",{src:e.featured_image,alt:e.title,loading:"lazy"}))))},F=()=>{const{data:e,isLoading:t,error:r,refetch:a}=(0,x.I)({queryKey:["newsfeed-posts"],queryFn:()=>(async(e={})=>{const t={page:1,per_page:10,...e},r=await fetch(`${window.feedlaneData.rest_url}wp/v2/feedlane_posts?${new URLSearchParams(t)}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(!r.ok)throw new Error("Failed to fetch newsfeed posts");return(await r.json()).map((e=>({id:e.id,title:e.title.rendered,content:e.content.rendered,excerpt:e.excerpt.rendered,date:e.date,featured_image:e.featured_media?S(e.featured_media):null,author:e.author,link:e.link})))})({per_page:10}),staleTime:3e5});return t?(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading newsfeed...")):r?(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load newsfeed posts."),(0,n.createElement)("button",{onClick:()=>a(),className:"feedlane-button feedlane-button--secondary"},"Try Again")):e&&0!==e.length?(0,n.createElement)("div",{className:"feedlane-newsfeed"},(0,n.createElement)("div",{className:"feedlane-subscribe-banner"},(0,n.createElement)("div",{className:"feedlane-subscribe-banner__icon"},(0,n.createElement)(C.zd,{size:20})),(0,n.createElement)("span",{className:"feedlane-subscribe-banner__text"},"Subscribe to get our latest updates")),(0,n.createElement)("div",{className:"feedlane-newsfeed__posts"},e.map((e=>(0,n.createElement)(R,{key:e.id,post:e}))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"📢"),(0,n.createElement)("h3",null,"No News Yet"),(0,n.createElement)("p",null,"Check back later for updates and announcements."))};var T=s(449),I=s(559);const Q=({idea:e})=>{const[t,r]=(0,n.useState)(!1),a=(0,E.jE)(),s=(0,T.n)({mutationFn:e=>(0,I.Q2)(e),onSuccess:()=>{r(!0),a.invalidateQueries(["ideas"])}}),i={"feature-request":{label:"FEATURE REQUEST",color:"#10B981"},improvement:{label:"IMPROVEMENT",color:"#F59E0B"},"bug-fix":{label:"BUG FIX",color:"#EF4444"},integration:{label:"INTEGRATION",color:"#8B5CF6"}}[e.category]||{label:"FEATURE REQUEST",color:"#10B981"};var o;return(0,n.createElement)("article",{className:"feedlane-beamer-idea"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__header"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__vote"},(0,n.createElement)("button",{className:"feedlane-beamer-idea__vote-btn "+(t?"voted":""),onClick:()=>{t||s.mutate(e.id)},disabled:t},(0,n.createElement)(C.wAb,{size:16})),(0,n.createElement)("span",{className:"feedlane-beamer-idea__vote-count"},e.vote_count||0)),(0,n.createElement)("div",{className:"feedlane-beamer-idea__content"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__meta"},(0,n.createElement)("span",{className:"feedlane-beamer-idea__category",style:{color:i.color}},i.label),(0,n.createElement)("span",{className:"feedlane-beamer-idea__comments"},e.comment_count||0," ",(0,n.createElement)(C.mEP,{size:14}))),(0,n.createElement)("h3",{className:"feedlane-beamer-idea__title"},e.title),e.status&&(0,n.createElement)("div",{className:"feedlane-beamer-idea__status"},(0,n.createElement)("span",{className:"feedlane-beamer-idea__status-indicator",style:{backgroundColor:(o=e.status,{completed:"#10B981","in-progress":"#F59E0B",planned:"#EF4444","under-review":"#6B7280"}[o]||"#6B7280")}}),(0,n.createElement)("span",{className:"feedlane-beamer-idea__status-text"},e.status.toUpperCase().replace("-"," "))),e.details&&(0,n.createElement)("div",{className:"feedlane-beamer-idea__details"},(0,n.createElement)("ul",null,e.details.split("\n").filter((e=>e.trim())).map(((e,t)=>(0,n.createElement)("li",{key:t},e.trim()))))))))},P=()=>{const[e,t]=(0,n.useState)(!1),[r,a]=(0,n.useState)({title:"",email:"",firstName:"",lastName:"",details:"",category:""}),[s,i]=(0,n.useState)(!1),o=(0,E.jE)(),{data:l,isLoading:c,error:u,refetch:d}=(0,x.I)({queryKey:["ideas"],queryFn:()=>(0,I.Zu)({per_page:20,status:"approved"}),staleTime:18e4}),h=(0,T.n)({mutationFn:I.Gn,onSuccess:()=>{t(!1),a({title:"",email:"",firstName:"",lastName:"",details:"",category:""}),o.invalidateQueries(["ideas"])}}),m=e=>{const{name:t,value:r}=e.target;a((e=>({...e,[t]:r})))};if(c)return(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading ideas..."));if(u)return(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load ideas."),(0,n.createElement)("button",{onClick:()=>d(),className:"feedlane-button feedlane-button--secondary"},"Try Again"));const p=l?.ideas||[],f=window.feedlaneData?.categories||[];return(0,n.createElement)("div",{className:"feedlane-ideas"},e?(0,n.createElement)("div",{className:"feedlane-idea-form"},(0,n.createElement)("div",{className:"feedlane-idea-form__header"},(0,n.createElement)("h3",null,"Suggest an Idea"),(0,n.createElement)("p",null,"What would make ",window.feedlaneData?.siteName||"our product"," more useful to you?"),(0,n.createElement)("button",{className:"feedlane-idea-form__close",onClick:()=>t(!1)},(0,n.createElement)(C.yGN,{size:20}))),(0,n.createElement)("form",{onSubmit:async e=>{e.preventDefault(),i(!0);try{await h.mutateAsync(r)}catch(e){console.error("Error submitting idea:",e)}finally{i(!1)}},className:"feedlane-idea-form__form"},(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"title"},"TITLE"),(0,n.createElement)("input",{type:"text",id:"title",name:"title",value:r.title,onChange:m,placeholder:"Your idea",required:!0})),(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"email"},"EMAIL"),(0,n.createElement)("input",{type:"email",id:"email",name:"email",value:r.email,onChange:m,placeholder:"E-mail (required)",required:!0})),(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"firstName"},"FIRST NAME"),(0,n.createElement)("input",{type:"text",id:"firstName",name:"firstName",value:r.firstName,onChange:m,placeholder:"First name (optional)"})),(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"lastName"},"LAST NAME"),(0,n.createElement)("input",{type:"text",id:"lastName",name:"lastName",value:r.lastName,onChange:m,placeholder:"Last name (optional)"})),(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"details"},"DETAILS"),(0,n.createElement)("textarea",{id:"details",name:"details",value:r.details,onChange:m,placeholder:"Any additional details",rows:4})),(0,n.createElement)("div",{className:"feedlane-form-group"},(0,n.createElement)("label",{htmlFor:"category"},"CATEGORY"),(0,n.createElement)("select",{id:"category",name:"category",value:r.category,onChange:m,required:!0},(0,n.createElement)("option",{value:""},"Select Category"),f.map((e=>(0,n.createElement)("option",{key:e.id,value:e.id},e.name))))),(0,n.createElement)("div",{className:"feedlane-form-actions"},(0,n.createElement)("button",{type:"button",className:"feedlane-form-attach"},(0,n.createElement)(C.fZZ,{size:16})),(0,n.createElement)("button",{type:"submit",className:"feedlane-form-submit",disabled:s},s?"SUBMITTING...":"SUBMIT")),(0,n.createElement)("div",{className:"feedlane-form-footer"},(0,n.createElement)("span",null,"🔗 Powered by ",window.feedlaneData?.siteName||"Feedlane")))):(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-ideas__list"},0===p.length?(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,n.createElement)("h3",null,"No Ideas Yet"),(0,n.createElement)("p",null,"Be the first to share your brilliant idea!")):p.map((e=>(0,n.createElement)(Q,{key:e.id,idea:e})))),(0,n.createElement)("div",{className:"feedlane-suggest-button"},(0,n.createElement)("button",{className:"feedlane-suggest-btn",onClick:()=>t(!0)},"💡 SUGGEST A NEW IDEA"))))},q=({status:e})=>{const{data:t,isLoading:r,error:a}=(0,x.I)({queryKey:["roadmap-ideas",e.slug],queryFn:()=>(0,I.Zu)({status:e.slug,per_page:50,orderby:"votes"}),staleTime:3e5});if(r)return(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading roadmap..."));if(a)return(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load roadmap items."));const s=t?.ideas||[];return 0===s.length?(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"🚧"),(0,n.createElement)("h3",null,"No Items"),(0,n.createElement)("p",null,"No items in ",e.name.toLowerCase()," yet.")):(0,n.createElement)("div",{className:"feedlane-roadmap-items"},s.map((e=>(0,n.createElement)(j,{key:e.id,idea:e}))))},j=({idea:e})=>{const t={"feature-request":{label:"FEATURE REQUEST",color:"#10B981"},improvement:{label:"IMPROVEMENT",color:"#F59E0B"},"bug-fix":{label:"BUG FIX",color:"#EF4444"},integration:{label:"INTEGRATION",color:"#8B5CF6"}}[e.category]||{label:"FEATURE REQUEST",color:"#10B981"};return(0,n.createElement)("article",{className:"feedlane-beamer-roadmap-item"},(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__header"},(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__vote"},(0,n.createElement)("button",{className:"feedlane-beamer-roadmap-item__vote-btn"},(0,n.createElement)(C.wAb,{size:16})),(0,n.createElement)("span",{className:"feedlane-beamer-roadmap-item__vote-count"},e.vote_count||0)),(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__content"},(0,n.createElement)("h3",{className:"feedlane-beamer-roadmap-item__title"},e.title),(0,n.createElement)("span",{className:"feedlane-beamer-roadmap-item__category",style:{color:t.color}},t.label),e.details&&(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__details"},(0,n.createElement)("ul",null,e.details.split("\n").filter((e=>e.trim())).map(((e,t)=>(0,n.createElement)("li",{key:t},e.trim()))))),e.comment_count>0&&(0,n.createElement)("button",{className:"feedlane-beamer-roadmap-item__comments"},(0,n.createElement)(C.mEP,{size:14}),"Add a comment"))))},D=()=>{const[e,t]=(0,n.useState)("under-review"),r=[{slug:"under-review",name:"Under review",color:"#6B7280",icon:"●"},{slug:"planned",name:"Planned",color:"#EF4444",icon:"●"},{slug:"in-progress",name:"In Progress",color:"#F59E0B",icon:"●"},{slug:"completed",name:"Completed",color:"#10B981",icon:"●"}];return(0,n.createElement)("div",{className:"feedlane-roadmap"},(0,n.createElement)("div",{className:"feedlane-roadmap__tabs"},r.map((r=>(0,n.createElement)("button",{key:r.slug,onClick:()=>t(r.slug),className:"feedlane-roadmap__tab "+(e===r.slug?"active":""),style:e===r.slug?{borderBottomColor:r.color,color:r.color}:{}},(0,n.createElement)("span",{className:"feedlane-roadmap__tab-indicator",style:{color:r.color}},r.icon),r.name)))),(0,n.createElement)("div",{className:"feedlane-roadmap__content"},(0,n.createElement)(q,{status:r.find((t=>t.slug===e))})))},A=()=>{const[e,t]=(0,n.useState)(!1),[r,a]=(0,n.useState)("newsfeed"),[s,i]=(0,n.useState)({});(0,n.useEffect)((()=>{if(window.feedlaneData&&window.feedlaneData.settings){i(window.feedlaneData.settings);const{enable_newsfeed:e,enable_ideas:t,enable_roadmap:r}=window.feedlaneData.settings;e?a("newsfeed"):t?a("ideas"):r&&a("roadmap")}}),[]);const o=()=>{t(!1)},l=(()=>{const e=[];return s.enable_newsfeed&&e.push({id:"newsfeed",label:"Newsfeed",icon:N,component:F}),s.enable_ideas&&e.push({id:"ideas",label:"Ideas",icon:O,component:P}),s.enable_roadmap&&e.push({id:"roadmap",label:"Roadmap",icon:k,component:D}),e})();if(0===l.length)return null;const c=s.sidebar_position||"right",u=s.primary_color||"#4F46E5",d=l.find((e=>e.id===r))?.component||F;return(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:`feedlane-floating-button feedlane-floating-button--${c}`,onClick:()=>{t(!e)},style:{backgroundColor:"#000"}},(0,n.createElement)("span",{className:"feedlane-floating-text"},"Feedback")),e&&(0,n.createElement)("div",{className:"feedlane-overlay",onClick:o}),(0,n.createElement)("div",{className:`feedlane-sidebar feedlane-sidebar--${c} ${e?"feedlane-sidebar--open":""}`,style:{"--feedlane-primary-color":u}},(0,n.createElement)("div",{className:"feedlane-sidebar__header",style:{backgroundColor:u}},(0,n.createElement)("div",{className:"feedlane-sidebar__header-content"},(0,n.createElement)("h2",{className:"feedlane-sidebar__title"},(()=>{switch(r){case"newsfeed":return"What's new on "+(window.feedlaneData?.siteName||"Our Site");case"ideas":return(window.feedlaneData?.siteName||"Our Site")+" Ideas";case"roadmap":return(window.feedlaneData?.siteName||"Our Site")+" Roadmap";default:return"Feedback"}})()),(0,n.createElement)("div",{className:"feedlane-sidebar__header-actions"},(0,n.createElement)("button",{className:"feedlane-sidebar__search-btn"},(0,n.createElement)(C.CKj,{size:20})),(0,n.createElement)("button",{onClick:o,className:"feedlane-sidebar__close","aria-label":"Close sidebar"},(0,n.createElement)(C.yGN,{size:20}))))),(0,n.createElement)("div",{className:"feedlane-sidebar__content"},(0,n.createElement)(d,null)),l.length>1&&(0,n.createElement)("div",{className:"feedlane-sidebar__bottom-nav"},l.map((e=>{const t=e.icon;return(0,n.createElement)("button",{key:e.id,onClick:()=>a(e.id),className:"feedlane-sidebar__nav-item "+(r===e.id?"feedlane-sidebar__nav-item--active":""),style:r===e.id?{color:u}:{}},(0,n.createElement)(t,{size:24}),(0,n.createElement)("span",{className:"feedlane-sidebar__nav-label"},e.label))})))))},M=new class{#H;#s;#A;#K;#z;#W;#Z;#V;constructor(e={}){this.#H=e.queryCache||new d,this.#s=e.mutationCache||new m,this.#A=e.defaultOptions||{},this.#K=new Map,this.#z=new Map,this.#W=0}mount(){this.#W++,1===this.#W&&(this.#Z=f.m.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#H.onFocus())})),this.#V=y.t.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#H.onOnline())})))}unmount(){this.#W--,0===this.#W&&(this.#Z?.(),this.#Z=void 0,this.#V?.(),this.#V=void 0)}isFetching(e){return this.#H.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#H.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#H.build(this,t),a=r.state.data;return void 0===a?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,o.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(a))}getQueriesData(e){return this.#H.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const a=this.defaultQueryOptions({queryKey:e}),s=this.#H.get(a.queryHash),n=s?.state.data,i=(0,o.Zw)(t,n);if(void 0!==i)return this.#H.build(this,a).setData(i,{...r,manual:!0})}setQueriesData(e,t,r){return c.jG.batch((()=>this.#H.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#H.get(t.queryHash)?.state}removeQueries(e){const t=this.#H;c.jG.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#H;return c.jG.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const r={revert:!0,...t},a=c.jG.batch((()=>this.#H.findAll(e).map((e=>e.cancel(r)))));return Promise.all(a).then(o.lQ).catch(o.lQ)}invalidateQueries(e,t={}){return c.jG.batch((()=>(this.#H.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},a=c.jG.batch((()=>this.#H.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(o.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(a).then(o.lQ)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#H.build(this,t);return r.isStaleByTime((0,o.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(o.lQ).catch(o.lQ)}fetchInfiniteQuery(e){return e.behavior=b(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(o.lQ).catch(o.lQ)}ensureInfiniteQueryData(e){return e.behavior=b(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return y.t.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#H}getMutationCache(){return this.#s}getDefaultOptions(){return this.#A}setDefaultOptions(e){this.#A=e}setQueryDefaults(e,t){this.#K.set((0,o.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#K.values()],r={};return t.forEach((t=>{(0,o.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#z.set((0,o.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#z.values()],r={};return t.forEach((t=>{(0,o.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#A.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,o.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===o.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#A.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#H.clear(),this.#s.clear()}}({defaultOptions:{queries:{retry:2,staleTime:3e5}}}),L=()=>(0,n.createElement)(E.Ht,{client:M},(0,n.createElement)(A,null),(0,n.createElement)(w.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelectorAll(".feedlane-sidebar-container");if(e.length>0)e.forEach((e=>{(0,i.H)(e).render((0,n.createElement)(L,null))}));else{const e=document.createElement("div");e.id="feedlane-floating-sidebar",e.className="feedlane-floating-sidebar",document.body.appendChild(e),(0,i.H)(e).render((0,n.createElement)(L,null))}document.querySelectorAll(".feedlane-reactions").forEach((e=>{const t=e.dataset.postId;t&&s.e(828).then(s.bind(s,828)).then((({default:r})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:M},(0,n.createElement)(r,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-feedback-form").forEach((e=>{const t=e.dataset.postId;t&&s.e(258).then(s.bind(s,258)).then((({default:r})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:M},(0,n.createElement)(r,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-idea-form").forEach((e=>{s.e(332).then(s.bind(s,332)).then((({default:t})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:M},(0,n.createElement)(t,null)))}))})),document.querySelectorAll(".feedlane-idea-actions, .feedlane-item-actions").forEach((e=>{const t=e.dataset.ideaId;t&&s.e(510).then(s.bind(s,510)).then((({default:r})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:M},(0,n.createElement)(r,{ideaId:parseInt(t)})))}))}))}))})();