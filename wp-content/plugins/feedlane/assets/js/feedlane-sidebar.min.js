(()=>{"use strict";var e,t,a={14:(e,t,a)=>{a.d(t,{n:()=>u});var r=a(609),s=a(259),n=a(474),i=a(613),o=a(383),l=class extends i.Q{#e;#t=void 0;#a;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#a,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutation<PERSON>ey)!==(0,o.EN)(this.options.mutationKey)?this.reset():"pending"===this.#a?.state.status&&this.#a.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#a?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#a?.removeObserver(this),this.#a=void 0,this.#s(),this.#n()}mutate(e,t){return this.#r=t,this.#a?.removeObserver(this),this.#a=this.#e.getMutationCache().build(this.#e,this.options),this.#a.addObserver(this),this.#a.execute(e)}#s(){const e=this.#a?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch((()=>{if(this.#r&&this.hasListeners()){const t=this.#t.variables,a=this.#t.context;"success"===e?.type?(this.#r.onSuccess?.(e.data,t,a),this.#r.onSettled?.(e.data,null,t,a)):"error"===e?.type&&(this.#r.onError?.(e.error,t,a),this.#r.onSettled?.(void 0,e.error,t,a))}this.listeners.forEach((e=>{e(this.#t)}))}))}},c=a(967);function u(e,t){const a=(0,c.jE)(t),[s]=r.useState((()=>new l(a,e)));r.useEffect((()=>{s.setOptions(e)}),[s,e]);const i=r.useSyncExternalStore(r.useCallback((e=>s.subscribe(n.jG.batchCalls(e))),[s]),(()=>s.getCurrentResult()),(()=>s.getCurrentResult())),u=r.useCallback(((e,t)=>{s.mutate(e,t).catch(o.lQ)}),[s]);if(i.error&&(0,o.GU)(s.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:u,mutateAsync:i.mutate}}},44:(e,t,a)=>{a.d(t,{A:()=>n});const r=window.feedlaneData?.rest_url||"/wp-json/",s=window.feedlaneData?.nonce||"",n=new class{constructor(){this.baseURL=r,this.nonce=s}async request(e,t={}){const a=`${this.baseURL}feedlane/v1${e}`,r={headers:{"Content-Type":"application/json","X-WP-Nonce":this.nonce}},s={...r,...t,headers:{...r.headers,...t.headers}};try{const e=await fetch(a,s);if(!e.ok){const t=await e.json().catch((()=>({})));throw new Error(t.message||`HTTP error! status: ${e.status}`)}return await e.json()}catch(e){throw e}}async get(e,t={}){const a=new URLSearchParams(t).toString(),r=a?`${e}?${a}`:e;return this.request(r,{method:"GET"})}async post(e,t={}){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t={}){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}async postFormData(e,t){if(Array.from(t.entries()).some((([e,t])=>t instanceof File)))return this.request(e,{method:"POST",headers:{"X-WP-Nonce":this.nonce},body:t});{const a={};for(const[e,r]of t.entries())a[e]=r;return this.request(e,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":this.nonce},body:JSON.stringify(a)})}}}},69:(e,t,a)=>{a.d(t,{AH:()=>m,I4:()=>g,i7:()=>b,mj:()=>v});let r={data:""},s=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||r,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,i=/\/\*[^]*?\*\/|  +/g,o=/\n+/g,l=(e,t)=>{let a="",r="",s="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?a=n+" "+i+";":r+="f"==n[1]?l(i,n):n+"{"+l(i,"k"==n[1]?"":t)+"}":"object"==typeof i?r+=l(i,t?t.replace(/([^,])+/g,(e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=l.p?l.p(n,i):n+":"+i+";")}return a+(t&&s?t+"{"+s+"}":s)+r},c={},u=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+u(e[a]);return t}return e},d=(e,t,a,r,s)=>{let d=u(e),h=c[d]||(c[d]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(d));if(!c[h]){let t=d!==e?e:(e=>{let t,a,r=[{}];for(;t=n.exec(e.replace(i,""));)t[4]?r.shift():t[3]?(a=t[3].replace(o," ").trim(),r.unshift(r[0][a]=r[0][a]||{})):r[0][t[1]]=t[2].replace(o," ").trim();return r[0]})(e);c[h]=l(s?{["@keyframes "+h]:t}:t,a?"":"."+h)}let m=a&&c.g?c.g:null;return a&&(c.g=c[h]),((e,t,a,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=a?e+t.data:t.data+e)})(c[h],t,r,m),h},h=(e,t,a)=>e.reduce(((e,r,s)=>{let n=t[s];if(n&&n.call){let e=n(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":l(e,""):!1===e?"":e}return e+r+(null==n?"":n)}),"");function m(e){let t=this||{},a=e.call?e(t.p):e;return d(a.unshift?a.raw?h(a,[].slice.call(arguments,1),t.p):a.reduce(((e,a)=>Object.assign(e,a&&a.call?a(t.p):a)),{}):a,s(t.target),t.g,t.o,t.k)}m.bind({g:1});let f,p,y,b=m.bind({k:1});function v(e,t,a,r){l.p=t,f=e,p=a,y=r}function g(e,t){let a=this||{};return function(){let r=arguments;function s(n,i){let o=Object.assign({},n),l=o.className||s.className;a.p=Object.assign({theme:p&&p()},o),a.o=/ *go\d+/.test(l),o.className=m.apply(a,r)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),y&&c[0]&&y(o),f(c,o)}return t?t(s):s}}},70:(e,t,a)=>{e.exports=a(462)},79:(e,t,a)=>{a.d(t,{m:()=>n});var r=a(613),s=a(383),n=new class extends r.Q{#i;#o;#l;constructor(){super(),this.#l=e=>{if(!s.S$&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#o||this.setEventListener(this.#l)}onUnsubscribe(){this.hasListeners()||(this.#o?.(),this.#o=void 0)}setEventListener(e){this.#l=e,this.#o?.(),this.#o=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#i!==e&&(this.#i=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#i?this.#i:"hidden"!==globalThis.document?.visibilityState}}},258:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var r=a(609),s=a(967),n=a(470),i=a(14),o=a(880),l=a(326),c=a(44);const u=({postId:e})=>{const[t,a]=(0,r.useState)(!1),[u,d]=(0,r.useState)(!1),[h,m]=(0,r.useState)({feedback_text:"",user_name:"",user_email:""}),f=(0,s.jE)(),p=window.feedlaneData?.is_user_logged_in||!1,y=window.feedlaneData?.settings?.enable_guest_submissions||!1,{data:b,isLoading:v}=(0,n.I)({queryKey:["feedback",e],queryFn:()=>(async(e,t={})=>{const a={page:1,per_page:10,...t};return c.A.get(`/feedback/${e}`,a)})(e),enabled:u,staleTime:12e4}),g=(0,i.n)({mutationFn:t=>(async(e,t)=>c.A.post("/feedback",{post_id:e,...t}))(e,t),onSuccess:()=>{l.Ay.success("Feedback submitted successfully!"),m({feedback_text:"",user_name:"",user_email:""}),a(!1),f.invalidateQueries(["feedback",e])},onError:e=>{const t=e.response?.data?.message||"Failed to submit feedback";l.Ay.error(t)}}),E=e=>{const{name:t,value:a}=e.target;m((e=>({...e,[t]:a})))};return p||y?(0,r.createElement)("div",{className:"feedlane-feedback"},(0,r.createElement)("div",{className:"feedlane-feedback__toggle"},(0,r.createElement)("button",{onClick:()=>a(!t),className:"feedlane-feedback__toggle-button"},(0,r.createElement)(o.X6_,{size:16}),(0,r.createElement)("span",null,"Leave Feedback"),t?(0,r.createElement)(o.wAb,{size:16}):(0,r.createElement)(o.fK4,{size:16})),b&&b.pagination.total>0&&(0,r.createElement)("button",{onClick:()=>d(!u),className:"feedlane-feedback__view-button"},"View ",b.pagination.total," feedback",u?(0,r.createElement)(o.wAb,{size:14}):(0,r.createElement)(o.fK4,{size:14}))),t&&(0,r.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),h.feedback_text.trim()){if(!p&&y){if(!h.user_name.trim()||!h.user_email.trim())return void l.Ay.error("Name and email are required");if(t=h.user_email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return void l.Ay.error("Please enter a valid email address")}g.mutate(h)}else l.Ay.error("Please enter your feedback");var t},className:"feedlane-feedback__form"},!p&&y&&(0,r.createElement)("div",{className:"feedlane-feedback__guest-fields"},(0,r.createElement)("div",{className:"feedlane-form-row"},(0,r.createElement)("input",{type:"text",name:"user_name",value:h.user_name,onChange:E,placeholder:"Your name",className:"feedlane-input feedlane-input--small",required:!0}),(0,r.createElement)("input",{type:"email",name:"user_email",value:h.user_email,onChange:E,placeholder:"Your email",className:"feedlane-input feedlane-input--small",required:!0}))),(0,r.createElement)("div",{className:"feedlane-feedback__text-field"},(0,r.createElement)("textarea",{name:"feedback_text",value:h.feedback_text,onChange:E,placeholder:"Share your thoughts about this update...",className:"feedlane-textarea",rows:"3",required:!0})),(0,r.createElement)("div",{className:"feedlane-feedback__actions"},(0,r.createElement)("button",{type:"button",onClick:()=>a(!1),className:"feedlane-button feedlane-button--secondary feedlane-button--small"},"Cancel"),(0,r.createElement)("button",{type:"submit",disabled:g.isPending,className:"feedlane-button feedlane-button--primary feedlane-button--small"},g.isPending?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Sending..."):(0,r.createElement)(r.Fragment,null,(0,r.createElement)(o.kGk,{size:14}),"Send Feedback")))),u&&(0,r.createElement)("div",{className:"feedlane-feedback__list"},v?(0,r.createElement)("div",{className:"feedlane-feedback__loading"},(0,r.createElement)("div",{className:"feedlane-spinner"}),(0,r.createElement)("span",null,"Loading feedback...")):b&&b.feedback.length>0?(0,r.createElement)("div",{className:"feedlane-feedback__items"},b.feedback.map((e=>(0,r.createElement)("div",{key:e.id,className:"feedlane-feedback__item"},(0,r.createElement)("div",{className:"feedlane-feedback__item-header"},(0,r.createElement)("span",{className:"feedlane-feedback__author"},e.author.name),(0,r.createElement)("span",{className:"feedlane-feedback__time"},e.time_ago)),(0,r.createElement)("div",{className:"feedlane-feedback__item-content"},e.feedback_text))))):(0,r.createElement)("div",{className:"feedlane-feedback__empty"},(0,r.createElement)("p",null,"No feedback yet. Be the first to share your thoughts!")))):null}},259:(e,t,a)=>{a.d(t,{$:()=>o,s:()=>i});var r=a(474),s=a(515),n=a(483),i=class extends s.k{#c;#u;#d;constructor(e){super(),this.mutationId=e.mutationId,this.#u=e.mutationCache,this.#c=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#c.includes(e)||(this.#c.push(e),this.clearGcTimeout(),this.#u.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#c=this.#c.filter((t=>t!==e)),this.scheduleGc(),this.#u.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#c.length||("pending"===this.state.status?this.scheduleGc():this.#u.remove(this))}continue(){return this.#d?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#d=(0,n.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#u.canRun(this)});const a="pending"===this.state.status,r=!this.#d.canStart();try{if(a)t();else{this.#h({type:"pending",variables:e,isPaused:r}),await(this.#u.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:r})}const s=await this.#d.start();return await(this.#u.config.onSuccess?.(s,e,this.state.context,this)),await(this.options.onSuccess?.(s,e,this.state.context)),await(this.#u.config.onSettled?.(s,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(s,null,e,this.state.context)),this.#h({type:"success",data:s}),s}catch(t){try{throw await(this.#u.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#u.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#u.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),r.jG.batch((()=>{this.#c.forEach((t=>{t.onMutationUpdate(e)})),this.#u.notify({mutation:this,type:"updated",action:e})}))}};function o(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},326:(e,t,a)=>{a.d(t,{Ay:()=>L,l$:()=>M});var r=a(609),s=a(69),n=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,i=(()=>{let e=0;return()=>(++e).toString()})(),o=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),l=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:a}=t;return l(e,{type:e.toasts.find((e=>e.id===a.id))?1:0,toast:a});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map((e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+s})))}}},c=[],u={toasts:[],pausedAt:void 0},d=e=>{u=l(u,e),c.forEach((e=>{e(u)}))},h={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},m=e=>(t,a)=>{let r=((e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||i()}))(t,e,a);return d({type:2,toast:r}),r.id},f=(e,t)=>m("blank")(e,t);f.error=m("error"),f.success=m("success"),f.loading=m("loading"),f.custom=m("custom"),f.dismiss=e=>{d({type:3,toastId:e})},f.remove=e=>d({type:4,toastId:e}),f.promise=(e,t,a)=>{let r=f.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let s=t.success?n(t.success,e):void 0;return s?f.success(s,{id:r,...a,...null==a?void 0:a.success}):f.dismiss(r),e})).catch((e=>{let s=t.error?n(t.error,e):void 0;s?f.error(s,{id:r,...a,...null==a?void 0:a.error}):f.dismiss(r)})),e};var p=(e,t)=>{d({type:1,toast:{id:e,height:t}})},y=()=>{d({type:5,time:Date.now()})},b=new Map,v=s.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,g=s.i7`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,E=s.i7`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,_=(0,s.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${v} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${g} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${E} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,w=s.i7`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,k=(0,s.I4)("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${w} 1s linear infinite;
`,N=s.i7`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,C=s.i7`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,O=(0,s.I4)("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${N} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${C} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,S=(0,s.I4)("div")`
  position: absolute;
`,x=(0,s.I4)("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,R=s.i7`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,F=(0,s.I4)("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${R} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,P=({toast:e})=>{let{icon:t,type:a,iconTheme:s}=e;return void 0!==t?"string"==typeof t?r.createElement(F,null,t):t:"blank"===a?null:r.createElement(x,null,r.createElement(k,{...s}),"loading"!==a&&r.createElement(S,null,"error"===a?r.createElement(_,{...s}):r.createElement(O,{...s})))},q=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,I=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,T=(0,s.I4)("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Q=(0,s.I4)("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,A=r.memo((({toast:e,position:t,style:a,children:i})=>{let l=e.height?((e,t)=>{let a=e.includes("top")?1:-1,[r,n]=o()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[q(a),I(a)];return{animation:t?`${(0,s.i7)(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${(0,s.i7)(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},c=r.createElement(P,{toast:e}),u=r.createElement(Q,{...e.ariaProps},n(e.message,e));return r.createElement(T,{className:e.className,style:{...l,...a,...e.style}},"function"==typeof i?i({icon:c,message:u}):r.createElement(r.Fragment,null,c,u))}));(0,s.mj)(r.createElement);var j=({id:e,className:t,style:a,onHeightUpdate:s,children:n})=>{let i=r.useCallback((t=>{if(t){let a=()=>{let a=t.getBoundingClientRect().height;s(e,a)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,s]);return r.createElement("div",{ref:i,className:t,style:a},n)},D=s.AH`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,M=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:s,children:i,containerStyle:l,containerClassName:m})=>{let{toasts:v,handlers:g}=(e=>{let{toasts:t,pausedAt:a}=((e={})=>{let[t,a]=(0,r.useState)(u),s=(0,r.useRef)(u);(0,r.useEffect)((()=>(s.current!==u&&a(u),c.push(a),()=>{let e=c.indexOf(a);e>-1&&c.splice(e,1)})),[]);let n=t.toasts.map((t=>{var a,r,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||h[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}}));return{...t,toasts:n}})(e);(0,r.useEffect)((()=>{if(a)return;let e=Date.now(),r=t.map((t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(a<0))return setTimeout((()=>f.dismiss(t.id)),a);t.visible&&f.dismiss(t.id)}));return()=>{r.forEach((e=>e&&clearTimeout(e)))}}),[t,a]);let s=(0,r.useCallback)((()=>{a&&d({type:6,time:Date.now()})}),[a]),n=(0,r.useCallback)(((e,a)=>{let{reverseOrder:r=!1,gutter:s=8,defaultPosition:n}=a||{},i=t.filter((t=>(t.position||n)===(e.position||n)&&t.height)),o=i.findIndex((t=>t.id===e.id)),l=i.filter(((e,t)=>t<o&&e.visible)).length;return i.filter((e=>e.visible)).slice(...r?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+s),0)}),[t]);return(0,r.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(b.has(e))return;let a=setTimeout((()=>{b.delete(e),d({type:4,toastId:e})}),t);b.set(e,a)})(e.id,e.removeDelay);else{let t=b.get(e.id);t&&(clearTimeout(t),b.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:p,startPause:y,endPause:s,calculateOffset:n}}})(a);return r.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...l},className:m,onMouseEnter:g.startPause,onMouseLeave:g.endPause},v.map((a=>{let l=a.position||t,c=((e,t)=>{let a=e.includes("top"),r=a?{top:0}:{bottom:0},s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:o()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...r,...s}})(l,g.calculateOffset(a,{reverseOrder:e,gutter:s,defaultPosition:t}));return r.createElement(j,{id:a.id,key:a.id,onHeightUpdate:g.updateHeight,className:a.visible?D:"",style:c},"custom"===a.type?n(a.message,a):i?i(a):r.createElement(A,{toast:a,position:l}))})))},L=f},332:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var r=a(609),s=a(14),n=a(880),i=a(326),o=a(559);const l=({onSuccess:e,onCancel:t})=>{const a=window.feedlaneData?.categories||[{value:"improvement",label:"Improvement"},{value:"feature-request",label:"Feature Request"},{value:"bug",label:"Bug Report"},{value:"feedback",label:"General Feedback"}],l=window.feedlaneData?.currentUser?.id||window.feedlaneData?.is_user_logged_in,[c,u]=(0,r.useState)({title:"",details:"",category:a[0]?.slug||a[0]?.value||"feature-request",email:"",first_name:"",last_name:"",image:null}),[d,h]=(0,r.useState)(null),m=(0,s.n)({mutationFn:e=>(0,o.Gn)(e),onSuccess:()=>{i.Ay.success("Idea submitted successfully! It will be reviewed before being published."),e&&e()},onError:e=>{const t=e.response?.data?.message||"Failed to submit idea";i.Ay.error(t)}}),f=e=>{const{name:t,value:a}=e.target;u((e=>({...e,[t]:a})))};return(0,r.createElement)("div",{className:"feedlane-idea-form"},(0,r.createElement)("div",{className:"feedlane-idea-form__header"},(0,r.createElement)("h4",null,"Submit Your Idea"),(0,r.createElement)("button",{onClick:t,className:"feedlane-idea-form__close"},(0,r.createElement)(n.yGN,{size:20}))),(0,r.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),c.title.trim()&&c.details.trim()){if(!l){if(!c.first_name.trim()||!c.last_name.trim()||!c.email.trim())return void i.Ay.error("Name and email are required");if(t=c.email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return void i.Ay.error("Please enter a valid email address")}if(c.image){const e=new FormData;Object.keys(c).forEach((t=>{null!==c[t]&&e.append(t,c[t])})),m.mutate(e)}else{const e={...c};delete e.image,m.mutate(e)}}else i.Ay.error("Title and details are required");var t},className:"feedlane-idea-form__form"},!l&&(0,r.createElement)("div",{className:"feedlane-idea-form__guest-fields"},(0,r.createElement)("div",{className:"feedlane-form-row"},(0,r.createElement)("input",{type:"text",name:"first_name",value:c.first_name,onChange:f,placeholder:"First name",className:"feedlane-input",required:!0}),(0,r.createElement)("input",{type:"text",name:"last_name",value:c.last_name,onChange:f,placeholder:"Last name",className:"feedlane-input",required:!0})),(0,r.createElement)("input",{type:"email",name:"email",value:c.email,onChange:f,placeholder:"Email address",className:"feedlane-input",required:!0})),(0,r.createElement)("div",{className:"feedlane-form-field"},(0,r.createElement)("label",{htmlFor:"idea-title",className:"feedlane-label"},"Title *"),(0,r.createElement)("input",{id:"idea-title",type:"text",name:"title",value:c.title,onChange:f,placeholder:"Brief description of your idea",className:"feedlane-input",required:!0})),(0,r.createElement)("div",{className:"feedlane-form-field"},(0,r.createElement)("label",{htmlFor:"idea-category",className:"feedlane-label"},"Category *"),(0,r.createElement)("select",{id:"idea-category",name:"category",value:c.category,onChange:f,className:"feedlane-select",required:!0},a.map((e=>(0,r.createElement)("option",{key:e.slug||e.value,value:e.slug||e.value},e.name||e.label))))),(0,r.createElement)("div",{className:"feedlane-form-field"},(0,r.createElement)("label",{htmlFor:"idea-details",className:"feedlane-label"},"Details *"),(0,r.createElement)("textarea",{id:"idea-details",name:"details",value:c.details,onChange:f,placeholder:"Provide more details about your idea...",className:"feedlane-textarea",rows:"4",required:!0})),(0,r.createElement)("div",{className:"feedlane-form-field"},(0,r.createElement)("label",{className:"feedlane-label"},"Image (optional)"),d?(0,r.createElement)("div",{className:"feedlane-image-preview"},(0,r.createElement)("img",{src:d,alt:"Preview"}),(0,r.createElement)("button",{type:"button",onClick:()=>{u((e=>({...e,image:null}))),h(null)},className:"feedlane-image-preview__remove"},(0,r.createElement)(n.yGN,{size:16}))):(0,r.createElement)("div",{className:"feedlane-file-upload"},(0,r.createElement)("input",{type:"file",id:"idea-image",accept:"image/*",onChange:e=>{const t=e.target.files[0];if(t){if(!t.type.startsWith("image/"))return void i.Ay.error("Please select an image file");if(t.size>5242880)return void i.Ay.error("Image size must be less than 5MB");u((e=>({...e,image:t})));const e=new FileReader;e.onload=e=>{h(e.target.result)},e.readAsDataURL(t)}},className:"feedlane-file-input"}),(0,r.createElement)("label",{htmlFor:"idea-image",className:"feedlane-file-label"},(0,r.createElement)(n.B88,{size:20}),(0,r.createElement)("span",null,"Upload Image"),(0,r.createElement)("small",null,"Max 5MB, JPG/PNG")))),(0,r.createElement)("div",{className:"feedlane-idea-form__actions"},(0,r.createElement)("button",{type:"button",onClick:t,className:"feedlane-button feedlane-button--secondary"},"Cancel"),(0,r.createElement)("button",{type:"submit",disabled:m.isPending,className:"feedlane-button feedlane-button--primary"},m.isPending?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Submitting..."):"Submit Idea"))))}},383:(e,t,a)=>{a.d(t,{Cp:()=>f,EN:()=>m,Eh:()=>c,F$:()=>h,GU:()=>O,MK:()=>u,S$:()=>r,ZM:()=>C,ZZ:()=>k,Zw:()=>n,d2:()=>l,f8:()=>y,gn:()=>i,hT:()=>N,j3:()=>o,lQ:()=>s,nJ:()=>d,pl:()=>_,y9:()=>w,yy:()=>E});var r="undefined"==typeof window||"Deno"in globalThis;function s(){}function n(e,t){return"function"==typeof e?e(t):e}function i(e){return"number"==typeof e&&e>=0&&e!==1/0}function o(e,t){return Math.max(e+(t||0)-Date.now(),0)}function l(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){const{type:a="all",exact:r,fetchStatus:s,predicate:n,queryKey:i,stale:o}=e;if(i)if(r){if(t.queryHash!==h(i,t.options))return!1}else if(!f(t.queryKey,i))return!1;if("all"!==a){const e=t.isActive();if("active"===a&&!e)return!1;if("inactive"===a&&e)return!1}return!("boolean"==typeof o&&t.isStale()!==o||s&&s!==t.state.fetchStatus||n&&!n(t))}function d(e,t){const{exact:a,status:r,predicate:s,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(a){if(m(t.options.mutationKey)!==m(n))return!1}else if(!f(t.options.mutationKey,n))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function h(e,t){return(t?.queryKeyHashFn||m)(e)}function m(e){return JSON.stringify(e,((e,t)=>v(t)?Object.keys(t).sort().reduce(((e,a)=>(e[a]=t[a],e)),{}):t))}function f(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((a=>f(e[a],t[a])))}function p(e,t){if(e===t)return e;const a=b(e)&&b(t);if(a||v(e)&&v(t)){const r=a?e:Object.keys(e),s=r.length,n=a?t:Object.keys(t),i=n.length,o=a?[]:{};let l=0;for(let s=0;s<i;s++){const i=a?s:n[s];(!a&&r.includes(i)||a)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,l++):(o[i]=p(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&l++)}return s===i&&l===s?e:o}return t}function y(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const a in e)if(e[a]!==t[a])return!1;return!0}function b(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function v(e){if(!g(e))return!1;const t=e.constructor;if(void 0===t)return!0;const a=t.prototype;return!!g(a)&&!!a.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function E(e){return new Promise((t=>{setTimeout(t,e)}))}function _(e,t,a){return"function"==typeof a.structuralSharing?a.structuralSharing(e,t):!1!==a.structuralSharing?p(e,t):t}function w(e,t,a=0){const r=[...e,t];return a&&r.length>a?r.slice(1):r}function k(e,t,a=0){const r=[t,...e];return a&&r.length>a?r.slice(0,-1):r}var N=Symbol();function C(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==N?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}function O(e,t){return"function"==typeof e?e(...t):!!e}},443:(e,t,a)=>{function r(){let e,t;const a=new Promise(((a,r)=>{e=a,t=r}));function r(e){Object.assign(a,e),delete a.resolve,delete a.reject}return a.status="pending",a.catch((()=>{})),a.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},a.reject=e=>{r({status:"rejected",reason:e}),t(e)},a}a.d(t,{T:()=>r})},462:(e,t,a)=>{var r=a(609),s=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,a){var r,l={},c=null,u=null;for(r in void 0!==a&&(c=""+a),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)n.call(t,r)&&!o.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:s,type:e,key:c,ref:u,props:l,_owner:i.current}}},470:(e,t,a)=>{a.d(t,{I:()=>S});var r=a(79),s=a(474),n=a(702),i=a(613),o=a(443),l=a(383),c=class extends i.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#m=null,this.#f=(0,o.T)(),this.options.experimental_prefetchInRender||this.#f.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#p=void 0;#y=void 0;#t=void 0;#b;#v;#f;#m;#g;#E;#_;#w;#k;#N;#C=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#p.addObserver(this),u(this.#p,this.options)?this.#O():this.updateResult(),this.#S())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#p,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#p,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#x(),this.#R(),this.#p.removeObserver(this)}setOptions(e){const t=this.options,a=this.#p;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#p))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#F(),this.#p.setOptions(this.options),t._defaulted&&!(0,l.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#p,observer:this});const r=this.hasListeners();r&&h(this.#p,a,this.options,t)&&this.#O(),this.updateResult(),!r||this.#p===a&&(0,l.Eh)(this.options.enabled,this.#p)===(0,l.Eh)(t.enabled,this.#p)&&(0,l.d2)(this.options.staleTime,this.#p)===(0,l.d2)(t.staleTime,this.#p)||this.#P();const s=this.#q();!r||this.#p===a&&(0,l.Eh)(this.options.enabled,this.#p)===(0,l.Eh)(t.enabled,this.#p)&&s===this.#N||this.#I(s)}getOptimisticResult(e){const t=this.#e.getQueryCache().build(this.#e,e),a=this.createResult(t,e);return r=this,s=a,!(0,l.f8)(r.getCurrentResult(),s)&&(this.#t=a,this.#v=this.options,this.#b=this.#p.state),a;var r,s}getCurrentResult(){return this.#t}trackResult(e,t){return new Proxy(e,{get:(e,a)=>(this.trackProp(a),t?.(a),Reflect.get(e,a))})}trackProp(e){this.#C.add(e)}getCurrentQuery(){return this.#p}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#e.defaultQueryOptions(e),a=this.#e.getQueryCache().build(this.#e,t);return a.fetch().then((()=>this.createResult(a,t)))}fetch(e){return this.#O({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#t)))}#O(e){this.#F();let t=this.#p.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#P(){this.#x();const e=(0,l.d2)(this.options.staleTime,this.#p);if(l.S$||this.#t.isStale||!(0,l.gn)(e))return;const t=(0,l.j3)(this.#t.dataUpdatedAt,e)+1;this.#w=setTimeout((()=>{this.#t.isStale||this.updateResult()}),t)}#q(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#p):this.options.refetchInterval)??!1}#I(e){this.#R(),this.#N=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#p)&&(0,l.gn)(this.#N)&&0!==this.#N&&(this.#k=setInterval((()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#O()}),this.#N))}#S(){this.#P(),this.#I(this.#q())}#x(){this.#w&&(clearTimeout(this.#w),this.#w=void 0)}#R(){this.#k&&(clearInterval(this.#k),this.#k=void 0)}createResult(e,t){const a=this.#p,r=this.options,s=this.#t,i=this.#b,c=this.#v,d=e!==a?e.state:this.#y,{state:f}=e;let p,y={...f},b=!1;if(t._optimisticResults){const s=this.hasListeners(),i=!s&&u(e,t),o=s&&h(e,a,t,r);(i||o)&&(y={...y,...(0,n.k)(f.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:v,errorUpdatedAt:g,status:E}=y;p=y.data;let _=!1;if(void 0!==t.placeholderData&&void 0===p&&"pending"===E){let e;s?.isPlaceholderData&&t.placeholderData===c?.placeholderData?(e=s.data,_=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#_?.state.data,this.#_):t.placeholderData,void 0!==e&&(E="success",p=(0,l.pl)(s?.data,e,t),b=!0)}if(t.select&&void 0!==p&&!_)if(s&&p===i?.data&&t.select===this.#g)p=this.#E;else try{this.#g=t.select,p=t.select(p),p=(0,l.pl)(s?.data,p,t),this.#E=p,this.#m=null}catch(e){this.#m=e}this.#m&&(v=this.#m,p=this.#E,g=Date.now(),E="error");const w="fetching"===y.fetchStatus,k="pending"===E,N="error"===E,C=k&&w,O=void 0!==p,S={status:E,fetchStatus:y.fetchStatus,isPending:k,isSuccess:"success"===E,isError:N,isInitialLoading:C,isLoading:C,data:p,dataUpdatedAt:y.dataUpdatedAt,error:v,errorUpdatedAt:g,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>d.dataUpdateCount||y.errorUpdateCount>d.errorUpdateCount,isFetching:w,isRefetching:w&&!k,isLoadingError:N&&!O,isPaused:"paused"===y.fetchStatus,isPlaceholderData:b,isRefetchError:N&&O,isStale:m(e,t),refetch:this.refetch,promise:this.#f};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===S.status?e.reject(S.error):void 0!==S.data&&e.resolve(S.data)},r=()=>{const e=this.#f=S.promise=(0,o.T)();t(e)},s=this.#f;switch(s.status){case"pending":e.queryHash===a.queryHash&&t(s);break;case"fulfilled":"error"!==S.status&&S.data===s.value||r();break;case"rejected":"error"===S.status&&S.error===s.reason||r()}}return S}updateResult(){const e=this.#t,t=this.createResult(this.#p,this.options);this.#b=this.#p.state,this.#v=this.options,void 0!==this.#b.data&&(this.#_=this.#p),(0,l.f8)(t,e)||(this.#t=t,this.#n({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,a="function"==typeof t?t():t;if("all"===a||!a&&!this.#C.size)return!0;const r=new Set(a??this.#C);return this.options.throwOnError&&r.add("error"),Object.keys(this.#t).some((t=>{const a=t;return this.#t[a]!==e[a]&&r.has(a)}))})()}))}#F(){const e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#p)return;const t=this.#p;this.#p=e,this.#y=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#S()}#n(e){s.jG.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#t)})),this.#e.getQueryCache().notify({query:this.#p,type:"observerResultsUpdated"})}))}};function u(e,t){return function(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,a){if(!1!==(0,l.Eh)(t.enabled,e)){const r="function"==typeof a?a(e):a;return"always"===r||!1!==r&&m(e,t)}return!1}function h(e,t,a,r){return(e!==t||!1===(0,l.Eh)(r.enabled,e))&&(!a.suspense||"error"!==e.state.status)&&m(e,a)}function m(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}var f=a(609),p=a(967);a(70);var y=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),b=()=>f.useContext(y),v=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},g=e=>{f.useEffect((()=>{e.clearReset()}),[e])},E=({result:e,errorResetBoundary:t,throwOnError:a,query:r,suspense:s})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(s&&void 0===e.data||(0,l.GU)(a,[e.error,r])),_=f.createContext(!1),w=()=>f.useContext(_),k=(_.Provider,e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))}),N=(e,t)=>e.isLoading&&e.isFetching&&!t,C=(e,t)=>e?.suspense&&t.isPending,O=(e,t,a)=>t.fetchOptimistic(e).catch((()=>{a.clearReset()}));function S(e,t){return function(e,t,a){const r=(0,p.jE)(a),n=w(),i=b(),o=r.defaultQueryOptions(e);r.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=n?"isRestoring":"optimistic",k(o),v(o,i),g(i);const c=!r.getQueryCache().get(o.queryHash),[u]=f.useState((()=>new t(r,o))),d=u.getOptimisticResult(o),h=!n&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback((e=>{const t=h?u.subscribe(s.jG.batchCalls(e)):l.lQ;return u.updateResult(),t}),[u,h]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),f.useEffect((()=>{u.setOptions(o)}),[o,u]),C(o,d))throw O(o,u,i);if(E({result:d,errorResetBoundary:i,throwOnError:o.throwOnError,query:r.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw d.error;if(r.getDefaultOptions().queries?._experimental_afterQuery?.(o,d),o.experimental_prefetchInRender&&!l.S$&&N(d,n)){const e=c?O(o,u,i):r.getQueryCache().get(o.queryHash)?.promise;e?.catch(l.lQ).finally((()=>{u.updateResult()}))}return o.notifyOnChangeProps?d:u.trackResult(d)}(e,c,t)}},474:(e,t,a)=>{a.d(t,{jG:()=>s});var r=e=>setTimeout(e,0),s=function(){let e=[],t=0,a=e=>{e()},s=e=>{e()},n=r;const i=r=>{t?e.push(r):n((()=>{a(r)}))};return{batch:r=>{let i;t++;try{i=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&n((()=>{s((()=>{t.forEach((e=>{a(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{a=e},setBatchNotifyFunction:e=>{s=e},setScheduler:e=>{n=e}}}()},483:(e,t,a)=>{a.d(t,{II:()=>d,v_:()=>l,wm:()=>u});var r=a(79),s=a(860),n=a(443),i=a(383);function o(e){return Math.min(1e3*2**e,3e4)}function l(e){return"online"!==(e??"online")||s.t.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function u(e){return e instanceof c}function d(e){let t,a=!1,u=0,d=!1;const h=(0,n.T)(),m=()=>r.m.isFocused()&&("always"===e.networkMode||s.t.isOnline())&&e.canRun(),f=()=>l(e.networkMode)&&e.canRun(),p=a=>{d||(d=!0,e.onSuccess?.(a),t?.(),h.resolve(a))},y=a=>{d||(d=!0,e.onError?.(a),t?.(),h.reject(a))},b=()=>new Promise((a=>{t=e=>{(d||m())&&a(e)},e.onPause?.()})).then((()=>{t=void 0,d||e.onContinue?.()})),v=()=>{if(d)return;let t;const r=0===u?e.initialPromise:void 0;try{t=r??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(p).catch((t=>{if(d)return;const r=e.retry??(i.S$?0:3),s=e.retryDelay??o,n="function"==typeof s?s(u,t):s,l=!0===r||"number"==typeof r&&u<r||"function"==typeof r&&r(u,t);!a&&l?(u++,e.onFail?.(u,t),(0,i.yy)(n).then((()=>m()?void 0:b())).then((()=>{a?y(t):v()}))):y(t)}))};return{promise:h,cancel:t=>{d||(y(new c(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{a=!0},continueRetry:()=>{a=!1},canStart:f,start:()=>(f()?v():b().then(v),h)}}},515:(e,t,a)=>{a.d(t,{k:()=>s});var r=a(383),s=class{#T;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,r.gn)(this.gcTime)&&(this.#T=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r.S$?1/0:3e5))}clearGcTimeout(){this.#T&&(clearTimeout(this.#T),this.#T=void 0)}}},559:(e,t,a)=>{a.d(t,{Gn:()=>n,Q2:()=>i,Zu:()=>s});var r=a(44);const s=async(e={})=>{const t={page:1,per_page:10,orderby:"votes",...e};return r.A.get("/ideas",t)},n=async e=>e instanceof FormData?r.A.postFormData("/ideas",e):r.A.post("/ideas",e),i=async e=>r.A.post(`/ideas/${e}/vote`)},576:(e,t,a)=>{var r=a(795);t.H=r.createRoot,r.hydrateRoot},609:e=>{e.exports=window.React},613:(e,t,a)=>{a.d(t,{Q:()=>r});var r=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},702:(e,t,a)=>{a.d(t,{X:()=>o,k:()=>l});var r=a(383),s=a(474),n=a(483),i=a(515),o=class extends i.k{#Q;#A;#j;#e;#d;#D;#M;constructor(e){super(),this.#M=!1,this.#D=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#e=e.client,this.#j=this.#e.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#Q=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,a=void 0!==t,r=a?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:a?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:a?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#Q,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#d?.promise}setOptions(e){this.options={...this.#D,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#j.remove(this)}setData(e,t){const a=(0,r.pl)(this.state.data,e,this.options);return this.#h({data:a,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),a}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#d?.promise;return this.#d?.cancel(e),t?t.then(r.lQ).catch(r.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#Q)}isActive(){return this.observers.some((e=>!1!==(0,r.Eh)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===r.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,r.j3)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#d?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#d?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#j.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#d&&(this.#M?this.#d.cancel({revert:!0}):this.#d.cancelRetry()),this.scheduleGc()),this.#j.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#d)return this.#d.continueRetry(),this.#d.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const a=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#M=!0,a.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#e,state:this.state,fetchFn:()=>{const e=(0,r.ZM)(this.options,t),a={client:this.#e,queryKey:this.queryKey,meta:this.meta};return s(a),this.#M=!1,this.options.persister?this.options.persister(e,a,this):e(a)}};s(i),this.options.behavior?.onFetch(i,this),this.#A=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#h({type:"fetch",meta:i.fetchOptions?.meta});const o=e=>{(0,n.wm)(e)&&e.silent||this.#h({type:"error",error:e}),(0,n.wm)(e)||(this.#j.config.onError?.(e,this),this.#j.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#d=(0,n.II)({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:a.abort.bind(a),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void o(e)}this.#j.config.onSuccess?.(e,this),this.#j.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else o(new Error(`${this.queryHash} data is undefined`))},onError:o,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#d.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const a=e.error;return(0,n.wm)(a)&&a.revert&&this.#A?{...this.#A,fetchStatus:"idle"}:{...t,error:a,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),s.jG.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#j.notify({query:this,type:"updated",action:e})}))}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,n.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},795:e=>{e.exports=window.ReactDOM},828:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=a(609),s=a(967),n=a(470),i=a(14),o=a(326),l=a(44);const c=({postId:e})=>{const t=(0,s.jE)(),{data:a,isLoading:c,error:u}=(0,n.I)({queryKey:["reactions",e],queryFn:()=>(async e=>l.A.get(`/reactions/${e}`))(e),staleTime:12e4}),d=(0,i.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>l.A.post("/reactions",{post_id:e,reaction_type:t}))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),o.Ay.success("Reaction added!")},onError:e=>{const t=e.response?.data?.message||"Failed to add reaction";o.Ay.error(t)}}),h=(0,i.n)({mutationFn:({postId:e,reactionType:t})=>(async(e,t)=>l.A.delete(`/reactions/${e}/${t}`))(e,t),onSuccess:a=>{t.setQueryData(["reactions",e],a),o.Ay.success("Reaction removed!")},onError:e=>{const t=e.response?.data?.message||"Failed to remove reaction";o.Ay.error(t)}});if(c)return(0,r.createElement)("div",{className:"feedlane-reactions feedlane-reactions--loading"},(0,r.createElement)("div",{className:"feedlane-reactions__skeleton"},[1,2,3].map((e=>(0,r.createElement)("div",{key:e,className:"feedlane-reaction-button feedlane-reaction-button--skeleton"},(0,r.createElement)("div",{className:"feedlane-reaction-button__emoji"}),(0,r.createElement)("div",{className:"feedlane-reaction-button__count"}))))));if(u||!a)return null;const{reaction_counts:m={},user_reaction:f,available_reactions:p={}}=a;return(0,r.createElement)("div",{className:"feedlane-reactions"},(0,r.createElement)("div",{className:"feedlane-reactions__buttons"},Object.entries(p).map((([t,s])=>{const n=m[t]||0,i=f===t,o=d.isPending||h.isPending;return(0,r.createElement)("button",{key:t,onClick:()=>(t=>{a&&(a.user_reaction===t?h.mutate({postId:e,reactionType:t}):d.mutate({postId:e,reactionType:t}))})(t),disabled:o,className:"feedlane-reaction-button "+(i?"feedlane-reaction-button--active":""),title:`React with ${t}`},(0,r.createElement)("span",{className:"feedlane-reaction-button__emoji"},s),n>0&&(0,r.createElement)("span",{className:"feedlane-reaction-button__count"},n))}))),Object.values(m).some((e=>e>0))&&(0,r.createElement)("div",{className:"feedlane-reactions__summary"},Object.entries(m).filter((([e,t])=>t>0)).map((([e,t])=>(0,r.createElement)("span",{key:e,className:"feedlane-reactions__summary-item"},p[e]," ",t)))))}},860:(e,t,a)=>{a.d(t,{t:()=>n});var r=a(613),s=a(383),n=new class extends r.Q{#L=!0;#o;#l;constructor(){super(),this.#l=e=>{if(!s.S$&&window.addEventListener){const t=()=>e(!0),a=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",a)}}}}onSubscribe(){this.#o||this.setEventListener(this.#l)}onUnsubscribe(){this.hasListeners()||(this.#o?.(),this.#o=void 0)}setEventListener(e){this.#l=e,this.#o?.(),this.#o=e(this.setOnline.bind(this))}setOnline(e){this.#L!==e&&(this.#L=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#L}}},880:(e,t,a)=>{a.d(t,{B88:()=>m,CKj:()=>u,Pum:()=>h,X6_:()=>o,aze:()=>c,fK4:()=>n,kGk:()=>d,mEP:()=>l,wAb:()=>i,yGN:()=>f,zd:()=>s});var r=a(992);function s(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"}},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"}}]})(e)}function n(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 12 15 18 9"}}]})(e)}function i(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"18 15 12 9 6 15"}}]})(e)}function o(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"}}]})(e)}function l(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}}]})(e)}function c(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"5 3 19 12 5 21 5 3"}}]})(e)}function u(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}}]})(e)}function d(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(e)}function h(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"18",cy:"5",r:"3"}},{tag:"circle",attr:{cx:"6",cy:"12",r:"3"}},{tag:"circle",attr:{cx:"18",cy:"19",r:"3"}},{tag:"line",attr:{x1:"8.59",y1:"13.51",x2:"15.42",y2:"17.49"}},{tag:"line",attr:{x1:"15.41",y1:"6.51",x2:"8.59",y2:"10.49"}}]})(e)}function m(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(e)}function f(e){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}},967:(e,t,a)=>{a.d(t,{Ht:()=>o,jE:()=>i});var r=a(609),s=a(70),n=r.createContext(void 0),i=e=>{const t=r.useContext(n);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(r.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,s.jsx)(n.Provider,{value:e,children:t}))},992:(e,t,a)=>{a.d(t,{k5:()=>u});var r=a(609),s=a.n(r),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},i=s().createContext&&s().createContext(n),o=function(){return o=Object.assign||function(e){for(var t,a=1,r=arguments.length;a<r;a++)for(var s in t=arguments[a])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},o.apply(this,arguments)},l=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]])}return a};function c(e){return e&&e.map((function(e,t){return s().createElement(e.tag,o({key:t},e.attr),c(e.child))}))}function u(e){return function(t){return s().createElement(d,o({attr:o({},e.attr)},t),c(e.child))}}function d(e){var t=function(t){var a,r=e.attr,n=e.size,i=e.title,c=l(e,["attr","size","title"]),u=n||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),s().createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,r,c,{className:a,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),i&&s().createElement("title",null,i),e.children)};return void 0!==i?s().createElement(i.Consumer,null,(function(e){return t(e)})):t(n)}}},r={};function s(e){var t=r[e];if(void 0!==t)return t.exports;var n=r[e]={exports:{}};return a[e](n,n.exports,s),n.exports}s.m=a,s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},s.f={},s.e=e=>Promise.all(Object.keys(s.f).reduce(((t,a)=>(s.f[a](e,t),t)),[])),s.u=e=>e+".js",s.miniCssF=e=>{},s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="feedlane:",s.l=(a,r,n,i)=>{if(e[a])e[a].push(r);else{var o,l;if(void 0!==n)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+n){o=d;break}}o||(l=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,s.nc&&o.setAttribute("nonce",s.nc),o.setAttribute("data-webpack",t+n),o.src=a),e[a]=[r];var h=(t,r)=>{o.onerror=o.onload=null,clearTimeout(m);var s=e[a];if(delete e[a],o.parentNode&&o.parentNode.removeChild(o),s&&s.forEach((e=>e(r))),t)return t(r)},m=setTimeout(h.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=h.bind(null,o.onerror),o.onload=h.bind(null,o.onload),l&&document.head.appendChild(o)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;s.g.importScripts&&(e=s.g.location+"");var t=s.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var a=t.getElementsByTagName("script");if(a.length)for(var r=a.length-1;r>-1&&(!e||!/^http(s?):/.test(e));)e=a[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),s.p=e+"../"})(),(()=>{var e={786:0};s.f.j=(t,a)=>{var r=s.o(e,t)?e[t]:void 0;if(0!==r)if(r)a.push(r[2]);else{var n=new Promise(((a,s)=>r=e[t]=[a,s]));a.push(r[2]=n);var i=s.p+s.u(t),o=new Error;s.l(i,(a=>{if(s.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var n=a&&("load"===a.type?"missing":a.type),i=a&&a.target&&a.target.src;o.message="Loading chunk "+t+" failed.\n("+n+": "+i+")",o.name="ChunkLoadError",o.type=n,o.request=i,r[1](o)}}),"chunk-"+t,t)}};var t=(t,a)=>{var r,n,[i,o,l]=a,c=0;if(i.some((t=>0!==e[t]))){for(r in o)s.o(o,r)&&(s.m[r]=o[r]);l&&l(s)}for(t&&t(a);c<i.length;c++)n=i[c],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0},a=globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var n=s(609),i=s(576),o=s(383),l=s(702),c=s(474),u=s(613),d=class extends u.Q{constructor(e={}){super(),this.config=e,this.#U=new Map}#U;build(e,t,a){const r=t.queryKey,s=t.queryHash??(0,o.F$)(r,t);let n=this.get(s);return n||(n=new l.X({client:e,queryKey:r,queryHash:s,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(r)}),this.add(n)),n}add(e){this.#U.has(e.queryHash)||(this.#U.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#U.get(e.queryHash);t&&(e.destroy(),t===e&&this.#U.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){c.jG.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#U.get(e)}getAll(){return[...this.#U.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,o.MK)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,o.MK)(e,t))):t}notify(e){c.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){c.jG.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){c.jG.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},h=s(259),m=class extends u.Q{constructor(e={}){super(),this.config=e,this.#$=new Set,this.#G=new Map,this.#B=0}#$;#G;#B;build(e,t,a){const r=new h.s({mutationCache:this,mutationId:++this.#B,options:e.defaultMutationOptions(t),state:a});return this.add(r),r}add(e){this.#$.add(e);const t=f(e);if("string"==typeof t){const a=this.#G.get(t);a?a.push(e):this.#G.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#$.delete(e)){const t=f(e);if("string"==typeof t){const a=this.#G.get(t);if(a)if(a.length>1){const t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&this.#G.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=f(e);if("string"==typeof t){const a=this.#G.get(t),r=a?.find((e=>"pending"===e.state.status));return!r||r===e}return!0}runNext(e){const t=f(e);if("string"==typeof t){const a=this.#G.get(t)?.find((t=>t!==e&&t.state.isPaused));return a?.continue()??Promise.resolve()}return Promise.resolve()}clear(){c.jG.batch((()=>{this.#$.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#$.clear(),this.#G.clear()}))}getAll(){return Array.from(this.#$)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,o.nJ)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,o.nJ)(e,t)))}notify(e){c.jG.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.jG.batch((()=>Promise.all(e.map((e=>e.continue().catch(o.lQ))))))}};function f(e){return e.options.scope?.id}var p=s(79),y=s(860);function b(e){return{onFetch:(t,a)=>{const r=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],i=t.state.data?.pageParams||[];let l={pages:[],pageParams:[]},c=0;const u=async()=>{let a=!1;const u=(0,o.ZM)(t.options,t.fetchOptions),d=async(e,r,s)=>{if(a)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);const n={client:t.client,queryKey:t.queryKey,pageParam:r,direction:s?"backward":"forward",meta:t.options.meta};var i;i=n,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",(()=>{a=!0})),t.signal)});const l=await u(n),{maxPages:c}=t.options,d=s?o.ZZ:o.y9;return{pages:d(e.pages,l,c),pageParams:d(e.pageParams,r,c)}};if(s&&n.length){const e="backward"===s,t={pages:n,pageParams:i},a=(e?g:v)(r,t);l=await d(t,a,e)}else{const t=e??n.length;do{const e=0===c?i[0]??r.initialPageParam:v(r,l);if(c>0&&null==e)break;l=await d(l,e),c++}while(c<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a):t.fetchFn=u}}}function v(e,{pages:t,pageParams:a}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,a[r],a):void 0}function g(e,{pages:t,pageParams:a}){return t.length>0?e.getPreviousPageParam?.(t[0],t,a[0],a):void 0}var E=s(967),_=s(326),w=s(880),k=s(992);function N(e){return(0,k.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"}}]})(e)}function C(e){return(0,k.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"}}]})(e)}function O(e){return(0,k.k5)({tag:"svg",attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"}}]})(e)}var S=s(470);s(44);const x=async e=>{try{const t=await fetch(`${window.feedlaneData.rest_url}wp/v2/media/${e}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(t.ok)return(await t.json()).source_url}catch(e){}return null};var R=s(828),F=s(258);const P=({post:e})=>{const t={integrations:{label:"INTEGRATIONS",color:"#8B5CF6",bg:"#F3F4F6"},features:{label:"FEATURES",color:"#10B981",bg:"#F0FDF4"},improvements:{label:"IMPROVEMENTS",color:"#F59E0B",bg:"#FFFBEB"},announcements:{label:"ANNOUNCEMENTS",color:"#EF4444",bg:"#FEF2F2"}}[e.category]||{label:"NEW",color:"#6366F1",bg:"#EEF2FF"};var a;return(0,n.createElement)("article",{className:"feedlane-beamer-post"},(0,n.createElement)("div",{className:"feedlane-beamer-post__header"},(0,n.createElement)("div",{className:"feedlane-beamer-post__badges"},(0,n.createElement)("span",{className:"feedlane-beamer-post__badge",style:{color:t.color,backgroundColor:t.bg}},t.label),(0,n.createElement)("span",{className:"feedlane-beamer-post__date"},(a=e.date,new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})))),(0,n.createElement)("button",{className:"feedlane-beamer-post__share"},(0,n.createElement)(w.Pum,{size:16}))),(0,n.createElement)("h3",{className:"feedlane-beamer-post__title"},"integrations"===e.category&&"🚀 ",e.title),(0,n.createElement)("div",{className:"feedlane-beamer-post__content"},e.availability&&(0,n.createElement)("p",{className:"feedlane-beamer-post__availability"},(0,n.createElement)("em",null,"Available for ",e.availability," customers")),(0,n.createElement)("div",{className:"feedlane-beamer-post__text",dangerouslySetInnerHTML:{__html:e.content}}),e.video_url&&(0,n.createElement)("div",{className:"feedlane-beamer-post__video"},(0,n.createElement)("div",{className:"feedlane-beamer-post__video-thumbnail"},e.featured_image&&(0,n.createElement)("img",{src:e.featured_image,alt:e.title,loading:"lazy"}),(0,n.createElement)("button",{className:"feedlane-beamer-post__play-btn"},(0,n.createElement)(w.aze,{size:24})),(0,n.createElement)("div",{className:"feedlane-beamer-post__video-info"},(0,n.createElement)("span",{className:"feedlane-beamer-post__video-title"},e.video_title||e.title),(0,n.createElement)("div",{className:"feedlane-beamer-post__video-meta"},(0,n.createElement)("span",null,"0"),(0,n.createElement)("span",null,"📎"),(0,n.createElement)("span",null,"✏️"))))),e.featured_image&&!e.video_url&&(0,n.createElement)("div",{className:"feedlane-beamer-post__image"},(0,n.createElement)("img",{src:e.featured_image,alt:e.title,loading:"lazy"}))),(0,n.createElement)("div",{className:"feedlane-beamer-post__interactions"},(0,n.createElement)(R.default,{postId:e.id}),(0,n.createElement)(F.default,{postId:e.id})))},q=()=>{const{data:e,isLoading:t,error:a,refetch:r}=(0,S.I)({queryKey:["newsfeed-posts"],queryFn:()=>(async(e={})=>{const t={page:1,per_page:10,...e},a=await fetch(`${window.feedlaneData.rest_url}wp/v2/feedlane-posts?${new URLSearchParams(t)}`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(!a.ok)throw new Error("Failed to fetch newsfeed posts");return(await a.json()).map((e=>({id:e.id,title:e.title.rendered,content:e.content.rendered,excerpt:e.excerpt.rendered,date:e.date,featured_image:e.featured_media?x(e.featured_media):null,author:e.author,link:e.link})))})({per_page:10}),staleTime:3e5});return t?(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading newsfeed...")):a?(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load newsfeed posts."),(0,n.createElement)("button",{onClick:()=>r(),className:"feedlane-button feedlane-button--secondary"},"Try Again")):e&&0!==e.length?(0,n.createElement)("div",{className:"feedlane-newsfeed"},(0,n.createElement)("div",{className:"feedlane-subscribe-banner"},(0,n.createElement)("div",{className:"feedlane-subscribe-banner__icon"},(0,n.createElement)(w.zd,{size:20})),(0,n.createElement)("span",{className:"feedlane-subscribe-banner__text"},"Subscribe to get our latest updates")),(0,n.createElement)("div",{className:"feedlane-newsfeed__posts"},e.map((e=>(0,n.createElement)(P,{key:e.id,post:e}))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"📢"),(0,n.createElement)("h3",null,"No News Yet"),(0,n.createElement)("p",null,"Check back later for updates and announcements."))};var I=s(14),T=s(559),Q=s(332);const A=({idea:e})=>{const[t,a]=(0,n.useState)(!1),r=(0,E.jE)(),s=(0,I.n)({mutationFn:e=>(0,T.Q2)(e),onSuccess:()=>{a(!0),r.invalidateQueries(["ideas"])}}),i={"feature-request":{label:"FEATURE REQUEST",color:"#10B981"},improvement:{label:"IMPROVEMENT",color:"#F59E0B"},"bug-fix":{label:"BUG FIX",color:"#EF4444"},integration:{label:"INTEGRATION",color:"#8B5CF6"}}[e.category]||{label:"FEATURE REQUEST",color:"#10B981"};var o;return(0,n.createElement)("article",{className:"feedlane-beamer-idea"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__header"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__vote"},(0,n.createElement)("button",{className:"feedlane-beamer-idea__vote-btn "+(t?"voted":""),onClick:()=>{t||s.mutate(e.id)},disabled:t},(0,n.createElement)(w.wAb,{size:16})),(0,n.createElement)("span",{className:"feedlane-beamer-idea__vote-count"},e.vote_count||0)),(0,n.createElement)("div",{className:"feedlane-beamer-idea__content"},(0,n.createElement)("div",{className:"feedlane-beamer-idea__meta"},(0,n.createElement)("span",{className:"feedlane-beamer-idea__category",style:{color:i.color}},i.label),(0,n.createElement)("span",{className:"feedlane-beamer-idea__comments"},e.comment_count||0," ",(0,n.createElement)(w.mEP,{size:14}))),(0,n.createElement)("h3",{className:"feedlane-beamer-idea__title"},e.title),e.status&&(0,n.createElement)("div",{className:"feedlane-beamer-idea__status"},(0,n.createElement)("span",{className:"feedlane-beamer-idea__status-indicator",style:{backgroundColor:(o=e.status,{completed:"#10B981","in-progress":"#F59E0B",planned:"#EF4444","under-review":"#6B7280"}[o]||"#6B7280")}}),(0,n.createElement)("span",{className:"feedlane-beamer-idea__status-text"},e.status.toUpperCase().replace("-"," "))),e.details&&(0,n.createElement)("div",{className:"feedlane-beamer-idea__details"},(0,n.createElement)("ul",null,e.details.split("\n").filter((e=>e.trim())).map(((e,t)=>(0,n.createElement)("li",{key:t},e.trim()))))))))},j=()=>{const[e,t]=(0,n.useState)(!1),a=(0,E.jE)(),{data:r,isLoading:s,error:i,refetch:o}=(0,S.I)({queryKey:["ideas"],queryFn:()=>(0,T.Zu)({per_page:20}),staleTime:18e4});if(s)return(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading ideas..."));if(i)return(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load ideas."),(0,n.createElement)("button",{onClick:()=>o(),className:"feedlane-button feedlane-button--secondary"},"Try Again"));const l=r?.ideas||[];return window.feedlaneData,(0,n.createElement)("div",{className:"feedlane-ideas"},e?(0,n.createElement)(Q.default,{onSuccess:()=>{t(!1),a.invalidateQueries(["ideas"])},onCancel:()=>{t(!1)}}):(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-ideas__list"},0===l.length?(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,n.createElement)("h3",null,"No Ideas Yet"),(0,n.createElement)("p",null,"Be the first to share your brilliant idea!")):l.map((e=>(0,n.createElement)(A,{key:e.id,idea:e})))),(0,n.createElement)("div",{className:"feedlane-suggest-button"},(0,n.createElement)("button",{className:"feedlane-suggest-btn",onClick:()=>t(!0)},"💡 SUGGEST A NEW IDEA"))))},D=({status:e})=>{const{data:t,isLoading:a,error:r}=(0,S.I)({queryKey:["roadmap-ideas",e.slug],queryFn:()=>(0,T.Zu)({status:e.slug,per_page:50,orderby:"votes"}),staleTime:3e5});if(a)return(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading roadmap..."));if(r)return(0,n.createElement)("div",{className:"feedlane-error"},(0,n.createElement)("p",null,"Failed to load roadmap items."));const s=t?.ideas||[];return 0===s.length?(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"🚧"),(0,n.createElement)("h3",null,"No Items"),(0,n.createElement)("p",null,"No items in ",e.name.toLowerCase()," yet.")):(0,n.createElement)("div",{className:"feedlane-roadmap-items"},s.map((e=>(0,n.createElement)(M,{key:e.id,idea:e}))))},M=({idea:e})=>{const t={"feature-request":{label:"FEATURE REQUEST",color:"#10B981"},improvement:{label:"IMPROVEMENT",color:"#F59E0B"},"bug-fix":{label:"BUG FIX",color:"#EF4444"},integration:{label:"INTEGRATION",color:"#8B5CF6"}}[e.category]||{label:"FEATURE REQUEST",color:"#10B981"};return(0,n.createElement)("article",{className:"feedlane-beamer-roadmap-item"},(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__header"},(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__vote"},(0,n.createElement)("button",{className:"feedlane-beamer-roadmap-item__vote-btn"},(0,n.createElement)(w.wAb,{size:16})),(0,n.createElement)("span",{className:"feedlane-beamer-roadmap-item__vote-count"},e.vote_count||0)),(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__content"},(0,n.createElement)("h3",{className:"feedlane-beamer-roadmap-item__title"},e.title),(0,n.createElement)("span",{className:"feedlane-beamer-roadmap-item__category",style:{color:t.color}},t.label),e.details&&(0,n.createElement)("div",{className:"feedlane-beamer-roadmap-item__details"},(0,n.createElement)("ul",null,e.details.split("\n").filter((e=>e.trim())).map(((e,t)=>(0,n.createElement)("li",{key:t},e.trim()))))),e.comment_count>0&&(0,n.createElement)("button",{className:"feedlane-beamer-roadmap-item__comments"},(0,n.createElement)(w.mEP,{size:14}),"Add a comment"))))},L=()=>{const[e,t]=(0,n.useState)("under-review"),a=[{slug:"under-review",name:"Under review",color:"#6B7280",icon:"●"},{slug:"planned",name:"Planned",color:"#EF4444",icon:"●"},{slug:"in-progress",name:"In Progress",color:"#F59E0B",icon:"●"},{slug:"completed",name:"Completed",color:"#10B981",icon:"●"}];return(0,n.createElement)("div",{className:"feedlane-roadmap"},(0,n.createElement)("div",{className:"feedlane-roadmap__tabs"},a.map((a=>(0,n.createElement)("button",{key:a.slug,onClick:()=>t(a.slug),className:"feedlane-roadmap__tab "+(e===a.slug?"active":""),style:e===a.slug?{borderBottomColor:a.color,color:a.color}:{}},(0,n.createElement)("span",{className:"feedlane-roadmap__tab-indicator",style:{color:a.color}},a.icon),a.name)))),(0,n.createElement)("div",{className:"feedlane-roadmap__content"},(0,n.createElement)(D,{status:a.find((t=>t.slug===e))})))},U=()=>{const[e,t]=(0,n.useState)(!1),[a,r]=(0,n.useState)("newsfeed"),[s,i]=(0,n.useState)({});(0,n.useEffect)((()=>{if(window.feedlaneData&&window.feedlaneData.settings){i(window.feedlaneData.settings);const{enable_newsfeed:e,enable_ideas:t,enable_roadmap:a}=window.feedlaneData.settings;e?r("newsfeed"):t?r("ideas"):a&&r("roadmap")}}),[]);const o=()=>{t(!1)},l=(()=>{const e=[];return s.enable_newsfeed&&e.push({id:"newsfeed",label:"Newsfeed",icon:O,component:q}),s.enable_ideas&&e.push({id:"ideas",label:"Ideas",icon:N,component:j}),s.enable_roadmap&&e.push({id:"roadmap",label:"Roadmap",icon:C,component:L}),e})();if(0===l.length)return null;const c=s.sidebar_position||"right",u=s.primary_color||"#4F46E5",d=l.find((e=>e.id===a))?.component||q;return(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:`feedlane-floating-button feedlane-floating-button--${c}`,onClick:()=>{t(!e)},style:{backgroundColor:"#000"}},(0,n.createElement)("span",{className:"feedlane-floating-text"},"Feedback")),e&&(0,n.createElement)("div",{className:"feedlane-overlay",onClick:o}),(0,n.createElement)("div",{className:`feedlane-sidebar feedlane-sidebar--${c} ${e?"feedlane-sidebar--open":""}`,style:{"--feedlane-primary-color":u}},(0,n.createElement)("div",{className:"feedlane-sidebar__header",style:{backgroundColor:u}},(0,n.createElement)("div",{className:"feedlane-sidebar__header-content"},(0,n.createElement)("h2",{className:"feedlane-sidebar__title"},(()=>{switch(a){case"newsfeed":return"What's new on "+(window.feedlaneData?.siteName||"Our Site");case"ideas":return(window.feedlaneData?.siteName||"Our Site")+" Ideas";case"roadmap":return(window.feedlaneData?.siteName||"Our Site")+" Roadmap";default:return"Feedback"}})()),(0,n.createElement)("div",{className:"feedlane-sidebar__header-actions"},(0,n.createElement)("button",{className:"feedlane-sidebar__search-btn"},(0,n.createElement)(w.CKj,{size:20})),(0,n.createElement)("button",{onClick:o,className:"feedlane-sidebar__close","aria-label":"Close sidebar"},(0,n.createElement)(w.yGN,{size:20}))))),(0,n.createElement)("div",{className:"feedlane-sidebar__content"},(0,n.createElement)(d,null)),l.length>1&&(0,n.createElement)("div",{className:"feedlane-sidebar__bottom-nav"},l.map((e=>{const t=e.icon;return(0,n.createElement)("button",{key:e.id,onClick:()=>r(e.id),className:"feedlane-sidebar__nav-item "+(a===e.id?"feedlane-sidebar__nav-item--active":""),style:a===e.id?{color:u}:{}},(0,n.createElement)(t,{size:24}),(0,n.createElement)("span",{className:"feedlane-sidebar__nav-label"},e.label))})))))},$=new class{#z;#u;#D;#H;#K;#W;#Z;#V;constructor(e={}){this.#z=e.queryCache||new d,this.#u=e.mutationCache||new m,this.#D=e.defaultOptions||{},this.#H=new Map,this.#K=new Map,this.#W=0}mount(){this.#W++,1===this.#W&&(this.#Z=p.m.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#z.onFocus())})),this.#V=y.t.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#z.onOnline())})))}unmount(){this.#W--,0===this.#W&&(this.#Z?.(),this.#Z=void 0,this.#V?.(),this.#V=void 0)}isFetching(e){return this.#z.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#u.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#z.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),a=this.#z.build(this,t),r=a.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime((0,o.d2)(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#z.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,a){const r=this.defaultQueryOptions({queryKey:e}),s=this.#z.get(r.queryHash),n=s?.state.data,i=(0,o.Zw)(t,n);if(void 0!==i)return this.#z.build(this,r).setData(i,{...a,manual:!0})}setQueriesData(e,t,a){return c.jG.batch((()=>this.#z.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,a)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#z.get(t.queryHash)?.state}removeQueries(e){const t=this.#z;c.jG.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const a=this.#z;return c.jG.batch((()=>(a.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const a={revert:!0,...t},r=c.jG.batch((()=>this.#z.findAll(e).map((e=>e.cancel(a)))));return Promise.all(r).then(o.lQ).catch(o.lQ)}invalidateQueries(e,t={}){return c.jG.batch((()=>(this.#z.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const a={...t,cancelRefetch:t.cancelRefetch??!0},r=c.jG.batch((()=>this.#z.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,a);return a.throwOnError||(t=t.catch(o.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(r).then(o.lQ)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const a=this.#z.build(this,t);return a.isStaleByTime((0,o.d2)(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(o.lQ).catch(o.lQ)}fetchInfiniteQuery(e){return e.behavior=b(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(o.lQ).catch(o.lQ)}ensureInfiniteQueryData(e){return e.behavior=b(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return y.t.isOnline()?this.#u.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#z}getMutationCache(){return this.#u}getDefaultOptions(){return this.#D}setDefaultOptions(e){this.#D=e}setQueryDefaults(e,t){this.#H.set((0,o.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#H.values()],a={};return t.forEach((t=>{(0,o.Cp)(e,t.queryKey)&&Object.assign(a,t.defaultOptions)})),a}setMutationDefaults(e,t){this.#K.set((0,o.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#K.values()],a={};return t.forEach((t=>{(0,o.Cp)(e,t.mutationKey)&&Object.assign(a,t.defaultOptions)})),a}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#D.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,o.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===o.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#D.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#z.clear(),this.#u.clear()}}({defaultOptions:{queries:{retry:2,staleTime:3e5}}}),G=()=>(0,n.createElement)(E.Ht,{client:$},(0,n.createElement)(U,null),(0,n.createElement)(_.l$,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelectorAll(".feedlane-sidebar-container");if(e.length>0)e.forEach((e=>{(0,i.H)(e).render((0,n.createElement)(G,null))}));else{const e=document.createElement("div");e.id="feedlane-floating-sidebar",e.className="feedlane-floating-sidebar",document.body.appendChild(e),(0,i.H)(e).render((0,n.createElement)(G,null))}document.querySelectorAll(".feedlane-reactions").forEach((e=>{const t=e.dataset.postId;t&&Promise.resolve().then(s.bind(s,828)).then((({default:a})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:$},(0,n.createElement)(a,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-feedback-form").forEach((e=>{const t=e.dataset.postId;t&&Promise.resolve().then(s.bind(s,258)).then((({default:a})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:$},(0,n.createElement)(a,{postId:parseInt(t)})))}))})),document.querySelectorAll(".feedlane-idea-form").forEach((e=>{Promise.resolve().then(s.bind(s,332)).then((({default:t})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:$},(0,n.createElement)(t,null)))}))})),document.querySelectorAll(".feedlane-idea-actions, .feedlane-item-actions").forEach((e=>{const t=e.dataset.ideaId;t&&s.e(510).then(s.bind(s,510)).then((({default:a})=>{(0,i.H)(e).render((0,n.createElement)(E.Ht,{client:$},(0,n.createElement)(a,{ideaId:parseInt(t)})))}))}))}))})();