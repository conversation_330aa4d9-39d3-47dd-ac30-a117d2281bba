(()=>{"use strict";var e={70:(e,t,s)=>{e.exports=s(462)},462:(e,t,s)=>{var a=s(609),r=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};t.jsx=function(e,t,s){var a,l={},c=null,u=null;for(a in void 0!==s&&(c=""+s),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)n.call(t,a)&&!o.hasOwnProperty(a)&&(l[a]=t[a]);if(e&&e.defaultProps)for(a in t=e.defaultProps)void 0===l[a]&&(l[a]=t[a]);return{$$typeof:r,type:e,key:c,ref:u,props:l,_owner:i.current}}},576:(e,t,s)=>{var a=s(795);t.H=a.createRoot,a.hydrateRoot},609:e=>{e.exports=window.React},795:e=>{e.exports=window.ReactDOM}},t={};function s(a){var r=t[a];if(void 0!==r)return r.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,s),n.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var a in t)s.o(t,a)&&!s.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var a=s(609),r=s.n(a),n=s(576),i="undefined"==typeof window||"Deno"in globalThis;function o(){}function l(e){return"number"==typeof e&&e>=0&&e!==1/0}function c(e,t){return Math.max(e+(t||0)-Date.now(),0)}function u(e,t){return"function"==typeof e?e(t):e}function d(e,t){return"function"==typeof e?e(t):e}function h(e,t){const{type:s="all",exact:a,fetchStatus:r,predicate:n,queryKey:i,stale:o}=e;if(i)if(a){if(t.queryHash!==p(i,t.options))return!1}else if(!y(t.queryKey,i))return!1;if("all"!==s){const e=t.isActive();if("active"===s&&!e)return!1;if("inactive"===s&&e)return!1}return!("boolean"==typeof o&&t.isStale()!==o||r&&r!==t.state.fetchStatus||n&&!n(t))}function m(e,t){const{exact:s,status:a,predicate:r,mutationKey:n}=e;if(n){if(!t.options.mutationKey)return!1;if(s){if(f(t.options.mutationKey)!==f(n))return!1}else if(!y(t.options.mutationKey,n))return!1}return!(a&&t.state.status!==a||r&&!r(t))}function p(e,t){return(t?.queryKeyHashFn||f)(e)}function f(e){return JSON.stringify(e,((e,t)=>E(t)?Object.keys(t).sort().reduce(((e,s)=>(e[s]=t[s],e)),{}):t))}function y(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&Object.keys(t).every((s=>y(e[s],t[s])))}function b(e,t){if(e===t)return e;const s=v(e)&&v(t);if(s||E(e)&&E(t)){const a=s?e:Object.keys(e),r=a.length,n=s?t:Object.keys(t),i=n.length,o=s?[]:{};let l=0;for(let r=0;r<i;r++){const i=s?r:n[r];(!s&&a.includes(i)||s)&&void 0===e[i]&&void 0===t[i]?(o[i]=void 0,l++):(o[i]=b(e[i],t[i]),o[i]===e[i]&&void 0!==e[i]&&l++)}return r===i&&l===r?e:o}return t}function g(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function v(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function E(e){if(!w(e))return!1;const t=e.constructor;if(void 0===t)return!0;const s=t.prototype;return!!w(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function w(e){return"[object Object]"===Object.prototype.toString.call(e)}function x(e,t,s){return"function"==typeof s.structuralSharing?s.structuralSharing(e,t):!1!==s.structuralSharing?b(e,t):t}function C(e,t,s=0){const a=[...e,t];return s&&a.length>s?a.slice(1):a}function N(e,t,s=0){const a=[t,...e];return s&&a.length>s?a.slice(0,-1):a}var O=Symbol();function R(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==O?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}function S(e,t){return"function"==typeof e?e(...t):!!e}var _=e=>setTimeout(e,0),k=function(){let e=[],t=0,s=e=>{e()},a=e=>{e()},r=_;const n=a=>{t?e.push(a):r((()=>{s(a)}))};return{batch:n=>{let i;t++;try{i=n()}finally{t--,t||(()=>{const t=e;e=[],t.length&&r((()=>{a((()=>{t.forEach((e=>{s(e)}))}))}))})()}return i},batchCalls:e=>(...t)=>{n((()=>{e(...t)}))},schedule:n,setNotifyFunction:e=>{s=e},setBatchNotifyFunction:e=>{a=e},setScheduler:e=>{r=e}}}(),P=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},F=new class extends P{#e;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},q=new class extends P{#a=!0;#t;#s;constructor(){super(),this.#s=e=>{if(!i&&window.addEventListener){const t=()=>e(!0),s=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#t||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#s=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#a!==e&&(this.#a=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#a}};function D(){let e,t;const s=new Promise(((s,a)=>{e=s,t=a}));function a(e){Object.assign(s,e),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=t=>{a({status:"fulfilled",value:t}),e(t)},s.reject=e=>{a({status:"rejected",reason:e}),t(e)},s}function Q(e){return Math.min(1e3*2**e,3e4)}function I(e){return"online"!==(e??"online")||q.isOnline()}var T=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function M(e){return e instanceof T}function A(e){let t,s=!1,a=0,r=!1;const n=D(),o=()=>F.isFocused()&&("always"===e.networkMode||q.isOnline())&&e.canRun(),l=()=>I(e.networkMode)&&e.canRun(),c=s=>{r||(r=!0,e.onSuccess?.(s),t?.(),n.resolve(s))},u=s=>{r||(r=!0,e.onError?.(s),t?.(),n.reject(s))},d=()=>new Promise((s=>{t=e=>{(r||o())&&s(e)},e.onPause?.()})).then((()=>{t=void 0,r||e.onContinue?.()})),h=()=>{if(r)return;let t;const n=0===a?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(c).catch((t=>{if(r)return;const n=e.retry??(i?0:3),l=e.retryDelay??Q,c="function"==typeof l?l(a,t):l,m=!0===n||"number"==typeof n&&a<n||"function"==typeof n&&n(a,t);var p;!s&&m?(a++,e.onFail?.(a,t),(p=c,new Promise((e=>{setTimeout(e,p)}))).then((()=>o()?void 0:d())).then((()=>{s?u(t):h()}))):u(t)}))};return{promise:n,cancel:t=>{r||(u(new T(t)),e.abort?.())},continue:()=>(t?.(),n),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:l,start:()=>(l()?h():d().then(h),n)}}var j=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),l(this.gcTime)&&(this.#r=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},U=class extends j{#n;#i;#o;#l;#c;#u;#d;constructor(e){super(),this.#d=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#o=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#n=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,a=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,t){const s=x(this.state.data,e,this.options);return this.#h({data:s,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),s}setState(e,t){this.#h({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#c?.promise;return this.#c?.cancel(e),t?t.then(o).catch(o):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some((e=>!1!==d(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===O||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!c(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#c&&(this.#d?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#h({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const s=new AbortController,a=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,s.signal)})},r={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{const e=R(this.options,t),s={client:this.#l,queryKey:this.queryKey,meta:this.meta};return a(s),this.#d=!1,this.options.persister?this.options.persister(e,s,this):e(s)}};a(r),this.options.behavior?.onFetch(r,this),this.#i=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===r.fetchOptions?.meta||this.#h({type:"fetch",meta:r.fetchOptions?.meta});const n=e=>{M(e)&&e.silent||this.#h({type:"error",error:e}),M(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#c=A({initialPromise:t?.initialPromise,fn:r.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void n(e)}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else n(new Error(`${this.queryHash} data is undefined`))},onError:n,onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:()=>{this.#h({type:"continue"})},retry:r.options.retry,retryDelay:r.options.retryDelay,networkMode:r.options.networkMode,canRun:()=>!0}),this.#c.start()}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...L(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=e.error;return M(s)&&s.revert&&this.#i?{...this.#i,fetchStatus:"idle"}:{...t,error:s,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),k.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#o.notify({query:this,type:"updated",action:e})}))}};function L(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:I(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}var K=class extends P{constructor(e={}){super(),this.config=e,this.#m=new Map}#m;build(e,t,s){const a=t.queryKey,r=t.queryHash??p(a,t);let n=this.get(r);return n||(n=new U({client:e,queryKey:a,queryHash:r,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(a)}),this.add(n)),n}add(e){this.#m.has(e.queryHash)||(this.#m.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#m.get(e.queryHash);t&&(e.destroy(),t===e&&this.#m.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){k.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#m.get(e)}getAll(){return[...this.#m.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>h(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>h(e,t))):t}notify(e){k.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){k.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){k.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}},H=class extends j{#p;#f;#c;constructor(e){super(),this.mutationId=e.mutationId,this.#f=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#f.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter((t=>t!==e)),this.scheduleGc(),this.#f.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#f.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(e){const t=()=>{this.#h({type:"continue"})};this.#c=A({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#h({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#h({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#f.canRun(this)});const s="pending"===this.state.status,a=!this.#c.canStart();try{if(s)t();else{this.#h({type:"pending",variables:e,isPaused:a}),await(this.#f.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#h({type:"pending",context:t,variables:e,isPaused:a})}const r=await this.#c.start();return await(this.#f.config.onSuccess?.(r,e,this.state.context,this)),await(this.options.onSuccess?.(r,e,this.state.context)),await(this.#f.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,e,this.state.context)),this.#h({type:"success",data:r}),r}catch(t){try{throw await(this.#f.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#f.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#h({type:"error",error:t})}}finally{this.#f.runNext(this)}}#h(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),k.batch((()=>{this.#p.forEach((t=>{t.onMutationUpdate(e)})),this.#f.notify({mutation:this,type:"updated",action:e})}))}},$=class extends P{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#b=new Map,this.#g=0}#y;#b;#g;build(e,t,s){const a=new H({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(t),state:s});return this.add(a),a}add(e){this.#y.add(e);const t=B(e);if("string"==typeof t){const s=this.#b.get(t);s?s.push(e):this.#b.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){const t=B(e);if("string"==typeof t){const s=this.#b.get(t);if(s)if(s.length>1){const t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#b.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){const t=B(e);if("string"==typeof t){const s=this.#b.get(t),a=s?.find((e=>"pending"===e.state.status));return!a||a===e}return!0}runNext(e){const t=B(e);if("string"==typeof t){const s=this.#b.get(t)?.find((t=>t!==e&&t.state.isPaused));return s?.continue()??Promise.resolve()}return Promise.resolve()}clear(){k.batch((()=>{this.#y.forEach((e=>{this.notify({type:"removed",mutation:e})})),this.#y.clear(),this.#b.clear()}))}getAll(){return Array.from(this.#y)}find(e){const t={exact:!0,...e};return this.getAll().find((e=>m(t,e)))}findAll(e={}){return this.getAll().filter((t=>m(e,t)))}notify(e){k.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return k.batch((()=>Promise.all(e.map((e=>e.continue().catch(o))))))}};function B(e){return e.options.scope?.id}function z(e){return{onFetch:(t,s)=>{const a=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],i=t.state.data?.pageParams||[];let o={pages:[],pageParams:[]},l=0;const c=async()=>{let s=!1;const c=R(t.options,t.fetchOptions),u=async(e,a,r)=>{if(s)return Promise.reject();if(null==a&&e.pages.length)return Promise.resolve(e);const n={client:t.client,queryKey:t.queryKey,pageParam:a,direction:r?"backward":"forward",meta:t.options.meta};var i;i=n,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",(()=>{s=!0})),t.signal)});const o=await c(n),{maxPages:l}=t.options,u=r?N:C;return{pages:u(e.pages,o,l),pageParams:u(e.pageParams,a,l)}};if(r&&n.length){const e="backward"===r,t={pages:n,pageParams:i},s=(e?G:W)(a,t);o=await u(t,s,e)}else{const t=e??n.length;do{const e=0===l?i[0]??a.initialPageParam:W(a,o);if(l>0&&null==e)break;o=await u(o,e),l++}while(l<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=c}}}function W(e,{pages:t,pageParams:s}){const a=t.length-1;return t.length>0?e.getNextPageParam(t[a],t,s[a],s):void 0}function G(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}var V=s(70),J=a.createContext(void 0),X=e=>{const t=a.useContext(J);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Y=({client:e,children:t})=>(a.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,V.jsx)(J.Provider,{value:e,children:t}));let Z={data:""},ee=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Z,te=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,se=/\/\*[^]*?\*\/|  +/g,ae=/\n+/g,re=(e,t)=>{let s="",a="",r="";for(let n in e){let i=e[n];"@"==n[0]?"i"==n[1]?s=n+" "+i+";":a+="f"==n[1]?re(i,n):n+"{"+re(i,"k"==n[1]?"":t)+"}":"object"==typeof i?a+=re(i,t?t.replace(/([^,])+/g,(e=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):n):null!=i&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=re.p?re.p(n,i):n+":"+i+";")}return s+(t&&r?t+"{"+r+"}":r)+a},ne={},ie=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+ie(e[s]);return t}return e},oe=(e,t,s,a,r)=>{let n=ie(e),i=ne[n]||(ne[n]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(n));if(!ne[i]){let t=n!==e?e:(e=>{let t,s,a=[{}];for(;t=te.exec(e.replace(se,""));)t[4]?a.shift():t[3]?(s=t[3].replace(ae," ").trim(),a.unshift(a[0][s]=a[0][s]||{})):a[0][t[1]]=t[2].replace(ae," ").trim();return a[0]})(e);ne[i]=re(r?{["@keyframes "+i]:t}:t,s?"":"."+i)}let o=s&&ne.g?ne.g:null;return s&&(ne.g=ne[i]),((e,t,s,a)=>{a?t.data=t.data.replace(a,e):-1===t.data.indexOf(e)&&(t.data=s?e+t.data:t.data+e)})(ne[i],t,a,o),i};function le(e){let t=this||{},s=e.call?e(t.p):e;return oe(s.unshift?s.raw?((e,t,s)=>e.reduce(((e,a,r)=>{let n=t[r];if(n&&n.call){let e=n(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?"."+t:e&&"object"==typeof e?e.props?"":re(e,""):!1===e?"":e}return e+a+(null==n?"":n)}),""))(s,[].slice.call(arguments,1),t.p):s.reduce(((e,s)=>Object.assign(e,s&&s.call?s(t.p):s)),{}):s,ee(t.target),t.g,t.o,t.k)}le.bind({g:1});let ce,ue,de,he=le.bind({k:1});function me(e,t){let s=this||{};return function(){let a=arguments;function r(n,i){let o=Object.assign({},n),l=o.className||r.className;s.p=Object.assign({theme:ue&&ue()},o),s.o=/ *go\d+/.test(l),o.className=le.apply(s,a)+(l?" "+l:""),t&&(o.ref=i);let c=e;return e[0]&&(c=o.as||e,delete o.as),de&&c[0]&&de(o),ce(c,o)}return t?t(r):r}}var pe=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,fe=(()=>{let e=0;return()=>(++e).toString()})(),ye=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),be=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:s}=t;return be(e,{type:e.toasts.find((e=>e.id===s.id))?1:0,toast:s});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map((e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+r})))}}},ge=[],ve={toasts:[],pausedAt:void 0},Ee=e=>{ve=be(ve,e),ge.forEach((e=>{e(ve)}))},we={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},xe=e=>(t,s)=>{let a=((e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||fe()}))(t,e,s);return Ee({type:2,toast:a}),a.id},Ce=(e,t)=>xe("blank")(e,t);Ce.error=xe("error"),Ce.success=xe("success"),Ce.loading=xe("loading"),Ce.custom=xe("custom"),Ce.dismiss=e=>{Ee({type:3,toastId:e})},Ce.remove=e=>Ee({type:4,toastId:e}),Ce.promise=(e,t,s)=>{let a=Ce.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let r=t.success?pe(t.success,e):void 0;return r?Ce.success(r,{id:a,...s,...null==s?void 0:s.success}):Ce.dismiss(a),e})).catch((e=>{let r=t.error?pe(t.error,e):void 0;r?Ce.error(r,{id:a,...s,...null==s?void 0:s.error}):Ce.dismiss(a)})),e};var Ne=(e,t)=>{Ee({type:1,toast:{id:e,height:t}})},Oe=()=>{Ee({type:5,time:Date.now()})},Re=new Map,Se=he`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,_e=he`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ke=he`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Pe=me("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Se} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${_e} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${ke} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Fe=he`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,qe=me("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Fe} 1s linear infinite;
`,De=he`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Qe=he`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Ie=me("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${De} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Qe} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Te=me("div")`
  position: absolute;
`,Me=me("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Ae=he`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,je=me("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Ae} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Ue=({toast:e})=>{let{icon:t,type:s,iconTheme:r}=e;return void 0!==t?"string"==typeof t?a.createElement(je,null,t):t:"blank"===s?null:a.createElement(Me,null,a.createElement(qe,{...r}),"loading"!==s&&a.createElement(Te,null,"error"===s?a.createElement(Pe,{...r}):a.createElement(Ie,{...r})))},Le=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Ke=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,He=me("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,$e=me("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Be=a.memo((({toast:e,position:t,style:s,children:r})=>{let n=e.height?((e,t)=>{let s=e.includes("top")?1:-1,[a,r]=ye()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[Le(s),Ke(s)];return{animation:t?`${he(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${he(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},i=a.createElement(Ue,{toast:e}),o=a.createElement($e,{...e.ariaProps},pe(e.message,e));return a.createElement(He,{className:e.className,style:{...n,...s,...e.style}},"function"==typeof r?r({icon:i,message:o}):a.createElement(a.Fragment,null,i,o))}));!function(e){re.p=void 0,ce=e,ue=void 0,de=void 0}(a.createElement);var ze=({id:e,className:t,style:s,onHeightUpdate:r,children:n})=>{let i=a.useCallback((t=>{if(t){let s=()=>{let s=t.getBoundingClientRect().height;r(e,s)};s(),new MutationObserver(s).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,r]);return a.createElement("div",{ref:i,className:t,style:s},n)},We=le`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ge=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:r,children:n,containerStyle:i,containerClassName:o})=>{let{toasts:l,handlers:c}=(e=>{let{toasts:t,pausedAt:s}=((e={})=>{let[t,s]=(0,a.useState)(ve),r=(0,a.useRef)(ve);(0,a.useEffect)((()=>(r.current!==ve&&s(ve),ge.push(s),()=>{let e=ge.indexOf(s);e>-1&&ge.splice(e,1)})),[]);let n=t.toasts.map((t=>{var s,a,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||we[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}}));return{...t,toasts:n}})(e);(0,a.useEffect)((()=>{if(s)return;let e=Date.now(),a=t.map((t=>{if(t.duration===1/0)return;let s=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(s<0))return setTimeout((()=>Ce.dismiss(t.id)),s);t.visible&&Ce.dismiss(t.id)}));return()=>{a.forEach((e=>e&&clearTimeout(e)))}}),[t,s]);let r=(0,a.useCallback)((()=>{s&&Ee({type:6,time:Date.now()})}),[s]),n=(0,a.useCallback)(((e,s)=>{let{reverseOrder:a=!1,gutter:r=8,defaultPosition:n}=s||{},i=t.filter((t=>(t.position||n)===(e.position||n)&&t.height)),o=i.findIndex((t=>t.id===e.id)),l=i.filter(((e,t)=>t<o&&e.visible)).length;return i.filter((e=>e.visible)).slice(...a?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+r),0)}),[t]);return(0,a.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(Re.has(e))return;let s=setTimeout((()=>{Re.delete(e),Ee({type:4,toastId:e})}),t);Re.set(e,s)})(e.id,e.removeDelay);else{let t=Re.get(e.id);t&&(clearTimeout(t),Re.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:Ne,startPause:Oe,endPause:r,calculateOffset:n}}})(s);return a.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:o,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map((s=>{let i=s.position||t,o=((e,t)=>{let s=e.includes("top"),a=s?{top:0}:{bottom:0},r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:ye()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...a,...r}})(i,c.calculateOffset(s,{reverseOrder:e,gutter:r,defaultPosition:t}));return a.createElement(ze,{id:s.id,key:s.id,onHeightUpdate:c.updateHeight,className:s.visible?We:"",style:o},"custom"===s.type?pe(s.message,s):n?n(s):a.createElement(Be,{toast:s,position:i}))})))},Ve=Ce;const Je=()=>(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__header"},(0,a.createElement)("h1",null,"Feedlane Dashboard"),(0,a.createElement)("p",null,"Overview of your feedback system")),(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"feedlane-admin__grid"},(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},12),(0,a.createElement)("div",{className:"stat-label"},"Newsfeed Posts"),(0,a.createElement)("p",null,"Published announcements and updates")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},45),(0,a.createElement)("div",{className:"stat-label"},"Total Ideas"),(0,a.createElement)("p",null,"Ideas submitted by users")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},8),(0,a.createElement)("div",{className:"stat-label"},"Pending Ideas"),(0,a.createElement)("p",null,"Ideas awaiting approval")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},156),(0,a.createElement)("div",{className:"stat-label"},"Feedback Comments"),(0,a.createElement)("p",null,"User feedback on posts")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},234),(0,a.createElement)("div",{className:"stat-label"},"Total Votes"),(0,a.createElement)("p",null,"Votes cast on ideas")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("h3",null,"Quick Actions"),(0,a.createElement)("div",{className:"space-y-2"},(0,a.createElement)("a",{href:"post-new.php?post_type=feedlane_posts",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center"},"Add Newsfeed Post"),(0,a.createElement)("a",{href:"admin.php?page=feedlane-ideas",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Manage Ideas"),(0,a.createElement)("a",{href:"admin.php?page=feedlane-settings",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Settings")))))),Xe=()=>{const[e,t]=(0,a.useState)({enable_newsfeed:!0,enable_ideas:!0,enable_roadmap:!0,enable_guest_submissions:!0,sidebar_position:"left",primary_color:"#0ea5e9",firebase_config:"",firebase_webhook_secret:""}),[s,r]=(0,a.useState)(!1),n=e=>{const{name:s,value:a,type:r,checked:n}=e.target;t((e=>({...e,[s]:"checkbox"===r?n:a})))};return(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__header"},(0,a.createElement)("h1",null,"Feedlane Settings"),(0,a.createElement)("p",null,"Configure your feedback system")),(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("form",{onSubmit:async e=>{e.preventDefault(),r(!0);try{await new Promise((e=>setTimeout(e,1e3))),Ve.success("Settings saved successfully!")}catch(e){Ve.error("Failed to save settings")}finally{r(!1)}},className:"feedlane-form"},(0,a.createElement)("div",{className:"feedlane-form__section"},(0,a.createElement)("h3",null,"General Settings"),(0,a.createElement)("p",null,"Configure which tabs are enabled and basic appearance"),(0,a.createElement)("div",{className:"space-y-4"},(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",null,(0,a.createElement)("input",{type:"checkbox",name:"enable_newsfeed",checked:e.enable_newsfeed,onChange:n,className:"mr-2"}),"Enable Newsfeed Tab"),(0,a.createElement)("div",{className:"description"},"Show the newsfeed tab in the sidebar")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",null,(0,a.createElement)("input",{type:"checkbox",name:"enable_ideas",checked:e.enable_ideas,onChange:n,className:"mr-2"}),"Enable Ideas Tab"),(0,a.createElement)("div",{className:"description"},"Show the ideas submission tab in the sidebar")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",null,(0,a.createElement)("input",{type:"checkbox",name:"enable_roadmap",checked:e.enable_roadmap,onChange:n,className:"mr-2"}),"Enable Roadmap Tab"),(0,a.createElement)("div",{className:"description"},"Show the roadmap tab in the sidebar")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",null,(0,a.createElement)("input",{type:"checkbox",name:"enable_guest_submissions",checked:e.enable_guest_submissions,onChange:n,className:"mr-2"}),"Enable Guest Submissions"),(0,a.createElement)("div",{className:"description"},"Allow non-logged-in users to submit feedback and ideas")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",{htmlFor:"sidebar_position"},"Sidebar Position"),(0,a.createElement)("select",{id:"sidebar_position",name:"sidebar_position",value:e.sidebar_position,onChange:n},(0,a.createElement)("option",{value:"left"},"Left"),(0,a.createElement)("option",{value:"right"},"Right")),(0,a.createElement)("div",{className:"description"},"Choose which side of the screen the sidebar appears on")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",{htmlFor:"primary_color"},"Primary Color"),(0,a.createElement)("input",{type:"color",id:"primary_color",name:"primary_color",value:e.primary_color,onChange:n}),(0,a.createElement)("div",{className:"description"},"Choose the primary color for the sidebar and buttons")))),(0,a.createElement)("div",{className:"feedlane-form__section"},(0,a.createElement)("h3",null,"Firebase Configuration"),(0,a.createElement)("p",null,"Configure Firebase for real-time comments functionality"),(0,a.createElement)("div",{className:"space-y-4"},(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",{htmlFor:"firebase_config"},"Firebase Configuration JSON"),(0,a.createElement)("textarea",{id:"firebase_config",name:"firebase_config",value:e.firebase_config,onChange:n,rows:"6",placeholder:'{"apiKey": "...", "authDomain": "...", "projectId": "..."}'}),(0,a.createElement)("div",{className:"description"},"Paste your Firebase configuration JSON here for real-time comments")),(0,a.createElement)("div",{className:"feedlane-form__field"},(0,a.createElement)("label",{htmlFor:"firebase_webhook_secret"},"Webhook Secret"),(0,a.createElement)("input",{type:"password",id:"firebase_webhook_secret",name:"firebase_webhook_secret",value:e.firebase_webhook_secret,onChange:n}),(0,a.createElement)("div",{className:"description"},"Secret key for Firebase webhook authentication")))),(0,a.createElement)("div",{className:"feedlane-form__actions"},(0,a.createElement)("button",{type:"submit",disabled:s,className:"feedlane-btn feedlane-btn--primary"},s?"Saving...":"Save Settings")))))},Ye=()=>{const e={totalViews:1234,totalReactions:567,totalVotes:234,topPosts:[{id:1,title:"New Feature Release",views:234,reactions:45},{id:2,title:"Bug Fix Update",views:189,reactions:32},{id:3,title:"Roadmap Update",views:156,reactions:28}],topIdeas:[{id:1,title:"Dark Mode Support",votes:89,category:"Feature Request"},{id:2,title:"Mobile App",votes:67,category:"Feature Request"},{id:3,title:"Better Search",votes:45,category:"Improvement"}]};return(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__header"},(0,a.createElement)("h1",null,"Analytics"),(0,a.createElement)("p",null,"Track engagement and performance metrics")),(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"feedlane-admin__grid"},(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},e.totalViews),(0,a.createElement)("div",{className:"stat-label"},"Total Views"),(0,a.createElement)("p",null,"Post and idea views")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},e.totalReactions),(0,a.createElement)("div",{className:"stat-label"},"Total Reactions"),(0,a.createElement)("p",null,"Emoji reactions on posts")),(0,a.createElement)("div",{className:"feedlane-admin__card"},(0,a.createElement)("div",{className:"stat-number"},e.totalVotes),(0,a.createElement)("div",{className:"stat-label"},"Total Votes"),(0,a.createElement)("p",null,"Votes on ideas"))),(0,a.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"},(0,a.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,a.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Posts"),(0,a.createElement)("div",{className:"space-y-3"},e.topPosts.map((e=>(0,a.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,a.createElement)("div",null,(0,a.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,a.createElement)("p",{className:"text-sm text-gray-500"},e.views," views • ",e.reactions," reactions"))))))),(0,a.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,a.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Ideas"),(0,a.createElement)("div",{className:"space-y-3"},e.topIdeas.map((e=>(0,a.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,a.createElement)("div",null,(0,a.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,a.createElement)("p",{className:"text-sm text-gray-500"},e.category," • ",e.votes," votes"))))))))))},Ze=()=>{const[e,t]=(0,a.useState)([{id:1,title:"Dark Mode Support",category:"Feature Request",status:"pending",votes:89,submitter:"John Doe",date:"2024-01-15",excerpt:"Add dark mode theme option for better user experience..."},{id:2,title:"Mobile App",category:"Feature Request",status:"under-review",votes:67,submitter:"Jane Smith",date:"2024-01-14",excerpt:"Create a mobile application for iOS and Android..."},{id:3,title:"Better Search",category:"Improvement",status:"planned",votes:45,submitter:"Mike Johnson",date:"2024-01-13",excerpt:"Improve search functionality with filters and sorting..."}]),[s,r]=(0,a.useState)("all"),n=(e,s)=>{t((t=>t.map((t=>t.id===e?{...t,status:s}:t)))),Ve.success("Status updated successfully!")},i="all"===s?e:e.filter((e=>e.status===s));return(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__header"},(0,a.createElement)("h1",null,"Ideas Management"),(0,a.createElement)("p",null,"Review and manage submitted ideas")),(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"mb-6"},(0,a.createElement)("div",{className:"flex gap-2"},(0,a.createElement)("button",{onClick:()=>r("all"),className:"feedlane-btn feedlane-btn--small "+("all"===s?"feedlane-btn--primary":"feedlane-btn--secondary")},"All (",e.length,")"),(0,a.createElement)("button",{onClick:()=>r("pending"),className:"feedlane-btn feedlane-btn--small "+("pending"===s?"feedlane-btn--primary":"feedlane-btn--secondary")},"Pending (",e.filter((e=>"pending"===e.status)).length,")"),(0,a.createElement)("button",{onClick:()=>r("under-review"),className:"feedlane-btn feedlane-btn--small "+("under-review"===s?"feedlane-btn--primary":"feedlane-btn--secondary")},"Under Review (",e.filter((e=>"under-review"===e.status)).length,")"),(0,a.createElement)("button",{onClick:()=>r("planned"),className:"feedlane-btn feedlane-btn--small "+("planned"===s?"feedlane-btn--primary":"feedlane-btn--secondary")},"Planned (",e.filter((e=>"planned"===e.status)).length,")"))),(0,a.createElement)("div",{className:"overflow-x-auto"},(0,a.createElement)("table",{className:"feedlane-table"},(0,a.createElement)("thead",null,(0,a.createElement)("tr",null,(0,a.createElement)("th",null,"Title"),(0,a.createElement)("th",null,"Category"),(0,a.createElement)("th",null,"Status"),(0,a.createElement)("th",null,"Votes"),(0,a.createElement)("th",null,"Submitter"),(0,a.createElement)("th",null,"Date"),(0,a.createElement)("th",null,"Actions"))),(0,a.createElement)("tbody",null,i.map((e=>{return(0,a.createElement)("tr",{key:e.id},(0,a.createElement)("td",null,(0,a.createElement)("div",null,(0,a.createElement)("div",{className:"font-medium text-gray-900"},e.title),(0,a.createElement)("div",{className:"text-sm text-gray-500"},e.excerpt))),(0,a.createElement)("td",null,e.category),(0,a.createElement)("td",null,(s=e.status,(0,a.createElement)("span",{className:`feedlane-badge ${{pending:"feedlane-badge--warning","under-review":"feedlane-badge--info",planned:"feedlane-badge--success","in-progress":"feedlane-badge--info",completed:"feedlane-badge--success"}[s]||"feedlane-badge--gray"}`},s.replace("-"," ").replace(/\b\w/g,(e=>e.toUpperCase()))))),(0,a.createElement)("td",null,e.votes),(0,a.createElement)("td",null,e.submitter),(0,a.createElement)("td",null,e.date),(0,a.createElement)("td",null,(0,a.createElement)("div",{className:"flex gap-2"},"pending"===e.status&&(0,a.createElement)(a.Fragment,null,(0,a.createElement)("button",{onClick:()=>{return t=e.id,void n(t,"under-review");var t},className:"feedlane-btn feedlane-btn--primary feedlane-btn--small"},"Approve"),(0,a.createElement)("button",{onClick:()=>{return s=e.id,t((e=>e.filter((e=>e.id!==s)))),void Ve.success("Idea rejected and removed");var s},className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Reject")),"pending"!==e.status&&(0,a.createElement)("select",{value:e.status,onChange:t=>n(e.id,t.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1"},(0,a.createElement)("option",{value:"under-review"},"Under Review"),(0,a.createElement)("option",{value:"planned"},"Planned"),(0,a.createElement)("option",{value:"in-progress"},"In Progress"),(0,a.createElement)("option",{value:"completed"},"Completed")))));var s}))))),0===i.length&&(0,a.createElement)("div",{className:"feedlane-empty"},(0,a.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,a.createElement)("h3",null,"No Ideas Found"),(0,a.createElement)("p",null,"No ideas match the current filter."))))};var et=class extends P{constructor(e,t){super(),this.options=t,this.#l=e,this.#v=null,this.#E=D(),this.options.experimental_prefetchInRender||this.#E.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#l;#w=void 0;#x=void 0;#C=void 0;#N;#O;#E;#v;#R;#S;#_;#k;#P;#F;#q=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#w.addObserver(this),tt(this.#w,this.options)?this.#D():this.updateResult(),this.#Q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return st(this.#w,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return st(this.#w,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#I(),this.#T(),this.#w.removeObserver(this)}setOptions(e){const t=this.options,s=this.#w;if(this.options=this.#l.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof d(this.options.enabled,this.#w))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#M(),this.#w.setOptions(this.options),t._defaulted&&!g(this.options,t)&&this.#l.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#w,observer:this});const a=this.hasListeners();a&&at(this.#w,s,this.options,t)&&this.#D(),this.updateResult(),!a||this.#w===s&&d(this.options.enabled,this.#w)===d(t.enabled,this.#w)&&u(this.options.staleTime,this.#w)===u(t.staleTime,this.#w)||this.#A();const r=this.#j();!a||this.#w===s&&d(this.options.enabled,this.#w)===d(t.enabled,this.#w)&&r===this.#F||this.#U(r)}getOptimisticResult(e){const t=this.#l.getQueryCache().build(this.#l,e),s=this.createResult(t,e);return a=s,!g(this.getCurrentResult(),a)&&(this.#C=s,this.#O=this.options,this.#N=this.#w.state),s;var a}getCurrentResult(){return this.#C}trackResult(e,t){return new Proxy(e,{get:(e,s)=>(this.trackProp(s),t?.(s),Reflect.get(e,s))})}trackProp(e){this.#q.add(e)}getCurrentQuery(){return this.#w}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#l.defaultQueryOptions(e),s=this.#l.getQueryCache().build(this.#l,t);return s.fetch().then((()=>this.createResult(s,t)))}fetch(e){return this.#D({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#C)))}#D(e){this.#M();let t=this.#w.fetch(this.options,e);return e?.throwOnError||(t=t.catch(o)),t}#A(){this.#I();const e=u(this.options.staleTime,this.#w);if(i||this.#C.isStale||!l(e))return;const t=c(this.#C.dataUpdatedAt,e)+1;this.#k=setTimeout((()=>{this.#C.isStale||this.updateResult()}),t)}#j(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#w):this.options.refetchInterval)??!1}#U(e){this.#T(),this.#F=e,!i&&!1!==d(this.options.enabled,this.#w)&&l(this.#F)&&0!==this.#F&&(this.#P=setInterval((()=>{(this.options.refetchIntervalInBackground||F.isFocused())&&this.#D()}),this.#F))}#Q(){this.#A(),this.#U(this.#j())}#I(){this.#k&&(clearTimeout(this.#k),this.#k=void 0)}#T(){this.#P&&(clearInterval(this.#P),this.#P=void 0)}createResult(e,t){const s=this.#w,a=this.options,r=this.#C,n=this.#N,i=this.#O,o=e!==s?e.state:this.#x,{state:l}=e;let c,u={...l},d=!1;if(t._optimisticResults){const r=this.hasListeners(),n=!r&&tt(e,t),i=r&&at(e,s,t,a);(n||i)&&(u={...u,...L(l.data,e.options)}),"isRestoring"===t._optimisticResults&&(u.fetchStatus="idle")}let{error:h,errorUpdatedAt:m,status:p}=u;c=u.data;let f=!1;if(void 0!==t.placeholderData&&void 0===c&&"pending"===p){let e;r?.isPlaceholderData&&t.placeholderData===i?.placeholderData?(e=r.data,f=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#_?.state.data,this.#_):t.placeholderData,void 0!==e&&(p="success",c=x(r?.data,e,t),d=!0)}if(t.select&&void 0!==c&&!f)if(r&&c===n?.data&&t.select===this.#R)c=this.#S;else try{this.#R=t.select,c=t.select(c),c=x(r?.data,c,t),this.#S=c,this.#v=null}catch(e){this.#v=e}this.#v&&(h=this.#v,c=this.#S,m=Date.now(),p="error");const y="fetching"===u.fetchStatus,b="pending"===p,g="error"===p,v=b&&y,E=void 0!==c,w={status:p,fetchStatus:u.fetchStatus,isPending:b,isSuccess:"success"===p,isError:g,isInitialLoading:v,isLoading:v,data:c,dataUpdatedAt:u.dataUpdatedAt,error:h,errorUpdatedAt:m,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>o.dataUpdateCount||u.errorUpdateCount>o.errorUpdateCount,isFetching:y,isRefetching:y&&!b,isLoadingError:g&&!E,isPaused:"paused"===u.fetchStatus,isPlaceholderData:d,isRefetchError:g&&E,isStale:rt(e,t),refetch:this.refetch,promise:this.#E};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===w.status?e.reject(w.error):void 0!==w.data&&e.resolve(w.data)},a=()=>{const e=this.#E=w.promise=D();t(e)},r=this.#E;switch(r.status){case"pending":e.queryHash===s.queryHash&&t(r);break;case"fulfilled":"error"!==w.status&&w.data===r.value||a();break;case"rejected":"error"===w.status&&w.error===r.reason||a()}}return w}updateResult(){const e=this.#C,t=this.createResult(this.#w,this.options);this.#N=this.#w.state,this.#O=this.options,void 0!==this.#N.data&&(this.#_=this.#w),g(t,e)||(this.#C=t,this.#L({listeners:(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!this.#q.size)return!0;const a=new Set(s??this.#q);return this.options.throwOnError&&a.add("error"),Object.keys(this.#C).some((t=>{const s=t;return this.#C[s]!==e[s]&&a.has(s)}))})()}))}#M(){const e=this.#l.getQueryCache().build(this.#l,this.options);if(e===this.#w)return;const t=this.#w;this.#w=e,this.#x=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#Q()}#L(e){k.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#C)})),this.#l.getQueryCache().notify({query:this.#w,type:"observerResultsUpdated"})}))}};function tt(e,t){return function(e,t){return!1!==d(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&st(e,t,t.refetchOnMount)}function st(e,t,s){if(!1!==d(t.enabled,e)){const a="function"==typeof s?s(e):s;return"always"===a||!1!==a&&rt(e,t)}return!1}function at(e,t,s,a){return(e!==t||!1===d(a.enabled,e))&&(!s.suspense||"error"!==e.state.status)&&rt(e,s)}function rt(e,t){return!1!==d(t.enabled,e)&&e.isStaleByTime(u(t.staleTime,e))}var nt=a.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),it=a.createContext(!1),ot=(it.Provider,(e,t,s)=>t.fetchOptimistic(e).catch((()=>{s.clearReset()})));var lt=class extends P{#l;#C=void 0;#K;#H;constructor(e,t){super(),this.#l=e,this.setOptions(t),this.bindMethods(),this.#$()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#l.defaultMutationOptions(e),g(this.options,t)||this.#l.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#K,observer:this}),t?.mutationKey&&this.options.mutationKey&&f(t.mutationKey)!==f(this.options.mutationKey)?this.reset():"pending"===this.#K?.state.status&&this.#K.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#K?.removeObserver(this)}onMutationUpdate(e){this.#$(),this.#L(e)}getCurrentResult(){return this.#C}reset(){this.#K?.removeObserver(this),this.#K=void 0,this.#$(),this.#L()}mutate(e,t){return this.#H=t,this.#K?.removeObserver(this),this.#K=this.#l.getMutationCache().build(this.#l,this.options),this.#K.addObserver(this),this.#K.execute(e)}#$(){const e=this.#K?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#C={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#L(e){k.batch((()=>{if(this.#H&&this.hasListeners()){const t=this.#C.variables,s=this.#C.context;"success"===e?.type?(this.#H.onSuccess?.(e.data,t,s),this.#H.onSettled?.(e.data,null,t,s)):"error"===e?.type&&(this.#H.onError?.(e.error,t,s),this.#H.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach((e=>{e(this.#C)}))}))}};function ct(e,t){const s=X(t),[r]=a.useState((()=>new lt(s,e)));a.useEffect((()=>{r.setOptions(e)}),[r,e]);const n=a.useSyncExternalStore(a.useCallback((e=>r.subscribe(k.batchCalls(e))),[r]),(()=>r.getCurrentResult()),(()=>r.getCurrentResult())),i=a.useCallback(((e,t)=>{r.mutate(e,t).catch(o)}),[r]);if(n.error&&S(r.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:i,mutateAsync:n.mutate}}var ut={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},dt=r().createContext&&r().createContext(ut),ht=function(){return ht=Object.assign||function(e){for(var t,s=1,a=arguments.length;s<a;s++)for(var r in t=arguments[s])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},ht.apply(this,arguments)};function mt(e){return e&&e.map((function(e,t){return r().createElement(e.tag,ht({key:t},e.attr),mt(e.child))}))}function pt(e){return function(t){return r().createElement(ft,ht({attr:ht({},e.attr)},t),mt(e.child))}}function ft(e){var t=function(t){var s,a=e.attr,n=e.size,i=e.title,o=function(e,t){var s={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(s[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(s[a[r]]=e[a[r]])}return s}(e,["attr","size","title"]),l=n||t.size||"1em";return t.className&&(s=t.className),e.className&&(s=(s?s+" ":"")+e.className),r().createElement("svg",ht({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,o,{className:s,style:ht(ht({color:e.color||t.color},t.style),e.style),height:l,width:l,xmlns:"http://www.w3.org/2000/svg"}),i&&r().createElement("title",null,i),e.children)};return void 0!==dt?r().createElement(dt.Consumer,null,(function(e){return t(e)})):t(ut)}function yt(e){return pt({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"}}]})(e)}function bt(e){return pt({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(e)}function gt(e){return pt({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}},{tag:"polyline",attr:{points:"17 21 17 13 7 13 7 21"}},{tag:"polyline",attr:{points:"7 3 7 8 15 8"}}]})(e)}function vt(e){return pt({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"3 6 5 6 21 6"}},{tag:"path",attr:{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}},{tag:"line",attr:{x1:"10",y1:"11",x2:"10",y2:"17"}},{tag:"line",attr:{x1:"14",y1:"11",x2:"14",y2:"17"}}]})(e)}function Et(e){return pt({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}const wt=({category:e,isEditing:t,editingCategory:s,setEditingCategory:r,onEdit:n,onUpdate:i,onDelete:o,isUpdating:l,isDeleting:c})=>t?(0,a.createElement)("div",{className:"p-6"},(0,a.createElement)("form",{onSubmit:i,className:"space-y-4"},(0,a.createElement)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4"},(0,a.createElement)("div",null,(0,a.createElement)("label",{className:"block text-sm font-medium text-gray-700 mb-1"},"Name *"),(0,a.createElement)("input",{type:"text",value:s.name,onChange:e=>r((t=>({...t,name:e.target.value}))),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})),(0,a.createElement)("div",null,(0,a.createElement)("label",{className:"block text-sm font-medium text-gray-700 mb-1"},"Color"),(0,a.createElement)("input",{type:"color",value:s.color,onChange:e=>r((t=>({...t,color:e.target.value}))),className:"w-full h-10 border border-gray-300 rounded-lg"}))),(0,a.createElement)("div",null,(0,a.createElement)("label",{className:"block text-sm font-medium text-gray-700 mb-1"},"Description"),(0,a.createElement)("textarea",{value:s.description,onChange:e=>r((t=>({...t,description:e.target.value}))),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3})),(0,a.createElement)("div",{className:"flex gap-2"},(0,a.createElement)("button",{type:"submit",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small",disabled:l},(0,a.createElement)(gt,{size:14}),l?"Saving...":"Save"),(0,a.createElement)("button",{type:"button",onClick:()=>r(null),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},(0,a.createElement)(Et,{size:14}),"Cancel")))):(0,a.createElement)("div",{className:"p-6 flex items-center justify-between"},(0,a.createElement)("div",{className:"flex items-center gap-4"},(0,a.createElement)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e.meta?.color||"#10B981"}}),(0,a.createElement)("div",null,(0,a.createElement)("h4",{className:"font-medium text-gray-900"},e.name),e.description&&(0,a.createElement)("p",{className:"text-sm text-gray-500 mt-1"},e.description),(0,a.createElement)("p",{className:"text-xs text-gray-400 mt-1"},e.count||0," ideas"))),(0,a.createElement)("div",{className:"flex gap-2"},(0,a.createElement)("button",{onClick:()=>n(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small",title:"Edit category"},(0,a.createElement)(yt,{size:14})),(0,a.createElement)("button",{onClick:()=>o(e.id,e.name),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small",disabled:c,title:"Delete category"},(0,a.createElement)(vt,{size:14})))),xt=()=>{const[e,t]=(0,a.useState)(!1),[s,r]=(0,a.useState)(null),[n,l]=(0,a.useState)({name:"",description:"",color:"#10B981"}),c=X(),{data:u=[],isLoading:d,error:h}=function(e,t,s){const r=X(s),n=a.useContext(it),l=a.useContext(nt),c=r.defaultQueryOptions(e);r.getDefaultOptions().queries?._experimental_beforeQuery?.(c),c._optimisticResults=n?"isRestoring":"optimistic",(e=>{const t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))})(c),((e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))})(c,l),(e=>{a.useEffect((()=>{e.clearReset()}),[e])})(l);const u=!r.getQueryCache().get(c.queryHash),[d]=a.useState((()=>new t(r,c))),h=d.getOptimisticResult(c),m=!n&&!1!==e.subscribed;if(a.useSyncExternalStore(a.useCallback((e=>{const t=m?d.subscribe(k.batchCalls(e)):o;return d.updateResult(),t}),[d,m]),(()=>d.getCurrentResult()),(()=>d.getCurrentResult())),a.useEffect((()=>{d.setOptions(c)}),[c,d]),((e,t)=>e?.suspense&&t.isPending)(c,h))throw ot(c,d,l);if((({result:e,errorResetBoundary:t,throwOnError:s,query:a,suspense:r})=>e.isError&&!t.isReset()&&!e.isFetching&&a&&(r&&void 0===e.data||S(s,[e.error,a])))({result:h,errorResetBoundary:l,throwOnError:c.throwOnError,query:r.getQueryCache().get(c.queryHash),suspense:c.suspense}))throw h.error;if(r.getDefaultOptions().queries?._experimental_afterQuery?.(c,h),c.experimental_prefetchInRender&&!i&&((e,t)=>e.isLoading&&e.isFetching&&!t)(h,n)){const e=u?ot(c,d,l):r.getQueryCache().get(c.queryHash)?.promise;e?.catch(o).finally((()=>{d.updateResult()}))}return c.notifyOnChangeProps?h:d.trackResult(h)}({queryKey:["categories"],queryFn:async()=>{const e=await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories?per_page=100`,{headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(!e.ok)throw new Error("Failed to fetch categories");return e.json()}},et,void 0),m=ct({mutationFn:async e=>{const t=await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":window.feedlaneData.nonce},body:JSON.stringify(e)});if(!t.ok)throw new Error("Failed to create category");return t.json()},onSuccess:()=>{c.invalidateQueries(["categories"]),t(!1),l({name:"",description:"",color:"#10B981"}),Ce.success("Category created successfully!")},onError:e=>{Ce.error(e.message||"Failed to create category")}}),p=ct({mutationFn:async({id:e,...t})=>{const s=await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories/${e}`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":window.feedlaneData.nonce},body:JSON.stringify(t)});if(!s.ok)throw new Error("Failed to update category");return s.json()},onSuccess:()=>{c.invalidateQueries(["categories"]),r(null),Ce.success("Category updated successfully!")},onError:e=>{Ce.error(e.message||"Failed to update category")}}),f=ct({mutationFn:async e=>{const t=await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories/${e}?force=true`,{method:"DELETE",headers:{"X-WP-Nonce":window.feedlaneData.nonce}});if(!t.ok)throw new Error("Failed to delete category");return t.json()},onSuccess:()=>{c.invalidateQueries(["categories"]),Ce.success("Category deleted successfully!")},onError:e=>{Ce.error(e.message||"Failed to delete category")}}),y=e=>{e.preventDefault(),s.name.trim()?p.mutate({id:s.id,name:s.name,description:s.description,meta:{color:s.color}}):Ce.error("Category name is required")},b=(e,t)=>{window.confirm(`Are you sure you want to delete the category "${t}"? This action cannot be undone.`)&&f.mutate(e)},g=e=>{r({id:e.id,name:e.name,description:e.description||"",color:e.meta?.color||"#10B981"})};return d?(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"text-center py-12"},(0,a.createElement)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.createElement)("p",{className:"mt-4 text-gray-600"},"Loading categories...")))):h?(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"text-center py-12"},(0,a.createElement)("p",{className:"text-red-600"},"Failed to load categories: ",h.message)))):(0,a.createElement)("div",{className:"feedlane-admin"},(0,a.createElement)("div",{className:"feedlane-admin__header"},(0,a.createElement)("h1",null,"Categories Management"),(0,a.createElement)("p",null,"Manage idea categories for better organization")),(0,a.createElement)("div",{className:"feedlane-admin__content"},(0,a.createElement)("div",{className:"mb-6"},(0,a.createElement)("button",{onClick:()=>t(!0),className:"feedlane-btn feedlane-btn--primary",disabled:e},(0,a.createElement)(bt,{size:16}),"Add New Category")),e&&(0,a.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6"},(0,a.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Create New Category"),(0,a.createElement)("form",{onSubmit:e=>{e.preventDefault(),n.name.trim()?m.mutate({name:n.name,description:n.description,meta:{color:n.color}}):Ce.error("Category name is required")},className:"space-y-4"},(0,a.createElement)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4"},(0,a.createElement)("div",null,(0,a.createElement)("label",{htmlFor:"new-name",className:"block text-sm font-medium text-gray-700 mb-1"},"Name *"),(0,a.createElement)("input",{type:"text",id:"new-name",value:n.name,onChange:e=>l((t=>({...t,name:e.target.value}))),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"e.g., Feature Request",required:!0})),(0,a.createElement)("div",null,(0,a.createElement)("label",{htmlFor:"new-color",className:"block text-sm font-medium text-gray-700 mb-1"},"Color"),(0,a.createElement)("input",{type:"color",id:"new-color",value:n.color,onChange:e=>l((t=>({...t,color:e.target.value}))),className:"w-full h-10 border border-gray-300 rounded-lg"}))),(0,a.createElement)("div",null,(0,a.createElement)("label",{htmlFor:"new-description",className:"block text-sm font-medium text-gray-700 mb-1"},"Description"),(0,a.createElement)("textarea",{id:"new-description",value:n.description,onChange:e=>l((t=>({...t,description:e.target.value}))),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"Optional description for this category"})),(0,a.createElement)("div",{className:"flex gap-2"},(0,a.createElement)("button",{type:"submit",className:"feedlane-btn feedlane-btn--primary",disabled:m.isLoading},(0,a.createElement)(gt,{size:16}),m.isLoading?"Creating...":"Create Category"),(0,a.createElement)("button",{type:"button",onClick:()=>{t(!1),l({name:"",description:"",color:"#10B981"})},className:"feedlane-btn feedlane-btn--secondary"},(0,a.createElement)(Et,{size:16}),"Cancel")))),(0,a.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200"},(0,a.createElement)("div",{className:"px-6 py-4 border-b border-gray-200"},(0,a.createElement)("h3",{className:"text-lg font-semibold text-gray-900"},"Categories (",u.length,")")),0===u.length?(0,a.createElement)("div",{className:"text-center py-12"},(0,a.createElement)("p",{className:"text-gray-500"},"No categories found. Create your first category to get started.")):(0,a.createElement)("div",{className:"divide-y divide-gray-200"},u.map((e=>(0,a.createElement)(wt,{key:e.id,category:e,isEditing:s?.id===e.id,editingCategory:s,setEditingCategory:r,onEdit:g,onUpdate:y,onDelete:b,isUpdating:p.isLoading,isDeleting:f.isLoading})))))))},Ct=new class{#B;#f;#u;#z;#W;#G;#V;#J;constructor(e={}){this.#B=e.queryCache||new K,this.#f=e.mutationCache||new $,this.#u=e.defaultOptions||{},this.#z=new Map,this.#W=new Map,this.#G=0}mount(){this.#G++,1===this.#G&&(this.#V=F.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#B.onFocus())})),this.#J=q.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#B.onOnline())})))}unmount(){this.#G--,0===this.#G&&(this.#V?.(),this.#V=void 0,this.#J?.(),this.#J=void 0)}isFetching(e){return this.#B.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#f.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#B.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=this.#B.build(this,t),a=s.state.data;return void 0===a?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(u(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(a))}getQueriesData(e){return this.#B.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,s){const a=this.defaultQueryOptions({queryKey:e}),r=this.#B.get(a.queryHash),n=r?.state.data,i=function(e,t){return"function"==typeof e?e(t):e}(t,n);if(void 0!==i)return this.#B.build(this,a).setData(i,{...s,manual:!0})}setQueriesData(e,t,s){return k.batch((()=>this.#B.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,s)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#B.get(t.queryHash)?.state}removeQueries(e){const t=this.#B;k.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const s=this.#B;return k.batch((()=>(s.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries({type:"active",...e},t))))}cancelQueries(e,t={}){const s={revert:!0,...t},a=k.batch((()=>this.#B.findAll(e).map((e=>e.cancel(s)))));return Promise.all(a).then(o).catch(o)}invalidateQueries(e,t={}){return k.batch((()=>(this.#B.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))))}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},a=k.batch((()=>this.#B.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(o)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(a).then(o)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const s=this.#B.build(this,t);return s.isStaleByTime(u(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(o).catch(o)}fetchInfiniteQuery(e){return e.behavior=z(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(o).catch(o)}ensureInfiniteQueryData(e){return e.behavior=z(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return q.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#B}getMutationCache(){return this.#f}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#z.set(f(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#z.values()],s={};return t.forEach((t=>{y(e,t.queryKey)&&Object.assign(s,t.defaultOptions)})),s}setMutationDefaults(e,t){this.#W.set(f(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#W.values()],s={};return t.forEach((t=>{y(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)})),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=p(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===O&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#B.clear(),this.#f.clear()}}({defaultOptions:{queries:{retry:2,staleTime:3e5}}}),Nt=({page:e})=>(0,a.createElement)(Y,{client:Ct},(()=>{switch(e){case"dashboard":default:return(0,a.createElement)(Je,null);case"settings":return(0,a.createElement)(Xe,null);case"analytics":return(0,a.createElement)(Ye,null);case"ideas":return(0,a.createElement)(Ze,null);case"categories":return(0,a.createElement)(xt,null)}})(),(0,a.createElement)(Ge,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("feedlane-admin-dashboard");e&&(0,n.H)(e).render((0,a.createElement)(Nt,{page:"dashboard"}));const t=document.getElementById("feedlane-admin-settings");t&&(0,n.H)(t).render((0,a.createElement)(Nt,{page:"settings"}));const s=document.getElementById("feedlane-admin-analytics");s&&(0,n.H)(s).render((0,a.createElement)(Nt,{page:"analytics"}));const r=document.getElementById("feedlane-admin-ideas");r&&(0,n.H)(r).render((0,a.createElement)(Nt,{page:"ideas"}));const i=document.getElementById("feedlane-admin-categories");i&&(0,n.H)(i).render((0,a.createElement)(Nt,{page:"categories"}))}))})();