(()=>{"use strict";var e={576:(e,a,t)=>{var n=t(795);a.H=n.createRoot,n.hydrateRoot},795:e=>{e.exports=window.ReactDOM}},a={};function t(n){var l=a[n];if(void 0!==l)return l.exports;var s=a[n]={exports:{}};return e[n](s,s.exports,t),s.exports}t.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return t.d(a,{a}),a},t.d=(e,a)=>{for(var n in a)t.o(a,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:a[n]})},t.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a);const n=window.React;var l=t.n(n),s=t(576);let r={data:""},c=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||r,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,o=/\/\*[^]*?\*\/|  +/g,i=/\n+/g,m=(e,a)=>{let t="",n="",l="";for(let s in e){let r=e[s];"@"==s[0]?"i"==s[1]?t=s+" "+r+";":n+="f"==s[1]?m(r,s):s+"{"+m(r,"k"==s[1]?"":a)+"}":"object"==typeof r?n+=m(r,a?a.replace(/([^,])+/g,(e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(a=>/&/.test(a)?a.replace(/&/g,e):e?e+" "+a:a)))):s):null!=r&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),l+=m.p?m.p(s,r):s+":"+r+";")}return t+(a&&l?a+"{"+l+"}":l)+n},u={},f=e=>{if("object"==typeof e){let a="";for(let t in e)a+=t+f(e[t]);return a}return e},p=(e,a,t,n,l)=>{let s=f(e),r=u[s]||(u[s]=(e=>{let a=0,t=11;for(;a<e.length;)t=101*t+e.charCodeAt(a++)>>>0;return"go"+t})(s));if(!u[r]){let a=s!==e?e:(e=>{let a,t,n=[{}];for(;a=d.exec(e.replace(o,""));)a[4]?n.shift():a[3]?(t=a[3].replace(i," ").trim(),n.unshift(n[0][t]=n[0][t]||{})):n[0][a[1]]=a[2].replace(i," ").trim();return n[0]})(e);u[r]=m(l?{["@keyframes "+r]:a}:a,t?"":"."+r)}let c=t&&u.g?u.g:null;return t&&(u.g=u[r]),((e,a,t,n)=>{n?a.data=a.data.replace(n,e):-1===a.data.indexOf(e)&&(a.data=t?e+a.data:a.data+e)})(u[r],a,n,c),r};function E(e){let a=this||{},t=e.call?e(a.p):e;return p(t.unshift?t.raw?((e,a,t)=>e.reduce(((e,n,l)=>{let s=a[l];if(s&&s.call){let e=s(t),a=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=a?"."+a:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+n+(null==s?"":s)}),""))(t,[].slice.call(arguments,1),a.p):t.reduce(((e,t)=>Object.assign(e,t&&t.call?t(a.p):t)),{}):t,c(a.target),a.g,a.o,a.k)}E.bind({g:1});let g,b,_,v=E.bind({k:1});function y(e,a){let t=this||{};return function(){let n=arguments;function l(s,r){let c=Object.assign({},s),d=c.className||l.className;t.p=Object.assign({theme:b&&b()},c),t.o=/ *go\d+/.test(d),c.className=E.apply(t,n)+(d?" "+d:""),a&&(c.ref=r);let o=e;return e[0]&&(o=c.as||e,delete c.as),_&&o[0]&&_(c),g(o,c)}return a?a(l):l}}var h=(e,a)=>(e=>"function"==typeof e)(e)?e(a):e,N=(()=>{let e=0;return()=>(++e).toString()})(),w=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let a=matchMedia("(prefers-reduced-motion: reduce)");e=!a||a.matches}return e}})(),x=(e,a)=>{switch(a.type){case 0:return{...e,toasts:[a.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===a.toast.id?{...e,...a.toast}:e))};case 2:let{toast:t}=a;return x(e,{type:e.toasts.find((e=>e.id===t.id))?1:0,toast:t});case 3:let{toastId:n}=a;return{...e,toasts:e.toasts.map((e=>e.id===n||void 0===n?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==a.toastId))};case 5:return{...e,pausedAt:a.time};case 6:let l=a.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+l})))}}},k=[],C={toasts:[],pausedAt:void 0},S=e=>{C=x(C,e),k.forEach((e=>{e(C)}))},F={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},A=e=>(a,t)=>{let n=((e,a="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||N()}))(a,e,t);return S({type:2,toast:n}),n.id},D=(e,a)=>A("blank")(e,a);D.error=A("error"),D.success=A("success"),D.loading=A("loading"),D.custom=A("custom"),D.dismiss=e=>{S({type:3,toastId:e})},D.remove=e=>S({type:4,toastId:e}),D.promise=(e,a,t)=>{let n=D.loading(a.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let l=a.success?h(a.success,e):void 0;return l?D.success(l,{id:n,...t,...null==t?void 0:t.success}):D.dismiss(n),e})).catch((e=>{let l=a.error?h(a.error,e):void 0;l?D.error(l,{id:n,...t,...null==t?void 0:t.error}):D.dismiss(n)})),e};var j=(e,a)=>{S({type:1,toast:{id:e,height:a}})},P=()=>{S({type:5,time:Date.now()})},O=new Map,T=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,I=v`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,M=v`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${T} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${I} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${M} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,$=v`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,R=y("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${$} 1s linear infinite;
`,L=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,H=v`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,z=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${H} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=y("div")`
  position: absolute;
`,U=y("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,q=v`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,G=y("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${q} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,J=({toast:e})=>{let{icon:a,type:t,iconTheme:l}=e;return void 0!==a?"string"==typeof a?n.createElement(G,null,a):a:"blank"===t?null:n.createElement(U,null,n.createElement(R,{...l}),"loading"!==t&&n.createElement(V,null,"error"===t?n.createElement(B,{...l}):n.createElement(z,{...l})))},K=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Q=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,W=y("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Y=y("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Z=n.memo((({toast:e,position:a,style:t,children:l})=>{let s=e.height?((e,a)=>{let t=e.includes("top")?1:-1,[n,l]=w()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[K(t),Q(t)];return{animation:a?`${v(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${v(l)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||a||"top-center",e.visible):{opacity:0},r=n.createElement(J,{toast:e}),c=n.createElement(Y,{...e.ariaProps},h(e.message,e));return n.createElement(W,{className:e.className,style:{...s,...t,...e.style}},"function"==typeof l?l({icon:r,message:c}):n.createElement(n.Fragment,null,r,c))}));!function(e){m.p=void 0,g=e,b=void 0,_=void 0}(n.createElement);var X=({id:e,className:a,style:t,onHeightUpdate:l,children:s})=>{let r=n.useCallback((a=>{if(a){let t=()=>{let t=a.getBoundingClientRect().height;l(e,t)};t(),new MutationObserver(t).observe(a,{subtree:!0,childList:!0,characterData:!0})}}),[e,l]);return n.createElement("div",{ref:r,className:a,style:t},s)},ee=E`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ae=({reverseOrder:e,position:a="top-center",toastOptions:t,gutter:l,children:s,containerStyle:r,containerClassName:c})=>{let{toasts:d,handlers:o}=(e=>{let{toasts:a,pausedAt:t}=((e={})=>{let[a,t]=(0,n.useState)(C),l=(0,n.useRef)(C);(0,n.useEffect)((()=>(l.current!==C&&t(C),k.push(t),()=>{let e=k.indexOf(t);e>-1&&k.splice(e,1)})),[]);let s=a.toasts.map((a=>{var t,n,l;return{...e,...e[a.type],...a,removeDelay:a.removeDelay||(null==(t=e[a.type])?void 0:t.removeDelay)||(null==e?void 0:e.removeDelay),duration:a.duration||(null==(n=e[a.type])?void 0:n.duration)||(null==e?void 0:e.duration)||F[a.type],style:{...e.style,...null==(l=e[a.type])?void 0:l.style,...a.style}}}));return{...a,toasts:s}})(e);(0,n.useEffect)((()=>{if(t)return;let e=Date.now(),n=a.map((a=>{if(a.duration===1/0)return;let t=(a.duration||0)+a.pauseDuration-(e-a.createdAt);if(!(t<0))return setTimeout((()=>D.dismiss(a.id)),t);a.visible&&D.dismiss(a.id)}));return()=>{n.forEach((e=>e&&clearTimeout(e)))}}),[a,t]);let l=(0,n.useCallback)((()=>{t&&S({type:6,time:Date.now()})}),[t]),s=(0,n.useCallback)(((e,t)=>{let{reverseOrder:n=!1,gutter:l=8,defaultPosition:s}=t||{},r=a.filter((a=>(a.position||s)===(e.position||s)&&a.height)),c=r.findIndex((a=>a.id===e.id)),d=r.filter(((e,a)=>a<c&&e.visible)).length;return r.filter((e=>e.visible)).slice(...n?[d+1]:[0,d]).reduce(((e,a)=>e+(a.height||0)+l),0)}),[a]);return(0,n.useEffect)((()=>{a.forEach((e=>{if(e.dismissed)((e,a=1e3)=>{if(O.has(e))return;let t=setTimeout((()=>{O.delete(e),S({type:4,toastId:e})}),a);O.set(e,t)})(e.id,e.removeDelay);else{let a=O.get(e.id);a&&(clearTimeout(a),O.delete(e.id))}}))}),[a]),{toasts:a,handlers:{updateHeight:j,startPause:P,endPause:l,calculateOffset:s}}})(t);return n.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...r},className:c,onMouseEnter:o.startPause,onMouseLeave:o.endPause},d.map((t=>{let r=t.position||a,c=((e,a)=>{let t=e.includes("top"),n=t?{top:0}:{bottom:0},l=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:w()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${a*(t?1:-1)}px)`,...n,...l}})(r,o.calculateOffset(t,{reverseOrder:e,gutter:l,defaultPosition:a}));return n.createElement(X,{id:t.id,key:t.id,onHeightUpdate:o.updateHeight,className:t.visible?ee:"",style:c},"custom"===t.type?h(t.message,t):s?s(t):n.createElement(Z,{toast:t,position:r}))})))},te=D;const ne=()=>(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Dashboard"),(0,n.createElement)("p",null,"Overview of your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-admin__grid"},(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},12),(0,n.createElement)("div",{className:"stat-label"},"Newsfeed Posts"),(0,n.createElement)("p",null,"Published announcements and updates")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},45),(0,n.createElement)("div",{className:"stat-label"},"Total Ideas"),(0,n.createElement)("p",null,"Ideas submitted by users")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},8),(0,n.createElement)("div",{className:"stat-label"},"Pending Ideas"),(0,n.createElement)("p",null,"Ideas awaiting approval")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},156),(0,n.createElement)("div",{className:"stat-label"},"Feedback Comments"),(0,n.createElement)("p",null,"User feedback on posts")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},234),(0,n.createElement)("div",{className:"stat-label"},"Total Votes"),(0,n.createElement)("p",null,"Votes cast on ideas")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("h3",null,"Quick Actions"),(0,n.createElement)("div",{className:"space-y-2"},(0,n.createElement)("a",{href:"post-new.php?post_type=feedlane_posts",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center"},"Add Newsfeed Post"),(0,n.createElement)("a",{href:"admin.php?page=feedlane-ideas",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Manage Ideas"),(0,n.createElement)("a",{href:"admin.php?page=feedlane-settings",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Settings"))))))),le=()=>{const[e,a]=(0,n.useState)({feedlane_enable_newsfeed:!0,feedlane_enable_ideas:!0,feedlane_enable_roadmap:!0,feedlane_enable_guest_submissions:!0,feedlane_enable_floating_sidebar:!0,feedlane_sidebar_position:"left",feedlane_primary_color:"#0ea5e9",feedlane_firebase_config:"",feedlane_firebase_webhook_secret:""}),[t,l]=(0,n.useState)(!1),[s,r]=(0,n.useState)(!0);(0,n.useEffect)((()=>{c()}),[]);const c=async()=>{try{const e=new FormData;e.append("action","feedlane_get_settings"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e});if(t.ok){const e=await t.json();e.success&&a(e.data)}}catch(e){console.error("Failed to load settings:",e)}finally{r(!1)}},d=e=>{const{name:t,value:n,type:l,checked:s}=e.target;a((e=>({...e,[t]:"checkbox"===l?s:n})))};return s?(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Settings"),(0,n.createElement)("p",null,"Configure your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading settings...")))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Settings"),(0,n.createElement)("p",null,"Configure your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("form",{onSubmit:async a=>{a.preventDefault(),l(!0);try{const a=new FormData;a.append("action","feedlane_save_settings"),a.append("nonce",feedlaneAdmin.nonce),Object.keys(e).forEach((t=>{a.append(t,e[t])}));const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?te.success("Settings saved successfully!"):te.error(n.data||"Failed to save settings")}catch(e){te.error("Failed to save settings")}finally{l(!1)}},className:"feedlane-form"},(0,n.createElement)("div",{className:"feedlane-form__section"},(0,n.createElement)("h3",null,"General Settings"),(0,n.createElement)("p",null,"Configure which tabs are enabled and basic appearance"),(0,n.createElement)("div",{className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_newsfeed",checked:e.feedlane_enable_newsfeed,onChange:d,className:"mr-2"}),"Enable Newsfeed Tab"),(0,n.createElement)("div",{className:"description"},"Show the newsfeed tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_ideas",checked:e.feedlane_enable_ideas,onChange:d,className:"mr-2"}),"Enable Ideas Tab"),(0,n.createElement)("div",{className:"description"},"Show the ideas submission tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_roadmap",checked:e.feedlane_enable_roadmap,onChange:d,className:"mr-2"}),"Enable Roadmap Tab"),(0,n.createElement)("div",{className:"description"},"Show the roadmap tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_guest_submissions",checked:e.feedlane_enable_guest_submissions,onChange:d,className:"mr-2"}),"Enable Guest Submissions"),(0,n.createElement)("div",{className:"description"},"Allow non-logged-in users to submit feedback and ideas")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_floating_sidebar",checked:e.feedlane_enable_floating_sidebar,onChange:d,className:"mr-2"}),"Enable Floating Sidebar"),(0,n.createElement)("div",{className:"description"},"Show the floating feedback sidebar on all pages")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_sidebar_position"},"Sidebar Position"),(0,n.createElement)("select",{id:"feedlane_sidebar_position",name:"feedlane_sidebar_position",value:e.feedlane_sidebar_position,onChange:d,className:"feedlane-select"},(0,n.createElement)("option",{value:"left"},"Left"),(0,n.createElement)("option",{value:"right"},"Right")),(0,n.createElement)("div",{className:"description"},"Choose which side of the screen the sidebar appears on")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_primary_color"},"Primary Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"feedlane_primary_color",name:"feedlane_primary_color",value:e.feedlane_primary_color,onChange:d,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:e.feedlane_primary_color,onChange:d,name:"feedlane_primary_color",className:"feedlane-color-text",placeholder:"#0ea5e9"})),(0,n.createElement)("div",{className:"description"},"Choose the primary color for the sidebar and buttons")))),(0,n.createElement)("div",{className:"feedlane-form__section"},(0,n.createElement)("h3",null,"Firebase Configuration"),(0,n.createElement)("p",null,"Configure Firebase for real-time comments functionality"),(0,n.createElement)("div",{className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_firebase_config"},"Firebase Configuration JSON"),(0,n.createElement)("textarea",{id:"feedlane_firebase_config",name:"feedlane_firebase_config",value:e.feedlane_firebase_config,onChange:d,rows:"6",className:"feedlane-textarea",placeholder:'{"apiKey": "...", "authDomain": "...", "projectId": "..."}'}),(0,n.createElement)("div",{className:"description"},"Paste your Firebase configuration JSON here for real-time comments")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_firebase_webhook_secret"},"Webhook Secret"),(0,n.createElement)("input",{type:"password",id:"feedlane_firebase_webhook_secret",name:"feedlane_firebase_webhook_secret",value:e.feedlane_firebase_webhook_secret,onChange:d,className:"feedlane-input"}),(0,n.createElement)("div",{className:"description"},"Secret key for Firebase webhook authentication")))),(0,n.createElement)("div",{className:"feedlane-form__actions"},(0,n.createElement)("button",{type:"submit",disabled:t,className:"feedlane-btn feedlane-btn--primary"},t?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Saving..."):"Save Settings"))))))},se=()=>{const e={totalViews:1234,totalReactions:567,totalVotes:234,topPosts:[{id:1,title:"New Feature Release",views:234,reactions:45},{id:2,title:"Bug Fix Update",views:189,reactions:32},{id:3,title:"Roadmap Update",views:156,reactions:28}],topIdeas:[{id:1,title:"Dark Mode Support",votes:89,category:"Feature Request"},{id:2,title:"Mobile App",votes:67,category:"Feature Request"},{id:3,title:"Better Search",votes:45,category:"Improvement"}]};return(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Analytics"),(0,n.createElement)("p",null,"Track engagement and performance metrics")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-admin__grid"},(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalViews),(0,n.createElement)("div",{className:"stat-label"},"Total Views"),(0,n.createElement)("p",null,"Post and idea views")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalReactions),(0,n.createElement)("div",{className:"stat-label"},"Total Reactions"),(0,n.createElement)("p",null,"Emoji reactions on posts")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalVotes),(0,n.createElement)("div",{className:"stat-label"},"Total Votes"),(0,n.createElement)("p",null,"Votes on ideas"))),(0,n.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"},(0,n.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,n.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Posts"),(0,n.createElement)("div",{className:"space-y-3"},e.topPosts.map((e=>(0,n.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,n.createElement)("div",null,(0,n.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,n.createElement)("p",{className:"text-sm text-gray-500"},e.views," views • ",e.reactions," reactions"))))))),(0,n.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,n.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Ideas"),(0,n.createElement)("div",{className:"space-y-3"},e.topIdeas.map((e=>(0,n.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,n.createElement)("div",null,(0,n.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,n.createElement)("p",{className:"text-sm text-gray-500"},e.category," • ",e.votes," votes")))))))))))},re=()=>{const[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)(!0),[s,r]=(0,n.useState)(1),[c,d]=(0,n.useState)(1),[o,i]=(0,n.useState)("all");(0,n.useEffect)((()=>{m()}),[s,o]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_ideas"),e.append("nonce",feedlaneAdmin.nonce),e.append("page",s),e.append("per_page",20),e.append("status",o);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?(a(n.data.ideas),d(n.data.pages)):te.error("Failed to load ideas")}catch(e){te.error("Failed to load ideas")}finally{l(!1)}};return t?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Ideas Management"),(0,n.createElement)("p",null,"Manage submitted ideas and feedback")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading ideas..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Ideas Management"),(0,n.createElement)("p",null,"Manage submitted ideas and feedback")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"mb-6 flex items-center gap-4"},(0,n.createElement)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-gray-700"},"Filter by status:"),(0,n.createElement)("select",{id:"status-filter",value:o,onChange:e=>i(e.target.value),className:"feedlane-select w-auto"},(0,n.createElement)("option",{value:"all"},"All Status"),(0,n.createElement)("option",{value:"publish"},"Published"),(0,n.createElement)("option",{value:"draft"},"Draft"),(0,n.createElement)("option",{value:"pending"},"Pending"),(0,n.createElement)("option",{value:"private"},"Private"))),e.length>0?(0,n.createElement)("div",{className:"feedlane-card"},(0,n.createElement)("div",{className:"feedlane-card__content"},(0,n.createElement)("table",{className:"feedlane-table"},(0,n.createElement)("thead",null,(0,n.createElement)("tr",null,(0,n.createElement)("th",null,"Title"),(0,n.createElement)("th",null,"Author"),(0,n.createElement)("th",null,"Status"),(0,n.createElement)("th",null,"Categories"),(0,n.createElement)("th",null,"Votes"),(0,n.createElement)("th",null,"Date"),(0,n.createElement)("th",null,"Actions"))),(0,n.createElement)("tbody",null,e.map((e=>(0,n.createElement)("tr",{key:e.id},(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"font-medium text-gray-900"},e.title),e.content&&(0,n.createElement)("div",{className:"text-sm text-gray-500 mt-1"},e.content.substring(0,100),"...")),(0,n.createElement)("td",null,e.author),(0,n.createElement)("td",null,(e=>{const a={publish:{label:"Published",class:"feedlane-badge--success"},draft:{label:"Draft",class:"feedlane-badge--gray"},pending:{label:"Pending",class:"feedlane-badge--warning"},private:{label:"Private",class:"feedlane-badge--info"}}[e]||{label:e,class:"feedlane-badge--gray"};return(0,n.createElement)("span",{className:`feedlane-badge ${a.class}`},a.label)})(e.status)),(0,n.createElement)("td",null,e.categories.map((e=>(0,n.createElement)("span",{key:e.id,className:"feedlane-badge feedlane-badge--info mr-1"},e.name)))),(0,n.createElement)("td",null,(0,n.createElement)("span",{className:"font-medium"},e.votes)),(0,n.createElement)("td",null,new Date(e.date).toLocaleDateString()),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"feedlane-table__actions"},(0,n.createElement)("select",{value:e.status,onChange:a=>(async(e,a)=>{try{const t=new FormData;t.append("action","feedlane_update_idea_status"),t.append("nonce",feedlaneAdmin.nonce),t.append("idea_id",e),t.append("status",a);const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),l=await n.json();l.success?(te.success("Status updated successfully"),m()):te.error(l.data||"Failed to update status")}catch(e){te.error("Failed to update status")}})(e.id,a.target.value),className:"feedlane-select text-xs"},(0,n.createElement)("option",{value:"publish"},"Published"),(0,n.createElement)("option",{value:"draft"},"Draft"),(0,n.createElement)("option",{value:"pending"},"Pending"),(0,n.createElement)("option",{value:"private"},"Private")),(0,n.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this idea?"))try{const a=new FormData;a.append("action","feedlane_delete_idea"),a.append("nonce",feedlaneAdmin.nonce),a.append("idea_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(te.success("Idea deleted successfully"),m()):te.error(n.data||"Failed to delete idea")}catch(e){te.error("Failed to delete idea")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete"))))))))),c>1&&(0,n.createElement)("div",{className:"feedlane-pagination"},(0,n.createElement)("div",{className:"feedlane-pagination__info"},"Page ",s," of ",c),(0,n.createElement)("div",{className:"feedlane-pagination__nav"},(0,n.createElement)("button",{onClick:()=>r(s-1),disabled:1===s,className:"feedlane-pagination__btn"},"Previous"),(0,n.createElement)("button",{onClick:()=>r(s+1),disabled:s===c,className:"feedlane-pagination__btn"},"Next")))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,n.createElement)("h3",null,"No Ideas Found"),(0,n.createElement)("p",null,"No ideas have been submitted yet or match your current filter.")))))},ce=()=>{const[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)(!0),[s,r]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[o,i]=(0,n.useState)({name:"",slug:"",description:"",color:"#10B981"});(0,n.useEffect)((()=>{m()}),[]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_categories"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?a(n.data):te.error("Failed to load categories")}catch(e){te.error("Failed to load categories")}finally{l(!1)}},u=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_category"),e.append("nonce",feedlaneAdmin.nonce),c&&e.append("category_id",c.id),Object.keys(o).forEach((a=>{e.append(a,o[a])}));const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),t=await a.json();t.success?(te.success(c?"Category updated successfully":"Category created successfully"),r(!1),d(null),i({name:"",slug:"",description:"",color:"#10B981"}),m()):te.error(t.data||"Failed to save category")}catch(e){te.error("Failed to save category")}},f=e=>{const{name:a,value:t}=e.target;i((e=>({...e,[a]:t})))};return t?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Categories Management"),(0,n.createElement)("p",null,"Manage idea categories")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading categories..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Categories Management"),(0,n.createElement)("p",null,"Manage idea categories")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"mb-6"},(0,n.createElement)("button",{onClick:()=>{d(null),i({name:"",slug:"",description:"",color:"#10B981"}),r(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Category")),e.length>0?(0,n.createElement)("div",{className:"feedlane-card"},(0,n.createElement)("div",{className:"feedlane-card__content"},(0,n.createElement)("table",{className:"feedlane-table"},(0,n.createElement)("thead",null,(0,n.createElement)("tr",null,(0,n.createElement)("th",null,"Name"),(0,n.createElement)("th",null,"Slug"),(0,n.createElement)("th",null,"Description"),(0,n.createElement)("th",null,"Color"),(0,n.createElement)("th",null,"Ideas Count"),(0,n.createElement)("th",null,"Actions"))),(0,n.createElement)("tbody",null,e.map((e=>(0,n.createElement)("tr",{key:e.id},(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,n.createElement)("td",null,(0,n.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"flex items-center gap-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,n.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,n.createElement)("td",null,(0,n.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"feedlane-table__actions"},(0,n.createElement)("button",{onClick:()=>(e=>{d(e),i({name:e.name,slug:e.slug,description:e.description,color:e.color}),r(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,n.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this category?"))try{const a=new FormData;a.append("action","feedlane_delete_category"),a.append("nonce",feedlaneAdmin.nonce),a.append("category_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(te.success("Category deleted successfully"),m()):te.error(n.data||"Failed to delete category")}catch(e){te.error("Failed to delete category")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"🏷️"),(0,n.createElement)("h3",null,"No Categories Found"),(0,n.createElement)("p",null,"Create your first category to organize ideas.")),s&&(0,n.createElement)("div",{className:"feedlane-modal"},(0,n.createElement)("div",{className:"feedlane-modal__backdrop",onClick:()=>r(!1)}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content"},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,c?"Edit Category":"Add New Category"),(0,n.createElement)("button",{onClick:()=>r(!1),className:"feedlane-modal__close"},"✕")),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("form",{onSubmit:u,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-name"},"Name *"),(0,n.createElement)("input",{type:"text",id:"category-name",name:"name",value:o.name,onChange:f,className:"feedlane-input",required:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-slug"},"Slug"),(0,n.createElement)("input",{type:"text",id:"category-slug",name:"slug",value:o.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-description"},"Description"),(0,n.createElement)("textarea",{id:"category-description",name:"description",value:o.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-color"},"Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"category-color",name:"color",value:o.color,onChange:f,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:o.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#10B981"}))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:()=>r(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,n.createElement)("button",{onClick:u,className:"feedlane-btn feedlane-btn--primary"},c?"Update Category":"Create Category"))))))))},de=()=>{const[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)(!0),[s,r]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[o,i]=(0,n.useState)({name:"",slug:"",description:"",color:"#6B7280"});(0,n.useEffect)((()=>{m()}),[]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_status"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?a(n.data):te.error("Failed to load status")}catch(e){te.error("Failed to load status")}finally{l(!1)}},u=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_status"),e.append("nonce",feedlaneAdmin.nonce),c&&e.append("status_id",c.id),Object.keys(o).forEach((a=>{e.append(a,o[a])}));const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),t=await a.json();t.success?(te.success(c?"Status updated successfully":"Status created successfully"),r(!1),d(null),i({name:"",slug:"",description:"",color:"#6B7280"}),m()):te.error(t.data||"Failed to save status")}catch(e){te.error("Failed to save status")}},f=e=>{const{name:a,value:t}=e.target;i((e=>({...e,[a]:t})))};return t?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Status Management"),(0,n.createElement)("p",null,"Manage roadmap status")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading status..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Status Management"),(0,n.createElement)("p",null,"Manage roadmap status")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"mb-6"},(0,n.createElement)("button",{onClick:()=>{d(null),i({name:"",slug:"",description:"",color:"#6B7280"}),r(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Status")),e.length>0?(0,n.createElement)("div",{className:"feedlane-card"},(0,n.createElement)("div",{className:"feedlane-card__content"},(0,n.createElement)("table",{className:"feedlane-table"},(0,n.createElement)("thead",null,(0,n.createElement)("tr",null,(0,n.createElement)("th",null,"Name"),(0,n.createElement)("th",null,"Slug"),(0,n.createElement)("th",null,"Description"),(0,n.createElement)("th",null,"Color"),(0,n.createElement)("th",null,"Ideas Count"),(0,n.createElement)("th",null,"Actions"))),(0,n.createElement)("tbody",null,e.map((e=>(0,n.createElement)("tr",{key:e.id},(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,n.createElement)("td",null,(0,n.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"flex items-center gap-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,n.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,n.createElement)("td",null,(0,n.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"feedlane-table__actions"},(0,n.createElement)("button",{onClick:()=>(e=>{d(e),i({name:e.name,slug:e.slug,description:e.description,color:e.color}),r(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,n.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this status?"))try{const a=new FormData;a.append("action","feedlane_delete_status"),a.append("nonce",feedlaneAdmin.nonce),a.append("status_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(te.success("Status deleted successfully"),m()):te.error(n.data||"Failed to delete status")}catch(e){te.error("Failed to delete status")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"📊"),(0,n.createElement)("h3",null,"No Status Found"),(0,n.createElement)("p",null,"Create your first status to track idea progress.")),s&&(0,n.createElement)("div",{className:"feedlane-modal"},(0,n.createElement)("div",{className:"feedlane-modal__backdrop",onClick:()=>r(!1)}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content"},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,c?"Edit Status":"Add New Status"),(0,n.createElement)("button",{onClick:()=>r(!1),className:"feedlane-modal__close"},"✕")),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("form",{onSubmit:u,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-name"},"Name *"),(0,n.createElement)("input",{type:"text",id:"status-name",name:"name",value:o.name,onChange:f,className:"feedlane-input",required:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-slug"},"Slug"),(0,n.createElement)("input",{type:"text",id:"status-slug",name:"slug",value:o.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-description"},"Description"),(0,n.createElement)("textarea",{id:"status-description",name:"description",value:o.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-color"},"Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"status-color",name:"color",value:o.color,onChange:f,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:o.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#6B7280"}))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:()=>r(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,n.createElement)("button",{onClick:u,className:"feedlane-btn feedlane-btn--primary"},c?"Update Status":"Create Status"))))))))},oe=()=>{const[e,a]=(0,n.useState)([]),[t,l]=(0,n.useState)([]),[s,r]=(0,n.useState)(!0),[c,d]=(0,n.useState)(null);(0,n.useEffect)((()=>{o()}),[]);const o=async()=>{try{r(!0);const e=new FormData;e.append("action","feedlane_get_ideas"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success&&a(n.data);const s=new FormData;s.append("action","feedlane_get_statuses"),s.append("nonce",feedlaneAdmin.nonce);const c=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:s}),d=await c.json();d.success&&l(d.data)}catch(e){te.error("Failed to load roadmap data")}finally{r(!1)}},i=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},m=a=>e.filter((e=>e.status===a));return s?(0,n.createElement)("div",{className:"feedlane-admin__loading"},(0,n.createElement)("div",{className:"feedlane-spinner"}),(0,n.createElement)("p",null,"Loading roadmap...")):(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h2",null,"Roadmap Manager"),(0,n.createElement)("p",null,"Drag and drop ideas between status columns to update their roadmap status.")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-roadmap-board"},t.map((e=>{return(0,n.createElement)("div",{key:e.slug,className:"feedlane-roadmap-column",onDragOver:i,onDrop:a=>(async(e,a)=>{if(e.preventDefault(),c){try{const e=new FormData;e.append("action","feedlane_update_idea_status"),e.append("nonce",feedlaneAdmin.nonce),e.append("idea_id",c.id),e.append("status",a);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?(te.success("Idea status updated successfully"),o()):te.error(n.data||"Failed to update idea status")}catch(e){te.error("Failed to update idea status")}d(null)}})(a,e.slug)},(0,n.createElement)("div",{className:"feedlane-roadmap-column__header"},(0,n.createElement)("div",{className:"feedlane-roadmap-column__indicator",style:{backgroundColor:(a=e.slug,{"under-review":"#6B7280",planned:"#EF4444","in-progress":"#F59E0B",completed:"#10B981"}[a]||"#6B7280")}}),(0,n.createElement)("h3",null,e.name),(0,n.createElement)("span",{className:"feedlane-roadmap-column__count"},m(e.slug).length)),(0,n.createElement)("div",{className:"feedlane-roadmap-column__content"},m(e.slug).map((e=>(0,n.createElement)("div",{key:e.id,className:"feedlane-roadmap-card",draggable:!0,onDragStart:a=>((e,a)=>{d(a),e.dataTransfer.effectAllowed="move"})(a,e)},(0,n.createElement)("div",{className:"feedlane-roadmap-card__header"},(0,n.createElement)("h4",null,e.title),(0,n.createElement)("div",{className:"feedlane-roadmap-card__meta"},(0,n.createElement)("span",{className:"feedlane-roadmap-card__votes"},"👍 ",e.vote_count||0),(0,n.createElement)("span",{className:"feedlane-roadmap-card__comments"},"💬 ",e.comment_count||0))),e.excerpt&&(0,n.createElement)("p",{className:"feedlane-roadmap-card__excerpt"},e.excerpt),e.category&&(0,n.createElement)("div",{className:"feedlane-roadmap-card__category"},(0,n.createElement)("span",{className:"feedlane-badge",style:{backgroundColor:e.category_color||"#10B981"}},e.category))))),0===m(e.slug).length&&(0,n.createElement)("div",{className:"feedlane-roadmap-column__empty"},(0,n.createElement)("p",null,"No ideas in this status"))));var a}))),0===t.length&&(0,n.createElement)("div",{className:"feedlane-admin__empty"},(0,n.createElement)("h3",null,"No Status Found"),(0,n.createElement)("p",null,"Please create some roadmap statuses first in the Status Management page."))))},ie=({page:e})=>(0,n.createElement)(n.Fragment,null,(()=>{switch(e){case"dashboard":default:return(0,n.createElement)(ne,null);case"settings":return(0,n.createElement)(le,null);case"analytics":return(0,n.createElement)(se,null);case"ideas":return(0,n.createElement)(re,null);case"categories":return(0,n.createElement)(ce,null);case"roadmap":return(0,n.createElement)(oe,null);case"status":return(0,n.createElement)(de,null)}})(),(0,n.createElement)(ae,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{console.log("React available:",void 0!==l()),console.log("createRoot available:",void 0!==s.H),console.log("wp.element available:","undefined"!=typeof wp&&void 0!==wp.element);const e=document.getElementById("feedlane-admin-dashboard");if(e)try{(0,s.H)(e).render((0,n.createElement)(ie,{page:"dashboard"}))}catch(e){console.error("Error creating dashboard root:",e)}const a=document.getElementById("feedlane-admin-settings");a&&(0,s.H)(a).render((0,n.createElement)(ie,{page:"settings"}));const t=document.getElementById("feedlane-admin-analytics");t&&(0,s.H)(t).render((0,n.createElement)(ie,{page:"analytics"}));const r=document.getElementById("feedlane-admin-ideas");r&&(0,s.H)(r).render((0,n.createElement)(ie,{page:"ideas"}));const c=document.getElementById("feedlane-admin-categories");c&&(0,s.H)(c).render((0,n.createElement)(ie,{page:"categories"}));const d=document.getElementById("feedlane-admin-roadmap");d&&(0,s.H)(d).render((0,n.createElement)(ie,{page:"roadmap"}));const o=document.getElementById("feedlane-admin-status");o&&(0,s.H)(o).render((0,n.createElement)(ie,{page:"status"}))}))})();