(()=>{"use strict";var e={576:(e,t,a)=>{var n=a(795);t.H=n.createRoot,n.hydrateRoot},795:e=>{e.exports=window.ReactDOM}},t={};function a(n){var r=t[n];if(void 0!==r)return r.exports;var l=t[n]={exports:{}};return e[n](l,l.exports,a),l.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const n=window.React;var r=a.n(n),l=a(576);let o={data:""},s=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,i=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,u=(e,t)=>{let a="",n="",r="";for(let l in e){let o=e[l];"@"==l[0]?"i"==l[1]?a=l+" "+o+";":n+="f"==l[1]?u(o,l):l+"{"+u(o,"k"==l[1]?"":t)+"}":"object"==typeof o?n+=u(o,t?t.replace(/([^,])+/g,(e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):l):null!=o&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=u.p?u.p(l,o):l+":"+o+";")}return a+(t&&r?t+"{"+r+"}":r)+n},m={},f=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+f(e[a]);return t}return e},p=(e,t,a,n,r)=>{let l=f(e),o=m[l]||(m[l]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(l));if(!m[o]){let t=l!==e?e:(e=>{let t,a,n=[{}];for(;t=i.exec(e.replace(c,""));)t[4]?n.shift():t[3]?(a=t[3].replace(d," ").trim(),n.unshift(n[0][a]=n[0][a]||{})):n[0][t[1]]=t[2].replace(d," ").trim();return n[0]})(e);m[o]=u(r?{["@keyframes "+o]:t}:t,a?"":"."+o)}let s=a&&m.g?m.g:null;return a&&(m.g=m[o]),((e,t,a,n)=>{n?t.data=t.data.replace(n,e):-1===t.data.indexOf(e)&&(t.data=a?e+t.data:t.data+e)})(m[o],t,n,s),o};function g(e){let t=this||{},a=e.call?e(t.p):e;return p(a.unshift?a.raw?((e,t,a)=>e.reduce(((e,n,r)=>{let l=t[r];if(l&&l.call){let e=l(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+n+(null==l?"":l)}),""))(a,[].slice.call(arguments,1),t.p):a.reduce(((e,a)=>Object.assign(e,a&&a.call?a(t.p):a)),{}):a,s(t.target),t.g,t.o,t.k)}g.bind({g:1});let v,h,b,y=g.bind({k:1});function E(e,t){let a=this||{};return function(){let n=arguments;function r(l,o){let s=Object.assign({},l),i=s.className||r.className;a.p=Object.assign({theme:h&&h()},s),a.o=/ *go\d+/.test(i),s.className=g.apply(a,n)+(i?" "+i:""),t&&(s.ref=o);let c=e;return e[0]&&(c=s.as||e,delete s.as),b&&c[0]&&b(s),v(c,s)}return t?t(r):r}}var x=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,w=(()=>{let e=0;return()=>(++e).toString()})(),N=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),_=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===t.toast.id?{...e,...t.toast}:e))};case 2:let{toast:a}=t;return _(e,{type:e.toasts.find((e=>e.id===a.id))?1:0,toast:a});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map((e=>e.id===n||void 0===n?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==t.toastId))};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+r})))}}},C=[],k={toasts:[],pausedAt:void 0},S=e=>{k=_(k,e),C.forEach((e=>{e(k)}))},D={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},R=e=>(t,a)=>{let n=((e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||w()}))(t,e,a);return S({type:2,toast:n}),n.id},A=(e,t)=>R("blank")(e,t);A.error=R("error"),A.success=R("success"),A.loading=R("loading"),A.custom=R("custom"),A.dismiss=e=>{S({type:3,toastId:e})},A.remove=e=>S({type:4,toastId:e}),A.promise=(e,t,a)=>{let n=A.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let r=t.success?x(t.success,e):void 0;return r?A.success(r,{id:n,...a,...null==a?void 0:a.success}):A.dismiss(n),e})).catch((e=>{let r=t.error?x(t.error,e):void 0;r?A.error(r,{id:n,...a,...null==a?void 0:a.error}):A.dismiss(n)})),e};var O=(e,t)=>{S({type:1,toast:{id:e,height:t}})},F=()=>{S({type:5,time:Date.now()})},I=new Map,M=y`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,T=y`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,j=y`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,L=E("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${M} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${T} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${j} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,P=y`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,B=E("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${P} 1s linear infinite;
`,z=y`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,U=y`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,H=E("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${U} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,W=E("div")`
  position: absolute;
`,V=E("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=y`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Y=E("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,$=({toast:e})=>{let{icon:t,type:a,iconTheme:r}=e;return void 0!==t?"string"==typeof t?n.createElement(Y,null,t):t:"blank"===a?null:n.createElement(V,null,n.createElement(B,{...r}),"loading"!==a&&n.createElement(W,null,"error"===a?n.createElement(L,{...r}):n.createElement(H,{...r})))},q=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,K=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,J=E("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,G=E("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Q=n.memo((({toast:e,position:t,style:a,children:r})=>{let l=e.height?((e,t)=>{let a=e.includes("top")?1:-1,[n,r]=N()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[q(a),K(a)];return{animation:t?`${y(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${y(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||t||"top-center",e.visible):{opacity:0},o=n.createElement($,{toast:e}),s=n.createElement(G,{...e.ariaProps},x(e.message,e));return n.createElement(J,{className:e.className,style:{...l,...a,...e.style}},"function"==typeof r?r({icon:o,message:s}):n.createElement(n.Fragment,null,o,s))}));!function(e){u.p=void 0,v=e,h=void 0,b=void 0}(n.createElement);var Z=({id:e,className:t,style:a,onHeightUpdate:r,children:l})=>{let o=n.useCallback((t=>{if(t){let a=()=>{let a=t.getBoundingClientRect().height;r(e,a)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}}),[e,r]);return n.createElement("div",{ref:o,className:t,style:a},l)},ee=g`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,te=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:r,children:l,containerStyle:o,containerClassName:s})=>{let{toasts:i,handlers:c}=(e=>{let{toasts:t,pausedAt:a}=((e={})=>{let[t,a]=(0,n.useState)(k),r=(0,n.useRef)(k);(0,n.useEffect)((()=>(r.current!==k&&a(k),C.push(a),()=>{let e=C.indexOf(a);e>-1&&C.splice(e,1)})),[]);let l=t.toasts.map((t=>{var a,n,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(n=e[t.type])?void 0:n.duration)||(null==e?void 0:e.duration)||D[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}}));return{...t,toasts:l}})(e);(0,n.useEffect)((()=>{if(a)return;let e=Date.now(),n=t.map((t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(a<0))return setTimeout((()=>A.dismiss(t.id)),a);t.visible&&A.dismiss(t.id)}));return()=>{n.forEach((e=>e&&clearTimeout(e)))}}),[t,a]);let r=(0,n.useCallback)((()=>{a&&S({type:6,time:Date.now()})}),[a]),l=(0,n.useCallback)(((e,a)=>{let{reverseOrder:n=!1,gutter:r=8,defaultPosition:l}=a||{},o=t.filter((t=>(t.position||l)===(e.position||l)&&t.height)),s=o.findIndex((t=>t.id===e.id)),i=o.filter(((e,t)=>t<s&&e.visible)).length;return o.filter((e=>e.visible)).slice(...n?[i+1]:[0,i]).reduce(((e,t)=>e+(t.height||0)+r),0)}),[t]);return(0,n.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)((e,t=1e3)=>{if(I.has(e))return;let a=setTimeout((()=>{I.delete(e),S({type:4,toastId:e})}),t);I.set(e,a)})(e.id,e.removeDelay);else{let t=I.get(e.id);t&&(clearTimeout(t),I.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:O,startPause:F,endPause:r,calculateOffset:l}}})(a);return n.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:s,onMouseEnter:c.startPause,onMouseLeave:c.endPause},i.map((a=>{let o=a.position||t,s=((e,t)=>{let a=e.includes("top"),n=a?{top:0}:{bottom:0},r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:N()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...n,...r}})(o,c.calculateOffset(a,{reverseOrder:e,gutter:r,defaultPosition:t}));return n.createElement(Z,{id:a.id,key:a.id,onHeightUpdate:c.updateHeight,className:a.visible?ee:"",style:s},"custom"===a.type?x(a.message,a):l?l(a):n.createElement(Q,{toast:a,position:o}))})))},ae=A;const ne=()=>(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Dashboard"),(0,n.createElement)("p",null,"Overview of your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-admin__grid"},(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},12),(0,n.createElement)("div",{className:"stat-label"},"Newsfeed Posts"),(0,n.createElement)("p",null,"Published announcements and updates")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},45),(0,n.createElement)("div",{className:"stat-label"},"Total Ideas"),(0,n.createElement)("p",null,"Ideas submitted by users")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},8),(0,n.createElement)("div",{className:"stat-label"},"Pending Ideas"),(0,n.createElement)("p",null,"Ideas awaiting approval")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},156),(0,n.createElement)("div",{className:"stat-label"},"Feedback Comments"),(0,n.createElement)("p",null,"User feedback on posts")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},234),(0,n.createElement)("div",{className:"stat-label"},"Total Votes"),(0,n.createElement)("p",null,"Votes cast on ideas")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("h3",null,"Quick Actions"),(0,n.createElement)("div",{className:"space-y-2"},(0,n.createElement)("a",{href:"post-new.php?post_type=feedlane_posts",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center"},"Add Newsfeed Post"),(0,n.createElement)("a",{href:"admin.php?page=feedlane-ideas",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Manage Ideas"),(0,n.createElement)("a",{href:"admin.php?page=feedlane-settings",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Settings"))))))),re=()=>{const[e,t]=(0,n.useState)({feedlane_enable_newsfeed:!0,feedlane_enable_ideas:!0,feedlane_enable_roadmap:!0,feedlane_enable_guest_submissions:!0,feedlane_enable_floating_sidebar:!0,feedlane_sidebar_position:"left",feedlane_primary_color:"#0ea5e9",feedlane_firebase_config:"",feedlane_firebase_webhook_secret:""}),[a,r]=(0,n.useState)(!1),[l,o]=(0,n.useState)(!0);(0,n.useEffect)((()=>{s()}),[]);const s=async()=>{try{const e=new FormData;e.append("action","feedlane_get_settings"),e.append("nonce",feedlaneAdmin.nonce);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e});if(a.ok){const e=await a.json();e.success&&t(e.data)}}catch(e){console.error("Failed to load settings:",e)}finally{o(!1)}},i=e=>{const{name:a,value:n,type:r,checked:l}=e.target;t((e=>({...e,[a]:"checkbox"===r?l:n})))};return l?(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Settings"),(0,n.createElement)("p",null,"Configure your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading settings...")))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Feedlane Settings"),(0,n.createElement)("p",null,"Configure your feedback system")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("form",{onSubmit:async t=>{t.preventDefault(),r(!0);try{const t=new FormData;t.append("action","feedlane_save_settings"),t.append("nonce",feedlaneAdmin.nonce),Object.keys(e).forEach((a=>{t.append(a,e[a])}));const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),n=await a.json();n.success?ae.success("Settings saved successfully!"):ae.error(n.data||"Failed to save settings")}catch(e){ae.error("Failed to save settings")}finally{r(!1)}},className:"feedlane-form"},(0,n.createElement)("div",{className:"feedlane-form__section"},(0,n.createElement)("h3",null,"General Settings"),(0,n.createElement)("p",null,"Configure which tabs are enabled and basic appearance"),(0,n.createElement)("div",{className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_newsfeed",checked:e.feedlane_enable_newsfeed,onChange:i,className:"mr-2"}),"Enable Newsfeed Tab"),(0,n.createElement)("div",{className:"description"},"Show the newsfeed tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_ideas",checked:e.feedlane_enable_ideas,onChange:i,className:"mr-2"}),"Enable Ideas Tab"),(0,n.createElement)("div",{className:"description"},"Show the ideas submission tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_roadmap",checked:e.feedlane_enable_roadmap,onChange:i,className:"mr-2"}),"Enable Roadmap Tab"),(0,n.createElement)("div",{className:"description"},"Show the roadmap tab in the sidebar")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_guest_submissions",checked:e.feedlane_enable_guest_submissions,onChange:i,className:"mr-2"}),"Enable Guest Submissions"),(0,n.createElement)("div",{className:"description"},"Allow non-logged-in users to submit feedback and ideas")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,(0,n.createElement)("input",{type:"checkbox",name:"feedlane_enable_floating_sidebar",checked:e.feedlane_enable_floating_sidebar,onChange:i,className:"mr-2"}),"Enable Floating Sidebar"),(0,n.createElement)("div",{className:"description"},"Show the floating feedback sidebar on all pages")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_sidebar_position"},"Sidebar Position"),(0,n.createElement)("select",{id:"feedlane_sidebar_position",name:"feedlane_sidebar_position",value:e.feedlane_sidebar_position,onChange:i,className:"feedlane-select"},(0,n.createElement)("option",{value:"left"},"Left"),(0,n.createElement)("option",{value:"right"},"Right")),(0,n.createElement)("div",{className:"description"},"Choose which side of the screen the sidebar appears on")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_primary_color"},"Primary Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"feedlane_primary_color",name:"feedlane_primary_color",value:e.feedlane_primary_color,onChange:i,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:e.feedlane_primary_color,onChange:i,name:"feedlane_primary_color",className:"feedlane-color-text",placeholder:"#0ea5e9"})),(0,n.createElement)("div",{className:"description"},"Choose the primary color for the sidebar and buttons")))),(0,n.createElement)("div",{className:"feedlane-form__section"},(0,n.createElement)("h3",null,"Firebase Configuration"),(0,n.createElement)("p",null,"Configure Firebase for real-time comments functionality"),(0,n.createElement)("div",{className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_firebase_config"},"Firebase Configuration JSON"),(0,n.createElement)("textarea",{id:"feedlane_firebase_config",name:"feedlane_firebase_config",value:e.feedlane_firebase_config,onChange:i,rows:"6",className:"feedlane-textarea",placeholder:'{"apiKey": "...", "authDomain": "...", "projectId": "..."}'}),(0,n.createElement)("div",{className:"description"},"Paste your Firebase configuration JSON here for real-time comments")),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"feedlane_firebase_webhook_secret"},"Webhook Secret"),(0,n.createElement)("input",{type:"password",id:"feedlane_firebase_webhook_secret",name:"feedlane_firebase_webhook_secret",value:e.feedlane_firebase_webhook_secret,onChange:i,className:"feedlane-input"}),(0,n.createElement)("div",{className:"description"},"Secret key for Firebase webhook authentication")))),(0,n.createElement)("div",{className:"feedlane-form__actions"},(0,n.createElement)("button",{type:"submit",disabled:a,className:"feedlane-btn feedlane-btn--primary"},a?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Saving..."):"Save Settings"))))))},le=()=>{const e={totalViews:1234,totalReactions:567,totalVotes:234,topPosts:[{id:1,title:"New Feature Release",views:234,reactions:45},{id:2,title:"Bug Fix Update",views:189,reactions:32},{id:3,title:"Roadmap Update",views:156,reactions:28}],topIdeas:[{id:1,title:"Dark Mode Support",votes:89,category:"Feature Request"},{id:2,title:"Mobile App",votes:67,category:"Feature Request"},{id:3,title:"Better Search",votes:45,category:"Improvement"}]};return(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Analytics"),(0,n.createElement)("p",null,"Track engagement and performance metrics")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-admin__grid"},(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalViews),(0,n.createElement)("div",{className:"stat-label"},"Total Views"),(0,n.createElement)("p",null,"Post and idea views")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalReactions),(0,n.createElement)("div",{className:"stat-label"},"Total Reactions"),(0,n.createElement)("p",null,"Emoji reactions on posts")),(0,n.createElement)("div",{className:"feedlane-admin__card"},(0,n.createElement)("div",{className:"stat-number"},e.totalVotes),(0,n.createElement)("div",{className:"stat-label"},"Total Votes"),(0,n.createElement)("p",null,"Votes on ideas"))),(0,n.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"},(0,n.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,n.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Posts"),(0,n.createElement)("div",{className:"space-y-3"},e.topPosts.map((e=>(0,n.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,n.createElement)("div",null,(0,n.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,n.createElement)("p",{className:"text-sm text-gray-500"},e.views," views • ",e.reactions," reactions"))))))),(0,n.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,n.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Ideas"),(0,n.createElement)("div",{className:"space-y-3"},e.topIdeas.map((e=>(0,n.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,n.createElement)("div",null,(0,n.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,n.createElement)("p",{className:"text-sm text-gray-500"},e.category," • ",e.votes," votes")))))))))))};var oe={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},se=r().createContext&&r().createContext(oe),ie=function(){return ie=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++)for(var r in t=arguments[a])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},ie.apply(this,arguments)};function ce(e){return e&&e.map((function(e,t){return r().createElement(e.tag,ie({key:t},e.attr),ce(e.child))}))}function de(e){return function(t){return r().createElement(ue,ie({attr:ie({},e.attr)},t),ce(e.child))}}function ue(e){var t=function(t){var a,n=e.attr,l=e.size,o=e.title,s=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]])}return a}(e,["attr","size","title"]),i=l||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),r().createElement("svg",ie({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,s,{className:a,style:ie(ie({color:e.color||t.color},t.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),o&&r().createElement("title",null,o),e.children)};return void 0!==se?r().createElement(se.Consumer,null,(function(e){return t(e)})):t(oe)}function me(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"18 15 12 9 6 15"}}]})(e)}function fe(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}},{tag:"path",attr:{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}}]})(e)}function pe(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}},{tag:"line",attr:{x1:"1",y1:"1",x2:"23",y2:"23"}}]})(e)}function ge(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}}]})(e)}function ve(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"}}]})(e)}function he(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"1"}},{tag:"circle",attr:{cx:"19",cy:"12",r:"1"}},{tag:"circle",attr:{cx:"5",cy:"12",r:"1"}}]})(e)}function be(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(e)}function ye(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}},{tag:"polyline",attr:{points:"17 21 17 13 7 13 7 21"}},{tag:"polyline",attr:{points:"7 3 7 8 15 8"}}]})(e)}function Ee(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}}]})(e)}function xe(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"22",y1:"2",x2:"11",y2:"13"}},{tag:"polygon",attr:{points:"22 2 15 22 11 13 2 9 22 2"}}]})(e)}function we(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"3 6 5 6 21 6"}},{tag:"path",attr:{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}},{tag:"line",attr:{x1:"10",y1:"11",x2:"10",y2:"17"}},{tag:"line",attr:{x1:"14",y1:"11",x2:"14",y2:"17"}}]})(e)}function Ne(e){return de({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}const _e=({isOpen:e,onClose:t,onSave:a,idea:r,categories:l,statuses:o})=>{const[s,i]=(0,n.useState)({title:"",content:"",status:"",categories:[],visibility:"public"}),[c,d]=(0,n.useState)([]),[u,m]=(0,n.useState)(""),[f,p]=(0,n.useState)(!1),[g,v]=(0,n.useState)(!1);(0,n.useEffect)((()=>{e&&(r?(i({title:r.title||"",content:r.content||"",status:r.status||"",categories:r.categories?r.categories.map((e=>e.slug)):[],visibility:r.visibility||"public"}),h(r.id)):(i({title:"",content:"",status:o[0]?.slug||"",categories:[],visibility:"public"}),d([])))}),[e,r,o]),(0,n.useEffect)((()=>{const a=a=>{"Escape"===a.key&&e&&t()};return e&&(document.addEventListener("keydown",a),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",a),document.body.style.overflow="unset"}}),[e,t]);const h=async e=>{try{v(!0);const t=await fetch(`${feedlaneAdmin.rest_url}wp/v2/comments?post=${e}&per_page=100`,{headers:{"X-WP-Nonce":feedlaneAdmin.nonce}});if(t.ok){const e=await t.json();d(e)}}catch(e){console.error("Error loading comments:",e)}finally{v(!1)}},b=e=>{const{name:t,value:a,type:n,checked:r}=e.target;if("categories"===t){const e=a;i((t=>({...t,categories:r?[...t.categories,e]:t.categories.filter((t=>t!==e))})))}else i((e=>({...e,[t]:"checkbox"===n?r:a})))},y=async e=>{if(e.preventDefault(),s.title.trim())if(s.content.trim())try{p(!0);const e={title:s.title,content:s.content,status:s.status,categories:s.categories,visibility:s.visibility};if(r){const t=new FormData;t.append("action","feedlane_update_idea"),t.append("nonce",feedlaneAdmin.nonce),t.append("idea_id",r.id),Object.keys(e).forEach((a=>{"categories"===a?t.append(a,JSON.stringify(e[a])):t.append(a,e[a])}));const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),l=await n.json();if(!l.success)throw new Error(l.data||"Failed to update idea");ae.success("Idea updated successfully"),a()}else{const t=new FormData;t.append("action","feedlane_create_idea"),t.append("nonce",feedlaneAdmin.nonce),Object.keys(e).forEach((a=>{"categories"===a?t.append(a,JSON.stringify(e[a])):t.append(a,e[a])}));const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),r=await n.json();if(!r.success)throw new Error(r.data||"Failed to create idea");ae.success("Idea created successfully"),a()}}catch(e){console.error("Error saving idea:",e),ae.error(e.message||"Failed to save idea")}finally{p(!1)}else ae.error("Description is required");else ae.error("Title is required")};return e?(0,n.createElement)("div",{className:"feedlane-modal",onClick:t},(0,n.createElement)("div",{className:"feedlane-modal__backdrop"}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content max-w-4xl",onClick:e=>e.stopPropagation()},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,r?"Edit Idea":"Create New Idea"),(0,n.createElement)("button",{type:"button",onClick:t,className:"feedlane-modal__close","aria-label":"Close modal"},(0,n.createElement)(Ne,{className:"w-5 h-5"}))),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6"},(0,n.createElement)("div",{className:"lg:col-span-2"},(0,n.createElement)("form",{onSubmit:y,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-title"},"Title *"),(0,n.createElement)("input",{type:"text",id:"idea-title",name:"title",value:s.title,onChange:b,className:"feedlane-input",placeholder:"Enter idea title...",required:!0,autoFocus:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-content"},"Description *"),(0,n.createElement)("textarea",{id:"idea-content",name:"content",value:s.content,onChange:b,className:"feedlane-textarea",rows:"6",placeholder:"Describe your idea in detail...",required:!0})),(0,n.createElement)("div",{className:"grid grid-cols-2 gap-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-status"},"Status"),(0,n.createElement)("select",{id:"idea-status",name:"status",value:s.status,onChange:b,className:"feedlane-select"},o.map((e=>(0,n.createElement)("option",{key:e.slug,value:e.slug},e.name))))),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-visibility"},"Visibility"),(0,n.createElement)("select",{id:"idea-visibility",name:"visibility",value:s.visibility,onChange:b,className:"feedlane-select"},(0,n.createElement)("option",{value:"public"},"Public"),(0,n.createElement)("option",{value:"private"},"Private")))),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",null,"Categories"),(0,n.createElement)("div",{className:"grid grid-cols-2 gap-2 mt-2"},l.map((e=>(0,n.createElement)("label",{key:e.slug,className:"flex items-center gap-2"},(0,n.createElement)("input",{type:"checkbox",name:"categories",value:e.slug,checked:s.categories.includes(e.slug),onChange:b,className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.createElement)("span",{className:"text-sm"},e.name)))))))),(0,n.createElement)("div",{className:"lg:col-span-1"},(0,n.createElement)("div",{className:"bg-gray-50 rounded-lg p-4"},(0,n.createElement)("h4",{className:"font-semibold text-gray-900 mb-4 flex items-center gap-2"},(0,n.createElement)(ve,{className:"w-4 h-4"}),"Comments (",c.length,")"),(0,n.createElement)("div",{className:"space-y-3 mb-4 max-h-64 overflow-y-auto"},g?(0,n.createElement)("div",{className:"text-center py-4"},(0,n.createElement)("div",{className:"feedlane-loading__spinner w-4 h-4 mx-auto"})):c.length>0?c.map((e=>{return(0,n.createElement)("div",{key:e.id,className:"bg-white rounded-md p-3 text-sm"},(0,n.createElement)("div",{className:"flex items-center gap-2 mb-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium"},(a=e.author_name)?a.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"?"),(0,n.createElement)("span",{className:"font-medium text-gray-900"},e.author_name),(0,n.createElement)("span",{className:"text-gray-500 text-xs"},(t=e.date,new Date(t).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit"})))),(0,n.createElement)("div",{className:"text-gray-700",dangerouslySetInnerHTML:{__html:e.content.rendered}}));var t,a})):(0,n.createElement)("p",{className:"text-gray-500 text-sm text-center py-4"},"No comments yet")),r&&(0,n.createElement)("form",{onSubmit:async e=>{if(e.preventDefault(),u.trim())if(r)try{const e=await fetch(`${feedlaneAdmin.rest_url}wp/v2/comments`,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":feedlaneAdmin.nonce},body:JSON.stringify({post:r.id,content:u,status:"approved"})});if(!e.ok)throw new Error("Failed to add comment");{const t=await e.json();d((e=>[...e,t])),m(""),ae.success("Comment added successfully")}}catch(e){console.error("Error adding comment:",e),ae.error("Failed to add comment")}else ae.error("Save the idea first before adding comments");else ae.error("Comment cannot be empty")},className:"space-y-2"},(0,n.createElement)("textarea",{value:u,onChange:e=>m(e.target.value),placeholder:"Add a comment...",className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",rows:"3"}),(0,n.createElement)("button",{type:"submit",className:"w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700 flex items-center justify-center gap-2"},(0,n.createElement)(xe,{className:"w-4 h-4"}),"Add Comment")))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:t,className:"feedlane-btn feedlane-btn--secondary",disabled:f},"Cancel"),(0,n.createElement)("button",{type:"button",onClick:y,className:"feedlane-btn feedlane-btn--primary",disabled:f},f?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small mr-2"}),r?"Updating...":"Creating..."):(0,n.createElement)(n.Fragment,null,(0,n.createElement)(ye,{className:"w-4 h-4 mr-2"}),r?"Update Idea":"Create Idea")))))):null},Ce=e=>{if(!e)return"";const t=document.createElement("textarea");return t.innerHTML=e,t.value},ke=e=>{if(!e)return"";let t=Ce(e);return t=t.replace(/<[^>]*>/g,""),t=t.replace(/\s+/g," ").trim(),t},Se=(e,t=100)=>{const a=(e=>{if(!e)return"";let t=e.replace(/<!-- wp:[\s\S]*? -->/g,"");return t=t.replace(/<!-- \/wp:[\s\S]*? -->/g,""),t=t.replace(/<[^>]*>/g,""),t=Ce(t),t=t.replace(/\s+/g," ").trim(),t})(e);if(!a)return"No content available";if(a.length<=t)return a;const n=a.substring(0,t),r=n.lastIndexOf(" ");return r>0?n.substring(0,r)+"...":n+"..."},De=({idea:e,onEdit:t,onDelete:a,onToggleVisibility:r,getAuthorInitials:l,getStatusInfo:o,getCategoryInfo:s,formatDate:i})=>{const[c,d]=(0,n.useState)(!1),u=o(e.status);return(0,n.createElement)("tr",{className:"hover:bg-gray-50"},(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("button",{onClick:()=>r(e.id,e.visibility),className:"text-gray-400 hover:text-gray-600",title:"public"===e.visibility?"Make private":"Make public"},"public"===e.visibility?(0,n.createElement)(ge,{className:"w-4 h-4"}):(0,n.createElement)(pe,{className:"w-4 h-4"}))),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("div",{className:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600"},l(e.author_name))),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("button",{onClick:()=>t(e),className:"text-blue-600 hover:underline font-medium text-left"},ke(e.title)),e.content&&(0,n.createElement)("div",{className:"text-xs text-gray-500 mt-1"},Se(e.content,60))),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",style:{backgroundColor:u.color+"20",color:u.color}},(0,n.createElement)("span",{className:"w-2 h-2 rounded-full",style:{backgroundColor:u.color}}),u.name)),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("div",{className:"flex items-center gap-1"},(0,n.createElement)(me,{className:"w-4 h-4 text-gray-400"}),(0,n.createElement)("span",{className:"font-medium"},e.vote_count||0))),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("div",{className:"flex items-center gap-1"},(0,n.createElement)(ve,{className:"w-4 h-4 text-gray-400"}),(0,n.createElement)("span",null,e.comment_count||0))),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("div",{className:"flex flex-wrap gap-1"},e.categories&&e.categories.length>0?e.categories.slice(0,2).map((e=>{const t=s(e.slug);return(0,n.createElement)("span",{key:e.id,className:"inline-block px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700"},t.name)})):(0,n.createElement)("span",{className:"text-xs text-gray-400"},"No categories"),e.categories&&e.categories.length>2&&(0,n.createElement)("span",{className:"text-xs text-gray-400"},"+",e.categories.length-2))),(0,n.createElement)("td",{className:"px-4 py-3 text-gray-500"},i(e.date)),(0,n.createElement)("td",{className:"px-4 py-3"},(0,n.createElement)("div",{className:"relative"},(0,n.createElement)("button",{onClick:()=>d(!c),className:"text-gray-400 hover:text-gray-600 p-1"},(0,n.createElement)(he,{className:"w-4 h-4"})),c&&(0,n.createElement)("div",{className:"absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]"},(0,n.createElement)("button",{onClick:()=>{t(e),d(!1)},className:"flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"},(0,n.createElement)(fe,{className:"w-4 h-4"}),"Edit"),(0,n.createElement)("button",{onClick:()=>{a(e.id),d(!1)},className:"flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"},(0,n.createElement)(we,{className:"w-4 h-4"}),"Delete")))))},Re=()=>{const[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)([]),[l,o]=(0,n.useState)([]),[s,i]=(0,n.useState)(!0),[c,d]=(0,n.useState)(""),[u,m]=(0,n.useState)("date"),[f,p]=(0,n.useState)(!1),[g,v]=(0,n.useState)(null),[h,b]=(0,n.useState)(1),[y,E]=(0,n.useState)(1);(0,n.useEffect)((()=>{x()}),[h,c,u]);const x=async()=>{try{i(!0),await Promise.all([w(),N(),_()])}catch(e){ae.error("Failed to load data")}finally{i(!1)}},w=async()=>{try{const e=new FormData;e.append("action","feedlane_get_ideas"),e.append("nonce",feedlaneAdmin.nonce),e.append("page",h),e.append("search",c),e.append("orderby",u);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await a.json();if(!n.success)throw new Error(n.data||"Failed to load ideas");t(n.data.ideas||[]),E(n.data.pages||1)}catch(e){throw console.error("Error loading ideas:",e),e}},N=async()=>{try{const e=new FormData;e.append("action","feedlane_get_categories"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();a.success&&r(a.data)}catch(e){console.error("Error loading categories:",e)}},_=async()=>{try{const e=new FormData;e.append("action","feedlane_get_status"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();a.success&&o(a.data)}catch(e){console.error("Error loading status:",e)}},C=e=>{v(e),p(!0)},k=async e=>{if(confirm("Are you sure you want to delete this idea?"))try{const t=new FormData;t.append("action","feedlane_delete_idea"),t.append("nonce",feedlaneAdmin.nonce),t.append("idea_id",e);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),n=await a.json();n.success?(ae.success("Idea deleted successfully"),w()):ae.error(n.data||"Failed to delete idea")}catch(e){ae.error("Failed to delete idea")}},S=async(e,t)=>{try{const a=new FormData;a.append("action","feedlane_toggle_idea_visibility"),a.append("nonce",feedlaneAdmin.nonce),a.append("idea_id",e),a.append("visibility","public"===t?"private":"public");const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),r=await n.json();r.success?(ae.success("Visibility updated"),w()):ae.error(r.data||"Failed to update visibility")}catch(e){ae.error("Failed to update visibility")}},D=e=>e?e.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"?",R=e=>{const t=l.find((t=>t.slug===e));return t||{name:"Unknown",color:"#6B7280"}},A=e=>{const t=a.find((t=>t.slug===e));return t||{name:"Uncategorized",color:"#6B7280"}},O=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"2-digit",year:"2-digit"});return s?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading ideas...")))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"All Ideas"),(0,n.createElement)("p",null,"Manage and moderate submitted ideas")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"flex items-center justify-between mb-6"},(0,n.createElement)("div",{className:"flex items-center gap-4"},(0,n.createElement)("div",{className:"relative"},(0,n.createElement)(Ee,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,n.createElement)("input",{type:"text",placeholder:"Search your ideas",value:c,onChange:e=>d(e.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"})),(0,n.createElement)("select",{value:u,onChange:e=>m(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},(0,n.createElement)("option",{value:"date"},"Sort by Date"),(0,n.createElement)("option",{value:"title"},"Sort by Title"),(0,n.createElement)("option",{value:"votes"},"Sort by Votes"),(0,n.createElement)("option",{value:"comments"},"Sort by Comments"))),(0,n.createElement)("button",{onClick:()=>{v(null),p(!0)},className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 font-medium"},(0,n.createElement)(be,{className:"w-4 h-4"}),"CREATE IDEA")),(0,n.createElement)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden"},(0,n.createElement)("table",{className:"w-full text-sm"},(0,n.createElement)("thead",{className:"bg-gray-50 border-b border-gray-200"},(0,n.createElement)("tr",null,(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-12"}),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-16"},"BY"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700"},"TITLE"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700"},"STATUS"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-20"},"VOTES"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-24"},"COMMENTS"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700"},"CATEGORIES"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-24"},"DATE"),(0,n.createElement)("th",{className:"px-4 py-3 text-left font-semibold text-gray-700 w-20"}))),(0,n.createElement)("tbody",{className:"divide-y divide-gray-100"},e.map((e=>(0,n.createElement)(De,{key:e.id,idea:e,onEdit:C,onDelete:k,onToggleVisibility:S,getAuthorInitials:D,getStatusInfo:R,getCategoryInfo:A,formatDate:O}))))),0===e.length&&(0,n.createElement)("div",{className:"text-center py-12"},(0,n.createElement)("div",{className:"text-gray-400 text-lg mb-2"},"💡"),(0,n.createElement)("h3",{className:"text-lg font-medium text-gray-900 mb-1"},"No ideas found"),(0,n.createElement)("p",{className:"text-gray-500"},"Get started by creating your first idea."))),y>1&&(0,n.createElement)("div",{className:"flex items-center justify-between mt-6"},(0,n.createElement)("div",{className:"text-sm text-gray-700"},"Page ",h," of ",y),(0,n.createElement)("div",{className:"flex gap-2"},(0,n.createElement)("button",{onClick:()=>b(h-1),disabled:1===h,className:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},"Previous"),(0,n.createElement)("button",{onClick:()=>b(h+1),disabled:h===y,className:"px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},"Next"))),f&&(0,n.createElement)(_e,{isOpen:f,onClose:()=>{p(!1),v(null)},onSave:()=>{p(!1),v(null),w()},idea:g,categories:a,statuses:l}))))},Ae=()=>{const[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)(!0),[l,o]=(0,n.useState)(!1),[s,i]=(0,n.useState)(null),[c,d]=(0,n.useState)({name:"",slug:"",description:"",color:"#10B981"});(0,n.useEffect)((()=>{u()}),[]),(0,n.useEffect)((()=>{const e=e=>{"Escape"===e.key&&l&&o(!1)};return l&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}}),[l]);const u=async()=>{try{r(!0);const e=new FormData;e.append("action","feedlane_get_categories"),e.append("nonce",feedlaneAdmin.nonce);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await a.json();n.success?t(n.data):ae.error("Failed to load categories")}catch(e){ae.error("Failed to load categories")}finally{r(!1)}},m=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_category"),e.append("nonce",feedlaneAdmin.nonce),s&&e.append("category_id",s.id),Object.keys(c).forEach((t=>{e.append(t,c[t])}));const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();a.success?(ae.success(s?"Category updated successfully":"Category created successfully"),o(!1),i(null),d({name:"",slug:"",description:"",color:"#10B981"}),u()):ae.error(a.data||"Failed to save category")}catch(e){ae.error("Failed to save category")}},f=e=>{const{name:t,value:a}=e.target;d((e=>({...e,[t]:a})))};return a?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Categories Management"),(0,n.createElement)("p",null,"Manage idea categories")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading categories..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Categories Management"),(0,n.createElement)("p",null,"Manage idea categories")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"mb-6"},(0,n.createElement)("button",{onClick:()=>{i(null),d({name:"",slug:"",description:"",color:"#10B981"}),o(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Category")),e.length>0?(0,n.createElement)("div",{className:"feedlane-card"},(0,n.createElement)("div",{className:"feedlane-card__content"},(0,n.createElement)("table",{className:"feedlane-table"},(0,n.createElement)("thead",null,(0,n.createElement)("tr",null,(0,n.createElement)("th",null,"Name"),(0,n.createElement)("th",null,"Slug"),(0,n.createElement)("th",null,"Description"),(0,n.createElement)("th",null,"Color"),(0,n.createElement)("th",null,"Ideas Count"),(0,n.createElement)("th",null,"Actions"))),(0,n.createElement)("tbody",null,e.map((e=>(0,n.createElement)("tr",{key:e.id},(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,n.createElement)("td",null,(0,n.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"flex items-center gap-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,n.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,n.createElement)("td",null,(0,n.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"feedlane-table__actions"},(0,n.createElement)("button",{onClick:()=>(e=>{i(e),d({name:e.name,slug:e.slug,description:e.description,color:e.color}),o(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,n.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this category?"))try{const t=new FormData;t.append("action","feedlane_delete_category"),t.append("nonce",feedlaneAdmin.nonce),t.append("category_id",e);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),n=await a.json();n.success?(ae.success("Category deleted successfully"),u()):ae.error(n.data||"Failed to delete category")}catch(e){ae.error("Failed to delete category")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"🏷️"),(0,n.createElement)("h3",null,"No Categories Found"),(0,n.createElement)("p",null,"Create your first category to organize ideas.")),l&&(0,n.createElement)("div",{className:"feedlane-modal",onClick:()=>o(!1)},(0,n.createElement)("div",{className:"feedlane-modal__backdrop"}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content",onClick:e=>e.stopPropagation()},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,s?"Edit Category":"Add New Category"),(0,n.createElement)("button",{type:"button",onClick:()=>o(!1),className:"feedlane-modal__close","aria-label":"Close modal"},"✕")),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("form",{onSubmit:m,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-name"},"Name *"),(0,n.createElement)("input",{type:"text",id:"category-name",name:"name",value:c.name,onChange:f,className:"feedlane-input",required:!0,autoFocus:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-slug"},"Slug"),(0,n.createElement)("input",{type:"text",id:"category-slug",name:"slug",value:c.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-description"},"Description"),(0,n.createElement)("textarea",{id:"category-description",name:"description",value:c.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"category-color"},"Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"category-color",name:"color",value:c.color,onChange:f,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:c.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#10B981"}))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:()=>o(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,n.createElement)("button",{type:"button",onClick:m,className:"feedlane-btn feedlane-btn--primary"},s?"Update Category":"Create Category"))))))))},Oe=()=>{const[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)(!0),[l,o]=(0,n.useState)(!1),[s,i]=(0,n.useState)(null),[c,d]=(0,n.useState)({name:"",slug:"",description:"",color:"#6B7280"});(0,n.useEffect)((()=>{u()}),[]),(0,n.useEffect)((()=>{const e=e=>{"Escape"===e.key&&l&&o(!1)};return l&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}}),[l]);const u=async()=>{try{r(!0);const e=new FormData;e.append("action","feedlane_get_status"),e.append("nonce",feedlaneAdmin.nonce);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await a.json();n.success?t(n.data):ae.error("Failed to load status")}catch(e){ae.error("Failed to load status")}finally{r(!1)}},m=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_status"),e.append("nonce",feedlaneAdmin.nonce),s&&e.append("status_id",s.id),Object.keys(c).forEach((t=>{e.append(t,c[t])}));const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();a.success?(ae.success(s?"Status updated successfully":"Status created successfully"),o(!1),i(null),d({name:"",slug:"",description:"",color:"#6B7280"}),u()):ae.error(a.data||"Failed to save status")}catch(e){ae.error("Failed to save status")}},f=e=>{const{name:t,value:a}=e.target;d((e=>({...e,[t]:a})))};return a?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Status Management"),(0,n.createElement)("p",null,"Manage roadmap status")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading status..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Status Management"),(0,n.createElement)("p",null,"Manage roadmap status")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"mb-6"},(0,n.createElement)("button",{onClick:()=>{i(null),d({name:"",slug:"",description:"",color:"#6B7280"}),o(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Status")),e.length>0?(0,n.createElement)("div",{className:"feedlane-card"},(0,n.createElement)("div",{className:"feedlane-card__content"},(0,n.createElement)("table",{className:"feedlane-table"},(0,n.createElement)("thead",null,(0,n.createElement)("tr",null,(0,n.createElement)("th",null,"Name"),(0,n.createElement)("th",null,"Slug"),(0,n.createElement)("th",null,"Description"),(0,n.createElement)("th",null,"Color"),(0,n.createElement)("th",null,"Ideas Count"),(0,n.createElement)("th",null,"Actions"))),(0,n.createElement)("tbody",null,e.map((e=>(0,n.createElement)("tr",{key:e.id},(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,n.createElement)("td",null,(0,n.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"flex items-center gap-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,n.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,n.createElement)("td",null,(0,n.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,n.createElement)("td",null,(0,n.createElement)("div",{className:"feedlane-table__actions"},(0,n.createElement)("button",{onClick:()=>(e=>{i(e),d({name:e.name,slug:e.slug,description:e.description,color:e.color}),o(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,n.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this status?"))try{const t=new FormData;t.append("action","feedlane_delete_status"),t.append("nonce",feedlaneAdmin.nonce),t.append("status_id",e);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),n=await a.json();n.success?(ae.success("Status deleted successfully"),u()):ae.error(n.data||"Failed to delete status")}catch(e){ae.error("Failed to delete status")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,n.createElement)("div",{className:"feedlane-empty"},(0,n.createElement)("div",{className:"feedlane-empty__icon"},"📊"),(0,n.createElement)("h3",null,"No Status Found"),(0,n.createElement)("p",null,"Create your first status to track idea progress.")),l&&(0,n.createElement)("div",{className:"feedlane-modal",onClick:()=>o(!1)},(0,n.createElement)("div",{className:"feedlane-modal__backdrop"}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content",onClick:e=>e.stopPropagation()},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,s?"Edit Status":"Add New Status"),(0,n.createElement)("button",{type:"button",onClick:()=>o(!1),className:"feedlane-modal__close","aria-label":"Close modal"},"✕")),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("form",{onSubmit:m,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-name"},"Name *"),(0,n.createElement)("input",{type:"text",id:"status-name",name:"name",value:c.name,onChange:f,className:"feedlane-input",required:!0,autoFocus:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-slug"},"Slug"),(0,n.createElement)("input",{type:"text",id:"status-slug",name:"slug",value:c.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-description"},"Description"),(0,n.createElement)("textarea",{id:"status-description",name:"description",value:c.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"status-color"},"Color"),(0,n.createElement)("div",{className:"feedlane-color-picker"},(0,n.createElement)("input",{type:"color",id:"status-color",name:"color",value:c.color,onChange:f,className:"feedlane-color-input"}),(0,n.createElement)("input",{type:"text",value:c.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#6B7280"}))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:()=>o(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,n.createElement)("button",{type:"button",onClick:m,className:"feedlane-btn feedlane-btn--primary"},s?"Update Status":"Create Status"))))))))};var Fe=a(795);const Ie="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function Me(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function Te(e){return"nodeType"in e}function je(e){var t,a;return e?Me(e)?e:Te(e)&&null!=(t=null==(a=e.ownerDocument)?void 0:a.defaultView)?t:window:window}function Le(e){const{Document:t}=je(e);return e instanceof t}function Pe(e){return!Me(e)&&e instanceof je(e).HTMLElement}function Be(e){return e instanceof je(e).SVGElement}function ze(e){return e?Me(e)?e.document:Te(e)?Le(e)?e:Pe(e)||Be(e)?e.ownerDocument:document:document:document}const Ue=Ie?n.useLayoutEffect:n.useEffect;function He(e){const t=(0,n.useRef)(e);return Ue((()=>{t.current=e})),(0,n.useCallback)((function(){for(var e=arguments.length,a=new Array(e),n=0;n<e;n++)a[n]=arguments[n];return null==t.current?void 0:t.current(...a)}),[])}function We(e,t){void 0===t&&(t=[e]);const a=(0,n.useRef)(e);return Ue((()=>{a.current!==e&&(a.current=e)}),t),a}function Ve(e,t){const a=(0,n.useRef)();return(0,n.useMemo)((()=>{const t=e(a.current);return a.current=t,t}),[...t])}function Xe(e){const t=He(e),a=(0,n.useRef)(null),r=(0,n.useCallback)((e=>{e!==a.current&&(null==t||t(e,a.current)),a.current=e}),[]);return[a,r]}function Ye(e){const t=(0,n.useRef)();return(0,n.useEffect)((()=>{t.current=e}),[e]),t.current}let $e={};function qe(e,t){return(0,n.useMemo)((()=>{if(t)return t;const a=null==$e[e]?0:$e[e]+1;return $e[e]=a,e+"-"+a}),[e,t])}function Ke(e){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];return n.reduce(((t,a)=>{const n=Object.entries(a);for(const[a,r]of n){const n=t[a];null!=n&&(t[a]=n+e*r)}return t}),{...t})}}const Je=Ke(1),Ge=Ke(-1);function Qe(e){if(!e)return!1;const{KeyboardEvent:t}=je(e.target);return t&&e instanceof t}function Ze(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=je(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:a}=e.touches[0];return{x:t,y:a}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:a}=e.changedTouches[0];return{x:t,y:a}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const et=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:a}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(a?Math.round(a):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:a}=e;return"scaleX("+t+") scaleY("+a+")"}},Transform:{toString(e){if(e)return[et.Translate.toString(e),et.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:a,easing:n}=e;return t+" "+a+"ms "+n}}}),tt="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function at(e){return e.matches(tt)?e:e.querySelector(tt)}const nt={display:"none"};function rt(e){let{id:t,value:a}=e;return r().createElement("div",{id:t,style:nt},a)}function lt(e){let{id:t,announcement:a,ariaLiveType:n="assertive"}=e;return r().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},a)}const ot=(0,n.createContext)(null),st={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},it={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:a}=e;return a?"Draggable item "+t.id+" was moved over droppable area "+a.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:a}=e;return a?"Draggable item "+t.id+" was dropped over droppable area "+a.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function ct(e){let{announcements:t=it,container:a,hiddenTextDescribedById:l,screenReaderInstructions:o=st}=e;const{announce:s,announcement:i}=function(){const[e,t]=(0,n.useState)("");return{announce:(0,n.useCallback)((e=>{null!=e&&t(e)}),[]),announcement:e}}(),c=qe("DndLiveRegion"),[d,u]=(0,n.useState)(!1);if((0,n.useEffect)((()=>{u(!0)}),[]),function(e){const t=(0,n.useContext)(ot);(0,n.useEffect)((()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)}),[e,t])}((0,n.useMemo)((()=>({onDragStart(e){let{active:a}=e;s(t.onDragStart({active:a}))},onDragMove(e){let{active:a,over:n}=e;t.onDragMove&&s(t.onDragMove({active:a,over:n}))},onDragOver(e){let{active:a,over:n}=e;s(t.onDragOver({active:a,over:n}))},onDragEnd(e){let{active:a,over:n}=e;s(t.onDragEnd({active:a,over:n}))},onDragCancel(e){let{active:a,over:n}=e;s(t.onDragCancel({active:a,over:n}))}})),[s,t])),!d)return null;const m=r().createElement(r().Fragment,null,r().createElement(rt,{id:l,value:o.draggable}),r().createElement(lt,{id:c,announcement:i}));return a?(0,Fe.createPortal)(m,a):m}var dt;function ut(){}function mt(e,t){return(0,n.useMemo)((()=>({sensor:e,options:null!=t?t:{}})),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(dt||(dt={}));const ft=Object.freeze({x:0,y:0});function pt(e,t){const a=Ze(e);return a?(a.x-t.left)/t.width*100+"% "+(a.y-t.top)/t.height*100+"%":"0 0"}function gt(e,t){let{data:{value:a}}=e,{data:{value:n}}=t;return a-n}function vt(e,t){let{data:{value:a}}=e,{data:{value:n}}=t;return n-a}function ht(e){let{left:t,top:a,height:n,width:r}=e;return[{x:t,y:a},{x:t+r,y:a},{x:t,y:a+n},{x:t+r,y:a+n}]}const bt=e=>{let{collisionRect:t,droppableRects:a,droppableContainers:n}=e;const r=ht(t),l=[];for(const e of n){const{id:t}=e,n=a.get(t);if(n){const a=ht(n),o=r.reduce(((e,t,n)=>{return e+(r=a[n],l=t,Math.sqrt(Math.pow(r.x-l.x,2)+Math.pow(r.y-l.y,2)));var r,l}),0),s=Number((o/4).toFixed(4));l.push({id:t,data:{droppableContainer:e,value:s}})}}return l.sort(gt)};function yt(e,t){const a=Math.max(t.top,e.top),n=Math.max(t.left,e.left),r=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height),o=r-n,s=l-a;if(n<r&&a<l){const a=t.width*t.height,n=e.width*e.height,r=o*s;return Number((r/(a+n-r)).toFixed(4))}return 0}const Et=e=>{let{collisionRect:t,droppableRects:a,droppableContainers:n}=e;const r=[];for(const e of n){const{id:n}=e,l=a.get(n);if(l){const a=yt(l,t);a>0&&r.push({id:n,data:{droppableContainer:e,value:a}})}}return r.sort(vt)};function xt(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:ft}function wt(e){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),r=1;r<a;r++)n[r-1]=arguments[r];return n.reduce(((t,a)=>({...t,top:t.top+e*a.y,bottom:t.bottom+e*a.y,left:t.left+e*a.x,right:t.right+e*a.x})),{...t})}}const Nt=wt(1);function _t(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}const Ct={ignoreTransform:!1};function kt(e,t){void 0===t&&(t=Ct);let a=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:n}=je(e).getComputedStyle(e);t&&(a=function(e,t,a){const n=_t(t);if(!n)return e;const{scaleX:r,scaleY:l,x:o,y:s}=n,i=e.left-o-(1-r)*parseFloat(a),c=e.top-s-(1-l)*parseFloat(a.slice(a.indexOf(" ")+1)),d=r?e.width/r:e.width,u=l?e.height/l:e.height;return{width:d,height:u,top:c,right:i+d,bottom:c+u,left:i}}(a,t,n))}const{top:n,left:r,width:l,height:o,bottom:s,right:i}=a;return{top:n,left:r,width:l,height:o,bottom:s,right:i}}function St(e){return kt(e,{ignoreTransform:!0})}function Dt(e,t){const a=[];return e?function n(r){if(null!=t&&a.length>=t)return a;if(!r)return a;if(Le(r)&&null!=r.scrollingElement&&!a.includes(r.scrollingElement))return a.push(r.scrollingElement),a;if(!Pe(r)||Be(r))return a;if(a.includes(r))return a;const l=je(e).getComputedStyle(r);return r!==e&&function(e,t){void 0===t&&(t=je(e).getComputedStyle(e));const a=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const n=t[e];return"string"==typeof n&&a.test(n)}))}(r,l)&&a.push(r),function(e,t){return void 0===t&&(t=je(e).getComputedStyle(e)),"fixed"===t.position}(r,l)?a:n(r.parentNode)}(e):a}function Rt(e){const[t]=Dt(e,1);return null!=t?t:null}function At(e){return Ie&&e?Me(e)?e:Te(e)?Le(e)||e===ze(e).scrollingElement?window:Pe(e)?e:null:null:null}function Ot(e){return Me(e)?e.scrollX:e.scrollLeft}function Ft(e){return Me(e)?e.scrollY:e.scrollTop}function It(e){return{x:Ot(e),y:Ft(e)}}var Mt;function Tt(e){return!(!Ie||!e)&&e===document.scrollingElement}function jt(e){const t={x:0,y:0},a=Tt(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-a.width,y:e.scrollHeight-a.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(Mt||(Mt={}));const Lt={x:.2,y:.2};function Pt(e,t,a,n,r){let{top:l,left:o,right:s,bottom:i}=a;void 0===n&&(n=10),void 0===r&&(r=Lt);const{isTop:c,isBottom:d,isLeft:u,isRight:m}=jt(e),f={x:0,y:0},p={x:0,y:0},g=t.height*r.y,v=t.width*r.x;return!c&&l<=t.top+g?(f.y=Mt.Backward,p.y=n*Math.abs((t.top+g-l)/g)):!d&&i>=t.bottom-g&&(f.y=Mt.Forward,p.y=n*Math.abs((t.bottom-g-i)/g)),!m&&s>=t.right-v?(f.x=Mt.Forward,p.x=n*Math.abs((t.right-v-s)/v)):!u&&o<=t.left+v&&(f.x=Mt.Backward,p.x=n*Math.abs((t.left+v-o)/v)),{direction:f,speed:p}}function Bt(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:a,right:n,bottom:r}=e.getBoundingClientRect();return{top:t,left:a,right:n,bottom:r,width:e.clientWidth,height:e.clientHeight}}function zt(e){return e.reduce(((e,t)=>Je(e,It(t))),ft)}function Ut(e,t){if(void 0===t&&(t=kt),!e)return;const{top:a,left:n,bottom:r,right:l}=t(e);Rt(e)&&(r<=0||l<=0||a>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const Ht=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+Ot(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+Ft(t)),0)}]];class Wt{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const a=Dt(t),n=zt(a);this.rect={...e},this.width=e.width,this.height=e.height;for(const[e,t,r]of Ht)for(const l of t)Object.defineProperty(this,l,{get:()=>{const t=r(a),o=n[e]-t;return this.rect[l]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Vt{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,a){var n;null==(n=this.target)||n.addEventListener(e,t,a),this.listeners.push([e,t,a])}}function Xt(e,t){const a=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(a**2+n**2)>t:"x"in t&&"y"in t?a>t.x&&n>t.y:"x"in t?a>t.x:"y"in t&&n>t.y}var Yt,$t,qt;function Kt(e){e.preventDefault()}function Jt(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(Yt||(Yt={})),(qt=$t||($t={})).Space="Space",qt.Down="ArrowDown",qt.Right="ArrowRight",qt.Left="ArrowLeft",qt.Up="ArrowUp",qt.Esc="Escape",qt.Enter="Enter",qt.Tab="Tab";const Gt={start:[$t.Space,$t.Enter],cancel:[$t.Esc],end:[$t.Space,$t.Enter,$t.Tab]},Qt=(e,t)=>{let{currentCoordinates:a}=t;switch(e.code){case $t.Right:return{...a,x:a.x+25};case $t.Left:return{...a,x:a.x-25};case $t.Down:return{...a,y:a.y+25};case $t.Up:return{...a,y:a.y-25}}};class Zt{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new Vt(ze(t)),this.windowListeners=new Vt(je(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(Yt.Resize,this.handleCancel),this.windowListeners.add(Yt.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(Yt.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,a=e.node.current;a&&Ut(a),t(ft)}handleKeyDown(e){if(Qe(e)){const{active:t,context:a,options:n}=this.props,{keyboardCodes:r=Gt,coordinateGetter:l=Qt,scrollBehavior:o="smooth"}=n,{code:s}=e;if(r.end.includes(s))return void this.handleEnd(e);if(r.cancel.includes(s))return void this.handleCancel(e);const{collisionRect:i}=a.current,c=i?{x:i.left,y:i.top}:ft;this.referenceCoordinates||(this.referenceCoordinates=c);const d=l(e,{active:t,context:a.current,currentCoordinates:c});if(d){const t=Ge(d,c),n={x:0,y:0},{scrollableAncestors:r}=a.current;for(const a of r){const r=e.code,{isTop:l,isRight:s,isLeft:i,isBottom:c,maxScroll:u,minScroll:m}=jt(a),f=Bt(a),p={x:Math.min(r===$t.Right?f.right-f.width/2:f.right,Math.max(r===$t.Right?f.left:f.left+f.width/2,d.x)),y:Math.min(r===$t.Down?f.bottom-f.height/2:f.bottom,Math.max(r===$t.Down?f.top:f.top+f.height/2,d.y))},g=r===$t.Right&&!s||r===$t.Left&&!i,v=r===$t.Down&&!c||r===$t.Up&&!l;if(g&&p.x!==d.x){const e=a.scrollLeft+t.x,l=r===$t.Right&&e<=u.x||r===$t.Left&&e>=m.x;if(l&&!t.y)return void a.scrollTo({left:e,behavior:o});n.x=l?a.scrollLeft-e:r===$t.Right?a.scrollLeft-u.x:a.scrollLeft-m.x,n.x&&a.scrollBy({left:-n.x,behavior:o});break}if(v&&p.y!==d.y){const e=a.scrollTop+t.y,l=r===$t.Down&&e<=u.y||r===$t.Up&&e>=m.y;if(l&&!t.x)return void a.scrollTo({top:e,behavior:o});n.y=l?a.scrollTop-e:r===$t.Down?a.scrollTop-u.y:a.scrollTop-m.y,n.y&&a.scrollBy({top:-n.y,behavior:o});break}}this.handleMove(e,Je(Ge(d,this.referenceCoordinates),n))}}}handleMove(e,t){const{onMove:a}=this.props;e.preventDefault(),a(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ea(e){return Boolean(e&&"distance"in e)}function ta(e){return Boolean(e&&"delay"in e)}Zt.activators=[{eventName:"onKeyDown",handler:(e,t,a)=>{let{keyboardCodes:n=Gt,onActivation:r}=t,{active:l}=a;const{code:o}=e.nativeEvent;if(n.start.includes(o)){const t=l.activatorNode.current;return!(t&&e.target!==t||(e.preventDefault(),null==r||r({event:e.nativeEvent}),0))}return!1}}];class aa{constructor(e,t,a){var n;void 0===a&&(a=function(e){const{EventTarget:t}=je(e);return e instanceof t?e:ze(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:r}=e,{target:l}=r;this.props=e,this.events=t,this.document=ze(l),this.documentListeners=new Vt(this.document),this.listeners=new Vt(a),this.windowListeners=new Vt(je(l)),this.initialCoordinates=null!=(n=Ze(r))?n:ft,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:a}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(Yt.Resize,this.handleCancel),this.windowListeners.add(Yt.DragStart,Kt),this.windowListeners.add(Yt.VisibilityChange,this.handleCancel),this.windowListeners.add(Yt.ContextMenu,Kt),this.documentListeners.add(Yt.Keydown,this.handleKeydown),t){if(null!=a&&a({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ta(t))return this.timeoutId=setTimeout(this.handleStart,t.delay),void this.handlePending(t);if(ea(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){const{active:a,onPending:n}=this.props;n(a,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(Yt.Click,Jt,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(Yt.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:a,initialCoordinates:n,props:r}=this,{onMove:l,options:{activationConstraint:o}}=r;if(!n)return;const s=null!=(t=Ze(e))?t:ft,i=Ge(n,s);if(!a&&o){if(ea(o)){if(null!=o.tolerance&&Xt(i,o.tolerance))return this.handleCancel();if(Xt(i,o.distance))return this.handleStart()}return ta(o)&&Xt(i,o.tolerance)?this.handleCancel():void this.handlePending(o,i)}e.cancelable&&e.preventDefault(),l(s)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===$t.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const na={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ra extends aa{constructor(e){const{event:t}=e,a=ze(t.target);super(e,na,a)}}ra.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:n}=t;return!(!a.isPrimary||0!==a.button||(null==n||n({event:a}),0))}}];const la={move:{name:"mousemove"},end:{name:"mouseup"}};var oa;!function(e){e[e.RightClick=2]="RightClick"}(oa||(oa={})),class extends aa{constructor(e){super(e,la,ze(e.event.target))}}.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:n}=t;return a.button!==oa.RightClick&&(null==n||n({event:a}),!0)}}];const sa={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};var ia,ca;(class extends aa{constructor(e){super(e,sa)}static setup(){return window.addEventListener(sa.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(sa.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:a}=e,{onActivation:n}=t;const{touches:r}=a;return!(r.length>1||(null==n||n({event:a}),0))}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(ia||(ia={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(ca||(ca={}));const da={x:{[Mt.Backward]:!1,[Mt.Forward]:!1},y:{[Mt.Backward]:!1,[Mt.Forward]:!1}};var ua,ma;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(ua||(ua={})),function(e){e.Optimized="optimized"}(ma||(ma={}));const fa=new Map;function pa(e,t){return Ve((a=>e?a||("function"==typeof t?t(e):e):null),[t,e])}function ga(e){let{callback:t,disabled:a}=e;const r=He(t),l=(0,n.useMemo)((()=>{if(a||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(r)}),[a]);return(0,n.useEffect)((()=>()=>null==l?void 0:l.disconnect()),[l]),l}function va(e){return new Wt(kt(e),e)}function ha(e,t,a){void 0===t&&(t=va);const[r,l]=(0,n.useState)(null);function o(){l((n=>{if(!e)return null;var r;if(!1===e.isConnected)return null!=(r=null!=n?n:a)?r:null;const l=t(e);return JSON.stringify(n)===JSON.stringify(l)?n:l}))}const s=function(e){let{callback:t,disabled:a}=e;const r=He(t),l=(0,n.useMemo)((()=>{if(a||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(r)}),[r,a]);return(0,n.useEffect)((()=>()=>null==l?void 0:l.disconnect()),[l]),l}({callback(t){if(e)for(const a of t){const{type:t,target:n}=a;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){o();break}}}}),i=ga({callback:o});return Ue((()=>{o(),e?(null==i||i.observe(e),null==s||s.observe(document.body,{childList:!0,subtree:!0})):(null==i||i.disconnect(),null==s||s.disconnect())}),[e]),r}const ba=[];function ya(e,t){void 0===t&&(t=[]);const a=(0,n.useRef)(null);return(0,n.useEffect)((()=>{a.current=null}),t),(0,n.useEffect)((()=>{const t=e!==ft;t&&!a.current&&(a.current=e),!t&&a.current&&(a.current=null)}),[e]),a.current?Ge(e,a.current):ft}function Ea(e){return(0,n.useMemo)((()=>e?function(e){const t=e.innerWidth,a=e.innerHeight;return{top:0,left:0,right:t,bottom:a,width:t,height:a}}(e):null),[e])}const xa=[];function wa(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return Pe(t)?t:e}const Na=[{sensor:ra,options:{}},{sensor:Zt,options:{}}],_a={current:{}},Ca={draggable:{measure:St},droppable:{measure:St,strategy:ua.WhileDragging,frequency:ma.Optimized},dragOverlay:{measure:kt}};class ka extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,a;return null!=(t=null==(a=this.get(e))?void 0:a.node.current)?t:void 0}}const Sa={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ka,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:ut},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Ca,measureDroppableContainers:ut,windowRect:null,measuringScheduled:!1},Da={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:ut,draggableNodes:new Map,over:null,measureDroppableContainers:ut},Ra=(0,n.createContext)(Da),Aa=(0,n.createContext)(Sa);function Oa(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ka}}}function Fa(e,t){switch(t.type){case dt.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case dt.DragMove:return null==e.draggable.active?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case dt.DragEnd:case dt.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case dt.RegisterDroppable:{const{element:a}=t,{id:n}=a,r=new ka(e.droppable.containers);return r.set(n,a),{...e,droppable:{...e.droppable,containers:r}}}case dt.SetDroppableDisabled:{const{id:a,key:n,disabled:r}=t,l=e.droppable.containers.get(a);if(!l||n!==l.key)return e;const o=new ka(e.droppable.containers);return o.set(a,{...l,disabled:r}),{...e,droppable:{...e.droppable,containers:o}}}case dt.UnregisterDroppable:{const{id:a,key:n}=t,r=e.droppable.containers.get(a);if(!r||n!==r.key)return e;const l=new ka(e.droppable.containers);return l.delete(a),{...e,droppable:{...e.droppable,containers:l}}}default:return e}}function Ia(e){let{disabled:t}=e;const{active:a,activatorEvent:r,draggableNodes:l}=(0,n.useContext)(Ra),o=Ye(r),s=Ye(null==a?void 0:a.id);return(0,n.useEffect)((()=>{if(!t&&!r&&o&&null!=s){if(!Qe(o))return;if(document.activeElement===o.target)return;const e=l.get(s);if(!e)return;const{activatorNode:t,node:a}=e;if(!t.current&&!a.current)return;requestAnimationFrame((()=>{for(const e of[t.current,a.current]){if(!e)continue;const t=at(e);if(t){t.focus();break}}}))}}),[r,t,l,s,o]),null}function Ma(e,t){let{transform:a,...n}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...n})),a):a}const Ta=(0,n.createContext)({...ft,scaleX:1,scaleY:1});var ja;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(ja||(ja={}));const La=(0,n.memo)((function(e){var t,a,l,o;let{id:s,accessibility:i,autoScroll:c=!0,children:d,sensors:u=Na,collisionDetection:m=Et,measuring:f,modifiers:p,...g}=e;const v=(0,n.useReducer)(Fa,void 0,Oa),[h,b]=v,[y,E]=function(){const[e]=(0,n.useState)((()=>new Set)),t=(0,n.useCallback)((t=>(e.add(t),()=>e.delete(t))),[e]);return[(0,n.useCallback)((t=>{let{type:a,event:n}=t;e.forEach((e=>{var t;return null==(t=e[a])?void 0:t.call(e,n)}))}),[e]),t]}(),[x,w]=(0,n.useState)(ja.Uninitialized),N=x===ja.Initialized,{draggable:{active:_,nodes:C,translate:k},droppable:{containers:S}}=h,D=null!=_?C.get(_):null,R=(0,n.useRef)({initial:null,translated:null}),A=(0,n.useMemo)((()=>{var e;return null!=_?{id:_,data:null!=(e=null==D?void 0:D.data)?e:_a,rect:R}:null}),[_,D]),O=(0,n.useRef)(null),[F,I]=(0,n.useState)(null),[M,T]=(0,n.useState)(null),j=We(g,Object.values(g)),L=qe("DndDescribedBy",s),P=(0,n.useMemo)((()=>S.getEnabled()),[S]),B=(z=f,(0,n.useMemo)((()=>({draggable:{...Ca.draggable,...null==z?void 0:z.draggable},droppable:{...Ca.droppable,...null==z?void 0:z.droppable},dragOverlay:{...Ca.dragOverlay,...null==z?void 0:z.dragOverlay}})),[null==z?void 0:z.draggable,null==z?void 0:z.droppable,null==z?void 0:z.dragOverlay]));var z;const{droppableRects:U,measureDroppableContainers:H,measuringScheduled:W}=function(e,t){let{dragging:a,dependencies:r,config:l}=t;const[o,s]=(0,n.useState)(null),{frequency:i,measure:c,strategy:d}=l,u=(0,n.useRef)(e),m=function(){switch(d){case ua.Always:return!1;case ua.BeforeDragging:return a;default:return!a}}(),f=We(m),p=(0,n.useCallback)((function(e){void 0===e&&(e=[]),f.current||s((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[f]),g=(0,n.useRef)(null),v=Ve((t=>{if(m&&!a)return fa;if(!t||t===fa||u.current!==e||null!=o){const t=new Map;for(let a of e){if(!a)continue;if(o&&o.length>0&&!o.includes(a.id)&&a.rect.current){t.set(a.id,a.rect.current);continue}const e=a.node.current,n=e?new Wt(c(e),e):null;a.rect.current=n,n&&t.set(a.id,n)}return t}return t}),[e,o,a,m,c]);return(0,n.useEffect)((()=>{u.current=e}),[e]),(0,n.useEffect)((()=>{m||p()}),[a,m]),(0,n.useEffect)((()=>{o&&o.length>0&&s(null)}),[JSON.stringify(o)]),(0,n.useEffect)((()=>{m||"number"!=typeof i||null!==g.current||(g.current=setTimeout((()=>{p(),g.current=null}),i))}),[i,m,p,...r]),{droppableRects:v,measureDroppableContainers:p,measuringScheduled:null!=o}}(P,{dragging:N,dependencies:[k.x,k.y],config:B.droppable}),V=function(e,t){const a=null!=t?e.get(t):void 0,n=a?a.node.current:null;return Ve((e=>{var a;return null==t?null:null!=(a=null!=n?n:e)?a:null}),[n,t])}(C,_),X=(0,n.useMemo)((()=>M?Ze(M):null),[M]),Y=function(){const e=!1===(null==F?void 0:F.autoScrollEnabled),t="object"==typeof c?!1===c.enabled:!1===c,a=N&&!e&&!t;return"object"==typeof c?{...c,enabled:a}:{enabled:a}}(),$=function(e,t){return pa(e,t)}(V,B.draggable.measure);!function(e){let{activeNode:t,measure:a,initialRect:r,config:l=!0}=e;const o=(0,n.useRef)(!1),{x:s,y:i}="boolean"==typeof l?{x:l,y:l}:l;Ue((()=>{if(!s&&!i||!t)return void(o.current=!1);if(o.current||!r)return;const e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;const n=xt(a(e),r);if(s||(n.x=0),i||(n.y=0),o.current=!0,Math.abs(n.x)>0||Math.abs(n.y)>0){const t=Rt(e);t&&t.scrollBy({top:n.y,left:n.x})}}),[t,s,i,r,a])}({activeNode:null!=_?C.get(_):null,config:Y.layoutShiftCompensation,initialRect:$,measure:B.draggable.measure});const q=ha(V,B.draggable.measure,$),K=ha(V?V.parentElement:null),J=(0,n.useRef)({activatorEvent:null,active:null,activeNode:V,collisionRect:null,collisions:null,droppableRects:U,draggableNodes:C,draggingNode:null,draggingNodeRect:null,droppableContainers:S,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),G=S.getNodeFor(null==(t=J.current.over)?void 0:t.id),Q=function(e){let{measure:t}=e;const[a,r]=(0,n.useState)(null),l=ga({callback:(0,n.useCallback)((e=>{for(const{target:a}of e)if(Pe(a)){r((e=>{const n=t(a);return e?{...e,width:n.width,height:n.height}:n}));break}}),[t])}),o=(0,n.useCallback)((e=>{const a=wa(e);null==l||l.disconnect(),a&&(null==l||l.observe(a)),r(a?t(a):null)}),[t,l]),[s,i]=Xe(o);return(0,n.useMemo)((()=>({nodeRef:s,rect:a,setRef:i})),[a,s,i])}({measure:B.dragOverlay.measure}),Z=null!=(a=Q.nodeRef.current)?a:V,ee=N?null!=(l=Q.rect)?l:q:null,te=Boolean(Q.nodeRef.current&&Q.rect),ae=xt(ne=te?null:q,pa(ne));var ne;const re=Ea(Z?je(Z):null),le=function(e){const t=(0,n.useRef)(e),a=Ve((a=>e?a&&a!==ba&&e&&t.current&&e.parentNode===t.current.parentNode?a:Dt(e):ba),[e]);return(0,n.useEffect)((()=>{t.current=e}),[e]),a}(N?null!=G?G:V:null),oe=function(e,t){void 0===t&&(t=kt);const[a]=e,r=Ea(a?je(a):null),[l,o]=(0,n.useState)(xa);function s(){o((()=>e.length?e.map((e=>Tt(e)?r:new Wt(t(e),e))):xa))}const i=ga({callback:s});return Ue((()=>{null==i||i.disconnect(),s(),e.forEach((e=>null==i?void 0:i.observe(e)))}),[e]),l}(le),se=Ma(p,{transform:{x:k.x-ae.x,y:k.y-ae.y,scaleX:1,scaleY:1},activatorEvent:M,active:A,activeNodeRect:q,containerNodeRect:K,draggingNodeRect:ee,over:J.current.over,overlayNodeRect:Q.rect,scrollableAncestors:le,scrollableAncestorRects:oe,windowRect:re}),ie=X?Je(X,k):null,ce=function(e){const[t,a]=(0,n.useState)(null),r=(0,n.useRef)(e),l=(0,n.useCallback)((e=>{const t=At(e.target);t&&a((e=>e?(e.set(t,It(t)),new Map(e)):null))}),[]);return(0,n.useEffect)((()=>{const t=r.current;if(e!==t){n(t);const o=e.map((e=>{const t=At(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,It(t)]):null})).filter((e=>null!=e));a(o.length?new Map(o):null),r.current=e}return()=>{n(e),n(t)};function n(e){e.forEach((e=>{const t=At(e);null==t||t.removeEventListener("scroll",l)}))}}),[l,e]),(0,n.useMemo)((()=>e.length?t?Array.from(t.values()).reduce(((e,t)=>Je(e,t)),ft):zt(e):ft),[e,t])}(le),de=ya(ce),ue=ya(ce,[q]),me=Je(se,de),fe=ee?Nt(ee,se):null,pe=A&&fe?m({active:A,collisionRect:fe,droppableRects:U,droppableContainers:P,pointerCoordinates:ie}):null,ge=function(e){if(!e||0===e.length)return null;const[t]=e;return t.id}(pe),[ve,he]=(0,n.useState)(null),be=function(e,t,a){return{...e,scaleX:t&&a?t.width/a.width:1,scaleY:t&&a?t.height/a.height:1}}(te?se:Je(se,ue),null!=(o=null==ve?void 0:ve.rect)?o:null,q),ye=(0,n.useRef)(null),Ee=(0,n.useCallback)(((e,t)=>{let{sensor:a,options:n}=t;if(null==O.current)return;const r=C.get(O.current);if(!r)return;const l=e.nativeEvent,o=new a({active:O.current,activeNode:r,event:l,options:n,context:J,onAbort(e){if(!C.get(e))return;const{onDragAbort:t}=j.current,a={id:e};null==t||t(a),y({type:"onDragAbort",event:a})},onPending(e,t,a,n){if(!C.get(e))return;const{onDragPending:r}=j.current,l={id:e,constraint:t,initialCoordinates:a,offset:n};null==r||r(l),y({type:"onDragPending",event:l})},onStart(e){const t=O.current;if(null==t)return;const a=C.get(t);if(!a)return;const{onDragStart:n}=j.current,r={activatorEvent:l,active:{id:t,data:a.data,rect:R}};(0,Fe.unstable_batchedUpdates)((()=>{null==n||n(r),w(ja.Initializing),b({type:dt.DragStart,initialCoordinates:e,active:t}),y({type:"onDragStart",event:r}),I(ye.current),T(l)}))},onMove(e){b({type:dt.DragMove,coordinates:e})},onEnd:s(dt.DragEnd),onCancel:s(dt.DragCancel)});function s(e){return async function(){const{active:t,collisions:a,over:n,scrollAdjustedTranslate:r}=J.current;let o=null;if(t&&r){const{cancelDrop:s}=j.current;o={activatorEvent:l,active:t,collisions:a,delta:r,over:n},e===dt.DragEnd&&"function"==typeof s&&await Promise.resolve(s(o))&&(e=dt.DragCancel)}O.current=null,(0,Fe.unstable_batchedUpdates)((()=>{b({type:e}),w(ja.Uninitialized),he(null),I(null),T(null),ye.current=null;const t=e===dt.DragEnd?"onDragEnd":"onDragCancel";if(o){const e=j.current[t];null==e||e(o),y({type:t,event:o})}}))}}ye.current=o}),[C]),xe=(0,n.useCallback)(((e,t)=>(a,n)=>{const r=a.nativeEvent,l=C.get(n);if(null!==O.current||!l||r.dndKit||r.defaultPrevented)return;const o={active:l};!0===e(a,t.options,o)&&(r.dndKit={capturedBy:t.sensor},O.current=n,Ee(a,t))}),[C,Ee]),we=function(e,t){return(0,n.useMemo)((()=>e.reduce(((e,a)=>{const{sensor:n}=a;return[...e,...n.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,a)})))]}),[])),[e,t])}(u,xe);!function(e){(0,n.useEffect)((()=>{if(!Ie)return;const t=e.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const e of t)null==e||e()}}),e.map((e=>{let{sensor:t}=e;return t})))}(u),Ue((()=>{q&&x===ja.Initializing&&w(ja.Initialized)}),[q,x]),(0,n.useEffect)((()=>{const{onDragMove:e}=j.current,{active:t,activatorEvent:a,collisions:n,over:r}=J.current;if(!t||!a)return;const l={active:t,activatorEvent:a,collisions:n,delta:{x:me.x,y:me.y},over:r};(0,Fe.unstable_batchedUpdates)((()=>{null==e||e(l),y({type:"onDragMove",event:l})}))}),[me.x,me.y]),(0,n.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:a,droppableContainers:n,scrollAdjustedTranslate:r}=J.current;if(!e||null==O.current||!t||!r)return;const{onDragOver:l}=j.current,o=n.get(ge),s=o&&o.rect.current?{id:o.id,rect:o.rect.current,data:o.data,disabled:o.disabled}:null,i={active:e,activatorEvent:t,collisions:a,delta:{x:r.x,y:r.y},over:s};(0,Fe.unstable_batchedUpdates)((()=>{he(s),null==l||l(i),y({type:"onDragOver",event:i})}))}),[ge]),Ue((()=>{J.current={activatorEvent:M,active:A,activeNode:V,collisionRect:fe,collisions:pe,droppableRects:U,draggableNodes:C,draggingNode:Z,draggingNodeRect:ee,droppableContainers:S,over:ve,scrollableAncestors:le,scrollAdjustedTranslate:me},R.current={initial:ee,translated:fe}}),[A,V,pe,fe,C,Z,ee,U,S,ve,le,me]),function(e){let{acceleration:t,activator:a=ia.Pointer,canScroll:r,draggingRect:l,enabled:o,interval:s=5,order:i=ca.TreeOrder,pointerCoordinates:c,scrollableAncestors:d,scrollableAncestorRects:u,delta:m,threshold:f}=e;const p=function(e){let{delta:t,disabled:a}=e;const n=Ye(t);return Ve((e=>{if(a||!n||!e)return da;const r=Math.sign(t.x-n.x),l=Math.sign(t.y-n.y);return{x:{[Mt.Backward]:e.x[Mt.Backward]||-1===r,[Mt.Forward]:e.x[Mt.Forward]||1===r},y:{[Mt.Backward]:e.y[Mt.Backward]||-1===l,[Mt.Forward]:e.y[Mt.Forward]||1===l}}}),[a,t,n])}({delta:m,disabled:!o}),[g,v]=function(){const e=(0,n.useRef)(null);return[(0,n.useCallback)(((t,a)=>{e.current=setInterval(t,a)}),[]),(0,n.useCallback)((()=>{null!==e.current&&(clearInterval(e.current),e.current=null)}),[])]}(),h=(0,n.useRef)({x:0,y:0}),b=(0,n.useRef)({x:0,y:0}),y=(0,n.useMemo)((()=>{switch(a){case ia.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case ia.DraggableRect:return l}}),[a,l,c]),E=(0,n.useRef)(null),x=(0,n.useCallback)((()=>{const e=E.current;if(!e)return;const t=h.current.x*b.current.x,a=h.current.y*b.current.y;e.scrollBy(t,a)}),[]),w=(0,n.useMemo)((()=>i===ca.TreeOrder?[...d].reverse():d),[i,d]);(0,n.useEffect)((()=>{if(o&&d.length&&y){for(const e of w){if(!1===(null==r?void 0:r(e)))continue;const a=d.indexOf(e),n=u[a];if(!n)continue;const{direction:l,speed:o}=Pt(e,n,y,t,f);for(const e of["x","y"])p[e][l[e]]||(o[e]=0,l[e]=0);if(o.x>0||o.y>0)return v(),E.current=e,g(x,s),h.current=o,void(b.current=l)}h.current={x:0,y:0},b.current={x:0,y:0},v()}else v()}),[t,x,r,v,o,s,JSON.stringify(y),JSON.stringify(p),g,d,w,u,JSON.stringify(f)])}({...Y,delta:k,draggingRect:fe,pointerCoordinates:ie,scrollableAncestors:le,scrollableAncestorRects:oe});const Ne=(0,n.useMemo)((()=>({active:A,activeNode:V,activeNodeRect:q,activatorEvent:M,collisions:pe,containerNodeRect:K,dragOverlay:Q,draggableNodes:C,droppableContainers:S,droppableRects:U,over:ve,measureDroppableContainers:H,scrollableAncestors:le,scrollableAncestorRects:oe,measuringConfiguration:B,measuringScheduled:W,windowRect:re})),[A,V,q,M,pe,K,Q,C,S,U,ve,H,le,oe,B,W,re]),_e=(0,n.useMemo)((()=>({activatorEvent:M,activators:we,active:A,activeNodeRect:q,ariaDescribedById:{draggable:L},dispatch:b,draggableNodes:C,over:ve,measureDroppableContainers:H})),[M,we,A,q,b,L,C,ve,H]);return r().createElement(ot.Provider,{value:E},r().createElement(Ra.Provider,{value:_e},r().createElement(Aa.Provider,{value:Ne},r().createElement(Ta.Provider,{value:be},d)),r().createElement(Ia,{disabled:!1===(null==i?void 0:i.restoreFocus)})),r().createElement(ct,{...i,hiddenTextDescribedById:L}))})),Pa=(0,n.createContext)(null),Ba="button";function za(){return(0,n.useContext)(Aa)}const Ua={timeout:25};function Ha(e){let{data:t,disabled:a=!1,id:r,resizeObserverConfig:l}=e;const o=qe("Droppable"),{active:s,dispatch:i,over:c,measureDroppableContainers:d}=(0,n.useContext)(Ra),u=(0,n.useRef)({disabled:a}),m=(0,n.useRef)(!1),f=(0,n.useRef)(null),p=(0,n.useRef)(null),{disabled:g,updateMeasurementsFor:v,timeout:h}={...Ua,...l},b=We(null!=v?v:r),y=ga({callback:(0,n.useCallback)((()=>{m.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{d(Array.isArray(b.current)?b.current:[b.current]),p.current=null}),h)):m.current=!0}),[h]),disabled:g||!s}),E=(0,n.useCallback)(((e,t)=>{y&&(t&&(y.unobserve(t),m.current=!1),e&&y.observe(e))}),[y]),[x,w]=Xe(E),N=We(t);return(0,n.useEffect)((()=>{y&&x.current&&(y.disconnect(),m.current=!1,y.observe(x.current))}),[x,y]),(0,n.useEffect)((()=>(i({type:dt.RegisterDroppable,element:{id:r,key:o,disabled:a,node:x,rect:f,data:N}}),()=>i({type:dt.UnregisterDroppable,key:o,id:r}))),[r]),(0,n.useEffect)((()=>{a!==u.current.disabled&&(i({type:dt.SetDroppableDisabled,id:r,key:o,disabled:a}),u.current.disabled=a)}),[r,o,a,i]),{active:s,rect:f,isOver:(null==c?void 0:c.id)===r,node:x,over:c,setNodeRef:w}}function Wa(e){let{animation:t,children:a}=e;const[l,o]=(0,n.useState)(null),[s,i]=(0,n.useState)(null),c=Ye(a);return a||l||!c||o(c),Ue((()=>{if(!s)return;const e=null==l?void 0:l.key,a=null==l?void 0:l.props.id;null!=e&&null!=a?Promise.resolve(t(a,s)).then((()=>{o(null)})):o(null)}),[t,l,s]),r().createElement(r().Fragment,null,a,l?(0,n.cloneElement)(l,{ref:i}):null)}const Va={x:0,y:0,scaleX:1,scaleY:1};function Xa(e){let{children:t}=e;return r().createElement(Ra.Provider,{value:Da},r().createElement(Ta.Provider,{value:Va},t))}const Ya={position:"fixed",touchAction:"none"},$a=e=>Qe(e)?"transform 250ms ease":void 0,qa=(0,n.forwardRef)(((e,t)=>{let{as:a,activatorEvent:n,adjustScale:l,children:o,className:s,rect:i,style:c,transform:d,transition:u=$a}=e;if(!i)return null;const m=l?d:{...d,scaleX:1,scaleY:1},f={...Ya,width:i.width,height:i.height,top:i.top,left:i.left,transform:et.Transform.toString(m),transformOrigin:l&&n?pt(n,i):void 0,transition:"function"==typeof u?u(n):u,...c};return r().createElement(a,{className:s,style:f,ref:t},o)})),Ka=e=>t=>{let{active:a,dragOverlay:n}=t;const r={},{styles:l,className:o}=e;if(null!=l&&l.active)for(const[e,t]of Object.entries(l.active))void 0!==t&&(r[e]=a.node.style.getPropertyValue(e),a.node.style.setProperty(e,t));if(null!=l&&l.dragOverlay)for(const[e,t]of Object.entries(l.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=o&&o.active&&a.node.classList.add(o.active),null!=o&&o.dragOverlay&&n.node.classList.add(o.dragOverlay),function(){for(const[e,t]of Object.entries(r))a.node.style.setProperty(e,t);null!=o&&o.active&&a.node.classList.remove(o.active)}},Ja={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:a}}=e;return[{transform:et.Transform.toString(t)},{transform:et.Transform.toString(a)}]},sideEffects:Ka({styles:{active:{opacity:"0"}}})};let Ga=0;function Qa(e){return(0,n.useMemo)((()=>{if(null!=e)return Ga++,Ga}),[e])}const Za=r().memo((e=>{let{adjustScale:t=!1,children:a,dropAnimation:l,style:o,transition:s,modifiers:i,wrapperElement:c="div",className:d,zIndex:u=999}=e;const{activatorEvent:m,active:f,activeNodeRect:p,containerNodeRect:g,draggableNodes:v,droppableContainers:h,dragOverlay:b,over:y,measuringConfiguration:E,scrollableAncestors:x,scrollableAncestorRects:w,windowRect:N}=za(),_=(0,n.useContext)(Ta),C=Qa(null==f?void 0:f.id),k=Ma(i,{activatorEvent:m,active:f,activeNodeRect:p,containerNodeRect:g,draggingNodeRect:b.rect,over:y,overlayNodeRect:b.rect,scrollableAncestors:x,scrollableAncestorRects:w,transform:_,windowRect:N}),S=pa(p),D=function(e){let{config:t,draggableNodes:a,droppableContainers:n,measuringConfiguration:r}=e;return He(((e,l)=>{if(null===t)return;const o=a.get(e);if(!o)return;const s=o.node.current;if(!s)return;const i=wa(l);if(!i)return;const{transform:c}=je(l).getComputedStyle(l),d=_t(c);if(!d)return;const u="function"==typeof t?t:function(e){const{duration:t,easing:a,sideEffects:n,keyframes:r}={...Ja,...e};return e=>{let{active:l,dragOverlay:o,transform:s,...i}=e;if(!t)return;const c=o.rect.left-l.rect.left,d=o.rect.top-l.rect.top,u={scaleX:1!==s.scaleX?l.rect.width*s.scaleX/o.rect.width:1,scaleY:1!==s.scaleY?l.rect.height*s.scaleY/o.rect.height:1},m={x:s.x-c,y:s.y-d,...u},f=r({...i,active:l,dragOverlay:o,transform:{initial:s,final:m}}),[p]=f,g=f[f.length-1];if(JSON.stringify(p)===JSON.stringify(g))return;const v=null==n?void 0:n({active:l,dragOverlay:o,...i}),h=o.node.animate(f,{duration:t,easing:a,fill:"forwards"});return new Promise((e=>{h.onfinish=()=>{null==v||v(),e()}}))}}(t);return Ut(s,r.draggable.measure),u({active:{id:e,data:o.data,node:s,rect:r.draggable.measure(s)},draggableNodes:a,dragOverlay:{node:l,rect:r.dragOverlay.measure(i)},droppableContainers:n,measuringConfiguration:r,transform:d})}))}({config:l,draggableNodes:v,droppableContainers:h,measuringConfiguration:E}),R=S?b.setRef:void 0;return r().createElement(Xa,null,r().createElement(Wa,{animation:D},f&&C?r().createElement(qa,{key:C,id:f.id,ref:R,as:c,activatorEvent:m,adjustScale:t,className:d,transition:s,rect:S,style:{zIndex:u,...o},transform:k},a):null))}));function en(e,t,a){const n=e.slice();return n.splice(a<0?n.length+a:a,0,n.splice(t,1)[0]),n}function tn(e,t){return e.reduce(((e,a,n)=>{const r=t.get(a);return r&&(e[n]=r),e}),Array(e.length))}function an(e){return null!==e&&e>=0}const nn=e=>{let{rects:t,activeIndex:a,overIndex:n,index:r}=e;const l=en(t,n,a),o=t[r],s=l[r];return s&&o?{x:s.left-o.left,y:s.top-o.top,scaleX:s.width/o.width,scaleY:s.height/o.height}:null},rn={scaleX:1,scaleY:1},ln=e=>{var t;let{activeIndex:a,activeNodeRect:n,index:r,rects:l,overIndex:o}=e;const s=null!=(t=l[a])?t:n;if(!s)return null;if(r===a){const e=l[o];return e?{x:0,y:a<o?e.top+e.height-(s.top+s.height):e.top-s.top,...rn}:null}const i=function(e,t,a){const n=e[t],r=e[t-1],l=e[t+1];return n?a<t?r?n.top-(r.top+r.height):l?l.top-(n.top+n.height):0:l?l.top-(n.top+n.height):r?n.top-(r.top+r.height):0:0}(l,r,a);return r>a&&r<=o?{x:0,y:-s.height-i,...rn}:r<a&&r>=o?{x:0,y:s.height+i,...rn}:{x:0,y:0,...rn}},on="Sortable",sn=r().createContext({activeIndex:-1,containerId:on,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:nn,disabled:{draggable:!1,droppable:!1}});function cn(e){let{children:t,id:a,items:l,strategy:o=nn,disabled:s=!1}=e;const{active:i,dragOverlay:c,droppableRects:d,over:u,measureDroppableContainers:m}=za(),f=qe(on,a),p=Boolean(null!==c.rect),g=(0,n.useMemo)((()=>l.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[l]),v=null!=i,h=i?g.indexOf(i.id):-1,b=u?g.indexOf(u.id):-1,y=(0,n.useRef)(g),E=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let a=0;a<e.length;a++)if(e[a]!==t[a])return!1;return!0}(g,y.current),x=-1!==b&&-1===h||E,w=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(s);Ue((()=>{E&&v&&m(g)}),[E,g,v,m]),(0,n.useEffect)((()=>{y.current=g}),[g]);const N=(0,n.useMemo)((()=>({activeIndex:h,containerId:f,disabled:w,disableTransforms:x,items:g,overIndex:b,useDragOverlay:p,sortedRects:tn(g,d),strategy:o})),[h,f,w.draggable,w.droppable,x,g,b,d,p,o]);return r().createElement(sn.Provider,{value:N},t)}const dn=e=>{let{id:t,items:a,activeIndex:n,overIndex:r}=e;return en(a,n,r).indexOf(t)},un=e=>{let{containerId:t,isSorting:a,wasDragging:n,index:r,items:l,newIndex:o,previousItems:s,previousContainerId:i,transition:c}=e;return!(!c||!n||s!==l&&r===o||!a&&(o===r||t!==i))},mn={duration:200,easing:"ease"},fn="transform",pn=et.Transition.toString({property:fn,duration:0,easing:"linear"}),gn={roleDescription:"sortable"};function vn(e){let{animateLayoutChanges:t=un,attributes:a,disabled:r,data:l,getNewIndex:o=dn,id:s,strategy:i,resizeObserverConfig:c,transition:d=mn}=e;const{items:u,containerId:m,activeIndex:f,disabled:p,disableTransforms:g,sortedRects:v,overIndex:h,useDragOverlay:b,strategy:y}=(0,n.useContext)(sn),E=function(e,t){var a,n;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(a=null==e?void 0:e.draggable)?a:t.draggable,droppable:null!=(n=null==e?void 0:e.droppable)?n:t.droppable}}(r,p),x=u.indexOf(s),w=(0,n.useMemo)((()=>({sortable:{containerId:m,index:x,items:u},...l})),[m,l,x,u]),N=(0,n.useMemo)((()=>u.slice(u.indexOf(s))),[u,s]),{rect:_,node:C,isOver:k,setNodeRef:S}=Ha({id:s,data:w,disabled:E.droppable,resizeObserverConfig:{updateMeasurementsFor:N,...c}}),{active:D,activatorEvent:R,activeNodeRect:A,attributes:O,setNodeRef:F,listeners:I,isDragging:M,over:T,setActivatorNodeRef:j,transform:L}=function(e){let{id:t,data:a,disabled:r=!1,attributes:l}=e;const o=qe("Draggable"),{activators:s,activatorEvent:i,active:c,activeNodeRect:d,ariaDescribedById:u,draggableNodes:m,over:f}=(0,n.useContext)(Ra),{role:p=Ba,roleDescription:g="draggable",tabIndex:v=0}=null!=l?l:{},h=(null==c?void 0:c.id)===t,b=(0,n.useContext)(h?Ta:Pa),[y,E]=Xe(),[x,w]=Xe(),N=function(e,t){return(0,n.useMemo)((()=>e.reduce(((e,a)=>{let{eventName:n,handler:r}=a;return e[n]=e=>{r(e,t)},e}),{})),[e,t])}(s,t),_=We(a);return Ue((()=>(m.set(t,{id:t,key:o,node:y,activatorNode:x,data:_}),()=>{const e=m.get(t);e&&e.key===o&&m.delete(t)})),[m,t]),{active:c,activatorEvent:i,activeNodeRect:d,attributes:(0,n.useMemo)((()=>({role:p,tabIndex:v,"aria-disabled":r,"aria-pressed":!(!h||p!==Ba)||void 0,"aria-roledescription":g,"aria-describedby":u.draggable})),[r,p,v,h,g,u.draggable]),isDragging:h,listeners:r?void 0:N,node:y,over:f,setNodeRef:E,setActivatorNodeRef:w,transform:b}}({id:s,data:w,attributes:{...gn,...a},disabled:E.draggable}),P=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}(S,F),B=Boolean(D),z=B&&!g&&an(f)&&an(h),U=!b&&M,H=U&&z?L:null,W=z?null!=H?H:(null!=i?i:y)({rects:v,activeNodeRect:A,activeIndex:f,overIndex:h,index:x}):null,V=an(f)&&an(h)?o({id:s,items:u,activeIndex:f,overIndex:h}):x,X=null==D?void 0:D.id,Y=(0,n.useRef)({activeId:X,items:u,newIndex:V,containerId:m}),$=u!==Y.current.items,q=t({active:D,containerId:m,isDragging:M,isSorting:B,id:s,index:x,items:u,newIndex:Y.current.newIndex,previousItems:Y.current.items,previousContainerId:Y.current.containerId,transition:d,wasDragging:null!=Y.current.activeId}),K=function(e){let{disabled:t,index:a,node:r,rect:l}=e;const[o,s]=(0,n.useState)(null),i=(0,n.useRef)(a);return Ue((()=>{if(!t&&a!==i.current&&r.current){const e=l.current;if(e){const t=kt(r.current,{ignoreTransform:!0}),a={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(a.x||a.y)&&s(a)}}a!==i.current&&(i.current=a)}),[t,a,r,l]),(0,n.useEffect)((()=>{o&&s(null)}),[o]),o}({disabled:!q,index:x,node:C,rect:_});return(0,n.useEffect)((()=>{B&&Y.current.newIndex!==V&&(Y.current.newIndex=V),m!==Y.current.containerId&&(Y.current.containerId=m),u!==Y.current.items&&(Y.current.items=u)}),[B,V,m,u]),(0,n.useEffect)((()=>{if(X===Y.current.activeId)return;if(null!=X&&null==Y.current.activeId)return void(Y.current.activeId=X);const e=setTimeout((()=>{Y.current.activeId=X}),50);return()=>clearTimeout(e)}),[X]),{active:D,activeIndex:f,attributes:O,data:w,rect:_,index:x,newIndex:V,items:u,isOver:k,isSorting:B,isDragging:M,listeners:I,node:C,overIndex:h,over:T,setNodeRef:P,setActivatorNodeRef:j,setDroppableNodeRef:S,setDraggableNodeRef:F,transform:null!=K?K:W,transition:K||$&&Y.current.newIndex===x?pn:U&&!Qe(R)||!d?void 0:B||q?et.Transition.toString({...d,property:fn}):void 0}}$t.Down,$t.Right,$t.Up,$t.Left;const hn=e=>{let{transform:t,draggingNodeRect:a,windowRect:n}=e;return a&&n?function(e,t,a){const n={...e};return t.top+e.y<=a.top?n.y=a.top-t.top:t.bottom+e.y>=a.top+a.height&&(n.y=a.top+a.height-t.bottom),t.left+e.x<=a.left?n.x=a.left-t.left:t.right+e.x>=a.left+a.width&&(n.x=a.left+a.width-t.right),n}(t,a,n):t},bn=({idea:e,isDragging:t=!1})=>{const[a,r]=(0,n.useState)(!1),{attributes:l,listeners:o,setNodeRef:s,transform:i,transition:c,isDragging:d}=vn({id:e.id}),u={transform:et.Transform.toString(i),transition:c,opacity:t||d?.5:1};return(0,n.createElement)("div",{ref:s,style:u,...l,...o,className:"bg-white rounded-lg shadow p-3 cursor-move hover:shadow-md transition-shadow relative "+(t||d?"rotate-3 scale-105":"")},(0,n.createElement)("div",{className:"flex items-start justify-between mb-2"},(0,n.createElement)("h4",{className:"font-semibold text-gray-800 text-sm leading-tight flex-1 pr-2"},ke(e.title)),(0,n.createElement)("div",{className:"relative"},(0,n.createElement)("button",{onClick:e=>{e.stopPropagation(),r(!a)},className:"text-gray-400 hover:text-gray-600 p-1 rounded"},(0,n.createElement)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},(0,n.createElement)("path",{d:"M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"}))),a&&(0,n.createElement)("div",{className:"absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]"},(0,n.createElement)("button",{onClick:t=>{t.stopPropagation(),r(!1),console.log("Edit idea:",e.id)},className:"block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"},"Edit"),(0,n.createElement)("button",{onClick:t=>{t.stopPropagation(),r(!1),confirm("Are you sure you want to delete this idea?")&&console.log("Delete idea:",e.id)},className:"block w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50"},"Delete")))),(e.excerpt||e.content)&&(0,n.createElement)("p",{className:"text-xs text-gray-600 mb-3 line-clamp-2"},Se(e.excerpt||e.content,120)),e.category&&(0,n.createElement)("div",{className:"mb-3"},(0,n.createElement)("span",{className:"text-xs rounded-full px-2 py-0.5 font-medium",style:{backgroundColor:(f=e.category,{"feature-request":"#F3E8FF",bug:"#FEF2F2",improvement:"#ECFDF5",feedback:"#FFFBEB"}[f]||"#F3F4F6"),color:(e=>({"feature-request":"#8B5CF6",bug:"#EF4444",improvement:"#10B981",feedback:"#F59E0B"}[e]||"#6B7280"))(e.category)}},e.categories?.[0]?.name||e.category)),(0,n.createElement)("div",{className:"flex items-center justify-between"},(0,n.createElement)("div",{className:"flex items-center gap-2"},(0,n.createElement)("div",{className:"w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600"},(m=e.author_name||"Anonymous")?m.split(" ").map((e=>e.charAt(0))).join("").toUpperCase().slice(0,2):"?"),(0,n.createElement)("span",{className:"text-xs text-gray-500"},e.author_name||"Anonymous")),(0,n.createElement)("div",{className:"flex items-center gap-3 text-xs text-gray-500"},(0,n.createElement)("div",{className:"flex items-center gap-1"},(0,n.createElement)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},(0,n.createElement)("path",{d:"M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"})),(0,n.createElement)("span",null,e.vote_count||0)),(0,n.createElement)("div",{className:"flex items-center gap-1"},(0,n.createElement)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},(0,n.createElement)("path",{fillRule:"evenodd",d:"M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z",clipRule:"evenodd"})),(0,n.createElement)("span",null,e.comment_count||0)))),(0,n.createElement)("div",{className:"absolute top-2 left-2 w-2 h-2 rounded-full",style:{backgroundColor:e.status_color||"#6B7280"}}));var m,f},yn=({status:e,ideas:t,onAddItem:a})=>{const{setNodeRef:r,isOver:l}=Ha({id:e.slug});return(0,n.createElement)("div",{ref:r,className:"bg-white rounded-xl shadow-sm p-4 min-w-[280px] space-y-3 flex-1 transition-colors "+(l?"bg-blue-50 border-2 border-blue-300 border-dashed":"")},(0,n.createElement)("div",{className:"flex items-center justify-between mb-4"},(0,n.createElement)("div",{className:"flex items-center gap-3"},(0,n.createElement)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:e.color||"#6B7280"}}),(0,n.createElement)("h3",{className:"font-semibold text-gray-800"},e.name),(0,n.createElement)("span",{className:"bg-gray-100 text-gray-600 text-xs font-medium px-2 py-1 rounded-full"},t.length))),(0,n.createElement)("div",{className:"space-y-3 min-h-[200px]"},t.map((e=>(0,n.createElement)(bn,{key:e.id,idea:e}))),0===t.length&&(0,n.createElement)("div",{className:"text-center py-8 text-gray-400"},(0,n.createElement)("div",{className:"text-2xl mb-2"},"📋"),(0,n.createElement)("p",{className:"text-sm"},"No items yet"))),(0,n.createElement)("button",{onClick:a,className:"w-full border border-blue-500 text-blue-500 hover:bg-blue-50 rounded-md px-3 py-2 text-sm font-medium transition-colors flex items-center justify-center gap-2"},(0,n.createElement)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},(0,n.createElement)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})),"Add Item"))},En=({isOpen:e,onClose:t,selectedStatus:a,statuses:r,onItemAdded:l})=>{const[o,s]=(0,n.useState)({title:"",details:"",category:"",status:a||"",email:"",first_name:"",last_name:""}),[i,c]=(0,n.useState)([]),[d,u]=(0,n.useState)(!1);(0,n.useEffect)((()=>{e&&(m(),s((e=>({...e,status:a||""}))))}),[e,a]),(0,n.useEffect)((()=>{const a=a=>{"Escape"===a.key&&e&&t()};return e&&(document.addEventListener("keydown",a),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",a),document.body.style.overflow="unset"}}),[e,t]);const m=async()=>{try{const e=new FormData;e.append("action","feedlane_get_categories"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();a.success&&(c(a.data),!e.category&&a.data.length>0&&s((e=>({...e,category:a.data[0].slug}))))}catch(e){console.error("Error loading categories:",e)}},f=e=>{const{name:t,value:a}=e.target;s((e=>({...e,[t]:a})))},p=async e=>{if(e.preventDefault(),o.title.trim())if(o.details.trim())try{u(!0);const e=new FormData;e.append("action","feedlane_create_idea"),e.append("nonce",feedlaneAdmin.nonce),e.append("title",o.title),e.append("content",o.details),e.append("categories",JSON.stringify([o.category])),e.append("status",o.status),e.append("email",o.email),e.append("first_name",o.first_name),e.append("last_name",o.last_name);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();if(!n.success)throw new Error(n.data||"Failed to add idea");ae.success("Idea added successfully"),l(),s({title:"",details:"",category:i[0]?.slug||"",status:a||"",email:"",first_name:"",last_name:""})}catch(e){console.error("Error adding idea:",e),ae.error(e.message||"Failed to add idea")}finally{u(!1)}else ae.error("Details are required");else ae.error("Title is required")};return e?(0,n.createElement)("div",{className:"feedlane-modal",onClick:t},(0,n.createElement)("div",{className:"feedlane-modal__backdrop"}),(0,n.createElement)("div",{className:"feedlane-modal__container"},(0,n.createElement)("div",{className:"feedlane-modal__content max-w-2xl",onClick:e=>e.stopPropagation()},(0,n.createElement)("div",{className:"feedlane-modal__header"},(0,n.createElement)("h3",null,"Add New Roadmap Item"),(0,n.createElement)("button",{type:"button",onClick:t,className:"feedlane-modal__close","aria-label":"Close modal"},"✕")),(0,n.createElement)("div",{className:"feedlane-modal__body"},(0,n.createElement)("form",{onSubmit:p,className:"space-y-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-title"},"Title *"),(0,n.createElement)("input",{type:"text",id:"idea-title",name:"title",value:o.title,onChange:f,className:"feedlane-input",placeholder:"e.g., Add voting option within widget",required:!0,autoFocus:!0})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-details"},"Details *"),(0,n.createElement)("textarea",{id:"idea-details",name:"details",value:o.details,onChange:f,className:"feedlane-textarea",rows:"4",placeholder:"Describe the idea in detail...",required:!0})),(0,n.createElement)("div",{className:"grid grid-cols-2 gap-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-category"},"Category"),(0,n.createElement)("select",{id:"idea-category",name:"category",value:o.category,onChange:f,className:"feedlane-select"},i.map((e=>(0,n.createElement)("option",{key:e.slug,value:e.slug},e.name))))),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-status"},"Status"),(0,n.createElement)("select",{id:"idea-status",name:"status",value:o.status,onChange:f,className:"feedlane-select"},r.map((e=>(0,n.createElement)("option",{key:e.slug,value:e.slug},e.name)))))),(0,n.createElement)("div",{className:"grid grid-cols-3 gap-4"},(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-first-name"},"First Name"),(0,n.createElement)("input",{type:"text",id:"idea-first-name",name:"first_name",value:o.first_name,onChange:f,className:"feedlane-input",placeholder:"John"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-last-name"},"Last Name"),(0,n.createElement)("input",{type:"text",id:"idea-last-name",name:"last_name",value:o.last_name,onChange:f,className:"feedlane-input",placeholder:"Doe"})),(0,n.createElement)("div",{className:"feedlane-form__field"},(0,n.createElement)("label",{htmlFor:"idea-email"},"Email"),(0,n.createElement)("input",{type:"email",id:"idea-email",name:"email",value:o.email,onChange:f,className:"feedlane-input",placeholder:"<EMAIL>"}))))),(0,n.createElement)("div",{className:"feedlane-modal__footer"},(0,n.createElement)("button",{type:"button",onClick:t,className:"feedlane-btn feedlane-btn--secondary",disabled:d},"Cancel"),(0,n.createElement)("button",{type:"button",onClick:p,className:"feedlane-btn feedlane-btn--primary",disabled:d},d?"Adding...":"Add Item"))))):null},xn=()=>{const[e,t]=(0,n.useState)([]),[a,r]=(0,n.useState)([]),[l,o]=(0,n.useState)(!0),[s,i]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null),f=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.useMemo)((()=>[...t].filter((e=>null!=e))),[...t])}(mt(ra,{activationConstraint:{distance:8}}),mt(Zt));(0,n.useEffect)((()=>{p()}),[]);const p=async()=>{try{o(!0),await Promise.all([g(),v()])}catch(e){ae.error("Failed to load roadmap data")}finally{o(!1)}},g=async()=>{try{const e=new FormData;e.append("action","feedlane_get_status"),e.append("nonce",feedlaneAdmin.nonce);const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await a.json();if(!n.success)throw new Error(n.data||"Failed to load status");t(n.data)}catch(e){throw console.error("Error loading status:",e),e}},v=async()=>{try{const e=new FormData;e.append("action","feedlane_get_ideas"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),a=await t.json();if(!a.success)throw new Error(a.data||"Failed to load ideas");console.log("Loaded ideas:",a.data.ideas),r(a.data.ideas||[])}catch(e){throw console.error("Error loading ideas:",e),e}},h=e=>{const t=a.filter((t=>t.status===e));return console.log(`Ideas for status ${e}:`,t),t},b=s?a.find((e=>e.id===parseInt(s))):null;return l?(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Roadmap Manager"),(0,n.createElement)("p",null,"Manage your product roadmap with drag & drop")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)("div",{className:"feedlane-loading"},(0,n.createElement)("div",{className:"feedlane-loading__spinner"}),(0,n.createElement)("p",null,"Loading roadmap..."))))):(0,n.createElement)("div",{className:"feedlane-admin-wrapper"},(0,n.createElement)("div",{className:"feedlane-admin"},(0,n.createElement)("div",{className:"feedlane-admin__header"},(0,n.createElement)("h1",null,"Roadmap Manager"),(0,n.createElement)("p",null,"Manage your product roadmap with drag & drop")),(0,n.createElement)("div",{className:"feedlane-admin__content"},(0,n.createElement)(La,{sensors:f,collisionDetection:bt,onDragStart:e=>{i(e.active.id)},onDragEnd:async t=>{const{active:n,over:l}=t;if(i(null),!l)return;const o=n.id,s=l.id,c=a.find((e=>e.id===parseInt(o)));if(!c)return;let d=null;const u=e.find((e=>e.slug===s));if(u)d=u.slug;else{const e=a.find((e=>e.id===parseInt(s)));e&&(d=e.status)}if(d!==c.status)d&&d!==c.status&&await(async(e,t)=>{try{const a=new FormData;a.append("action","feedlane_update_idea_status"),a.append("nonce",feedlaneAdmin.nonce),a.append("idea_id",e),a.append("status",t);const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),l=await n.json();if(!l.success)throw new Error(l.data||"Failed to update idea status");r((a=>a.map((a=>a.id===e?{...a,status:t}:a)))),ae.success("Idea status updated successfully")}catch(e){console.error("Error updating idea status:",e),ae.error("Failed to update idea status")}})(c.id,d);else{const e=h(d),t=e.findIndex((e=>e.id===c.id)),n=e.findIndex((e=>e.id===parseInt(s)));if(t!==n){const l=en(e,t,n),o=a.map((e=>l.find((t=>t.id===e.id))||e));r(o)}}},modifiers:[hn]},(0,n.createElement)("div",{className:"flex gap-6 overflow-x-auto pb-6 min-h-[600px]"},e.map((e=>(0,n.createElement)(cn,{key:e.slug,items:h(e.slug).map((e=>e.id)),strategy:ln},(0,n.createElement)(yn,{status:e,ideas:h(e.slug),onAddItem:()=>{return t=e.slug,m(t),void d(!0);var t}}))))),(0,n.createElement)(Za,null,b?(0,n.createElement)(bn,{idea:b,isDragging:!0}):null)),c&&(0,n.createElement)(En,{isOpen:c,onClose:()=>d(!1),selectedStatus:u,statuses:e,onItemAdded:()=>{d(!1),m(null),v()}}))))},wn=({page:e})=>(0,n.createElement)(n.Fragment,null,(()=>{switch(e){case"dashboard":default:return(0,n.createElement)(ne,null);case"settings":return(0,n.createElement)(re,null);case"analytics":return(0,n.createElement)(le,null);case"ideas":return(0,n.createElement)(Re,null);case"categories":return(0,n.createElement)(Ae,null);case"roadmap":return(0,n.createElement)(xn,null);case"status":return(0,n.createElement)(Oe,null)}})(),(0,n.createElement)(te,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{console.log("React available:",void 0!==r()),console.log("createRoot available:",void 0!==l.H),console.log("wp.element available:","undefined"!=typeof wp&&void 0!==wp.element);const e=document.getElementById("feedlane-admin-dashboard");if(e)try{(0,l.H)(e).render((0,n.createElement)(wn,{page:"dashboard"}))}catch(e){console.error("Error creating dashboard root:",e)}const t=document.getElementById("feedlane-admin-settings");t&&(0,l.H)(t).render((0,n.createElement)(wn,{page:"settings"}));const a=document.getElementById("feedlane-admin-analytics");a&&(0,l.H)(a).render((0,n.createElement)(wn,{page:"analytics"}));const o=document.getElementById("feedlane-admin-ideas");o&&(0,l.H)(o).render((0,n.createElement)(wn,{page:"ideas"}));const s=document.getElementById("feedlane-admin-categories");s&&(0,l.H)(s).render((0,n.createElement)(wn,{page:"categories"}));const i=document.getElementById("feedlane-admin-roadmap");i&&(0,l.H)(i).render((0,n.createElement)(wn,{page:"roadmap"}));const c=document.getElementById("feedlane-admin-status");c&&(0,l.H)(c).render((0,n.createElement)(wn,{page:"status"}))}))})();