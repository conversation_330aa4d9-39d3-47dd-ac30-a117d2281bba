(()=>{"use strict";var e={576:(e,a,t)=>{var n=t(795);a.H=n.createRoot,n.hydrateRoot},795:e=>{e.exports=window.ReactDOM}},a={};const t=window.React;var n=function t(n){var l=a[n];if(void 0!==l)return l.exports;var s=a[n]={exports:{}};return e[n](s,s.exports,t),s.exports}(576);let l={data:""},s=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||l,r=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,o=(e,a)=>{let t="",n="",l="";for(let s in e){let r=e[s];"@"==s[0]?"i"==s[1]?t=s+" "+r+";":n+="f"==s[1]?o(r,s):s+"{"+o(r,"k"==s[1]?"":a)+"}":"object"==typeof r?n+=o(r,a?a.replace(/([^,])+/g,(e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(a=>/&/.test(a)?a.replace(/&/g,e):e?e+" "+a:a)))):s):null!=r&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),l+=o.p?o.p(s,r):s+":"+r+";")}return t+(a&&l?a+"{"+l+"}":l)+n},i={},m=e=>{if("object"==typeof e){let a="";for(let t in e)a+=t+m(e[t]);return a}return e},u=(e,a,t,n,l)=>{let s=m(e),u=i[s]||(i[s]=(e=>{let a=0,t=11;for(;a<e.length;)t=101*t+e.charCodeAt(a++)>>>0;return"go"+t})(s));if(!i[u]){let a=s!==e?e:(e=>{let a,t,n=[{}];for(;a=r.exec(e.replace(c,""));)a[4]?n.shift():a[3]?(t=a[3].replace(d," ").trim(),n.unshift(n[0][t]=n[0][t]||{})):n[0][a[1]]=a[2].replace(d," ").trim();return n[0]})(e);i[u]=o(l?{["@keyframes "+u]:a}:a,t?"":"."+u)}let f=t&&i.g?i.g:null;return t&&(i.g=i[u]),((e,a,t,n)=>{n?a.data=a.data.replace(n,e):-1===a.data.indexOf(e)&&(a.data=t?e+a.data:a.data+e)})(i[u],a,n,f),u};function f(e){let a=this||{},t=e.call?e(a.p):e;return u(t.unshift?t.raw?((e,a,t)=>e.reduce(((e,n,l)=>{let s=a[l];if(s&&s.call){let e=s(t),a=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=a?"."+a:e&&"object"==typeof e?e.props?"":o(e,""):!1===e?"":e}return e+n+(null==s?"":s)}),""))(t,[].slice.call(arguments,1),a.p):t.reduce(((e,t)=>Object.assign(e,t&&t.call?t(a.p):t)),{}):t,s(a.target),a.g,a.o,a.k)}f.bind({g:1});let p,E,g,b=f.bind({k:1});function y(e,a){let t=this||{};return function(){let n=arguments;function l(s,r){let c=Object.assign({},s),d=c.className||l.className;t.p=Object.assign({theme:E&&E()},c),t.o=/ *go\d+/.test(d),c.className=f.apply(t,n)+(d?" "+d:""),a&&(c.ref=r);let o=e;return e[0]&&(o=c.as||e,delete c.as),g&&o[0]&&g(c),p(o,c)}return a?a(l):l}}var v=(e,a)=>(e=>"function"==typeof e)(e)?e(a):e,h=(()=>{let e=0;return()=>(++e).toString()})(),_=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let a=matchMedia("(prefers-reduced-motion: reduce)");e=!a||a.matches}return e}})(),N=(e,a)=>{switch(a.type){case 0:return{...e,toasts:[a.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map((e=>e.id===a.toast.id?{...e,...a.toast}:e))};case 2:let{toast:t}=a;return N(e,{type:e.toasts.find((e=>e.id===t.id))?1:0,toast:t});case 3:let{toastId:n}=a;return{...e,toasts:e.toasts.map((e=>e.id===n||void 0===n?{...e,dismissed:!0,visible:!1}:e))};case 4:return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==a.toastId))};case 5:return{...e,pausedAt:a.time};case 6:let l=a.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map((e=>({...e,pauseDuration:e.pauseDuration+l})))}}},w=[],x={toasts:[],pausedAt:void 0},k=e=>{x=N(x,e),w.forEach((e=>{e(x)}))},C={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},S=e=>(a,t)=>{let n=((e,a="blank",t)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:a,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...t,id:(null==t?void 0:t.id)||h()}))(a,e,t);return k({type:2,toast:n}),n.id},F=(e,a)=>S("blank")(e,a);F.error=S("error"),F.success=S("success"),F.loading=S("loading"),F.custom=S("custom"),F.dismiss=e=>{k({type:3,toastId:e})},F.remove=e=>k({type:4,toastId:e}),F.promise=(e,a,t)=>{let n=F.loading(a.loading,{...t,...null==t?void 0:t.loading});return"function"==typeof e&&(e=e()),e.then((e=>{let l=a.success?v(a.success,e):void 0;return l?F.success(l,{id:n,...t,...null==t?void 0:t.success}):F.dismiss(n),e})).catch((e=>{let l=a.error?v(a.error,e):void 0;l?F.error(l,{id:n,...t,...null==t?void 0:t.error}):F.dismiss(n)})),e};var A=(e,a)=>{k({type:1,toast:{id:e,height:a}})},D=()=>{k({type:5,time:Date.now()})},j=new Map,P=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,O=b`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,I=b`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,T=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${P} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${O} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${I} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,M=b`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,$=y("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${M} 1s linear infinite;
`,B=b`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,R=b`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,L=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${B} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${R} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,H=y("div")`
  position: absolute;
`,z=y("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,V=b`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,U=y("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${V} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,q=({toast:e})=>{let{icon:a,type:n,iconTheme:l}=e;return void 0!==a?"string"==typeof a?t.createElement(U,null,a):a:"blank"===n?null:t.createElement(z,null,t.createElement($,{...l}),"loading"!==n&&t.createElement(H,null,"error"===n?t.createElement(T,{...l}):t.createElement(L,{...l})))},G=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,J=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,K=y("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Q=y("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,W=t.memo((({toast:e,position:a,style:n,children:l})=>{let s=e.height?((e,a)=>{let t=e.includes("top")?1:-1,[n,l]=_()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[G(t),J(t)];return{animation:a?`${b(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${b(l)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(e.position||a||"top-center",e.visible):{opacity:0},r=t.createElement(q,{toast:e}),c=t.createElement(Q,{...e.ariaProps},v(e.message,e));return t.createElement(K,{className:e.className,style:{...s,...n,...e.style}},"function"==typeof l?l({icon:r,message:c}):t.createElement(t.Fragment,null,r,c))}));!function(e){o.p=void 0,p=e,E=void 0,g=void 0}(t.createElement);var Y=({id:e,className:a,style:n,onHeightUpdate:l,children:s})=>{let r=t.useCallback((a=>{if(a){let t=()=>{let t=a.getBoundingClientRect().height;l(e,t)};t(),new MutationObserver(t).observe(a,{subtree:!0,childList:!0,characterData:!0})}}),[e,l]);return t.createElement("div",{ref:r,className:a,style:n},s)},Z=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,X=({reverseOrder:e,position:a="top-center",toastOptions:n,gutter:l,children:s,containerStyle:r,containerClassName:c})=>{let{toasts:d,handlers:o}=(e=>{let{toasts:a,pausedAt:n}=((e={})=>{let[a,n]=(0,t.useState)(x),l=(0,t.useRef)(x);(0,t.useEffect)((()=>(l.current!==x&&n(x),w.push(n),()=>{let e=w.indexOf(n);e>-1&&w.splice(e,1)})),[]);let s=a.toasts.map((a=>{var t,n,l;return{...e,...e[a.type],...a,removeDelay:a.removeDelay||(null==(t=e[a.type])?void 0:t.removeDelay)||(null==e?void 0:e.removeDelay),duration:a.duration||(null==(n=e[a.type])?void 0:n.duration)||(null==e?void 0:e.duration)||C[a.type],style:{...e.style,...null==(l=e[a.type])?void 0:l.style,...a.style}}}));return{...a,toasts:s}})(e);(0,t.useEffect)((()=>{if(n)return;let e=Date.now(),t=a.map((a=>{if(a.duration===1/0)return;let t=(a.duration||0)+a.pauseDuration-(e-a.createdAt);if(!(t<0))return setTimeout((()=>F.dismiss(a.id)),t);a.visible&&F.dismiss(a.id)}));return()=>{t.forEach((e=>e&&clearTimeout(e)))}}),[a,n]);let l=(0,t.useCallback)((()=>{n&&k({type:6,time:Date.now()})}),[n]),s=(0,t.useCallback)(((e,t)=>{let{reverseOrder:n=!1,gutter:l=8,defaultPosition:s}=t||{},r=a.filter((a=>(a.position||s)===(e.position||s)&&a.height)),c=r.findIndex((a=>a.id===e.id)),d=r.filter(((e,a)=>a<c&&e.visible)).length;return r.filter((e=>e.visible)).slice(...n?[d+1]:[0,d]).reduce(((e,a)=>e+(a.height||0)+l),0)}),[a]);return(0,t.useEffect)((()=>{a.forEach((e=>{if(e.dismissed)((e,a=1e3)=>{if(j.has(e))return;let t=setTimeout((()=>{j.delete(e),k({type:4,toastId:e})}),a);j.set(e,t)})(e.id,e.removeDelay);else{let a=j.get(e.id);a&&(clearTimeout(a),j.delete(e.id))}}))}),[a]),{toasts:a,handlers:{updateHeight:A,startPause:D,endPause:l,calculateOffset:s}}})(n);return t.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...r},className:c,onMouseEnter:o.startPause,onMouseLeave:o.endPause},d.map((n=>{let r=n.position||a,c=((e,a)=>{let t=e.includes("top"),n=t?{top:0}:{bottom:0},l=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:_()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${a*(t?1:-1)}px)`,...n,...l}})(r,o.calculateOffset(n,{reverseOrder:e,gutter:l,defaultPosition:a}));return t.createElement(Y,{id:n.id,key:n.id,onHeightUpdate:o.updateHeight,className:n.visible?Z:"",style:c},"custom"===n.type?v(n.message,n):s?s(n):t.createElement(W,{toast:n,position:r}))})))},ee=F;const ae=()=>(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Feedlane Dashboard"),(0,t.createElement)("p",null,"Overview of your feedback system")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-admin__grid"},(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},12),(0,t.createElement)("div",{className:"stat-label"},"Newsfeed Posts"),(0,t.createElement)("p",null,"Published announcements and updates")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},45),(0,t.createElement)("div",{className:"stat-label"},"Total Ideas"),(0,t.createElement)("p",null,"Ideas submitted by users")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},8),(0,t.createElement)("div",{className:"stat-label"},"Pending Ideas"),(0,t.createElement)("p",null,"Ideas awaiting approval")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},156),(0,t.createElement)("div",{className:"stat-label"},"Feedback Comments"),(0,t.createElement)("p",null,"User feedback on posts")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},234),(0,t.createElement)("div",{className:"stat-label"},"Total Votes"),(0,t.createElement)("p",null,"Votes cast on ideas")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("h3",null,"Quick Actions"),(0,t.createElement)("div",{className:"space-y-2"},(0,t.createElement)("a",{href:"post-new.php?post_type=feedlane_posts",className:"feedlane-btn feedlane-btn--primary feedlane-btn--small block text-center"},"Add Newsfeed Post"),(0,t.createElement)("a",{href:"admin.php?page=feedlane-ideas",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Manage Ideas"),(0,t.createElement)("a",{href:"admin.php?page=feedlane-settings",className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small block text-center"},"Settings"))))))),te=()=>{const[e,a]=(0,t.useState)({feedlane_enable_newsfeed:!0,feedlane_enable_ideas:!0,feedlane_enable_roadmap:!0,feedlane_enable_guest_submissions:!0,feedlane_enable_floating_sidebar:!0,feedlane_sidebar_position:"left",feedlane_primary_color:"#0ea5e9",feedlane_firebase_config:"",feedlane_firebase_webhook_secret:""}),[n,l]=(0,t.useState)(!1),[s,r]=(0,t.useState)(!0);(0,t.useEffect)((()=>{c()}),[]);const c=async()=>{try{const e=new FormData;e.append("action","feedlane_get_settings"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e});if(t.ok){const e=await t.json();e.success&&a(e.data)}}catch(e){console.error("Failed to load settings:",e)}finally{r(!1)}},d=e=>{const{name:t,value:n,type:l,checked:s}=e.target;a((e=>({...e,[t]:"checkbox"===l?s:n})))};return s?(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Feedlane Settings"),(0,t.createElement)("p",null,"Configure your feedback system")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-loading"},(0,t.createElement)("div",{className:"feedlane-loading__spinner"}),(0,t.createElement)("p",null,"Loading settings...")))):(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Feedlane Settings"),(0,t.createElement)("p",null,"Configure your feedback system")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("form",{onSubmit:async a=>{a.preventDefault(),l(!0);try{const a=new FormData;a.append("action","feedlane_save_settings"),a.append("nonce",feedlaneAdmin.nonce),Object.keys(e).forEach((t=>{a.append(t,e[t])}));const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?ee.success("Settings saved successfully!"):ee.error(n.data||"Failed to save settings")}catch(e){ee.error("Failed to save settings")}finally{l(!1)}},className:"feedlane-form"},(0,t.createElement)("div",{className:"feedlane-form__section"},(0,t.createElement)("h3",null,"General Settings"),(0,t.createElement)("p",null,"Configure which tabs are enabled and basic appearance"),(0,t.createElement)("div",{className:"space-y-4"},(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",null,(0,t.createElement)("input",{type:"checkbox",name:"feedlane_enable_newsfeed",checked:e.feedlane_enable_newsfeed,onChange:d,className:"mr-2"}),"Enable Newsfeed Tab"),(0,t.createElement)("div",{className:"description"},"Show the newsfeed tab in the sidebar")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",null,(0,t.createElement)("input",{type:"checkbox",name:"feedlane_enable_ideas",checked:e.feedlane_enable_ideas,onChange:d,className:"mr-2"}),"Enable Ideas Tab"),(0,t.createElement)("div",{className:"description"},"Show the ideas submission tab in the sidebar")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",null,(0,t.createElement)("input",{type:"checkbox",name:"feedlane_enable_roadmap",checked:e.feedlane_enable_roadmap,onChange:d,className:"mr-2"}),"Enable Roadmap Tab"),(0,t.createElement)("div",{className:"description"},"Show the roadmap tab in the sidebar")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",null,(0,t.createElement)("input",{type:"checkbox",name:"feedlane_enable_guest_submissions",checked:e.feedlane_enable_guest_submissions,onChange:d,className:"mr-2"}),"Enable Guest Submissions"),(0,t.createElement)("div",{className:"description"},"Allow non-logged-in users to submit feedback and ideas")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",null,(0,t.createElement)("input",{type:"checkbox",name:"feedlane_enable_floating_sidebar",checked:e.feedlane_enable_floating_sidebar,onChange:d,className:"mr-2"}),"Enable Floating Sidebar"),(0,t.createElement)("div",{className:"description"},"Show the floating feedback sidebar on all pages")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"feedlane_sidebar_position"},"Sidebar Position"),(0,t.createElement)("select",{id:"feedlane_sidebar_position",name:"feedlane_sidebar_position",value:e.feedlane_sidebar_position,onChange:d,className:"feedlane-select"},(0,t.createElement)("option",{value:"left"},"Left"),(0,t.createElement)("option",{value:"right"},"Right")),(0,t.createElement)("div",{className:"description"},"Choose which side of the screen the sidebar appears on")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"feedlane_primary_color"},"Primary Color"),(0,t.createElement)("div",{className:"feedlane-color-picker"},(0,t.createElement)("input",{type:"color",id:"feedlane_primary_color",name:"feedlane_primary_color",value:e.feedlane_primary_color,onChange:d,className:"feedlane-color-input"}),(0,t.createElement)("input",{type:"text",value:e.feedlane_primary_color,onChange:d,name:"feedlane_primary_color",className:"feedlane-color-text",placeholder:"#0ea5e9"})),(0,t.createElement)("div",{className:"description"},"Choose the primary color for the sidebar and buttons")))),(0,t.createElement)("div",{className:"feedlane-form__section"},(0,t.createElement)("h3",null,"Firebase Configuration"),(0,t.createElement)("p",null,"Configure Firebase for real-time comments functionality"),(0,t.createElement)("div",{className:"space-y-4"},(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"feedlane_firebase_config"},"Firebase Configuration JSON"),(0,t.createElement)("textarea",{id:"feedlane_firebase_config",name:"feedlane_firebase_config",value:e.feedlane_firebase_config,onChange:d,rows:"6",className:"feedlane-textarea",placeholder:'{"apiKey": "...", "authDomain": "...", "projectId": "..."}'}),(0,t.createElement)("div",{className:"description"},"Paste your Firebase configuration JSON here for real-time comments")),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"feedlane_firebase_webhook_secret"},"Webhook Secret"),(0,t.createElement)("input",{type:"password",id:"feedlane_firebase_webhook_secret",name:"feedlane_firebase_webhook_secret",value:e.feedlane_firebase_webhook_secret,onChange:d,className:"feedlane-input"}),(0,t.createElement)("div",{className:"description"},"Secret key for Firebase webhook authentication")))),(0,t.createElement)("div",{className:"feedlane-form__actions"},(0,t.createElement)("button",{type:"submit",disabled:n,className:"feedlane-btn feedlane-btn--primary"},n?(0,t.createElement)(t.Fragment,null,(0,t.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Saving..."):"Save Settings"))))))},ne=()=>{const e={totalViews:1234,totalReactions:567,totalVotes:234,topPosts:[{id:1,title:"New Feature Release",views:234,reactions:45},{id:2,title:"Bug Fix Update",views:189,reactions:32},{id:3,title:"Roadmap Update",views:156,reactions:28}],topIdeas:[{id:1,title:"Dark Mode Support",votes:89,category:"Feature Request"},{id:2,title:"Mobile App",votes:67,category:"Feature Request"},{id:3,title:"Better Search",votes:45,category:"Improvement"}]};return(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Analytics"),(0,t.createElement)("p",null,"Track engagement and performance metrics")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-admin__grid"},(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},e.totalViews),(0,t.createElement)("div",{className:"stat-label"},"Total Views"),(0,t.createElement)("p",null,"Post and idea views")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},e.totalReactions),(0,t.createElement)("div",{className:"stat-label"},"Total Reactions"),(0,t.createElement)("p",null,"Emoji reactions on posts")),(0,t.createElement)("div",{className:"feedlane-admin__card"},(0,t.createElement)("div",{className:"stat-number"},e.totalVotes),(0,t.createElement)("div",{className:"stat-label"},"Total Votes"),(0,t.createElement)("p",null,"Votes on ideas"))),(0,t.createElement)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8"},(0,t.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,t.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Posts"),(0,t.createElement)("div",{className:"space-y-3"},e.topPosts.map((e=>(0,t.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,t.createElement)("div",null,(0,t.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,t.createElement)("p",{className:"text-sm text-gray-500"},e.views," views • ",e.reactions," reactions"))))))),(0,t.createElement)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},(0,t.createElement)("h3",{className:"text-lg font-semibold text-gray-900 mb-4"},"Top Ideas"),(0,t.createElement)("div",{className:"space-y-3"},e.topIdeas.map((e=>(0,t.createElement)("div",{key:e.id,className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg"},(0,t.createElement)("div",null,(0,t.createElement)("h4",{className:"font-medium text-gray-900"},e.title),(0,t.createElement)("p",{className:"text-sm text-gray-500"},e.category," • ",e.votes," votes")))))))))))},le=()=>{const[e,a]=(0,t.useState)([]),[n,l]=(0,t.useState)(!0),[s,r]=(0,t.useState)(1),[c,d]=(0,t.useState)(1),[o,i]=(0,t.useState)("all");(0,t.useEffect)((()=>{m()}),[s,o]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_ideas"),e.append("nonce",feedlaneAdmin.nonce),e.append("page",s),e.append("per_page",20),e.append("status",o);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?(a(n.data.ideas),d(n.data.pages)):ee.error("Failed to load ideas")}catch(e){ee.error("Failed to load ideas")}finally{l(!1)}};return n?(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Ideas Management"),(0,t.createElement)("p",null,"Manage submitted ideas and feedback")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-loading"},(0,t.createElement)("div",{className:"feedlane-loading__spinner"}),(0,t.createElement)("p",null,"Loading ideas..."))))):(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Ideas Management"),(0,t.createElement)("p",null,"Manage submitted ideas and feedback")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"mb-6 flex items-center gap-4"},(0,t.createElement)("label",{htmlFor:"status-filter",className:"text-sm font-medium text-gray-700"},"Filter by status:"),(0,t.createElement)("select",{id:"status-filter",value:o,onChange:e=>i(e.target.value),className:"feedlane-select w-auto"},(0,t.createElement)("option",{value:"all"},"All Statuses"),(0,t.createElement)("option",{value:"publish"},"Published"),(0,t.createElement)("option",{value:"draft"},"Draft"),(0,t.createElement)("option",{value:"pending"},"Pending"),(0,t.createElement)("option",{value:"private"},"Private"))),e.length>0?(0,t.createElement)("div",{className:"feedlane-card"},(0,t.createElement)("div",{className:"feedlane-card__content"},(0,t.createElement)("table",{className:"feedlane-table"},(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,(0,t.createElement)("th",null,"Title"),(0,t.createElement)("th",null,"Author"),(0,t.createElement)("th",null,"Status"),(0,t.createElement)("th",null,"Categories"),(0,t.createElement)("th",null,"Votes"),(0,t.createElement)("th",null,"Date"),(0,t.createElement)("th",null,"Actions"))),(0,t.createElement)("tbody",null,e.map((e=>(0,t.createElement)("tr",{key:e.id},(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"font-medium text-gray-900"},e.title),e.content&&(0,t.createElement)("div",{className:"text-sm text-gray-500 mt-1"},e.content.substring(0,100),"...")),(0,t.createElement)("td",null,e.author),(0,t.createElement)("td",null,(e=>{const a={publish:{label:"Published",class:"feedlane-badge--success"},draft:{label:"Draft",class:"feedlane-badge--gray"},pending:{label:"Pending",class:"feedlane-badge--warning"},private:{label:"Private",class:"feedlane-badge--info"}}[e]||{label:e,class:"feedlane-badge--gray"};return(0,t.createElement)("span",{className:`feedlane-badge ${a.class}`},a.label)})(e.status)),(0,t.createElement)("td",null,e.categories.map((e=>(0,t.createElement)("span",{key:e.id,className:"feedlane-badge feedlane-badge--info mr-1"},e.name)))),(0,t.createElement)("td",null,(0,t.createElement)("span",{className:"font-medium"},e.votes)),(0,t.createElement)("td",null,new Date(e.date).toLocaleDateString()),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"feedlane-table__actions"},(0,t.createElement)("select",{value:e.status,onChange:a=>(async(e,a)=>{try{const t=new FormData;t.append("action","feedlane_update_idea_status"),t.append("nonce",feedlaneAdmin.nonce),t.append("idea_id",e),t.append("status",a);const n=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:t}),l=await n.json();l.success?(ee.success("Status updated successfully"),m()):ee.error(l.data||"Failed to update status")}catch(e){ee.error("Failed to update status")}})(e.id,a.target.value),className:"feedlane-select text-xs"},(0,t.createElement)("option",{value:"publish"},"Published"),(0,t.createElement)("option",{value:"draft"},"Draft"),(0,t.createElement)("option",{value:"pending"},"Pending"),(0,t.createElement)("option",{value:"private"},"Private")),(0,t.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this idea?"))try{const a=new FormData;a.append("action","feedlane_delete_idea"),a.append("nonce",feedlaneAdmin.nonce),a.append("idea_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(ee.success("Idea deleted successfully"),m()):ee.error(n.data||"Failed to delete idea")}catch(e){ee.error("Failed to delete idea")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete"))))))))),c>1&&(0,t.createElement)("div",{className:"feedlane-pagination"},(0,t.createElement)("div",{className:"feedlane-pagination__info"},"Page ",s," of ",c),(0,t.createElement)("div",{className:"feedlane-pagination__nav"},(0,t.createElement)("button",{onClick:()=>r(s-1),disabled:1===s,className:"feedlane-pagination__btn"},"Previous"),(0,t.createElement)("button",{onClick:()=>r(s+1),disabled:s===c,className:"feedlane-pagination__btn"},"Next")))):(0,t.createElement)("div",{className:"feedlane-empty"},(0,t.createElement)("div",{className:"feedlane-empty__icon"},"💡"),(0,t.createElement)("h3",null,"No Ideas Found"),(0,t.createElement)("p",null,"No ideas have been submitted yet or match your current filter.")))))},se=()=>{const[e,a]=(0,t.useState)([]),[n,l]=(0,t.useState)(!0),[s,r]=(0,t.useState)(!1),[c,d]=(0,t.useState)(null),[o,i]=(0,t.useState)({name:"",slug:"",description:"",color:"#10B981"});(0,t.useEffect)((()=>{m()}),[]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_categories"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?a(n.data):ee.error("Failed to load categories")}catch(e){ee.error("Failed to load categories")}finally{l(!1)}},u=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_category"),e.append("nonce",feedlaneAdmin.nonce),c&&e.append("category_id",c.id),Object.keys(o).forEach((a=>{e.append(a,o[a])}));const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),t=await a.json();t.success?(ee.success(c?"Category updated successfully":"Category created successfully"),r(!1),d(null),i({name:"",slug:"",description:"",color:"#10B981"}),m()):ee.error(t.data||"Failed to save category")}catch(e){ee.error("Failed to save category")}},f=e=>{const{name:a,value:t}=e.target;i((e=>({...e,[a]:t})))};return n?(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Categories Management"),(0,t.createElement)("p",null,"Manage idea categories")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-loading"},(0,t.createElement)("div",{className:"feedlane-loading__spinner"}),(0,t.createElement)("p",null,"Loading categories..."))))):(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Categories Management"),(0,t.createElement)("p",null,"Manage idea categories")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"mb-6"},(0,t.createElement)("button",{onClick:()=>{d(null),i({name:"",slug:"",description:"",color:"#10B981"}),r(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Category")),e.length>0?(0,t.createElement)("div",{className:"feedlane-card"},(0,t.createElement)("div",{className:"feedlane-card__content"},(0,t.createElement)("table",{className:"feedlane-table"},(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,(0,t.createElement)("th",null,"Name"),(0,t.createElement)("th",null,"Slug"),(0,t.createElement)("th",null,"Description"),(0,t.createElement)("th",null,"Color"),(0,t.createElement)("th",null,"Ideas Count"),(0,t.createElement)("th",null,"Actions"))),(0,t.createElement)("tbody",null,e.map((e=>(0,t.createElement)("tr",{key:e.id},(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,t.createElement)("td",null,(0,t.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"flex items-center gap-2"},(0,t.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,t.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,t.createElement)("td",null,(0,t.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"feedlane-table__actions"},(0,t.createElement)("button",{onClick:()=>(e=>{d(e),i({name:e.name,slug:e.slug,description:e.description,color:e.color}),r(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,t.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this category?"))try{const a=new FormData;a.append("action","feedlane_delete_category"),a.append("nonce",feedlaneAdmin.nonce),a.append("category_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(ee.success("Category deleted successfully"),m()):ee.error(n.data||"Failed to delete category")}catch(e){ee.error("Failed to delete category")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,t.createElement)("div",{className:"feedlane-empty"},(0,t.createElement)("div",{className:"feedlane-empty__icon"},"🏷️"),(0,t.createElement)("h3",null,"No Categories Found"),(0,t.createElement)("p",null,"Create your first category to organize ideas.")),s&&(0,t.createElement)("div",{className:"feedlane-modal"},(0,t.createElement)("div",{className:"feedlane-modal__backdrop",onClick:()=>r(!1)}),(0,t.createElement)("div",{className:"feedlane-modal__container"},(0,t.createElement)("div",{className:"feedlane-modal__content"},(0,t.createElement)("div",{className:"feedlane-modal__header"},(0,t.createElement)("h3",null,c?"Edit Category":"Add New Category"),(0,t.createElement)("button",{onClick:()=>r(!1),className:"feedlane-modal__close"},"✕")),(0,t.createElement)("div",{className:"feedlane-modal__body"},(0,t.createElement)("form",{onSubmit:u,className:"space-y-4"},(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"category-name"},"Name *"),(0,t.createElement)("input",{type:"text",id:"category-name",name:"name",value:o.name,onChange:f,className:"feedlane-input",required:!0})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"category-slug"},"Slug"),(0,t.createElement)("input",{type:"text",id:"category-slug",name:"slug",value:o.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"category-description"},"Description"),(0,t.createElement)("textarea",{id:"category-description",name:"description",value:o.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"category-color"},"Color"),(0,t.createElement)("div",{className:"feedlane-color-picker"},(0,t.createElement)("input",{type:"color",id:"category-color",name:"color",value:o.color,onChange:f,className:"feedlane-color-input"}),(0,t.createElement)("input",{type:"text",value:o.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#10B981"}))))),(0,t.createElement)("div",{className:"feedlane-modal__footer"},(0,t.createElement)("button",{type:"button",onClick:()=>r(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,t.createElement)("button",{onClick:u,className:"feedlane-btn feedlane-btn--primary"},c?"Update Category":"Create Category"))))))))},re=()=>{const[e,a]=(0,t.useState)([]),[n,l]=(0,t.useState)(!0),[s,r]=(0,t.useState)(!1),[c,d]=(0,t.useState)(null),[o,i]=(0,t.useState)({name:"",slug:"",description:"",color:"#6B7280"});(0,t.useEffect)((()=>{m()}),[]);const m=async()=>{try{l(!0);const e=new FormData;e.append("action","feedlane_get_statuses"),e.append("nonce",feedlaneAdmin.nonce);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),n=await t.json();n.success?a(n.data):ee.error("Failed to load statuses")}catch(e){ee.error("Failed to load statuses")}finally{l(!1)}},u=async e=>{e.preventDefault();try{const e=new FormData;e.append("action","feedlane_save_status"),e.append("nonce",feedlaneAdmin.nonce),c&&e.append("status_id",c.id),Object.keys(o).forEach((a=>{e.append(a,o[a])}));const a=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:e}),t=await a.json();t.success?(ee.success(c?"Status updated successfully":"Status created successfully"),r(!1),d(null),i({name:"",slug:"",description:"",color:"#6B7280"}),m()):ee.error(t.data||"Failed to save status")}catch(e){ee.error("Failed to save status")}},f=e=>{const{name:a,value:t}=e.target;i((e=>({...e,[a]:t})))};return n?(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Status Management"),(0,t.createElement)("p",null,"Manage roadmap statuses")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"feedlane-loading"},(0,t.createElement)("div",{className:"feedlane-loading__spinner"}),(0,t.createElement)("p",null,"Loading statuses..."))))):(0,t.createElement)("div",{className:"feedlane-admin-wrapper"},(0,t.createElement)("div",{className:"feedlane-admin"},(0,t.createElement)("div",{className:"feedlane-admin__header"},(0,t.createElement)("h1",null,"Status Management"),(0,t.createElement)("p",null,"Manage roadmap statuses")),(0,t.createElement)("div",{className:"feedlane-admin__content"},(0,t.createElement)("div",{className:"mb-6"},(0,t.createElement)("button",{onClick:()=>{d(null),i({name:"",slug:"",description:"",color:"#6B7280"}),r(!0)},className:"feedlane-btn feedlane-btn--primary"},"Add New Status")),e.length>0?(0,t.createElement)("div",{className:"feedlane-card"},(0,t.createElement)("div",{className:"feedlane-card__content"},(0,t.createElement)("table",{className:"feedlane-table"},(0,t.createElement)("thead",null,(0,t.createElement)("tr",null,(0,t.createElement)("th",null,"Name"),(0,t.createElement)("th",null,"Slug"),(0,t.createElement)("th",null,"Description"),(0,t.createElement)("th",null,"Color"),(0,t.createElement)("th",null,"Ideas Count"),(0,t.createElement)("th",null,"Actions"))),(0,t.createElement)("tbody",null,e.map((e=>(0,t.createElement)("tr",{key:e.id},(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"font-medium text-gray-900"},e.name)),(0,t.createElement)("td",null,(0,t.createElement)("code",{className:"text-sm bg-gray-100 px-2 py-1 rounded"},e.slug)),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"text-sm text-gray-600"},e.description||"No description")),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"flex items-center gap-2"},(0,t.createElement)("div",{className:"w-6 h-6 rounded border border-gray-300",style:{backgroundColor:e.color}}),(0,t.createElement)("span",{className:"text-sm text-gray-600"},e.color))),(0,t.createElement)("td",null,(0,t.createElement)("span",{className:"feedlane-badge feedlane-badge--info"},e.count)),(0,t.createElement)("td",null,(0,t.createElement)("div",{className:"feedlane-table__actions"},(0,t.createElement)("button",{onClick:()=>(e=>{d(e),i({name:e.name,slug:e.slug,description:e.description,color:e.color}),r(!0)})(e),className:"feedlane-btn feedlane-btn--secondary feedlane-btn--small"},"Edit"),(0,t.createElement)("button",{onClick:()=>(async e=>{if(confirm("Are you sure you want to delete this status?"))try{const a=new FormData;a.append("action","feedlane_delete_status"),a.append("nonce",feedlaneAdmin.nonce),a.append("status_id",e);const t=await fetch(feedlaneAdmin.ajax_url,{method:"POST",body:a}),n=await t.json();n.success?(ee.success("Status deleted successfully"),m()):ee.error(n.data||"Failed to delete status")}catch(e){ee.error("Failed to delete status")}})(e.id),className:"feedlane-btn feedlane-btn--danger feedlane-btn--small"},"Delete")))))))))):(0,t.createElement)("div",{className:"feedlane-empty"},(0,t.createElement)("div",{className:"feedlane-empty__icon"},"📊"),(0,t.createElement)("h3",null,"No Statuses Found"),(0,t.createElement)("p",null,"Create your first status to track idea progress.")),s&&(0,t.createElement)("div",{className:"feedlane-modal"},(0,t.createElement)("div",{className:"feedlane-modal__backdrop",onClick:()=>r(!1)}),(0,t.createElement)("div",{className:"feedlane-modal__container"},(0,t.createElement)("div",{className:"feedlane-modal__content"},(0,t.createElement)("div",{className:"feedlane-modal__header"},(0,t.createElement)("h3",null,c?"Edit Status":"Add New Status"),(0,t.createElement)("button",{onClick:()=>r(!1),className:"feedlane-modal__close"},"✕")),(0,t.createElement)("div",{className:"feedlane-modal__body"},(0,t.createElement)("form",{onSubmit:u,className:"space-y-4"},(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"status-name"},"Name *"),(0,t.createElement)("input",{type:"text",id:"status-name",name:"name",value:o.name,onChange:f,className:"feedlane-input",required:!0})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"status-slug"},"Slug"),(0,t.createElement)("input",{type:"text",id:"status-slug",name:"slug",value:o.slug,onChange:f,className:"feedlane-input",placeholder:"Leave empty to auto-generate"})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"status-description"},"Description"),(0,t.createElement)("textarea",{id:"status-description",name:"description",value:o.description,onChange:f,className:"feedlane-textarea",rows:"3"})),(0,t.createElement)("div",{className:"feedlane-form__field"},(0,t.createElement)("label",{htmlFor:"status-color"},"Color"),(0,t.createElement)("div",{className:"feedlane-color-picker"},(0,t.createElement)("input",{type:"color",id:"status-color",name:"color",value:o.color,onChange:f,className:"feedlane-color-input"}),(0,t.createElement)("input",{type:"text",value:o.color,onChange:f,name:"color",className:"feedlane-color-text",placeholder:"#6B7280"}))))),(0,t.createElement)("div",{className:"feedlane-modal__footer"},(0,t.createElement)("button",{type:"button",onClick:()=>r(!1),className:"feedlane-btn feedlane-btn--secondary"},"Cancel"),(0,t.createElement)("button",{onClick:u,className:"feedlane-btn feedlane-btn--primary"},c?"Update Status":"Create Status"))))))))},ce=({page:e})=>(0,t.createElement)(t.Fragment,null,(()=>{switch(e){case"dashboard":default:return(0,t.createElement)(ae,null);case"settings":return(0,t.createElement)(te,null);case"analytics":return(0,t.createElement)(ne,null);case"ideas":return(0,t.createElement)(le,null);case"categories":return(0,t.createElement)(se,null);case"statuses":return(0,t.createElement)(re,null)}})(),(0,t.createElement)(X,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#4ade80",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}}));document.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("feedlane-admin-dashboard");e&&(0,n.H)(e).render((0,t.createElement)(ce,{page:"dashboard"}));const a=document.getElementById("feedlane-admin-settings");a&&(0,n.H)(a).render((0,t.createElement)(ce,{page:"settings"}));const l=document.getElementById("feedlane-admin-analytics");l&&(0,n.H)(l).render((0,t.createElement)(ce,{page:"analytics"}));const s=document.getElementById("feedlane-admin-ideas");s&&(0,n.H)(s).render((0,t.createElement)(ce,{page:"ideas"}));const r=document.getElementById("feedlane-admin-categories");r&&(0,n.H)(r).render((0,t.createElement)(ce,{page:"categories"}));const c=document.getElementById("feedlane-admin-statuses");c&&(0,n.H)(c).render((0,t.createElement)(ce,{page:"statuses"}))}))})();