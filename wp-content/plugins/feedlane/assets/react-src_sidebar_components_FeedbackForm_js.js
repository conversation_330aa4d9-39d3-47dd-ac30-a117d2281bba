"use strict";
(globalThis["webpackChunkfeedlane"] = globalThis["webpackChunkfeedlane"] || []).push([["react-src_sidebar_components_FeedbackForm_js"],{

/***/ "./react-src/sidebar/api/feedback.js":
/*!*******************************************!*\
  !*** ./react-src/sidebar/api/feedback.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   fetchFeedback: () => (/* binding */ fetchFeedback),
/* harmony export */   submitFeedback: () => (/* binding */ submitFeedback)
/* harmony export */ });
/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ "./react-src/sidebar/api/client.js");


/**
 * Submit feedback for a post
 */
const submitFeedback = async (postId, feedbackData) => {
  return _client__WEBPACK_IMPORTED_MODULE_0__["default"].post('/feedback', {
    post_id: postId,
    ...feedbackData
  });
};

/**
 * Fetch feedback for a post
 */
const fetchFeedback = async (postId, params = {}) => {
  const defaultParams = {
    page: 1,
    per_page: 10
  };
  const queryParams = {
    ...defaultParams,
    ...params
  };
  return _client__WEBPACK_IMPORTED_MODULE_0__["default"].get(`/feedback/${postId}`, queryParams);
};

/***/ }),

/***/ "./react-src/sidebar/components/FeedbackForm.js":
/*!******************************************************!*\
  !*** ./react-src/sidebar/components/FeedbackForm.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js");
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useQuery.js");
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js");
/* harmony import */ var react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-icons/fi */ "./node_modules/.pnpm/react-icons@4.12.0_react@18.3.1/node_modules/react-icons/fi/index.esm.js");
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ "./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs");
/* harmony import */ var _api_feedback__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../api/feedback */ "./react-src/sidebar/api/feedback.js");






const FeedbackForm = ({
  postId
}) => {
  const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [showFeedback, setShowFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({
    feedback_text: '',
    user_name: '',
    user_email: ''
  });
  const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();
  const isLoggedIn = window.feedlaneData?.is_user_logged_in || false;
  const guestSubmissionsEnabled = window.feedlaneData?.settings?.enable_guest_submissions || false;

  // Fetch existing feedback
  const {
    data: feedbackData,
    isLoading: feedbackLoading
  } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({
    queryKey: ['feedback', postId],
    queryFn: () => (0,_api_feedback__WEBPACK_IMPORTED_MODULE_2__.fetchFeedback)(postId),
    enabled: showFeedback,
    staleTime: 2 * 60 * 1000
  });

  // Submit feedback mutation
  const submitMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({
    mutationFn: data => (0,_api_feedback__WEBPACK_IMPORTED_MODULE_2__.submitFeedback)(postId, data),
    onSuccess: () => {
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].success('Feedback submitted successfully!');
      setFormData({
        feedback_text: '',
        user_name: '',
        user_email: ''
      });
      setIsExpanded(false);

      // Invalidate feedback query to refresh the list
      queryClient.invalidateQueries(['feedback', postId]);
    },
    onError: error => {
      const message = error.response?.data?.message || 'Failed to submit feedback';
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error(message);
    }
  });
  const handleSubmit = e => {
    e.preventDefault();
    if (!formData.feedback_text.trim()) {
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Please enter your feedback');
      return;
    }

    // Validate guest user fields
    if (!isLoggedIn && guestSubmissionsEnabled) {
      if (!formData.user_name.trim() || !formData.user_email.trim()) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Name and email are required');
        return;
      }
      if (!isValidEmail(formData.user_email)) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Please enter a valid email address');
        return;
      }
    }
    submitMutation.mutate(formData);
  };
  const handleInputChange = e => {
    const {
      name,
      value
    } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const isValidEmail = email => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
  const canSubmitFeedback = isLoggedIn || guestSubmissionsEnabled;
  if (!canSubmitFeedback) {
    return null;
  }
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__toggle"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: () => setIsExpanded(!isExpanded),
    className: "feedlane-feedback__toggle-button"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiMessageCircle, {
    size: 16
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, "Leave Feedback"), isExpanded ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiChevronUp, {
    size: 16
  }) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiChevronDown, {
    size: 16
  })), feedbackData && feedbackData.pagination.total > 0 && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: () => setShowFeedback(!showFeedback),
    className: "feedlane-feedback__view-button"
  }, "View ", feedbackData.pagination.total, " feedback", showFeedback ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiChevronUp, {
    size: 14
  }) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiChevronDown, {
    size: 14
  }))), isExpanded && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("form", {
    onSubmit: handleSubmit,
    className: "feedlane-feedback__form"
  }, !isLoggedIn && guestSubmissionsEnabled && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__guest-fields"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-row"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "text",
    name: "user_name",
    value: formData.user_name,
    onChange: handleInputChange,
    placeholder: "Your name",
    className: "feedlane-input feedlane-input--small",
    required: true
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "email",
    name: "user_email",
    value: formData.user_email,
    onChange: handleInputChange,
    placeholder: "Your email",
    className: "feedlane-input feedlane-input--small",
    required: true
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__text-field"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("textarea", {
    name: "feedback_text",
    value: formData.feedback_text,
    onChange: handleInputChange,
    placeholder: "Share your thoughts about this update...",
    className: "feedlane-textarea",
    rows: "3",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__actions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "button",
    onClick: () => setIsExpanded(false),
    className: "feedlane-button feedlane-button--secondary feedlane-button--small"
  }, "Cancel"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "submit",
    disabled: submitMutation.isPending,
    className: "feedlane-button feedlane-button--primary feedlane-button--small"
  }, submitMutation.isPending ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-spinner feedlane-spinner--small"
  }), "Sending...") : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSend, {
    size: 14
  }), "Send Feedback")))), showFeedback && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__list"
  }, feedbackLoading ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__loading"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-spinner"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, "Loading feedback...")) : feedbackData && feedbackData.feedback.length > 0 ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__items"
  }, feedbackData.feedback.map(item => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    key: item.id,
    className: "feedlane-feedback__item"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__item-header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "feedlane-feedback__author"
  }, item.author.name), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "feedlane-feedback__time"
  }, item.time_ago)), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__item-content"
  }, item.feedback_text)))) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-feedback__empty"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("p", null, "No feedback yet. Be the first to share your thoughts!"))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeedbackForm);

/***/ })

}]);
//# sourceMappingURL=react-src_sidebar_components_FeedbackForm_js.js.map