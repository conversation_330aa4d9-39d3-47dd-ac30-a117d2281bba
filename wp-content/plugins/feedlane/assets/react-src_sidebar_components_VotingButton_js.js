"use strict";
(globalThis["webpackChunkfeedlane"] = globalThis["webpackChunkfeedlane"] || []).push([["react-src_sidebar_components_VotingButton_js"],{

/***/ "./react-src/sidebar/components/VotingButton.js":
/*!******************************************************!*\
  !*** ./react-src/sidebar/components/VotingButton.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.79.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js");
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.79.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js");
/* harmony import */ var react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-icons/fi */ "./node_modules/.pnpm/react-icons@4.12.0_react@18.3.1/node_modules/react-icons/fi/index.esm.js");
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ "./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs");
/* harmony import */ var _api_ideas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../api/ideas */ "./react-src/sidebar/api/ideas.js");






const VotingButton = ({
  ideaId,
  hasVoted = false,
  voteCount = 0
}) => {
  const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();
  const voteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({
    mutationFn: () => (0,_api_ideas__WEBPACK_IMPORTED_MODULE_2__.voteIdea)(ideaId),
    onSuccess: data => {
      // Update the ideas query cache
      queryClient.setQueryData(['ideas'], oldData => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          ideas: oldData.ideas.map(idea => idea.id === ideaId ? {
            ...idea,
            vote_count: data.vote_count,
            has_voted: true
          } : idea)
        };
      });

      // Also update individual idea cache if it exists
      queryClient.setQueryData(['idea', ideaId], oldData => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          vote_count: data.vote_count,
          has_voted: true
        };
      });
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].success('Vote recorded!');
    },
    onError: error => {
      const message = error.response?.data?.message || 'Failed to vote';
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error(message);
    }
  });
  const handleVote = () => {
    if (hasVoted) {
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].info('You have already voted on this idea');
      return;
    }
    voteMutation.mutate();
  };
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: handleVote,
    disabled: voteMutation.isPending || hasVoted,
    className: `feedlane-vote-button ${hasVoted ? 'feedlane-vote-button--voted' : ''}`,
    title: hasVoted ? 'You have voted' : 'Vote for this idea'
  }, voteMutation.isPending ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-spinner feedlane-spinner--small"
  }) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiChevronUp, {
    size: 16
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    className: "feedlane-vote-button__count"
  }, voteCount));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VotingButton);

/***/ })

}]);
//# sourceMappingURL=react-src_sidebar_components_VotingButton_js.js.map