"use strict";
(globalThis["webpackChunkfeedlane"] = globalThis["webpackChunkfeedlane"] || []).push([["react-src_sidebar_components_ReactionButtons_js"],{

/***/ "./react-src/sidebar/api/reactions.js":
/*!********************************************!*\
  !*** ./react-src/sidebar/api/reactions.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addReaction: () => (/* binding */ addReaction),
/* harmony export */   fetchReactions: () => (/* binding */ fetchReactions),
/* harmony export */   removeReaction: () => (/* binding */ removeReaction)
/* harmony export */ });
/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ "./react-src/sidebar/api/client.js");


/**
 * Fetch reactions for a post
 */
const fetchReactions = async postId => {
  return _client__WEBPACK_IMPORTED_MODULE_0__["default"].get(`/reactions/${postId}`);
};

/**
 * Add reaction to a post
 */
const addReaction = async (postId, reactionType) => {
  return _client__WEBPACK_IMPORTED_MODULE_0__["default"].post('/reactions', {
    post_id: postId,
    reaction_type: reactionType
  });
};

/**
 * Remove reaction from a post
 */
const removeReaction = async (postId, reactionType) => {
  return _client__WEBPACK_IMPORTED_MODULE_0__["default"].delete(`/reactions/${postId}/${reactionType}`);
};

/***/ }),

/***/ "./react-src/sidebar/components/ReactionButtons.js":
/*!*********************************************************!*\
  !*** ./react-src/sidebar/components/ReactionButtons.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js");
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useQuery.js");
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js");
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ "./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs");
/* harmony import */ var _api_reactions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../api/reactions */ "./react-src/sidebar/api/reactions.js");





const ReactionButtons = ({
  postId
}) => {
  const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();
  const {
    data: reactionData,
    isLoading,
    error
  } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({
    queryKey: ['reactions', postId],
    queryFn: () => (0,_api_reactions__WEBPACK_IMPORTED_MODULE_2__.fetchReactions)(postId),
    staleTime: 2 * 60 * 1000 // 2 minutes
  });
  const addReactionMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({
    mutationFn: ({
      postId,
      reactionType
    }) => (0,_api_reactions__WEBPACK_IMPORTED_MODULE_2__.addReaction)(postId, reactionType),
    onSuccess: data => {
      queryClient.setQueryData(['reactions', postId], data);
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].success('Reaction added!');
    },
    onError: error => {
      const message = error.response?.data?.message || 'Failed to add reaction';
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error(message);
    }
  });
  const removeReactionMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_5__.useMutation)({
    mutationFn: ({
      postId,
      reactionType
    }) => (0,_api_reactions__WEBPACK_IMPORTED_MODULE_2__.removeReaction)(postId, reactionType),
    onSuccess: data => {
      queryClient.setQueryData(['reactions', postId], data);
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].success('Reaction removed!');
    },
    onError: error => {
      const message = error.response?.data?.message || 'Failed to remove reaction';
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error(message);
    }
  });
  const handleReactionClick = reactionType => {
    if (!reactionData) return;
    const isCurrentReaction = reactionData.user_reaction === reactionType;
    if (isCurrentReaction) {
      // Remove current reaction
      removeReactionMutation.mutate({
        postId,
        reactionType
      });
    } else {
      // Add new reaction (this will replace any existing reaction)
      addReactionMutation.mutate({
        postId,
        reactionType
      });
    }
  };
  if (isLoading) {
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "feedlane-reactions feedlane-reactions--loading"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "feedlane-reactions__skeleton"
    }, [1, 2, 3].map(i => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      key: i,
      className: "feedlane-reaction-button feedlane-reaction-button--skeleton"
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "feedlane-reaction-button__emoji"
    }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
      className: "feedlane-reaction-button__count"
    })))));
  }
  if (error || !reactionData) {
    return null; // Fail silently for reactions
  }
  const {
    reaction_counts = {},
    user_reaction,
    available_reactions = {}
  } = reactionData;
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-reactions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-reactions__buttons"
  }, Object.entries(available_reactions).map(([reactionType, emoji]) => {
    const count = reaction_counts[reactionType] || 0;
    const isActive = user_reaction === reactionType;
    const isLoading = addReactionMutation.isPending || removeReactionMutation.isPending;
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
      key: reactionType,
      onClick: () => handleReactionClick(reactionType),
      disabled: isLoading,
      className: `feedlane-reaction-button ${isActive ? 'feedlane-reaction-button--active' : ''}`,
      title: `React with ${reactionType}`
    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
      className: "feedlane-reaction-button__emoji"
    }, emoji), count > 0 && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
      className: "feedlane-reaction-button__count"
    }, count));
  })), Object.values(reaction_counts).some(count => count > 0) && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-reactions__summary"
  }, Object.entries(reaction_counts).filter(([_, count]) => count > 0).map(([reactionType, count]) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", {
    key: reactionType,
    className: "feedlane-reactions__summary-item"
  }, available_reactions[reactionType], " ", count))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactionButtons);

/***/ })

}]);
//# sourceMappingURL=react-src_sidebar_components_ReactionButtons_js.js.map