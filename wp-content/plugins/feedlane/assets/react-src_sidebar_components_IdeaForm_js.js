"use strict";
(globalThis["webpackChunkfeedlane"] = globalThis["webpackChunkfeedlane"] || []).push([["react-src_sidebar_components_IdeaForm_js"],{

/***/ "./react-src/sidebar/components/IdeaForm.js":
/*!**************************************************!*\
  !*** ./react-src/sidebar/components/IdeaForm.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ "./node_modules/.pnpm/@tanstack+react-query@5.77.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/useMutation.js");
/* harmony import */ var react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-icons/fi */ "./node_modules/.pnpm/react-icons@4.12.0_react@18.3.1/node_modules/react-icons/fi/index.esm.js");
/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ "./node_modules/.pnpm/react-hot-toast@2.5.2_react-dom@18.3.1_react@18.3.1/node_modules/react-hot-toast/dist/index.mjs");
/* harmony import */ var _api_ideas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../api/ideas */ "./react-src/sidebar/api/ideas.js");






const IdeaForm = ({
  onSuccess,
  onCancel
}) => {
  const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({
    title: '',
    details: '',
    category: 'feature-request',
    email: '',
    first_name: '',
    last_name: '',
    image: null
  });
  const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);
  const isLoggedIn = window.feedlaneData?.is_user_logged_in || false;
  const submitMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({
    mutationFn: data => (0,_api_ideas__WEBPACK_IMPORTED_MODULE_2__.submitIdea)(data),
    onSuccess: () => {
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].success('Idea submitted successfully! It will be reviewed before being published.');
      onSuccess && onSuccess();
    },
    onError: error => {
      const message = error.response?.data?.message || 'Failed to submit idea';
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error(message);
    }
  });
  const handleInputChange = e => {
    const {
      name,
      value
    } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleImageChange = e => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Please select an image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Image size must be less than 5MB');
        return;
      }
      setFormData(prev => ({
        ...prev,
        image: file
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = e => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };
  const removeImage = () => {
    setFormData(prev => ({
      ...prev,
      image: null
    }));
    setImagePreview(null);
  };
  const handleSubmit = e => {
    e.preventDefault();

    // Validate required fields
    if (!formData.title.trim() || !formData.details.trim()) {
      react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Title and details are required');
      return;
    }
    if (!isLoggedIn) {
      if (!formData.first_name.trim() || !formData.last_name.trim() || !formData.email.trim()) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Name and email are required');
        return;
      }
      if (!isValidEmail(formData.email)) {
        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__["default"].error('Please enter a valid email address');
        return;
      }
    }

    // Create FormData for file upload
    const submitData = new FormData();
    Object.keys(formData).forEach(key => {
      if (formData[key] !== null) {
        submitData.append(key, formData[key]);
      }
    });
    submitMutation.mutate(submitData);
  };
  const isValidEmail = email => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
  const categories = [{
    value: 'improvement',
    label: 'Improvement'
  }, {
    value: 'feature-request',
    label: 'Feature Request'
  }, {
    value: 'bug',
    label: 'Bug Report'
  }, {
    value: 'feedback',
    label: 'General Feedback'
  }];
  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-idea-form"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-idea-form__header"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("h4", null, "Submit Your Idea"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    onClick: onCancel,
    className: "feedlane-idea-form__close"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiX, {
    size: 20
  }))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("form", {
    onSubmit: handleSubmit,
    className: "feedlane-idea-form__form"
  }, !isLoggedIn && (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-idea-form__guest-fields"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-row"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "text",
    name: "first_name",
    value: formData.first_name,
    onChange: handleInputChange,
    placeholder: "First name",
    className: "feedlane-input",
    required: true
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "text",
    name: "last_name",
    value: formData.last_name,
    onChange: handleInputChange,
    placeholder: "Last name",
    className: "feedlane-input",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "email",
    name: "email",
    value: formData.email,
    onChange: handleInputChange,
    placeholder: "Email address",
    className: "feedlane-input",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-field"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "idea-title",
    className: "feedlane-label"
  }, "Title *"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    id: "idea-title",
    type: "text",
    name: "title",
    value: formData.title,
    onChange: handleInputChange,
    placeholder: "Brief description of your idea",
    className: "feedlane-input",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-field"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "idea-category",
    className: "feedlane-label"
  }, "Category *"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("select", {
    id: "idea-category",
    name: "category",
    value: formData.category,
    onChange: handleInputChange,
    className: "feedlane-select",
    required: true
  }, categories.map(cat => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("option", {
    key: cat.value,
    value: cat.value
  }, cat.label)))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-field"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "idea-details",
    className: "feedlane-label"
  }, "Details *"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("textarea", {
    id: "idea-details",
    name: "details",
    value: formData.details,
    onChange: handleInputChange,
    placeholder: "Provide more details about your idea...",
    className: "feedlane-textarea",
    rows: "4",
    required: true
  })), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-form-field"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    className: "feedlane-label"
  }, "Image (optional)"), imagePreview ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-image-preview"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("img", {
    src: imagePreview,
    alt: "Preview"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "button",
    onClick: removeImage,
    className: "feedlane-image-preview__remove"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiX, {
    size: 16
  }))) : (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-file-upload"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("input", {
    type: "file",
    id: "idea-image",
    accept: "image/*",
    onChange: handleImageChange,
    className: "feedlane-file-input"
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("label", {
    htmlFor: "idea-image",
    className: "feedlane-file-label"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUpload, {
    size: 20
  }), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("span", null, "Upload Image"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("small", null, "Max 5MB, JPG/PNG")))), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-idea-form__actions"
  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "button",
    onClick: onCancel,
    className: "feedlane-button feedlane-button--secondary"
  }, "Cancel"), (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("button", {
    type: "submit",
    disabled: submitMutation.isPending,
    className: "feedlane-button feedlane-button--primary"
  }, submitMutation.isPending ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)("div", {
    className: "feedlane-spinner feedlane-spinner--small"
  }), "Submitting...") : 'Submit Idea'))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IdeaForm);

/***/ })

}]);
//# sourceMappingURL=react-src_sidebar_components_IdeaForm_js.js.map