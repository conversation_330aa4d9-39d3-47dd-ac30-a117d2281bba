{"version": 3, "file": "react-src_sidebar_components_ReactionButtons_js.js", "mappings": ";;;;;;;;;;;;;;;;AAAiC;;AAEjC;AACA;AACA;AACO,MAAMC,cAAc,GAAG,MAAOC,MAAM,IAAK;EAC5C,OAAOF,+CAAS,CAACG,GAAG,CAAC,cAAcD,MAAM,EAAE,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACO,MAAME,WAAW,GAAG,MAAAA,CAAOF,MAAM,EAAEG,YAAY,KAAK;EACvD,OAAOL,+CAAS,CAACM,IAAI,CAAC,YAAY,EAAE;IAChCC,OAAO,EAAEL,MAAM;IACfM,aAAa,EAAEH;EACnB,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA;AACO,MAAMI,cAAc,GAAG,MAAAA,CAAOP,MAAM,EAAEG,YAAY,KAAK;EAC1D,OAAOL,+CAAS,CAACU,MAAM,CAAC,cAAcR,MAAM,IAAIG,YAAY,EAAE,CAAC;AACnE,CAAC;;;;;;;;;;;;;;;;;;;;;;ACxByB;AACoD;AAC1C;AAC2C;AAE/E,MAAMW,eAAe,GAAGA,CAAC;EAAEd;AAAO,CAAC,KAAK;EACpC,MAAMe,WAAW,GAAGH,qEAAc,CAAC,CAAC;EAEpC,MAAM;IACFI,IAAI,EAAEC,YAAY;IAClBC,SAAS;IACTC;EACJ,CAAC,GAAGT,+DAAQ,CAAC;IACTU,QAAQ,EAAE,CAAC,WAAW,EAAEpB,MAAM,CAAC;IAC/BqB,OAAO,EAAEA,CAAA,KAAMtB,8DAAc,CAACC,MAAM,CAAC;IACrCsB,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;EAC9B,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAGZ,kEAAW,CAAC;IACpCa,UAAU,EAAEA,CAAC;MAAExB,MAAM;MAAEG;IAAa,CAAC,KAAKD,2DAAW,CAACF,MAAM,EAAEG,YAAY,CAAC;IAC3EsB,SAAS,EAAGT,IAAI,IAAK;MACjBD,WAAW,CAACW,YAAY,CAAC,CAAC,WAAW,EAAE1B,MAAM,CAAC,EAAEgB,IAAI,CAAC;MACrDH,uDAAK,CAACc,OAAO,CAAC,iBAAiB,CAAC;IACpC,CAAC;IACDC,OAAO,EAAGT,KAAK,IAAK;MAChB,MAAMU,OAAO,GAAGV,KAAK,CAACW,QAAQ,EAAEd,IAAI,EAAEa,OAAO,IAAI,wBAAwB;MACzEhB,uDAAK,CAACM,KAAK,CAACU,OAAO,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAME,sBAAsB,GAAGpB,kEAAW,CAAC;IACvCa,UAAU,EAAEA,CAAC;MAAExB,MAAM;MAAEG;IAAa,CAAC,KAAKI,8DAAc,CAACP,MAAM,EAAEG,YAAY,CAAC;IAC9EsB,SAAS,EAAGT,IAAI,IAAK;MACjBD,WAAW,CAACW,YAAY,CAAC,CAAC,WAAW,EAAE1B,MAAM,CAAC,EAAEgB,IAAI,CAAC;MACrDH,uDAAK,CAACc,OAAO,CAAC,mBAAmB,CAAC;IACtC,CAAC;IACDC,OAAO,EAAGT,KAAK,IAAK;MAChB,MAAMU,OAAO,GAAGV,KAAK,CAACW,QAAQ,EAAEd,IAAI,EAAEa,OAAO,IAAI,2BAA2B;MAC5EhB,uDAAK,CAACM,KAAK,CAACU,OAAO,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAMG,mBAAmB,GAAI7B,YAAY,IAAK;IAC1C,IAAI,CAACc,YAAY,EAAE;IAEnB,MAAMgB,iBAAiB,GAAGhB,YAAY,CAACiB,aAAa,KAAK/B,YAAY;IAErE,IAAI8B,iBAAiB,EAAE;MACnB;MACAF,sBAAsB,CAACI,MAAM,CAAC;QAAEnC,MAAM;QAAEG;MAAa,CAAC,CAAC;IAC3D,CAAC,MAAM;MACH;MACAoB,mBAAmB,CAACY,MAAM,CAAC;QAAEnC,MAAM;QAAEG;MAAa,CAAC,CAAC;IACxD;EACJ,CAAC;EAED,IAAIe,SAAS,EAAE;IACX,OACIkB,oDAAA;MAAKC,SAAS,EAAC;IAAgD,GAC3DD,oDAAA;MAAKC,SAAS,EAAC;IAA8B,GACxC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,CAAC,IACZH,oDAAA;MAAKI,GAAG,EAAED,CAAE;MAACF,SAAS,EAAC;IAA6D,GAChFD,oDAAA;MAAKC,SAAS,EAAC;IAAiC,CAAM,CAAC,EACvDD,oDAAA;MAAKC,SAAS,EAAC;IAAiC,CAAM,CACrD,CACR,CACA,CACJ,CAAC;EAEd;EAEA,IAAIlB,KAAK,IAAI,CAACF,YAAY,EAAE;IACxB,OAAO,IAAI,CAAC,CAAC;EACjB;EAEA,MAAM;IAAEwB,eAAe,GAAG,CAAC,CAAC;IAAEP,aAAa;IAAEQ,mBAAmB,GAAG,CAAC;EAAE,CAAC,GAAGzB,YAAY;EAEtF,OACImB,oDAAA;IAAKC,SAAS,EAAC;EAAoB,GAC/BD,oDAAA;IAAKC,SAAS,EAAC;EAA6B,GACvCM,MAAM,CAACC,OAAO,CAACF,mBAAmB,CAAC,CAACJ,GAAG,CAAC,CAAC,CAACnC,YAAY,EAAE0C,KAAK,CAAC,KAAK;IAChE,MAAMC,KAAK,GAAGL,eAAe,CAACtC,YAAY,CAAC,IAAI,CAAC;IAChD,MAAM4C,QAAQ,GAAGb,aAAa,KAAK/B,YAAY;IAC/C,MAAMe,SAAS,GAAGK,mBAAmB,CAACyB,SAAS,IAAIjB,sBAAsB,CAACiB,SAAS;IAEnF,OACIZ,oDAAA;MACII,GAAG,EAAErC,YAAa;MAClB8C,OAAO,EAAEA,CAAA,KAAMjB,mBAAmB,CAAC7B,YAAY,CAAE;MACjD+C,QAAQ,EAAEhC,SAAU;MACpBmB,SAAS,EAAE,4BAA4BU,QAAQ,GAAG,kCAAkC,GAAG,EAAE,EAAG;MAC5FI,KAAK,EAAE,cAAchD,YAAY;IAAG,GAEpCiC,oDAAA;MAAMC,SAAS,EAAC;IAAiC,GAC5CQ,KACC,CAAC,EACNC,KAAK,GAAG,CAAC,IACNV,oDAAA;MAAMC,SAAS,EAAC;IAAiC,GAC5CS,KACC,CAEN,CAAC;EAEjB,CAAC,CACA,CAAC,EAGLH,MAAM,CAACS,MAAM,CAACX,eAAe,CAAC,CAACY,IAAI,CAACP,KAAK,IAAIA,KAAK,GAAG,CAAC,CAAC,IACpDV,oDAAA;IAAKC,SAAS,EAAC;EAA6B,GACvCM,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAC3Ba,MAAM,CAAC,CAAC,CAACC,CAAC,EAAET,KAAK,CAAC,KAAKA,KAAK,GAAG,CAAC,CAAC,CACjCR,GAAG,CAAC,CAAC,CAACnC,YAAY,EAAE2C,KAAK,CAAC,KACvBV,oDAAA;IAAMI,GAAG,EAAErC,YAAa;IAACkC,SAAS,EAAC;EAAkC,GAChEK,mBAAmB,CAACvC,YAAY,CAAC,EAAC,GAAC,EAAC2C,KACnC,CACT,CAEJ,CAER,CAAC;AAEd,CAAC;AAED,iEAAehC,eAAe", "sources": ["webpack://feedlane/./react-src/sidebar/api/reactions.js", "webpack://feedlane/./react-src/sidebar/components/ReactionButtons.js"], "sourcesContent": ["import apiClient from './client';\n\n/**\n * Fetch reactions for a post\n */\nexport const fetchReactions = async (postId) => {\n    return apiClient.get(`/reactions/${postId}`);\n};\n\n/**\n * Add reaction to a post\n */\nexport const addReaction = async (postId, reactionType) => {\n    return apiClient.post('/reactions', {\n        post_id: postId,\n        reaction_type: reactionType,\n    });\n};\n\n/**\n * Remove reaction from a post\n */\nexport const removeReaction = async (postId, reactionType) => {\n    return apiClient.delete(`/reactions/${postId}/${reactionType}`);\n};\n", "import React from 'react';\nimport { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\nimport toast from 'react-hot-toast';\nimport { fetchReactions, addReaction, removeReaction } from '../api/reactions';\n\nconst ReactionButtons = ({ postId }) => {\n    const queryClient = useQueryClient();\n    \n    const {\n        data: reactionData,\n        isLoading,\n        error\n    } = useQuery({\n        queryKey: ['reactions', postId],\n        queryFn: () => fetchReactions(postId),\n        staleTime: 2 * 60 * 1000, // 2 minutes\n    });\n    \n    const addReactionMutation = useMutation({\n        mutationFn: ({ postId, reactionType }) => addReaction(postId, reactionType),\n        onSuccess: (data) => {\n            queryClient.setQueryData(['reactions', postId], data);\n            toast.success('Reaction added!');\n        },\n        onError: (error) => {\n            const message = error.response?.data?.message || 'Failed to add reaction';\n            toast.error(message);\n        },\n    });\n    \n    const removeReactionMutation = useMutation({\n        mutationFn: ({ postId, reactionType }) => removeReaction(postId, reactionType),\n        onSuccess: (data) => {\n            queryClient.setQueryData(['reactions', postId], data);\n            toast.success('Reaction removed!');\n        },\n        onError: (error) => {\n            const message = error.response?.data?.message || 'Failed to remove reaction';\n            toast.error(message);\n        },\n    });\n    \n    const handleReactionClick = (reactionType) => {\n        if (!reactionData) return;\n        \n        const isCurrentReaction = reactionData.user_reaction === reactionType;\n        \n        if (isCurrentReaction) {\n            // Remove current reaction\n            removeReactionMutation.mutate({ postId, reactionType });\n        } else {\n            // Add new reaction (this will replace any existing reaction)\n            addReactionMutation.mutate({ postId, reactionType });\n        }\n    };\n    \n    if (isLoading) {\n        return (\n            <div className=\"feedlane-reactions feedlane-reactions--loading\">\n                <div className=\"feedlane-reactions__skeleton\">\n                    {[1, 2, 3].map(i => (\n                        <div key={i} className=\"feedlane-reaction-button feedlane-reaction-button--skeleton\">\n                            <div className=\"feedlane-reaction-button__emoji\"></div>\n                            <div className=\"feedlane-reaction-button__count\"></div>\n                        </div>\n                    ))}\n                </div>\n            </div>\n        );\n    }\n    \n    if (error || !reactionData) {\n        return null; // Fail silently for reactions\n    }\n    \n    const { reaction_counts = {}, user_reaction, available_reactions = {} } = reactionData;\n    \n    return (\n        <div className=\"feedlane-reactions\">\n            <div className=\"feedlane-reactions__buttons\">\n                {Object.entries(available_reactions).map(([reactionType, emoji]) => {\n                    const count = reaction_counts[reactionType] || 0;\n                    const isActive = user_reaction === reactionType;\n                    const isLoading = addReactionMutation.isPending || removeReactionMutation.isPending;\n                    \n                    return (\n                        <button\n                            key={reactionType}\n                            onClick={() => handleReactionClick(reactionType)}\n                            disabled={isLoading}\n                            className={`feedlane-reaction-button ${isActive ? 'feedlane-reaction-button--active' : ''}`}\n                            title={`React with ${reactionType}`}\n                        >\n                            <span className=\"feedlane-reaction-button__emoji\">\n                                {emoji}\n                            </span>\n                            {count > 0 && (\n                                <span className=\"feedlane-reaction-button__count\">\n                                    {count}\n                                </span>\n                            )}\n                        </button>\n                    );\n                })}\n            </div>\n            \n            {/* Show total reactions if any */}\n            {Object.values(reaction_counts).some(count => count > 0) && (\n                <div className=\"feedlane-reactions__summary\">\n                    {Object.entries(reaction_counts)\n                        .filter(([_, count]) => count > 0)\n                        .map(([reactionType, count]) => (\n                            <span key={reactionType} className=\"feedlane-reactions__summary-item\">\n                                {available_reactions[reactionType]} {count}\n                            </span>\n                        ))\n                    }\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default ReactionButtons;\n"], "names": ["apiClient", "fetchReactions", "postId", "get", "addReaction", "reactionType", "post", "post_id", "reaction_type", "removeReaction", "delete", "React", "useQuery", "useMutation", "useQueryClient", "toast", "ReactionButtons", "queryClient", "data", "reactionData", "isLoading", "error", "query<PERSON><PERSON>", "queryFn", "staleTime", "addReactionMutation", "mutationFn", "onSuccess", "setQueryData", "success", "onError", "message", "response", "removeReactionMutation", "handleReactionClick", "isCurrentReaction", "user_reaction", "mutate", "createElement", "className", "map", "i", "key", "reaction_counts", "available_reactions", "Object", "entries", "emoji", "count", "isActive", "isPending", "onClick", "disabled", "title", "values", "some", "filter", "_"], "sourceRoot": ""}