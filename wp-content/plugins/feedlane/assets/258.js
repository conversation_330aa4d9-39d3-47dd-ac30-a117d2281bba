"use strict";(globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[]).push([[258],{258:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var n=t(609),l=t(967),s=t(470),d=t(14),r=t(880),c=t(326),m=t(44);const i=({postId:e})=>{const[a,t]=(0,n.useState)(!1),[i,f]=(0,n.useState)(!1),[u,o]=(0,n.useState)({feedback_text:"",user_name:"",user_email:""}),b=(0,l.jE)(),_=window.feedlaneData?.is_user_logged_in||!1,k=window.feedlaneData?.settings?.enable_guest_submissions||!1,{data:p,isLoading:E}=(0,s.I)({queryKey:["feedback",e],queryFn:()=>(async(e,a={})=>{const t={page:1,per_page:10,...a};return m.A.get(`/feedback/${e}`,t)})(e),enabled:i,staleTime:12e4}),g=(0,d.n)({mutationFn:a=>(async(e,a)=>m.A.post("/feedback",{post_id:e,...a}))(e,a),onSuccess:()=>{c.Ay.success("Feedback submitted successfully!"),o({feedback_text:"",user_name:"",user_email:""}),t(!1),b.invalidateQueries(["feedback",e])},onError:e=>{const a=e.response?.data?.message||"Failed to submit feedback";c.Ay.error(a)}}),v=e=>{const{name:a,value:t}=e.target;o((e=>({...e,[a]:t})))};return _||k?(0,n.createElement)("div",{className:"feedlane-feedback"},(0,n.createElement)("div",{className:"feedlane-feedback__toggle"},(0,n.createElement)("button",{onClick:()=>t(!a),className:"feedlane-feedback__toggle-button"},(0,n.createElement)(r.X6_,{size:16}),(0,n.createElement)("span",null,"Leave Feedback"),a?(0,n.createElement)(r.wAb,{size:16}):(0,n.createElement)(r.fK4,{size:16})),p&&p.pagination.total>0&&(0,n.createElement)("button",{onClick:()=>f(!i),className:"feedlane-feedback__view-button"},"View ",p.pagination.total," feedback",i?(0,n.createElement)(r.wAb,{size:14}):(0,n.createElement)(r.fK4,{size:14}))),a&&(0,n.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),u.feedback_text.trim()){if(!_&&k){if(!u.user_name.trim()||!u.user_email.trim())return void c.Ay.error("Name and email are required");if(a=u.user_email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))return void c.Ay.error("Please enter a valid email address")}g.mutate(u)}else c.Ay.error("Please enter your feedback");var a},className:"feedlane-feedback__form"},!_&&k&&(0,n.createElement)("div",{className:"feedlane-feedback__guest-fields"},(0,n.createElement)("div",{className:"feedlane-form-row"},(0,n.createElement)("input",{type:"text",name:"user_name",value:u.user_name,onChange:v,placeholder:"Your name",className:"feedlane-input feedlane-input--small",required:!0}),(0,n.createElement)("input",{type:"email",name:"user_email",value:u.user_email,onChange:v,placeholder:"Your email",className:"feedlane-input feedlane-input--small",required:!0}))),(0,n.createElement)("div",{className:"feedlane-feedback__text-field"},(0,n.createElement)("textarea",{name:"feedback_text",value:u.feedback_text,onChange:v,placeholder:"Share your thoughts about this update...",className:"feedlane-textarea",rows:"3",required:!0})),(0,n.createElement)("div",{className:"feedlane-feedback__actions"},(0,n.createElement)("button",{type:"button",onClick:()=>t(!1),className:"feedlane-button feedlane-button--secondary feedlane-button--small"},"Cancel"),(0,n.createElement)("button",{type:"submit",disabled:g.isPending,className:"feedlane-button feedlane-button--primary feedlane-button--small"},g.isPending?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Sending..."):(0,n.createElement)(n.Fragment,null,(0,n.createElement)(r.kGk,{size:14}),"Send Feedback")))),i&&(0,n.createElement)("div",{className:"feedlane-feedback__list"},E?(0,n.createElement)("div",{className:"feedlane-feedback__loading"},(0,n.createElement)("div",{className:"feedlane-spinner"}),(0,n.createElement)("span",null,"Loading feedback...")):p&&p.feedback.length>0?(0,n.createElement)("div",{className:"feedlane-feedback__items"},p.feedback.map((e=>(0,n.createElement)("div",{key:e.id,className:"feedlane-feedback__item"},(0,n.createElement)("div",{className:"feedlane-feedback__item-header"},(0,n.createElement)("span",{className:"feedlane-feedback__author"},e.author.name),(0,n.createElement)("span",{className:"feedlane-feedback__time"},e.time_ago)),(0,n.createElement)("div",{className:"feedlane-feedback__item-content"},e.feedback_text))))):(0,n.createElement)("div",{className:"feedlane-feedback__empty"},(0,n.createElement)("p",null,"No feedback yet. Be the first to share your thoughts!")))):null}}}]);