"use strict";(globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[]).push([[332],{332:(e,a,l)=>{l.r(a),l.d(a,{default:()=>m});var t=l(609),n=l(14),r=l(880),i=l(326),s=l(559);const m=({onSuccess:e,onCancel:a})=>{const[l,m]=(0,t.useState)({title:"",details:"",category:"feature-request",email:"",first_name:"",last_name:"",image:null}),[d,c]=(0,t.useState)(null),o=window.feedlaneData?.is_user_logged_in||!1,u=(0,n.n)({mutationFn:e=>(0,s.Gn)(e),onSuccess:()=>{i.Ay.success("Idea submitted successfully! It will be reviewed before being published."),e&&e()},onError:e=>{const a=e.response?.data?.message||"Failed to submit idea";i.Ay.error(a)}}),f=e=>{const{name:a,value:l}=e.target;m((e=>({...e,[a]:l})))};return(0,t.createElement)("div",{className:"feedlane-idea-form"},(0,t.createElement)("div",{className:"feedlane-idea-form__header"},(0,t.createElement)("h4",null,"Submit Your Idea"),(0,t.createElement)("button",{onClick:a,className:"feedlane-idea-form__close"},(0,t.createElement)(r.yGN,{size:20}))),(0,t.createElement)("form",{onSubmit:e=>{if(e.preventDefault(),!l.title.trim()||!l.details.trim())return void i.Ay.error("Title and details are required");if(!o){if(!l.first_name.trim()||!l.last_name.trim()||!l.email.trim())return void i.Ay.error("Name and email are required");if(a=l.email,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))return void i.Ay.error("Please enter a valid email address")}var a;const t=new FormData;Object.keys(l).forEach((e=>{null!==l[e]&&t.append(e,l[e])})),u.mutate(t)},className:"feedlane-idea-form__form"},!o&&(0,t.createElement)("div",{className:"feedlane-idea-form__guest-fields"},(0,t.createElement)("div",{className:"feedlane-form-row"},(0,t.createElement)("input",{type:"text",name:"first_name",value:l.first_name,onChange:f,placeholder:"First name",className:"feedlane-input",required:!0}),(0,t.createElement)("input",{type:"text",name:"last_name",value:l.last_name,onChange:f,placeholder:"Last name",className:"feedlane-input",required:!0})),(0,t.createElement)("input",{type:"email",name:"email",value:l.email,onChange:f,placeholder:"Email address",className:"feedlane-input",required:!0})),(0,t.createElement)("div",{className:"feedlane-form-field"},(0,t.createElement)("label",{htmlFor:"idea-title",className:"feedlane-label"},"Title *"),(0,t.createElement)("input",{id:"idea-title",type:"text",name:"title",value:l.title,onChange:f,placeholder:"Brief description of your idea",className:"feedlane-input",required:!0})),(0,t.createElement)("div",{className:"feedlane-form-field"},(0,t.createElement)("label",{htmlFor:"idea-category",className:"feedlane-label"},"Category *"),(0,t.createElement)("select",{id:"idea-category",name:"category",value:l.category,onChange:f,className:"feedlane-select",required:!0},[{value:"improvement",label:"Improvement"},{value:"feature-request",label:"Feature Request"},{value:"bug",label:"Bug Report"},{value:"feedback",label:"General Feedback"}].map((e=>(0,t.createElement)("option",{key:e.value,value:e.value},e.label))))),(0,t.createElement)("div",{className:"feedlane-form-field"},(0,t.createElement)("label",{htmlFor:"idea-details",className:"feedlane-label"},"Details *"),(0,t.createElement)("textarea",{id:"idea-details",name:"details",value:l.details,onChange:f,placeholder:"Provide more details about your idea...",className:"feedlane-textarea",rows:"4",required:!0})),(0,t.createElement)("div",{className:"feedlane-form-field"},(0,t.createElement)("label",{className:"feedlane-label"},"Image (optional)"),d?(0,t.createElement)("div",{className:"feedlane-image-preview"},(0,t.createElement)("img",{src:d,alt:"Preview"}),(0,t.createElement)("button",{type:"button",onClick:()=>{m((e=>({...e,image:null}))),c(null)},className:"feedlane-image-preview__remove"},(0,t.createElement)(r.yGN,{size:16}))):(0,t.createElement)("div",{className:"feedlane-file-upload"},(0,t.createElement)("input",{type:"file",id:"idea-image",accept:"image/*",onChange:e=>{const a=e.target.files[0];if(a){if(!a.type.startsWith("image/"))return void i.Ay.error("Please select an image file");if(a.size>5242880)return void i.Ay.error("Image size must be less than 5MB");m((e=>({...e,image:a})));const e=new FileReader;e.onload=e=>{c(e.target.result)},e.readAsDataURL(a)}},className:"feedlane-file-input"}),(0,t.createElement)("label",{htmlFor:"idea-image",className:"feedlane-file-label"},(0,t.createElement)(r.B88,{size:20}),(0,t.createElement)("span",null,"Upload Image"),(0,t.createElement)("small",null,"Max 5MB, JPG/PNG")))),(0,t.createElement)("div",{className:"feedlane-idea-form__actions"},(0,t.createElement)("button",{type:"button",onClick:a,className:"feedlane-button feedlane-button--secondary"},"Cancel"),(0,t.createElement)("button",{type:"submit",disabled:u.isPending,className:"feedlane-button feedlane-button--primary"},u.isPending?(0,t.createElement)(t.Fragment,null,(0,t.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}),"Submitting..."):"Submit Idea"))))}}}]);