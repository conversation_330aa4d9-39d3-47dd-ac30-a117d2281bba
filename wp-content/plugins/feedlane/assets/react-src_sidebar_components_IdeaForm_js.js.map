{"version": 3, "file": "react-src_sidebar_components_IdeaForm_js.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAwC;AACY;AACL;AACX;AACM;AAE1C,MAAMO,QAAQ,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAS,CAAC,KAAK;EAC1C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,+CAAQ,CAAC;IACrCW,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,iBAAiB;IAC3BC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,+CAAQ,CAAC,IAAI,CAAC;EACtD,MAAMoB,UAAU,GAAGC,MAAM,CAACC,YAAY,EAAEC,iBAAiB,IAAI,KAAK;EAElE,MAAMC,cAAc,GAAGvB,kEAAW,CAAC;IAC/BwB,UAAU,EAAGC,IAAI,IAAKrB,sDAAU,CAACqB,IAAI,CAAC;IACtCnB,SAAS,EAAEA,CAAA,KAAM;MACbH,uDAAK,CAACuB,OAAO,CAAC,0EAA0E,CAAC;MACzFpB,SAAS,IAAIA,SAAS,CAAC,CAAC;IAC5B,CAAC;IACDqB,OAAO,EAAGC,KAAK,IAAK;MAChB,MAAMC,OAAO,GAAGD,KAAK,CAACE,QAAQ,EAAEL,IAAI,EAAEI,OAAO,IAAI,uBAAuB;MACxE1B,uDAAK,CAACyB,KAAK,CAACC,OAAO,CAAC;IACxB;EACJ,CAAC,CAAC;EAEF,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1B,WAAW,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMG,iBAAiB,GAAIL,CAAC,IAAK;IAC7B,MAAMM,IAAI,GAAGN,CAAC,CAACG,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACN;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACjCtC,uDAAK,CAACyB,KAAK,CAAC,6BAA6B,CAAC;QAC1C;MACJ;;MAEA;MACA,IAAIU,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC7BvC,uDAAK,CAACyB,KAAK,CAAC,kCAAkC,CAAC;QAC/C;MACJ;MAEAnB,WAAW,CAAC2B,IAAI,KAAK;QACjB,GAAGA,IAAI;QACPpB,KAAK,EAAEsB;MACX,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIb,CAAC,IAAK;QACnBd,eAAe,CAACc,CAAC,CAACG,MAAM,CAACW,MAAM,CAAC;MACpC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;IAC9B;EACJ,CAAC;EAED,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACtBvC,WAAW,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPpB,KAAK,EAAE;IACX,CAAC,CAAC,CAAC;IACHE,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM+B,YAAY,GAAIjB,CAAC,IAAK;IACxBA,CAAC,CAACkB,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAAC1C,QAAQ,CAACE,KAAK,CAACyC,IAAI,CAAC,CAAC,IAAI,CAAC3C,QAAQ,CAACG,OAAO,CAACwC,IAAI,CAAC,CAAC,EAAE;MACpDhD,uDAAK,CAACyB,KAAK,CAAC,gCAAgC,CAAC;MAC7C;IACJ;IAEA,IAAI,CAACT,UAAU,EAAE;MACb,IAAI,CAACX,QAAQ,CAACM,UAAU,CAACqC,IAAI,CAAC,CAAC,IAAI,CAAC3C,QAAQ,CAACO,SAAS,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAC3C,QAAQ,CAACK,KAAK,CAACsC,IAAI,CAAC,CAAC,EAAE;QACrFhD,uDAAK,CAACyB,KAAK,CAAC,6BAA6B,CAAC;QAC1C;MACJ;MAEA,IAAI,CAACwB,YAAY,CAAC5C,QAAQ,CAACK,KAAK,CAAC,EAAE;QAC/BV,uDAAK,CAACyB,KAAK,CAAC,oCAAoC,CAAC;QACjD;MACJ;IACJ;;IAEA;IACA,MAAMyB,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;IACjCC,MAAM,CAACC,IAAI,CAAChD,QAAQ,CAAC,CAACiD,OAAO,CAACC,GAAG,IAAI;MACjC,IAAIlD,QAAQ,CAACkD,GAAG,CAAC,KAAK,IAAI,EAAE;QACxBL,UAAU,CAACM,MAAM,CAACD,GAAG,EAAElD,QAAQ,CAACkD,GAAG,CAAC,CAAC;MACzC;IACJ,CAAC,CAAC;IAEFnC,cAAc,CAACqC,MAAM,CAACP,UAAU,CAAC;EACrC,CAAC;EAED,MAAMD,YAAY,GAAIvC,KAAK,IAAK;IAC5B,OAAO,4BAA4B,CAACgD,IAAI,CAAChD,KAAK,CAAC;EACnD,CAAC;EAED,MAAMiD,UAAU,GAAG,CACf;IAAE5B,KAAK,EAAE,aAAa;IAAE6B,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAE7B,KAAK,EAAE,iBAAiB;IAAE6B,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAE7B,KAAK,EAAE,KAAK;IAAE6B,KAAK,EAAE;EAAa,CAAC,EACrC;IAAE7B,KAAK,EAAE,UAAU;IAAE6B,KAAK,EAAE;EAAmB,CAAC,CACnD;EAED,OACIC,oDAAA;IAAKC,SAAS,EAAC;EAAoB,GAC/BD,oDAAA;IAAKC,SAAS,EAAC;EAA4B,GACvCD,oDAAA,aAAI,kBAAoB,CAAC,EACzBA,oDAAA;IACIE,OAAO,EAAE3D,QAAS;IAClB0D,SAAS,EAAC;EAA2B,GAErCD,oDAAA,CAAC9D,+CAAG;IAACwC,IAAI,EAAE;EAAG,CAAE,CACZ,CACP,CAAC,EAENsB,oDAAA;IAAMG,QAAQ,EAAElB,YAAa;IAACgB,SAAS,EAAC;EAA0B,GAE7D,CAAC9C,UAAU,IACR6C,oDAAA;IAAKC,SAAS,EAAC;EAAkC,GAC7CD,oDAAA;IAAKC,SAAS,EAAC;EAAmB,GAC9BD,oDAAA;IACIxB,IAAI,EAAC,MAAM;IACXP,IAAI,EAAC,YAAY;IACjBC,KAAK,EAAE1B,QAAQ,CAACM,UAAW;IAC3BsD,QAAQ,EAAErC,iBAAkB;IAC5BsC,WAAW,EAAC,YAAY;IACxBJ,SAAS,EAAC,gBAAgB;IAC1BK,QAAQ;EAAA,CACX,CAAC,EACFN,oDAAA;IACIxB,IAAI,EAAC,MAAM;IACXP,IAAI,EAAC,WAAW;IAChBC,KAAK,EAAE1B,QAAQ,CAACO,SAAU;IAC1BqD,QAAQ,EAAErC,iBAAkB;IAC5BsC,WAAW,EAAC,WAAW;IACvBJ,SAAS,EAAC,gBAAgB;IAC1BK,QAAQ;EAAA,CACX,CACA,CAAC,EACNN,oDAAA;IACIxB,IAAI,EAAC,OAAO;IACZP,IAAI,EAAC,OAAO;IACZC,KAAK,EAAE1B,QAAQ,CAACK,KAAM;IACtBuD,QAAQ,EAAErC,iBAAkB;IAC5BsC,WAAW,EAAC,eAAe;IAC3BJ,SAAS,EAAC,gBAAgB;IAC1BK,QAAQ;EAAA,CACX,CACA,CACR,EAGDN,oDAAA;IAAKC,SAAS,EAAC;EAAqB,GAChCD,oDAAA;IAAOO,OAAO,EAAC,YAAY;IAACN,SAAS,EAAC;EAAgB,GAAC,SAEhD,CAAC,EACRD,oDAAA;IACIQ,EAAE,EAAC,YAAY;IACfhC,IAAI,EAAC,MAAM;IACXP,IAAI,EAAC,OAAO;IACZC,KAAK,EAAE1B,QAAQ,CAACE,KAAM;IACtB0D,QAAQ,EAAErC,iBAAkB;IAC5BsC,WAAW,EAAC,gCAAgC;IAC5CJ,SAAS,EAAC,gBAAgB;IAC1BK,QAAQ;EAAA,CACX,CACA,CAAC,EAGNN,oDAAA;IAAKC,SAAS,EAAC;EAAqB,GAChCD,oDAAA;IAAOO,OAAO,EAAC,eAAe;IAACN,SAAS,EAAC;EAAgB,GAAC,YAEnD,CAAC,EACRD,oDAAA;IACIQ,EAAE,EAAC,eAAe;IAClBvC,IAAI,EAAC,UAAU;IACfC,KAAK,EAAE1B,QAAQ,CAACI,QAAS;IACzBwD,QAAQ,EAAErC,iBAAkB;IAC5BkC,SAAS,EAAC,iBAAiB;IAC3BK,QAAQ;EAAA,GAEPR,UAAU,CAACW,GAAG,CAACC,GAAG,IACfV,oDAAA;IAAQN,GAAG,EAAEgB,GAAG,CAACxC,KAAM;IAACA,KAAK,EAAEwC,GAAG,CAACxC;EAAM,GACpCwC,GAAG,CAACX,KACD,CACX,CACG,CACP,CAAC,EAGNC,oDAAA;IAAKC,SAAS,EAAC;EAAqB,GAChCD,oDAAA;IAAOO,OAAO,EAAC,cAAc;IAACN,SAAS,EAAC;EAAgB,GAAC,WAElD,CAAC,EACRD,oDAAA;IACIQ,EAAE,EAAC,cAAc;IACjBvC,IAAI,EAAC,SAAS;IACdC,KAAK,EAAE1B,QAAQ,CAACG,OAAQ;IACxByD,QAAQ,EAAErC,iBAAkB;IAC5BsC,WAAW,EAAC,yCAAyC;IACrDJ,SAAS,EAAC,mBAAmB;IAC7BU,IAAI,EAAC,GAAG;IACRL,QAAQ;EAAA,CACX,CACA,CAAC,EAGNN,oDAAA;IAAKC,SAAS,EAAC;EAAqB,GAChCD,oDAAA;IAAOC,SAAS,EAAC;EAAgB,GAAC,kBAE3B,CAAC,EAEPhD,YAAY,GACT+C,oDAAA;IAAKC,SAAS,EAAC;EAAwB,GACnCD,oDAAA;IAAKY,GAAG,EAAE3D,YAAa;IAAC4D,GAAG,EAAC;EAAS,CAAE,CAAC,EACxCb,oDAAA;IACIxB,IAAI,EAAC,QAAQ;IACb0B,OAAO,EAAElB,WAAY;IACrBiB,SAAS,EAAC;EAAgC,GAE1CD,oDAAA,CAAC9D,+CAAG;IAACwC,IAAI,EAAE;EAAG,CAAE,CACZ,CACP,CAAC,GAENsB,oDAAA;IAAKC,SAAS,EAAC;EAAsB,GACjCD,oDAAA;IACIxB,IAAI,EAAC,MAAM;IACXgC,EAAE,EAAC,YAAY;IACfM,MAAM,EAAC,SAAS;IAChBV,QAAQ,EAAE/B,iBAAkB;IAC5B4B,SAAS,EAAC;EAAqB,CAClC,CAAC,EACFD,oDAAA;IAAOO,OAAO,EAAC,YAAY;IAACN,SAAS,EAAC;EAAqB,GACvDD,oDAAA,CAAC/D,oDAAQ;IAACyC,IAAI,EAAE;EAAG,CAAE,CAAC,EACtBsB,oDAAA,eAAM,cAAkB,CAAC,EACzBA,oDAAA,gBAAO,kBAAuB,CAC3B,CACN,CAER,CAAC,EAGNA,oDAAA;IAAKC,SAAS,EAAC;EAA6B,GACxCD,oDAAA;IACIxB,IAAI,EAAC,QAAQ;IACb0B,OAAO,EAAE3D,QAAS;IAClB0D,SAAS,EAAC;EAA4C,GACzD,QAEO,CAAC,EACTD,oDAAA;IACIxB,IAAI,EAAC,QAAQ;IACbuC,QAAQ,EAAExD,cAAc,CAACyD,SAAU;IACnCf,SAAS,EAAC;EAA0C,GAEnD1C,cAAc,CAACyD,SAAS,GACrBhB,oDAAA,CAAAiB,2CAAA,QACIjB,oDAAA;IAAKC,SAAS,EAAC;EAA0C,CAAM,CAAC,iBAElE,CAAC,GAEH,aAEA,CACP,CACH,CACL,CAAC;AAEd,CAAC;AAED,iEAAe5D,QAAQ", "sources": ["webpack://feedlane/./react-src/sidebar/components/IdeaForm.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useMutation } from '@tanstack/react-query';\nimport { FiUpload, FiX } from 'react-icons/fi';\nimport toast from 'react-hot-toast';\nimport { submitIdea } from '../api/ideas';\n\nconst IdeaForm = ({ onSuccess, onCancel }) => {\n    const [formData, setFormData] = useState({\n        title: '',\n        details: '',\n        category: 'feature-request',\n        email: '',\n        first_name: '',\n        last_name: '',\n        image: null\n    });\n    \n    const [imagePreview, setImagePreview] = useState(null);\n    const isLoggedIn = window.feedlaneData?.is_user_logged_in || false;\n    \n    const submitMutation = useMutation({\n        mutationFn: (data) => submitIdea(data),\n        onSuccess: () => {\n            toast.success('Idea submitted successfully! It will be reviewed before being published.');\n            onSuccess && onSuccess();\n        },\n        onError: (error) => {\n            const message = error.response?.data?.message || 'Failed to submit idea';\n            toast.error(message);\n        },\n    });\n    \n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setFormData(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n    \n    const handleImageChange = (e) => {\n        const file = e.target.files[0];\n        if (file) {\n            // Validate file type\n            if (!file.type.startsWith('image/')) {\n                toast.error('Please select an image file');\n                return;\n            }\n            \n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                toast.error('Image size must be less than 5MB');\n                return;\n            }\n            \n            setFormData(prev => ({\n                ...prev,\n                image: file\n            }));\n            \n            // Create preview\n            const reader = new FileReader();\n            reader.onload = (e) => {\n                setImagePreview(e.target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    \n    const removeImage = () => {\n        setFormData(prev => ({\n            ...prev,\n            image: null\n        }));\n        setImagePreview(null);\n    };\n    \n    const handleSubmit = (e) => {\n        e.preventDefault();\n        \n        // Validate required fields\n        if (!formData.title.trim() || !formData.details.trim()) {\n            toast.error('Title and details are required');\n            return;\n        }\n        \n        if (!isLoggedIn) {\n            if (!formData.first_name.trim() || !formData.last_name.trim() || !formData.email.trim()) {\n                toast.error('Name and email are required');\n                return;\n            }\n            \n            if (!isValidEmail(formData.email)) {\n                toast.error('Please enter a valid email address');\n                return;\n            }\n        }\n        \n        // Create FormData for file upload\n        const submitData = new FormData();\n        Object.keys(formData).forEach(key => {\n            if (formData[key] !== null) {\n                submitData.append(key, formData[key]);\n            }\n        });\n        \n        submitMutation.mutate(submitData);\n    };\n    \n    const isValidEmail = (email) => {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n    };\n    \n    const categories = [\n        { value: 'improvement', label: 'Improvement' },\n        { value: 'feature-request', label: 'Feature Request' },\n        { value: 'bug', label: 'Bug Report' },\n        { value: 'feedback', label: 'General Feedback' }\n    ];\n    \n    return (\n        <div className=\"feedlane-idea-form\">\n            <div className=\"feedlane-idea-form__header\">\n                <h4>Submit Your Idea</h4>\n                <button\n                    onClick={onCancel}\n                    className=\"feedlane-idea-form__close\"\n                >\n                    <FiX size={20} />\n                </button>\n            </div>\n            \n            <form onSubmit={handleSubmit} className=\"feedlane-idea-form__form\">\n                {/* Guest user fields */}\n                {!isLoggedIn && (\n                    <div className=\"feedlane-idea-form__guest-fields\">\n                        <div className=\"feedlane-form-row\">\n                            <input\n                                type=\"text\"\n                                name=\"first_name\"\n                                value={formData.first_name}\n                                onChange={handleInputChange}\n                                placeholder=\"First name\"\n                                className=\"feedlane-input\"\n                                required\n                            />\n                            <input\n                                type=\"text\"\n                                name=\"last_name\"\n                                value={formData.last_name}\n                                onChange={handleInputChange}\n                                placeholder=\"Last name\"\n                                className=\"feedlane-input\"\n                                required\n                            />\n                        </div>\n                        <input\n                            type=\"email\"\n                            name=\"email\"\n                            value={formData.email}\n                            onChange={handleInputChange}\n                            placeholder=\"Email address\"\n                            className=\"feedlane-input\"\n                            required\n                        />\n                    </div>\n                )}\n                \n                {/* Title */}\n                <div className=\"feedlane-form-field\">\n                    <label htmlFor=\"idea-title\" className=\"feedlane-label\">\n                        Title *\n                    </label>\n                    <input\n                        id=\"idea-title\"\n                        type=\"text\"\n                        name=\"title\"\n                        value={formData.title}\n                        onChange={handleInputChange}\n                        placeholder=\"Brief description of your idea\"\n                        className=\"feedlane-input\"\n                        required\n                    />\n                </div>\n                \n                {/* Category */}\n                <div className=\"feedlane-form-field\">\n                    <label htmlFor=\"idea-category\" className=\"feedlane-label\">\n                        Category *\n                    </label>\n                    <select\n                        id=\"idea-category\"\n                        name=\"category\"\n                        value={formData.category}\n                        onChange={handleInputChange}\n                        className=\"feedlane-select\"\n                        required\n                    >\n                        {categories.map(cat => (\n                            <option key={cat.value} value={cat.value}>\n                                {cat.label}\n                            </option>\n                        ))}\n                    </select>\n                </div>\n                \n                {/* Details */}\n                <div className=\"feedlane-form-field\">\n                    <label htmlFor=\"idea-details\" className=\"feedlane-label\">\n                        Details *\n                    </label>\n                    <textarea\n                        id=\"idea-details\"\n                        name=\"details\"\n                        value={formData.details}\n                        onChange={handleInputChange}\n                        placeholder=\"Provide more details about your idea...\"\n                        className=\"feedlane-textarea\"\n                        rows=\"4\"\n                        required\n                    />\n                </div>\n                \n                {/* Image Upload */}\n                <div className=\"feedlane-form-field\">\n                    <label className=\"feedlane-label\">\n                        Image (optional)\n                    </label>\n                    \n                    {imagePreview ? (\n                        <div className=\"feedlane-image-preview\">\n                            <img src={imagePreview} alt=\"Preview\" />\n                            <button\n                                type=\"button\"\n                                onClick={removeImage}\n                                className=\"feedlane-image-preview__remove\"\n                            >\n                                <FiX size={16} />\n                            </button>\n                        </div>\n                    ) : (\n                        <div className=\"feedlane-file-upload\">\n                            <input\n                                type=\"file\"\n                                id=\"idea-image\"\n                                accept=\"image/*\"\n                                onChange={handleImageChange}\n                                className=\"feedlane-file-input\"\n                            />\n                            <label htmlFor=\"idea-image\" className=\"feedlane-file-label\">\n                                <FiUpload size={20} />\n                                <span>Upload Image</span>\n                                <small>Max 5MB, JPG/PNG</small>\n                            </label>\n                        </div>\n                    )}\n                </div>\n                \n                {/* Actions */}\n                <div className=\"feedlane-idea-form__actions\">\n                    <button\n                        type=\"button\"\n                        onClick={onCancel}\n                        className=\"feedlane-button feedlane-button--secondary\"\n                    >\n                        Cancel\n                    </button>\n                    <button\n                        type=\"submit\"\n                        disabled={submitMutation.isPending}\n                        className=\"feedlane-button feedlane-button--primary\"\n                    >\n                        {submitMutation.isPending ? (\n                            <>\n                                <div className=\"feedlane-spinner feedlane-spinner--small\"></div>\n                                Submitting...\n                            </>\n                        ) : (\n                            'Submit Idea'\n                        )}\n                    </button>\n                </div>\n            </form>\n        </div>\n    );\n};\n\nexport default IdeaForm;\n"], "names": ["React", "useState", "useMutation", "FiUpload", "FiX", "toast", "submitIdea", "IdeaForm", "onSuccess", "onCancel", "formData", "setFormData", "title", "details", "category", "email", "first_name", "last_name", "image", "imagePreview", "setImagePreview", "isLoggedIn", "window", "feedlaneData", "is_user_logged_in", "submitMutation", "mutationFn", "data", "success", "onError", "error", "message", "response", "handleInputChange", "e", "name", "value", "target", "prev", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onload", "result", "readAsDataURL", "removeImage", "handleSubmit", "preventDefault", "trim", "isValidEmail", "submitData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "append", "mutate", "test", "categories", "label", "createElement", "className", "onClick", "onSubmit", "onChange", "placeholder", "required", "htmlFor", "id", "map", "cat", "rows", "src", "alt", "accept", "disabled", "isPending", "Fragment"], "sourceRoot": ""}