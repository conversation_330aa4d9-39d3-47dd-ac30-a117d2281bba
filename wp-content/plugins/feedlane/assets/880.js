"use strict";(globalThis.webpackChunkfeedlane=globalThis.webpackChunkfeedlane||[]).push([[880],{499:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var o=a(609),n=a(245),s=a(757),d=a(880),i=a(326),l=a(393);const c=({ideaId:e,hasVoted:t=!1,voteCount:a=0})=>{const c=(0,n.jE)(),r=(0,s.n)({mutationFn:()=>(0,l.Q2)(e),onSuccess:t=>{c.setQueryData(["ideas"],(a=>a?{...a,ideas:a.ideas.map((a=>a.id===e?{...a,vote_count:t.vote_count,has_voted:!0}:a))}:a)),c.setQueryData(["idea",e],(e=>e?{...e,vote_count:t.vote_count,has_voted:!0}:e)),i.Ay.success("Vote recorded!")},onError:e=>{const t=e.response?.data?.message||"Failed to vote";i.Ay.error(t)}});return(0,o.createElement)("button",{onClick:()=>{t?i.Ay.info("You have already voted on this idea"):r.mutate()},disabled:r.isPending||t,className:"feedlane-vote-button "+(t?"feedlane-vote-button--voted":""),title:t?"You have voted":"Vote for this idea"},r.isPending?(0,o.createElement)("div",{className:"feedlane-spinner feedlane-spinner--small"}):(0,o.createElement)(d.wAb,{size:16}),(0,o.createElement)("span",{className:"feedlane-vote-button__count"},a))}}}]);