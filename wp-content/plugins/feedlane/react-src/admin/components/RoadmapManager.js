import React, { useState, useEffect } from 'react';
import {
    DndContext,
    DragOverlay,
    closestCorners,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
} from '@dnd-kit/core';
import {
    SortableContext,
    verticalListSortingStrategy,
    arrayMove
} from '@dnd-kit/sortable';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import RoadmapColumn from './RoadmapColumn';
import RoadmapCard from './RoadmapCard';
import AddItemModal from './AddItemModal';
import toast from 'react-hot-toast';

const RoadmapManager = () => {
    const [statuses, setStatuses] = useState([]);
    const [ideas, setIdeas] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeId, setActiveId] = useState(null);
    const [showAddModal, setShowAddModal] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState(null);

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 8,
            },
        }),
        useSensor(KeyboardSensor)
    );

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            await Promise.all([loadStatus(), loadIdeas()]);
        } catch (error) {
            toast.error('Failed to load roadmap data');
        } finally {
            setLoading(false);
        }
    };

    const loadStatus = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_status');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setStatuses(data.data);
            } else {
                throw new Error(data.data || 'Failed to load status');
            }
        } catch (error) {
            console.error('Error loading status:', error);
            throw error;
        }
    };

    const loadIdeas = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_ideas');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                console.log('Loaded ideas:', data.data.ideas);
                setIdeas(data.data.ideas || []);
            } else {
                throw new Error(data.data || 'Failed to load ideas');
            }
        } catch (error) {
            console.error('Error loading ideas:', error);
            throw error;
        }
    };

    const getIdeasByStatus = (statusSlug) => {
        const filtered = ideas.filter(idea => idea.status === statusSlug);
        console.log(`Ideas for status ${statusSlug}:`, filtered);
        return filtered;
    };

    const handleDragStart = (event) => {
        setActiveId(event.active.id);
    };

    const handleDragEnd = async (event) => {
        const { active, over } = event;
        setActiveId(null);

        if (!over) return;

        const activeId = active.id;
        const overId = over.id;

        // Find the active idea
        const activeIdea = ideas.find(idea => idea.id === parseInt(activeId));
        if (!activeIdea) return;

        // Determine the new status
        let newStatus = null;

        // Check if dropped on a column
        const targetStatus = statuses.find(status => status.slug === overId);
        if (targetStatus) {
            newStatus = targetStatus.slug;
        } else {
            // Check if dropped on another card
            const targetIdea = ideas.find(idea => idea.id === parseInt(overId));
            if (targetIdea) {
                newStatus = targetIdea.status;
            }
        }

        // If status hasn't changed, just reorder within the same column
        if (newStatus === activeIdea.status) {
            const statusIdeas = getIdeasByStatus(newStatus);
            const oldIndex = statusIdeas.findIndex(idea => idea.id === activeIdea.id);
            const newIndex = statusIdeas.findIndex(idea => idea.id === parseInt(overId));

            if (oldIndex !== newIndex) {
                const reorderedIdeas = arrayMove(statusIdeas, oldIndex, newIndex);
                // Update the ideas state with new order
                const updatedIdeas = ideas.map(idea => {
                    const reorderedIdea = reorderedIdeas.find(ri => ri.id === idea.id);
                    return reorderedIdea || idea;
                });
                setIdeas(updatedIdeas);
            }
            return;
        }

        // Update status if it changed
        if (newStatus && newStatus !== activeIdea.status) {
            await updateIdeaStatus(activeIdea.id, newStatus);
        }
    };

    const updateIdeaStatus = async (ideaId, newStatus) => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_update_idea_status');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);
            formData.append('status', newStatus);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                // Update local state
                setIdeas(prevIdeas =>
                    prevIdeas.map(idea =>
                        idea.id === ideaId
                            ? { ...idea, status: newStatus }
                            : idea
                    )
                );
                toast.success('Idea status updated successfully');
            } else {
                throw new Error(data.data || 'Failed to update idea status');
            }
        } catch (error) {
            console.error('Error updating idea status:', error);
            toast.error('Failed to update idea status');
        }
    };

    const handleAddItem = (statusSlug) => {
        setSelectedStatus(statusSlug);
        setShowAddModal(true);
    };

    const handleItemAdded = () => {
        setShowAddModal(false);
        setSelectedStatus(null);
        loadIdeas(); // Reload ideas to show the new one
    };

    const activeIdea = activeId ? ideas.find(idea => idea.id === parseInt(activeId)) : null;

    if (loading) {
        return (
            <div className="feedlane-admin-wrapper">
                <div className="feedlane-admin">
                    <div className="feedlane-admin__header">
                        <h1>Roadmap Manager</h1>
                        <p>Manage your product roadmap with drag & drop</p>
                    </div>
                    <div className="feedlane-admin__content">
                        <div className="feedlane-loading">
                            <div className="feedlane-loading__spinner"></div>
                            <p>Loading roadmap...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Roadmap Manager</h1>
                    <p>Manage your product roadmap with drag & drop</p>
                </div>

                <div className="feedlane-admin__content">
                    <DndContext
                        sensors={sensors}
                        collisionDetection={closestCorners}
                        onDragStart={handleDragStart}
                        onDragEnd={handleDragEnd}
                        modifiers={[restrictToWindowEdges]}
                    >
                        <div className="flex gap-6 overflow-x-auto pb-6 min-h-[600px]">
                            {statuses.map((status) => (
                                <SortableContext
                                    key={status.slug}
                                    items={getIdeasByStatus(status.slug).map(idea => idea.id)}
                                    strategy={verticalListSortingStrategy}
                                >
                                    <RoadmapColumn
                                        status={status}
                                        ideas={getIdeasByStatus(status.slug)}
                                        onAddItem={() => handleAddItem(status.slug)}
                                    />
                                </SortableContext>
                            ))}
                        </div>

                        <DragOverlay>
                            {activeIdea ? (
                                <RoadmapCard idea={activeIdea} isDragging />
                            ) : null}
                        </DragOverlay>
                    </DndContext>

                    {/* Add Item Modal */}
                    {showAddModal && (
                        <AddItemModal
                            isOpen={showAddModal}
                            onClose={() => setShowAddModal(false)}
                            selectedStatus={selectedStatus}
                            statuses={statuses}
                            onItemAdded={handleItemAdded}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default RoadmapManager;
