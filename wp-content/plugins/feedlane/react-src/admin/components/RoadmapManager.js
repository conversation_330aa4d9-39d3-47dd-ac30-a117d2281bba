import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const RoadmapManager = () => {
    const [ideas, setIdeas] = useState([]);
    const [statuses, setStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [draggedItem, setDraggedItem] = useState(null);

    useEffect(() => {
        loadData();
    }, []);

    const loadData = async () => {
        try {
            setLoading(true);
            
            // Load ideas
            const ideasFormData = new FormData();
            ideasFormData.append('action', 'feedlane_get_ideas');
            ideasFormData.append('nonce', feedlaneAdmin.nonce);

            const ideasResponse = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: ideasFormData
            });

            const ideasData = await ideasResponse.json();
            if (ideasData.success) {
                setIdeas(ideasData.data);
            }

            // Load statuses
            const statusFormData = new FormData();
            statusFormData.append('action', 'feedlane_get_statuses');
            statusFormData.append('nonce', feedlaneAdmin.nonce);

            const statusResponse = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: statusFormData
            });

            const statusData = await statusResponse.json();
            if (statusData.success) {
                setStatuses(statusData.data);
            }

        } catch (error) {
            toast.error('Failed to load roadmap data');
        } finally {
            setLoading(false);
        }
    };

    const handleDragStart = (e, idea) => {
        setDraggedItem(idea);
        e.dataTransfer.effectAllowed = 'move';
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
    };

    const handleDrop = async (e, targetStatus) => {
        e.preventDefault();
        
        if (!draggedItem) return;

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_update_idea_status');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', draggedItem.id);
            formData.append('status', targetStatus);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Idea status updated successfully');
                loadData(); // Reload data
            } else {
                toast.error(data.data || 'Failed to update idea status');
            }
        } catch (error) {
            toast.error('Failed to update idea status');
        }

        setDraggedItem(null);
    };

    const getIdeasByStatus = (statusSlug) => {
        return ideas.filter(idea => idea.status === statusSlug);
    };

    const getStatusColor = (statusSlug) => {
        const statusMap = {
            'under-review': '#6B7280',
            'planned': '#EF4444',
            'in-progress': '#F59E0B',
            'completed': '#10B981'
        };
        return statusMap[statusSlug] || '#6B7280';
    };

    if (loading) {
        return (
            <div className="feedlane-admin__loading">
                <div className="feedlane-spinner"></div>
                <p>Loading roadmap...</p>
            </div>
        );
    }

    return (
        <div className="feedlane-admin">
            <div className="feedlane-admin__header">
                <h2>Roadmap Manager</h2>
                <p>Drag and drop ideas between status columns to update their roadmap status.</p>
            </div>

            <div className="feedlane-admin__content">
                <div className="feedlane-roadmap-board">
                    {statuses.map((status) => (
                        <div
                            key={status.slug}
                            className="feedlane-roadmap-column"
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDrop(e, status.slug)}
                        >
                            <div className="feedlane-roadmap-column__header">
                                <div
                                    className="feedlane-roadmap-column__indicator"
                                    style={{ backgroundColor: getStatusColor(status.slug) }}
                                ></div>
                                <h3>{status.name}</h3>
                                <span className="feedlane-roadmap-column__count">
                                    {getIdeasByStatus(status.slug).length}
                                </span>
                            </div>

                            <div className="feedlane-roadmap-column__content">
                                {getIdeasByStatus(status.slug).map((idea) => (
                                    <div
                                        key={idea.id}
                                        className="feedlane-roadmap-card"
                                        draggable
                                        onDragStart={(e) => handleDragStart(e, idea)}
                                    >
                                        <div className="feedlane-roadmap-card__header">
                                            <h4>{idea.title}</h4>
                                            <div className="feedlane-roadmap-card__meta">
                                                <span className="feedlane-roadmap-card__votes">
                                                    👍 {idea.vote_count || 0}
                                                </span>
                                                <span className="feedlane-roadmap-card__comments">
                                                    💬 {idea.comment_count || 0}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        {idea.excerpt && (
                                            <p className="feedlane-roadmap-card__excerpt">
                                                {idea.excerpt}
                                            </p>
                                        )}

                                        {idea.category && (
                                            <div className="feedlane-roadmap-card__category">
                                                <span
                                                    className="feedlane-badge"
                                                    style={{ backgroundColor: idea.category_color || '#10B981' }}
                                                >
                                                    {idea.category}
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                ))}

                                {getIdeasByStatus(status.slug).length === 0 && (
                                    <div className="feedlane-roadmap-column__empty">
                                        <p>No ideas in this status</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                {statuses.length === 0 && (
                    <div className="feedlane-admin__empty">
                        <h3>No Status Found</h3>
                        <p>Please create some roadmap statuses first in the Status Management page.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default RoadmapManager;
