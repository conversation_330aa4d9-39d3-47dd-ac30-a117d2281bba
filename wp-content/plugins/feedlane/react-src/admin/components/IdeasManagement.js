import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const IdeasManagement = () => {
    const [ideas, setIdeas] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [statusFilter, setStatusFilter] = useState('all');

    useEffect(() => {
        loadIdeas();
    }, [currentPage, statusFilter]);

    const loadIdeas = async () => {
        try {
            setLoading(true);
            const formData = new FormData();
            formData.append('action', 'feedlane_get_ideas');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('page', currentPage);
            formData.append('per_page', 20);
            formData.append('status', statusFilter);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setIdeas(data.data.ideas);
                setTotalPages(data.data.pages);
            } else {
                toast.error('Failed to load ideas');
            }
        } catch (error) {
            toast.error('Failed to load ideas');
        } finally {
            setLoading(false);
        }
    };

    const updateIdeaStatus = async (ideaId, newStatus) => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_update_idea_status');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);
            formData.append('status', newStatus);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Status updated successfully');
                loadIdeas();
            } else {
                toast.error(data.data || 'Failed to update status');
            }
        } catch (error) {
            toast.error('Failed to update status');
        }
    };

    const deleteIdea = async (ideaId) => {
        if (!confirm('Are you sure you want to delete this idea?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_delete_idea');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Idea deleted successfully');
                loadIdeas();
            } else {
                toast.error(data.data || 'Failed to delete idea');
            }
        } catch (error) {
            toast.error('Failed to delete idea');
        }
    };

    const getStatusBadge = (status) => {
        const statusMap = {
            'publish': { label: 'Published', class: 'feedlane-badge--success' },
            'draft': { label: 'Draft', class: 'feedlane-badge--gray' },
            'pending': { label: 'Pending', class: 'feedlane-badge--warning' },
            'private': { label: 'Private', class: 'feedlane-badge--info' },
        };

        const statusInfo = statusMap[status] || { label: status, class: 'feedlane-badge--gray' };

        return (
            <span className={`feedlane-badge ${statusInfo.class}`}>
                {statusInfo.label}
            </span>
        );
    };

    if (loading) {
        return (
            <div className="feedlane-admin-wrapper">
                <div className="feedlane-admin">
                    <div className="feedlane-admin__header">
                        <h1>Ideas Management</h1>
                        <p>Manage submitted ideas and feedback</p>
                    </div>
                    <div className="feedlane-admin__content">
                        <div className="feedlane-loading">
                            <div className="feedlane-loading__spinner"></div>
                            <p>Loading ideas...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Ideas Management</h1>
                    <p>Manage submitted ideas and feedback</p>
                </div>

            <div className="feedlane-admin__content">
                {/* Filters */}
                <div className="mb-6 flex items-center gap-4">
                    <label htmlFor="status-filter" className="text-sm font-medium text-gray-700">
                        Filter by status:
                    </label>
                    <select
                        id="status-filter"
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="feedlane-select w-auto"
                    >
                        <option value="all">All Status</option>
                        <option value="publish">Published</option>
                        <option value="draft">Draft</option>
                        <option value="pending">Pending</option>
                        <option value="private">Private</option>
                    </select>
                </div>

                {/* Ideas Table */}
                {ideas.length > 0 ? (
                    <div className="feedlane-card">
                        <div className="feedlane-card__content">
                            <table className="feedlane-table">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Author</th>
                                        <th>Status</th>
                                        <th>Categories</th>
                                        <th>Votes</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {ideas.map((idea) => (
                                        <tr key={idea.id}>
                                            <td>
                                                <div className="font-medium text-gray-900">
                                                    {idea.title}
                                                </div>
                                                {idea.content && (
                                                    <div className="text-sm text-gray-500 mt-1">
                                                        {idea.content.substring(0, 100)}...
                                                    </div>
                                                )}
                                            </td>
                                            <td>{idea.author}</td>
                                            <td>{getStatusBadge(idea.status)}</td>
                                            <td>
                                                {idea.categories.map((cat) => (
                                                    <span key={cat.id} className="feedlane-badge feedlane-badge--info mr-1">
                                                        {cat.name}
                                                    </span>
                                                ))}
                                            </td>
                                            <td>
                                                <span className="font-medium">{idea.votes}</span>
                                            </td>
                                            <td>
                                                {new Date(idea.date).toLocaleDateString()}
                                            </td>
                                            <td>
                                                <div className="feedlane-table__actions">
                                                    <select
                                                        value={idea.status}
                                                        onChange={(e) => updateIdeaStatus(idea.id, e.target.value)}
                                                        className="feedlane-select text-xs"
                                                    >
                                                        <option value="publish">Published</option>
                                                        <option value="draft">Draft</option>
                                                        <option value="pending">Pending</option>
                                                        <option value="private">Private</option>
                                                    </select>
                                                    <button
                                                        onClick={() => deleteIdea(idea.id)}
                                                        className="feedlane-btn feedlane-btn--danger feedlane-btn--small"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* Pagination */}
                        {totalPages > 1 && (
                            <div className="feedlane-pagination">
                                <div className="feedlane-pagination__info">
                                    Page {currentPage} of {totalPages}
                                </div>
                                <div className="feedlane-pagination__nav">
                                    <button
                                        onClick={() => setCurrentPage(currentPage - 1)}
                                        disabled={currentPage === 1}
                                        className="feedlane-pagination__btn"
                                    >
                                        Previous
                                    </button>
                                    <button
                                        onClick={() => setCurrentPage(currentPage + 1)}
                                        disabled={currentPage === totalPages}
                                        className="feedlane-pagination__btn"
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="feedlane-empty">
                        <div className="feedlane-empty__icon">💡</div>
                        <h3>No Ideas Found</h3>
                        <p>No ideas have been submitted yet or match your current filter.</p>
                    </div>
                )}
            </div>
            </div>
        </div>
    );
};

export default IdeasManagement;
