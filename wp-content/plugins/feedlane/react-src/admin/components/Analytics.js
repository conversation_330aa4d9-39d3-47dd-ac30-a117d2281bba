import React from 'react';

const Analytics = () => {
    // Mock data for now
    const analyticsData = {
        totalViews: 1234,
        totalReactions: 567,
        totalVotes: 234,
        topPosts: [
            { id: 1, title: 'New Feature Release', views: 234, reactions: 45 },
            { id: 2, title: 'Bug Fix Update', views: 189, reactions: 32 },
            { id: 3, title: 'Roadmap Update', views: 156, reactions: 28 }
        ],
        topIdeas: [
            { id: 1, title: 'Dark Mode Support', votes: 89, category: 'Feature Request' },
            { id: 2, title: 'Mobile App', votes: 67, category: 'Feature Request' },
            { id: 3, title: 'Better Search', votes: 45, category: 'Improvement' }
        ]
    };

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Analytics</h1>
                    <p>Track engagement and performance metrics</p>
                </div>

            <div className="feedlane-admin__content">
                <div className="feedlane-admin__grid">
                    <div className="feedlane-admin__card">
                        <div className="stat-number">{analyticsData.totalViews}</div>
                        <div className="stat-label">Total Views</div>
                        <p>Post and idea views</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{analyticsData.totalReactions}</div>
                        <div className="stat-label">Total Reactions</div>
                        <p>Emoji reactions on posts</p>
                    </div>

                    <div className="feedlane-admin__card">
                        <div className="stat-number">{analyticsData.totalVotes}</div>
                        <div className="stat-label">Total Votes</div>
                        <p>Votes on ideas</p>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
                    {/* Top Posts */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Posts</h3>
                        <div className="space-y-3">
                            {analyticsData.topPosts.map(post => (
                                <div key={post.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h4 className="font-medium text-gray-900">{post.title}</h4>
                                        <p className="text-sm text-gray-500">{post.views} views • {post.reactions} reactions</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Top Ideas */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Ideas</h3>
                        <div className="space-y-3">
                            {analyticsData.topIdeas.map(idea => (
                                <div key={idea.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h4 className="font-medium text-gray-900">{idea.title}</h4>
                                        <p className="text-sm text-gray-500">{idea.category} • {idea.votes} votes</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    );
};

export default Analytics;
