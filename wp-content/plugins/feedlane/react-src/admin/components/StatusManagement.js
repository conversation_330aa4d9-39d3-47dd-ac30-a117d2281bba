import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const StatusManagement = () => {
    const [statuses, setStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingStatus, setEditingStatus] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        slug: '',
        description: '',
        color: '#6B7280'
    });

    useEffect(() => {
        loadStatuses();
    }, []);

    const loadStatuses = async () => {
        try {
            setLoading(true);
            const formData = new FormData();
            formData.append('action', 'feedlane_get_statuses');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setStatuses(data.data);
            } else {
                toast.error('Failed to load statuses');
            }
        } catch (error) {
            toast.error('Failed to load statuses');
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            const submitData = new FormData();
            submitData.append('action', 'feedlane_save_status');
            submitData.append('nonce', feedlaneAdmin.nonce);

            if (editingStatus) {
                submitData.append('status_id', editingStatus.id);
            }

            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: submitData
            });

            const data = await response.json();
            if (data.success) {
                toast.success(editingStatus ? 'Status updated successfully' : 'Status created successfully');
                setShowModal(false);
                setEditingStatus(null);
                setFormData({ name: '', slug: '', description: '', color: '#6B7280' });
                loadStatuses();
            } else {
                toast.error(data.data || 'Failed to save status');
            }
        } catch (error) {
            toast.error('Failed to save status');
        }
    };

    const handleEdit = (status) => {
        setEditingStatus(status);
        setFormData({
            name: status.name,
            slug: status.slug,
            description: status.description,
            color: status.color
        });
        setShowModal(true);
    };

    const handleDelete = async (statusId) => {
        if (!confirm('Are you sure you want to delete this status?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_delete_status');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('status_id', statusId);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Status deleted successfully');
                loadStatuses();
            } else {
                toast.error(data.data || 'Failed to delete status');
            }
        } catch (error) {
            toast.error('Failed to delete status');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    if (loading) {
        return (
            <div className="feedlane-admin-wrapper">
                <div className="feedlane-admin">
                    <div className="feedlane-admin__header">
                        <h1>Status Management</h1>
                        <p>Manage roadmap statuses</p>
                    </div>
                    <div className="feedlane-admin__content">
                        <div className="feedlane-loading">
                            <div className="feedlane-loading__spinner"></div>
                            <p>Loading statuses...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Status Management</h1>
                    <p>Manage roadmap statuses</p>
                </div>

            <div className="feedlane-admin__content">
                {/* Add Status Button */}
                <div className="mb-6">
                    <button
                        onClick={() => {
                            setEditingStatus(null);
                            setFormData({ name: '', slug: '', description: '', color: '#6B7280' });
                            setShowModal(true);
                        }}
                        className="feedlane-btn feedlane-btn--primary"
                    >
                        Add New Status
                    </button>
                </div>

                {/* Statuses Table */}
                {statuses.length > 0 ? (
                    <div className="feedlane-card">
                        <div className="feedlane-card__content">
                            <table className="feedlane-table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Slug</th>
                                        <th>Description</th>
                                        <th>Color</th>
                                        <th>Ideas Count</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {statuses.map((status) => (
                                        <tr key={status.id}>
                                            <td>
                                                <div className="font-medium text-gray-900">
                                                    {status.name}
                                                </div>
                                            </td>
                                            <td>
                                                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                                    {status.slug}
                                                </code>
                                            </td>
                                            <td>
                                                <div className="text-sm text-gray-600">
                                                    {status.description || 'No description'}
                                                </div>
                                            </td>
                                            <td>
                                                <div className="flex items-center gap-2">
                                                    <div
                                                        className="w-6 h-6 rounded border border-gray-300"
                                                        style={{ backgroundColor: status.color }}
                                                    ></div>
                                                    <span className="text-sm text-gray-600">
                                                        {status.color}
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <span className="feedlane-badge feedlane-badge--info">
                                                    {status.count}
                                                </span>
                                            </td>
                                            <td>
                                                <div className="feedlane-table__actions">
                                                    <button
                                                        onClick={() => handleEdit(status)}
                                                        className="feedlane-btn feedlane-btn--secondary feedlane-btn--small"
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(status.id)}
                                                        className="feedlane-btn feedlane-btn--danger feedlane-btn--small"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ) : (
                    <div className="feedlane-empty">
                        <div className="feedlane-empty__icon">📊</div>
                        <h3>No Statuses Found</h3>
                        <p>Create your first status to track idea progress.</p>
                    </div>
                )}

                {/* Modal */}
                {showModal && (
                    <div className="feedlane-modal">
                        <div className="feedlane-modal__backdrop" onClick={() => setShowModal(false)}></div>
                        <div className="feedlane-modal__container">
                            <div className="feedlane-modal__content">
                                <div className="feedlane-modal__header">
                                    <h3>{editingStatus ? 'Edit Status' : 'Add New Status'}</h3>
                                    <button
                                        onClick={() => setShowModal(false)}
                                        className="feedlane-modal__close"
                                    >
                                        ✕
                                    </button>
                                </div>
                                <div className="feedlane-modal__body">
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div className="feedlane-form__field">
                                            <label htmlFor="status-name">Name *</label>
                                            <input
                                                type="text"
                                                id="status-name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                className="feedlane-input"
                                                required
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="status-slug">Slug</label>
                                            <input
                                                type="text"
                                                id="status-slug"
                                                name="slug"
                                                value={formData.slug}
                                                onChange={handleInputChange}
                                                className="feedlane-input"
                                                placeholder="Leave empty to auto-generate"
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="status-description">Description</label>
                                            <textarea
                                                id="status-description"
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                className="feedlane-textarea"
                                                rows="3"
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="status-color">Color</label>
                                            <div className="feedlane-color-picker">
                                                <input
                                                    type="color"
                                                    id="status-color"
                                                    name="color"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    className="feedlane-color-input"
                                                />
                                                <input
                                                    type="text"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    name="color"
                                                    className="feedlane-color-text"
                                                    placeholder="#6B7280"
                                                />
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div className="feedlane-modal__footer">
                                    <button
                                        type="button"
                                        onClick={() => setShowModal(false)}
                                        className="feedlane-btn feedlane-btn--secondary"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        onClick={handleSubmit}
                                        className="feedlane-btn feedlane-btn--primary"
                                    >
                                        {editingStatus ? 'Update Status' : 'Create Status'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            </div>
        </div>
    );
};

export default StatusManagement;
