import React, { useState, useEffect } from 'react';
import { FiSearch, FiPlus, FiEye, FiEyeOff, FiChevronUp, FiMessageSquare, FiMoreHorizontal, FiEdit, FiTrash2 } from 'react-icons/fi';
import toast from 'react-hot-toast';
import IdeaModal from './IdeaModal';
import { cleanTitle, formatContentForDisplay } from '../../utils/contentParser';

const IdeaManager = () => {
    const [ideas, setIdeas] = useState([]);
    const [categories, setCategories] = useState([]);
    const [statuses, setStatuses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [sortBy, setSortBy] = useState('date');
    const [showModal, setShowModal] = useState(false);
    const [editingIdea, setEditingIdea] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    useEffect(() => {
        loadData();
    }, [currentPage, searchTerm, sortBy]);

    const loadData = async () => {
        try {
            setLoading(true);
            await Promise.all([loadIdeas(), loadCategories(), loadStatus()]);
        } catch (error) {
            toast.error('Failed to load data');
        } finally {
            setLoading(false);
        }
    };

    const loadIdeas = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_ideas');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('page', currentPage);
            formData.append('search', searchTerm);
            formData.append('orderby', sortBy);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setIdeas(data.data.ideas || []);
                setTotalPages(data.data.pages || 1);
            } else {
                throw new Error(data.data || 'Failed to load ideas');
            }
        } catch (error) {
            console.error('Error loading ideas:', error);
            throw error;
        }
    };

    const loadCategories = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_categories');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setCategories(data.data);
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    };

    const loadStatus = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_status');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setStatuses(data.data);
            }
        } catch (error) {
            console.error('Error loading status:', error);
        }
    };

    const handleCreateIdea = () => {
        setEditingIdea(null);
        setShowModal(true);
    };

    const handleEditIdea = (idea) => {
        setEditingIdea(idea);
        setShowModal(true);
    };

    const handleDeleteIdea = async (ideaId) => {
        if (!confirm('Are you sure you want to delete this idea?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_delete_idea');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Idea deleted successfully');
                loadIdeas();
            } else {
                toast.error(data.data || 'Failed to delete idea');
            }
        } catch (error) {
            toast.error('Failed to delete idea');
        }
    };

    const toggleVisibility = async (ideaId, currentVisibility) => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_toggle_idea_visibility');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);
            formData.append('visibility', currentVisibility === 'public' ? 'private' : 'public');

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Visibility updated');
                loadIdeas();
            } else {
                toast.error(data.data || 'Failed to update visibility');
            }
        } catch (error) {
            toast.error('Failed to update visibility');
        }
    };

    const handleModalClose = () => {
        setShowModal(false);
        setEditingIdea(null);
    };

    const handleIdeaSaved = () => {
        setShowModal(false);
        setEditingIdea(null);
        loadIdeas();
    };

    const getAuthorInitials = (name) => {
        if (!name) return '?';
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const getStatusInfo = (statusSlug) => {
        const status = statuses.find(s => s.slug === statusSlug);
        if (!status) return { name: 'Unknown', color: '#6B7280' };
        return status;
    };

    const getCategoryInfo = (categorySlug) => {
        const category = categories.find(c => c.slug === categorySlug);
        if (!category) return { name: 'Uncategorized', color: '#6B7280' };
        return category;
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
            year: '2-digit'
        });
    };

    if (loading) {
        return (
            <div className="feedlane-admin-wrapper">
                <div className="feedlane-admin">
                    <div className="feedlane-loading">
                        <div className="feedlane-loading__spinner"></div>
                        <p>Loading ideas...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>All Ideas</h1>
                    <p>Manage and moderate submitted ideas</p>
                </div>

                <div className="feedlane-admin__content">
                    {/* Top Bar */}
                    <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                            {/* Search */}
                            <div className="relative">
                                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <input
                                    type="text"
                                    placeholder="Search your ideas"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                                />
                            </div>

                            {/* Sort By */}
                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="date">Sort by Date</option>
                                <option value="title">Sort by Title</option>
                                <option value="votes">Sort by Votes</option>
                                <option value="comments">Sort by Comments</option>
                            </select>
                        </div>

                        {/* Create Button */}
                        <button
                            onClick={handleCreateIdea}
                            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 font-medium"
                        >
                            <FiPlus className="w-4 h-4" />
                            CREATE IDEA
                        </button>
                    </div>

                    {/* Ideas Table */}
                    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                        <table className="w-full text-sm">
                            <thead className="bg-gray-50 border-b border-gray-200">
                                <tr>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-12"></th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-16">BY</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700">TITLE</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700">STATUS</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-20">VOTES</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-24">COMMENTS</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700">CATEGORIES</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-24">DATE</th>
                                    <th className="px-4 py-3 text-left font-semibold text-gray-700 w-20"></th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-100">
                                {ideas.map((idea) => (
                                    <IdeaRow
                                        key={idea.id}
                                        idea={idea}
                                        onEdit={handleEditIdea}
                                        onDelete={handleDeleteIdea}
                                        onToggleVisibility={toggleVisibility}
                                        getAuthorInitials={getAuthorInitials}
                                        getStatusInfo={getStatusInfo}
                                        getCategoryInfo={getCategoryInfo}
                                        formatDate={formatDate}
                                    />
                                ))}
                            </tbody>
                        </table>

                        {ideas.length === 0 && (
                            <div className="text-center py-12">
                                <div className="text-gray-400 text-lg mb-2">💡</div>
                                <h3 className="text-lg font-medium text-gray-900 mb-1">No ideas found</h3>
                                <p className="text-gray-500">Get started by creating your first idea.</p>
                            </div>
                        )}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex items-center justify-between mt-6">
                            <div className="text-sm text-gray-700">
                                Page {currentPage} of {totalPages}
                            </div>
                            <div className="flex gap-2">
                                <button
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Previous
                                </button>
                                <button
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage === totalPages}
                                    className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Next
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Modal */}
                    {showModal && (
                        <IdeaModal
                            isOpen={showModal}
                            onClose={handleModalClose}
                            onSave={handleIdeaSaved}
                            idea={editingIdea}
                            categories={categories}
                            statuses={statuses}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

const IdeaRow = ({
    idea,
    onEdit,
    onDelete,
    onToggleVisibility,
    getAuthorInitials,
    getStatusInfo,
    getCategoryInfo,
    formatDate
}) => {
    const [showActions, setShowActions] = useState(false);
    const statusInfo = getStatusInfo(idea.status);

    return (
        <tr className="hover:bg-gray-50">
            {/* Visibility Toggle */}
            <td className="px-4 py-3">
                <button
                    onClick={() => onToggleVisibility(idea.id, idea.visibility)}
                    className="text-gray-400 hover:text-gray-600"
                    title={idea.visibility === 'public' ? 'Make private' : 'Make public'}
                >
                    {idea.visibility === 'public' ? (
                        <FiEye className="w-4 h-4" />
                    ) : (
                        <FiEyeOff className="w-4 h-4" />
                    )}
                </button>
            </td>

            {/* Author */}
            <td className="px-4 py-3">
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium text-gray-600">
                    {getAuthorInitials(idea.author_name)}
                </div>
            </td>

            {/* Title */}
            <td className="px-4 py-3">
                <button
                    onClick={() => onEdit(idea)}
                    className="text-blue-600 hover:underline font-medium text-left"
                >
                    {cleanTitle(idea.title)}
                </button>
                {idea.content && (
                    <div className="text-xs text-gray-500 mt-1">
                        {formatContentForDisplay(idea.content, 60)}
                    </div>
                )}
            </td>

            {/* Status */}
            <td className="px-4 py-3">
                <span
                    className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
                    style={{
                        backgroundColor: statusInfo.color + '20',
                        color: statusInfo.color
                    }}
                >
                    <span
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: statusInfo.color }}
                    ></span>
                    {statusInfo.name}
                </span>
            </td>

            {/* Votes */}
            <td className="px-4 py-3">
                <div className="flex items-center gap-1">
                    <FiChevronUp className="w-4 h-4 text-gray-400" />
                    <span className="font-medium">{idea.vote_count || 0}</span>
                </div>
            </td>

            {/* Comments */}
            <td className="px-4 py-3">
                <div className="flex items-center gap-1">
                    <FiMessageSquare className="w-4 h-4 text-gray-400" />
                    <span>{idea.comment_count || 0}</span>
                </div>
            </td>

            {/* Categories */}
            <td className="px-4 py-3">
                <div className="flex flex-wrap gap-1">
                    {idea.categories && idea.categories.length > 0 ? (
                        idea.categories.slice(0, 2).map((category) => {
                            const categoryInfo = getCategoryInfo(category.slug);
                            return (
                                <span
                                    key={category.id}
                                    className="inline-block px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-700"
                                >
                                    {categoryInfo.name}
                                </span>
                            );
                        })
                    ) : (
                        <span className="text-xs text-gray-400">No categories</span>
                    )}
                    {idea.categories && idea.categories.length > 2 && (
                        <span className="text-xs text-gray-400">+{idea.categories.length - 2}</span>
                    )}
                </div>
            </td>

            {/* Date */}
            <td className="px-4 py-3 text-gray-500">
                {formatDate(idea.date)}
            </td>

            {/* Actions */}
            <td className="px-4 py-3">
                <div className="relative">
                    <button
                        onClick={() => setShowActions(!showActions)}
                        className="text-gray-400 hover:text-gray-600 p-1"
                    >
                        <FiMoreHorizontal className="w-4 h-4" />
                    </button>

                    {showActions && (
                        <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                            <button
                                onClick={() => {
                                    onEdit(idea);
                                    setShowActions(false);
                                }}
                                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                            >
                                <FiEdit className="w-4 h-4" />
                                Edit
                            </button>
                            <button
                                onClick={() => {
                                    onDelete(idea.id);
                                    setShowActions(false);
                                }}
                                className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                            >
                                <FiTrash2 className="w-4 h-4" />
                                Delete
                            </button>
                        </div>
                    )}
                </div>
            </td>
        </tr>
    );
};

export default IdeaManager;
