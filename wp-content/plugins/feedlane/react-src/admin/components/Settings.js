import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const Settings = () => {
    const [settings, setSettings] = useState({
        feedlane_enable_newsfeed: true,
        feedlane_enable_ideas: true,
        feedlane_enable_roadmap: true,
        feedlane_enable_guest_submissions: true,
        feedlane_enable_floating_sidebar: true,
        feedlane_sidebar_position: 'left',
        feedlane_primary_color: '#0ea5e9',
        feedlane_firebase_config: '',
        feedlane_firebase_webhook_secret: ''
    });

    const [saving, setSaving] = useState(false);
    const [loading, setLoading] = useState(true);

    // Load current settings on component mount
    useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_settings');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    setSettings(data.data);
                }
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSaving(true);

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_save_settings');
            formData.append('nonce', feedlaneAdmin.nonce);

            // Add all settings to form data
            Object.keys(settings).forEach(key => {
                formData.append(key, settings[key]);
            });

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                toast.success('Settings saved successfully!');
            } else {
                toast.error(data.data || 'Failed to save settings');
            }
        } catch (error) {
            toast.error('Failed to save settings');
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Feedlane Settings</h1>
                    <p>Configure your feedback system</p>
                </div>
                <div className="feedlane-admin__content">
                    <div className="feedlane-loading">
                        <div className="feedlane-loading__spinner"></div>
                        <p>Loading settings...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Feedlane Settings</h1>
                    <p>Configure your feedback system</p>
                </div>

            <div className="feedlane-admin__content">
                <form onSubmit={handleSubmit} className="feedlane-form">
                    {/* General Settings */}
                    <div className="feedlane-form__section">
                        <h3>General Settings</h3>
                        <p>Configure which tabs are enabled and basic appearance</p>

                        <div className="space-y-4">
                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="feedlane_enable_newsfeed"
                                        checked={settings.feedlane_enable_newsfeed}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Newsfeed Tab
                                </label>
                                <div className="description">Show the newsfeed tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="feedlane_enable_ideas"
                                        checked={settings.feedlane_enable_ideas}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Ideas Tab
                                </label>
                                <div className="description">Show the ideas submission tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="feedlane_enable_roadmap"
                                        checked={settings.feedlane_enable_roadmap}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Roadmap Tab
                                </label>
                                <div className="description">Show the roadmap tab in the sidebar</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="feedlane_enable_guest_submissions"
                                        checked={settings.feedlane_enable_guest_submissions}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Guest Submissions
                                </label>
                                <div className="description">Allow non-logged-in users to submit feedback and ideas</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="feedlane_enable_floating_sidebar"
                                        checked={settings.feedlane_enable_floating_sidebar}
                                        onChange={handleInputChange}
                                        className="mr-2"
                                    />
                                    Enable Floating Sidebar
                                </label>
                                <div className="description">Show the floating feedback sidebar on all pages</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="feedlane_sidebar_position">Sidebar Position</label>
                                <select
                                    id="feedlane_sidebar_position"
                                    name="feedlane_sidebar_position"
                                    value={settings.feedlane_sidebar_position}
                                    onChange={handleInputChange}
                                    className="feedlane-select"
                                >
                                    <option value="left">Left</option>
                                    <option value="right">Right</option>
                                </select>
                                <div className="description">Choose which side of the screen the sidebar appears on</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="feedlane_primary_color">Primary Color</label>
                                <div className="feedlane-color-picker">
                                    <input
                                        type="color"
                                        id="feedlane_primary_color"
                                        name="feedlane_primary_color"
                                        value={settings.feedlane_primary_color}
                                        onChange={handleInputChange}
                                        className="feedlane-color-input"
                                    />
                                    <input
                                        type="text"
                                        value={settings.feedlane_primary_color}
                                        onChange={handleInputChange}
                                        name="feedlane_primary_color"
                                        className="feedlane-color-text"
                                        placeholder="#0ea5e9"
                                    />
                                </div>
                                <div className="description">Choose the primary color for the sidebar and buttons</div>
                            </div>
                        </div>
                    </div>

                    {/* Firebase Settings */}
                    <div className="feedlane-form__section">
                        <h3>Firebase Configuration</h3>
                        <p>Configure Firebase for real-time comments functionality</p>

                        <div className="space-y-4">
                            <div className="feedlane-form__field">
                                <label htmlFor="feedlane_firebase_config">Firebase Configuration JSON</label>
                                <textarea
                                    id="feedlane_firebase_config"
                                    name="feedlane_firebase_config"
                                    value={settings.feedlane_firebase_config}
                                    onChange={handleInputChange}
                                    rows="6"
                                    className="feedlane-textarea"
                                    placeholder='{"apiKey": "...", "authDomain": "...", "projectId": "..."}'
                                />
                                <div className="description">Paste your Firebase configuration JSON here for real-time comments</div>
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="feedlane_firebase_webhook_secret">Webhook Secret</label>
                                <input
                                    type="password"
                                    id="feedlane_firebase_webhook_secret"
                                    name="feedlane_firebase_webhook_secret"
                                    value={settings.feedlane_firebase_webhook_secret}
                                    onChange={handleInputChange}
                                    className="feedlane-input"
                                />
                                <div className="description">Secret key for Firebase webhook authentication</div>
                            </div>
                        </div>
                    </div>

                    <div className="feedlane-form__actions">
                        <button
                            type="submit"
                            disabled={saving}
                            className="feedlane-btn feedlane-btn--primary"
                        >
                            {saving ? (
                                <>
                                    <div className="feedlane-spinner feedlane-spinner--small"></div>
                                    Saving...
                                </>
                            ) : (
                                'Save Settings'
                            )}
                        </button>
                    </div>
                </form>
            </div>
            </div>
        </div>
    );
};

export default Settings;
