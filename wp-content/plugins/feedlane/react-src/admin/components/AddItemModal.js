import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const AddItemModal = ({ isOpen, onClose, selectedStatus, statuses, onItemAdded }) => {
    const [formData, setFormData] = useState({
        title: '',
        details: '',
        category: '',
        status: selectedStatus || '',
        email: '',
        first_name: '',
        last_name: '',
    });
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (isOpen) {
            loadCategories();
            setFormData(prev => ({
                ...prev,
                status: selectedStatus || ''
            }));
        }
    }, [isOpen, selectedStatus]);

    // Handle ESC key to close modal
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const loadCategories = async () => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_get_categories');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setCategories(data.data);
                // Set default category if none selected
                if (!formData.category && data.data.length > 0) {
                    setFormData(prev => ({
                        ...prev,
                        category: data.data[0].slug
                    }));
                }
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!formData.title.trim()) {
            toast.error('Title is required');
            return;
        }

        if (!formData.details.trim()) {
            toast.error('Details are required');
            return;
        }

        try {
            setLoading(true);

            const submitData = {
                title: formData.title,
                details: formData.details,
                category: formData.category,
                email: formData.email,
                first_name: formData.first_name,
                last_name: formData.last_name,
            };

            const response = await fetch(`${feedlaneAdmin.rest_url}feedlane/v1/ideas`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': feedlaneAdmin.nonce,
                },
                body: JSON.stringify(submitData),
            });

            const data = await response.json();

            if (response.ok) {
                // Update the idea status if different from default
                if (formData.status && formData.status !== 'pending') {
                    await updateIdeaStatus(data.id, formData.status);
                }

                toast.success('Idea added successfully');
                onItemAdded();

                // Reset form
                setFormData({
                    title: '',
                    details: '',
                    category: categories[0]?.slug || '',
                    status: selectedStatus || '',
                    email: '',
                    first_name: '',
                    last_name: '',
                });
            } else {
                throw new Error(data.message || 'Failed to add idea');
            }
        } catch (error) {
            console.error('Error adding idea:', error);
            toast.error(error.message || 'Failed to add idea');
        } finally {
            setLoading(false);
        }
    };

    const updateIdeaStatus = async (ideaId, status) => {
        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_update_idea_status');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('idea_id', ideaId);
            formData.append('status', status);

            await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });
        } catch (error) {
            console.error('Error updating idea status:', error);
        }
    };

    if (!isOpen) return null;

    return (
        <div className="feedlane-modal" onClick={onClose}>
            <div className="feedlane-modal__backdrop"></div>
            <div className="feedlane-modal__container">
                <div
                    className="feedlane-modal__content max-w-2xl"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="feedlane-modal__header">
                        <h3>Add New Roadmap Item</h3>
                        <button
                            type="button"
                            onClick={onClose}
                            className="feedlane-modal__close"
                            aria-label="Close modal"
                        >
                            ✕
                        </button>
                    </div>

                    <div className="feedlane-modal__body">
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="feedlane-form__field">
                                <label htmlFor="idea-title">Title *</label>
                                <input
                                    type="text"
                                    id="idea-title"
                                    name="title"
                                    value={formData.title}
                                    onChange={handleInputChange}
                                    className="feedlane-input"
                                    placeholder="e.g., Add voting option within widget"
                                    required
                                    autoFocus
                                />
                            </div>

                            <div className="feedlane-form__field">
                                <label htmlFor="idea-details">Details *</label>
                                <textarea
                                    id="idea-details"
                                    name="details"
                                    value={formData.details}
                                    onChange={handleInputChange}
                                    className="feedlane-textarea"
                                    rows="4"
                                    placeholder="Describe the idea in detail..."
                                    required
                                />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="feedlane-form__field">
                                    <label htmlFor="idea-category">Category</label>
                                    <select
                                        id="idea-category"
                                        name="category"
                                        value={formData.category}
                                        onChange={handleInputChange}
                                        className="feedlane-select"
                                    >
                                        {categories.map((category) => (
                                            <option key={category.slug} value={category.slug}>
                                                {category.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="feedlane-form__field">
                                    <label htmlFor="idea-status">Status</label>
                                    <select
                                        id="idea-status"
                                        name="status"
                                        value={formData.status}
                                        onChange={handleInputChange}
                                        className="feedlane-select"
                                    >
                                        {statuses.map((status) => (
                                            <option key={status.slug} value={status.slug}>
                                                {status.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                                <div className="feedlane-form__field">
                                    <label htmlFor="idea-first-name">First Name</label>
                                    <input
                                        type="text"
                                        id="idea-first-name"
                                        name="first_name"
                                        value={formData.first_name}
                                        onChange={handleInputChange}
                                        className="feedlane-input"
                                        placeholder="John"
                                    />
                                </div>

                                <div className="feedlane-form__field">
                                    <label htmlFor="idea-last-name">Last Name</label>
                                    <input
                                        type="text"
                                        id="idea-last-name"
                                        name="last_name"
                                        value={formData.last_name}
                                        onChange={handleInputChange}
                                        className="feedlane-input"
                                        placeholder="Doe"
                                    />
                                </div>

                                <div className="feedlane-form__field">
                                    <label htmlFor="idea-email">Email</label>
                                    <input
                                        type="email"
                                        id="idea-email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleInputChange}
                                        className="feedlane-input"
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                            </div>
                        </form>
                    </div>

                    <div className="feedlane-modal__footer">
                        <button
                            type="button"
                            onClick={onClose}
                            className="feedlane-btn feedlane-btn--secondary"
                            disabled={loading}
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={handleSubmit}
                            className="feedlane-btn feedlane-btn--primary"
                            disabled={loading}
                        >
                            {loading ? 'Adding...' : 'Add Item'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddItemModal;
