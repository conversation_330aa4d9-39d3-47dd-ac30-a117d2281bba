import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

const CategoriesManagement = () => {
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingCategory, setEditingCategory] = useState(null);
    const [formData, setFormData] = useState({
        name: '',
        slug: '',
        description: '',
        color: '#10B981'
    });

    useEffect(() => {
        loadCategories();
    }, []);

    // Handle ESC key to close modal
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape' && showModal) {
                setShowModal(false);
            }
        };

        if (showModal) {
            document.addEventListener('keydown', handleEscKey);
            // Prevent body scroll when modal is open
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'unset';
        };
    }, [showModal]);

    const loadCategories = async () => {
        try {
            setLoading(true);
            const formData = new FormData();
            formData.append('action', 'feedlane_get_categories');
            formData.append('nonce', feedlaneAdmin.nonce);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                setCategories(data.data);
            } else {
                toast.error('Failed to load categories');
            }
        } catch (error) {
            toast.error('Failed to load categories');
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            const submitData = new FormData();
            submitData.append('action', 'feedlane_save_category');
            submitData.append('nonce', feedlaneAdmin.nonce);

            if (editingCategory) {
                submitData.append('category_id', editingCategory.id);
            }

            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: submitData
            });

            const data = await response.json();
            if (data.success) {
                toast.success(editingCategory ? 'Category updated successfully' : 'Category created successfully');
                setShowModal(false);
                setEditingCategory(null);
                setFormData({ name: '', slug: '', description: '', color: '#10B981' });
                loadCategories();
            } else {
                toast.error(data.data || 'Failed to save category');
            }
        } catch (error) {
            toast.error('Failed to save category');
        }
    };

    const handleEdit = (category) => {
        setEditingCategory(category);
        setFormData({
            name: category.name,
            slug: category.slug,
            description: category.description,
            color: category.color
        });
        setShowModal(true);
    };

    const handleDelete = async (categoryId) => {
        if (!confirm('Are you sure you want to delete this category?')) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'feedlane_delete_category');
            formData.append('nonce', feedlaneAdmin.nonce);
            formData.append('category_id', categoryId);

            const response = await fetch(feedlaneAdmin.ajax_url, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                toast.success('Category deleted successfully');
                loadCategories();
            } else {
                toast.error(data.data || 'Failed to delete category');
            }
        } catch (error) {
            toast.error('Failed to delete category');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    if (loading) {
        return (
            <div className="feedlane-admin-wrapper">
                <div className="feedlane-admin">
                    <div className="feedlane-admin__header">
                        <h1>Categories Management</h1>
                        <p>Manage idea categories</p>
                    </div>
                    <div className="feedlane-admin__content">
                        <div className="feedlane-loading">
                            <div className="feedlane-loading__spinner"></div>
                            <p>Loading categories...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="feedlane-admin-wrapper">
            <div className="feedlane-admin">
                <div className="feedlane-admin__header">
                    <h1>Categories Management</h1>
                    <p>Manage idea categories</p>
                </div>

            <div className="feedlane-admin__content">
                {/* Add Category Button */}
                <div className="mb-6">
                    <button
                        onClick={() => {
                            setEditingCategory(null);
                            setFormData({ name: '', slug: '', description: '', color: '#10B981' });
                            setShowModal(true);
                        }}
                        className="feedlane-btn feedlane-btn--primary"
                    >
                        Add New Category
                    </button>
                </div>

                {/* Categories Table */}
                {categories.length > 0 ? (
                    <div className="feedlane-card">
                        <div className="feedlane-card__content">
                            <table className="feedlane-table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Slug</th>
                                        <th>Description</th>
                                        <th>Color</th>
                                        <th>Ideas Count</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {categories.map((category) => (
                                        <tr key={category.id}>
                                            <td>
                                                <div className="font-medium text-gray-900">
                                                    {category.name}
                                                </div>
                                            </td>
                                            <td>
                                                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                                    {category.slug}
                                                </code>
                                            </td>
                                            <td>
                                                <div className="text-sm text-gray-600">
                                                    {category.description || 'No description'}
                                                </div>
                                            </td>
                                            <td>
                                                <div className="flex items-center gap-2">
                                                    <div
                                                        className="w-6 h-6 rounded border border-gray-300"
                                                        style={{ backgroundColor: category.color }}
                                                    ></div>
                                                    <span className="text-sm text-gray-600">
                                                        {category.color}
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <span className="feedlane-badge feedlane-badge--info">
                                                    {category.count}
                                                </span>
                                            </td>
                                            <td>
                                                <div className="feedlane-table__actions">
                                                    <button
                                                        onClick={() => handleEdit(category)}
                                                        className="feedlane-btn feedlane-btn--secondary feedlane-btn--small"
                                                    >
                                                        Edit
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(category.id)}
                                                        className="feedlane-btn feedlane-btn--danger feedlane-btn--small"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                ) : (
                    <div className="feedlane-empty">
                        <div className="feedlane-empty__icon">🏷️</div>
                        <h3>No Categories Found</h3>
                        <p>Create your first category to organize ideas.</p>
                    </div>
                )}

                {/* Modal */}
                {showModal && (
                    <div className="feedlane-modal" onClick={() => setShowModal(false)}>
                        <div className="feedlane-modal__backdrop"></div>
                        <div className="feedlane-modal__container">
                            <div
                                className="feedlane-modal__content"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <div className="feedlane-modal__header">
                                    <h3>{editingCategory ? 'Edit Category' : 'Add New Category'}</h3>
                                    <button
                                        type="button"
                                        onClick={() => setShowModal(false)}
                                        className="feedlane-modal__close"
                                        aria-label="Close modal"
                                    >
                                        ✕
                                    </button>
                                </div>
                                <div className="feedlane-modal__body">
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div className="feedlane-form__field">
                                            <label htmlFor="category-name">Name *</label>
                                            <input
                                                type="text"
                                                id="category-name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                className="feedlane-input"
                                                required
                                                autoFocus
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="category-slug">Slug</label>
                                            <input
                                                type="text"
                                                id="category-slug"
                                                name="slug"
                                                value={formData.slug}
                                                onChange={handleInputChange}
                                                className="feedlane-input"
                                                placeholder="Leave empty to auto-generate"
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="category-description">Description</label>
                                            <textarea
                                                id="category-description"
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                className="feedlane-textarea"
                                                rows="3"
                                            />
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="category-color">Color</label>
                                            <div className="feedlane-color-picker">
                                                <input
                                                    type="color"
                                                    id="category-color"
                                                    name="color"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    className="feedlane-color-input"
                                                />
                                                <input
                                                    type="text"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    name="color"
                                                    className="feedlane-color-text"
                                                    placeholder="#10B981"
                                                />
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div className="feedlane-modal__footer">
                                    <button
                                        type="button"
                                        onClick={() => setShowModal(false)}
                                        className="feedlane-btn feedlane-btn--secondary"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="button"
                                        onClick={handleSubmit}
                                        className="feedlane-btn feedlane-btn--primary"
                                    >
                                        {editingCategory ? 'Update Category' : 'Create Category'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            </div>
        </div>
    );
};

export default CategoriesManagement;
