import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { FiPlus, FiEdit2, FiTrash2, FiSave, FiX } from 'react-icons/fi';

const CategoriesManagement = () => {
    const [isCreating, setIsCreating] = useState(false);
    const [editingCategory, setEditingCategory] = useState(null);
    const [newCategory, setNewCategory] = useState({ name: '', description: '', color: '#10B981' });
    
    const queryClient = useQueryClient();
    
    // Fetch categories
    const { data: categories = [], isLoading, error } = useQuery({
        queryKey: ['categories'],
        queryFn: async () => {
            const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories?per_page=100`, {
                headers: {
                    'X-WP-Nonce': window.feedlaneData.nonce,
                },
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch categories');
            }
            
            return response.json();
        },
    });
    
    // Create category mutation
    const createMutation = useMutation({
        mutationFn: async (categoryData) => {
            const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.feedlaneData.nonce,
                },
                body: JSON.stringify(categoryData),
            });
            
            if (!response.ok) {
                throw new Error('Failed to create category');
            }
            
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries(['categories']);
            setIsCreating(false);
            setNewCategory({ name: '', description: '', color: '#10B981' });
            toast.success('Category created successfully!');
        },
        onError: (error) => {
            toast.error(error.message || 'Failed to create category');
        },
    });
    
    // Update category mutation
    const updateMutation = useMutation({
        mutationFn: async ({ id, ...categoryData }) => {
            const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.feedlaneData.nonce,
                },
                body: JSON.stringify(categoryData),
            });
            
            if (!response.ok) {
                throw new Error('Failed to update category');
            }
            
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries(['categories']);
            setEditingCategory(null);
            toast.success('Category updated successfully!');
        },
        onError: (error) => {
            toast.error(error.message || 'Failed to update category');
        },
    });
    
    // Delete category mutation
    const deleteMutation = useMutation({
        mutationFn: async (id) => {
            const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/idea-categories/${id}?force=true`, {
                method: 'DELETE',
                headers: {
                    'X-WP-Nonce': window.feedlaneData.nonce,
                },
            });
            
            if (!response.ok) {
                throw new Error('Failed to delete category');
            }
            
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries(['categories']);
            toast.success('Category deleted successfully!');
        },
        onError: (error) => {
            toast.error(error.message || 'Failed to delete category');
        },
    });
    
    const handleCreate = (e) => {
        e.preventDefault();
        if (!newCategory.name.trim()) {
            toast.error('Category name is required');
            return;
        }
        
        createMutation.mutate({
            name: newCategory.name,
            description: newCategory.description,
            meta: {
                color: newCategory.color,
            },
        });
    };
    
    const handleUpdate = (e) => {
        e.preventDefault();
        if (!editingCategory.name.trim()) {
            toast.error('Category name is required');
            return;
        }
        
        updateMutation.mutate({
            id: editingCategory.id,
            name: editingCategory.name,
            description: editingCategory.description,
            meta: {
                color: editingCategory.color,
            },
        });
    };
    
    const handleDelete = (id, name) => {
        if (window.confirm(`Are you sure you want to delete the category "${name}"? This action cannot be undone.`)) {
            deleteMutation.mutate(id);
        }
    };
    
    const startEditing = (category) => {
        setEditingCategory({
            id: category.id,
            name: category.name,
            description: category.description || '',
            color: category.meta?.color || '#10B981',
        });
    };
    
    if (isLoading) {
        return (
            <div className="feedlane-admin">
                <div className="feedlane-admin__content">
                    <div className="text-center py-12">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">Loading categories...</p>
                    </div>
                </div>
            </div>
        );
    }
    
    if (error) {
        return (
            <div className="feedlane-admin">
                <div className="feedlane-admin__content">
                    <div className="text-center py-12">
                        <p className="text-red-600">Failed to load categories: {error.message}</p>
                    </div>
                </div>
            </div>
        );
    }
    
    return (
        <div className="feedlane-admin">
            <div className="feedlane-admin__header">
                <h1>Categories Management</h1>
                <p>Manage idea categories for better organization</p>
            </div>
            
            <div className="feedlane-admin__content">
                {/* Add New Category Button */}
                <div className="mb-6">
                    <button
                        onClick={() => setIsCreating(true)}
                        className="feedlane-btn feedlane-btn--primary"
                        disabled={isCreating}
                    >
                        <FiPlus size={16} />
                        Add New Category
                    </button>
                </div>
                
                {/* Create Category Form */}
                {isCreating && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Category</h3>
                        <form onSubmit={handleCreate} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="new-name" className="block text-sm font-medium text-gray-700 mb-1">
                                        Name *
                                    </label>
                                    <input
                                        type="text"
                                        id="new-name"
                                        value={newCategory.name}
                                        onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        placeholder="e.g., Feature Request"
                                        required
                                    />
                                </div>
                                <div>
                                    <label htmlFor="new-color" className="block text-sm font-medium text-gray-700 mb-1">
                                        Color
                                    </label>
                                    <input
                                        type="color"
                                        id="new-color"
                                        value={newCategory.color}
                                        onChange={(e) => setNewCategory(prev => ({ ...prev, color: e.target.value }))}
                                        className="w-full h-10 border border-gray-300 rounded-lg"
                                    />
                                </div>
                            </div>
                            <div>
                                <label htmlFor="new-description" className="block text-sm font-medium text-gray-700 mb-1">
                                    Description
                                </label>
                                <textarea
                                    id="new-description"
                                    value={newCategory.description}
                                    onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    rows={3}
                                    placeholder="Optional description for this category"
                                />
                            </div>
                            <div className="flex gap-2">
                                <button
                                    type="submit"
                                    className="feedlane-btn feedlane-btn--primary"
                                    disabled={createMutation.isLoading}
                                >
                                    <FiSave size={16} />
                                    {createMutation.isLoading ? 'Creating...' : 'Create Category'}
                                </button>
                                <button
                                    type="button"
                                    onClick={() => {
                                        setIsCreating(false);
                                        setNewCategory({ name: '', description: '', color: '#10B981' });
                                    }}
                                    className="feedlane-btn feedlane-btn--secondary"
                                >
                                    <FiX size={16} />
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                )}
                
                {/* Categories List */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Categories ({categories.length})</h3>
                    </div>
                    
                    {categories.length === 0 ? (
                        <div className="text-center py-12">
                            <p className="text-gray-500">No categories found. Create your first category to get started.</p>
                        </div>
                    ) : (
                        <div className="divide-y divide-gray-200">
                            {categories.map(category => (
                                <CategoryRow
                                    key={category.id}
                                    category={category}
                                    isEditing={editingCategory?.id === category.id}
                                    editingCategory={editingCategory}
                                    setEditingCategory={setEditingCategory}
                                    onEdit={startEditing}
                                    onUpdate={handleUpdate}
                                    onDelete={handleDelete}
                                    isUpdating={updateMutation.isLoading}
                                    isDeleting={deleteMutation.isLoading}
                                />
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

const CategoryRow = ({ 
    category, 
    isEditing, 
    editingCategory, 
    setEditingCategory, 
    onEdit, 
    onUpdate, 
    onDelete, 
    isUpdating, 
    isDeleting 
}) => {
    if (isEditing) {
        return (
            <div className="p-6">
                <form onSubmit={onUpdate} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Name *
                            </label>
                            <input
                                type="text"
                                value={editingCategory.name}
                                onChange={(e) => setEditingCategory(prev => ({ ...prev, name: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Color
                            </label>
                            <input
                                type="color"
                                value={editingCategory.color}
                                onChange={(e) => setEditingCategory(prev => ({ ...prev, color: e.target.value }))}
                                className="w-full h-10 border border-gray-300 rounded-lg"
                            />
                        </div>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description
                        </label>
                        <textarea
                            value={editingCategory.description}
                            onChange={(e) => setEditingCategory(prev => ({ ...prev, description: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            rows={3}
                        />
                    </div>
                    <div className="flex gap-2">
                        <button
                            type="submit"
                            className="feedlane-btn feedlane-btn--primary feedlane-btn--small"
                            disabled={isUpdating}
                        >
                            <FiSave size={14} />
                            {isUpdating ? 'Saving...' : 'Save'}
                        </button>
                        <button
                            type="button"
                            onClick={() => setEditingCategory(null)}
                            className="feedlane-btn feedlane-btn--secondary feedlane-btn--small"
                        >
                            <FiX size={14} />
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        );
    }
    
    return (
        <div className="p-6 flex items-center justify-between">
            <div className="flex items-center gap-4">
                <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.meta?.color || '#10B981' }}
                />
                <div>
                    <h4 className="font-medium text-gray-900">{category.name}</h4>
                    {category.description && (
                        <p className="text-sm text-gray-500 mt-1">{category.description}</p>
                    )}
                    <p className="text-xs text-gray-400 mt-1">
                        {category.count || 0} ideas
                    </p>
                </div>
            </div>
            <div className="flex gap-2">
                <button
                    onClick={() => onEdit(category)}
                    className="feedlane-btn feedlane-btn--secondary feedlane-btn--small"
                    title="Edit category"
                >
                    <FiEdit2 size={14} />
                </button>
                <button
                    onClick={() => onDelete(category.id, category.name)}
                    className="feedlane-btn feedlane-btn--danger feedlane-btn--small"
                    disabled={isDeleting}
                    title="Delete category"
                >
                    <FiTrash2 size={14} />
                </button>
            </div>
        </div>
    );
};

export default CategoriesManagement;
