import React, { useState, useEffect } from 'react';
import { FiX, FiSave, FiMessageSquare, FiSend } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { cleanTitle, formatContentForDisplay } from '../../utils/contentParser';

const IdeaModal = ({ isOpen, onClose, onSave, idea, categories, statuses }) => {
    const [formData, setFormData] = useState({
        title: '',
        content: '',
        status: '',
        categories: [],
        visibility: 'public'
    });
    const [comments, setComments] = useState([]);
    const [newComment, setNewComment] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingComments, setLoadingComments] = useState(false);

    useEffect(() => {
        if (isOpen) {
            if (idea) {
                setFormData({
                    title: idea.title || '',
                    content: idea.content || '',
                    status: idea.status || '',
                    categories: idea.categories ? idea.categories.map(c => c.slug) : [],
                    visibility: idea.visibility || 'public'
                });
                loadComments(idea.id);
            } else {
                setFormData({
                    title: '',
                    content: '',
                    status: statuses[0]?.slug || '',
                    categories: [],
                    visibility: 'public'
                });
                setComments([]);
            }
        }
    }, [isOpen, idea, statuses]);

    // Handle ESC key to close modal
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const loadComments = async (ideaId) => {
        try {
            setLoadingComments(true);
            const response = await fetch(`${feedlaneAdmin.rest_url}wp/v2/comments?post=${ideaId}&per_page=100`, {
                headers: {
                    'X-WP-Nonce': feedlaneAdmin.nonce,
                },
            });

            if (response.ok) {
                const commentsData = await response.json();
                setComments(commentsData);
            }
        } catch (error) {
            console.error('Error loading comments:', error);
        } finally {
            setLoadingComments(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        
        if (name === 'categories') {
            const categorySlug = value;
            setFormData(prev => ({
                ...prev,
                categories: checked 
                    ? [...prev.categories, categorySlug]
                    : prev.categories.filter(c => c !== categorySlug)
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : value
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!formData.title.trim()) {
            toast.error('Title is required');
            return;
        }

        if (!formData.content.trim()) {
            toast.error('Description is required');
            return;
        }

        try {
            setLoading(true);

            const submitData = {
                title: formData.title,
                content: formData.content,
                status: formData.status,
                categories: formData.categories,
                visibility: formData.visibility
            };

            if (idea) {
                // Update existing idea
                const formDataToSend = new FormData();
                formDataToSend.append('action', 'feedlane_update_idea');
                formDataToSend.append('nonce', feedlaneAdmin.nonce);
                formDataToSend.append('idea_id', idea.id);
                Object.keys(submitData).forEach(key => {
                    if (key === 'categories') {
                        formDataToSend.append(key, JSON.stringify(submitData[key]));
                    } else {
                        formDataToSend.append(key, submitData[key]);
                    }
                });

                const response = await fetch(feedlaneAdmin.ajax_url, {
                    method: 'POST',
                    body: formDataToSend
                });

                const data = await response.json();
                if (data.success) {
                    toast.success('Idea updated successfully');
                    onSave();
                } else {
                    throw new Error(data.data || 'Failed to update idea');
                }
            } else {
                // Create new idea
                const response = await fetch(`${feedlaneAdmin.rest_url}feedlane/v1/ideas`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': feedlaneAdmin.nonce,
                    },
                    body: JSON.stringify(submitData),
                });

                const data = await response.json();
                if (response.ok) {
                    toast.success('Idea created successfully');
                    onSave();
                } else {
                    throw new Error(data.message || 'Failed to create idea');
                }
            }
        } catch (error) {
            console.error('Error saving idea:', error);
            toast.error(error.message || 'Failed to save idea');
        } finally {
            setLoading(false);
        }
    };

    const handleAddComment = async (e) => {
        e.preventDefault();
        
        if (!newComment.trim()) {
            toast.error('Comment cannot be empty');
            return;
        }

        if (!idea) {
            toast.error('Save the idea first before adding comments');
            return;
        }

        try {
            const response = await fetch(`${feedlaneAdmin.rest_url}wp/v2/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': feedlaneAdmin.nonce,
                },
                body: JSON.stringify({
                    post: idea.id,
                    content: newComment,
                    status: 'approved'
                }),
            });

            if (response.ok) {
                const comment = await response.json();
                setComments(prev => [...prev, comment]);
                setNewComment('');
                toast.success('Comment added successfully');
            } else {
                throw new Error('Failed to add comment');
            }
        } catch (error) {
            console.error('Error adding comment:', error);
            toast.error('Failed to add comment');
        }
    };

    const formatCommentDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getAuthorInitials = (name) => {
        if (!name) return '?';
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    if (!isOpen) return null;

    return (
        <div className="feedlane-modal" onClick={onClose}>
            <div className="feedlane-modal__backdrop"></div>
            <div className="feedlane-modal__container">
                <div 
                    className="feedlane-modal__content max-w-4xl"
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="feedlane-modal__header">
                        <h3>{idea ? 'Edit Idea' : 'Create New Idea'}</h3>
                        <button
                            type="button"
                            onClick={onClose}
                            className="feedlane-modal__close"
                            aria-label="Close modal"
                        >
                            <FiX className="w-5 h-5" />
                        </button>
                    </div>
                    
                    <div className="feedlane-modal__body">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Main Form */}
                            <div className="lg:col-span-2">
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="feedlane-form__field">
                                        <label htmlFor="idea-title">Title *</label>
                                        <input
                                            type="text"
                                            id="idea-title"
                                            name="title"
                                            value={formData.title}
                                            onChange={handleInputChange}
                                            className="feedlane-input"
                                            placeholder="Enter idea title..."
                                            required
                                            autoFocus
                                        />
                                    </div>

                                    <div className="feedlane-form__field">
                                        <label htmlFor="idea-content">Description *</label>
                                        <textarea
                                            id="idea-content"
                                            name="content"
                                            value={formData.content}
                                            onChange={handleInputChange}
                                            className="feedlane-textarea"
                                            rows="6"
                                            placeholder="Describe your idea in detail..."
                                            required
                                        />
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="feedlane-form__field">
                                            <label htmlFor="idea-status">Status</label>
                                            <select
                                                id="idea-status"
                                                name="status"
                                                value={formData.status}
                                                onChange={handleInputChange}
                                                className="feedlane-select"
                                            >
                                                {statuses.map((status) => (
                                                    <option key={status.slug} value={status.slug}>
                                                        {status.name}
                                                    </option>
                                                ))}
                                            </select>
                                        </div>

                                        <div className="feedlane-form__field">
                                            <label htmlFor="idea-visibility">Visibility</label>
                                            <select
                                                id="idea-visibility"
                                                name="visibility"
                                                value={formData.visibility}
                                                onChange={handleInputChange}
                                                className="feedlane-select"
                                            >
                                                <option value="public">Public</option>
                                                <option value="private">Private</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div className="feedlane-form__field">
                                        <label>Categories</label>
                                        <div className="grid grid-cols-2 gap-2 mt-2">
                                            {categories.map((category) => (
                                                <label key={category.slug} className="flex items-center gap-2">
                                                    <input
                                                        type="checkbox"
                                                        name="categories"
                                                        value={category.slug}
                                                        checked={formData.categories.includes(category.slug)}
                                                        onChange={handleInputChange}
                                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                    />
                                                    <span className="text-sm">{category.name}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                </form>
                            </div>

                            {/* Comments Section */}
                            <div className="lg:col-span-1">
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                                        <FiMessageSquare className="w-4 h-4" />
                                        Comments ({comments.length})
                                    </h4>

                                    {/* Comments List */}
                                    <div className="space-y-3 mb-4 max-h-64 overflow-y-auto">
                                        {loadingComments ? (
                                            <div className="text-center py-4">
                                                <div className="feedlane-loading__spinner w-4 h-4 mx-auto"></div>
                                            </div>
                                        ) : comments.length > 0 ? (
                                            comments.map((comment) => (
                                                <div key={comment.id} className="bg-white rounded-md p-3 text-sm">
                                                    <div className="flex items-center gap-2 mb-2">
                                                        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
                                                            {getAuthorInitials(comment.author_name)}
                                                        </div>
                                                        <span className="font-medium text-gray-900">{comment.author_name}</span>
                                                        <span className="text-gray-500 text-xs">{formatCommentDate(comment.date)}</span>
                                                    </div>
                                                    <div 
                                                        className="text-gray-700"
                                                        dangerouslySetInnerHTML={{ __html: comment.content.rendered }}
                                                    />
                                                </div>
                                            ))
                                        ) : (
                                            <p className="text-gray-500 text-sm text-center py-4">No comments yet</p>
                                        )}
                                    </div>

                                    {/* Add Comment Form */}
                                    {idea && (
                                        <form onSubmit={handleAddComment} className="space-y-2">
                                            <textarea
                                                value={newComment}
                                                onChange={(e) => setNewComment(e.target.value)}
                                                placeholder="Add a comment..."
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                rows="3"
                                            />
                                            <button
                                                type="submit"
                                                className="w-full bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700 flex items-center justify-center gap-2"
                                            >
                                                <FiSend className="w-4 h-4" />
                                                Add Comment
                                            </button>
                                        </form>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div className="feedlane-modal__footer">
                        <button
                            type="button"
                            onClick={onClose}
                            className="feedlane-btn feedlane-btn--secondary"
                            disabled={loading}
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={handleSubmit}
                            className="feedlane-btn feedlane-btn--primary"
                            disabled={loading}
                        >
                            {loading ? (
                                <>
                                    <div className="feedlane-spinner feedlane-spinner--small mr-2"></div>
                                    {idea ? 'Updating...' : 'Creating...'}
                                </>
                            ) : (
                                <>
                                    <FiSave className="w-4 h-4 mr-2" />
                                    {idea ? 'Update Idea' : 'Create Idea'}
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IdeaModal;
