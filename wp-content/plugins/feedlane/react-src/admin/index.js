import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { Toaster } from 'react-hot-toast';
import Dashboard from './components/Dashboard';
import Settings from './components/Settings';
import Analytics from './components/Analytics';
import IdeaManager from './components/IdeaManager';
import CategoriesManagement from './components/CategoriesManagement';
import StatusManagement from './components/StatusManagement';
import RoadmapManager from './components/RoadmapManager';
import './scss/admin.scss';

// Smart Navigation Component
const SmartNavigation = () => {
    const [currentPage, setCurrentPage] = useState('dashboard');

    useEffect(() => {
        // Get initial page from URL or container
        const getInitialPage = () => {
            const containers = [
                { id: 'feedlane-admin-dashboard', page: 'dashboard' },
                { id: 'feedlane-admin-settings', page: 'settings' },
                { id: 'feedlane-admin-analytics', page: 'analytics' },
                { id: 'feedlane-admin-ideas', page: 'ideas' },
                { id: 'feedlane-admin-categories', page: 'categories' },
                { id: 'feedlane-admin-roadmap', page: 'roadmap' },
                { id: 'feedlane-admin-status', page: 'status' }
            ];

            for (const container of containers) {
                if (document.getElementById(container.id)) {
                    return container.page;
                }
            }
            return 'dashboard';
        };

        setCurrentPage(getInitialPage());

        // Listen for navigation events
        const handleNavigation = (event) => {
            if (event.detail && event.detail.page) {
                setCurrentPage(event.detail.page);
            }
        };

        window.addEventListener('feedlane-navigate', handleNavigation);
        return () => window.removeEventListener('feedlane-navigate', handleNavigation);
    }, []);

    const renderPage = () => {
        switch (currentPage) {
            case 'dashboard':
                return <Dashboard />;
            case 'settings':
                return <Settings />;
            case 'analytics':
                return <Analytics />;
            case 'ideas':
                return <IdeaManager />;
            case 'categories':
                return <CategoriesManagement />;
            case 'roadmap':
                return <RoadmapManager />;
            case 'status':
                return <StatusManagement />;
            default:
                return <Dashboard />;
        }
    };

    return (
        <>
            {renderPage()}
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        iconTheme: {
                            primary: '#4ade80',
                            secondary: '#fff',
                        },
                    },
                    error: {
                        duration: 5000,
                        iconTheme: {
                            primary: '#ef4444',
                            secondary: '#fff',
                        },
                    },
                }}
            />
        </>
    );
};

// Legacy App component for backward compatibility
const App = ({ page }) => {
    const renderPage = () => {
        switch (page) {
            case 'dashboard':
                return <Dashboard />;
            case 'settings':
                return <Settings />;
            case 'analytics':
                return <Analytics />;
            case 'ideas':
                return <IdeaManager />;
            case 'categories':
                return <CategoriesManagement />;
            case 'roadmap':
                return <RoadmapManager />;
            case 'status':
                return <StatusManagement />;
            default:
                return <Dashboard />;
        }
    };

    return (
        <>
            {renderPage()}
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        iconTheme: {
                            primary: '#4ade80',
                            secondary: '#fff',
                        },
                    },
                    error: {
                        duration: 5000,
                        iconTheme: {
                            primary: '#ef4444',
                            secondary: '#fff',
                        },
                    },
                }}
            />
        </>
    );
};

// Navigation helper function
window.feedlaneNavigate = (page) => {
    // Dispatch custom event for smart navigation
    window.dispatchEvent(new CustomEvent('feedlane-navigate', {
        detail: { page }
    }));

    // Update URL without page reload for React pages only
    const reactPages = ['dashboard', 'settings', 'analytics', 'ideas', 'categories', 'roadmap', 'status'];
    if (reactPages.includes(page)) {
        const url = new URL(window.location);
        url.searchParams.set('page', `feedlane-${page}`);
        window.history.pushState({}, '', url);
    }
};

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    // Debug: Check if React and ReactDOM are available
    console.log('React available:', typeof React !== 'undefined');
    console.log('createRoot available:', typeof createRoot !== 'undefined');

    // Check if any React admin container exists
    const containers = [
        'feedlane-admin-dashboard',
        'feedlane-admin-settings',
        'feedlane-admin-analytics',
        'feedlane-admin-ideas',
        'feedlane-admin-categories',
        'feedlane-admin-roadmap',
        'feedlane-admin-status'
    ];

    let reactContainer = null;
    for (const containerId of containers) {
        const container = document.getElementById(containerId);
        if (container) {
            reactContainer = container;
            break;
        }
    }

    // If we found a React container, use SmartNavigation
    if (reactContainer) {
        try {
            const root = createRoot(reactContainer);
            root.render(<SmartNavigation />);

            // Intercept admin menu clicks for React pages
            setTimeout(() => {
                const adminMenuLinks = document.querySelectorAll('#adminmenu a[href*="feedlane"]');
                adminMenuLinks.forEach(link => {
                    const href = link.getAttribute('href');
                    if (href && href.includes('page=feedlane-')) {
                        const page = href.split('page=feedlane-')[1].split('&')[0];
                        const reactPages = ['dashboard', 'settings', 'analytics', 'ideas', 'categories', 'roadmap', 'status'];

                        if (reactPages.includes(page)) {
                            link.addEventListener('click', (e) => {
                                e.preventDefault();
                                window.feedlaneNavigate(page);

                                // Update active menu item
                                document.querySelectorAll('#adminmenu .current').forEach(el => el.classList.remove('current'));
                                link.parentElement.classList.add('current');
                            });
                        }
                    }
                });
            }, 100);

        } catch (error) {
            console.error('Error creating smart navigation root:', error);
        }
    }
});
