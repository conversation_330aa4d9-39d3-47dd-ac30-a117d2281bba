import React from 'react';
import { createRoot } from 'react-dom/client';
import { Toaster } from 'react-hot-toast';
import Dashboard from './components/Dashboard';
import Settings from './components/Settings';
import Analytics from './components/Analytics';
import IdeasManagement from './components/IdeasManagement';
import CategoriesManagement from './components/CategoriesManagement';
import StatusManagement from './components/StatusManagement';
import RoadmapManager from './components/RoadmapManager';
import './scss/admin.scss';

// Main App component
const App = ({ page }) => {
    const renderPage = () => {
        switch (page) {
            case 'dashboard':
                return <Dashboard />;
            case 'settings':
                return <Settings />;
            case 'analytics':
                return <Analytics />;
            case 'ideas':
                return <IdeasManagement />;
            case 'categories':
                return <CategoriesManagement />;
            case 'roadmap':
                return <RoadmapManager />;
            case 'status':
                return <StatusManagement />;
            default:
                return <Dashboard />;
        }
    };

    return (
        <>
            {renderPage()}
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        iconTheme: {
                            primary: '#4ade80',
                            secondary: '#fff',
                        },
                    },
                    error: {
                        duration: 5000,
                        iconTheme: {
                            primary: '#ef4444',
                            secondary: '#fff',
                        },
                    },
                }}
            />
        </>
    );
};

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    // Debug: Check if React and ReactDOM are available
    console.log('React available:', typeof React !== 'undefined');
    console.log('createRoot available:', typeof createRoot !== 'undefined');
    console.log('wp.element available:', typeof wp !== 'undefined' && typeof wp.element !== 'undefined');

    // Dashboard
    const dashboardContainer = document.getElementById('feedlane-admin-dashboard');
    if (dashboardContainer) {
        try {
            const root = createRoot(dashboardContainer);
            root.render(<App page="dashboard" />);
        } catch (error) {
            console.error('Error creating dashboard root:', error);
        }
    }

    // Settings
    const settingsContainer = document.getElementById('feedlane-admin-settings');
    if (settingsContainer) {
        const root = createRoot(settingsContainer);
        root.render(<App page="settings" />);
    }

    // Analytics
    const analyticsContainer = document.getElementById('feedlane-admin-analytics');
    if (analyticsContainer) {
        const root = createRoot(analyticsContainer);
        root.render(<App page="analytics" />);
    }

    // Ideas Management
    const ideasContainer = document.getElementById('feedlane-admin-ideas');
    if (ideasContainer) {
        const root = createRoot(ideasContainer);
        root.render(<App page="ideas" />);
    }

    // Categories Management
    const categoriesContainer = document.getElementById('feedlane-admin-categories');
    if (categoriesContainer) {
        const root = createRoot(categoriesContainer);
        root.render(<App page="categories" />);
    }

    // Roadmap Manager
    const roadmapContainer = document.getElementById('feedlane-admin-roadmap');
    if (roadmapContainer) {
        const root = createRoot(roadmapContainer);
        root.render(<App page="roadmap" />);
    }

    // Status Management
    const statusContainer = document.getElementById('feedlane-admin-status');
    if (statusContainer) {
        const root = createRoot(statusContainer);
        root.render(<App page="status" />);
    }
});
