// Only import Tailwind components and utilities, not the base reset
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// Scope all our styles to prevent conflicts with WordPress admin
.feedlane-admin-wrapper {
  // Apply Tailwind base styles only within our components
  *, ::before, ::after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb;
  }

  ::before, ::after {
    --tw-content: '';
  }

  // Ensure modals appear above WordPress admin elements
  .feedlane-modal {
    z-index: 999999;
  }
}

// Admin Panel Styles
.feedlane-admin {
  @apply max-w-7xl mx-auto p-6;

  &__header {
    @apply mb-8;

    h1 {
      @apply text-3xl font-bold text-gray-900 mb-2;
    }

    p {
      @apply text-lg text-gray-600;
    }
  }

  &__content {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
}

// Loading States
.feedlane-loading {
  @apply flex flex-col items-center justify-center py-12 text-gray-500;

  &__spinner {
    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4;
  }
}

// Form Styles
.feedlane-form {
  @apply space-y-8;

  &__section {
    @apply border-b border-gray-200 pb-8 last:border-b-0 last:pb-0;

    h3 {
      @apply text-xl font-semibold text-gray-900 mb-2;
    }

    > p {
      @apply text-gray-600 mb-6;
    }
  }

  &__field {
    @apply space-y-2;

    label {
      @apply block text-sm font-medium text-gray-700;
    }

    .description {
      @apply text-sm text-gray-500;
    }
  }

  &__actions {
    @apply flex justify-end pt-6 border-t border-gray-200;
  }
}

// Enhanced Form Controls
.feedlane-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;

  &:disabled {
    @apply bg-gray-50 text-gray-500 cursor-not-allowed;
  }
}

.feedlane-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;

  &:disabled {
    @apply bg-gray-50 text-gray-500 cursor-not-allowed;
  }
}

.feedlane-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;

  &:disabled {
    @apply bg-gray-50 text-gray-500 cursor-not-allowed;
  }
}

// Enhanced Color Picker
.feedlane-color-picker {
  @apply flex items-center gap-3;

  .feedlane-color-input {
    @apply w-12 h-10 border border-gray-300 rounded-lg cursor-pointer;

    &::-webkit-color-swatch-wrapper {
      @apply p-0;
    }

    &::-webkit-color-swatch {
      @apply border-0 rounded-md;
    }
  }

  .feedlane-color-text {
    @apply flex-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
  }
}

// Enhanced Buttons
.feedlane-btn {
  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;

  &--primary {
    @apply bg-blue-600 text-white shadow-sm hover:bg-blue-700 focus:ring-blue-500;
  }

  &--secondary {
    @apply bg-white text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50 focus:ring-gray-500;
  }

  &--danger {
    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500;
  }

  &--small {
    @apply px-3 py-1.5 text-xs;
  }

  &--large {
    @apply px-6 py-3 text-base;
  }
}

// Spinner
.feedlane-spinner {
  @apply w-4 h-4 border-2 border-gray-200 border-t-current rounded-full animate-spin;

  &--small {
    @apply w-3 h-3 border;
  }
}

// Tables
.feedlane-table {
  @apply w-full border-collapse bg-white shadow-sm rounded-lg overflow-hidden;

  thead {
    @apply bg-gray-50;

    th {
      @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
    }
  }

  tbody {
    @apply divide-y divide-gray-200;

    td {
      @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }
  }

  &__actions {
    @apply flex items-center gap-2;
  }
}

// Cards
.feedlane-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;

  &__header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;

    h3 {
      @apply text-lg font-medium text-gray-900;
    }
  }

  &__content {
    @apply p-6;
  }

  &__footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
}

// Badges
.feedlane-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;

  &--success {
    @apply bg-green-100 text-green-800;
  }

  &--warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  &--danger {
    @apply bg-red-100 text-red-800;
  }

  &--info {
    @apply bg-blue-100 text-blue-800;
  }

  &--gray {
    @apply bg-gray-100 text-gray-800;
  }
}

// Alerts
.feedlane-alert {
  @apply p-4 rounded-lg border;

  &--success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  &--warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  &--danger {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  &--info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }
}

// Modal
.feedlane-modal {
  @apply fixed inset-0 z-50 overflow-y-auto;

  &__backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity;
  }

  &__container {
    @apply flex min-h-full items-center justify-center p-4;
  }

  &__content {
    @apply bg-white rounded-lg shadow-xl max-w-lg w-full;
  }

  &__header {
    @apply px-6 py-4 border-b border-gray-200;

    h3 {
      @apply text-lg font-medium text-gray-900;
    }
  }

  &__body {
    @apply p-6;
  }

  &__footer {
    @apply px-6 py-4 border-t border-gray-200 flex justify-end gap-3;
  }
}

// Pagination
.feedlane-pagination {
  @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;

  &__info {
    @apply text-sm text-gray-700;
  }

  &__nav {
    @apply flex gap-2;
  }

  &__btn {
    @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;

    &--active {
      @apply bg-blue-600 text-white border-blue-600;
    }
  }
}
