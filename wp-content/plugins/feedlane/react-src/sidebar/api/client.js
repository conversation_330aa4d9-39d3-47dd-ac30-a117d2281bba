// API client utility
const API_BASE = window.feedlaneData?.rest_url || '/wp-json/';
const NONCE = window.feedlaneData?.nonce || '';

class ApiClient {
    constructor() {
        this.baseURL = API_BASE;
        this.nonce = NONCE;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}feedlane/v1${endpoint}`;

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': this.nonce,
            },
        };

        const config = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {

            throw error;
        }
    }

    async get(endpoint, params = {}) {
        const searchParams = new URLSearchParams(params);
        const queryString = searchParams.toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;

        return this.request(url, {
            method: 'GET',
        });
    }

    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE',
        });
    }

    async postFormData(endpoint, formData) {
        return this.request(endpoint, {
            method: 'POST',
            headers: {
                'X-WP-Nonce': this.nonce,
                // Don't set Content-Type for FormData, let browser set it
            },
            body: formData,
        });
    }
}

export const apiClient = new ApiClient();
export default apiClient;
