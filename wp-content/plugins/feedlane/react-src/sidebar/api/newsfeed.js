import apiClient from './client';

/**
 * Fetch newsfeed posts
 */
export const fetchNewsfeedPosts = async (params = {}) => {
    const defaultParams = {
        page: 1,
        per_page: 10,
    };

    const queryParams = { ...defaultParams, ...params };

    // Use WordPress REST API for posts
    const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/feedlane_posts?${new URLSearchParams(queryParams)}`, {
        headers: {
            'X-WP-Nonce': window.feedlaneData.nonce,
        },
    });

    if (!response.ok) {
        throw new Error('Failed to fetch newsfeed posts');
    }

    const posts = await response.json();

    // Transform the data to include additional fields
    return posts.map(post => ({
        id: post.id,
        title: post.title.rendered,
        content: post.content.rendered,
        excerpt: post.excerpt.rendered,
        date: post.date,
        featured_image: post.featured_media ? getMediaUrl(post.featured_media) : null,
        author: post.author,
        link: post.link,
    }));
};

/**
 * Get media URL by ID
 */
const getMediaUrl = async (mediaId) => {
    try {
        const response = await fetch(`${window.feedlaneData.rest_url}wp/v2/media/${mediaId}`, {
            headers: {
                'X-WP-Nonce': window.feedlaneData.nonce,
            },
        });

        if (response.ok) {
            const media = await response.json();
            return media.source_url;
        }
    } catch (error) {

    }

    return null;
};
