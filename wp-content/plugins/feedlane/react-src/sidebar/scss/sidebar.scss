@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

// CSS Variables
:root {
  --feedlane-primary-color: #0ea5e9;
  --feedlane-primary-hover: #0284c7;
  --feedlane-text-primary: #1f2937;
  --feedlane-text-secondary: #6b7280;
  --feedlane-bg-primary: #ffffff;
  --feedlane-bg-secondary: #f9fafb;
  --feedlane-border: #e5e7eb;
  --feedlane-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --feedlane-z-index: 9999;
}

// Floating Button - Beamer Style
.feedlane-floating-button {
  @apply fixed z-[9999] transition-all duration-300 hover:scale-105 focus:outline-none cursor-pointer;
  background-color: #000;
  border-radius: 20px 20px 20px 4px;
  padding: 12px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  top: 50%;
  transform: translateY(-50%);

  &--left {
    @apply left-6;
    transform: translateY(-50%);
  }

  &--right {
    @apply right-6;
    transform: translateY(-50%);
  }

  .feedlane-floating-text {
    @apply text-white text-sm font-medium;
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

// Overlay
.feedlane-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-[9998] transition-opacity duration-300;
}

// Sidebar
.feedlane-sidebar {
  @apply fixed top-0 h-full w-96 bg-white shadow-2xl z-[9999] transform transition-transform duration-300 ease-in-out;

  &--left {
    @apply left-0 -translate-x-full;

    &.feedlane-sidebar--open {
      @apply translate-x-0;
    }
  }

  &--right {
    @apply right-0 translate-x-full;

    &.feedlane-sidebar--open {
      @apply translate-x-0;
    }
  }

  // Header
  &__header {
    @apply flex items-center justify-between p-6 border-b border-gray-200;
  }

  &__title {
    @apply text-xl font-bold text-gray-900 m-0;
  }

  &__close {
    @apply p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300;
  }

  // Tabs
  &__tabs {
    @apply flex border-b border-gray-200;
  }

  &__tab {
    @apply flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 transition-colors duration-200 border-b-2 border-transparent;

    &--active {
      @apply text-gray-900 border-b-2;
      border-bottom-color: var(--feedlane-primary-color);
    }
  }

  &__tab-icon {
    @apply text-lg;
  }

  &__tab-label {
    @apply hidden sm:block;
  }

  // Content
  &__content {
    @apply flex-1 overflow-y-auto p-6;
    max-height: calc(100vh - 140px);
  }
}

// Loading States
.feedlane-loading {
  @apply flex flex-col items-center justify-center py-12 text-gray-500;

  &__spinner {
    @apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin mb-4;
  }
}

// Error States
.feedlane-error {
  @apply text-center py-12;

  p {
    @apply text-gray-500 mb-4;
  }
}

// Empty States
.feedlane-empty {
  @apply text-center py-12;

  &__icon {
    @apply text-4xl mb-4;
  }

  h3 {
    @apply text-lg font-semibold text-gray-900 mb-2;
  }

  p {
    @apply text-gray-500;
  }
}

// Buttons
.feedlane-button {
  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;

  &--primary {
    @apply text-white shadow-sm hover:shadow-md;
    background-color: var(--feedlane-primary-color);

    &:hover:not(:disabled) {
      background-color: var(--feedlane-primary-hover);
    }

    &:focus {
      --tw-ring-color: var(--feedlane-primary-color);
    }
  }

  &--secondary {
    @apply text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
  }

  &--small {
    @apply px-3 py-1.5 text-xs;
  }
}

// Form Elements
.feedlane-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-transparent;

  &:focus {
    --tw-ring-color: var(--feedlane-primary-color);
  }

  &--small {
    @apply px-2 py-1.5 text-sm;
  }
}

.feedlane-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-offset-2 focus:border-transparent;

  &:focus {
    --tw-ring-color: var(--feedlane-primary-color);
  }
}

.feedlane-form-row {
  @apply flex gap-2;
}

// Spinner
.feedlane-spinner {
  @apply w-4 h-4 border-2 border-gray-200 border-t-current rounded-full animate-spin;

  &--small {
    @apply w-3 h-3 border;
  }
}

// Newsfeed Styles
.feedlane-newsfeed {
  &__header {
    @apply mb-6;

    h3 {
      @apply text-lg font-semibold text-gray-900 mb-1;
    }

    p {
      @apply text-sm text-gray-500;
    }
  }

  &__posts {
    @apply space-y-6;
  }
}

.feedlane-post {
  @apply border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200;

  &__header {
    @apply mb-3;
  }

  &__title {
    @apply text-base font-semibold text-gray-900 mb-2 leading-tight;
  }

  &__meta {
    @apply flex items-center gap-2 text-xs text-gray-500;
  }

  &__date {
    @apply flex items-center gap-1;
  }

  &__image {
    @apply mb-3 rounded-lg overflow-hidden;

    img {
      @apply w-full h-auto;
    }
  }

  &__content {
    @apply mb-4;
  }

  &__excerpt {
    @apply text-sm text-gray-700 leading-relaxed;

    p {
      @apply mb-2 last:mb-0;
    }
  }

  &__read-more {
    @apply text-xs font-medium mt-2 hover:underline;
    color: var(--feedlane-primary-color);
  }

  &__footer {
    @apply space-y-3;
  }
}

// Reaction Styles
.feedlane-reactions {
  &__buttons {
    @apply flex gap-2 mb-2;
  }

  &__summary {
    @apply flex gap-3 text-xs text-gray-500;
  }

  &__summary-item {
    @apply flex items-center gap-1;
  }

  &--loading {
    .feedlane-reactions__skeleton {
      @apply flex gap-2;
    }
  }
}

.feedlane-reaction-button {
  @apply flex items-center gap-1 px-2 py-1 rounded-full border border-gray-200 hover:border-gray-300 transition-colors duration-200 text-sm;

  &--active {
    @apply border-blue-300 bg-blue-50;
  }

  &--skeleton {
    @apply animate-pulse;

    .feedlane-reaction-button__emoji {
      @apply w-4 h-4 bg-gray-200 rounded;
    }

    .feedlane-reaction-button__count {
      @apply w-3 h-3 bg-gray-200 rounded;
    }
  }

  &__emoji {
    @apply text-base leading-none;
  }

  &__count {
    @apply text-xs font-medium text-gray-600;
  }
}

// Feedback Styles
.feedlane-feedback {
  &__toggle {
    @apply flex items-center justify-between mb-3;
  }

  &__toggle-button {
    @apply flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors duration-200;
  }

  &__view-button {
    @apply flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 transition-colors duration-200;
  }

  &__form {
    @apply space-y-3 p-3 bg-gray-50 rounded-lg;
  }

  &__guest-fields {
    @apply space-y-2;
  }

  &__actions {
    @apply flex justify-end gap-2;
  }

  &__list {
    @apply mt-3 space-y-3;
  }

  &__loading {
    @apply flex items-center justify-center gap-2 py-4 text-sm text-gray-500;
  }

  &__items {
    @apply space-y-3;
  }

  &__item {
    @apply p-3 bg-gray-50 rounded-lg;
  }

  &__item-header {
    @apply flex items-center justify-between mb-2;
  }

  &__author {
    @apply text-sm font-medium text-gray-900;
  }

  &__time {
    @apply text-xs text-gray-500;
  }

  &__item-content {
    @apply text-sm text-gray-700;
  }

  &__empty {
    @apply text-center py-4;

    p {
      @apply text-sm text-gray-500;
    }
  }
}

// Responsive Design
@media (max-width: 640px) {
  .feedlane-sidebar {
    @apply w-full;
  }

  .feedlane-floating-button {
    &--left {
      @apply left-4;
    }

    &--right {
      @apply right-4;
    }
  }
}

// Beamer-Style Sidebar Updates
.feedlane-sidebar {
  &__header {
    @apply p-0 border-b-0;
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);

    .feedlane-sidebar__header-content {
      @apply flex items-center justify-between p-4;
    }

    .feedlane-sidebar__title {
      @apply text-white text-lg font-semibold;
    }

    .feedlane-sidebar__header-actions {
      @apply flex items-center gap-2;
    }

    .feedlane-sidebar__search-btn,
    .feedlane-sidebar__close {
      @apply text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-lg transition-colors;
    }
  }

  &__content {
    @apply flex-1 overflow-y-auto p-0;
  }

  &__bottom-nav {
    @apply flex border-t border-gray-200 bg-white;

    .feedlane-sidebar__nav-item {
      @apply flex-1 flex flex-col items-center justify-center py-3 px-2 text-xs font-medium text-gray-500 hover:text-gray-700 transition-colors;

      &--active {
        @apply text-blue-600;
      }

      .feedlane-sidebar__nav-label {
        @apply mt-1;
      }
    }
  }
}

// Subscribe Banner
.feedlane-subscribe-banner {
  @apply flex items-center gap-3 p-4 bg-gray-100 border-b border-gray-200;

  &__icon {
    @apply text-gray-600;
  }

  &__text {
    @apply text-sm font-medium text-gray-700;
  }
}

// Beamer Post Styles
.feedlane-beamer-post {
  @apply border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors;

  &__header {
    @apply flex items-start justify-between mb-3;
  }

  &__badges {
    @apply flex items-center gap-2 flex-1;
  }

  &__badge {
    @apply px-2 py-1 text-xs font-bold rounded;
  }

  &__date {
    @apply text-xs text-gray-500;
  }

  &__share {
    @apply text-gray-400 hover:text-gray-600 p-1 rounded transition-colors;
  }

  &__title {
    @apply text-base font-semibold text-gray-900 mb-3 leading-tight;
  }

  &__content {
    @apply space-y-3;
  }

  &__availability {
    @apply text-sm text-gray-600;
  }

  &__text {
    @apply text-sm text-gray-700 leading-relaxed;

    &.expanded {
      @apply block;
    }
  }

  &__read-more {
    @apply text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors;
  }

  &__video {
    @apply mt-4;
  }

  &__video-thumbnail {
    @apply relative bg-gray-100 rounded-lg overflow-hidden;

    img {
      @apply w-full h-auto;
    }
  }

  &__play-btn {
    @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 text-white hover:bg-opacity-40 transition-colors;
  }

  &__video-info {
    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 text-white;
  }

  &__video-title {
    @apply text-sm font-medium;
  }

  &__video-meta {
    @apply flex items-center gap-2 mt-1 text-xs;
  }

  &__image {
    @apply mt-4 rounded-lg overflow-hidden;

    img {
      @apply w-full h-auto;
    }
  }
}

// Idea Form Styles
.feedlane-idea-form {
  @apply p-4;

  &__header {
    @apply relative mb-6;

    h3 {
      @apply text-lg font-semibold text-gray-900 mb-2;
    }

    p {
      @apply text-sm text-gray-600;
    }
  }

  &__close {
    @apply absolute top-0 right-0 text-gray-400 hover:text-gray-600 p-1 rounded transition-colors;
  }

  &__form {
    @apply space-y-4;
  }
}

.feedlane-form-group {
  @apply space-y-1;

  label {
    @apply block text-xs font-bold text-gray-700 uppercase tracking-wide;
  }

  input,
  textarea,
  select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  textarea {
    @apply resize-none;
  }
}

.feedlane-form-actions {
  @apply flex items-center justify-between pt-4;
}

.feedlane-form-attach {
  @apply p-2 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.feedlane-form-submit {
  @apply px-6 py-2 bg-blue-600 text-white text-sm font-bold rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50;
}

.feedlane-form-footer {
  @apply text-center pt-4 border-t border-gray-200;

  span {
    @apply text-xs text-gray-500;
  }
}

// Suggest Button
.feedlane-suggest-button {
  @apply p-4 border-t border-gray-200;
}

.feedlane-suggest-btn {
  @apply w-full py-3 bg-blue-600 text-white text-sm font-bold rounded-lg hover:bg-blue-700 transition-colors;
}

// Beamer Idea Styles
.feedlane-beamer-idea {
  @apply border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors;

  &__header {
    @apply flex gap-4;
  }

  &__vote {
    @apply flex flex-col items-center;
  }

  &__vote-btn {
    @apply p-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-800 transition-colors;

    &.voted {
      @apply bg-blue-100 border-blue-300 text-blue-600;
    }
  }

  &__vote-count {
    @apply text-sm font-medium text-gray-700 mt-1;
  }

  &__content {
    @apply flex-1;
  }

  &__meta {
    @apply flex items-center gap-4 mb-2;
  }

  &__category {
    @apply text-xs font-bold;
  }

  &__comments {
    @apply flex items-center gap-1 text-xs text-gray-500;
  }

  &__title {
    @apply text-base font-semibold text-gray-900 mb-2;
  }

  &__status {
    @apply flex items-center gap-2 mb-3;
  }

  &__status-indicator {
    @apply w-2 h-2 rounded-full;
  }

  &__status-text {
    @apply text-xs font-medium text-gray-600;
  }

  &__details {
    @apply mb-3;

    ul {
      @apply space-y-1;
    }

    li {
      @apply text-sm text-gray-700;

      &:before {
        content: "• ";
        @apply text-gray-400;
      }
    }
  }
}

// Roadmap Styles
.feedlane-roadmap {
  &__tabs {
    @apply flex border-b border-gray-200 bg-white;
  }

  &__tab {
    @apply flex-1 flex items-center justify-center gap-2 py-3 px-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent transition-colors;

    &.active {
      @apply border-blue-600 text-blue-600;
    }
  }

  &__tab-indicator {
    @apply text-xs;
  }

  &__content {
    @apply flex-1 overflow-y-auto;
  }
}

.feedlane-roadmap-items {
  @apply divide-y divide-gray-200;
}

.feedlane-beamer-roadmap-item {
  @apply p-4 hover:bg-gray-50 transition-colors;

  &__header {
    @apply flex gap-4;
  }

  &__vote {
    @apply flex flex-col items-center;
  }

  &__vote-btn {
    @apply p-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-800 transition-colors;
  }

  &__vote-count {
    @apply text-sm font-medium text-gray-700 mt-1;
  }

  &__content {
    @apply flex-1;
  }

  &__title {
    @apply text-base font-semibold text-gray-900 mb-2;
  }

  &__category {
    @apply text-xs font-bold mb-3;
  }

  &__details {
    @apply mb-3;

    ul {
      @apply space-y-1;
    }

    li {
      @apply text-sm text-gray-700;

      &:before {
        content: "• ";
        @apply text-gray-400;
      }
    }
  }

  &__comments {
    @apply flex items-center gap-1 text-sm text-blue-600 hover:text-blue-700 transition-colors;
  }
}