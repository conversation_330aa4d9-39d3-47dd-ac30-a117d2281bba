import React, { useState, useEffect } from 'react';
import { FiX, FiSearch } from 'react-icons/fi';
import { HiOutlineViewGrid, HiOutlineLightBulb, HiOutlineMap } from 'react-icons/hi';
import NewsfeedTab from './NewsfeedTab';
import IdeasTab from './IdeasTab';
import RoadmapTab from './RoadmapTab';

const Sidebar = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('newsfeed');
    const [settings, setSettings] = useState({});

    useEffect(() => {
        // Get settings from localized data
        if (window.feedlaneData && window.feedlaneData.settings) {
            setSettings(window.feedlaneData.settings);

            // Set default active tab based on enabled features
            const { enable_newsfeed, enable_ideas, enable_roadmap } = window.feedlaneData.settings;

            if (enable_newsfeed) {
                setActiveTab('newsfeed');
            } else if (enable_ideas) {
                setActiveTab('ideas');
            } else if (enable_roadmap) {
                setActiveTab('roadmap');
            }
        }
    }, []);

    const toggleSidebar = () => {
        setIsOpen(!isOpen);
    };

    const closeSidebar = () => {
        setIsOpen(false);
    };

    const getAvailableTabs = () => {
        const tabs = [];

        if (settings.enable_newsfeed) {
            tabs.push({
                id: 'newsfeed',
                label: 'Newsfeed',
                icon: HiOutlineViewGrid,
                component: NewsfeedTab
            });
        }

        if (settings.enable_ideas) {
            tabs.push({
                id: 'ideas',
                label: 'Ideas',
                icon: HiOutlineLightBulb,
                component: IdeasTab
            });
        }

        if (settings.enable_roadmap) {
            tabs.push({
                id: 'roadmap',
                label: 'Roadmap',
                icon: HiOutlineMap,
                component: RoadmapTab
            });
        }

        return tabs;
    };

    const availableTabs = getAvailableTabs();

    // Don't render if no tabs are enabled
    if (availableTabs.length === 0) {
        return null;
    }

    const position = settings.sidebar_position || 'right';
    const primaryColor = settings.primary_color || '#4F46E5';

    const ActiveTabComponent = availableTabs.find(tab => tab.id === activeTab)?.component || NewsfeedTab;

    const getTabTitle = () => {
        switch(activeTab) {
            case 'newsfeed':
                return "What's new on " + (window.feedlaneData?.siteName || 'Our Site');
            case 'ideas':
                return (window.feedlaneData?.siteName || 'Our Site') + " Ideas";
            case 'roadmap':
                return (window.feedlaneData?.siteName || 'Our Site') + " Roadmap";
            default:
                return "Feedback";
        }
    };

    return (
        <>
            {/* Floating Button - Beamer Style */}
            <div
                className={`feedlane-floating-button feedlane-floating-button--${position}`}
                onClick={toggleSidebar}
                style={{ backgroundColor: '#000' }}
            >
                <span className="feedlane-floating-text">Feedback</span>
            </div>

            {/* Overlay */}
            {isOpen && (
                <div
                    className="feedlane-overlay"
                    onClick={closeSidebar}
                />
            )}

            {/* Sidebar - Beamer Style */}
            <div
                className={`feedlane-sidebar feedlane-sidebar--${position} ${isOpen ? 'feedlane-sidebar--open' : ''}`}
                style={{ '--feedlane-primary-color': primaryColor }}
            >
                {/* Header with Search */}
                <div className="feedlane-sidebar__header" style={{ backgroundColor: primaryColor }}>
                    <div className="feedlane-sidebar__header-content">
                        <h2 className="feedlane-sidebar__title">{getTabTitle()}</h2>
                        <div className="feedlane-sidebar__header-actions">
                            <button className="feedlane-sidebar__search-btn">
                                <FiSearch size={20} />
                            </button>
                            <button
                                onClick={closeSidebar}
                                className="feedlane-sidebar__close"
                                aria-label="Close sidebar"
                            >
                                <FiX size={20} />
                            </button>
                        </div>
                    </div>
                </div>

                {/* Content */}
                <div className="feedlane-sidebar__content">
                    <ActiveTabComponent />
                </div>

                {/* Bottom Navigation - Beamer Style */}
                {availableTabs.length > 1 && (
                    <div className="feedlane-sidebar__bottom-nav">
                        {availableTabs.map(tab => {
                            const IconComponent = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id)}
                                    className={`feedlane-sidebar__nav-item ${activeTab === tab.id ? 'feedlane-sidebar__nav-item--active' : ''}`}
                                    style={activeTab === tab.id ? { color: primaryColor } : {}}
                                >
                                    <IconComponent size={24} />
                                    <span className="feedlane-sidebar__nav-label">{tab.label}</span>
                                </button>
                            );
                        })}
                    </div>
                )}
            </div>
        </>
    );
};

export default Sidebar;
