import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FiChevronUp, FiMessageSquare } from 'react-icons/fi';
import { fetchIdeas, voteIdea } from '../api/ideas';
import IdeaForm from './IdeaForm';

const IdeasTab = () => {
    const [showForm, setShowForm] = useState(false);
    const queryClient = useQueryClient();

    const {
        data: ideasData,
        isLoading,
        error,
        refetch
    } = useQuery({
        queryKey: ['ideas'],
        queryFn: () => fetchIdeas({ per_page: 20 }),
        staleTime: 3 * 60 * 1000, // 3 minutes
    });

    const handleFormSuccess = () => {
        setShowForm(false);
        queryClient.invalidateQueries(['ideas']);
    };

    const handleFormCancel = () => {
        setShowForm(false);
    };

    if (isLoading) {
        return (
            <div className="feedlane-loading">
                <div className="feedlane-loading__spinner"></div>
                <p>Loading ideas...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="feedlane-error">
                <p>Failed to load ideas.</p>
                <button
                    onClick={() => refetch()}
                    className="feedlane-button feedlane-button--secondary"
                >
                    Try Again
                </button>
            </div>
        );
    }

    const ideas = ideasData?.ideas || [];
    const categories = window.feedlaneData?.categories || [];

    return (
        <div className="feedlane-ideas">
            {/* Idea Submission Form */}
            {showForm ? (
                <IdeaForm
                    onSuccess={handleFormSuccess}
                    onCancel={handleFormCancel}
                />
            ) : (
                <>
                    {/* Ideas List */}
                    <div className="feedlane-ideas__list">
                        {ideas.length === 0 ? (
                            <div className="feedlane-empty">
                                <div className="feedlane-empty__icon">💡</div>
                                <h3>No Ideas Yet</h3>
                                <p>Be the first to share your brilliant idea!</p>
                            </div>
                        ) : (
                            ideas.map(idea => (
                                <IdeaCard key={idea.id} idea={idea} />
                            ))
                        )}
                    </div>

                    {/* Suggest Button */}
                    <div className="feedlane-suggest-button">
                        <button
                            className="feedlane-suggest-btn"
                            onClick={() => setShowForm(true)}
                        >
                            💡 SUGGEST A NEW IDEA
                        </button>
                    </div>
                </>
            )}
        </div>
    );
};

const IdeaCard = ({ idea }) => {
    const [hasVoted, setHasVoted] = useState(false);
    const queryClient = useQueryClient();

    const voteMutation = useMutation({
        mutationFn: (ideaId) => voteIdea(ideaId),
        onSuccess: () => {
            setHasVoted(true);
            queryClient.invalidateQueries(['ideas']);
        }
    });

    const handleVote = () => {
        if (!hasVoted) {
            voteMutation.mutate(idea.id);
        }
    };

    const getStatusColor = (status) => {
        const statusMap = {
            'completed': '#10B981',
            'in-progress': '#F59E0B',
            'planned': '#EF4444',
            'under-review': '#6B7280'
        };
        return statusMap[status] || '#6B7280';
    };

    const getCategoryBadge = (category) => {
        const categoryMap = {
            'feature-request': { label: 'FEATURE REQUEST', color: '#10B981' },
            'improvement': { label: 'IMPROVEMENT', color: '#F59E0B' },
            'bug-fix': { label: 'BUG FIX', color: '#EF4444' },
            'integration': { label: 'INTEGRATION', color: '#8B5CF6' }
        };

        return categoryMap[category] || { label: 'FEATURE REQUEST', color: '#10B981' };
    };

    const categoryInfo = getCategoryBadge(idea.category);

    return (
        <article className="feedlane-beamer-idea">
            <div className="feedlane-beamer-idea__header">
                <div className="feedlane-beamer-idea__vote">
                    <button
                        className={`feedlane-beamer-idea__vote-btn ${hasVoted ? 'voted' : ''}`}
                        onClick={handleVote}
                        disabled={hasVoted}
                    >
                        <FiChevronUp size={16} />
                    </button>
                    <span className="feedlane-beamer-idea__vote-count">{idea.vote_count || 0}</span>
                </div>

                <div className="feedlane-beamer-idea__content">
                    <div className="feedlane-beamer-idea__meta">
                        <span
                            className="feedlane-beamer-idea__category"
                            style={{ color: categoryInfo.color }}
                        >
                            {categoryInfo.label}
                        </span>
                        <span className="feedlane-beamer-idea__comments">
                            {idea.comment_count || 0} <FiMessageSquare size={14} />
                        </span>
                    </div>

                    <h3 className="feedlane-beamer-idea__title">{idea.title}</h3>

                    {idea.status && (
                        <div className="feedlane-beamer-idea__status">
                            <span
                                className="feedlane-beamer-idea__status-indicator"
                                style={{ backgroundColor: getStatusColor(idea.status) }}
                            ></span>
                            <span className="feedlane-beamer-idea__status-text">
                                {idea.status.toUpperCase().replace('-', ' ')}
                            </span>
                        </div>
                    )}

                    {idea.details && (
                        <div className="feedlane-beamer-idea__details">
                            <ul>
                                {idea.details.split('\n').filter(detail => detail.trim()).map((detail, index) => (
                                    <li key={index}>{detail.trim()}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            </div>
        </article>
    );
};

export default IdeasTab;
