import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FiX, FiChevronUp, FiMessageSquare, FiImage } from 'react-icons/fi';
import { fetchIdeas, submitIdea, voteIdea } from '../api/ideas';

const IdeasTab = () => {
    const [showForm, setShowForm] = useState(false);
    const [formData, setFormData] = useState({
        title: '',
        email: '',
        firstName: '',
        lastName: '',
        details: '',
        category: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);

    const queryClient = useQueryClient();

    const {
        data: ideasData,
        isLoading,
        error,
        refetch
    } = useQuery({
        queryKey: ['ideas'],
        queryFn: () => fetchIdeas({ per_page: 20, status: 'approved' }),
        staleTime: 3 * 60 * 1000, // 3 minutes
    });

    const submitMutation = useMutation({
        mutationFn: submitIdea,
        onSuccess: () => {
            setShowForm(false);
            setFormData({
                title: '',
                email: '',
                firstName: '',
                lastName: '',
                details: '',
                category: ''
            });
            queryClient.invalidateQueries(['ideas']);
        }
    });

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            await submitMutation.mutateAsync(formData);
        } catch (error) {
            console.error('Error submitting idea:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    if (isLoading) {
        return (
            <div className="feedlane-loading">
                <div className="feedlane-loading__spinner"></div>
                <p>Loading ideas...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="feedlane-error">
                <p>Failed to load ideas.</p>
                <button
                    onClick={() => refetch()}
                    className="feedlane-button feedlane-button--secondary"
                >
                    Try Again
                </button>
            </div>
        );
    }

    const ideas = ideasData?.ideas || [];
    const categories = window.feedlaneData?.categories || [];

    return (
        <div className="feedlane-ideas">
            {/* Idea Submission Form */}
            {showForm ? (
                <div className="feedlane-idea-form">
                    <div className="feedlane-idea-form__header">
                        <h3>Suggest an Idea</h3>
                        <p>What would make {window.feedlaneData?.siteName || 'our product'} more useful to you?</p>
                        <button
                            className="feedlane-idea-form__close"
                            onClick={() => setShowForm(false)}
                        >
                            <FiX size={20} />
                        </button>
                    </div>

                    <form onSubmit={handleSubmit} className="feedlane-idea-form__form">
                        <div className="feedlane-form-group">
                            <label htmlFor="title">TITLE</label>
                            <input
                                type="text"
                                id="title"
                                name="title"
                                value={formData.title}
                                onChange={handleInputChange}
                                placeholder="Your idea"
                                required
                            />
                        </div>

                        <div className="feedlane-form-group">
                            <label htmlFor="email">EMAIL</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value={formData.email}
                                onChange={handleInputChange}
                                placeholder="E-mail (required)"
                                required
                            />
                        </div>

                        <div className="feedlane-form-group">
                            <label htmlFor="firstName">FIRST NAME</label>
                            <input
                                type="text"
                                id="firstName"
                                name="firstName"
                                value={formData.firstName}
                                onChange={handleInputChange}
                                placeholder="First name (optional)"
                            />
                        </div>

                        <div className="feedlane-form-group">
                            <label htmlFor="lastName">LAST NAME</label>
                            <input
                                type="text"
                                id="lastName"
                                name="lastName"
                                value={formData.lastName}
                                onChange={handleInputChange}
                                placeholder="Last name (optional)"
                            />
                        </div>

                        <div className="feedlane-form-group">
                            <label htmlFor="details">DETAILS</label>
                            <textarea
                                id="details"
                                name="details"
                                value={formData.details}
                                onChange={handleInputChange}
                                placeholder="Any additional details"
                                rows={4}
                            />
                        </div>

                        <div className="feedlane-form-group">
                            <label htmlFor="category">CATEGORY</label>
                            <select
                                id="category"
                                name="category"
                                value={formData.category}
                                onChange={handleInputChange}
                                required
                            >
                                <option value="">Select Category</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.id}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div className="feedlane-form-actions">
                            <button type="button" className="feedlane-form-attach">
                                <FiImage size={16} />
                            </button>
                            <button
                                type="submit"
                                className="feedlane-form-submit"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? 'SUBMITTING...' : 'SUBMIT'}
                            </button>
                        </div>

                        <div className="feedlane-form-footer">
                            <span>🔗 Powered by {window.feedlaneData?.siteName || 'Feedlane'}</span>
                        </div>
                    </form>
                </div>
            ) : (
                <>
                    {/* Ideas List */}
                    <div className="feedlane-ideas__list">
                        {ideas.length === 0 ? (
                            <div className="feedlane-empty">
                                <div className="feedlane-empty__icon">💡</div>
                                <h3>No Ideas Yet</h3>
                                <p>Be the first to share your brilliant idea!</p>
                            </div>
                        ) : (
                            ideas.map(idea => (
                                <IdeaCard key={idea.id} idea={idea} />
                            ))
                        )}
                    </div>

                    {/* Suggest Button */}
                    <div className="feedlane-suggest-button">
                        <button
                            className="feedlane-suggest-btn"
                            onClick={() => setShowForm(true)}
                        >
                            💡 SUGGEST A NEW IDEA
                        </button>
                    </div>
                </>
            )}
        </div>
    );
};

const IdeaCard = ({ idea }) => {
    const [hasVoted, setHasVoted] = useState(false);
    const queryClient = useQueryClient();

    const voteMutation = useMutation({
        mutationFn: (ideaId) => voteIdea(ideaId),
        onSuccess: () => {
            setHasVoted(true);
            queryClient.invalidateQueries(['ideas']);
        }
    });

    const handleVote = () => {
        if (!hasVoted) {
            voteMutation.mutate(idea.id);
        }
    };

    const getStatusColor = (status) => {
        const statusMap = {
            'completed': '#10B981',
            'in-progress': '#F59E0B',
            'planned': '#EF4444',
            'under-review': '#6B7280'
        };
        return statusMap[status] || '#6B7280';
    };

    const getCategoryBadge = (category) => {
        const categoryMap = {
            'feature-request': { label: 'FEATURE REQUEST', color: '#10B981' },
            'improvement': { label: 'IMPROVEMENT', color: '#F59E0B' },
            'bug-fix': { label: 'BUG FIX', color: '#EF4444' },
            'integration': { label: 'INTEGRATION', color: '#8B5CF6' }
        };

        return categoryMap[category] || { label: 'FEATURE REQUEST', color: '#10B981' };
    };

    const categoryInfo = getCategoryBadge(idea.category);

    return (
        <article className="feedlane-beamer-idea">
            <div className="feedlane-beamer-idea__header">
                <div className="feedlane-beamer-idea__vote">
                    <button
                        className={`feedlane-beamer-idea__vote-btn ${hasVoted ? 'voted' : ''}`}
                        onClick={handleVote}
                        disabled={hasVoted}
                    >
                        <FiChevronUp size={16} />
                    </button>
                    <span className="feedlane-beamer-idea__vote-count">{idea.vote_count || 0}</span>
                </div>

                <div className="feedlane-beamer-idea__content">
                    <div className="feedlane-beamer-idea__meta">
                        <span
                            className="feedlane-beamer-idea__category"
                            style={{ color: categoryInfo.color }}
                        >
                            {categoryInfo.label}
                        </span>
                        <span className="feedlane-beamer-idea__comments">
                            {idea.comment_count || 0} <FiMessageSquare size={14} />
                        </span>
                    </div>

                    <h3 className="feedlane-beamer-idea__title">{idea.title}</h3>

                    {idea.status && (
                        <div className="feedlane-beamer-idea__status">
                            <span
                                className="feedlane-beamer-idea__status-indicator"
                                style={{ backgroundColor: getStatusColor(idea.status) }}
                            ></span>
                            <span className="feedlane-beamer-idea__status-text">
                                {idea.status.toUpperCase().replace('-', ' ')}
                            </span>
                        </div>
                    )}

                    {idea.details && (
                        <div className="feedlane-beamer-idea__details">
                            <ul>
                                {idea.details.split('\n').filter(detail => detail.trim()).map((detail, index) => (
                                    <li key={index}>{detail.trim()}</li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>
            </div>
        </article>
    );
};

export default IdeasTab;
