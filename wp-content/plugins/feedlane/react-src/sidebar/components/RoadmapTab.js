import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiChevronUp, FiMessageSquare } from 'react-icons/fi';
import { fetchIdeas } from '../api/ideas';

const RoadmapTab = () => {
    const [selectedStatus, setSelectedStatus] = useState('under-review');

    const roadmapStatuses = [
        { slug: 'under-review', name: 'Under review', color: '#6B7280', icon: '●' },
        { slug: 'planned', name: 'Planned', color: '#EF4444', icon: '●' },
        { slug: 'in-progress', name: 'In Progress', color: '#F59E0B', icon: '●' },
        { slug: 'completed', name: 'Completed', color: '#10B981', icon: '●' }
    ];

    return (
        <div className="feedlane-roadmap">
            {/* Status Tabs */}
            <div className="feedlane-roadmap__tabs">
                {roadmapStatuses.map(status => (
                    <button
                        key={status.slug}
                        onClick={() => setSelectedStatus(status.slug)}
                        className={`feedlane-roadmap__tab ${selectedStatus === status.slug ? 'active' : ''}`}
                        style={selectedStatus === status.slug ? {
                            borderBottomColor: status.color,
                            color: status.color
                        } : {}}
                    >
                        <span
                            className="feedlane-roadmap__tab-indicator"
                            style={{ color: status.color }}
                        >
                            {status.icon}
                        </span>
                        {status.name}
                    </button>
                ))}
            </div>

            {/* Status Content */}
            <div className="feedlane-roadmap__content">
                <RoadmapSection
                    status={roadmapStatuses.find(s => s.slug === selectedStatus)}
                />
            </div>
        </div>
    );
};

const RoadmapSection = ({ status }) => {
    const {
        data: ideasData,
        isLoading,
        error
    } = useQuery({
        queryKey: ['roadmap-ideas', status.slug],
        queryFn: () => fetchIdeas({
            status: status.slug,
            per_page: 50,
            orderby: 'votes'
        }),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    if (isLoading) {
        return (
            <div className="feedlane-loading">
                <div className="feedlane-loading__spinner"></div>
                <p>Loading roadmap...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="feedlane-error">
                <p>Failed to load roadmap items.</p>
            </div>
        );
    }

    const ideas = ideasData?.ideas || [];

    if (ideas.length === 0) {
        return (
            <div className="feedlane-empty">
                <div className="feedlane-empty__icon">🚧</div>
                <h3>No Items</h3>
                <p>No items in {status.name.toLowerCase()} yet.</p>
            </div>
        );
    }

    return (
        <div className="feedlane-roadmap-items">
            {ideas.map(idea => (
                <RoadmapItem key={idea.id} idea={idea} />
            ))}
        </div>
    );
};

const RoadmapItem = ({ idea }) => {
    const getCategoryBadge = (category) => {
        const categoryMap = {
            'feature-request': { label: 'FEATURE REQUEST', color: '#10B981' },
            'improvement': { label: 'IMPROVEMENT', color: '#F59E0B' },
            'bug-fix': { label: 'BUG FIX', color: '#EF4444' },
            'integration': { label: 'INTEGRATION', color: '#8B5CF6' }
        };

        return categoryMap[category] || { label: 'FEATURE REQUEST', color: '#10B981' };
    };

    const categoryInfo = getCategoryBadge(idea.category);

    return (
        <article className="feedlane-beamer-roadmap-item">
            <div className="feedlane-beamer-roadmap-item__header">
                <div className="feedlane-beamer-roadmap-item__vote">
                    <button className="feedlane-beamer-roadmap-item__vote-btn">
                        <FiChevronUp size={16} />
                    </button>
                    <span className="feedlane-beamer-roadmap-item__vote-count">{idea.vote_count || 0}</span>
                </div>

                <div className="feedlane-beamer-roadmap-item__content">
                    <h3 className="feedlane-beamer-roadmap-item__title">{idea.title}</h3>
                    <span
                        className="feedlane-beamer-roadmap-item__category"
                        style={{ color: categoryInfo.color }}
                    >
                        {categoryInfo.label}
                    </span>

                    {idea.details && (
                        <div className="feedlane-beamer-roadmap-item__details">
                            <ul>
                                {idea.details.split('\n').filter(detail => detail.trim()).map((detail, index) => (
                                    <li key={index}>{detail.trim()}</li>
                                ))}
                            </ul>
                        </div>
                    )}

                    {idea.comment_count > 0 && (
                        <button className="feedlane-beamer-roadmap-item__comments">
                            <FiMessageSquare size={14} />
                            Add a comment
                        </button>
                    )}
                </div>
            </div>
        </article>
    );
};

export default RoadmapTab;
