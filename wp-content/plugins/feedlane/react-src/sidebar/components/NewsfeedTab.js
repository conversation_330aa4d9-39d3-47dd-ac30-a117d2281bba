import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { FiBell, FiShare2, FiPlay } from 'react-icons/fi';
import { fetchNewsfeedPosts } from '../api/newsfeed';
import ReactionButtons from './ReactionButtons';
import FeedbackForm from './FeedbackForm';

const NewsfeedTab = () => {
    const {
        data: posts,
        isLoading,
        error,
        refetch
    } = useQuery({
        queryKey: ['newsfeed-posts'],
        queryFn: () => fetchNewsfeedPosts({ per_page: 10 }),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    if (isLoading) {
        return (
            <div className="feedlane-loading">
                <div className="feedlane-loading__spinner"></div>
                <p>Loading newsfeed...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="feedlane-error">
                <p>Failed to load newsfeed posts.</p>
                <button
                    onClick={() => refetch()}
                    className="feedlane-button feedlane-button--secondary"
                >
                    Try Again
                </button>
            </div>
        );
    }

    if (!posts || posts.length === 0) {
        return (
            <div className="feedlane-empty">
                <div className="feedlane-empty__icon">📢</div>
                <h3>No News Yet</h3>
                <p>Check back later for updates and announcements.</p>
            </div>
        );
    }

    return (
        <div className="feedlane-newsfeed">
            {/* Subscribe Banner */}
            <div className="feedlane-subscribe-banner">
                <div className="feedlane-subscribe-banner__icon">
                    <FiBell size={20} />
                </div>
                <span className="feedlane-subscribe-banner__text">
                    Subscribe to get our latest updates
                </span>
            </div>

            <div className="feedlane-newsfeed__posts">
                {posts.map(post => (
                    <NewsfeedPost
                        key={post.id}
                        post={post}
                    />
                ))}
            </div>
        </div>
    );
};

const NewsfeedPost = ({ post }) => {

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const getCategoryBadge = (category) => {
        const categoryMap = {
            'integrations': { label: 'INTEGRATIONS', color: '#8B5CF6', bg: '#F3F4F6' },
            'features': { label: 'FEATURES', color: '#10B981', bg: '#F0FDF4' },
            'improvements': { label: 'IMPROVEMENTS', color: '#F59E0B', bg: '#FFFBEB' },
            'announcements': { label: 'ANNOUNCEMENTS', color: '#EF4444', bg: '#FEF2F2' }
        };

        return categoryMap[category] || { label: 'NEW', color: '#6366F1', bg: '#EEF2FF' };
    };

    const categoryInfo = getCategoryBadge(post.category);

    return (
        <article className="feedlane-beamer-post">
            {/* Category Badge */}
            <div className="feedlane-beamer-post__header">
                <div className="feedlane-beamer-post__badges">
                    <span
                        className="feedlane-beamer-post__badge"
                        style={{
                            color: categoryInfo.color,
                            backgroundColor: categoryInfo.bg
                        }}
                    >
                        {categoryInfo.label}
                    </span>
                    <span className="feedlane-beamer-post__date">
                        {formatDate(post.date)}
                    </span>
                </div>
                <button className="feedlane-beamer-post__share">
                    <FiShare2 size={16} />
                </button>
            </div>

            {/* Post Title */}
            <h3 className="feedlane-beamer-post__title">
                {post.category === 'integrations' && '🚀 '}
                {post.title}
            </h3>

            {/* Post Content */}
            <div className="feedlane-beamer-post__content">
                {/* Availability Info */}
                {post.availability && (
                    <p className="feedlane-beamer-post__availability">
                        <em>Available for {post.availability} customers</em>
                    </p>
                )}

                {/* Main Content */}
                <div
                    className="feedlane-beamer-post__text"
                    dangerouslySetInnerHTML={{
                        __html: post.content
                    }}
                />

                {/* Video Thumbnail */}
                {post.video_url && (
                    <div className="feedlane-beamer-post__video">
                        <div className="feedlane-beamer-post__video-thumbnail">
                            {post.featured_image && (
                                <img
                                    src={post.featured_image}
                                    alt={post.title}
                                    loading="lazy"
                                />
                            )}
                            <button className="feedlane-beamer-post__play-btn">
                                <FiPlay size={24} />
                            </button>
                            <div className="feedlane-beamer-post__video-info">
                                <span className="feedlane-beamer-post__video-title">
                                    {post.video_title || post.title}
                                </span>
                                <div className="feedlane-beamer-post__video-meta">
                                    <span>0</span>
                                    <span>📎</span>
                                    <span>✏️</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Regular Image */}
                {post.featured_image && !post.video_url && (
                    <div className="feedlane-beamer-post__image">
                        <img
                            src={post.featured_image}
                            alt={post.title}
                            loading="lazy"
                        />
                    </div>
                )}
            </div>

            {/* Reactions and Feedback */}
            <div className="feedlane-beamer-post__interactions">
                <ReactionButtons postId={post.id} />
                <FeedbackForm postId={post.id} />
            </div>
        </article>
    );
};

export default NewsfeedTab;
