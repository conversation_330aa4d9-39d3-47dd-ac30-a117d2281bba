import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { FiUpload, FiX } from 'react-icons/fi';
import toast from 'react-hot-toast';
import { submitIdea } from '../api/ideas';

const IdeaForm = ({ onSuccess, onCancel }) => {
    const categories = window.feedlaneData?.categories || [
        { value: 'improvement', label: 'Improvement' },
        { value: 'feature-request', label: 'Feature Request' },
        { value: 'bug', label: 'Bug Report' },
        { value: 'feedback', label: 'General Feedback' }
    ];

    const isLoggedIn = window.feedlaneData?.currentUser?.id || window.feedlaneData?.is_user_logged_in;

    const [formData, setFormData] = useState({
        title: '',
        details: '',
        category: categories[0]?.slug || categories[0]?.value || 'feature-request',
        email: '',
        first_name: '',
        last_name: '',
        image: null
    });

    const [imagePreview, setImagePreview] = useState(null);

    const submitMutation = useMutation({
        mutationFn: (data) => submitIdea(data),
        onSuccess: () => {
            toast.success('Idea submitted successfully! It will be reviewed before being published.');
            onSuccess && onSuccess();
        },
        onError: (error) => {
            const message = error.response?.data?.message || 'Failed to submit idea';
            toast.error(message);
        },
    });

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                toast.error('Please select an image file');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                toast.error('Image size must be less than 5MB');
                return;
            }

            setFormData(prev => ({
                ...prev,
                image: file
            }));

            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }
    };

    const removeImage = () => {
        setFormData(prev => ({
            ...prev,
            image: null
        }));
        setImagePreview(null);
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Validate required fields
        if (!formData.title.trim() || !formData.details.trim()) {
            toast.error('Title and details are required');
            return;
        }

        if (!isLoggedIn) {
            if (!formData.first_name.trim() || !formData.last_name.trim() || !formData.email.trim()) {
                toast.error('Name and email are required');
                return;
            }

            if (!isValidEmail(formData.email)) {
                toast.error('Please enter a valid email address');
                return;
            }
        }

        // Check if we have an image to upload
        if (formData.image) {
            // Create FormData for file upload
            const submitData = new FormData();
            Object.keys(formData).forEach(key => {
                if (formData[key] !== null) {
                    submitData.append(key, formData[key]);
                }
            });
            submitMutation.mutate(submitData);
        } else {
            // Send as JSON if no image
            const submitData = { ...formData };
            delete submitData.image; // Remove null image field
            submitMutation.mutate(submitData);
        }
    };

    const isValidEmail = (email) => {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    };



    return (
        <div className="feedlane-idea-form">
            <div className="feedlane-idea-form__header">
                <h4>Submit Your Idea</h4>
                <button
                    onClick={onCancel}
                    className="feedlane-idea-form__close"
                >
                    <FiX size={20} />
                </button>
            </div>

            <form onSubmit={handleSubmit} className="feedlane-idea-form__form">
                {/* Guest user fields */}
                {!isLoggedIn && (
                    <div className="feedlane-idea-form__guest-fields">
                        <div className="feedlane-form-row">
                            <input
                                type="text"
                                name="first_name"
                                value={formData.first_name}
                                onChange={handleInputChange}
                                placeholder="First name"
                                className="feedlane-input"
                                required
                            />
                            <input
                                type="text"
                                name="last_name"
                                value={formData.last_name}
                                onChange={handleInputChange}
                                placeholder="Last name"
                                className="feedlane-input"
                                required
                            />
                        </div>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="Email address"
                            className="feedlane-input"
                            required
                        />
                    </div>
                )}

                {/* Title */}
                <div className="feedlane-form-field">
                    <label htmlFor="idea-title" className="feedlane-label">
                        Title *
                    </label>
                    <input
                        id="idea-title"
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Brief description of your idea"
                        className="feedlane-input"
                        required
                    />
                </div>

                {/* Category */}
                <div className="feedlane-form-field">
                    <label htmlFor="idea-category" className="feedlane-label">
                        Category *
                    </label>
                    <select
                        id="idea-category"
                        name="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="feedlane-select"
                        required
                    >
                        {categories.map(cat => (
                            <option key={cat.slug || cat.value} value={cat.slug || cat.value}>
                                {cat.name || cat.label}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Details */}
                <div className="feedlane-form-field">
                    <label htmlFor="idea-details" className="feedlane-label">
                        Details *
                    </label>
                    <textarea
                        id="idea-details"
                        name="details"
                        value={formData.details}
                        onChange={handleInputChange}
                        placeholder="Provide more details about your idea..."
                        className="feedlane-textarea"
                        rows="4"
                        required
                    />
                </div>

                {/* Image Upload */}
                <div className="feedlane-form-field">
                    <label className="feedlane-label">
                        Image (optional)
                    </label>

                    {imagePreview ? (
                        <div className="feedlane-image-preview">
                            <img src={imagePreview} alt="Preview" />
                            <button
                                type="button"
                                onClick={removeImage}
                                className="feedlane-image-preview__remove"
                            >
                                <FiX size={16} />
                            </button>
                        </div>
                    ) : (
                        <div className="feedlane-file-upload">
                            <input
                                type="file"
                                id="idea-image"
                                accept="image/*"
                                onChange={handleImageChange}
                                className="feedlane-file-input"
                            />
                            <label htmlFor="idea-image" className="feedlane-file-label">
                                <FiUpload size={20} />
                                <span>Upload Image</span>
                                <small>Max 5MB, JPG/PNG</small>
                            </label>
                        </div>
                    )}
                </div>

                {/* Actions */}
                <div className="feedlane-idea-form__actions">
                    <button
                        type="button"
                        onClick={onCancel}
                        className="feedlane-button feedlane-button--secondary"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={submitMutation.isPending}
                        className="feedlane-button feedlane-button--primary"
                    >
                        {submitMutation.isPending ? (
                            <>
                                <div className="feedlane-spinner feedlane-spinner--small"></div>
                                Submitting...
                            </>
                        ) : (
                            'Submit Idea'
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default IdeaForm;
