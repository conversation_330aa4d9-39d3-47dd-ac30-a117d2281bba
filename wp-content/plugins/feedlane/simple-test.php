<?php
/**
 * Simple Test for Feedlane - No init hooks
 */

// Include WordPress
require_once '../../../wp-config.php';

echo "=== Feedlane Simple Test ===\n\n";

// Test 1: Plugin activation
echo "1. Plugin Status:\n";
$is_active = is_plugin_active('feedlane/feedlane.php');
echo "   " . ($is_active ? "✅" : "❌") . " Plugin Active\n";

if (!$is_active) {
    echo "   🔄 Activating plugin...\n";
    $result = activate_plugin('feedlane/feedlane.php');
    if (is_wp_error($result)) {
        echo "   ❌ Activation failed: " . $result->get_error_message() . "\n";
        exit;
    } else {
        echo "   ✅ Plugin activated\n";
    }
}

// Test 2: Classes
echo "\n2. Classes:\n";
$classes = [
    'WPDeveloper\Feedlane\Plugin',
    'WPDeveloper\Feedlane\Core\Admin',
    'WPDeveloper\Feedlane\Core\Shortcodes',
];

foreach ($classes as $class) {
    $exists = class_exists($class);
    echo "   " . ($exists ? "✅" : "❌") . " $class\n";
}

// Test 3: Assets
echo "\n3. Assets:\n";
$assets = [
    'js/feedlane-sidebar.min.js',
    'css/feedlane-sidebar.min.css',
    'js/feedlane-admin.min.js',
    'css/feedlane-admin.min.css',
];

foreach ($assets as $asset) {
    $file_path = FEEDLANE_ASSETS_DIR_PATH . $asset;
    $exists = file_exists($file_path);
    echo "   " . ($exists ? "✅" : "❌") . " $asset";
    if ($exists) {
        echo " (" . round(filesize($file_path)/1024, 1) . "KB)";
    }
    echo "\n";
}

// Test 4: Constants
echo "\n4. Constants:\n";
$constants = [
    'FEEDLANE_VERSION',
    'FEEDLANE_FILE',
    'FEEDLANE_ASSETS_DIR_PATH',
    'FEEDLANE_ASSETS_DIR_URL',
];

foreach ($constants as $constant) {
    $exists = defined($constant);
    echo "   " . ($exists ? "✅" : "❌") . " $constant";
    if ($exists) {
        echo " = " . constant($constant);
    }
    echo "\n";
}

// Test 5: Manual post type registration
echo "\n5. Manual Post Type Test:\n";

// Register post types manually to test
register_post_type('feedlane_posts_test', [
    'labels' => [
        'name' => 'Test Newsfeed Posts',
        'singular_name' => 'Test Newsfeed Post',
    ],
    'public' => false,
    'show_ui' => true,
    'supports' => ['title', 'editor'],
]);

$test_post_type_exists = post_type_exists('feedlane_posts_test');
echo "   " . ($test_post_type_exists ? "✅" : "❌") . " Manual post type registration works\n";

// Test 6: Database tables
echo "\n6. Database Tables:\n";
global $wpdb;

$tables = [
    $wpdb->prefix . 'feedlane_reactions',
    $wpdb->prefix . 'feedlane_feedback',
    $wpdb->prefix . 'feedlane_votes',
    $wpdb->prefix . 'feedlane_analytics',
];

foreach ($tables as $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") === $table;
    echo "   " . ($exists ? "✅" : "❌") . " $table\n";
}

// Test 7: Options
echo "\n7. Plugin Options:\n";
$options = [
    'feedlane_enable_newsfeed',
    'feedlane_enable_ideas',
    'feedlane_enable_roadmap',
    'feedlane_enable_floating_sidebar',
];

foreach ($options as $option) {
    $value = get_option($option);
    $exists = $value !== false;
    echo "   " . ($exists ? "✅" : "❌") . " $option";
    if ($exists) {
        echo " = " . (is_bool($value) ? ($value ? 'true' : 'false') : $value);
    }
    echo "\n";
}

// Test 8: URLs
echo "\n8. Important URLs:\n";
echo "   🌐 Homepage: " . home_url() . "\n";
echo "   ⚙️ Admin: " . admin_url('admin.php?page=feedlane') . "\n";
echo "   🔧 Settings: " . admin_url('admin.php?page=feedlane-settings') . "\n";
echo "   🔍 Debug: " . admin_url('admin.php?page=feedlane-debug') . "\n";
echo "   🧪 Browser Test: " . home_url('/wp-content/plugins/feedlane/browser-test.php') . "\n";

// Test 9: Try to manually trigger plugin initialization
echo "\n9. Manual Plugin Initialization:\n";
try {
    if (function_exists('feedlane')) {
        echo "   ✅ feedlane() function exists\n";
        
        // Get the plugin instance
        $plugin = feedlane();
        if ($plugin) {
            echo "   ✅ Plugin instance retrieved\n";
            
            // Try to call init method directly
            if (method_exists($plugin, 'init')) {
                echo "   ✅ init() method exists\n";
                echo "   🔄 Calling init() manually...\n";
                
                // Call init method
                $plugin->init();
                
                echo "   ✅ init() called successfully\n";
                
                // Now check if post types are registered
                $feedlane_posts_exists = post_type_exists('feedlane_posts');
                $feedlane_ideas_exists = post_type_exists('feedlane_ideas');
                
                echo "   " . ($feedlane_posts_exists ? "✅" : "❌") . " feedlane_posts registered\n";
                echo "   " . ($feedlane_ideas_exists ? "✅" : "❌") . " feedlane_ideas registered\n";
                
                // Check taxonomies
                $idea_category_exists = taxonomy_exists('idea_category');
                $roadmap_status_exists = taxonomy_exists('roadmap_status');
                
                echo "   " . ($idea_category_exists ? "✅" : "❌") . " idea_category registered\n";
                echo "   " . ($roadmap_status_exists ? "✅" : "❌") . " roadmap_status registered\n";
                
                // Check shortcodes
                $test_shortcode_exists = shortcode_exists('feedlane_test');
                echo "   " . ($test_shortcode_exists ? "✅" : "❌") . " feedlane_test shortcode registered\n";
                
            } else {
                echo "   ❌ init() method not found\n";
            }
        } else {
            echo "   ❌ Plugin instance is null\n";
        }
    } else {
        echo "   ❌ feedlane() function not found\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "\nNext Steps:\n";
echo "1. Visit: " . home_url('/wp-content/plugins/feedlane/browser-test.php') . "\n";
echo "2. Go to WordPress admin and check for Feedlane menu\n";
echo "3. Try accessing: " . admin_url('post-new.php?post_type=feedlane_posts') . "\n";
echo "4. Test shortcodes in a page/post\n";
?>
