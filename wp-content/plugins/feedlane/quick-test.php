<?php
/**
 * Quick Test Script for Feedlane
 * 
 * Run this to quickly test if everything is working
 */

// Include WordPress
require_once '../../../wp-config.php';

echo "=== Feedlane Quick Test ===\n\n";

// Force plugin activation if not active
if (!is_plugin_active('feedlane/feedlane.php')) {
    echo "🔄 Activating plugin...\n";
    activate_plugin('feedlane/feedlane.php');
}

// Trigger init hook to register post types
echo "🔄 Triggering WordPress init...\n";
do_action('init');

// Test post types
echo "\n1. Post Types:\n";
$post_types = ['feedlane_posts', 'feedlane_ideas'];
foreach ($post_types as $post_type) {
    $exists = post_type_exists($post_type);
    echo "   " . ($exists ? "✅" : "❌") . " $post_type\n";
    
    if ($exists) {
        // Try to access the add new page URL
        $add_new_url = admin_url("post-new.php?post_type=$post_type");
        echo "   📝 Add new: $add_new_url\n";
    }
}

// Test taxonomies
echo "\n2. Taxonomies:\n";
$taxonomies = ['idea_category', 'roadmap_status'];
foreach ($taxonomies as $taxonomy) {
    $exists = taxonomy_exists($taxonomy);
    echo "   " . ($exists ? "✅" : "❌") . " $taxonomy\n";
    
    if ($exists) {
        $terms = get_terms(['taxonomy' => $taxonomy, 'hide_empty' => false]);
        echo "   📊 Terms: " . (is_array($terms) ? count($terms) : 0) . "\n";
    }
}

// Test shortcodes
echo "\n3. Shortcodes:\n";
$shortcodes = ['feedlane_test', 'feedlane_sidebar', 'feedlane_newsfeed'];
foreach ($shortcodes as $shortcode) {
    $exists = shortcode_exists($shortcode);
    echo "   " . ($exists ? "✅" : "❌") . " [$shortcode]\n";
}

// Test Gutenberg block
echo "\n4. Gutenberg Block:\n";
$block_registry = WP_Block_Type_Registry::get_instance();
$block_exists = $block_registry->is_registered('feedlane/sidebar');
echo "   " . ($block_exists ? "✅" : "❌") . " feedlane/sidebar\n";

// Test admin menu
echo "\n5. Admin Menu:\n";
global $menu;
$menu_found = false;
if (isset($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'feedlane') {
            $menu_found = true;
            break;
        }
    }
}
echo "   " . ($menu_found ? "✅" : "❌") . " Feedlane menu\n";

// Test assets
echo "\n6. Assets:\n";
$assets = [
    'js/feedlane-sidebar.min.js',
    'css/feedlane-sidebar.min.css',
];

foreach ($assets as $asset) {
    $file_path = FEEDLANE_ASSETS_DIR_PATH . $asset;
    $exists = file_exists($file_path);
    echo "   " . ($exists ? "✅" : "❌") . " $asset\n";
}

// Create a test post if post types are working
if (post_type_exists('feedlane_posts')) {
    echo "\n7. Creating Test Content:\n";
    
    // Create test newsfeed post
    $test_post = wp_insert_post([
        'post_title' => 'Test Newsfeed Post - ' . date('Y-m-d H:i:s'),
        'post_content' => 'This is a test post to verify that the feedlane_posts post type is working correctly.',
        'post_status' => 'publish',
        'post_type' => 'feedlane_posts',
    ]);
    
    if ($test_post && !is_wp_error($test_post)) {
        echo "   ✅ Test newsfeed post created (ID: $test_post)\n";
        echo "   📝 Edit: " . admin_url("post.php?post=$test_post&action=edit") . "\n";
    } else {
        echo "   ❌ Failed to create test post\n";
    }
}

if (post_type_exists('feedlane_ideas')) {
    // Create test idea
    $test_idea = wp_insert_post([
        'post_title' => 'Test Idea - ' . date('Y-m-d H:i:s'),
        'post_content' => 'This is a test idea to verify that the feedlane_ideas post type is working correctly.',
        'post_status' => 'publish',
        'post_type' => 'feedlane_ideas',
    ]);
    
    if ($test_idea && !is_wp_error($test_idea)) {
        echo "   ✅ Test idea created (ID: $test_idea)\n";
        echo "   📝 Edit: " . admin_url("post.php?post=$test_idea&action=edit") . "\n";
        
        // Add category and status if taxonomies exist
        if (taxonomy_exists('idea_category')) {
            wp_set_post_terms($test_idea, ['Feature Request'], 'idea_category');
            echo "   🏷️ Category added\n";
        }
        
        if (taxonomy_exists('roadmap_status')) {
            wp_set_post_terms($test_idea, ['Under Review'], 'roadmap_status');
            echo "   📊 Status added\n";
        }
    } else {
        echo "   ❌ Failed to create test idea\n";
    }
}

// Test URLs
echo "\n8. Important URLs:\n";
echo "   🏠 Homepage: " . home_url() . "\n";
echo "   ⚙️ Admin: " . admin_url('admin.php?page=feedlane') . "\n";
echo "   🔧 Settings: " . admin_url('admin.php?page=feedlane-settings') . "\n";
echo "   🔍 Debug: " . admin_url('admin.php?page=feedlane-debug') . "\n";
echo "   📝 Add Newsfeed: " . admin_url('post-new.php?post_type=feedlane_posts') . "\n";
echo "   💡 Add Idea: " . admin_url('post-new.php?post_type=feedlane_ideas') . "\n";

// Summary
echo "\n=== SUMMARY ===\n";
$post_types_ok = post_type_exists('feedlane_posts') && post_type_exists('feedlane_ideas');
$taxonomies_ok = taxonomy_exists('idea_category') && taxonomy_exists('roadmap_status');
$shortcodes_ok = shortcode_exists('feedlane_test');
$assets_ok = file_exists(FEEDLANE_ASSETS_DIR_PATH . 'js/feedlane-sidebar.min.js');

echo "Post Types: " . ($post_types_ok ? "✅ WORKING" : "❌ BROKEN") . "\n";
echo "Taxonomies: " . ($taxonomies_ok ? "✅ WORKING" : "❌ BROKEN") . "\n";
echo "Shortcodes: " . ($shortcodes_ok ? "✅ WORKING" : "❌ BROKEN") . "\n";
echo "Assets: " . ($assets_ok ? "✅ WORKING" : "❌ BROKEN") . "\n";
echo "Admin Menu: " . ($menu_found ? "✅ WORKING" : "❌ BROKEN") . "\n";

if ($post_types_ok && $taxonomies_ok && $shortcodes_ok && $assets_ok) {
    echo "\n🎉 ALL SYSTEMS GO! Plugin is working correctly.\n";
    echo "\nNext steps:\n";
    echo "1. Visit admin to manage content\n";
    echo "2. Test shortcodes in pages/posts\n";
    echo "3. Check Gutenberg blocks in editor\n";
    echo "4. Test Elementor widgets if available\n";
    echo "5. Check floating sidebar on frontend\n";
} else {
    echo "\n⚠️ Some issues detected. Check the details above.\n";
}

echo "\n=== Test Complete ===\n";
?>
