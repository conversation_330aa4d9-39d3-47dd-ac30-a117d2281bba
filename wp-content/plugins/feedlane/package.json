{"name": "feedlane", "version": "1.0.0", "description": "A React-powered WordPress plugin for feedback management", "main": "index.js", "scripts": {"start": "wp-scripts start", "build": "wp-scripts build", "build:zip": "npm run build && wp dist-archive .", "dev": "wp-scripts start --mode=development", "lint:js": "wp-scripts lint-js", "lint:css": "wp-scripts lint-style", "test": "wp-scripts test-unit-js"}, "repository": {"type": "git", "url": "https://github.com/feedlane/feedlane-wp-plugin.git"}, "keywords": ["wordpress", "plugin", "feedback", "react", "firebase", "tailwindcss"], "author": "Feedlane Team", "license": "GPL-3.0+", "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@wordpress/api-fetch": "^6.55.0", "@wordpress/i18n": "^5.24.0", "@wordpress/scripts": "26.6.0", "autoprefixer": "^10.4.21", "copy-webpack-plugin": "^11.0.0", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "url-loader": "^4.1.1", "webpack-remove-empty-scripts": "^1.1.1"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/react-query": "^5.79.0", "dompurify": "^3.2.6", "firebase": "^10.14.1", "html-react-parser": "^5.2.5", "js-cookie": "^3.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^4.12.0", "react-router-dom": "^6.30.1", "sweetalert2": "^11.22.0"}}