/* betterdocs-code-snippet-ceef835a Starts */ .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a { } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .betterdocs-code-snippet pre code { font-size: 14px; color: #333333; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .line-numbers { background-color: #f5f5f5; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .line-numbers .line-number { color: #999999; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .copy-button { font-size: 12px; margin: 0px; padding: 8px; color: null; background: transparent; border: 1px solid currentColor; border-radius: 8px; padding: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .copy-button:hover { color: null; border-color: null; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .betterdocs-code-snippet.theme-dark pre code { color: #e6edf3; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .betterdocs-code-snippet.theme-dark .line-numbers { background-color: #161b22; border-right: 1px solid #30363d; } .betterdocs-code-snippet-block.betterdocs-code-snippet-ceef835a .betterdocs-code-snippet.theme-dark .line-numbers .line-number { color: #656d76; }  @media(max-width: 1024px){ } @media(max-width: 767px){ }/* =betterdocs-code-snippet-ceef835a= Ends */