# Translation of Plugins - Yoast SEO - Stable (latest release) in Arabic
# This file is distributed under the same license as the Plugins - Yoast SEO - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-11-04 12:13:40+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: ar\n"
"Project-Id-Version: Plugins - Yoast SEO - Stable (latest release)\n"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:10
msgid "Website"
msgstr "الموقع"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:22
msgid "Alternate website name"
msgstr "اسم موقع بديل"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:11
msgid "This name is shown for your site in the search results."
msgstr "يظهر هذا الاسم لموقعك في نتائج البحث."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:26
msgid "Organization or Person"
msgstr "منظمة أو شخص"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:103
msgid "Alternate organization name"
msgstr "اسم المنظمة البديل"

#: src/integrations/admin/crawl-settings-integration.php:208
#: js/dist/new-settings.js:126
msgid "Remove unused resources"
msgstr "إزالة الموارد غير المستخدمة"

#: src/deprecated/admin/metabox/class-metabox-section-inclusive-language.php:49
msgid "Inclusive language"
msgstr "لغة شاملة"

#: admin/views/class-yoast-feature-toggles.php:91 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Inclusive language analysis"
msgstr "تحليل لغوي شامل"

#: admin/views/tabs/dashboard/features.php:84
msgid "This feature has been disabled, since it is not supported for your language yet."
msgstr "هدا تم تعطيل هذه الميزة لأنها غير داعمة للغتك حتى الآن."

#: admin/views/class-yoast-feature-toggles.php:98
msgid "Discover why inclusive language is important for SEO."
msgstr "اكتشف سبب أهمية اللغة الشاملة لتحسين محركات البحث."

#: admin/views/class-yoast-feature-toggles.php:97 js/dist/new-settings.js:213
msgid "The inclusive language analysis offers suggestions to write more inclusive copy."
msgstr "يقدم تحليل اللغة الشامل اقتراحات لكتابة نسخة أكثر شمولاً."

#. translators: 1: Yoast SEO Premium
#: src/integrations/admin/deactivated-premium-integration.php:99
msgid "Activate %1$s!"
msgstr "تفعيل %1$s!"

#. translators: %s expands to WordProof.
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:53
msgid "Contact %s support."
msgstr "اتصل %s بالدعم"

#: src/config/wordproof-translations.php:88
msgid "Contact WordProof support"
msgstr "اتصل بدعم WordProof"

#. translators: 1: Yoast SEO Premium 2: Link start tag to activate premium, 3:
#. Link closing tag.
#: src/integrations/admin/deactivated-premium-integration.php:86
msgid "You've installed %1$s but it's not activated yet. %2$sActivate %1$s now!%3$s"
msgstr "لقد قمت بتنصيب %1$s ولكن لم يتم تنشيطه بعد. %2$s تنشيط%1$s  الآن!%3$s"

#: src/integrations/admin/crawl-settings-integration.php:223
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Max number of characters to allow in searches"
msgstr "الحد الأقصى لعدد الأحرف المسموح به في عمليات البحث"

#: src/integrations/admin/crawl-settings-integration.php:113
#: js/dist/new-settings.js:224
msgid "Global feed"
msgstr "خلاصة (Feed) العالمية"

#: src/integrations/admin/crawl-settings-integration.php:114
#: js/dist/new-settings.js:224
msgid "Global comment feeds"
msgstr "خلاصات (feeds) التعليق العالمية"

#: src/integrations/admin/crawl-settings-integration.php:116
#: js/dist/new-settings.js:224
msgid "Post authors feeds"
msgstr "خلاصات (feeds) مؤلفو المقالة"

#: src/integrations/admin/crawl-settings-integration.php:117
#: js/dist/new-settings.js:224
msgid "Post type feeds"
msgstr "خلاصات (feeds) نوع المقالة"

#: src/integrations/admin/crawl-settings-integration.php:118
#: js/dist/new-settings.js:224
msgid "Category feeds"
msgstr "تصنيف الخلاصات (feeds)"

#: src/integrations/admin/crawl-settings-integration.php:119
#: js/dist/new-settings.js:224
msgid "Tag feeds"
msgstr "وسم الخلاصات (feeds)"

#: src/integrations/admin/crawl-settings-integration.php:120
#: js/dist/new-settings.js:224
msgid "Custom taxonomy feeds"
msgstr "فئة الخلاصات (feeds) المخصصة"

#: src/integrations/admin/crawl-settings-integration.php:121
#: js/dist/new-settings.js:224
msgid "Search results feeds"
msgstr "خلاصات (feeds) نتائج البحث "

#: src/integrations/admin/crawl-settings-integration.php:122
#: js/dist/new-settings.js:224
msgid "Atom/RDF feeds"
msgstr "خلاصات (feeds) Atom/RDF"

#: src/integrations/admin/crawl-settings-integration.php:126
#: js/dist/new-settings.js:224
msgid "Shortlinks"
msgstr "روابط مختصرة"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "REST API links"
msgstr "روابط REST API"

#: src/integrations/admin/crawl-settings-integration.php:128
#: js/dist/new-settings.js:224
msgid "RSD / WLW links"
msgstr "روابط RSD / WLW"

#: src/integrations/admin/crawl-settings-integration.php:129
#: js/dist/new-settings.js:224
msgid "oEmbed links"
msgstr "روابط oEmbed"

#: src/integrations/admin/crawl-settings-integration.php:130
#: js/dist/new-settings.js:224
msgid "Generator tag"
msgstr "مولد وسم "

#: src/integrations/admin/crawl-settings-integration.php:149
#: js/dist/new-settings.js:224
msgid "Emoji scripts"
msgstr "برامج نصية إيموجي Emoji"

#: src/integrations/admin/crawl-settings-integration.php:131
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Pingback HTTP header"
msgstr "ترويسة Pingback HTTP"

#: src/integrations/admin/crawl-settings-integration.php:132
#: js/dist/new-settings.js:224
msgid "Powered by HTTP header"
msgstr "مشغل بواسطة ترويسة HTTP"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Basic crawl settings"
msgstr "إعدادات تتبع الارتباطات الأساسية"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Remove links added by WordPress to the header and &lt;head&gt;."
msgstr "قم بإزالة الروابط المضافة بواسطة WordPress إلى الترويسة و  &lt;head&gt;."

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Feed crawl settings"
msgstr "إعدادات تتبع الارتباطات خلاصة Feed"

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Remove feed links added by WordPress that aren't needed for this site."
msgstr "قم بإزالة روابط الخلاصة (feed) المضافة بواسطة WordPress والتي ليست ضرورية لهذا الموقع."

#: src/integrations/admin/crawl-settings-integration.php:313
msgid "By removing Global comments feed, Post comments feeds will be removed too."
msgstr "عن طريق إزالة خلاصة (feed) التعليقات العالمية ، ستتم أيضًا إزالة خلاصات (feeds) تعليقات المقالة."

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "SEO configuration"
msgstr "تكوين SEO"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:151
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "لاحظنا أنك لم تقم بتكوين Yoast SEO بالكامل حتى الآن. قم بتحسين إعدادات تحسين محركات البحث (SEO) بشكل أكبر عن طريق استخدام %1$s التكوين المحسن لأول مرة%2$s."

#: admin/views/redirects.php:188
msgid "No items found."
msgstr "لم يتم العثور على شيء."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/crawl-settings.php:22
msgid "To make the crawling of your site more efficient and environmental friendly, %1$s allows you to remove URLs (added by WordPress) that might not be needed for your site."
msgstr "لجعل تتبع الارتباطات إلى موقعك أكثر كفاءة وصديقًا للبيئة ، يسمح لك %1$s بإزالة عناوين URL (المضافة بواسطة WordPress) التي قد لا تكون ضرورية لموقعك."

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/dashboard/crawl-settings.php:29
msgid "%1$sLearn more about crawl settings and how they could benefit your site.%2$s"
msgstr "%1$sتعرف على المزيد حول إعدادات تتبع الارتباطات وكيف يمكن أن تفيد موقعك.%2$s"

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sتعرف على المزيد حول إعدادات تتبع الارتباطات.%2$s"

#: src/integrations/admin/crawl-settings-integration.php:115
#: js/dist/new-settings.js:224
msgid "Post comments feeds"
msgstr " خلاصة Feed تعليقات المقالة "

#: admin/pages/network.php:28 admin/views/tabs/dashboard/crawl-settings.php:17
#: admin/views/tabs/network/crawl-settings.php:19
#: src/integrations/admin/crawl-settings-integration.php:165
msgid "Crawl settings"
msgstr "إعدادات تتبع الارتباطات"

#: admin/views/redirects.php:37
msgid "Regex Redirects"
msgstr "تحويلات Regex"

#: admin/views/redirects.php:50
msgid "Plain redirects"
msgstr "عمليات إعادة التوجيه البسيطة"

#: admin/views/redirects.php:59 admin/views/redirects.php:155
#: admin/views/redirects.php:203
msgid "Type"
msgstr "الصنف"

#: admin/views/redirects.php:66 admin/views/redirects.php:75
msgid "301 Moved Permanently"
msgstr "301 منقول بشكل دائم"

#. translators: 1: opens a link. 2: closes the link.
#: admin/views/redirects.php:91
msgid "The redirect type is the HTTP response code sent to the browser telling the browser what type of redirect is served. %1$sLearn more about redirect types%2$s."
msgstr "نوع إعادة توجيه هو رمز استجابة HTTP إرسالها إلى مستعرض قول متصفح ما يتم تقديم نوع من إعادة التوجيه. %1$s مزيد من المعلومات حول أنواع إعادة التوجيه %2$s  الصورة."

#: admin/views/redirects.php:100 admin/views/redirects.php:165
#: admin/views/redirects.php:212
msgid "Old URL"
msgstr "URL القديم"

#: admin/views/redirects.php:122
msgid "Add Redirect"
msgstr "إضافة إعادة توجية"

#: admin/views/redirects.php:137
msgid "All redirect types"
msgstr "جميع أنواع إعادة التوجيه"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:99
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "يجب عليك الانتهاء من تكوين%1$s لأول مرة%2$s للتأكد من تحسين بيانات تحسين محركات البحث (SEO) لديك وتعيين إعدادات Yoast SEO الأساسية لموقعك."

#: src/integrations/admin/first-time-configuration-integration.php:125
msgid "First-time configuration"
msgstr "التكوين لأول مرة"

#: admin/views/tabs/tool/import-seo.php:94
msgid "Step 4: Go through the first time configuration"
msgstr "الخطوة 4: انتقل إلى التكوين لأول مرة"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "فشل التحقق من صحة بنية بيانات AIOSEO."

#: src/integrations/admin/import-integration.php:212
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "تم إلغاء استيراد AIOSEO لأن بعض بيانات AIOSEO مفقودة. يرجى المحاولة واتخاذ الخطوات التالية للإصلاح:"

#: src/integrations/admin/import-integration.php:215
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "إذا لم تقم مطلقًا بحفظ أي من إعدادات \"مظهر البحث\" من AIOSEO ، فالرجاء القيام بذلك أولاً وتشغيل الاستيراد مرة أخرى."

#: src/integrations/admin/import-integration.php:218
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "إذا كنت قد قمت بالفعل بحفظ إعدادات \"مظهر البحث\" من AIOSEO واستمرت المشكلة ، فيرجى الاتصال بفريق الدعم لدينا حتى نتمكن من إلقاء نظرة فاحصة."

#: src/services/health-check/links-table-check.php:48
msgid "Links table"
msgstr "جدول الروابط"

#: src/services/health-check/page-comments-check.php:48
msgid "Page comments"
msgstr "تعليقات الصفحة"

#: src/services/health-check/postname-permalink-check.php:48
msgid "Postname permalink"
msgstr " الرابط الثابت لاسم المقالة"

#. translators: %s expands to Wincher
#. translators: %s expands to WordProof
#: src/integrations/third-party/wincher.php:125
#: src/integrations/third-party/wordproof-integration-toggle.php:130
#: src/integrations/third-party/wordproof-integration-toggle.php:152
msgid "Currently, the %s integration is not available for multisites."
msgstr "حاليًا ، لا يتوفر تكامل ال %s للمواقع المتعددة."

#: src/config/wordproof-translations.php:79
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:47
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-block-editor.js:1
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-classic-editor.js:1
msgid "Open settings"
msgstr "فتح الإعدادات"

#: src/config/wordproof-translations.php:70
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-block-editor.js:1
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-classic-editor.js:1
msgid "Open authentication"
msgstr "فتح المصادقة"

#: src/integrations/admin/import-integration.php:95
msgid "The cleanup can take a long time depending on your site's size."
msgstr "يمكن أن تستغرق عملية التنظيف وقتًا طويلاً بناءً على حجم موقعك."

#: src/integrations/admin/import-integration.php:96
msgid "Note: "
msgstr "ملاحظة:"

#: src/integrations/admin/import-integration.php:97
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "بعد استيراد البيانات من إضافة تحسين محركات البحث (SEO) ، يرجى التأكد من تنظيف جميع البيانات الأصلية من هذه الإضافة. (الخطوة 5)"

#: src/integrations/admin/import-integration.php:105
msgid "Clean up"
msgstr "عملية تنظيف"

#: src/integrations/admin/import-integration.php:106
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "الرجاء تحديد إضافة تحسين محركات البحث SEO أدناه لمعرفة البيانات التي يمكن استيرادها."

#: src/integrations/admin/import-integration.php:107
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "بمجرد التأكد من أن موقعك يعمل بشكل صحيح مع البيانات المستوردة من إضافة اخرى لتحسين محركات البحث SEO، يمكنك تنظيف جميع البيانات الأصلية من تلك الإضافة."

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:109
msgid "The import from %s includes:"
msgstr "الاستيراد من %s يتضمن:"

#: src/integrations/admin/import-integration.php:113
#: src/integrations/admin/import-integration.php:123
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "البيانات الوصفية للمقالة (عناوين وأوصاف تحسين محركات البحث (SEO) ، وما إلى ذلك)"

#: src/integrations/admin/import-integration.php:114
#: src/integrations/admin/import-integration.php:124
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "ملاحظة: لن يتم استيراد هذه البيانات الوصفية إلا إذا لم تكن هناك بيانات وصفية حالية لـ Yoast SEO حتى الآن."

#: src/integrations/admin/import-integration.php:118
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "ملاحظة: ستحل هذه الإعدادات محل الإعدادات الافتراضية لـ Yoast SEO."

#: src/integrations/admin/import-integration.php:234
msgid "Cleanup failed with the following error:"
msgstr "فشلت عملية التنظيف مع الخطأ التالي:"

#: src/integrations/admin/import-integration.php:98
msgid "Select SEO plugin"
msgstr "حدد إضافة SEO"

#: src/integrations/admin/import-integration.php:99
msgid "No data found from other SEO plugins."
msgstr "لا توجد بيانات من الإضافات SEO الأخرى."

#: src/integrations/admin/import-integration.php:236
msgid "Import failed with the following error:"
msgstr "فشل الاستيراد مع الخطأ التالي:"

#: src/services/health-check/default-tagline-check.php:48
msgid "Default tagline"
msgstr "سطر الوصف الافتراضي"

#: src/integrations/admin/import-integration.php:94
msgid "The import can take a long time depending on your site's size."
msgstr "يمكن أن يستغرق الاستيراد وقتًا طويلاً بناءً على حجم موقعك."

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "تم التنصيب بنجاح"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "تدوينة"

#. translators: %s: 'Wincher'
#: src/integrations/third-party/wincher.php:77
msgid "The %s integration offers the option to track specific keyphrases and gain insights in their positions."
msgstr "يوفر تكامل %s خيار تتبع عبارة رئيسية معينة واكتساب رؤى على ترتيبها."

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:309
#: js/dist/integrations-page.js:3
msgid "Activate %s"
msgstr "تفعيل %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:303
msgid "Update %s"
msgstr "تحديث %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:296
msgid "Renew %s"
msgstr "تجديد %s"

#: src/integrations/admin/workouts-integration.php:238
msgid "Get help activating your subscription"
msgstr "احصل على مساعدة لتفعيل اشتراكك"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:232
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "يبدو أنك تقوم بتشغيل نسخة قديمة وغير مفعلة من %1$s ، يرجى تفعيل اشتراكك في %2$sMyYoast%3$s والتحديث إلى أحدث نسخة (17.7 على الأقل) للوصول إلى قسم التدريبات المحدثة لدينا ."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:228
msgid "Activate your subscription of %s"
msgstr "تفعيل اشتراكك من %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:216
msgid "Update to the latest version of %s"
msgstr "قم بالتحديث إلى أحدث نسخة من %s"

#: src/integrations/admin/workouts-integration.php:209
msgid "Renew your subscription"
msgstr "جدد اشتراكك"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:202
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "يتطلب الوصول إلى أحدث التدريبات نسخة محدثة من %s (17.7 على الأقل) ، ولكن يبدو أن اشتراكك قد انتهى. يرجى تجديد اشتراكك للتحديث والوصول إلى أحدث المميزات."

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:219
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "يبدو أنك تقوم بتشغيل نسخة قديم من%1$s ، الرجاء %2$s التحديث إلى أحدث نسخة (على الأقل 17.7)%3$s للوصول إلى قسم التدريبات المحدث."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:199
msgid "Renew your subscription of %s"
msgstr "قم بتجديد اشتراكك من %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:142
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "ابدأ بسرعة مع %1$s%2$s تكوين المرة الأولى%3$s وقم بتهيئة Yoast SEO باستخدام إعدادات تحسين محركات البحث SEO المثلى لموقعك!"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Post Content"
msgstr "محتوى المقالة"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the permalink"
msgstr "تم الاستبدال بالرابط الدائم"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Current or first category title"
msgstr "عنوان التصنيف الحالي أو الأول"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Category Title"
msgstr "عنوان التصنيف"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the post content"
msgstr "تم استبداله بمحتوى المقالة"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Author first name"
msgstr "الاسم الأول للمؤلف"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current day"
msgstr "اليوم الحالي"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the last name of the author"
msgstr "تم استبداله باسم عائلة المؤلف"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the first name of the author"
msgstr "تم استبداله بلاسم الأول للمؤلف"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Current month"
msgstr "الشهر الحالي"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Current date"
msgstr "التاريخ الحالي"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Author last name"
msgstr "اسم عائلة المؤلف"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Post day"
msgstr "يوم المقالة"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Post month"
msgstr "شهر المقالة"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Post year"
msgstr "سنة المقالة"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the day the post was published"
msgstr "تم استبداله باليوم الذي تم فيه نشر المقالة"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the month the post was published"
msgstr "تم استبداله بالشهر الذي تم فيه نشر المقالة"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the year the post was published"
msgstr "تم استبداله بالسنة الذي تم فيها نشر المقالة"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:110
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "يبدو أنك لا تستخدم الاضافة %1$s%2$s الخاص بنا%3$s. %4$s قم بالترقية اليوم%5$s لفتح المزيد من الأدوات وميزات تحسين محركات البحث SEO لإبراز منتجاتك في نتائج البحث."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "فيما يلي التفاصيل الفنية للخطأ. راجع %1$sهذه الصفحة%2$s للحصول على شرح أكثر تفصيلاً."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "تدريبات"

#: src/schema-templates/cooking-time.block.php:12
msgid "Recipe cooking time"
msgstr "وقت طهي الوصفة"

#: src/schema-templates/preparation-time.block.php:14
msgid "Prep time"
msgstr "وقت التحضير"

#: src/schema-templates/preparation-time.block.php:12
msgid "Recipe prep time"
msgstr "وقت تحضير الوصفة"

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:370
msgid "A new version of %1$s is available. %2$sRenew your subscription%3$s if you want to update to the latest version."
msgstr "تتوفر نسخة جديدة من%1$s. %2$s جدد اشتراكك%3$s إذا كنت تريد التحديث إلى أحدث نسخة."

#: src/schema-templates/cooking-time.block.php:12
msgid "The time it takes to actually cook the dish."
msgstr "الوقت المستغرق بالفعل لطهي الطبق."

#: src/schema-templates/preparation-time.block.php:12
msgid "The time it takes to prepare the items to be used in the instructions."
msgstr "الوقت المستغرق لإعداد العناصر التي سيتم استخدامها في التعليمات."

#: admin/views/class-yoast-integration-toggles.php:99
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "حسن جودة البحث في موقعك! يساعد تلقائيًا المستخدمين لديك في العثور على الأساس والمحتوى الأكثر أهمية في نتائج البحث الداخلية الخاصة بك. كما أنه يزيل الصفحات والمقالات بدون فهرسة من نتائج البحث في موقعك."

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "Recipe ingredient(s)"
msgstr "مكونات الوصفة"

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "The ingredients used in the recipe, e.g. sugar, flour or garlic."
msgstr "المكونات المستخدمة في الوصفة، على سبيل المثال: سكر، طحين أو ثوم."

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients title"
msgstr "عنوان المكونات"

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients"
msgstr "المكونات"

#: src/schema-templates/recipe-ingredients.block.php:15
msgid "Enter an ingredient"
msgstr "أدخل مكونا"

#: src/schema-templates/recipe-instructions.block.php:13
msgid "Recipe instructions"
msgstr "تعليمات الوصفة"

#: src/schema-templates/recipe-instructions.block.php:13
msgid "The steps of making the recipe, in the form of an ordered list with HowToStep and/or HowToSection items."
msgstr "خطوات عمل الوصفة، في شكل قائمة مرتبة باستخدام عناصر HowToStep و / أو HowToSection."

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions title"
msgstr "عنوان التعليمات"

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions"
msgstr "التعليمات"

#: src/schema-templates/recipe-instructions.block.php:16
msgid "Enter step"
msgstr "خطوة الإدخال"

#. translators: 1: Yoast SEO, 2: Zapier.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Set up automated actions when you publish or update your content. By connecting %1$s with %2$s, you can easily send out your published posts to any of its 2000+ destinations, such as Twitter, Facebook and more."
msgstr "قم بإعداد الإجراءات التلقائية عند نشر أو تحديث المحتوى الخاص بك. من خلال ربط %1$s بـ%2$s، يمكنك بسهولة إرسال مقالاتك المنشورة إلى أي من أكثر من 2000 وجهة، مثل فيسبوك وتويتر والمزيد."

#. translators: %s: Zapier.
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:88
#: admin/views/class-yoast-integration-toggles.php:101
msgid "Find out more about our %s integration."
msgstr "اكتشف المزيد حول تكامل %s الخاص بنا."

#: admin/views/class-yoast-feature-toggles.php:134
msgid "Read more about how internal linking can improve your site structure."
msgstr "اقرأ المزيد حول كيفية تحسين الارتباط الداخلي لبنية موقعك."

#: admin/views/class-yoast-feature-toggles.php:123
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "ابحث عن البيانات ذات الصلة حول المحتوى الخاص بك في قسم Insights في صندوق ميتا Yoast SEO. سترى الكلمات التي تستخدمها كثيرًا وما إذا كانت تتطابق مع كلماتك المفتاحية!"

#: admin/views/class-yoast-feature-toggles.php:124
msgid "Find out how Insights can help you improve your content."
msgstr "اكتشف كيف يمكن أن تساعدك الرؤى (Insights) في تحسين المحتوى الخاص بك."

#: admin/views/class-yoast-feature-toggles.php:130 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Link suggestions"
msgstr "اقتراحات الرابط"

#: admin/views/class-yoast-feature-toggles.php:121 js/dist/block-editor.js:447
#: js/dist/classic-editor.js:443 js/dist/elementor.js:447
#: js/dist/new-settings.js:215 js/dist/new-settings.js:224
msgid "Insights"
msgstr "الرؤى"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "عفوًا، حدث خطأ ما ولم نتمكن من إكمال عملية تحسين بيانات SEO الخاصة بك. يرجى التأكد من تفعيل اشتراكك في MyYoast من خلال إكمال%1$sهذه الخطوات%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:148
msgid "The social appearance settings for archives require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "تتطلب إعدادات المظهر الاجتماعي لأرشيف البيانات الوصفية لـ Open Graph (والتي تم تعطيلها حاليًا). يمكنك تفعيل هذا في إعدادات %1$s 'الاجتماعي' ضمن علامة التبويب %2$s 'الفيسبوك'."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:136
msgid "The social appearance settings for taxonomies require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "تتطلب إعدادات المظهر الاجتماعي لفئات البيانات الوصفية لـ Open Graph (والتي تم تعطيلها حاليًا). يمكنك تفعيل هذا في إعدادات %1$s 'الاجتماعي' ضمن علامة التبويب %2$s 'الفيسبوك'."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:124
msgid "The social appearance settings for content types require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "تتطلب إعدادات المظهر الاجتماعي لأنواع المحتويات البيانات الوصفية لـ Open Graph (والتي تم تعطيلها حاليًا). يمكنك تفعيل هذا في إعدادات %1$s 'الاجتماعي' ضمن علامة التبويب %2$s 'الفيسبوك'."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:108
msgid "The social appearance settings for your homepage require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "تتطلب إعدادات المظهر الاجتماعي لصفحتك الرئيسية البيانات الوصفية لـ Open Graph (والتي تم تعطيلها حاليًا). يمكنك تفعيل هذا في إعدادات %1$s 'الاجتماعي' ضمن علامة التبويب %2$s 'الفيسبوك'."

#: admin/views/tabs/social/facebook.php:48
msgid "Default image"
msgstr "الصورة الافتراضية"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/social/facebook.php:33
msgid "The social homepage settings have been moved to the %1$s‘Search appearance’ settings under the ‘General’ tab%2$s."
msgstr "تم نقل إعدادات الصفحة الرئيسية الاجتماعية إلى إعدادات %1$s 'مظهر بحث' ضمن علامة التبويب 'عام' %2$s."

#: src/schema-templates/cooking-time.block.php:14
#: js/dist/externals/schemaBlocks.js:13
msgid "Cooking time"
msgstr "وقت الطبخ"

#: src/schema-templates/recipe-description.block.php:14
msgid "Enter a recipe description"
msgstr "أدخل وصف الوصفة"

#: src/schema-templates/recipe-description.block.php:12
msgid "A description of the recipe."
msgstr "وصف الوصفة"

#. translators: %s is the plural version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:42
msgid "%s archive settings"
msgstr "%s إعدادات الأرشيف"

#: admin/views/tabs/metas/general.php:15
msgid "Rewrite titles"
msgstr "أعد كتابة العناوين"

#: admin/views/tabs/metas/paper-content/front-page-content.php:31
msgid "These are the image, title and description used when a link to your homepage is shared on social media."
msgstr "هذه هي الصورة والعنوان والوصف المستخدم عند مشاركة رابط إلى صفحتك الرئيسية على وسائل التواصل الاجتماعي."

#: src/schema-templates/recipe-description.block.php:12
msgid "Recipe description"
msgstr "وصف الوصفة"

#: admin/views/tabs/metas/paper-content/front-page-content.php:58
#: src/integrations/admin/social-templates-integration.php:223
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social title"
msgstr "اللقب الاجتماعي"

#: admin/views/tabs/metas/paper-content/front-page-content.php:59
#: src/integrations/admin/social-templates-integration.php:224
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social description"
msgstr "الوصف الاجتماعي"

#. translators: %s expands to 'Yoast SEO Premium'.
#: src/integrations/admin/social-templates-integration.php:236
msgid "To unlock this feature please update %s to the latest version."
msgstr "لإلغاء قفل هذه الميزة، يرجى تحديث %s إلى أحدث نسخة."

#: admin/views/redirects.php:22
#: src/integrations/admin/crawl-settings-integration.php:332
#: src/integrations/admin/social-templates-integration.php:251
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "Unlock with Premium"
msgstr "إلغاء القفل مع Premium"

#. translators: %s is the singular version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:18
msgid "Single %s settings"
msgstr "إعدادات %s فردية"

#: src/integrations/admin/addon-installation/installation-integration.php:97
msgid "Installing and activating addons"
msgstr "تنصيب وتفعيل الإضافات"

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:120
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s تابع إلى%2$s%3$s"

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:145
msgid "Addon activated."
msgstr "تم تفعيل الإضافة."

#: src/integrations/admin/addon-installation/installation-integration.php:147
msgid "You are not allowed to activate plugins."
msgstr "غير مسموح لك بتفعيل الإضافات."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:151
msgid "Addon activation failed because of an error: %s."
msgstr "فشل تفعيل الإضافة بسبب خطأ: %s."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:175
msgid "Addon installed."
msgstr "تم تنصيب الإضافة."

#: src/integrations/admin/addon-installation/installation-integration.php:179
msgid "You are not allowed to install plugins."
msgstr "غير مسموح لك بتنصيب الإضافات."

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:183
msgid "Addon installation failed because of an error: %s."
msgstr "فشل تنصيب الإضافة بسبب خطأ: %s."

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:92
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "لم يتم تنصيب أي إضافة %1$s. يبدو أنك لا تملك أي اشتراكات مفعلة."

#: src/schema-templates/recipe.block.php:39
msgid "Add a block to your recipe..."
msgstr "أضف مكوّن لوصفتك..."

#: src/schema-templates/recipe-name.block.php:10
msgid "Enter a recipe name"
msgstr "أدخل اسم وصفة"

#. translators: %1$s expands to Yoast
#: src/schema-templates/recipe.block.php:9
msgid "%1$s Recipe"
msgstr "وصفة %1$s"

#: src/schema-templates/recipe.block.php:36
msgid "Create a Recipe in an SEO-friendly way. You can only use one Recipe block per post."
msgstr "قم بإنشاء وصفة بطريقة صديقة لSEO. يمكنك استخدام مجموعة مكوّن واحدة فقط لكل مقالة."

#: src/schema-templates/recipe.block.php:37
msgid "Serves #"
msgstr "يقدم #"

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:237
msgid "Required by %s"
msgstr "مطلوب من قبل %s"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:83
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "تم تفعيل التحديثات التلقائية بناءً على هذا الإعداد لـ %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:93
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "تم تعطيل التحديثات التلقائية بناءً على هذا الإعداد لـ %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:136 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "New"
msgstr "جديد"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:114
msgid "Enable Breadcrumbs for your theme"
msgstr "تفعيل مسارات التنقل (Breadcrumbs) لقالبك"

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:111
msgid "Note: You can always choose to enable / disable them for your theme below. This setting will not apply to breadcrumbs inserted through a widget, a block or a shortcode."
msgstr "ملاحظة: يمكنك دائمًا اختيار تفعيلهم/ تعطيلهم للقالب الخاص بك أدناه. لن يتم تطبيق هذا الإعداد على مسارات التنقل المُدرجة من خلال ودجت أو مكوّن أو رمز قصير."

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "لا يمكن العثور على هذه المقالة."

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "لا يمكن العثور على هذه العنصر."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "يعتبر المصطلح غير صالح. تم إعطاء السبب التالي بواسطة ووردبريس: %s"

#: admin/class-yoast-form.php:1045 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "تم تعطيل هذه الميزة لأن المواقع الفرعية لا تُرسل بيانات التعقب أبدًا."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the Yoast SEO Multilingual add-on%2$s as well!"
msgstr "لاحظنا أنك قمت بتثبيت إضافة WPML. للتأكد من تعيين عناوين URL الأساسية الخاصة بك بشكل صحيح، %1$sقم بتثبيت وبتنشيط إضافة Yoast SEO Multilingual%2$s أيضًا!"

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "بسبب تغيير في إعداد بنية الفئات لديك، بعض بيانات تحسين محركات البحث SEO الخاصة بك بحاجة إلى إعادة المعالجة."

#: admin/views/class-yoast-feature-toggles.php:194 js/dist/new-settings.js:217
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "يؤدي هذا إلى إضافة سطر المؤلف وتقدير وقت القراءة إلى مقتطف المقالة عند مشاركته على Slack."

#: admin/views/class-yoast-feature-toggles.php:195
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "اكتشف كيف يمكن للمقتطف المنسق rich snippet تحسين الرؤية ونسبة النقر إلى الظهور."

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s دقيقة"
msgstr[1] "دقيقة واحدة (%s)"
msgstr[2] "دقيقتان (%s)"
msgstr[3] "%s دقائق"
msgstr[4] "%s دقيقة"
msgstr[5] "%s دقيقة"

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "كُتب بواسطة"

#: inc/class-wpseo-admin-bar-menu.php:364
msgid "Google Rich Results Test"
msgstr "نتائج اختبار Google Rich"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "وقت القراءة المُقدّر"

#: admin/views/class-yoast-feature-toggles.php:192
msgid "Enhanced Slack sharing"
msgstr "مشاركة Slack المحسنة"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:120
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "انتظر لمدة أسبوع أو نحو ذلك، حتى يقوم %1$s تلقائيًا بمعالجة معظم المحتوى الخاص بك في الخلفية."

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "نظرًا لتغيير في إعداد تركيبة الوسم الخاص بك، يلزم إعادة معالجة بعض بيانات الـ SEO."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "يقدم تكامل %s اقتراحات ورؤى للكلمات الرئيسية المتعلقة بعبارة التركيز التي تم إدخالها."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "تتيح لك علامة التبويب هذه تعطيل عمليات دمج %1$s بشكل انتقائي مع منتجات الجهات الخارجية لجميع المواقع في الشبكة. بشكل افتراضي، يتم تمكين جميع عمليات الدمج، مما يسمح لمسؤولي الموقع بالاختيار لأنفسهم ما إذا كانوا يريدون التبديل بين تشغيل الدمج أو إيقاف تشغيله لموقعهم. عند تعطيل الدمج هنا، لن يتمكن مسؤولو الموقع من استخدام هذا الدمج على الإطلاق."

#: admin/class-admin.php:248
msgid "Activate your subscription"
msgstr "تفعيل اشتراكك"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "عفوًا، حدث خطأ ما ولم نتمكن من استكمال تحسين بيانات SEO الخاصة بك. الرجاء النقر فوق الزر مرة أخرى لإعادة بدء العملية."

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "تم إعادة تعيين كل الروابط الدائمة بنجاح"

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. "
msgstr "يمكنك تسريع موقعك والحصول على نظرة ثاقبة على بنية الروابط الداخلية الخاصة بك عن طريق السماح لنا بإجراء بعض التحسينات على طريقة تخزين بيانات \"تحسين محركات البحث\" SEO."

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/first-time-configuration.js:3 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "البدء بإجراء تحسين بيانات SEO"

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "تعرف على المزيد حول فوائد بيانات (SEO) المُحسّنة."

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "يمكنك تسريع موقعك والحصول على نظرة ثاقبة على بنية الروابط الداخلية الخاصة بك عن طريق السماح لنا بإجراء بعض التحسينات على طريقة تخزين بيانات \"تحسين محركات البحث\" SEO. إذا كان لديك الكثير من المحتوى، فقد يستغرق الأمر بعض الوقت، لكن ثق بنا، فالأمر يستحق ذلك."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "تحسين بيانات SEO"

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "إذا استمرت المشكلة، يرجى الاتصال بالدعم."

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "حدث خطأ ما ولم نتمكن من إكمال تحسين بيانات SEO الخاصة بك. الرجاء %1$sإعادة بدء العملية%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:822
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "إذا كنت لا تزال بحاجة إلى الدعم ولديك اشتراك نشط لهذا المنتج، فيرجى إرسال بريد إلكتروني إلى %s."

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:819
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "ربما تجد إجابة لسؤالك في %1$sمركز المساعدة%2$s."

#: inc/class-addon-manager.php:816
msgid "Need support?"
msgstr "بحاجة إلى الدعم؟"

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:235
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "لن يؤدي تعطيل خرائط مواقع XML الخاصة بـ Yoast SEO إلى تعطيل خرائط مواقع ووردبريس الأساسية. في بعض الحالات، قد ينتج عن %1$s أخطاء SEO على موقعك%2$s. قد يتم الإبلاغ عن هذه الأخطاء في Google Search Console وأدوات أخرى."

#: admin/views/licenses.php:75
msgid "Make your products stand out in Google"
msgstr "اجعل منتجاتك مميزة في Google"

#: admin/views/licenses.php:58
msgid "Everything you need for Google News"
msgstr "كل ما تحتاجه لأخبار Google"

#: admin/views/licenses.php:45
msgid "Start ranking better for your videos"
msgstr "ابدأ بالحصول على ترتيب أفضل لمقاطع الفيديو الخاصة بك"

#: admin/views/licenses.php:31
msgid "Stop losing customers to other local businesses"
msgstr "توقف عن فقد العملاء لصالح أعمال محلية أخرى"

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:163
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "يتيح القسم المتقدم لـ صندوق الحقول التعريفية %1$s للمستخدم بإزالة المقالات من نتائج البحث أو تغيير الرابط القياسي (canonical). تسمح الإعدادات الموجودة في علامة تبويب مخطط Schema للمستخدم بتغيير بيانات مخطط meta الوصفية لمقالة ما. هذه أشياء قد لا تريد أن يفعلها أي كاتب. لهذا السبب، بشكل افتراضي، يمكن للمحررين والمسؤولين فقط القيام بذلك. الضبط على \"%2$s\" يتيح لجميع المستخدمين تغيير هذه الإعدادات."

#: admin/views/class-yoast-feature-toggles.php:159
msgid "Security: no advanced or schema settings for authors"
msgstr "الأمان: لا توجد إعدادات متقدمة أو إعدادات مخطط Schema للكتّاب"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "صفحة حول"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "مقالة"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "مقالة إخبارية"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "مقالة محتوى إعلاني"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "مقالة ساخرة"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "مقالة علمية"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "مقالة تقنية"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "تقرير"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "صفحة ويب"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "صفحة أسئلة شائعة"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "صفحة أسئلة وأجوبة"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "صفحة ملف شخصي"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "صفحة ويب طبية"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "صفحة مجموعة (تشكيلة)"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "صفحة إتمام طلب"

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "قائمة عقارات"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "صفحة نتائج بحث"

#: src/config/schema-types.php:64
msgid "Item Page"
msgstr "صفحة عنصر"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "صفحة تواصل"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "منشورات شبكات تواصل اجتماعي"

#: admin/views/class-yoast-feature-toggles.php:170
#: admin/views/class-yoast-feature-toggles.php:171 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Usage tracking"
msgstr "تتبع الاستخدام"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:175
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "اسمح لنا بتتبع بعض البيانات حول موقعك لتحسين الإضافة الخاصة بنا."

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "نظرًا لتغيير في إعداد عنوان الرابط لصفحتك الرئيسية، يلزم إعادة معالجة بعض بيانات SEO."

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "بسبب التغيير في بنية الرابط الثابت الخاص بك، يجب إعادة معالجة بعض بيانات SEO الخاصة بك."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:70
msgid "%1$s Internal Linking Blocks"
msgstr "مكوّنات %1$s الربط الداخلي"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#. translators: %s: Expands to an indexable object's name, like a post type or
#. taxonomy.
#: admin/views/tabs/metas/paper-content/post_type/post-type.php:31
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:39
msgid "Show SEO settings for %1$s?"
msgstr "عرض إعدادات SEO لـ %1$s"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:74
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "يساعدك عداد الروابط النصية على تحسين بنية موقعك.%1$sاكتشف كيف يمكن لعداد الروابط النصية تحسين SEO الخاصة بك%2$s."

#: src/services/health-check/links-table-reports.php:59
msgid "The text link counter feature is not working as expected"
msgstr "لا تعمل ميزة عداد الروابط النصية كما هو متوقع"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:48
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "تعرض أعمدة الروابط عدد المقالات الموجودة على هذا الموقع والتي ترتبط %3$sبـ%4$s هذه المقالة وعدد الروابط المرتبطة%3$sمن%4$s هذه المقالة. تعرف على المزيد حول %1$sكيفية استخدام هذه الميزات لتحسين الارتباط الداخلي الخاص بك %2$s، مما يعزز بشكل كبير الـ SEO الخاص بك."

#: src/services/health-check/links-table-reports.php:46
msgid "The text link counter is working as expected"
msgstr "عداد الروابط النصية يعمل كما هو متوقع"

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:101
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sاكتشف كيفية حل هذه المشكلة على مركز المساعدة الخاص بنا%2$s."

#: src/presenters/admin/migration-error-presenter.php:59
msgid "Show debug information"
msgstr "أظهر معلومات التصحيح"

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:32
msgid "%1$s adds several columns to this page."
msgstr "يضيف %1$s عدة أعمدة إلى هذه الصفحة."

#: src/presenters/admin/indexing-notification-presenter.php:112
msgid "We estimate this will take a couple of minutes."
msgstr "نقدر أن هذا سيستغرق بضع دقائق."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "واجهت %s مشاكل في إنشاء جداول قاعدة البيانات اللازمة لتسريع موقعك."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "إذا كنت تريد أن تعرض محركات البحث هذا الموقع ضمن نتائجها، فيجب عليك %1$sالانتقال إلى إعدادات القراءة%2$s وإلغاء تحديد المربع الخاص بالظهور لمحركات البحث."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "لا أريد أن يظهر هذا الموقع ضمن نتائج البحث."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:39
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "لقد كتبنا مقالة حول %1$sطريقة استخدام نتيجة SEO ودرجة قابلية القراءة%2$s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:53
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "سيستمر موقعك في العمل بشكل طبيعي، ولكنه لن يستفيد بالكامل من %s. "

#: src/presenters/admin/indexing-notification-presenter.php:108
msgid "We estimate this will take less than a minute."
msgstr "نتوقع أن يستغرق هذا أقل من دقيقة."

#: src/presenters/admin/indexing-notification-presenter.php:115
msgid "We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr "نقدر أن هذا قد يستغرق وقتًا طويلاً، نظرًا لحجم موقعك. كبديل للانتظار، يمكنك:"

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:127
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sتشغيل عملية الفهرسة على الخادم %2$sباستخدام %3$sWP CLI%2$s."

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "يرجى قراءة%1$sهذه المقالة المساعدة%2$s لمعرفة كيفية حل هذه المشكلة."

#: src/integrations/front-end/theme-titles.php:49
msgid "a theme that has proper title-tag theme support, or adapt your theme to have that support"
msgstr "قالب يحتوي على دعم مناسب title-tag بالقالب، أو قم بضبط القالب الاص بك للحصول على الدعم"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Term hierarchy"
msgstr "مصطلح التسلسل الهرمي"

#: admin/views/class-yoast-feature-toggles.php:182
msgid "REST API: Head endpoint"
msgstr "REST API: Head endpoint"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term ancestors hierarchy"
msgstr "تم استبداله بـ ancestors hierarchy للعنصر"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:186
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "تمنحك %1$s REST API endpoint جميع البيانات الوصفية (metadata) التي تحتاجها لعنوان URL محدد. هذا سيجعل من السهل جدًا على مواقع ووردبريس الخالية من الترويسات (headless) باستخدام %1$s لجميع المخرجات الوصفية لتحسين محركات البحث SEO."

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:64
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sيمكنك تغيير سطر الوصف في أداة التخصيص%2$s."

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "اسم مقالتك موجود بالفعل في رابط URL الخاص بمقالاتك وصفحاتك."

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "يوصى بشدة أن يكون اسم المقالة الخاص بك ضمن الرابط URL لمقالاتك وصفحاتك. ضع في اعتبارك إعداد تركيبة الرابط الدائم على %s."

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "يتم عرض تعليقاتك مقالاتك على صفحة واحدة. هذا تمامًا كما نقترحه. أنت تقوم بعمل جيد!"

#: src/services/health-check/default-tagline-reports.php:33
msgid "You are using a custom tagline or an empty one."
msgstr "أنت تستخدم سطر وصف مخصص، أو أنّ الوصف فارغاً."

#: src/services/health-check/default-tagline-reports.php:44
msgid "You should change the default WordPress tagline"
msgstr "يجب عليك تغيير سطر الوصف الافتراضي لـ ووردبريس"

#: src/services/health-check/default-tagline-reports.php:46
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "لا يزال لديك سطر وصف ووردبريس الافتراضي. حتى لو كان فارغًا ربما يكون أفضل."

#: src/services/health-check/default-tagline-reports.php:31
msgid "You changed the default WordPress tagline"
msgstr "لقد قمت بتغيير سطر وصف ووردبريس الافتراضي"

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "تتضمن بنية الرابط الدائم الخاص بك اسم المقالة"

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "ليس لديك اسم المقالة الخاص بك ضمن الرابط URL لمقالاتك وصفحاتك"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "التعليقات على مشاركاتك تقسم إلى صفحات متعددة. نظرًا لأن هذا ليس ضروريًا في ٩٩٩ حالة من أصل ١٠٠٠ حالة ، نوصيك بتعطيله. لإصلاح هذا ، قم بإلغاء تحديد \"تقسيم التعليقات إلى صفحات ...\" في صفحة إعدادات المناقشة."

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "تنقسم التعليقات إلى صفحات متعددة"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "يتم عرض التعليقات على صفحة واحدة"

#: src/helpers/post-helper.php:101
msgid "No title"
msgstr "بلا عنوان"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sالانتقال إلى صفحة إعدادات المناقشة%2$s"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sهذا التقرير بواسطة إضافة %2$s%3$s"

#: admin/metabox/class-metabox.php:192
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "إذا كنت تريد تطبيق <code>ميتا</code> إعدادات الروبوتات المتقدمة لهذه الصفحة ، يرجى تحديدها في الحقل التالي."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:155 admin/taxonomy/class-taxonomy.php:99
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "المتصفح الذي تستخدمه حاليًا قديم للأسف. نظرًا لأننا نسعى جاهدين لمنحك أفضل تجربة ممكنة، لم نعد ندعم هذا المتصفح. بدلاً من ذلك، الرجاء استخدام %1$sمتصفح فايرفوكس%4$s، أو %2$sمتصفح كروم%4$s أو %3$sمايكروسوفت ايدج%4$s."

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "استيراد %1$s الإعدادات من تثبيت آخر من خلال لصقهم هنا والنقر فوق\"%2$s\"."

#: src/presenters/admin/sidebar-presenter.php:87 js/dist/new-settings.js:11
msgid "Learn SEO"
msgstr "تعلم SEO"

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:95 js/dist/new-settings.js:9
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "تريد أن تتعلم عن SEO من فريق عمل Yoast؟ تحقق من %1$s!"

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:68
msgid "Your %1$s settings:"
msgstr "إعدادات %1$s الخاصة بك:"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "إعدادات %s للاستيراد:"

#. translators: %1$s expands to Yoast SEO academy
#: src/presenters/admin/sidebar-presenter.php:104 js/dist/new-settings.js:13
msgid "Check out %1$s"
msgstr "إتمام الطلب %1$s"

#: src/presenters/admin/sidebar-presenter.php:97 js/dist/new-settings.js:11
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "لدينا دورات مجانية ودورات مميزة أيضًا عبر الإنترنت لتعلّم كل ما تحتاج معرفته حول SEO."

#: admin/metabox/class-metabox.php:431 js/dist/block-editor.js:464
#: js/dist/elementor.js:447 js/dist/new-settings.js:32
#: js/dist/new-settings.js:188 js/dist/structured-data-blocks.js:13
msgid "Schema"
msgstr "مخطط Schema"

#: admin/views/tabs/social/twitterbox.php:21
msgid "Twitter uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph metadata\" setting on the Facebook tab enabled if you want to optimize your site for Twitter."
msgstr "يستخدم Twitter البيانات الوصفية لـ Open Graph تمامًا مثل Facebook، لذا تأكد من تمكين إعداد \"إضافة البيانات الوصفية لـ Open Graph\" في علامة تبويب Facebook إذا كنت تريد تحسين موقعك على Twitter."

#: admin/admin-settings-changed-listener.php:81
msgid "Settings saved."
msgstr "تم حفظ الإعدادات."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:157
msgid "Please check the format of the Wikipedia URL you entered. %s"
msgstr "من فضلك يرجى التحقق من Wikipedia URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:142
msgid "Please check the format of the Pinterest URL you entered. %s"
msgstr "من فضلك يرجى التحقق من Pinterest URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:137
msgid "Please check the format of the MySpace URL you entered. %s"
msgstr "من فضلك يرجى التحقق من MySpace URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:122
msgid "Please check the format of the Instagram URL you entered. %s"
msgstr "من فضلك يرجى التحقق من Instagram URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:112
msgid "Please check the format of the Facebook Page URL you entered. %s"
msgstr "من فضلك يرجى التحقق من Facebook Page URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:127
msgid "Please check the format of the LinkedIn URL you entered. %s"
msgstr "من فضلك يرجى التحقق من LinkedIn URL الذي قمت بإدخاله. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:167
msgid "Please check the format of the YouTube URL you entered. %s"
msgstr "من فضلك يرجى التحقق من Youtube URL الذي قمت بإدخاله. %s"

#: admin/views/partial-notifications-template.php:45
msgid "Show this item."
msgstr "إظهار هذا العنصر."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:162
msgid "Yandex confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "يمكن أن تحتوي رموز التحقق من Yandex فقط على أحرف من A إلى F وأرقام وواصلات وشرطات سفلية. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:132
msgid "Bing confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "يمكن أن تحتوي رموز التحقق من Bing فقط على أحرف من A إلى F وأرقام وواصلات وشرطات سفلية. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:107
msgid "Baidu verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "يجب أن تحتوي رموز التحقق من Baidu على أحرف وأرقام وواصلات وشرطات سفلية فقط. %s"

#. translators: %s expands to an invalid Facebook App ID.
#: inc/options/class-wpseo-option.php:482
msgid "%s does not seem to be a valid Facebook App ID. Please correct."
msgstr "لا يبدو أن %s معرف تطبيق Facebook صالح. يرجى التصحيح."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:152
msgid "Twitter usernames can only contain letters, numbers, and underscores. %s"
msgstr "يمكن أن تحتوي أسماء مستخدمي Twitter فقط على أحرف وأرقام وشرطات سفلية. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:147
msgid "Pinterest confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "يمكن أن تحتوي رموز التحقق من Pinterest فقط على أحرف من A إلى F وأرقام وواصلات وشرطات سفلية. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:117
msgid "Google verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "يمكن أن تحتوي رموز التحقق من Google على أحرف وأرقام وواصلات وشرطات سفلية فقط. %s"

#: admin/views/partial-notifications-template.php:38
msgid "Hide this item."
msgstr "إخفاء هذا العنصر."

#. translators: %s: form value as submitted.
#: admin/class-yoast-input-validation.php:319
msgid "The submitted value was: %s"
msgstr "القيمة المرسلة كانت: %s"

#. translators: %d expands the amount of hidden problems.
#: admin/views/partial-notifications-errors.php:25
msgid "You have %d hidden problem:"
msgid_plural "You have %d hidden problems:"
msgstr[0] "لديك %d مشكلة مخفية:"
msgstr[1] "لديك %d مشكلة مخفية:"
msgstr[2] "لديك %d مشكلتان مخفيتان:"
msgstr[3] "لديك %d مشاكل مخفية:"
msgstr[4] "لديك %d مشكلة مخفية:"
msgstr[5] "لديك %d مشكلة مخفية:"

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "لديك %d تنبيه مخفي:"
msgstr[1] "لديك %d تنبيه مخفي:"
msgstr[2] "لديك %d تنبيهان مخفيان:"
msgstr[3] "لديك %d تنبيهات مخفية:"
msgstr[4] "لديك %d تنبيه مخفي:"
msgstr[5] "لديك %d تنبيه مخفي:"

#: src/helpers/score-icon-helper.php:66
msgid "Focus keyphrase not set"
msgstr "لم يتم تعيين تركيز العبارة الرئيسية."

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "يحتوي النموذج على %1$s خطأ. %2$s"
msgstr[1] "يحتوي النموذج على %1$s خطأ. %2$s"
msgstr[2] "يحتوي النموذج على %1$s خطأين. %2$s"
msgstr[3] "يحتوي النموذج على %1$s أخطاء. %2$s"
msgstr[4] "يحتوي النموذج على %1$s أخطاء. %2$s"
msgstr[5] "يحتوي النموذج على %1$s أخطاء. %2$s"

#. translators: %s expands to the extension title
#: admin/views/licenses.php:165 admin/views/licenses.php:270
msgid "Activate %s for your site on MyYoast"
msgstr "تفعيل %s من أجل موقعك على MyYoast"

#: admin/class-customizer.php:109
msgid "Show blog page in breadcrumbs"
msgstr "إظهار صفحة المدونة في مسارات التنقل (Breadcrumbs)"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the readability score.
#: admin/formatter/class-metabox-formatter.php:140
#: admin/formatter/class-metabox-formatter.php:147
#: admin/formatter/class-metabox-formatter.php:154
#: admin/formatter/class-metabox-formatter.php:161
msgid "%1$sReadability%2$s: %3$s"
msgstr "%1$sقابلية القراءة%2$s: %3$s"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the SEO score.
#: admin/formatter/class-metabox-formatter.php:110
#: admin/formatter/class-metabox-formatter.php:117
#: admin/formatter/class-metabox-formatter.php:124
#: admin/formatter/class-metabox-formatter.php:131
msgid "%1$sSEO%2$s: %3$s"
msgstr "%1$sSEO%2$s: %3$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "مقالات مع نتيجة SEO: %s"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:157 inc/class-wpseo-rank.php:162
#: inc/class-wpseo-rank.php:167 inc/class-wpseo-rank.php:172
#: inc/class-wpseo-rank.php:177
msgid "SEO: %s"
msgstr "SEO: %s"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:340
msgid "%s video tutorial"
msgstr "شرح فيديو %s"

#: inc/class-wpseo-rank.php:158
msgid "No Focus Keyphrase"
msgstr "لا توجد عبارة رئيسية مفتاحية"

#: inc/class-wpseo-rank.php:178
msgid "Post Noindexed"
msgstr "مقالة غير مفهرسة"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "لعرض أخطاء الزحف الحالية، %1$s يرجى زيارة Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "أوقفت Google واجهة برمجة التطبيقات الخاصة بأخطاء الزحف Crawl API. لذلك، قد لا يمكن عرض أي أخطاء crawl محتملة لديك هنا بعد الآن. %1$sقراءة بياننا حول هذا لمزيد من المعلومات%2$s."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:113
#: js/dist/new-settings.js:232
msgid "Personal info"
msgstr "معلومات شخصية"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:95
#: js/dist/first-time-configuration.js:5 js/dist/new-settings.js:224
#: js/dist/new-settings.js:231
msgid "Organization name"
msgstr "اسم المنظمة"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:59
msgid "Choose whether the site represents an organization or a person."
msgstr "اختر ما إذا كان الموقع يمثل منظمة أو شخصًا."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:66
msgid "Organization or person"
msgstr "منظمة أو شخص"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:63
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:69
#: src/integrations/admin/first-time-configuration-integration.php:486
#: src/integrations/admin/first-time-configuration-integration.php:499
#: js/dist/new-settings.js:230
msgid "Organization"
msgstr "منظمة / شركة"

#: admin/views/tabs/metas/general.php:67
msgid "Knowledge Graph & Schema.org"
msgstr "بيانات Knowledge Graph و Schema.org"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:60
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "لقد قمت مسبقًا بتعيين موقعك على الويب لتمثيل شخص ما. لقد قمنا بتحسين وظائفنا المتعلقة بالمخطط والرسم البياني المعرفي، لذا يجب عليك الدخول وإكمال %1$sهذه الإعدادات%2$s."

#: src/generators/schema/article.php:136
msgid "Uncategorized"
msgstr "غير مصنف"

#: admin/class-admin.php:297
msgid "(if one exists)"
msgstr "(إن وجد)"

#: admin/class-admin.php:297
msgid "Wikipedia page about you"
msgstr "صفحة Wikipedia عنك"

#: admin/class-admin.php:294
msgid "Tumblr profile URL"
msgstr "رابط الملف الشخصي على Tumblr"

#: admin/class-admin.php:296
msgid "YouTube profile URL"
msgstr "رابط الملف الشخصي على YouTube"

#: admin/class-admin.php:293
msgid "SoundCloud profile URL"
msgstr "رابط الملف الشخصي على SoundCloud"

#: admin/class-admin.php:291
msgid "MySpace profile URL"
msgstr "رابط الملف الشخصي على MySpace"

#: admin/class-admin.php:290
msgid "LinkedIn profile URL"
msgstr "رابط الملف الشخصي على LinkedIn"

#: admin/class-admin.php:289
msgid "Instagram profile URL"
msgstr "رابط الملف الشخصي على Instagram"

#: admin/class-admin.php:292
msgid "Pinterest profile URL"
msgstr "رابط الملف الشخصي على Pinterest"

#: admin/views/tabs/social/accounts.php:20
msgid "If a Wikipedia page for you or your organization exists, add it too."
msgstr "إذا كانت هناك صفحة موجودة لك على Wikipedia أو لمؤسستك، فأضفها أيضًا."

#: inc/class-my-yoast-api-request.php:141
msgid "No JSON object was returned."
msgstr "لم يتم إرجاع أي كائن JSON."

#: src/integrations/admin/link-count-columns-integration.php:138
msgid "Outgoing internal links"
msgstr "الروابط الداخلية الصادرة"

#: src/integrations/admin/link-count-columns-integration.php:145
msgid "Received internal links"
msgstr "الروابط الداخلية المستلمة"

#: admin/class-meta-columns.php:111 js/dist/block-editor.js:347
#: js/dist/classic-editor.js:343 js/dist/dashboard-widget.js:24
#: js/dist/editor-modules.js:289 js/dist/elementor.js:347
msgid "Keyphrase"
msgstr "العبارة الرئيسية"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:88
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "لكي تعمل هذه الميزة، تحتاج %1$s إلى إنشاء جدول في قاعدة البيانات الخاصة بك. لم نتمكن من إنشاء هذا الجدول تلقائيًا."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "لا يمكن الحصول على الحجم %1$s لأسباب غير معروفة."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "لا يمكن الحصول على الحجم %1$s لأنه مستضاف خارجيًا."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:394
msgid "Page %s"
msgstr "صفحة %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "الطريقة %1$s() غير موجودة في الفئة%2$s"

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:219
msgid "With %s, you can easily create such redirects."
msgstr "باستخدام %s ، يمكنك بسهولة إنشاء عمليات إعادة التوجيه."

#: admin/views/licenses.php:80
msgid "Improve sharing on Facebook and Pinterest"
msgstr "تحسين المشاركة على Facebook و Pinterest"

#: admin/import/class-import-settings.php:79
msgid "No settings found."
msgstr "لم يتم العثور على الإعدادات."

#: admin/class-export.php:50
msgid "You do not have the required rights to export settings."
msgstr "ليس لديك الحقوق المطلوبة لتصدير الإعدادات."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:25
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "يجب أن تقوم بتصدير إعدادات %1$s الخاصة بك من هنا، لنسخهم في موقع إلكتروني آخر."

#. translators: %1$s expands to Import settings
#: admin/class-export.php:57
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "نسخ كل هذه الإعدادات إلى علامة التبويب %1$s لموقع آخر وأنقر على \"%1$s\" هناك."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:89
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "هذه إعدادات إضافة %1$s بواسطة %2$s"

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "استيراد الإعدادات المدعومة فقط على الخوادم التي تقوم بتشغيل PHP 5.3 أو أعلى."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:84 js/dist/new-settings.js:237
msgid "Upgrade to %s"
msgstr "الترقية إلى %s"

#: admin/class-admin-init.php:324
msgid "WARNING:"
msgstr "تحذير"

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:327
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "يمكن أن يؤثر تغيير إعدادات الروابط الثابتة بشكل خطير على رؤية محرك البحث الخاص بك. من المفترض ألا يتم %1$ss مطلقًا %2$s على موقع ويب مباشر."

#: admin/class-admin-init.php:333
msgid "Learn about why permalinks are important for SEO."
msgstr "تعرف على سبب أهمية الروابط الثابتة لSEO."

#: inc/class-wpseo-admin-bar-menu.php:288
msgid "Google Ads"
msgstr "إعلانات Google"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:28
msgid "Not showing the date archives in the search results technically means those will have a %1$s robots meta. %2$sMore info on the search results settings%3$s."
msgstr "عدم عرض أرشيفات التاريخ في نتائج البحث يعني تقنيًا أن تلك المحفوظات تحتوي على %1$s للـ robots meta. %2$sالمزيد من المعلومات حول إعدادات نتائج البحث%3$s."

#: inc/class-wpseo-admin-bar-menu.php:353
msgid "Check Keyphrase Density"
msgstr "تحقق من كثافة العبارة الرئيسية"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:291
msgid "Allow Control"
msgstr "السماح بالتحكم"

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:293
msgid "Disable"
msgstr "تعطيل"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "تسمح لك علامة التبويب هذه بتعطيل ميزات %s بشكل انتقائي لجميع المواقع في الشبكة. بشكل افتراضي، يتم تمكين جميع الميزات، مما يسمح لمسؤولي الموقع باختيار ما إذا كانوا يريدون تبديل الميزة أو إيقاف تشغيلها لموقعهم. عند تعطيل الميزة هنا، لن يتمكن مسؤولو الموقع من استخدام هذه الميزة على الإطلاق."

#: admin/formatter/class-metabox-formatter.php:69
msgid "Keyphrase:"
msgstr "العبارة الرئيسية:"

#: admin/formatter/class-metabox-formatter.php:70
msgid "Remove keyphrase"
msgstr "إزالة العبارة الرئيسية"

#: admin/views/licenses.php:128
msgid "Synonyms & related keyphrases"
msgstr "المرادفات والعبارات الرئيسية ذات الصلة"

#: admin/views/licenses.php:129
msgid "optimize a single post for synonyms and related keyphrases."
msgstr "تحسين منشور واحد للمرادفات والعبارات الرئيسية ذات الصلة."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s مطلوب لميزة تبديل الوسيطة."

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Multiple keyphrases"
msgstr "عبارات رئيسية متعددة"

#: admin/class-yoast-form.php:1041 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled by the network admin."
msgstr "تم تعطيل هذه الميزة من قبل مسؤول الشبكة."

#: admin/class-meta-columns.php:166
msgid "Focus keyphrase not set."
msgstr "لم يتم تعيين تركيز العبارة الرئيسية."

#: admin/class-yoast-form.php:749 admin/metabox/class-metabox.php:654
#: admin/taxonomy/class-taxonomy-fields-presenter.php:133
msgid "Clear Image"
msgstr "مسح الصورة"

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/class-premium-popup.php:81
#: admin/class-premium-upsell-admin-block.php:67
#: admin/formatter/class-metabox-formatter.php:325
#: admin/watchers/class-slug-change-watcher.php:223
#: src/deprecated/admin/add-keyword-modal.php:47
#: src/deprecated/admin/keyword-synonyms-modal.php:47
#: src/deprecated/admin/multiple-keywords-modal.php:47
#: src/presenters/admin/sidebar-presenter.php:48
#: src/presenters/admin/sidebar-presenter.php:61 js/dist/block-editor.js:382
#: js/dist/classic-editor.js:378 js/dist/elementor.js:382
#: js/dist/externals-components.js:15 js/dist/externals-components.js:77
#: js/dist/externals-components.js:95 js/dist/externals-components.js:97
#: js/dist/externals-components.js:99 js/dist/externals-components.js:101
#: js/dist/new-settings.js:7 js/dist/new-settings.js:239
msgid "Get %s"
msgstr "احصل على %s"

#: inc/options/class-wpseo-option-titles.php:967
msgid "Colon"
msgstr "نقطتان"

#: inc/class-wpseo-admin-bar-menu.php:680
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "هناك إشعار جديد."
msgstr[1] "هناك إشعار جديد."
msgstr[2] "هناك إشعاران جديدان."
msgstr[3] "هناك إشعارات جديدة."
msgstr[4] "هناك إشعارات جديدة."
msgstr[5] "هناك إشعارات جديدة."

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:88
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "يدير كل من %1$s و%2$s SEO لموقعك. يعد تشغيل إضافيين لSEO في نفس الوقت ضارًا."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:62
msgid "%1$s Structured Data Blocks"
msgstr "%1$s مكونات البيانات المنظمة"

#: inc/class-wpseo-admin-bar-menu.php:283
msgid "Keyword research training"
msgstr "تدريب البحث عن الكلمات الرئيسية"

#. translators: %s expands to a 'Yoast SEO Premium' text linked to the
#. yoast.com website.
#: admin/formatter/class-metabox-formatter.php:313
#: src/deprecated/admin/add-keyword-modal.php:35
#: src/deprecated/admin/keyword-synonyms-modal.php:35
#: src/deprecated/admin/multiple-keywords-modal.php:35
#: js/dist/block-editor.js:374 js/dist/classic-editor.js:370
#: js/dist/elementor.js:374 js/dist/externals-components.js:7
msgid "Great news: you can, with %s!"
msgstr "أخبار رائعة: يمكنك ذلك مع %s!"

#: inc/class-wpseo-admin-bar-menu.php:348
msgid "Check links to this URL"
msgstr "تحقق من الروابط إلى عنوان URL هذا"

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:198
msgid "Error: %s"
msgstr "خطأ: %s"

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "تم الحذف"

#: admin/pages/network.php:36
msgid "Restore Site"
msgstr "استعادة الموقع"

#: admin/class-yoast-network-admin.php:266
msgid "You are not allowed to perform this action."
msgstr "غير مسموح لك بتنفيذ هذا الإجراء."

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:196
msgid "Success: %s"
msgstr "النجاح: %s"

#: admin/class-yoast-network-admin.php:110
msgid "You are not allowed to modify unregistered network settings."
msgstr "لا يسمح لك بتعديل إعدادات الشبكة غير المسجلة."

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "إعدادات الشبكة. "

#: src/deprecated/admin/multiple-keywords-modal.php:32
msgid "Would you like to add another keyphrase?"
msgstr "هل ترغب في إضافة عبارة رئيسية أخرى؟"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:158
msgid "Site with ID %d not found."
msgstr "الصفحة ذات المعرف %d غير موجود."

#: admin/class-yoast-network-admin.php:149
msgid "No site has been selected to restore."
msgstr "لم يتم اختيار موقع للإستعادة."

#: inc/class-wpseo-replace-vars.php:1472
msgid "The site's tagline"
msgstr "سطر الوصف للموقع"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:150
msgid "Not all required fields are given. Missing field %1$s"
msgstr "لم يتم توفير جميع الحقول المطلوبة. الحقل مفقود %1$s"

#: admin/formatter/class-metabox-formatter.php:310
#: src/deprecated/admin/add-keyword-modal.php:32
msgid "Would you like to add more than one keyphrase?"
msgstr "هل ترغب في إضافة أكثر من عبارة رئيسية واحدة؟"

#: src/deprecated/admin/keyword-synonyms-modal.php:32
#: js/dist/externals-components.js:97
msgid "Would you like to add keyphrase synonyms?"
msgstr "هل ترغب في إضافة مرادفات للعبارة رئيسية؟"

#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:10
msgid "You haven't set a Shop page in your WooCommerce settings. Please do this first."
msgstr "لم تقم بتعيين صفحة متجر في إعدادات ووكومرس الخاصة بك. الرجاء القيام بهذا أولاً."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to a
#. closing anchor tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:15
#: js/dist/new-settings.js:21
msgid "You can edit the SEO metadata for this custom type on the %1$sShop page%2$s."
msgstr "يمكنك تحرير بيانات meta-data الوصفية SEO لهذا النوع المخصص في %1$sصفحة المتجر%2$s."

#: inc/class-wpseo-replace-vars.php:1486 js/dist/externals-redux.js:1
msgid "Current year"
msgstr "السنة الحالية"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Tag"
msgstr "وسم"

#: inc/class-wpseo-replace-vars.php:1512 js/dist/externals-redux.js:1
msgid "ID"
msgstr "المُعرّف"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Page number"
msgstr "رقم الصفحة"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "عبارة البحث"

#: inc/class-wpseo-replace-vars.php:1471 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Site title"
msgstr "عنوان الموقع"

#: inc/class-wpseo-replace-vars.php:1467 js/dist/externals-redux.js:1
msgid "Date"
msgstr "التاريخ"

#: inc/class-wpseo-replace-vars.php:1514
msgid "User description"
msgstr "وصف المستخدم"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Pagenumber"
msgstr "رقم الصفحة"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Modified"
msgstr "تم التعديل"

#: inc/class-wpseo-replace-vars.php:1484 js/dist/externals-redux.js:1
msgid "Separator"
msgstr "الفاصل"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/externals-redux.js:1
msgid "Category"
msgstr "تصنيف"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Archive title"
msgstr "عنوان الأرشيف"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Post type (singular)"
msgstr "نوع المشاركة (المفرد)"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Term404"
msgstr "مصطلح ٤٠٤"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Post type (plural)"
msgstr "نوع المشاركة (جمع)"

#: inc/class-wpseo-replace-vars.php:1473 js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "المقتطف"

#. translators: %1$s expands to the translated name of the post type.
#. translators: 1: term label
#: admin/watchers/class-slug-change-watcher.php:84
#: admin/watchers/class-slug-change-watcher.php:104
msgid "You just deleted a %1$s."
msgstr "لقد حذفت للتو %1$s."

#: inc/class-wpseo-replace-vars.php:1478 js/dist/externals-redux.js:1
msgid "Category description"
msgstr "وصف التصنيفات"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Caption"
msgstr "كلمات توضيحية"

#: inc/class-wpseo-replace-vars.php:1523
msgid "description (custom taxonomy)"
msgstr "الوصف (التصنيف المخصص)"

#: inc/class-wpseo-replace-vars.php:1522
msgid "(custom taxonomy)"
msgstr "(التصنيف المخصص)"

#: inc/class-wpseo-replace-vars.php:1521
msgid "(custom field)"
msgstr "(حقل مخصص)"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Pagetotal"
msgstr "مجموع الصفحة"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Term title"
msgstr "عنوان العنصر"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/externals-redux.js:1
msgid "Term description"
msgstr "وصف العنصر"

#: inc/class-wpseo-replace-vars.php:1479 js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "وصف الوسم"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "مقتطفات فقط"

#: admin/watchers/class-slug-change-watcher.php:217
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "يجب عليك إنشاء إعادة توجيه للتأكد أن زائري موقعك لا يحصلون على خطأ 404 عند النقرعلى URL لا يعمل."

#: admin/views/tabs/metas/post-types.php:25
msgid "The settings on this page allow you to specify what the default search appearance should be for any type of content you have. You can choose which content types appear in search results and what their default description should be."
msgstr "تسمح لك الإعدادات في هذه الصفحة بتحديد مظهر البحث الافتراضي لأي نوع من المحتوى لديك. يمكنك اختيار أنواع المحتوى التي تظهر في نتائج البحث والوصف الافتراضي الذي يجب أن يكون."

#. translators: %s expands to the post type name.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:22
msgid "Settings for %s archive"
msgstr "إعدادات الأرشيف %s."

#: inc/class-wpseo-replace-vars.php:1469 js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "العنوان الأصل"

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:65
msgid "You just trashed a %1$s."
msgstr "لقد نقلت %1$s إلى سلة المهملات."

#: admin/watchers/class-slug-change-watcher.php:213
msgid "Make sure you don't miss out on traffic!"
msgstr "تأكد من عدم تفويت حركة الزيارات!"

#: admin/watchers/class-slug-change-watcher.php:216
msgid "Search engines and other websites can still send traffic to your deleted post."
msgstr "لا يزال بإمكان محركات البحث والمواقع الأخرى إرسال حركة زيارات إلى مقالتك المحذوفة."

#: inc/class-wpseo-replace-vars.php:1477 js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "التصنيف الأساسي"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Tagline"
msgstr "سطر الوصف"

#. translators: %1$s expands to a link start tag to the Baidu Webmaster Tools
#. site add page, %2$s is the link closing tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:44
msgid "Get your Baidu verification code in %1$sBaidu Webmaster Tools%2$s."
msgstr "احصل على رمز التحقق الخاص بـ Baidu في %1$sBaidu Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:40
msgid "Baidu verification code"
msgstr "رمز التحقق لـ Baidu"

#: admin/class-bulk-editor-list-table.php:406
msgid "Filter by content type"
msgstr "تصفية حسب نوع المحتوى"

#: admin/class-bulk-editor-list-table.php:993
msgid "Content Type"
msgstr "نوع المحتوى"

#: admin/class-bulk-editor-list-table.php:389
msgid "Show All Content Types"
msgstr "إظهار جميع أنواع المحتوى"

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:257
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "تستخدم وظيفة المستورد %s جداول قاعدة بيانات مؤقتة. يبدو أن تثبيت ووردبريس الخاص بك ليس لديه القدرة على القيام بذلك ، يرجى استشارة مزود الاستضافة الخاص بك."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "فشل ترتيب البيانات %s."

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "تم استبدال العنوان العادي لأرشيف تم إنشاؤه بواسطة ووردبريس"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "يرجى عمل نسخة احتياطية من قاعدة البيانات الخاصة بك قبل البدء في هذه العملية."

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "%s البيانات التي تم استيرادها بنجاح."

#: admin/views/tabs/tool/import-seo.php:108
msgid "Step 5: Clean up"
msgstr "الخطوة الخامسة: الترتيب"

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "الإضافة "

#: admin/views/tabs/tool/import-seo.php:119
msgid "Clean"
msgstr "ترتيب"

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "الخطوة ٢: الاستيراد"

#: admin/views/tabs/tool/import-seo.php:110
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "بمجرد التأكد من أن موقعك على ما يرام ، يمكنك التنظيف. سيؤدي هذا إلى إزالة كافة البيانات الأصلية."

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "تم العثور على بيانات %s."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "لم يتم العثور على بيانات %s."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "الخطوة ١: إنشاء نسخة احتياطية"

#: admin/views/tabs/tool/import-seo.php:87
msgid "Step 3: Check your data"
msgstr "الخطوة ٣: التحقق من بياناتك"

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "لقد اكتشفنا بيانات من واحد أو أكثر من إضافات SEO على موقعك. يرجى اتباع الخطوات التالية لاستيراد تلك البيانات:"

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "سيؤدي هذا إلى استيراد بيانات الميتا الخاصة بالمقالة مثل عناوين وأوصاف SEO إلى %1$s  بيانات الميتا الخاصة بك. سيفعل ذلك فقط في حالة عدم وجود %1$s بيانات ميتا حتى الآن. ستبقى البيانات الأصلية في مكانها."

#: admin/views/tabs/tool/import-seo.php:89
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "يرجى التحقق من مقالتك وصفحاتك ومعرفة ما إذا تم استيرادبيانات الميتا بنجاح."

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "المقالات التي لا يجب أن تظهر في نتائج البحث"

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "تم إزالة بيانات %s بنجاح."

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "لم تكتشف %s أي بيانات لإضافة من الإضافات التي يمكنها الاستيراد منها."

#: admin/class-premium-upsell-admin-block.php:58 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "24/7 email support"
msgstr "24/7 دعم عبر البريد الإلكتروني"

#: admin/views/tabs/metas/paper-content/media-content.php:15
msgid "We recommend you set this to Yes."
msgstr "نوصي بتعيين هذا على نعم."

#: admin/views/tabs/metas/paper-content/rss-content.php:19
msgid "Learn more about the available variables"
msgstr "معرفة المزيد عن المتغيرات المتاحة"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:14
msgid "Do not allow search engines to show %s in search results."
msgstr "لا تسمح لمحركات البحث بإظهار %s في نتائج البحث."

#. translators: %s expands to <code>/category/</code>
#: admin/views/tabs/metas/taxonomies/category-url.php:17
msgid "Category URLs in WordPress contain a prefix, usually %s, this feature removes that prefix, for categories only."
msgstr "تحتوي عناوين URL الخاصة بالتصنيفات في ووربريس على بادئة، عادةً %s، هذه الميزة تزيل تلك البادئة ، للتصنيفات فقط"

#: admin/views/tabs/metas/taxonomies.php:54
msgid "Category URLs"
msgstr "تصنيفات URLs"

#: admin/views/tabs/metas/taxonomies/category-url.php:14
msgid "Help on the category prefix setting"
msgstr "تعليمات حول إعداد بادئة التصنيف"

#: admin/views/tabs/metas/paper-content/media-content.php:25
msgid "Redirect attachment URLs to the attachment itself?"
msgstr "هل تريد إعادة توجيه عناوين URL للمرفقات إلى المرفق نفسه؟"

#: admin/views/tabs/social/accounts.php:18
msgid "Learn more about your social profiles settings"
msgstr "تعرف على المزيد حول إعدادات ملفات التعريف الاجتماعية الخاصة بك"

#: admin/views/tabs/metas/media.php:15
msgid "Media & attachment URLs"
msgstr "روابط URLs الخاصة بالوسائط والمرفقات"

#: admin/views/tabs/metas/media.php:21
msgid "When you upload media (an image or video for example) to WordPress, it doesn't just save the media, it creates an attachment URL for it. These attachment pages are quite empty: they contain the media item and maybe a title if you entered one. Because of that, if you never use these attachment URLs, it's better to disable them, and redirect them to the media item itself."
msgstr "عند رفع الوسائط (صورة أو مقطع فيديو على سبيل المثال) إلى ووردبريس، فإنها لا تقوم فقط بحفظ الوسائط، بل تنشئ رابط مرفقًا لها. صفحات المرفقات فارغة تمامًا: فهي تحتوي على عنصر الوسائط وربما عنوانًا إذا قمت بإدخاله. لهذا السبب، إذا لم تستخدم روابط المرفقات هذه مطلقًا، فمن الأفضل تعطيلها وإعادة توجيهها إلى عنصر الوسائط نفسه."

#: admin/views/tabs/social/accounts.php:19
msgid "To let search engines know which social profiles are associated to this site, enter your site social profiles data below."
msgstr "للسماح لمحركات البحث بمعرفة ملفات التعريف الاجتماعية المرتبطة بهذا الموقع، أدخل بيانات ملفات التعريف الاجتماعية لموقعك أدناه."

#. translators: %s expands to the post type's name with a link to the archive.
#: admin/views/tabs/metas/paper-content/post-type-content.php:52
msgid "the archive for %s"
msgstr "الأرشيف لـ %s"

#: admin/views/user-profile.php:15
msgid "this author's archives"
msgstr "أرشيف هذا الكاتب"

#: admin/views/tabs/metas/rss.php:20
msgid "Learn more about the RSS feed setting"
msgstr "معرفة المزيد حول إعداد RSS feed"

#: admin/views/tabs/metas/taxonomies/category-url.php:24
msgid "Remove the categories prefix?"
msgstr "إزالة بادئة التصنيفات"

#: admin/views/tabs/metas/paper-content/special-pages.php:12
msgid "Learn more about the special pages setting"
msgstr "تعرف على المزيد حول إعداد الصفحات الخاصة"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:37
msgid "date archives"
msgstr "أرشيف التاريخ"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:25
msgid "Help on the date archives search results setting"
msgstr "مساعدة في إعداد نتائج بحث ارشيف التاريخ"

#: admin/views/tabs/metas/paper-content/general/homepage.php:13
msgid "This is what shows in the search results when people find your homepage. This means this is probably what they see when they search for your brand name."
msgstr "هذا ما يظهر في نتائج البحث عندما يجد الأشخاص صفحتك الرئيسية. هذا يعني أن هذا ربما يكون ما يرونه عندما يبحثون عن اسم علامتك التجارية."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:62
msgid "archives for authors without posts"
msgstr "أرشيف للكتّاب بدون مقالات."

#: admin/views/tabs/metas/media.php:20
msgid "Learn more about the Media and attachment URLs setting"
msgstr "معرفة المزيد حول إعداد روابط الوسائط والمرفقات"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:53
msgid "Not showing the archives for authors without posts in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "عدم عرض أرشيف الكتّاب الذين ليس لديهم مقالات في نتائج البحث، تقنيًا يعني أن هؤلاء سيكون لديهم %1$s للـ robots meta وسيتم استبعادهم من خرائط XML sitemaps. %2$sلمزيد من المعلومات حول إعدادات نتائج البحث%3$s."

#: admin/views/tabs/metas/archives/help.php:32
msgid "Archives settings help"
msgstr "مساعدة إعدادات الأرشيف"

#: admin/views/tabs/metas/archives/help.php:26
msgid "Learn more about the archives setting"
msgstr "تعلم المزيد حول إعدادات الأرشفة"

#: admin/views/tabs/dashboard/webmaster-tools.php:55
msgid "Bing verification code"
msgstr "رمز التحقق لـ Bing"

#: admin/views/tabs/dashboard/webmaster-tools.php:65
msgid "Google verification code"
msgstr "رمز التحقق لـ Google"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:59
msgid "Get your Bing verification code in %1$sBing Webmaster Tools%2$s."
msgstr "احصل على رمز التحقق الخاص بـ Bing في %1$sBing Webmaster Tools%2$s. "

#: admin/views/tabs/dashboard/webmaster-tools.php:75
msgid "Yandex verification code"
msgstr "رمز التحقق لـ Yandex "

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:79
msgid "Get your Yandex verification code in %1$sYandex Webmaster Tools%2$s."
msgstr "احصل على رمز تحقق Yandex الخاص بك في %1$sأدوات مشرفي المواقع من Yandex%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:69
msgid "Get your Google verification code in %1$sGoogle Search Console%2$s."
msgstr "احصل على رمز التحقق من Google في %1$sGoogle Search Console%2$s."

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:30
msgid "Not showing the archive for authors in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "عدم عرض الأرشيف للكتّاب في نتائج البحث، تقنيًا يعني أن هؤلاء سيكون لديهم %1$s للـ robots meta وسيتم استبعادهم من خرائط XML sitemaps. %2$sللمزيد من المعلومات حول إعدادات نتائج البحث%3$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:50
msgid "Help on the authors without posts archive search results setting"
msgstr "إعدادات نتائج البحث لمساعدة الكتّاب بدون مقالات مؤرشفة"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:27
msgid "Help on the author archives search results setting"
msgstr "إعدادات نتائج البحث لمساعدة أرشيف الكاتب"

#: admin/views/tabs/dashboard/webmaster-tools.php:19
msgid "You can use the boxes below to verify with the different Webmaster Tools. This feature will add a verification meta tag on your home page. Follow the links to the different Webmaster Tools and look for instructions for the meta tag verification method to get the verification code. If your site is already verified, you can just forget about these."
msgstr "يمكنك استخدام المربعات أدناه للتحقق باستخدام أدوات مشرفي المواقع المختلفة. ستضيف هذه الميزة التحقق من وسم الميتا (meta tag) على صفحتك الرئيسية. اتبع الارتباطات المؤدية إلى أدوات مشرفي المواقع المختلفة وابحث عن إرشادات حول طريقة التحقق من وسم الميتا (meta tag) للحصول على رمز التحقق. إذا تم التحقق من موقعك بالفعل، فيمكنك أن تنسى هذه الأمور."

#: admin/class-yoast-form.php:937 admin/class-yoast-form.php:977
#: admin/metabox/class-metabox.php:212
#: admin/views/tabs/dashboard/features.php:106
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:13
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:13
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "On"
msgstr "تشغيل"

#: admin/class-yoast-form.php:938 admin/class-yoast-form.php:978
#: admin/metabox/class-metabox.php:211
#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/tabs/dashboard/features.php:107
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:14
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:14
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "Off"
msgstr "إيقاف"

#: admin/views/tabs/dashboard/webmaster-tools.php:18
msgid "Learn more about the Webmaster Tools verification"
msgstr "تعرف على المزيد حول التحقق من خلال أدوات مشرف الموقع"

#. translators: %s expands to a feature's name
#. translators: %s expands to an integration's name
#: admin/views/tabs/dashboard/features.php:55
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "مساعدة في: %s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/features.php:27
msgid "%1$s comes with a lot of features. You can enable / disable some of them below. Clicking the question mark gives more information about the feature."
msgstr "تأتي %1$s بالعديد من المميزات. يمكنك تمكين / تعطيل بعضها أدناه. يؤدي النقر فوق علامة الاستفهام إلى توفير المزيد من المعلومات حول الميزة."

#: admin/views/class-view-utils.php:56
msgid "Help on this search results setting"
msgstr "مساعدة في إعداد نتائج لهذا البحث"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:20
msgid "See who contributed to %1$s."
msgstr "تعرف على من ساهم في %1$s."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "انظر خريطة موقع XML."

#: admin/views/class-yoast-feature-toggles.php:145
msgid "Read why XML Sitemaps are important for your site."
msgstr "اقرأ لماذا تعتبر خرائط مواقع XML مهمة لموقعك."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:144
msgid "Enable the XML sitemaps that %s generates."
msgstr "تمكين خرائط مواقع XML التي تنشئها إضافة %s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:50
msgid "Not showing the archive for %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "عدم عرض أرشيف %1$s في نتائج البحث يعني تقنيًا أن هؤلاء سيكون لديهم %2$s للـ robots meta وسيتم استبعادهم من خرائط XML sitemaps. %3$sالمزيد من المعلومات حول إعدادات نتائج البحث%4$s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:46
msgid "Not showing %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "عدم عرض %1$s في نتائج البحث يعني تقنيًا أن هؤلاء سيكون لديهم %2$s للـ robots meta وسيتم استبعادهم من خرائط XML sitemaps. %3$sالمزيد من المعلومات حول إعدادات نتائج البحث%4$s."

#: admin/pages/metas.php:20
msgid "Media"
msgstr "الوسائط"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:948
msgid "Show %s in search results?"
msgstr "عرض %s في نتائج البحث؟"

#: admin/pages/metas.php:19
msgid "Content Types"
msgstr "أنواع المحتوى"

#. translators: %s expands to the post type name.
#. Translators: %s translates to the Post Label in singular form
#: admin/metabox/class-metabox.php:177 js/dist/block-editor.js:408
#: js/dist/classic-editor.js:404 js/dist/elementor.js:408
msgid "Allow search engines to show this %s in search results?"
msgstr "السماح لمحركات البحث بإظهار %s في نتائج البحث؟"

#: admin/menu/class-admin-menu.php:90
#: src/presenters/meta-description-presenter.php:37
msgid "Search Appearance"
msgstr "مظهر البحث"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:182
msgid "Default for %2$s, currently: %1$s"
msgstr "افتراضي لـ %2$s، حاليًا: %1$s"

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:187
msgid "Should search engines follow links on this %1$s?"
msgstr "هل يجب أن تتبع محركات البحث الروابط الموجودة على هذه ال%1$s؟"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:80
msgid "Toggle %1$s's XML Sitemap"
msgstr "تبديل خرائط %1$s XML Sitemap"

#: admin/views/tabs/social/twitterbox.php:27
msgid "Enable this feature if you want Twitter to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "قم بتمكين هذه الميزة إذا كنت تريد أن يعرض التويتر معاينة بالصور مع مقتطفات نصية عند مشاركة رابط إلى موقعك."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "تعلم كيف يمكن لتحليل SEO أن يساعدك في الترتيب."

#: admin/formatter/class-metabox-formatter.php:201
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Good results"
msgstr "نتائج جيدة"

#: admin/views/licenses.php:35
msgid "Get better search results in local search"
msgstr "الحصول على نتائج بحث أفضل في البحث المحلي"

#. translators: %s expands to WordProof
#: src/integrations/third-party/wordproof-integration-toggle.php:90
msgid "Read more about how %s works."
msgstr "اقرأ المزيد حول كيفية %s عمل."

#. translators: %1$s expands to WooCommerce
#: admin/views/licenses.php:38
msgid "Allow customers to pick up their %s order locally"
msgstr "السماح للزبائن بأخذ %s طلباتهم محليًّا"

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:213
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "يقدم تحليل قابلية القراءة اقتراحات لتحسين هيكل وأسلوب النص الخاص بك."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:104 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "SEO analysis"
msgstr "تحليل SEO"

#: admin/views/class-yoast-feature-toggles.php:77 js/dist/new-settings.js:213
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "يقدم تحليل SEO اقتراحات لتحسين SEO للنص الخاص بك."

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "اكتشف سبب أهمية قابلية القراءة بالنسبة إلى SEO."

#: admin/views/tabs/social/facebook.php:25
msgid "Enable this feature if you want Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "قم بتمكين هذه الميزة إذا كنت تريد أن يعرض Facebook ووسائل التواصل الاجتماعي الأخرى معاينة بالصور مع مقتطفات نصية عند مشاركة رابط إلى موقعك."

#. translators: %s: 'Semrush'
#. translators: %s: Zapier.
#. translators: %s: Algolia.
#. translators: %s: 'Wincher'
#. translators: %s expands to WordProof
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
#: admin/views/class-yoast-integration-toggles.php:96
#: src/integrations/third-party/wincher.php:73
#: src/integrations/third-party/wordproof-integration-toggle.php:82
msgid "%s integration"
msgstr "دمج %s"

#: admin/views/class-yoast-feature-toggles.php:108
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "اكتشف كيف يمكن أن يساعدك المحتوى الأساسي في تحسين بنية موقعك."

#: admin/views/class-yoast-feature-toggles.php:107 js/dist/new-settings.js:215
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "تتيح لك ميزة المحتوى الأساسي تحديد المحتوى الأساسي وتصفيته على موقعك."

#: admin/views/class-yoast-feature-toggles.php:116
msgid "Find out how the text link counter can enhance your SEO."
msgstr "اكتشف كيف يمكن لعداد الروابط النصية تحسين SEO لديك."

#: admin/views/class-yoast-feature-toggles.php:115 js/dist/new-settings.js:215
msgid "The text link counter helps you improve your site structure."
msgstr "يساعدك عداد الروابط النصية على تحسين بنية موقعك."

#. Author URI of the plugin
msgid "https://yoa.st/1uk"
msgstr "‫https://yoa.st/1uk"

#. Plugin URI of the plugin
msgid "https://yoa.st/1uj"
msgstr "‫https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:124
msgid "Latest blog posts on %1$s"
msgstr "أحدث مقالات المدونة على %1$s"

#: admin/formatter/class-metabox-formatter.php:203
#: js/dist/externals/analysisReport.js:17
msgid "Remove highlight from the text"
msgstr "إزالة التمييز من النص"

#: admin/formatter/class-metabox-formatter.php:197
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Errors"
msgstr "أخطاء"

#: admin/formatter/class-metabox-formatter.php:200
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Considerations"
msgstr "الاعتبارات"

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "First-time SEO configuration"
msgstr "تثبيت SEO لأول مرة"

#: admin/formatter/class-metabox-formatter.php:202
#: js/dist/externals/analysisReport.js:17
msgid "Highlight this result in the text"
msgstr "تمييز هذه النتيجة في النص"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "حفظ التغييرات إلى %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "تم تحديث %s"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "إنشاء ملف %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "تعديل المحتوى الخاص بك %s:"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "ملف %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "لا يمكنك تعديل ملف %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "لا يمكنك إنشاء ملف %s."

#. translators: %1$s expands to the dependency name.
#: admin/class-suggested-plugins.php:136
msgid "More information about %1$s"
msgstr "للمزيد من المعلومات عن %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "معالج التكوين القديم"

#: admin/class-admin-utils.php:78 admin/class-premium-popup.php:82
#: admin/class-premium-upsell-admin-block.php:68 admin/class-yoast-form.php:912
#: admin/formatter/class-metabox-formatter.php:205
#: admin/formatter/class-metabox-formatter.php:329 admin/views/licenses.php:97
#: admin/watchers/class-slug-change-watcher.php:224
#: src/deprecated/admin/add-keyword-modal.php:51
#: src/deprecated/admin/keyword-synonyms-modal.php:51
#: src/deprecated/admin/multiple-keywords-modal.php:51
#: src/integrations/admin/workouts-integration.php:210
#: src/integrations/admin/workouts-integration.php:239
#: src/presenters/admin/help-link-presenter.php:75 js/dist/block-editor.js:468
#: js/dist/externals/componentsNew.js:136 js/dist/externals/helpers.js:18
#: js/dist/indexables-page.js:35 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:5 js/dist/integrations-page.js:13
#: js/dist/integrations-page.js:15
msgid "(Opens in a new browser tab)"
msgstr "(يفتح في علامة تبويب متصفح جديدة)"

#. translators: %s expands to the extension title
#: admin/views/licenses.php:151 admin/views/licenses.php:255
msgid "Manage your %s subscription on MyYoast"
msgstr "إدارة %s اشتراكك في MyYoast"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "ضع علامة على أهم %1$s كـ \"محتوى أساسي\" لتحسين بنية موقعك. %2$sمعرفة المزيد حول المحتوى الأساسي%3$s."

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "المقالات %1$sبدون%2$s التركيز على العبارة الرئيسية"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "ليس لديك أية مقالات منشورة، نقاط SEO الخاصة بك ستظهر هنا بمجرد نشر أول مقالة لك!"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "مرحبًا، أداء الـ SEO لديك بحالة جيدة! تحقق من الإحصائيات:"

#: admin/class-yoast-dashboard-widget.php:127
msgid "Read more like this on our SEO blog"
msgstr "قراءة المزيد مثل هذا على مدونة SEO الخاصة بنا"

#: admin/class-meta-columns.php:267
msgid "All Readability Scores"
msgstr "كل نتائج تقييم قابلية القراءة"

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:194 inc/class-wpseo-rank.php:199
#: inc/class-wpseo-rank.php:204
msgid "Readability: %s"
msgstr "قابلية القراءة: %s"

#: admin/views/licenses.php:159 admin/views/licenses.php:264
msgid "Not activated"
msgstr "لم يتم التفعيل"

#: admin/views/licenses.php:145 admin/views/licenses.php:249
msgid "Activated"
msgstr "تم التفعيل"

#. translators: %1$s expands to the product name. %2$s expands to a link to My
#. Yoast
#: inc/class-addon-manager.php:469
msgid "You are not receiving updates or support! Fix this problem by adding this site and enabling %1$s for it in %2$s."
msgstr "أنت لا تتلقى أي تحديثات أو دعم! أصلح هذه المشكلة بإضافة هذا الموقع وتمكين %1$s له في%2$s."

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:28 js/dist/new-settings.js:11
msgid "%1$s recommendations for you"
msgstr "توصيات %1$s من أجلك"

#: admin/class-meta-columns.php:263
msgid "Filter by Readability Score"
msgstr "الفرز بحسب نتيجة تقييم قابلية القراءة"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:95
msgid "Request method %1$s is not valid."
msgstr "طريقة الطلب %1$s غير متاحة."

#: admin/views/class-yoast-feature-toggles.php:113 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Text link counter"
msgstr "عداد الروابط النصية"

#: src/integrations/admin/link-count-columns-integration.php:137
msgid "Number of outgoing internal links in this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "عدد الروابط الداخلية الصادرة في هذه المقالة. راجع نص \"أعمدة Yoast\" في علامة تبويب المساعدة لمزيد من المعلومات."

#: src/integrations/admin/link-count-columns-integration.php:144
msgid "Number of internal links linking to this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "عدد الروابط الداخلية المرتبطة بهذه المقالة. راجع نص \"أعمدة Yoast\" في علامة تبويب المساعدة لمزيد من المعلومات."

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:60
msgid "%s Columns"
msgstr "%s الأعمدة"

#: admin/class-meta-columns.php:104
#: admin/taxonomy/class-taxonomy-columns.php:91
msgid "Readability score"
msgstr "درجة قابلية القراءة"

#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/formatter/class-metabox-formatter.php:319
#: src/deprecated/admin/add-keyword-modal.php:41
#: src/deprecated/admin/keyword-synonyms-modal.php:41
#: src/deprecated/admin/multiple-keywords-modal.php:41
#: js/dist/block-editor.js:380 js/dist/classic-editor.js:376
#: js/dist/elementor.js:380 js/dist/externals-components.js:13
#: js/dist/externals-components.js:75
msgid "Other benefits of %s for you:"
msgstr "منافع أخرى من %s من أجلك:"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:105
#: js/dist/externals-components.js:18 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Cornerstone content"
msgstr "محتوى أساسي"

#: src/integrations/admin/crawl-settings-integration.php:332
msgid "Upgrade Premium"
msgstr "الترقية إلى Premium"

#: admin/class-yoast-form.php:141 admin/class-yoast-form.php:146
#: js/dist/first-time-configuration.js:3 js/dist/new-settings.js:1
#: js/dist/settings.js:112
msgid "Save changes"
msgstr "حفظ التغييرات"

#: admin/class-premium-popup.php:88
#: admin/formatter/class-metabox-formatter.php:328
#: src/deprecated/admin/add-keyword-modal.php:50
#: src/deprecated/admin/keyword-synonyms-modal.php:50
#: src/deprecated/admin/multiple-keywords-modal.php:50
#: js/dist/block-editor.js:382 js/dist/classic-editor.js:378
#: js/dist/elementor.js:382 js/dist/externals-components.js:15
#: js/dist/externals-components.js:77
msgid "1 year free support and updates included!"
msgstr "يشمل دعم وتحديث مجاني لمدة عام!"

#: admin/class-premium-upsell-admin-block.php:56 js/dist/block-editor.js:376
#: js/dist/classic-editor.js:372 js/dist/elementor.js:376
#: js/dist/externals-components.js:9 js/dist/externals-components.js:71
#: js/dist/new-settings.js:237
msgid "Superfast internal linking suggestions"
msgstr "اقتراحات فائقة السرعة للربط الداخلي "

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:85
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "تغير الإضافة %2$s مخرجات موقعك وبذلك يميز بين محركات البحث والمستخدمين العاديين، وهي عملية تسمى إخفاء الهوية. نوصي بشدة بتعطيله."

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "No more dead links"
msgstr "لا مزيد من الروابط الميتة"

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "Easy redirect manager"
msgstr "مدير إعادة التوجيه السهل"

#: admin/class-premium-upsell-admin-block.php:59 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "No ads!"
msgstr "لا توجد إعلانات!"

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Increase your SEO reach"
msgstr "زيادة وصول SEO الخاص بك"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Facebook & Twitter"
msgstr "Facebook و Twitter"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Social media preview"
msgstr "معاينة شبكات التواصل الاجتماعية"

#: admin/views/tabs/metas/paper-content/rss-content.php:25
#: js/dist/new-settings.js:189
msgid "Available variables"
msgstr "المتغيرات المتاحة"

#: admin/class-admin.php:341
msgid "Scroll to see the table content."
msgstr "قم بالتمرير لرؤية محتوى الجدول."

#: admin/views/tabs/metas/paper-content/rss-content.php:32
#: js/dist/new-settings.js:189
msgid "Variable"
msgstr "المتغير"

#: admin/views/partial-notifications-warnings.php:22
msgid "No new notifications."
msgstr "لا توجد إشعارات جديدة."

#: admin/class-bulk-editor-list-table.php:881
msgid "Save"
msgstr "حفظ"

#: admin/class-bulk-editor-list-table.php:882
msgid "Save all"
msgstr "حفظ الجميع"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:247
msgid "%1$s, Author at %2$s"
msgstr "%1$s، كاتب في %2$s"

#: admin/formatter/class-metabox-formatter.php:204
#: js/dist/externals/analysis.js:383 js/dist/externals/analysisReport.js:17
msgid "Marks are disabled in current view"
msgstr "العلامات معطلة في العرض الحالي"

#: inc/class-wpseo-replace-vars.php:1513 js/dist/first-time-configuration.js:5
#: js/dist/settings.js:102
msgid "Name"
msgstr "الاسم"

#: admin/views/tool-import-export.php:88
msgid "Export settings"
msgstr "إعدادات التصدير"

#: admin/class-product-upsell-notice.php:161
msgid "Please don't show me this notification anymore"
msgstr "الرجاء عدم إظهاء هذا التنبيه مجدداً"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:154
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "إذا كنت تواجه مشكلات، %1$s فيمكنك إرسال تقرير خطأ%2$s وسنبذل قصارى جهدنا لمساعدتك."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:146
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "لقد لاحظنا أنك تستخدم %1$s لبعض الوقت الآن؛ نأمل أنها قد نالت إعجابك! ويسعدنا أن %2$s تمنحنا تقييم 5 نجوم على WordPress.org%3$s!"

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:335
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "تحذير: لا يمكن استخدام المتغير %1$s في هذا القالب. شاهد %2$s لمزيد من المعلومات."

#: admin/class-bulk-editor-list-table.php:789
msgid "(no title)"
msgstr "(بدون عنوان)"

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:129
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "بالمناسبة، هل تعلم أن لدينا أيضًا %1$sإضافة بريميوم%2$s؟ توفّر ميزات متقدمة، مثل مدير إعادة التوجيه وتدعم استخدام عبارات رئيسية متعددة. تأتي أيضًا مع دعم فني شخصي على مدار الساعة طوال أيام الأسبوع."

#: admin/views/class-yoast-feature-toggles.php:152 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "Admin bar menu"
msgstr "قائمة شريط المسؤول"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:155
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "تحتوي قائمة شريط المسؤول لإضافة %1$s على روابط مفيدة لأدوات الجهات الخارجية لتحليل الصفحات وتسهل معرفة ما إذا كان لديك إشعارات جديدة."

#: admin/metabox/class-metabox.php:173 js/dist/externals/analysis.js:211
#: js/dist/externals/analysis.js:265 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "SEO title"
msgstr "عنوان SEO"

#: admin/pages/dashboard.php:41 admin/pages/network.php:19
#: admin/views/tabs/dashboard/features.php:22
#: admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "المميزات"

#: admin/metabox/class-metabox.php:184 admin/metabox/class-metabox.php:189
#: admin/views/tabs/metas/paper-content/media-content.php:20
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402
msgid "No"
msgstr "لا"

#: admin/metabox/class-metabox.php:183 admin/metabox/class-metabox.php:188
#: admin/views/tabs/metas/paper-content/media-content.php:19
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402 js/dist/externals/schemaBlocks.js:13
msgid "Yes"
msgstr "نعم"

#: inc/options/class-wpseo-option-titles.php:979
msgid "Asterisk"
msgstr "نجمة"

#: inc/options/class-wpseo-option-titles.php:975
msgid "Bullet"
msgstr "نقطة مدورة"

#: inc/options/class-wpseo-option-titles.php:959
msgid "En dash"
msgstr "شرطة قصيرة"

#: inc/options/class-wpseo-option-titles.php:999
msgid "Right angle quotation mark"
msgstr "علامة اقتباس الزاوية اليمنى"

#: inc/options/class-wpseo-option-titles.php:995
msgid "Left angle quotation mark"
msgstr "علامة اقتباس الزاوية اليسرى"

#: inc/options/class-wpseo-option-titles.php:987
msgid "Vertical bar"
msgstr "خط أفقي"

#: inc/options/class-wpseo-option-titles.php:1007
msgid "Greater than sign"
msgstr "أكبر من العلامة"

#: inc/options/class-wpseo-option-titles.php:1003
msgid "Less than sign"
msgstr "أقل من العلامة"

#: inc/options/class-wpseo-option-titles.php:955
msgid "Dash"
msgstr "خط فاصل"

#: inc/options/class-wpseo-option-titles.php:991
msgid "Small tilde"
msgstr "تلدة صغيرة"

#: inc/options/class-wpseo-option-titles.php:983
msgid "Low asterisk"
msgstr "نجمة على السطر"

#: inc/options/class-wpseo-option-titles.php:971
msgid "Middle dot"
msgstr "نقطة وسط السطر"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:10
msgid "Choose the symbol to use as your title separator. This will display, for instance, between your post title and site name. Symbols are shown in the size they'll appear in the search results."
msgstr "اختر الرمز المراد استخدامه كفاصل للعنوان. سيظهر هذا على سبيل المثال، بين عنوان مقالتك واسم الموقع. تظهر الرموز بالحجم الذي ستظهر به في نتائج البحث."

#: inc/options/class-wpseo-option-titles.php:963
msgid "Em dash"
msgstr "شرطة طويلة"

#: admin/views/licenses.php:125
msgid "create and manage redirects from within your WordPress install."
msgstr "إنشاء وإدارة عمليات إعادة التوجيه بداخل تثبيت ووردبريس الخاص بك."

#: admin/views/licenses.php:124
msgid "Redirect manager"
msgstr "مدير إعادة التوجيه"

#: admin/views/licenses.php:142 admin/views/licenses.php:246
msgid "Installed"
msgstr "تم التنصيب"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "قائمة المقالات"

#: admin/views/licenses.php:49
msgid "Show your videos in Google Videos"
msgstr "أعرض فيديوهاتك في Google Videos"

#. translators: Text between 1: and 2: will only be shown to screen readers. 3:
#. expands to the product name.
#: admin/views/licenses.php:196 admin/views/licenses.php:300
msgid "More information %1$sabout %3$s%2$s"
msgstr "المزيد من المعلومات %1$s عن %3$s%2$s"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "تصفية قائمة المقالات"

#. translators: 1: expands to Yoast SEO extensions
#: admin/views/licenses.php:225
msgid "%1$s to optimize your site even further"
msgstr " %1$s لتحسين موقعك بشكل أكبر"

#: admin/views/licenses.php:51
msgid "Make videos responsive through enabling fitvids.js"
msgstr "اجعل مقاطع الفيديو تستجيب من خلال تمكين fitvids.js"

#. translators: %1$s expands to Yoast SEO, %2$s expands to WooCommerce
#: admin/views/licenses.php:84
msgid "A seamless integration between %1$s and %2$s"
msgstr "تكامل سلس بين %1$s و %2$s"

#: admin/views/tabs/metas/archives/help.php:20
msgid "Note that links to archives might be still output by your theme and you would need to remove them separately."
msgstr "لاحظ أن الروابط إلى الأرشيف قد تظل ناتجة عن قالبك وستحتاج إلى إزالتها بشكل منفصل."

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "قائمة التنقل بين المقالات"

#: admin/views/licenses.php:136
msgid "Premium support"
msgstr "دعم البريميوم"

#: admin/views/licenses.php:133
msgid "check what your Facebook or Twitter post will look like."
msgstr "تحقق كيف سيبدو منشور Facebook أو Twitter الخاص بك."

#: admin/views/licenses.php:36
msgid "Easily insert Google Maps, a store locator, opening hours and more"
msgstr "أدخل خرائط Google، ومحدد موقع المتجر، وساعات العمل والمزيد بسهولة"

#: admin/views/licenses.php:62
msgid "Optimize your site for Google News"
msgstr "تحسين موقعك لأخبار Google"

#: admin/views/licenses.php:50
msgid "Enhance the experience of sharing posts with videos"
msgstr "تحسين تجربة مشاركة المقالات مع مقاطع الفيديو"

#: admin/views/licenses.php:209
msgid "Comes with our 30-day no questions asked money back guarantee"
msgstr "ضمان استرداد الأموال في خلال 30 يومًا دون طرح أي سؤال"

#. translators: 1: expands to Yoast SEO
#: admin/views/licenses.php:220
msgid "%1$s extensions"
msgstr "ملحقات %1$s"

#. translators: 1: expands to Yoast SEO Premium
#: admin/views/licenses.php:112
msgid "%1$s, take your optimization to the next level!"
msgstr "%1$s، انتقل إلى المستوى التالي من التحسينات الخاصة بك!"

#: admin/views/licenses.php:132
msgid "Social previews"
msgstr "معاينات شبكات التواصل الاجتماعي"

#. translators: %1$s expands to Yoast, %2$s expands to WooCommerce
#: admin/views/licenses.php:82
msgid "Use %1$s breadcrumbs instead of %2$s ones"
msgstr "استخدام مسارات التنقل %1$s بدلاً من %2$s"

#: admin/views/licenses.php:64
msgid "Creates XML News Sitemaps"
msgstr "يقوم بإنشاء خرائط مواقع أخبار XML News Sitemaps"

#: admin/views/licenses.php:63
msgid "Immediately pings Google on the publication of a new post"
msgstr "يقوم فورًا بتنبيه Google عند نشر مقالة جديدة"

#: admin/views/licenses.php:137
msgid "gain access to our 24/7 support team."
msgstr "الوصول إلى فريق الدعم الفني على مدار الساعة طوال أيام الأسبوع."

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:803
msgid "Edit &#8220;%s&#8221;"
msgstr "تعديل &#8220;%s&#8221;"

#: admin/menu/class-base-menu.php:258
#: src/integrations/admin/menu-badge-integration.php:35
msgid "Premium"
msgstr "البريميوم"

#: admin/class-admin.php:255
msgid "Get Premium"
msgstr "الحصول على إصدار Premium"

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:244
msgid "Notifications"
msgstr "الإشعارات"

#: admin/views/tabs/social/pinterest.php:16
msgid "Pinterest settings"
msgstr "إعدادات Pinterest"

#: admin/views/tabs/social/twitterbox.php:17
msgid "Twitter settings"
msgstr "إعدادات Twitter"

#: admin/views/tabs/social/facebook.php:18
msgid "Facebook settings"
msgstr "إعدادات Facebook"

#: inc/class-wpseo-admin-bar-menu.php:293
msgid "Google Trends"
msgstr "محتويات Google الرائجة"

#: admin/views/user-profile.php:45
msgid "Disable SEO analysis"
msgstr "تعطيل تحليل SEO"

#: admin/views/user-profile.php:48
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "يزيل قسم التركيز علي الكلمة الرئيسية من صندوق الحقول التعريفية (metabox) ويعطل جميع الاقتراحات المتعلقة بـ SEO."

#. translators: %s: number of notifications
#: admin/menu/class-admin-menu.php:120 inc/class-wpseo-admin-bar-menu.php:660
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s إشعارات"
msgstr[1] "%s إشعار"
msgstr[2] "%s إشعاران"
msgstr[3] "%s إشعارات"
msgstr[4] "%s إشعارات"
msgstr[5] "%s إشعارات"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:12
msgid "Title separator symbol"
msgstr "رمز فاصل العنوان"

#: admin/views/tabs/metas/breadcrumbs.php:15
msgid "Breadcrumbs settings"
msgstr "إعدادات مسارات التنقل (Breadcrumbs)"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "إعطاء الأولوية"

#: admin/views/tabs/metas/rss.php:15
msgid "RSS feed settings"
msgstr "إعدادات RSS feed"

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:44 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Readability analysis"
msgstr "تحليل قابلية القراءة"

#: admin/formatter/class-metabox-formatter.php:68
#: admin/metabox/class-metabox-section-readability.php:28
msgid "Readability"
msgstr "قابلية القراءة"

#: admin/views/user-profile.php:57
msgid "Disable readability analysis"
msgstr "تعطيل تحليل القراءة"

#: admin/views/user-profile.php:60
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "يزيل قسم تحليل قابلية القراءة من صندوق الحقول التعريفية (metabox) ويعطل جميع الاقتراحات المتعلقة بقابلية القراءة."

#: admin/formatter/class-metabox-formatter.php:120
#: admin/formatter/class-metabox-formatter.php:150
#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:163
#: inc/class-wpseo-rank.php:195 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "بحاجة إلى تحسين"

#: admin/formatter/class-metabox-formatter.php:170
#: js/dist/externals/schemaBlocks.js:9
msgid "Analysis"
msgstr "التحليل"

#: admin/formatter/class-metabox-formatter.php:198
#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Problems"
msgstr "مشاكل"

#: admin/formatter/class-metabox-formatter.php:113
#: admin/formatter/class-metabox-formatter.php:143 inc/class-wpseo-rank.php:138
msgid "Not available"
msgstr "غير متوفر"

#: admin/formatter/class-metabox-formatter.php:199
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Improvements"
msgstr "تحسينات"

#: admin/menu/class-admin-menu.php:54 admin/pages/dashboard.php:32
msgid "Dashboard"
msgstr "لوحة التحكم"

#: admin/views/partial-notifications-errors.php:21
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "لقد اكتشفنا المشاكل التالية التي تؤثر على SEO لموقعك."

#: admin/class-meta-columns.php:151
msgid "Meta description not set."
msgstr "لم يتم تعيين بيانات Meta الوصفية."

#: admin/class-meta-columns.php:235
msgid "Filter by SEO Score"
msgstr "تصفية حسب نتيجة SEO"

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "يمكنك إصلاح ذلك على %1$sصفحة إعدادات الرابط الدائم%2$s."

#: admin/views/partial-notifications-errors.php:22
msgid "Good job! We could detect no serious SEO problems."
msgstr "عمل جيد! لم نتمكن من اكتشاف أي مشاكل خطيرة في SEO."

#: admin/views/tabs/social/pinterest.php:36
msgid "Pinterest confirmation"
msgstr "تأكيد Pinterest"

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the primary category of the post/page"
msgstr "تم الاستبدال بالتصنيف الأساسي للمقالة / الصفحة"

#: admin/views/tabs/dashboard/webmaster-tools.php:24
msgid "Webmaster Tools verification"
msgstr "التحقق من أدوات مشرفي المواقع"

#: admin/views/tabs/metas/paper-content/taxonomy-content.php:18
#: js/dist/new-settings.js:155 js/dist/new-settings.js:228
msgid "Format-based archives"
msgstr "المحفوظات القائمة على التنسيق"

#: admin/views/tabs/social/pinterest.php:24
msgid "If you have already confirmed your website with Pinterest, you can skip the step below."
msgstr "إذا كنت قد أكدت موقعك مع Pinterest، يمكنك تخطي الخطوة أدناه."

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "عنوان %1$s جديد"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "العنوان %1$s الحالي"

#. translators: $s expands to Yoast SEO Premium
#. translators: %s expands to the product name
#. translators: 1: Yoast WooCommerce SEO
#: admin/views/licenses.php:182 admin/views/licenses.php:285
#: js/dist/integrations-page.js:5
msgid "Buy %s"
msgstr "شراء %s"

#: inc/sitemaps/class-sitemaps-cache-validator.php:294
msgid "Expected an integer as input."
msgstr "توقع عدداً صحيحاً للإدخال."

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "محاولة إنشاء مفتاح ذاكرة التخزين المؤقت لخريطة الموقع ، لكن تركيبة postfix و prefix تترك مساحة صغيرة جدًا للقيام بذلك. من المحتمل أنك تطلب صفحة بعيدة عن النطاق المتوقع."

#: admin/views/redirects.php:32
#: src/integrations/admin/redirects-integration.php:48
msgid "Redirects"
msgstr "التوجيهات"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:27
msgid "Regular"
msgstr "عادي"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:280
msgid "Remove"
msgstr "حذف"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:279
msgid "Keep"
msgstr "الاحتفاظ"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:32
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Bold the last page"
msgstr "جعل الخط عريض للصفحة الأخيرة"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "الأساسي"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:22
msgid "Show Blog page"
msgstr "إظهار صفحة المدونة"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "الأساسي %s"

#: admin/views/tabs/metas/archives.php:19
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:16
#: js/dist/new-settings.js:71 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Author archives"
msgstr "أرشيف الكاتب"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:26
msgid "Bold"
msgstr "عريض"

#. translators: accessibility text. %1$s expands to the term title, %2$s to the
#. taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "اجعل %1$s الأساسي %2$s"

#: admin/taxonomy/class-taxonomy-columns.php:152
msgid "Term is set to noindex."
msgstr "تم تعيين العنصر إلى بلا فهرسة"

#: admin/pages/dashboard.php:47 admin/pages/network.php:20
#: admin/views/tabs/dashboard/integrations.php:21
#: admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:73
#: js/dist/integrations-page.js:33
msgid "Integrations"
msgstr "التكامل والدمج (Integrations)"

#: src/integrations/admin/crawl-settings-integration.php:214
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "مُفعّل"

#: src/integrations/admin/crawl-settings-integration.php:213
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Disabled"
msgstr "معطل"

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "لا يوجد فهرس"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1459
msgid "The separator defined in your theme's %s tag."
msgstr "يحدد الفاصل في %s وسم القالب الخاص بك."

#: src/deprecated/admin/ryte/class-ryte.php:137
msgid "Once Weekly"
msgstr "مرة أسبوعيًا"

#: admin/class-meta-columns.php:100
#: admin/taxonomy/class-taxonomy-columns.php:87
msgid "SEO score"
msgstr "نتيجة SEO"

#: inc/class-wpseo-admin-bar-menu.php:339
msgid "Analyze this page"
msgstr "تحليل هذه الصفحة"

#: inc/options/class-wpseo-option-titles.php:256
msgid "Home"
msgstr "الرئيسية"

#. Author of the plugin
msgid "Team Yoast"
msgstr "فريق Yoast"

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1310 inc/options/class-wpseo-option-titles.php:284
msgid "%s Archive"
msgstr "%s الأرشيف"

#: inc/options/class-wpseo-option-social.php:109
msgid "Summary with large image"
msgstr "ملخص مع صورة كبيرة"

#: inc/options/class-wpseo-option-titles.php:257
msgid "You searched for"
msgstr "لقد بحثت عن"

#: inc/options/class-wpseo-option-titles.php:254
msgid "Error 404: Page not found"
msgstr "خطأ 404 - لم يتم العثور على الصفحة"

#: inc/options/class-wpseo-option-ms.php:245
msgid "No numeric value was received."
msgstr "لم يتم الحصول على قيمة رقمية."

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:233
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "هذه المدونة موجودة. المدونة %s غير موجودة أو تم وضع علامة عليها كمحذوفة."

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:209
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$s ليس خيارًا صالحًا لمن يجب السماح له بالوصول إلى %2$s الإعدادات. إعادة تعيين القيمة إلى الافتراضي."

#: inc/options/class-wpseo-option-ms.php:229
#: inc/options/class-wpseo-option-ms.php:245
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "يجب أن يكون إعداد المدونة الافتراضي هو معرف المدونة الرقمي للمدونة التي تريد استخدامها كمدونة افتراضية."

#. translators: %s expands to an invalid URL.
#: inc/options/class-wpseo-option.php:394
msgid "%s does not seem to be a valid url. Please correct."
msgstr "لا يبدو أن %s عنوان موقع صالح. يرجى التصحيح."

#. translators: 1: Verification string from user input; 2: Service name.
#: inc/options/class-wpseo-option.php:358
msgid "%1$s does not seem to be a valid %2$s verification string. Please correct."
msgstr "لا يبدو أن %1$s سلسلة تحقق %2$s صالحة. يرجى التصحيح."

#: inc/class-wpseo-admin-bar-menu.php:394
msgid "Mobile-Friendly Test"
msgstr "اختبار التوافق مع الأجهزة الجوّالة"

#: wp-seo-main.php:535
msgid "The filter extension seem to be unavailable. Please ask your web host to enable it."
msgstr "يبدو أن امتداد الفلتر غير متوفر. يرجى مطالبة مضيف الويب الخاص بك لتفعيله."

#. Plugin Name of the plugin
#: admin/capabilities/class-capability-manager-integration.php:72
#: js/dist/block-editor.js:468
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: inc/class-wpseo-admin-bar-menu.php:379
msgid "HTML Validator"
msgstr "مدقق HTML"

#: inc/options/class-wpseo-option-titles.php:255
msgid "Archives for"
msgstr "أرشيفات لـ"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:249
msgid "You searched for %s"
msgstr "لقد بحثت عن %s"

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:511
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "تثبيت الإضافية %1$s غير مكتمل. الرجاء الرجوع إلى %2$s إرشادات التثبيت%3$s.."

#: inc/class-wpseo-admin-bar-menu.php:369
msgid "Facebook Debugger"
msgstr "مصحح Facebook"

#: inc/class-wpseo-admin-bar-menu.php:389
msgid "Google Page Speed Test"
msgstr "اختبار سرعة صفحة Google"

#: inc/class-wpseo-admin-bar-menu.php:429
#: inc/class-wpseo-admin-bar-menu.php:477
msgid "SEO Settings"
msgstr "إعدادات SEO"

#: wp-seo-main.php:489
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "يبدو أن امتداد مكتبة PHP القياسية (SPL) غير متاحة. يرجى مطالبة مضيف الويب الخاص بك لتمكينها."

#: inc/class-wpseo-admin-bar-menu.php:374
msgid "Pinterest Rich Pins Validator"
msgstr "أداة التحقق من Pinterest Rich Pins"

#: inc/class-wpseo-admin-bar-menu.php:359
msgid "Check Google Cache"
msgstr "التحقق من ذاكرة Google Cache"

#. translators: %s expands to a twitter user name.
#: inc/options/class-wpseo-option-social.php:182
msgid "%s does not seem to be a valid Twitter Username. Please correct."
msgstr "لا يبدو أن %s اسم مستخدم Twitter صالح. يرجى التصحيح."

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:559
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "الرجاء تحديد نوع مقالة صالح للتصنيف \"%s\""

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:521
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "الرجاء إختيار فئة صحيحة لنوع المقالة \"%s\""

#: inc/class-wpseo-admin-bar-menu.php:275
msgid "Keyword Research"
msgstr "البحث عن الكلمة الرئيسية"

#: wp-seo-main.php:546
msgid "Activation failed:"
msgstr "فشل التفعيل:"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:252
msgid "The post %1$s appeared first on %2$s."
msgstr "ظهرت المقالة %1$s أولاً على %2$s."

#: admin/formatter/class-metabox-formatter.php:134
#: admin/formatter/class-metabox-formatter.php:164
#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:173
#: inc/class-wpseo-rank.php:205 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "جيدة"

#. Description of the plugin
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "أول حل حقيقي شامل الكل في واحد لتحسين محركات البحث SEO لووردبريس، بما في ذلك تحليل المحتوى على الصفحة وخرائط مواقع XML وغير ذلك الكثير."

#: inc/class-wpseo-admin-bar-menu.php:384
msgid "CSS Validator"
msgstr "أداة التحقق من CSS"

#: admin/views/tabs/tool/import-seo.php:82
msgid "Import"
msgstr "استيراد"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current day"
msgstr "يستبدل باليوم الحالي"

#: src/integrations/admin/import-integration.php:117
msgid "Default settings"
msgstr "الإعدادات الافتراضية"

#: admin/views/tabs/metas/paper-content/rss-content.php:33
#: admin/views/tool-bulk-editor.php:111 js/dist/new-settings.js:189
msgid "Description"
msgstr "الوصف"

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the current year"
msgstr "يستبدل بالعام الحالي"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Replaced with the current month"
msgstr "يستبدل بالشهر الحالي"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current date"
msgstr "يستبدل بالتاريخ الحالي"

#: inc/class-wpseo-replace-vars.php:1471
msgid "The site's name"
msgstr "اسم الموقع"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:77
#: js/dist/new-settings.js:239
msgid "Blog"
msgstr "المدونة"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "ليس لديك ملف %s ، قم بإنشاء واحد هنا:"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the term name"
msgstr "تم الاستبدال باسم المصطلح"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Replaced with the current tag/tags"
msgstr "تم الاستبدال بالوسم/الوسوم الحالية"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the current page number"
msgstr "استبدال مع رقم الصفحة الحالية"

#: inc/class-wpseo-replace-vars.php:1469
msgid "Replaced with the title of the parent page of the current page"
msgstr "تم استبداله بعنوان الصفحة الرئيسية للصفحة الحالية"

#: inc/options/class-wpseo-option-titles.php:250
msgid "Page not found"
msgstr "الصفحة غير موجودة"

#. translators: %s expands to the variable used for term title.
#: admin/formatter/class-term-metabox-formatter.php:164
#: inc/class-upgrade.php:1313 inc/options/class-wpseo-option-titles.php:320
msgid "%s Archives"
msgstr "%s الأرشيف"

#: admin/pages/tools.php:24
msgid "Import and Export"
msgstr "استيراد وتصدير"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Attachment caption"
msgstr "تسمية توضيحية للمرفق"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:11
msgid "%1$s settings"
msgstr "إعدادات %1$s"

#: admin/pages/tools.php:36
msgid "Bulk editor"
msgstr "المحرر المتعدد"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "تصدير الإعدادات%1$s الخاصة بك"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1030
msgid "Page %1$d of %2$d"
msgstr "الصفحة %1$d من %2$d"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "تم استبداله برقم الصفحة الحالي مع السياق (أي صفحة ٢ من ٤)"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "إذا كان لديك %s ملف وكان قابلاً للتحرير ، فيمكنك تحريره من هنا."

#. Translators: %1$s resolves to the SEO menu item, %2$s resolves to the Search
#. Appearance submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "إشعار للمسؤول فقط: لا تعرض هذه الصفحة وصفًا تعريفيًا لأنها لا تحتوي على وصف ، إما كتابته لهذه الصفحة تحديدًا أو الانتقال إلى قائمة [%1$s - %2$s] وإعداد نموذج."

#: inc/class-wpseo-replace-vars.php:118
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "تم بالفعل تسجيل متغير بديل بنفس الاسم. حاول جعل اسم المتغير فريدًا من نوعه."

#: admin/pages/tools.php:70
msgid "&laquo; Back to Tools page"
msgstr "&laquo; العودة إلى صفحة الأدوات"

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#. translators: %1$s and %2$s are replaced by opening and closing <a> tags.
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:103
#: js/dist/new-settings.js:92
msgid "Usage of this breadcrumbs feature is explained in %1$sour knowledge-base article on breadcrumbs implementation%2$s."
msgstr "تم شرح استخدام ميزة مسارات التنقل هذه في %1$s مقالة قاعدة المعارف الخاصة بنا حول تنفيذ مسارات التنقل%2$s."

#: inc/class-wpseo-replace-vars.php:105
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "يمكن أن يحتوي متغير الاستبدال على أحرف أبجدية رقمية وشرطة سفلية أو شرطة. حاول إعادة تسمية المتغير الخاص بك."

#: admin/views/tabs/social/facebook.php:54
msgid "This image is used if the post/page being shared does not contain any images."
msgstr "يتم استخدام هذه الصورة إذا كانت المقالة أو الصفحة التي يتم مشاركتها لا تحتوي على أي صورة."

#: inc/class-wpseo-replace-vars.php:1467
msgid "Replaced with the date of the post/page"
msgstr "تم الاستبدال بتاريخ المقالة/الصفحة"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the category description"
msgstr "تم استبداله بوصف الفئة"

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the tag description"
msgstr "تم الاستبدال بوصف الوسم"

#: inc/class-wpseo-replace-vars.php:1468
msgid "Replaced with the title of the post/page"
msgstr "تم الاستبدال بعنوان المقالة /الصفحة"

#: admin/views/tabs/social/facebook.php:20
msgid "Add Open Graph meta data"
msgstr "إضافة بيانات ميتا الوصفية لـ Open Graph"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "إذا كان %s الخاص بك قابلًا للكتابة ، فيمكنك تعديله من هنا."

#: admin/pages/tools.php:30
msgid "File editor"
msgstr "محرر الملف"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the current page total"
msgstr "تم استبداله بإجمالي الصفحة الحالية"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "تم استبداله بمقتطف المقالة / الصفحة (أو تم إنشاؤه تلقائيًا إذا لم يكن موجودًا)"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post categories (comma separated)"
msgstr "تم استبداله بتصنيفات المقالات (مفصولة بفواصل)"

#: inc/class-wpseo-replace-vars.php:1474
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "تم استبدال مقتطفات المقالة / الصفحة (بدون الإنشاء التلقائي)"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Replaced with a custom taxonomies description"
msgstr "تم استبداله بوصف فئة مخصص"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "تم استبداله بالفئات المخصصة للمقالات، مفصولة بفواصل."

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with a posts custom field value"
msgstr "تم استبدالها بقيمة حقل مخصصة للمقالات"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the slug which caused the 404"
msgstr "يستبدل بالاسم اللطيف (slug) الذي كان سببا في إظهار صفحة الخطأ 404"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the posts focus keyphrase"
msgstr "يستبدل بالكلمة المفتاحية الرئيسية للمقالة"

#: inc/class-wpseo-replace-vars.php:122
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "لا يمكنك إبطال استبدال متغير قياسي WPSEO بتسجيل متغير بنفس الاسم. استخدم عامل التصفية \"wpseo_replacements\" بدلاً من ذلك لضبط قيمة الاستبدال."

#: inc/class-wpseo-replace-vars.php:108
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "لا يمكن أن يبدأ المتغير البديل بـ \"%%cf_\" or \"%%ct_\" حيث إنهما محجوزان لمتغيرات المتغير القياسي WPSEO للحقول المخصصة والفئات المخصصة. حاول جعل اسم المتغير فريدًا."

#: admin/views/tabs/metas/paper-content/rss-content.php:15
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put after each post in the feed"
msgstr "المحتوى الذي سيتم وضعه بعد كل مقالة في الخلاصة"

#: admin/views/tabs/metas/paper-content/rss-content.php:14
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put before each post in the feed"
msgstr "المحتوى الذي سيتم وضعه قبل كل مقالة في الخلاصة"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:98
#: js/dist/new-settings.js:91
msgid "How to insert breadcrumbs in your theme"
msgstr "كيفية إدراج مسارات التنقل في قالب موقعك"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:73
msgid "Content type archive to show in breadcrumbs for taxonomies"
msgstr "أرشيف نوع المحتوى لإظهاره في مسارات التنقل للفئات"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:43
msgid "Taxonomy to show in breadcrumbs for content types"
msgstr "الفئات المراد إظهارها في مسارات التنقل لأنواع المحتوى"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:17
msgid "Breadcrumb for 404 Page"
msgstr "مسار التنقل لصفحة الخطأ 404"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:12
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Separator between breadcrumbs"
msgstr "فاصل بين مسارات التنقل"

#: admin/views/tabs/metas/rss.php:21
msgid "This feature is used to automatically add content to your RSS, more specifically, it's meant to add links back to your blog and your blog posts, so dumb scrapers will automatically add these links too, helping search engines identify you as the original source of the content."
msgstr "تُستخدم هذه الميزة لإضافة محتوى تلقائيًا إلى RSS الخاص بك ، وبشكل أكثر تحديدًا ، يُقصد منها إضافة روابط مرة أخرى إلى مدونتك ومقالات مدونتك ، لذلك ستضيف أدوات الكشط الغبية هذه الروابط تلقائيًا أيضًا ، مما يساعد محركات البحث على التعرف عليك كمصدر أصلي لـ المحتوى."

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "تسمح لك هذه الأداة بتغيير عناوين وأوصاف مقالاتك وصفحاتك بسرعة دون الحاجة إلى الانتقال إلى المحرر لكل صفحة."

#: admin/views/tabs/social/pinterest.php:20
msgid "Pinterest uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph meta data\" setting on the Facebook tab enabled if you want to optimize your site for Pinterest."
msgstr "يستخدم Pinterest بيانات وصف الميتا لـ Open Graph تمامًا مثل Facebook، لذا تأكد من تمكين إعداد \"إضافة بيانات وصف الميتا لـ Open Graph\" في علامة التبويب Facebook إذا كنت ترغب في تحسين موقعك لـ Pinterest."

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:91
msgid "Import from other SEO plugins"
msgstr "الاستيراد من إضافات SEO أخرى"

#: admin/views/tabs/metas/paper-content/rss-content.php:51
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name and description as anchor text."
msgstr "رابط إلى موقعك، مع اسم موقعك ووصفه كنص مرساة (رابط للقفز)."

#: admin/views/tabs/social/twitterbox.php:24
msgid "Add Twitter card meta data"
msgstr "إضافة بيانات ميتا الخاصة بـ تويتر Twitter card"

#: admin/pages/tools.php:31
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "تتيح لك هذه الأداة تغيير الملفات المهمة لإعدادات SEO الخاصة بك بسرعة، مثل ملف robots.txt الخاص بك، وملف htaccess. إذا كان لديك ذلك."

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:43
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "تأتي %1$s مع بعض الأدوات المدمجة الفعّالة جدًا:"

#: admin/class-export.php:61 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:85
msgid "Import settings"
msgstr "استيراد الإعدادات"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:14
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for the breadcrumb path"
msgstr "بادئة مسار التنقل"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:15
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for Archive breadcrumbs"
msgstr "بادئة مسارات التنقل للأرشيف"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:16
msgid "Prefix for Search Page breadcrumbs"
msgstr "بادئة مسارات التنقل لصفحة البحث"

#: admin/views/tabs/metas/paper-content/rss-content.php:20
#: js/dist/new-settings.js:189
msgid "You can use the following variables within the content, they will be replaced by the value on the right."
msgstr "يمكنك استخدام المتغيرات التالية في المحتوى، وسيتم استبدالها بالقيمة الموجودة على اليسار."

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "فيما يلي نقاط نتيجة SEO لمقالاتك. الآن هو الوقت المناسب لبدء تحسين بعض من مقالاتك!"

#: admin/views/user-profile.php:24
msgid "Title to use for Author page"
msgstr "العنوان المراد استخدامه لـ صفحة الكاتب"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "تم الاستبدال بـ \"معلومات السيرة الذاتية\" لكاتب المقالة / الصفحة"

#: inc/class-wpseo-replace-vars.php:1513
msgid "Replaced with the post/page author's 'nicename'"
msgstr "(الاسم اللطيف) تم استبداله باسم كاتب المقالة / الصفحة"

#: admin/views/user-profile.php:28
msgid "Meta description to use for Author page"
msgstr "بيانات Meta الوصفية لاستخدامها في صفحة الكاتب"

#: admin/views/tabs/metas/paper-content/rss-content.php:39
#: js/dist/new-settings.js:189
msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr "رابط لأرشيف كاتب المقالة، مع اسم الكاتب كـ نص مرساة (Anchor)."

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:13
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Anchor text for the Homepage"
msgstr "نص المرساة (Anchor) للرئيسية"

#: admin/views/tabs/dashboard/dashboard.php:51
msgid "Credits"
msgstr "حقوق المساهمة"

#: admin/views/tabs/metas/paper-content/rss-content.php:43
#: js/dist/new-settings.js:189
msgid "A link to the post, with the title as anchor text."
msgstr "رابط إلى المقالة، مع العنوان كنص مرساة (Anchor)."

#: admin/views/tabs/metas/paper-content/rss-content.php:47
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name as anchor text."
msgstr "رابط إلى موقعك، مع اسم موقعك كنص مرساة (Anchor)."

#. translators: %1$s / %2$s expands to a link to pinterest.com's help page.
#: admin/views/tabs/social/pinterest.php:30
msgid "To %1$sconfirm your site with Pinterest%2$s, add the meta tag here:"
msgstr "لتأكيد %1$sموقعك مع Pinterest%2$s، أضف وسم meta هنا:"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Replaced with the post/page modified time"
msgstr "تم استبداله بوقت تعديل المقالة / الصفحة"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Replaced with the content type plural label"
msgstr "تم استبداله بتسمية صيغة الجمع لنوع المحتوى"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Replaced with the content type single label"
msgstr "تم استبداله بتسمية صيغة المفرد لنوع المحتوى"

#: inc/class-wpseo-replace-vars.php:1512
msgid "Replaced with the post/page ID"
msgstr "تم الاستبدال بمعرّف المقالة / الصفحة"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the current search phrase"
msgstr "تم الاستبدال بعبارة البحث الحالية"

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the term description"
msgstr "تم الاستبدال بوصف المصطلح"

#: admin/pages/tools.php:25
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "استيراد الإعدادات من إضافات SEO وتصدير الإعدادات لإعادة استخدامها في موقع (آخر)."

#: admin/views/tool-bulk-editor.php:109 inc/class-wpseo-replace-vars.php:1468
#: js/dist/externals-redux.js:1
msgid "Title"
msgstr "العنوان"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "مُعرّف الموقع"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:64
#: src/integrations/admin/first-time-configuration-integration.php:491
#: js/dist/new-settings.js:230
msgid "Person"
msgstr "شخص"

#: admin/views/tabs/metas/archives.php:24
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:16
#: js/dist/new-settings.js:127 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Date archives"
msgstr "تاريخ الأرشيف"

#: admin/pages/metas.php:21
msgid "Taxonomies"
msgstr "الفئات"

#: admin/views/tabs/metas/general.php:47 js/dist/new-settings.js:172
#: js/dist/new-settings.js:224 js/dist/new-settings.js:235
msgid "Homepage"
msgstr "الصفحة الرئيسية"

#: admin/pages/social.php:18
msgid "Accounts"
msgstr "حسابات"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "عامة"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:14
msgid "Website name"
msgstr "اسم الموقع"

#: admin/pages/dashboard.php:56
msgid "Webmaster Tools"
msgstr "أدوات مشرفي المواقع"

#: admin/pages/metas.php:24 js/dist/new-settings.js:189
#: js/dist/new-settings.js:235
msgid "RSS"
msgstr "RSS"

#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:43
msgid "Authenticate"
msgstr "مصادقة"

#: admin/views/redirects.php:112
msgid "URL"
msgstr "عنوان URL"

#: admin/views/tabs/metas/paper-content/special-pages.php:26
#: js/dist/new-settings.js:228
msgid "404 pages"
msgstr "الصفحات غير الموجودة ٤٠٤"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "مؤرشف"

#: admin/class-yoast-network-admin.php:130
msgid "Settings Updated."
msgstr "تم تحديث الإعدادات."

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "مزعج"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:38 admin/views/licenses.php:22
msgid "The premium version of %1$s with more features & support."
msgstr "الإصدار المدفوع من %1$s مع مزيد من المميزات والدعم."

#. translators: %s expands to <code>noindex, follow</code>
#: admin/views/tabs/metas/archives/help.php:17
msgid "If this is the case on your site, you can choose to either disable it (which makes it redirect to the homepage), or to add %s to it so it doesn't show up in the search results."
msgstr "إذا كانت هذه هي الحالة على موقعك، فيمكنك اختيار إما تعطيلها (مما يجعلها تعيد التوجيه إلى الصفحة الرئيسية)، أو إضافة %s إليها حتى لا تظهر في نتائج البحث."

#: admin/views/tabs/metas/archives/help.php:21
msgid "Date-based archives could in some cases also be seen as duplicate content."
msgstr "يمكن أيضًا اعتبار الأرشيفات المستندة إلى التاريخ في بعض الحالات كمحتوى مكرر."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:14
msgid "%1$s has auto-detected whether it needs to force rewrite the titles for your pages, if you think it's wrong and you know what you're doing, you can change the setting here."
msgstr "قامت %1$s بالكشف تلقائياً عما إذا كانت بحاجة لفرض إعادة كتابة عناوين صفحاتك، إذا كنت تظن أنها مخطئة وكنت تعرف ماذا تفعل، بإمكانك تغيير الإعداد هنا."

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "بالغ"

#: admin/class-plugin-availability.php:50 admin/views/licenses.php:46
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "حسِّن مقاطع الفيديو الخاصة بك لإظهارها في نتائج البحث والحصول على المزيد من النقرات!"

#: admin/class-plugin-availability.php:70 admin/views/licenses.php:32
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "ترتيب أفضل محليًا وفي خرائط Google ، دون عناء!"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "أدخل %1$sمعرف الموقع%2$s للموقع الذي تريد استخدام إعداداته كافتراضي لكافة المواقع التي تمت إضافتها إلى شبكتك. اتركه فارغًا بلا شيء (أي سيتم استخدام الإعدادات الافتراضية للإضافة العادية)."

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "ترث المواقع الجديدة في الشبكة إعدادات SEO الخاصة بها من هذا الموقع"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "المدراء الكبار فقط (Super Admins)"

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "مدراء الموقع (افتراضي)"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:164
msgid "%s restored to default SEO settings."
msgstr "تمت استعادة %s إلى إعدادات SEO الافتراضية."

#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:10
msgid "Force rewrite titles"
msgstr "فرض إعادة كتابة العناوين"

#: admin/pages/metas.php:23 js/dist/new-settings.js:91
#: js/dist/new-settings.js:224 js/dist/new-settings.js:226
#: js/dist/new-settings.js:235
msgid "Breadcrumbs"
msgstr "مسارات التنقل"

#: admin/views/tabs/metas/general.php:31
msgid "Title Separator"
msgstr "فاصل العنوان"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "من ينبغي أن يكون لديه حق الوصول إلى إعدادات %1$s"

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "الإعدادات الحساسة للخصوصية (مدراء FB وما إلى ذلك)، القالب الخاص (إعادة كتابة العنوان)، ولن يتم استيراد بعض الإعدادات الخاصة بالموقع إلى مواقع جديدة."

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "باستخدام هذا النموذج يمكنك إعادة تعيين الموقع إلى إعدادات SEO الافتراضية."

#. translators: %1$s expands to Yoast SEO.
#: admin/views/licenses.php:94
msgid "%1$s Extensions"
msgstr "ملحقات %1$s الإضافية"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:82 admin/views/licenses.php:77
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "قم بدمج ووكومرس (WooCommerce) بسلاسة مع %1$s واحصل على ميزات إضافية!"

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "إنشاء إعادة التوجيه هي ميزة %s"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "إعادة الموقع للوضع الافتراضي"

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "اختر الموقع الذي تريد استخدام إعداداته كافتراضي لجميع المواقع التي تمت إضافتها إلى شبكتك. إذا اخترت \"لا شيء\"، فسيتم استخدام الإعدادات الافتراضية للإضافة العادية."

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "أخذ ملاحظة:"

#: admin/views/tabs/metas/paper-content/special-pages.php:21
#: js/dist/new-settings.js:228
msgid "Search pages"
msgstr "بحث في الصفحات"

#. translators: %s expands to <code>noindex, follow</code>.
#: admin/views/tabs/metas/paper-content/special-pages.php:15
msgid "These pages will be %s by default, so they will never show up in search results."
msgstr "ستكون هذه الصفحات %s بشكل افتراضي، لذا لن تظهر في نتائج البحث مطلقًا."

#. translators: %1$s / %2$s: links to an article about duplicate content on
#. yoast.com
#: admin/views/tabs/metas/archives/help.php:11
msgid "If you're running a one author blog, the author archive will be exactly the same as your homepage. This is what's called a %1$sduplicate content problem%2$s."
msgstr "إذا كنت تقوم بتشغيل مدونة لكاتب واحد، فإن أرشيف الكاتب سيكون تمامًا مثل صفحتك الرئيسية. هذا ما يسمى بـ %1$sمشكلة المحتوى المكرر%2$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:39
msgid "author archives"
msgstr "أرشيف الكاتب"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "لتتمكن من إنشاء إعادة توجيه وإصلاح هذه المشكلة، أنت بحاجة إلى %1$s. يمكنك شراء الإضافة، بما في ذلك عام واحد من الدعم والتحديثات، على %2$s."

#: admin/class-plugin-availability.php:60 admin/views/licenses.php:59
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "هل أنت في أخبار Google؟ ضاعف عدد زوارك من خدمة Google للأخبار بإضافة موقعك إليها؟"

#. translators: %1$s opens the link to the Yoast.com article about Google's
#. Knowledge Graph, %2$s closes the link,
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:29
msgid "This data is shown as metadata in your site. It is intended to appear in %1$sGoogle's Knowledge Graph%2$s. You can be either an organization, or a person."
msgstr "يتم عرض هذه البيانات كـ بيانات meta وصفية في موقعك. من المفترض أن تظهر في %1$sالرسم البياني المعرفي لـ Google's Knowledge Graph%2$s. يمكنك أن تكون إما منظمة، أو شخصًا."

#. translators: 1: link open tag; 2: link close tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:39
#: js/dist/new-settings.js:170
msgid "You can determine the title and description for the homepage by %1$sediting the homepage itself%2$s."
msgstr "يمكنك تحديد العنوان والوصف للصفحة الرئيسية عن طريق %1$sتحرير الصفحة الرئيسية نفسها%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:48
msgid "You can determine the title and description for the posts page by %1$sediting the posts page itself%2$s."
msgstr "يمكنك تحديد العنوان والوصف لصفحة المقالات عن طريق %1$sتحرير صفحة المقالات نفسها%2$s."

#: admin/views/tabs/metas/general.php:50
msgid "Homepage &amp; Posts page"
msgstr "الصفحة الرئيسية &amp; صفحة المقالات"

#: admin/views/tabs/metas/archives.php:29 js/dist/new-settings.js:235
msgid "Special pages"
msgstr "صفحات خاصّة"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Permalink"
msgstr "الرابط الدائم"

#: admin/views/redirects.php:175 admin/views/redirects.php:222
msgid "New URL"
msgstr "URL جديد"

#: admin/views/tabs/metas/paper-content/front-page-content.php:26
#: src/integrations/admin/social-templates-integration.php:188
msgid "Social settings"
msgstr "الإعدادات الاجتماعية"

#: admin/pages/metas.php:22
msgid "Archives"
msgstr "الأرشيف"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/externals-components.js:88 js/dist/externals/componentsNew.js:136
msgid "Close"
msgstr "إغلاق"

#: admin/pages/social.php:21 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:235
msgid "Pinterest"
msgstr "Pinterest"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:214
msgid "Deactivate %s"
msgstr "تعطيل %s"

#: admin/class-meta-columns.php:239
msgid "All SEO Scores"
msgstr "جميع نتائج SEO"

#: admin/views/tabs/metas/paper-content/post-type-content.php:81
#: js/dist/new-settings.js:45
msgid "Breadcrumbs title"
msgstr "عنوان مسارات التنقل"

#: admin/pages/social.php:20 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Twitter"
msgstr "Twitter"

#: admin/pages/social.php:19 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Facebook"
msgstr "Facebook"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:210
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "قد تتسبب إضافة %1$s في حدوث مشاكل عند استخدامها مع %2$s."

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:76
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "يمكن لكل من %1$s و %2$s إنشاء خرائط مواقع XML. إن وجود اثنين من خرائط مواقع XML ليس مفيدًا لمحركات البحث وقد يؤدي إلى إبطاء موقعك."

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:65
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, Twitter, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "يقوم كل من %1$s و %2$s بإنشاء مخرجات لـ Open Graph، مما قد يجعل Facebook و Twitter و LinkedIn والشبكات الاجتماعية الأخرى تستخدم نصوصًا وصورًا خاطئة عند مشاركة صفحاتك."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:69
msgid "Configure %1$s's Open Graph settings"
msgstr "تكوين إعدادات Open Graph لـ %1$s"

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:75
msgid "%s Posts Overview"
msgstr "نظرة عامة على المقالات %s"

#: admin/class-meta-columns.php:108
msgid "Meta Desc."
msgstr "البيانات الوصفية"

#: admin/formatter/class-metabox-formatter.php:127
#: admin/formatter/class-metabox-formatter.php:157
#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:168
#: inc/class-wpseo-rank.php:200 js/dist/block-editor.js:227
#: js/dist/editor-modules.js:227 js/dist/externals-components.js:120
#: js/dist/externals/analysis.js:383 js/dist/frontend-inspector-resources.js:1
#: js/dist/post-edit.js:11 js/dist/term-edit.js:1
msgid "OK"
msgstr "مقبولة"

#: admin/class-yoast-form.php:741 admin/metabox/class-metabox.php:649
#: admin/taxonomy/class-taxonomy-fields-presenter.php:127
msgid "Upload Image"
msgstr "رفع الصورة"

#: admin/metabox/class-metabox.php:927
#: src/integrations/third-party/elementor.php:443
msgid "SEO issue: The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr "مشكلةSEO: يجب أن تكون الصورة المميزة 200 × 200 بكسل على الأقل ليتم التقاطها بواسطة Facebook ومواقع التواصل الاجتماعي الأخرى."

#: admin/class-bulk-editor-list-table.php:415 admin/views/redirects.php:141
msgid "Filter"
msgstr "تصفية"

#: admin/menu/class-admin-menu.php:89
#: admin/menu/class-network-admin-menu.php:56 admin/pages/metas.php:18
#: admin/pages/network.php:18 js/dist/new-settings.js:235
msgid "General"
msgstr "عام"

#: admin/class-bulk-editor-list-table.php:826 js/dist/block-editor.js:311
#: js/dist/classic-editor.js:307 js/dist/dashboard-widget.js:1
#: js/dist/dashboard-widget.js:20 js/dist/editor-modules.js:261
#: js/dist/elementor.js:311
msgid "View"
msgstr "عرض"

#: admin/class-bulk-editor-list-table.php:816
msgid "Preview"
msgstr "معاينة"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:825
msgid "View &#8220;%s&#8221;"
msgstr "عرض &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:995
msgid "Publication date"
msgstr "تاريخ النشر"

#: admin/class-bulk-editor-list-table.php:992
msgid "WP Page Title"
msgstr "عنوان صفحة WP"

#: admin/class-meta-columns.php:673
msgid "Post is set to noindex."
msgstr "تم وضع المنشور في وضع عدم الفهرسة"

#: admin/class-bulk-editor-list-table.php:1001
msgid "Action"
msgstr "إجراء"

#: admin/menu/class-admin-menu.php:97 js/dist/new-settings.js:217
msgid "Tools"
msgstr "أدوات"

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Image Index"
msgstr "لا يوجد فهرس صور"

#: admin/metabox/class-metabox.php:216
msgid "The URL that this page should redirect to."
msgstr "عنوان URL الذي يجب إعادة توجيه هذه الصفحة إليه."

#: admin/import/class-import-settings.php:111
msgid "Settings successfully imported."
msgstr "تم استرداد الإعدادات بنجاح."

#: admin/class-bulk-editor-list-table.php:804
#: js/dist/first-time-configuration.js:13
#: js/dist/first-time-configuration.js:225 js/dist/indexables-page.js:34
#: js/dist/indexables-page.js:35 js/dist/indexables-page.js:46
#: js/dist/installation-success.js:13
msgid "Edit"
msgstr "تعديل"

#: admin/class-admin.php:222 admin/views/redirects.php:42
#: src/integrations/settings-integration.php:261
#: js/dist/structured-data-blocks.js:9
msgid "Settings"
msgstr "الإعدادات"

#: admin/class-admin-init.php:386
#: src/services/health-check/default-tagline-runner.php:31
msgid "Just another WordPress site"
msgstr "مجرد موقع وردبريس آخر"

#: admin/metabox/class-metabox.php:194 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Archive"
msgstr "لا يوجد أرشيف"

#: admin/class-config.php:135 admin/metabox/class-metabox.php:907
#: admin/taxonomy/class-taxonomy.php:166
#: src/integrations/third-party/elementor.php:424
msgid "Use Image"
msgstr "استخدم الصورة"

#: admin/class-yoast-network-admin.php:43
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:47
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:75
#: src/config/schema-types.php:163 js/dist/new-settings.js:239
msgid "None"
msgstr "لا شيء"

#: admin/class-customizer.php:169
msgid "Prefix for archive pages:"
msgstr "بادئه لصفحات الأرشفة:"

#: admin/class-customizer.php:181
msgid "Prefix for search result pages:"
msgstr "بادئه لصفحات نتائج البحث:"

#: admin/class-admin.php:227 js/dist/structured-data-blocks.js:13
msgid "FAQ"
msgstr "الأسئلة المتكررّة"

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "وحدة تحكم البحث"

#: admin/ajax.php:177
msgid "You have used HTML in your value which is not allowed."
msgstr "لقد استخدمت HTML في قيمتك ، وهو أمر غير مسموح به."

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "اكتشف %1$s أنك تستخدم الإصدار%2$s من%3$s ، يرجى التحديث إلى أحدث إصدار لمنع مشاكل التوافق."

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "تحرير الملفات"

#: admin/menu/class-network-admin-menu.php:66
msgid "Extensions"
msgstr "الملحقات"

#: admin/import/class-import-settings.php:79
msgid "Settings could not be imported:"
msgstr "لا يمكن استيراد الإعدادات:"

#: admin/views/class-yoast-feature-toggles.php:141 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "XML sitemaps"
msgstr "خرائط الموقع XML sitemaps"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:288
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "كل<span class=\"count\">(%s)</span>"
msgstr[1] "كل<span class=\"count\">(%s)</span>"
msgstr[2] "كل<span class=\"count\">(%s)</span>"
msgstr[3] "كل<span class=\"count\">(%s)</span>"
msgstr[4] "كل<span class=\"count\">(%s)</span>"
msgstr[5] "كل<span class=\"count\">(%s)</span>"

#: admin/metabox/class-metabox.php:418
#: admin/taxonomy/class-taxonomy-metabox.php:132
#: inc/class-wpseo-admin-bar-menu.php:508
#: src/presenters/meta-description-presenter.php:36
#: src/services/health-check/report-builder.php:168
#: js/dist/structured-data-blocks.js:13
msgid "SEO"
msgstr "SEO"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Focus keyword"
msgstr "التركيز على الكلمة الرئيسية"

#: admin/metabox/class-metabox.php:198
msgid "Title to use for this page in breadcrumb paths"
msgstr "العنوان المستخدم لهذه الصفحة في مسارات التنقل"

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Breadcrumbs Title"
msgstr "عنوان مسارات التنقل"

#: admin/class-meta-columns.php:107
msgid "SEO Title"
msgstr "عنوان SEO"

#: admin/class-customizer.php:193
msgid "Breadcrumb for 404 pages:"
msgstr "مسار التنقل لصفحات الخطأ 404:"

#: admin/class-bulk-editor-list-table.php:996
msgid "Page URL/Slug"
msgstr "URL الصفحة/الاسم اللطيف (Slug)"

#: admin/class-bulk-editor-list-table.php:994
msgid "Post Status"
msgstr "حالة المقالة"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "وصف ميتا Yoast الحالي"

#: admin/class-admin.php:295
msgid "Twitter username (without @)"
msgstr "اسم المستخدم على Twitter (بدون \"@\")"

#: admin/class-admin.php:182
msgid "Posts"
msgstr "المقالات"

#: admin/ajax.php:133
msgid "Post doesn't exist."
msgstr "المقالة غير موجودة."

#. translators: %s expands to post type name.
#: admin/ajax.php:156
msgid "You can't edit %s."
msgstr "لا يمكنك تحرير %s."

#: admin/metabox/class-metabox.php:200 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Canonical URL"
msgstr "الرابط القياسي (Canonical URL)"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Snippet"
msgstr "لا يوجد مقتطف"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:815
msgid "Preview &#8220;%s&#8221;"
msgstr "معاينة &#8221;%s&#8220;"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:337
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "سلة المهملات <span class=\"count\">(%s)</span>"
msgstr[1] "سلة المهملات <span class=\"count\">(%s)</span>"
msgstr[2] "سلة المهملات <span class=\"count\">(%s)</span>"
msgstr[3] "سلة المهملات <span class=\"count\">(%s)</span>"
msgstr[4] "سلة المهملات <span class=\"count\">(%s)</span>"
msgstr[5] "سلة المهملات <span class=\"count\">(%s)</span>"

#: admin/menu/class-admin-menu.php:96 admin/metabox/class-metabox.php:439
#: admin/taxonomy/class-taxonomy-metabox.php:146
msgid "Social"
msgstr "الشبكات الاجتماعية"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "وصف ميتا (Yoast Meta) جديد"

#: admin/class-admin.php:288
msgid "Facebook profile URL"
msgstr "رابط حساب Facebook"

#: admin/metabox/class-metabox.php:174 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "Meta description"
msgstr "بيانات Meta الوصفية"

#. translators: %s is the name of the plugin
#: admin/class-customizer.php:86
msgid "%s Breadcrumbs"
msgstr "مسارات التنقل لـ %s"

#: admin/class-customizer.php:132
msgid "Breadcrumbs separator:"
msgstr "فاصل مسارات التنقل:"

#: admin/class-customizer.php:157
msgid "Prefix for breadcrumbs:"
msgstr "بادئة مسارات التنقل:"

#: admin/class-customizer.php:145
msgid "Anchor text for the homepage:"
msgstr "نص مرساة (Anchor) الصفحة الرئيسية:"

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "مشكلة SEO كبيرة: أنت تمنع وصول الروبوتات (robots) إلى موقعك."

#. translators: %s expands to post type.
#: admin/ajax.php:144
msgid "Post has an invalid Content Type: %s."
msgstr "تحتوي المقالة على نوع محتوى غير صالح: %s."

#: admin/metabox/class-metabox.php:882 admin/taxonomy/class-taxonomy.php:151
#: admin/taxonomy/class-taxonomy.php:273
#: src/integrations/third-party/elementor.php:398
msgid "(no parent)"
msgstr "(بدون أب)"

#: admin/metabox/class-metabox.php:191 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Meta robots advanced"
msgstr "روبوتات Meta المتقدمة"

#: admin/metabox/class-metabox.php:179
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "تحذير: على الرغم من أنه يمكنك تعيين إعدادات روبوتات Meta هنا، فإنه تم تحديد خيار عدم الفهرسة لكافة للموقع في إعدادات الخصوصية الخاصة بالموقع كله، لذلك لن يكون لهذه الإعدادات أي تأثير."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:168
msgid "You can't edit %s that aren't yours."
msgstr "لا يمكنك تحرير %s الذي لا تملكه."

#: admin/metabox/class-metabox.php:215
msgid "301 Redirect"
msgstr "إعادة التوجيه 301"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:204
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "عنوان URL الأساسي الذي يجب أن تشير إليه هذه الصفحة. اتركه فارغًا حتى تشير افتراضيًا إلى الرابط الثابت. %1$sCross domain canonical%2$s معتمد أيضًا."