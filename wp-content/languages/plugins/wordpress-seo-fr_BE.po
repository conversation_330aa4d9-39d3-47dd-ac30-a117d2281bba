# Translation of Plugins - Yoast SEO - Stable (latest release) in French (Belgium)
# This file is distributed under the same license as the Plugins - Yoast SEO - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-09-20 08:28:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: fr_BE\n"
"Project-Id-Version: Plugins - Yoast SEO - Stable (latest release)\n"

#: src/integrations/admin/crawl-settings-integration.php:113
#: js/dist/new-settings.js:224
msgid "Global feed"
msgstr "Flux global"

#: src/integrations/admin/crawl-settings-integration.php:114
#: js/dist/new-settings.js:224
msgid "Global comment feeds"
msgstr "Flux global des commentaires"

#: src/integrations/admin/crawl-settings-integration.php:116
#: js/dist/new-settings.js:224
msgid "Post authors feeds"
msgstr "Flux des auteurs/autrices"

#: src/integrations/admin/crawl-settings-integration.php:117
#: js/dist/new-settings.js:224
msgid "Post type feeds"
msgstr "Flux des types de publication"

#: src/integrations/admin/crawl-settings-integration.php:118
#: js/dist/new-settings.js:224
msgid "Category feeds"
msgstr "Flux des catégories"

#: src/integrations/admin/crawl-settings-integration.php:119
#: js/dist/new-settings.js:224
msgid "Tag feeds"
msgstr "Flux des étiquettes"

#: src/integrations/admin/crawl-settings-integration.php:120
#: js/dist/new-settings.js:224
msgid "Custom taxonomy feeds"
msgstr "Flux des taxonomies personnalisées"

#: src/integrations/admin/crawl-settings-integration.php:121
#: js/dist/new-settings.js:224
msgid "Search results feeds"
msgstr "Flux des résultats de recherche"

#: src/integrations/admin/crawl-settings-integration.php:122
#: js/dist/new-settings.js:224
msgid "Atom/RDF feeds"
msgstr "Flux Atom/RDF"

#: src/integrations/admin/crawl-settings-integration.php:126
#: js/dist/new-settings.js:224
msgid "Shortlinks"
msgstr "Liens courts"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "REST API links"
msgstr "Liens d’API REST"

#: src/integrations/admin/crawl-settings-integration.php:128
#: js/dist/new-settings.js:224
msgid "RSD / WLW links"
msgstr "Liens RSD/WLW"

#: src/integrations/admin/crawl-settings-integration.php:129
#: js/dist/new-settings.js:224
msgid "oEmbed links"
msgstr "Liens oEmbed"

#: src/integrations/admin/crawl-settings-integration.php:130
#: js/dist/new-settings.js:224
msgid "Generator tag"
msgstr "Balise du générateur"

#: src/integrations/admin/crawl-settings-integration.php:149
#: js/dist/new-settings.js:224
msgid "Emoji scripts"
msgstr "Script des émojis"

#: src/integrations/admin/crawl-settings-integration.php:131
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Pingback HTTP header"
msgstr "En-tête HTTP des pings"

#: src/integrations/admin/crawl-settings-integration.php:132
#: js/dist/new-settings.js:224
msgid "Powered by HTTP header"
msgstr "En-tête HTTP « propulsé par »"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Basic crawl settings"
msgstr "Réglages basiques d’exploration"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Remove links added by WordPress to the header and &lt;head&gt;."
msgstr "Retire les liens ajoutés par WordPress à l’en-tête et la balise &lt;head&gt;."

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Feed crawl settings"
msgstr "Réglages d’exploration des flux"

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Remove feed links added by WordPress that aren't needed for this site."
msgstr "Retire les liens des flux ajoutés par WordPress qui ne sont pas nécessaires pour ce site."

#: src/integrations/admin/crawl-settings-integration.php:313
msgid "By removing Global comments feed, Post comments feeds will be removed too."
msgstr "En retirant le flux global des commentaires, les flux des commentaires seront également retirés."

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "SEO configuration"
msgstr "Configuration SEO"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:151
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "Nous avons remarqué que nous avons pas entièrement configuré Yoast SEO. Optimisez encore plus vos réglages SEO avec notre nouvelle %1$sconfiguration initiale%2$s."

#: admin/views/redirects.php:188
msgid "No items found."
msgstr "Aucun élément trouvé."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/crawl-settings.php:22
msgid "To make the crawling of your site more efficient and environmental friendly, %1$s allows you to remove URLs (added by WordPress) that might not be needed for your site."
msgstr "Pour rendre l’exploration de vitre site plus efficace et écologiquement sobre, %1$s vous permet de retirer les URL (ajoutées par WordPress) qui ne seraient pas utiles à votre site."

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/dashboard/crawl-settings.php:29
msgid "%1$sLearn more about crawl settings and how they could benefit your site.%2$s"
msgstr "%1$sEn savoir plus sur les réglages d’exploration et comment ils peuvent aider votre site.%2$s"

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sEn savoir plus sur les réglages d’exploration.%2$s"

#: src/integrations/admin/crawl-settings-integration.php:115
#: js/dist/new-settings.js:224
msgid "Post comments feeds"
msgstr "Flux des commentaires"

#: admin/pages/network.php:28 admin/views/tabs/dashboard/crawl-settings.php:17
#: admin/views/tabs/network/crawl-settings.php:19
#: src/integrations/admin/crawl-settings-integration.php:165
msgid "Crawl settings"
msgstr "Exploration"

#: admin/views/redirects.php:37
msgid "Regex Redirects"
msgstr "Redirections regex"

#: admin/views/redirects.php:50
msgid "Plain redirects"
msgstr "Redirections classiques"

#: admin/views/redirects.php:59 admin/views/redirects.php:155
#: admin/views/redirects.php:203
msgid "Type"
msgstr "Type"

#: admin/views/redirects.php:66 admin/views/redirects.php:75
msgid "301 Moved Permanently"
msgstr "301 Déplacé définitivement"

#. translators: 1: opens a link. 2: closes the link.
#: admin/views/redirects.php:91
msgid "The redirect type is the HTTP response code sent to the browser telling the browser what type of redirect is served. %1$sLearn more about redirect types%2$s."
msgstr "Le type de redirection est le code de réponse HTTP envoyé au navigateur. %1$sEn savoir plus sur les types de redirection%2$s."

#: admin/views/redirects.php:100 admin/views/redirects.php:165
#: admin/views/redirects.php:212
msgid "Old URL"
msgstr "Ancienne URL"

#: admin/views/redirects.php:122
msgid "Add Redirect"
msgstr "Ajouter une redirection"

#: admin/views/redirects.php:137
msgid "All redirect types"
msgstr "Tous les types de redirection"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:99
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "Vous devriez terminer la %1$sconfiguration initiale%2$s pour que les données SEO soient optimisées et que les réglages essentiels de Yoast SEO soient définis."

#: src/integrations/admin/first-time-configuration-integration.php:125
msgid "First-time configuration"
msgstr "Configuration initiale"

#: admin/views/tabs/tool/import-seo.php:94
msgid "Step 4: Go through the first time configuration"
msgstr "Étape 4 : Allez au bout de la configuration initiale"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "La validation de la structure de données de AIOSEO a échoué."

#: src/integrations/admin/import-integration.php:212
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "L’importation AIOSEO a été annulée car certaines données sont manquantes. Veuillez réessayer et suivre les étapes suivantes pour corriger le problème :"

#: src/integrations/admin/import-integration.php:215
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "Si vous n’avez jamais enregistré de réglages dans « Apparence de la recherche », veuillez le faire et relancer ensuite l’importation."

#: src/integrations/admin/import-integration.php:218
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "Si vous avez déjà enregistré des réglages de AIOSEO dans « Apparence de la recherche » et que le problème persiste, veuillez contacter notre équipe de support afin que nous regardions de plus près."

#: src/services/health-check/links-table-check.php:48
msgid "Links table"
msgstr "Tableau des liens"

#: src/services/health-check/page-comments-check.php:48
msgid "Page comments"
msgstr "Commentaires de la page"

#: src/services/health-check/postname-permalink-check.php:48
msgid "Postname permalink"
msgstr "Permalien en nom de publication"

#. translators: %s expands to Wincher
#. translators: %s expands to WordProof
#: src/integrations/third-party/wincher.php:125
#: src/integrations/third-party/wordproof-integration-toggle.php:130
#: src/integrations/third-party/wordproof-integration-toggle.php:152
msgid "Currently, the %s integration is not available for multisites."
msgstr "Actuellement, l’intégration à %s n’est pas disponible pour les multisites."

#: src/integrations/admin/import-integration.php:95
msgid "The cleanup can take a long time depending on your site's size."
msgstr "Le nettoyage peut prendre un certain temps en fonction de la taille de votre site."

#: src/integrations/admin/import-integration.php:96
msgid "Note: "
msgstr "Remarque : "

#: src/integrations/admin/import-integration.php:97
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "Après l’importation de données d’une autre extension SEO, assurez-vous de nettoyer ses données d’origine. (étape 5)"

#: src/integrations/admin/import-integration.php:105
msgid "Clean up"
msgstr "Nettoyer"

#: src/integrations/admin/import-integration.php:106
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "Veuillez sélectionner une extension SEO ci-dessous pour voir les données éligibles à l’importation."

#: src/integrations/admin/import-integration.php:107
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "Lorsque vous aurez la certitude que votre site fonctionne correctement avec le données importées d’une autre extension SEO, vous pouvez nettoyer ses anciennes données."

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:109
msgid "The import from %s includes:"
msgstr "L’importation de %s inclut :"

#: src/integrations/admin/import-integration.php:113
#: src/integrations/admin/import-integration.php:123
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "Les métadonnées des publications (titres SEO, descriptions, etc…)"

#: src/integrations/admin/import-integration.php:114
#: src/integrations/admin/import-integration.php:124
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "Remarque : Cette métadonnée ne sera importée que s’il n‘existe pas encore de métadonnée correspondante dans Yoast SEO."

#: src/integrations/admin/import-integration.php:118
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "Remarque : Ces réglages écraseront ceux par défaut de Yoast SEO."

#: src/integrations/admin/import-integration.php:234
msgid "Cleanup failed with the following error:"
msgstr "Le nettoyage a échoué avec l’erreur suivante :"

#: src/integrations/admin/import-integration.php:98
msgid "Select SEO plugin"
msgstr "Sélectionnez une extension SEO"

#: src/integrations/admin/import-integration.php:99
msgid "No data found from other SEO plugins."
msgstr "Aucune donnée trouvée de la part d’autres extensions SEO."

#: src/integrations/admin/import-integration.php:236
msgid "Import failed with the following error:"
msgstr "L’importation a échoué avec l’erreur suivante :"

#: src/services/health-check/default-tagline-check.php:48
msgid "Default tagline"
msgstr "Slogan par défaut"

#: src/integrations/admin/import-integration.php:94
msgid "The import can take a long time depending on your site's size."
msgstr "L’importation peut prendre un certain temps en fonction de la taille de votre site."

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "Installation réussie"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "Article de blog"

#. translators: %s: 'Wincher'
#: src/integrations/third-party/wincher.php:77
msgid "The %s integration offers the option to track specific keyphrases and gain insights in their positions."
msgstr "L’intégration à %s ajoute l’option de suivre des requêtes spécifiques afin de mesurer leurs positions."

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:309
#: js/dist/integrations-page.js:3
msgid "Activate %s"
msgstr "Activer %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:303
msgid "Update %s"
msgstr "Mettre à jour %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:296
msgid "Renew %s"
msgstr "Renouveler %s"

#: src/integrations/admin/workouts-integration.php:238
msgid "Get help activating your subscription"
msgstr "Obtenez de l’aide dans l’activation de votre abonnement"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:232
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "Il semblerait que vous utilisiez une version obsolète et désactivée de %1$s. Veuillez renouveler votre abonnement dans%2$sMyYoast%3$s puis mettez l’extension à jour (au moins en version 17.7) pour débloquer l’accès à notre section Entraînements mise à jour."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:228
msgid "Activate your subscription of %s"
msgstr "Activez votre abonnement de %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:216
msgid "Update to the latest version of %s"
msgstr "Passez à la dernière version de %s"

#: src/integrations/admin/workouts-integration.php:209
msgid "Renew your subscription"
msgstr "Renouvelez votre abonnement"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:202
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "L’accès aux derniers entraînements nécessite une version mise à jour de %s (au moins 17.7), mais il semblerait que votre abonnement ait expiré. Veuillez le renouveler pour mettre l’extension à jour et débloquer l’accès aux dernières fonctionnalités."

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:219
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "Il semblerait que vous utilisiez une version obsolète de %1$s. Veuillez %2$spasser à la dernière version%3$s pour débloquer la section des entraînements mise à jour."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:199
msgid "Renew your subscription of %s"
msgstr "Renouvelez votre abonnement à %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:142
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "Démarrez rapidement avec %1$sla première configuration de %2$s%3$s afin de configurer les réglages optimaux pour votre site !"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Category Title"
msgstr "Titre de catégorie"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Current or first category title"
msgstr "Titre de la catégorie actuelle ou de la première catégorie"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Post Content"
msgstr "Contenu de la publication"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the permalink"
msgstr "Remplacé par le permalien"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the post content"
msgstr "Remplacé par le contenu de la publication"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Author first name"
msgstr "Prénom de l‘auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Author last name"
msgstr "Nom de famille de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Current date"
msgstr "Date actuelle"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current day"
msgstr "Jour actuel"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Current month"
msgstr "Mois actuel"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Post day"
msgstr "Jour de publication"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Post month"
msgstr "Mois de publication"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Post year"
msgstr "Année de publication"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the day the post was published"
msgstr "Remplacé par le jour de publication"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the first name of the author"
msgstr "Remplacé par le prénom de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the last name of the author"
msgstr "Remplacé par le nom de famille de l’auteur/autrice"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the month the post was published"
msgstr "Remplacé par le mois de publication"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the year the post was published"
msgstr "Remplacé par l’année de publication"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:110
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "Il semblerait que vous n’utilisez pas notre %1$smodule %2$s%3$s. %4$sAchetez-le%5$s et débloquez plus d’outils et de fonctionnalités SEO pour mettre en avant vos produits dans les résultats de recherche."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "Vous trouverez ci-dessous les détails techniques de cette erreur. Consultez %1$scette page%2$s pour une explication détaillée."

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:370
msgid "A new version of %1$s is available. %2$sRenew your subscription%3$s if you want to update to the latest version."
msgstr "Une nouvelle version de %1$s est disponible. %2$sRenouvelez votre abonnement%3$s si vous souhaitez passer à la dernière version."

#: src/schema-templates/preparation-time.block.php:14
msgid "Prep time"
msgstr "Temps de préparation"

#: src/schema-templates/cooking-time.block.php:12
msgid "Recipe cooking time"
msgstr "Durée de cuisson de la recette"

#: src/schema-templates/preparation-time.block.php:12
msgid "Recipe prep time"
msgstr "Temps de préparation de la recette"

#: src/schema-templates/cooking-time.block.php:12
msgid "The time it takes to actually cook the dish."
msgstr "Le temps qu’il faut pour cuisiner le plat."

#: src/schema-templates/preparation-time.block.php:12
msgid "The time it takes to prepare the items to be used in the instructions."
msgstr "Le temps qu’il faut pour préparer les éléments qui seront utilisés ensuite."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "Entraînements"

#: src/schema-templates/recipe-ingredients.block.php:15
msgid "Enter an ingredient"
msgstr "Saisissez un ingrédient"

#: src/schema-templates/recipe-instructions.block.php:16
msgid "Enter step"
msgstr "Saisissez une étape"

#: admin/views/class-yoast-integration-toggles.php:99
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "Améliorez la qualité de la recherche sur votre site ! Cela aide automatiquement vos visiteurs à trouver vos contenus les plus importants dans les résultats de recherche interne. Cela retire également des publications désindexées de vos résultats de recheche."

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients"
msgstr "Ingrédients"

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients title"
msgstr "Titre des ingrédients"

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions"
msgstr "Instructions"

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions title"
msgstr "Titre des instructions"

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "Recipe ingredient(s)"
msgstr "Ingrédient(s) de la recette"

#: src/schema-templates/recipe-instructions.block.php:13
msgid "Recipe instructions"
msgstr "Instructions de la recette"

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "The ingredients used in the recipe, e.g. sugar, flour or garlic."
msgstr "Les ingrédients utilisés dans la recette, par ex. : sucre, farine ou ail."

#: src/schema-templates/recipe-instructions.block.php:13
msgid "The steps of making the recipe, in the form of an ordered list with HowToStep and/or HowToSection items."
msgstr "Les étapes de la recette, sous forme de liste ordonnée avec les éléments HowToStep et/ou HowToSection."

#. translators: %s: Zapier.
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:88
#: admin/views/class-yoast-integration-toggles.php:101
msgid "Find out more about our %s integration."
msgstr "En savoir plus sur l’intégration à %s."

#. translators: 1: Yoast SEO, 2: Zapier.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Set up automated actions when you publish or update your content. By connecting %1$s with %2$s, you can easily send out your published posts to any of its 2000+ destinations, such as Twitter, Facebook and more."
msgstr "Mettez en place des actions automatisées lorsque vous publiez ou mettez à jour votre contenu. En connectant %1$s à %2$s, vous pouvez facilement envoyer vos articles publiés vers plus de 2000 destinations dont Twitter, Facebook et plus encore."

#: admin/views/class-yoast-feature-toggles.php:134
msgid "Read more about how internal linking can improve your site structure."
msgstr "En savoir plus sur l’impact du maillage interne sur la structure de votre site."

#: admin/views/class-yoast-feature-toggles.php:124
msgid "Find out how Insights can help you improve your content."
msgstr "Découvrez comment Insights peut vous aider à améliorer votre contenu."

#: admin/views/class-yoast-feature-toggles.php:123
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "Trouvez des données pertinentes sur votre contenu directement dans la section Insights de l’encart Yoast SEO. Vous verrez les mots que vous utilisez le plus et s’ils correspondent à vos mots-clés ! "

#: admin/views/class-yoast-feature-toggles.php:130 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Link suggestions"
msgstr "Suggestions de liens"

#: admin/views/class-yoast-feature-toggles.php:121 js/dist/block-editor.js:447
#: js/dist/classic-editor.js:443 js/dist/elementor.js:447
#: js/dist/new-settings.js:215 js/dist/new-settings.js:224
msgid "Insights"
msgstr "Données"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "Oups, il y a eu une erreur et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez vous assurer de bien activer votre abonnement dans MyYoast en suivant %1$sces étapes%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:148
msgid "The social appearance settings for archives require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Les réglages de réseaux sociaux des archives nécessitent les métadonnées Open Graph (actuellement désactivées). Vous pouvez les activer dans l’onglet %1$s« Réseaux sociaux », dans l’onglet « Facebook »%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:136
msgid "The social appearance settings for taxonomies require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Les réglages de réseaux sociaux des taxonomies nécessitent les métadonnées Open Graph (actuellement désactivées). Vous pouvez les activer dans l’onglet %1$s« Réseaux sociaux », dans l’onglet « Facebook »%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:124
msgid "The social appearance settings for content types require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Les réglages de réseaux sociaux des publications nécessitent les métadonnées Open Graph (actuellement désactivées). Vous pouvez les activer dans l’onglet %1$s« Réseaux sociaux », dans l’onglet « Facebook »%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:108
msgid "The social appearance settings for your homepage require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Les réglages de réseaux sociaux de votre page d’accueil nécessitent les métadonnées Open Graph (actuellement désactivées). Vous pouvez les activer dans l’onglet %1$s« Réseaux sociaux », dans l’onglet « Facebook »%2$s."

#: admin/views/tabs/social/facebook.php:48
msgid "Default image"
msgstr "Image par défaut"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/social/facebook.php:33
msgid "The social homepage settings have been moved to the %1$s‘Search appearance’ settings under the ‘General’ tab%2$s."
msgstr "Les réglages de réseaux sociaux de la page d’accueil ont été déplacés dans les %1$s« Réglages SEO », dans l’onglet « Réglages généraux »%2$s."

#: src/schema-templates/recipe-description.block.php:12
msgid "A description of the recipe."
msgstr "Une description de la recette."

#: src/schema-templates/cooking-time.block.php:14
#: js/dist/externals/schemaBlocks.js:13
msgid "Cooking time"
msgstr "Temps de cuisson"

#: src/schema-templates/recipe-description.block.php:14
msgid "Enter a recipe description"
msgstr "Saisissez une description de la recette"

#: src/schema-templates/recipe-description.block.php:12
msgid "Recipe description"
msgstr "Description de la recette"

#: admin/views/tabs/metas/general.php:15
msgid "Rewrite titles"
msgstr "Réécrire les titres"

#: admin/views/tabs/metas/paper-content/front-page-content.php:59
#: src/integrations/admin/social-templates-integration.php:224
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social description"
msgstr "Description pour les réseaux sociaux"

#: admin/views/tabs/metas/paper-content/front-page-content.php:58
#: src/integrations/admin/social-templates-integration.php:223
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social title"
msgstr "Titre pour les réseaux sociaux"

#: admin/views/tabs/metas/paper-content/front-page-content.php:31
msgid "These are the image, title and description used when a link to your homepage is shared on social media."
msgstr "Ce sont les image, titre et description utilisés lorsqu’un lien vers votre page d’accueil est partagé sur les réseaux sociaux."

#. translators: %s expands to 'Yoast SEO Premium'.
#: src/integrations/admin/social-templates-integration.php:236
msgid "To unlock this feature please update %s to the latest version."
msgstr "Pour débloquer ces fonctionnalités, veuillez mettre à jour %s dans sa dernière version."

#: admin/views/redirects.php:22
#: src/integrations/admin/crawl-settings-integration.php:332
#: src/integrations/admin/social-templates-integration.php:251
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "Unlock with Premium"
msgstr "Débloquer avec la version premium"

#. translators: %s is the plural version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:42
msgid "%s archive settings"
msgstr "Réglages des archives : %s"

#. translators: %s is the singular version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:18
msgid "Single %s settings"
msgstr "Réglages de publication unique : %s"

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:120
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s Continuer vers %2$s%3$s"

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:145
msgid "Addon activated."
msgstr "Module activé."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:151
msgid "Addon activation failed because of an error: %s."
msgstr "Échec d’activation d’un module à cause d’une erreur : %s."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:175
msgid "Addon installed."
msgstr "Module installé."

#: src/integrations/admin/addon-installation/installation-integration.php:97
msgid "Installing and activating addons"
msgstr "Installation et activation des modules"

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:92
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "Aucune extension %1$s n’a été installée. Il semblerait que vous n’ayez aucune licence active."

#: src/integrations/admin/addon-installation/installation-integration.php:147
msgid "You are not allowed to activate plugins."
msgstr "Vous n’avez pas les droits nécessaires pour activer des extensions."

#: src/integrations/admin/addon-installation/installation-integration.php:179
msgid "You are not allowed to install plugins."
msgstr "Vous n’avez pas les droits suffisants pour installer des extensions."

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:183
msgid "Addon installation failed because of an error: %s."
msgstr "Échec d’installation d’un module à cause d’une erreur : %s."

#. translators: %1$s expands to Yoast
#: src/schema-templates/recipe.block.php:9
msgid "%1$s Recipe"
msgstr "Recette %1$s"

#: src/schema-templates/recipe.block.php:39
msgid "Add a block to your recipe..."
msgstr "Ajoutez un bloc à votre recette…"

#: src/schema-templates/recipe.block.php:36
msgid "Create a Recipe in an SEO-friendly way. You can only use one Recipe block per post."
msgstr "Créez une recette optimisée pour le référencement. Vous pouvez uniquement utiliser un bloc Recette par publication."

#: src/schema-templates/recipe-name.block.php:10
msgid "Enter a recipe name"
msgstr "Saisissez un nom de recette"

#: src/schema-templates/recipe.block.php:37
msgid "Serves #"
msgstr "# portions "

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:237
msgid "Required by %s"
msgstr "Nécessaire pour %s"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:93
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "Les mises à jour automatiques sont désactivées grâce à ce réglage de %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:83
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "Les mises à jour automatiques sont activées grâce à ce réglage de %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:136 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "New"
msgstr "Nouveau"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:114
msgid "Enable Breadcrumbs for your theme"
msgstr "Activer le fil d’Ariane pour votre thème"

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:111
msgid "Note: You can always choose to enable / disable them for your theme below. This setting will not apply to breadcrumbs inserted through a widget, a block or a shortcode."
msgstr "Note : vous pouvez choisir de les activer/désactiver pour votre thème ci-dessous. Ce réglage ne s’appliquera par contre pas aux fils d’Ariane insérés via un widget, un bloc ou un code court."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "Le terme est considéré comme non-valide. Voici la raison donnée par WordPress : %s"

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "Le terme est introuvable."

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "La publication est introuvable."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the Yoast SEO Multilingual add-on%2$s as well!"
msgstr "Nous remarquons que vous avez installé WPML. Pour s’assurer que vos URL canoniques sont correctement configurées, %1$sinstallez et activez le module Yoast SEO Multilingual%2$s également !"

#: admin/class-yoast-form.php:1045 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "Cette fonctionnalité a été désactivée puisque les sous-sites n’envoient jamais de données de suivi."

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages de préfixe de catégorie, une partie de vos données SEO doit être re-traitée."

#: admin/views/class-yoast-feature-toggles.php:195
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "Découvrez comment un extrait enrichi peut améliorer votre visibilité et votre taux de clic."

#: admin/views/class-yoast-feature-toggles.php:194 js/dist/new-settings.js:217
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "Cela ajoute une ligne à l’extrait de la publication sur Slack avec l’auteur et la durée de lecture estimée."

#: admin/views/class-yoast-feature-toggles.php:192
msgid "Enhanced Slack sharing"
msgstr "Partage amélioré sur Slack"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:120
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "Attendre une semaine environ, jusqu’à ce que %1$s traite automatiquement votre contenu en arrière-plan."

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s minute"
msgstr[1] "%s minutes"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "Durée de lecture est."

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "Écrit par"

#: inc/class-wpseo-admin-bar-menu.php:364
msgid "Google Rich Results Test"
msgstr "Test de résultats enrichis Google"

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages de préfixe d’étiquettes, une partie de vos données SEO doit être re-traitée."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "L’intégration à %s ajoute des suggestions et statistiques pour les mots-clés saisis dans la requête cible."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "Cet onglet vous permet de désactiver de façon sélective des intégrations de %1$s à des solutions tierces pour l’ensemble du réseau. Par défaut, toutes les intégrations sont activées, ce qui permet aux rôles administration de choisir s’ils veulent les utiliser sur leur site. Par contre, si vous désactivez une intégration ici, aucun site du réseau ne pourra l’utiliser."

#: admin/class-admin.php:248
msgid "Activate your subscription"
msgstr "Activer votre abonnement"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "Oups, un problème est survenu et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez relancer le traitement."

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "Tous les permaliens ont été réinitialisés avec succès"

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. "
msgstr "Vous pouvez accélérer votre site et obtenir des statistiques de maillage interne en nous autorisant à procéder à quelques optimisations sur le stockage des données SEO. "

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/first-time-configuration.js:3 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "Démarrer l’optimisation des données SEO"

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "En savoir plus sur les avantages des données SEO optimisées."

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "Vous pouvez accélérer votre site et obtenir des statistiques de maillage interne en nous autorisant à procéder à quelques optimisations sur le stockage des données SEO. Si vous avez beaucoup de contenu, cela peut prendre un moment. Mais faites-nous confiance, ça vaut le coup."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "Optimiser les données SEO"

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "Si le problème persiste, veuillez contacter le support."

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "Un problème est survenu et nous n’avons pas pu terminer l’optimisation de vos données SEO. Veuillez %1$srelancer le traitement%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:822
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "Si vous avez besoin de support et disposez d’une licence active pour ce produit, veuillez nous écrire un e-mail à %s."

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:819
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "Vous pouvez probablement trouver une réponse à votre question dans notre %1$scentre d’aide%2$s."

#: inc/class-addon-manager.php:816
msgid "Need support?"
msgstr "Besoin d’aide ?"

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:235
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "Désactiver les plans de site XML de Yoast SEO ne désactivera pas les plans de site du cœur de WordPress. Dans certains cas, cela %1$s peut provoquer des erreurs SEO sur votre site%2$s. Elles pourraient être relevées dans Google Search Console et d’autres outils."

#: admin/views/licenses.php:58
msgid "Everything you need for Google News"
msgstr "Tout ce dont vous avez besoin pour Google News"

#: admin/views/licenses.php:75
msgid "Make your products stand out in Google"
msgstr "Rendez vos produits très visibles dans Google"

#: admin/views/licenses.php:45
msgid "Start ranking better for your videos"
msgstr "Devenez plus visible avec vos vidéos"

#: admin/views/licenses.php:31
msgid "Stop losing customers to other local businesses"
msgstr "Arrêtez de laisser filer vos clients vers d’autres entreprises locales"

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:163
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "La section avancée de l’encart de %1$s permet de désindexer des publications ou changer l’URL canonique. Les réglages dans l’onglet « schema » permettent de changer les métadonnées de schema pour une publication. Ce sont des choses que vous ne voulez peut-être pas que tous les auteurs et autrices manipulent. C’est pourquoi, par défaut, seuls les éditeurs et éditrices, administrateurs et administratrices peuvent les modifier. En mettant le réglage à « %2$s », vous autoriseriez tous les utilisateurs et utilisatrices à modifier ces réglages."

#: admin/views/class-yoast-feature-toggles.php:159
msgid "Security: no advanced or schema settings for authors"
msgstr "Sécurité : pas de réglages avancés ou schema pour les auteurs et autrices"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "Page À propos"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "Article publi-rédactionnel"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "Article"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "Page de commande"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "Page de navigation"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "Page de contact"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "Page de FAQ"

#: src/config/schema-types.php:64
msgid "Item Page"
msgstr "Page d’élément"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "Page web médicale"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "Article d’actualité"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "Page de profil"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "Page de questions-réponses"

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "Fiche de bien immobilier"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "Rapport"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "Article satirique"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "Page de résultats de recherche"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "Publication de réseau social"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "Article technique"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "Page web"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "Article universitaire"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:175
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "Autorisez-nous à suivre quelques données de votre site pour améliorer notre extension."

#: admin/views/class-yoast-feature-toggles.php:170
#: admin/views/class-yoast-feature-toggles.php:171 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Usage tracking"
msgstr "Suivi d’utilisation"

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans vos réglages d’URL de page d’accueil, une partie de vos données SEO doit être re-traitée."

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "À cause d’un changement dans la structure de vos permaliens, une partie de vos données SEO doit être re-traitée."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:70
msgid "%1$s Internal Linking Blocks"
msgstr "Blocs %1$s de maillage interne"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#. translators: %s: Expands to an indexable object's name, like a post type or
#. taxonomy.
#: admin/views/tabs/metas/paper-content/post_type/post-type.php:31
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:39
msgid "Show SEO settings for %1$s?"
msgstr "Afficher les réglages SEO pour les %1$s ?"

#: src/services/health-check/links-table-reports.php:59
msgid "The text link counter feature is not working as expected"
msgstr "Le compteur de liens textuels ne fonctionne pas comme prévu"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:74
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "Le compteur de liens textuels vous aide à améliorer la structure de votre site. %1$sDécouvrez comment il peut améliorer votre référencement%2$s."

#: src/services/health-check/links-table-reports.php:46
msgid "The text link counter is working as expected"
msgstr "Le compteur de liens textuels fonctionne comme prévu"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:48
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "Les colonnes des liens affichent le nombre d’articles sur ce site faisant un lien %3$svers%4$s cet article et le nombre d’URL liées %3$sdepuis%4$s cet article. En savoir plus sur %1$sces fonctionnalités pour améliorer le maillage interne%2$s, ce qui perfectionnera votre référencement."

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:101
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sDécouvrez comment régler ce problème dans notre centre d’aide%2$s."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:39
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "Nous avons écrit un article sur %1$sl’utilisation des scores SEO et Lisibilité%2$s."

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:32
msgid "%1$s adds several columns to this page."
msgstr "%1$s ajoute plusieurs colonnes à cette page."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "Je ne veux pas que ce site apparaisse dans les résultats de recherche."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Si vous souhaitez que les moteurs de recherche affichent votre site dans leurs résultats, vous devez %1$saller dans les réglages de Lecture%2$s et décocher la case de l’option « Visibilité pour les moteurs de recherche »."

#: src/presenters/admin/migration-error-presenter.php:59
msgid "Show debug information"
msgstr "Afficher les informations de débogage"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:53
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "Votre site continuera de fonctionner normalement, mais ne profitera pas pleinement de %s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "%s a rencontré des problème pour créer les tables de base de données nécessaires pour optimiser votre site."

#: src/presenters/admin/indexing-notification-presenter.php:108
msgid "We estimate this will take less than a minute."
msgstr "Nous estimons que cela prendra moins d’une minute."

#: src/presenters/admin/indexing-notification-presenter.php:112
msgid "We estimate this will take a couple of minutes."
msgstr "Nous estimons que cela prendra quelques minutes."

#: src/presenters/admin/indexing-notification-presenter.php:115
msgid "We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr "Nous estimons que cela pourrait prendre beaucoup de temps en raison de la taille de votre site. Au lieu d’attendre, vous pourriez :"

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:127
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sLancer le processus d’indexation sur votre serveur%2$s en utilisant %3$sWP CLI%2$s."

#: src/integrations/front-end/theme-titles.php:49
msgid "a theme that has proper title-tag theme support, or adapt your theme to have that support"
msgstr "un thème qui a une bonne gestion de la balise « title » ou adaptez votre thème pour avoir cette prise en charge"

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "Veuillez lire %1$sce guide%2$s pour voir comment résoudre ce problème."

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term ancestors hierarchy"
msgstr "Remplacé par la hiérarchie des termes supérieurs"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Term hierarchy"
msgstr "Hiérarchie des termes"

#: admin/views/class-yoast-feature-toggles.php:182
msgid "REST API: Head endpoint"
msgstr "REST API : Point de terminaison Head"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:186
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "Le point de terminaison REST API de %1$s vous donne toutes les métadonnées d’une URL spécifique. Ainsi, les sites WordPress headless pourront continuer d’utiliser %1$s pour la gestion des métadonnées SEO."

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:64
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sVous pouvez changer le slogan dans l’outil de personnalisation%2$s."

#: src/services/health-check/default-tagline-reports.php:46
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "Vous avez encore le slogan WordPress par défaut. Même un slogan vide serait probablement mieux."

#: src/services/health-check/default-tagline-reports.php:44
msgid "You should change the default WordPress tagline"
msgstr "Vous devriez changer le slogan WordPress par défaut"

#: src/services/health-check/default-tagline-reports.php:33
msgid "You are using a custom tagline or an empty one."
msgstr "Vous utilisez un slogan personnalisé ou vide."

#: src/services/health-check/default-tagline-reports.php:31
msgid "You changed the default WordPress tagline"
msgstr "Vous avez changé le slogan WordPress par défaut"

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "Les commentaires de vos publications sont affichés sur une seule page. Cela correspond à notre recommandation, tout se passe donc très bien !"

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "Il est fortement recommandé d’avoir le nom de la publication dans l’URL de vos articles et pages. Envisagez de passer votre structure de permaliens à %s."

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "Vous n’avez pas le nom de la publication dans l’URL et de vos articles et pages."

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "Vous avez bien le nom de la publication dans l’URL et de vos articles et pages."

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "Votre structure de permaliens inclut le nom de la publication"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "Les commentaires sur vos publications sont divisés sur plusieurs pages. Ce qui n’est pas nécessaire dans 999 cas sur 1000. Pour corriger ce problème, décochez « Diviser les commentaires en pages […] » dans les réglages de Discussion."

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "Commentaires divisés en plusieurs pages"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "Les commentaires sont affichés sur une seule page"

#: src/helpers/post-helper.php:101
msgid "No title"
msgstr "Aucun titre"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sCela a été remonté par l’extension %2$s%3$s"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sAllez dans les réglages de discussion%2$s"

#: admin/metabox/class-metabox.php:192
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "Si vous souhaitez appliquer des réglages de <code>métadonnées</code> robots pour cette page, veuillez les définir dans le champ suivant."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:155 admin/taxonomy/class-taxonomy.php:99
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "Le navigateur que vous utilisez actuellement est malheureusement obsolète. Puisque nous tenons à vous proposer la meilleure expérience possible, nous ne le prenons plus en charge. À la place, veuillez utiliser %1$sFirefox%4$s, %2$sChrome%4$s ou %3$sMicrosoft Edge%4$s."

#. translators: %1$s expands to Yoast SEO academy
#: src/presenters/admin/sidebar-presenter.php:104 js/dist/new-settings.js:13
msgid "Check out %1$s"
msgstr "Jetez un œil à %1$s"

#: src/presenters/admin/sidebar-presenter.php:97 js/dist/new-settings.js:11
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "Nous avons des formations gratuites et payantes pour vous apprendre le référencement."

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:95 js/dist/new-settings.js:9
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "Vous souhaitez apprendre le référencement avec l’équipe de Yoast ? Jetez un œil à notre %1$s !"

#: src/presenters/admin/sidebar-presenter.php:87 js/dist/new-settings.js:11
msgid "Learn SEO"
msgstr "Apprenez le SEO"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "Réglages %s à importer :"

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:68
msgid "Your %1$s settings:"
msgstr "Vos réglages %1$s :"

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "Importez des réglages d’une autre installation %1$s en les collant ici et en cliquant sur « %2$s »."

#: admin/metabox/class-metabox.php:431 js/dist/block-editor.js:464
#: js/dist/elementor.js:447 js/dist/new-settings.js:32
#: js/dist/new-settings.js:188 js/dist/structured-data-blocks.js:13
msgid "Schema"
msgstr "Schema"

#: admin/views/tabs/social/twitterbox.php:21
msgid "Twitter uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph metadata\" setting on the Facebook tab enabled if you want to optimize your site for Twitter."
msgstr "Twitter, utilise les métadonnées Open Graph tout comme Facebook. Assurez-vous donc d’avoir activé le réglage « Ajouter les métadonnées Open Graph » dans l’onglet Facebook si vous souhaitez optimiser votre site pour Twitter."

#: admin/admin-settings-changed-listener.php:81
msgid "Settings saved."
msgstr "Réglages enregistrés."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:157
msgid "Please check the format of the Wikipedia URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL Wikipedia saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:142
msgid "Please check the format of the Pinterest URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL Pinterest saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:137
msgid "Please check the format of the MySpace URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL MySpace saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:122
msgid "Please check the format of the Instagram URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL Instagram saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:112
msgid "Please check the format of the Facebook Page URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL de la page Facebook saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:127
msgid "Please check the format of the LinkedIn URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL LinkedIn saisie. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:167
msgid "Please check the format of the YouTube URL you entered. %s"
msgstr "Veuillez vérifier le format de l’URL YouTube saisie. %s"

#. translators: %s expands to an invalid Facebook App ID.
#: inc/options/class-wpseo-option.php:482
msgid "%s does not seem to be a valid Facebook App ID. Please correct."
msgstr "%s ne semble pas être un ID d’App Facebook valide. Veuillez le corriger."

#. translators: %s: form value as submitted.
#: admin/class-yoast-input-validation.php:319
msgid "The submitted value was: %s"
msgstr "La valeur entrée était : %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:162
msgid "Yandex confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Les codes de confirmation de Yandex ne peuvent contenir que des lettres de A à Z, des chiffres, tirets courts et tirets bas. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:152
msgid "Twitter usernames can only contain letters, numbers, and underscores. %s"
msgstr "Les identifiants Twitter ne peuvent contenir que des lettres, chiffres et tirets bas. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:147
msgid "Pinterest confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Les codes de confirmation de Pinterest ne peuvent contenir que des lettres de A à Z, des chiffres, tirets courts et tirets bas. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:132
msgid "Bing confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Les codes de confirmation de Bing ne peuvent contenir que des lettres de A à Z, des chiffres, tirets courts et tirets bas. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:117
msgid "Google verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "Les codes de confirmation de Yandex ne peuvent contenir que des lettres, des chiffres, tirets courts et tirets bas. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:107
msgid "Baidu verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "Les codes de confirmation de Baidu ne peuvent contenir que des lettres, des chiffres, tirets courts et tirets bas. %s"

#: admin/views/partial-notifications-template.php:45
msgid "Show this item."
msgstr "Afficher cet élément."

#: admin/views/partial-notifications-template.php:38
msgid "Hide this item."
msgstr "Masquer cet élément."

#. translators: %d expands the amount of hidden problems.
#: admin/views/partial-notifications-errors.php:25
msgid "You have %d hidden problem:"
msgid_plural "You have %d hidden problems:"
msgstr[0] "Vous avez %d problème masqué :"
msgstr[1] "Vous avez %d problèmes masqués :"

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "Vous avez %d notification masquée :"
msgstr[1] "Vous avez %d notifications masquées :"

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "Le formulaire contient %1$s erreur. %2$s"
msgstr[1] "Le formulaire contient %1$s erreurs. %2$s"

#. translators: %s expands to the extension title
#: admin/views/licenses.php:165 admin/views/licenses.php:270
msgid "Activate %s for your site on MyYoast"
msgstr "Activez %s pour votre site sur MyYoast"

#: admin/class-customizer.php:109
msgid "Show blog page in breadcrumbs"
msgstr "Afficher la page de blog dans le fil d’Ariane"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the SEO score.
#: admin/formatter/class-metabox-formatter.php:110
#: admin/formatter/class-metabox-formatter.php:117
#: admin/formatter/class-metabox-formatter.php:124
#: admin/formatter/class-metabox-formatter.php:131
msgid "%1$sSEO%2$s: %3$s"
msgstr "%1$sSEO%2$s : %3$s"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the readability score.
#: admin/formatter/class-metabox-formatter.php:140
#: admin/formatter/class-metabox-formatter.php:147
#: admin/formatter/class-metabox-formatter.php:154
#: admin/formatter/class-metabox-formatter.php:161
msgid "%1$sReadability%2$s: %3$s"
msgstr "%1$sLisibilité%2$s : %3$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "Publications avec le score SEO : %s"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:340
msgid "%s video tutorial"
msgstr "Tutoriel vidéo de %s"

#: inc/class-wpseo-rank.php:178
msgid "Post Noindexed"
msgstr "Publication désindexée"

#: inc/class-wpseo-rank.php:158
msgid "No Focus Keyphrase"
msgstr "Pas de requête cible"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:157 inc/class-wpseo-rank.php:162
#: inc/class-wpseo-rank.php:167 inc/class-wpseo-rank.php:172
#: inc/class-wpseo-rank.php:177
msgid "SEO: %s"
msgstr "SEO : %s"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "Pour voir vos erreurs d’exploration actuelles, %1$sveuillez vous rendre sur Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "Google a mis fin à son API d’erreurs d’exploration. Par conséquent, les éventuelles erreurs que vous pourriez avoir ne peuvent plus être affichées ici. %1$sLisez notre déclaration à ce sujet pour plus d’informations%2$s."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:113
#: js/dist/new-settings.js:232
msgid "Personal info"
msgstr "Informations personnelles"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:95
#: js/dist/first-time-configuration.js:5 js/dist/new-settings.js:224
#: js/dist/new-settings.js:231
msgid "Organization name"
msgstr "Nom de l’organisation"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:59
msgid "Choose whether the site represents an organization or a person."
msgstr "Choisissez si votre site représente une organisation ou une personne."

#: admin/views/tabs/metas/general.php:67
msgid "Knowledge Graph & Schema.org"
msgstr "Knowledge Graph & Schema.org"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:66
msgid "Organization or person"
msgstr "Organisation ou personne"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:63
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:69
#: src/integrations/admin/first-time-configuration-integration.php:486
#: src/integrations/admin/first-time-configuration-integration.php:499
#: js/dist/new-settings.js:230
msgid "Organization"
msgstr "Organisation"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:60
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "Vous avez précédemment réglé votre site en tant que représentant d’une personne. Nous avons amélioré les fonctionnalités de données structurées (Schema et Knowledge Graph), donc nous vous invitons à %1$s compléter ces réglages%2$s."

#: admin/class-admin.php:297
msgid "(if one exists)"
msgstr "(si une existe)"

#: admin/class-admin.php:297
msgid "Wikipedia page about you"
msgstr "Page Wikipédia à votre propos"

#: admin/class-admin.php:296
msgid "YouTube profile URL"
msgstr "URL de profil Youtube"

#: admin/class-admin.php:294
msgid "Tumblr profile URL"
msgstr "URL de profil Tumblr"

#: admin/class-admin.php:293
msgid "SoundCloud profile URL"
msgstr "URL de profil Soundcloud"

#: admin/class-admin.php:291
msgid "MySpace profile URL"
msgstr "URL de profil Myspace"

#: src/generators/schema/article.php:136
msgid "Uncategorized"
msgstr "Non classé"

#: admin/views/tabs/social/accounts.php:20
msgid "If a Wikipedia page for you or your organization exists, add it too."
msgstr "Si une page Wikipédia existe à votre sujet ou à propos de votre organisation, ajoutez-la également."

#: admin/class-admin.php:292
msgid "Pinterest profile URL"
msgstr "URL de profil Pinterest"

#: admin/class-admin.php:290
msgid "LinkedIn profile URL"
msgstr "URL de profil LinkedIn"

#: admin/class-admin.php:289
msgid "Instagram profile URL"
msgstr "URL de profil Instagram"

#: inc/class-my-yoast-api-request.php:141
msgid "No JSON object was returned."
msgstr "Aucun objet JSON n’a été renvoyé."

#: src/integrations/admin/link-count-columns-integration.php:145
msgid "Received internal links"
msgstr "Liens internes reçus"

#: src/integrations/admin/link-count-columns-integration.php:138
msgid "Outgoing internal links"
msgstr "Liens internes émis"

#: admin/class-meta-columns.php:111 js/dist/block-editor.js:347
#: js/dist/classic-editor.js:343 js/dist/dashboard-widget.js:24
#: js/dist/editor-modules.js:289 js/dist/elementor.js:347
msgid "Keyphrase"
msgstr "Requête"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:88
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "Pour que cette fonctionnalité fonctionne, %1$s a besoin de créer une table dans votre base de données. Nous n’avons pas réussi à le faire automatiquement."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Impossible de récupérer la taille de %1$s pour des raisons inconnues."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Impossible de récupérer la taille de %1$s car elle est externe au site."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:394
msgid "Page %s"
msgstr "Page %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "La méthode %1$s() n’existe pas dans la classe %2$s"

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:219
msgid "With %s, you can easily create such redirects."
msgstr "Avec %s, vous pouvez facilement créer de telles redirections."

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "L’import de réglages ne fonctionne que sur les serveurs qui fonctionnent sous PHP 5.3 ou supérieur."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:25
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Exportez vos réglages de %1$s ici, afin de les copier vers un autre site."

#: admin/views/licenses.php:80
msgid "Improve sharing on Facebook and Pinterest"
msgstr "Améliore le partage sur Facebook et Pinterest"

#: admin/import/class-import-settings.php:79
msgid "No settings found."
msgstr "Aucun réglage trouvé."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:89
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Ce sont des réglages pour l’extension %1$s par %2$s"

#: admin/class-export.php:50
msgid "You do not have the required rights to export settings."
msgstr "Vous n’avez pas les droits suffisant pour exporter les réglages."

#. translators: %1$s expands to Import settings
#: admin/class-export.php:57
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Copiez tous ces réglages dans l’onglet %1$s d’un autre site et cliquez ensuite sur « %1$s »."

#: inc/class-wpseo-admin-bar-menu.php:288
msgid "Google Ads"
msgstr "Google Ads"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:28
msgid "Not showing the date archives in the search results technically means those will have a %1$s robots meta. %2$sMore info on the search results settings%3$s."
msgstr "Ne pas afficher la date des archives dans les résultats de recherche correspond techniquement à une métadonnée %1$s pour les robots. %2$sPlus d’informations sur les réglages des résultats de recherche%3$s."

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:84 js/dist/new-settings.js:237
msgid "Upgrade to %s"
msgstr "Passez à %s"

#: admin/class-admin-init.php:333
msgid "Learn about why permalinks are important for SEO."
msgstr "Découvrez en quoi les permaliens sont importants pour votre référencement."

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:327
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Changer les réglages de permaliens peut sérieusement impacter votre visibilité sur les moteurs de recherche. On ne devrait %1$s jamais %2$s les changer sur un site en production."

#: admin/class-admin-init.php:324
msgid "WARNING:"
msgstr "AVERTISSEMENT :"

#: inc/class-wpseo-admin-bar-menu.php:353
msgid "Check Keyphrase Density"
msgstr "Vérifier la densité de requête"

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:293
msgid "Disable"
msgstr "Désactiver"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:291
msgid "Allow Control"
msgstr "Autoriser le contrôle"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Cet onglet vous permet de désactiver certaines fonctionnalités de %s pour tous les sites du réseau. Par défaut, toutes les fonctionnalités sont activées, ce qui permet aux administrateurs de choisir lesquelles ils veulent activer/désactiver pour leur site. Lorsque vous désactivez une fonctionnalité ici, les administrateurs des sites ne pourront plus l’utiliser du tout."

#: admin/views/licenses.php:129
msgid "optimize a single post for synonyms and related keyphrases."
msgstr "optimisez une publication pour des synonymes et des variantes de votre requête."

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s est un argument d’activation de fonctionnalité obligatoire."

#: admin/views/licenses.php:128
msgid "Synonyms & related keyphrases"
msgstr "Synonymes et variantes de requête"

#: admin/formatter/class-metabox-formatter.php:70
msgid "Remove keyphrase"
msgstr "Retirer la requête"

#: admin/formatter/class-metabox-formatter.php:69
msgid "Keyphrase:"
msgstr "Requête :"

#: admin/class-yoast-form.php:1041 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled by the network admin."
msgstr "Cette fonctionnalité a été désactivée par l’administrateur du réseau."

#: admin/class-meta-columns.php:166
msgid "Focus keyphrase not set."
msgstr "Requête cible non définie."

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Multiple keyphrases"
msgstr "Plusieurs requêtes cibles"

#: admin/class-yoast-form.php:749 admin/metabox/class-metabox.php:654
#: admin/taxonomy/class-taxonomy-fields-presenter.php:133
msgid "Clear Image"
msgstr "Effacer l’image"

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/class-premium-popup.php:81
#: admin/class-premium-upsell-admin-block.php:67
#: admin/formatter/class-metabox-formatter.php:325
#: admin/watchers/class-slug-change-watcher.php:223
#: src/deprecated/admin/add-keyword-modal.php:47
#: src/deprecated/admin/keyword-synonyms-modal.php:47
#: src/deprecated/admin/multiple-keywords-modal.php:47
#: src/presenters/admin/sidebar-presenter.php:48
#: src/presenters/admin/sidebar-presenter.php:61 js/dist/block-editor.js:382
#: js/dist/classic-editor.js:378 js/dist/elementor.js:382
#: js/dist/externals-components.js:15 js/dist/externals-components.js:77
#: js/dist/externals-components.js:95 js/dist/externals-components.js:97
#: js/dist/externals-components.js:99 js/dist/externals-components.js:101
#: js/dist/new-settings.js:7 js/dist/new-settings.js:239
msgid "Get %s"
msgstr "%s"

#: inc/class-wpseo-admin-bar-menu.php:680
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "Il y a une nouvelle notification."
msgstr[1] "Il y a des nouvelles notifications."

#: inc/options/class-wpseo-option-titles.php:967
msgid "Colon"
msgstr "Deux-points"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:88
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "%1$s et %2$s gèrent tous les deux le référencement de votre site. En ayant deux solutions concurrentes, vous pourriez créer des conflits au détriment de votre visibilité."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:62
msgid "%1$s Structured Data Blocks"
msgstr "Blocs de données structurées de %1$s "

#. translators: %s expands to a 'Yoast SEO Premium' text linked to the
#. yoast.com website.
#: admin/formatter/class-metabox-formatter.php:313
#: src/deprecated/admin/add-keyword-modal.php:35
#: src/deprecated/admin/keyword-synonyms-modal.php:35
#: src/deprecated/admin/multiple-keywords-modal.php:35
#: js/dist/block-editor.js:374 js/dist/classic-editor.js:370
#: js/dist/elementor.js:374 js/dist/externals-components.js:7
msgid "Great news: you can, with %s!"
msgstr "Bonne nouvelle : vous le pouvez avec %s !"

#: inc/class-wpseo-admin-bar-menu.php:348
msgid "Check links to this URL"
msgstr "Vérifier les liens vers cette URL"

#: inc/class-wpseo-admin-bar-menu.php:283
msgid "Keyword research training"
msgstr "Formation à la recherche de mots-clés"

#: admin/pages/network.php:36
msgid "Restore Site"
msgstr "Restaurer le site"

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Réglages du réseau"

#: admin/class-yoast-network-admin.php:266
msgid "You are not allowed to perform this action."
msgstr "Vous n’avez pas les droits suffisants pour faire cette action."

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:198
msgid "Error: %s"
msgstr "Erreur : %s"

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:196
msgid "Success: %s"
msgstr "Succès : %s"

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:158
msgid "Site with ID %d not found."
msgstr "Le site avec l’ID %d est introuvable."

#: admin/class-yoast-network-admin.php:149
msgid "No site has been selected to restore."
msgstr "Aucun site n’a été sélectionné pour la restauration."

#: admin/class-yoast-network-admin.php:110
msgid "You are not allowed to modify unregistered network settings."
msgstr "Vous n’avez pas les droits suffisants pour modifier les réglages d’un réseau non-enregistré."

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "supprimé"

#: src/deprecated/admin/multiple-keywords-modal.php:32
msgid "Would you like to add another keyphrase?"
msgstr "Souhaitez-vous ajouter une autre requête ?"

#: inc/class-wpseo-replace-vars.php:1472
msgid "The site's tagline"
msgstr "Le slogan du site"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:150
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Tous les champs nécessaires ne sont pas renseignés. Champ manquant %1$s"

#: src/deprecated/admin/keyword-synonyms-modal.php:32
#: js/dist/externals-components.js:97
msgid "Would you like to add keyphrase synonyms?"
msgstr "Souhaitez-vous ajouter des requêtes synonymes ?"

#: admin/formatter/class-metabox-formatter.php:310
#: src/deprecated/admin/add-keyword-modal.php:32
msgid "Would you like to add more than one keyphrase?"
msgstr "Souhaitez-vous ajouter plus d’une requête ?"

#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:10
msgid "You haven't set a Shop page in your WooCommerce settings. Please do this first."
msgstr "Vous n’avez pas défini de page « Boutique » dans vos réglages WooCommerce. Veuillez le faire dans un premier temps."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to a
#. closing anchor tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:15
#: js/dist/new-settings.js:21
msgid "You can edit the SEO metadata for this custom type on the %1$sShop page%2$s."
msgstr "Vous pouvez modifier les métadonnées SEO de ce type de publication sur la %1$spage Boutique%2$s."

#: inc/class-wpseo-replace-vars.php:1486 js/dist/externals-redux.js:1
msgid "Current year"
msgstr "Année en cours"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Tagline"
msgstr "Slogan"

#: inc/class-wpseo-replace-vars.php:1523
msgid "description (custom taxonomy)"
msgstr "description (taxonomie personnalisée)"

#: inc/class-wpseo-replace-vars.php:1522
msgid "(custom taxonomy)"
msgstr "(taxonomie personnalisée)"

#: inc/class-wpseo-replace-vars.php:1521
msgid "(custom field)"
msgstr "(champ personnalisé)"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Term404"
msgstr "Phrase404"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Caption"
msgstr "Légende"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Pagenumber"
msgstr "Numéropage"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Pagetotal"
msgstr "Nombrepages"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Page number"
msgstr "Numéro de page"

#: inc/class-wpseo-replace-vars.php:1514
msgid "User description"
msgstr "Description de l’utilisateur"

#: inc/class-wpseo-replace-vars.php:1512 js/dist/externals-redux.js:1
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Modified"
msgstr "Modifié"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Post type (plural)"
msgstr "Types de publication (au pluriel)"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Post type (singular)"
msgstr "Type de publication (au singulier)"

#: inc/class-wpseo-replace-vars.php:1484 js/dist/externals-redux.js:1
msgid "Separator"
msgstr "Séparateur"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "Phrase de recherche"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Term title"
msgstr "Titre du terme"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/externals-redux.js:1
msgid "Term description"
msgstr "Description de l’élément"

#: inc/class-wpseo-replace-vars.php:1479 js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "Étiquette du contenu"

#: inc/class-wpseo-replace-vars.php:1478 js/dist/externals-redux.js:1
msgid "Category description"
msgstr "Description de la catégorie"

#: inc/class-wpseo-replace-vars.php:1477 js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "Catégorie principale"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/externals-redux.js:1
msgid "Category"
msgstr "Catégorie"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Tag"
msgstr "Étiquette"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "Extrait seulement"

#: inc/class-wpseo-replace-vars.php:1473 js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "Extrait"

#: inc/class-wpseo-replace-vars.php:1471 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Site title"
msgstr "Titre du site"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Archive title"
msgstr "Titre de l’archive"

#: inc/class-wpseo-replace-vars.php:1469 js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "Titre parent"

#: inc/class-wpseo-replace-vars.php:1467 js/dist/externals-redux.js:1
msgid "Date"
msgstr "Date"

#: admin/watchers/class-slug-change-watcher.php:217
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Vous devriez créer une redirection pour vous assurer que les visiteurs n’obtiennent pas une erreur 404 lorsqu’ils cliquent sur des URL cassées."

#: admin/watchers/class-slug-change-watcher.php:216
msgid "Search engines and other websites can still send traffic to your deleted post."
msgstr "Les moteurs de recherche et d’autres sites peuvent continuer à envoyer du trafic à votre publication effacée."

#: admin/watchers/class-slug-change-watcher.php:213
msgid "Make sure you don't miss out on traffic!"
msgstr "Assurez-vous de ne pas perdre de trafic !"

#. translators: %1$s expands to the translated name of the post type.
#. translators: 1: term label
#: admin/watchers/class-slug-change-watcher.php:84
#: admin/watchers/class-slug-change-watcher.php:104
msgid "You just deleted a %1$s."
msgstr "Vous venez de supprimer un·e %1$s."

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:65
msgid "You just trashed a %1$s."
msgstr "Vous venez de mettre un·e %1$s à la corbeille."

#. translators: %s expands to the post type name.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:22
msgid "Settings for %s archive"
msgstr "Réglages pour les archives des %s"

#: admin/views/tabs/metas/post-types.php:25
msgid "The settings on this page allow you to specify what the default search appearance should be for any type of content you have. You can choose which content types appear in search results and what their default description should be."
msgstr "Les réglages sur cette page vous permettent de définir l’apparence par défaut de vos publications dans les résultats de recherche. Vous pouvez choisir quels types de publications seront indexés et comment doit être construite leur description par défaut."

#. translators: %1$s expands to a link start tag to the Baidu Webmaster Tools
#. site add page, %2$s is the link closing tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:44
msgid "Get your Baidu verification code in %1$sBaidu Webmaster Tools%2$s."
msgstr "Récupérez votre code de vérification Baidu dans %1$sBaidu Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:40
msgid "Baidu verification code"
msgstr "Code de vérification Baidu"

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:257
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "L’importateur %s utilise des tables temporaires de base de données. Il semblerait que votre installation WordPress ne le permette pas. Veuillez solliciter votre hébergeur."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "Le nettoyage des données de %s a échoué."

#: admin/class-bulk-editor-list-table.php:993
msgid "Content Type"
msgstr "Type de contenu"

#: admin/class-bulk-editor-list-table.php:406
msgid "Filter by content type"
msgstr "Filtrer par type de contenu"

#: admin/class-bulk-editor-list-table.php:389
msgid "Show All Content Types"
msgstr "Afficher tous les types de publication"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Remplacé avec un titre d’archive normal généré par WordPress"

#: admin/views/tabs/tool/import-seo.php:119
msgid "Clean"
msgstr "Propre"

#: admin/views/tabs/tool/import-seo.php:110
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "Une fois que vous avez la certitude que votre site est OK, vous pouvez le nettoyer. Cela retirera toutes les données antérieures."

#: admin/views/tabs/tool/import-seo.php:108
msgid "Step 5: Clean up"
msgstr "Étape 5 : Nettoyez"

#: admin/views/tabs/tool/import-seo.php:89
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Veuillez vérifier que les métadonnées de vos articles et pages soient bien importés."

#: admin/views/tabs/tool/import-seo.php:87
msgid "Step 3: Check your data"
msgstr "Étape 3 : Vérifiez vos données"

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "Cela importera les métadonnées de vos publications telles que les titres et descriptions SEO dans %1$s. Cela ne fonctionnera que s’il n’existe pas encore de métadonnées appartenant à %1$s. Les données antérieures ne seront pas effacées."

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "Étape 2 : Importez"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "Veuillez faire une sauvegarde de votre base de données avant de lancer ce processus."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "Étape 1 : Sauvegardez"

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Nous avons détecté que vous utilisez une ou plusieurs extensions SEO sur votre site. Veuillez suivre les étapes suivantes pour en importer les données :"

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "Extension : "

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "%s n’a pas détecté d’extension dont il peut importer les données."

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "Articles qui ne devraient pas apparaître dans les résultats de recherche"

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "%s données trouvées."

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "Données de %s retirées."

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "Données de %s importées."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "%s données introuvables."

#: admin/class-premium-upsell-admin-block.php:58 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "24/7 email support"
msgstr "Support par e-mail 24/7"

#: admin/views/user-profile.php:15
msgid "this author's archives"
msgstr "archives de cet auteur"

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:14
msgid "Do not allow search engines to show %s in search results."
msgstr "Empêcher les moteurs de recherche d’afficher les %s dans les résultats de recherche."

#: admin/views/tabs/social/accounts.php:19
msgid "To let search engines know which social profiles are associated to this site, enter your site social profiles data below."
msgstr "Pour permettre aux moteurs de recherche de savoir quels profils sociaux sont associés à ce site, renseignez les données sociales ci-dessous."

#: admin/views/tabs/social/accounts.php:18
msgid "Learn more about your social profiles settings"
msgstr "En savoir plus au sujet des réglages des profils sociaux"

#. translators: %s expands to <code>/category/</code>
#: admin/views/tabs/metas/taxonomies/category-url.php:17
msgid "Category URLs in WordPress contain a prefix, usually %s, this feature removes that prefix, for categories only."
msgstr "Les URL de catégorie contiennent un préfixe dans WordPress tel que %s. Cette fonctionnalité le retire mais seulement pour les catégories."

#: admin/views/tabs/metas/taxonomies/category-url.php:14
msgid "Help on the category prefix setting"
msgstr "Aide sur le réglage de préfixe de catégorie"

#: admin/views/tabs/metas/taxonomies.php:54
msgid "Category URLs"
msgstr "URL des catégories"

#: admin/views/tabs/metas/paper-content/rss-content.php:19
msgid "Learn more about the available variables"
msgstr "En savoir plus sur les variables disponibles"

#: admin/views/tabs/metas/rss.php:20
msgid "Learn more about the RSS feed setting"
msgstr "En savoir plus sur les réglages de flux RSS"

#. translators: %s expands to the post type's name with a link to the archive.
#: admin/views/tabs/metas/paper-content/post-type-content.php:52
msgid "the archive for %s"
msgstr "l’archive des %s"

#: admin/views/tabs/metas/paper-content/media-content.php:25
msgid "Redirect attachment URLs to the attachment itself?"
msgstr "Rediriger les URL des fichiers joints aux médias ?"

#: admin/views/tabs/metas/paper-content/media-content.php:15
msgid "We recommend you set this to Yes."
msgstr "Nous vous recommandons de sélectionner « Oui »."

#: admin/views/tabs/metas/media.php:15
msgid "Media & attachment URLs"
msgstr "URL de médias et fichiers joints"

#: admin/views/tabs/metas/media.php:21
msgid "When you upload media (an image or video for example) to WordPress, it doesn't just save the media, it creates an attachment URL for it. These attachment pages are quite empty: they contain the media item and maybe a title if you entered one. Because of that, if you never use these attachment URLs, it's better to disable them, and redirect them to the media item itself."
msgstr "Lorsque vous téléversez un média dans WordPress (une image ou une vidéo par exemple), il crée également une URL de fichier joint. Ces pages sont relativement vides : elles contiennent le média et éventuellement un titre si vous en avez renseigné un. C’est pour cette raison que si vous n’utilisez pas les fichiers joints, il vaut mieux les désactiver et les rediriger vers les médias."

#: admin/views/tabs/metas/taxonomies/category-url.php:24
msgid "Remove the categories prefix?"
msgstr "Retirer le préfixe des catégories ?"

#: admin/views/tabs/metas/media.php:20
msgid "Learn more about the Media and attachment URLs setting"
msgstr "En savoir plus sur les réglages des URL de médias et fichiers joints"

#: admin/views/tabs/metas/paper-content/general/homepage.php:13
msgid "This is what shows in the search results when people find your homepage. This means this is probably what they see when they search for your brand name."
msgstr "C’est ce qui apparaît dans les résultats de recherche lorsque les internautes trouvent votre page d’accueil. Cela signifie qu’ils vous recherchaient probablement au travers du nom de votre marque."

#: admin/views/tabs/metas/paper-content/special-pages.php:12
msgid "Learn more about the special pages setting"
msgstr "En savoir plus sur le réglage des pages spéciales"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:37
msgid "date archives"
msgstr "archives de date"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:25
msgid "Help on the date archives search results setting"
msgstr "Aide sur le réglage des résultats de recherche pour les archives de date"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:62
msgid "archives for authors without posts"
msgstr "archives d’auteurs sans publication"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:53
msgid "Not showing the archives for authors without posts in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Ne pas afficher les archives d’auteurs sans publication dans les résultats de recherche implique qu’elles auront une consigne %1$s et seront exclues des plans de site XML. %2$sPlus d’informations sur les réglages de résultats de recherche%3$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:50
msgid "Help on the authors without posts archive search results setting"
msgstr "Aide sur le réglage des résultats de recherche pour les archives des auteurs sans publication"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:30
msgid "Not showing the archive for authors in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Ne pas afficher les archives d’auteurs dans les résultats de recherche implique qu’elles auront une consigne %1$s et seront exclues des plans de site XML. %2$sPlus d’informations sur les réglages de résultats de recherche%3$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:27
msgid "Help on the author archives search results setting"
msgstr "Aide sur le réglage des résultats de recherche pour les archives d’auteurs"

#: admin/views/tabs/metas/archives/help.php:32
msgid "Archives settings help"
msgstr "Aide sur les réglages d’archives"

#: admin/views/tabs/metas/archives/help.php:26
msgid "Learn more about the archives setting"
msgstr "En savoir plus sur les réglages des archives"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:79
msgid "Get your Yandex verification code in %1$sYandex Webmaster Tools%2$s."
msgstr "Récupérez votre code de vérification Yandex dans %1$sYandex Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:75
msgid "Yandex verification code"
msgstr "Code de vérification Yandex"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:69
msgid "Get your Google verification code in %1$sGoogle Search Console%2$s."
msgstr "Récupérez votre code de vérification Google dans %1$sGoogle Search Console%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:65
msgid "Google verification code"
msgstr "Code de vérification Google"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:59
msgid "Get your Bing verification code in %1$sBing Webmaster Tools%2$s."
msgstr "Récupérez votre code de vérification Bing dans %1$sBing Webmaster Tools%2$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:55
msgid "Bing verification code"
msgstr "Code de vérification Bing"

#: admin/views/tabs/dashboard/webmaster-tools.php:19
msgid "You can use the boxes below to verify with the different Webmaster Tools. This feature will add a verification meta tag on your home page. Follow the links to the different Webmaster Tools and look for instructions for the meta tag verification method to get the verification code. If your site is already verified, you can just forget about these."
msgstr "Vous pouvez utiliser les champs ci-dessous pour vérifier votre auprès des différents moteurs de recherche. Cette fonctionnalité ajoutera une métadonnée de vérification sur votre page d’accueil. Cliquez sur les liens des différents outils pour webmasters et utilisez les options de vérification par métadonnées pour obtenir votre code. Si votre site est déjà validé, vous pouvez oublier ces réglages."

#: admin/views/tabs/dashboard/webmaster-tools.php:18
msgid "Learn more about the Webmaster Tools verification"
msgstr "En savoir plus sur la vérification par les moteurs de recherche"

#: admin/class-yoast-form.php:937 admin/class-yoast-form.php:977
#: admin/metabox/class-metabox.php:212
#: admin/views/tabs/dashboard/features.php:106
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:13
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:13
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "On"
msgstr "Activé"

#. translators: %s expands to a feature's name
#. translators: %s expands to an integration's name
#: admin/views/tabs/dashboard/features.php:55
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "Aide sur : %s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/features.php:27
msgid "%1$s comes with a lot of features. You can enable / disable some of them below. Clicking the question mark gives more information about the feature."
msgstr "%1$s propose de nombreuses fonctionnalités. Vous pouvez en activer/désactiver certaines ci-dessous. En cliquant sur le point d’interrogation, vous obtiendrez plus d’informations à leur sujet."

#: admin/class-yoast-form.php:938 admin/class-yoast-form.php:978
#: admin/metabox/class-metabox.php:211
#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/tabs/dashboard/features.php:107
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:14
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:14
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "Off"
msgstr "Désactivé"

#: admin/views/class-yoast-feature-toggles.php:145
msgid "Read why XML Sitemaps are important for your site."
msgstr "Découvrez pourquoi les plans de site XML sont importants pour votre site."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:144
msgid "Enable the XML sitemaps that %s generates."
msgstr "Activer les plans de site générés par %s."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "Voir le plan de site XML."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:20
msgid "See who contributed to %1$s."
msgstr "Voir qui a contribué à %1$s."

#: admin/views/class-view-utils.php:56
msgid "Help on this search results setting"
msgstr "Aide sur les réglages de résultats de recherche"

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:50
msgid "Not showing the archive for %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "Ne pas afficher les archives des %1$s dans les résultats de recherche implique qu’elles auront une consigne %2$s et seront exclues des plans de site XML. %3$sPlus d’informations sur les réglages de résultats de recherche%4$s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:46
msgid "Not showing %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "Ne pas afficher les %1$s dans les résultats de recherche implique qu’ils ou elles auront une consigne %2$s et seront exclu·es des plans de site XML. %3$sPlus d’informations sur les réglages de résultats de recherche%4$s."

#: admin/pages/metas.php:20
msgid "Media"
msgstr "Médias"

#: admin/pages/metas.php:19
msgid "Content Types"
msgstr "Types de publication"

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:187
msgid "Should search engines follow links on this %1$s?"
msgstr "Les moteurs de recherche doivent-ils suivre les liens sur le contenu %1$s ?"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:182
msgid "Default for %2$s, currently: %1$s"
msgstr "Réglages par défaut pour %2$s, actuellement : %1$s"

#. translators: %s expands to the post type name.
#. Translators: %s translates to the Post Label in singular form
#: admin/metabox/class-metabox.php:177 js/dist/block-editor.js:408
#: js/dist/classic-editor.js:404 js/dist/elementor.js:408
msgid "Allow search engines to show this %s in search results?"
msgstr "Autoriser les moteurs de recherche à afficher le contenu %s dans les résultats de recherche ?"

#: admin/menu/class-admin-menu.php:90
#: src/presenters/meta-description-presenter.php:37
msgid "Search Appearance"
msgstr "Réglages SEO"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:948
msgid "Show %s in search results?"
msgstr "Afficher les %s dans les résultats de recherche ?"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:80
msgid "Toggle %1$s's XML Sitemap"
msgstr "Activer le plan de site XML de %1$s"

#: admin/views/tabs/social/twitterbox.php:27
msgid "Enable this feature if you want Twitter to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Activez cette fonctionnalité si vous voulez que Twitter affiche un extrait avec images lorsque votre site est partagé."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:104 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "SEO analysis"
msgstr "Analyse SEO"

#: admin/views/class-yoast-feature-toggles.php:115 js/dist/new-settings.js:215
msgid "The text link counter helps you improve your site structure."
msgstr "Le compteur de liens vous aide à améliorer la structure de votre site."

#: admin/formatter/class-metabox-formatter.php:201
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Good results"
msgstr "Déjà optimisé"

#: admin/views/licenses.php:35
msgid "Get better search results in local search"
msgstr "Obtenez de meilleurs résultats locaux"

#. translators: %1$s expands to WooCommerce
#: admin/views/licenses.php:38
msgid "Allow customers to pick up their %s order locally"
msgstr "Permet à vos clients de récupérer leur commandes %s sur place."

#: admin/views/class-yoast-feature-toggles.php:77 js/dist/new-settings.js:213
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "L’analyse SEO propose des suggestions pour améliorer le référencement de votre texte."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "Découvrez comment l’analyse SEO peut améliorer votre référencement."

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:213
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "L’analyse de lisibilité propose des suggestions pour améliorer la structure et le style de votre texte."

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "Découvrez pourquoi la lisibilité est importante pour le SEO."

#: admin/views/class-yoast-feature-toggles.php:107 js/dist/new-settings.js:215
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "La fonctionnalité des contenus Cornerstone vous permets de distinguer et filtrer les contenus les plus importants de votre site."

#: admin/views/class-yoast-feature-toggles.php:108
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Découvrez comment les contenus Cornerstone peuvent améliorer votre structure de site."

#: admin/views/class-yoast-feature-toggles.php:116
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Découvrez comment le compteur de liens peut améliorer votre SEO."

#. translators: %s: 'Semrush'
#. translators: %s: Zapier.
#. translators: %s: Algolia.
#. translators: %s: 'Wincher'
#. translators: %s expands to WordProof
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
#: admin/views/class-yoast-integration-toggles.php:96
#: src/integrations/third-party/wincher.php:73
#: src/integrations/third-party/wordproof-integration-toggle.php:82
msgid "%s integration"
msgstr "Intégration de %s"

#. translators: %s expands to WordProof
#: src/integrations/third-party/wordproof-integration-toggle.php:90
msgid "Read more about how %s works."
msgstr "Découvrez comment %s fonctionne."

#: admin/views/tabs/social/facebook.php:25
msgid "Enable this feature if you want Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Activez cette fonctionnalité si vous voulez que Facebook et d’autres réseaux sociaux affichent un extrait avec images lorsque votre site est partagé."

#. Author URI of the plugin
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:124
msgid "Latest blog posts on %1$s"
msgstr "Derniers articles sur %1$s"

#: admin/formatter/class-metabox-formatter.php:203
#: js/dist/externals/analysisReport.js:17
msgid "Remove highlight from the text"
msgstr "Retirer le surlignement du texte"

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "First-time SEO configuration"
msgstr "Première configuration SEO"

#: admin/formatter/class-metabox-formatter.php:202
#: js/dist/externals/analysisReport.js:17
msgid "Highlight this result in the text"
msgstr "Surligner ce résultat dans le texte"

#: admin/formatter/class-metabox-formatter.php:200
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Considerations"
msgstr "Considérations"

#: admin/formatter/class-metabox-formatter.php:197
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Errors"
msgstr "Erreurs"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "Vous ne pouvez pas créer de fichier %s."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "Vous ne pouvez pas modifier le fichier %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "%s mis à jour"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "Créer le fichier %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "Modifiez le contenu de votre %s :"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "Enregistrer les changements de %s"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "fichier %s"

#. translators: %1$s expands to the dependency name.
#: admin/class-suggested-plugins.php:136
msgid "More information about %1$s"
msgstr "Plus d’informations au sujet de %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "Ancien assistant de configuration"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Marquez les contenus les plus importants des %1$s comme « contenus Cornerstone » pour améliorer la structure de votre site. %2$sEn savoir plus sur les contenus Cornerstone (en anglais)%3$s."

#: admin/class-admin-utils.php:78 admin/class-premium-popup.php:82
#: admin/class-premium-upsell-admin-block.php:68 admin/class-yoast-form.php:912
#: admin/formatter/class-metabox-formatter.php:205
#: admin/formatter/class-metabox-formatter.php:329 admin/views/licenses.php:97
#: admin/watchers/class-slug-change-watcher.php:224
#: src/deprecated/admin/add-keyword-modal.php:51
#: src/deprecated/admin/keyword-synonyms-modal.php:51
#: src/deprecated/admin/multiple-keywords-modal.php:51
#: src/integrations/admin/workouts-integration.php:210
#: src/integrations/admin/workouts-integration.php:239
#: src/presenters/admin/help-link-presenter.php:75 js/dist/block-editor.js:468
#: js/dist/externals/componentsNew.js:136 js/dist/externals/helpers.js:18
#: js/dist/indexables-page.js:35 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:5 js/dist/integrations-page.js:13
#: js/dist/integrations-page.js:15
msgid "(Opens in a new browser tab)"
msgstr "(S’ouvre dans un nouvel onglet)"

#. translators: %s expands to the extension title
#: admin/views/licenses.php:151 admin/views/licenses.php:255
msgid "Manage your %s subscription on MyYoast"
msgstr "Gérez votre abonnement à %s sur MyYoast"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Articles %1$ssans%2$s requête cible"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Hey, votre SEO se porte plutôt bien ! Regardez les statistiques :"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Vous n’avez pas encore publié d’articles, vos scores SEO apparaîtront une fois que vous l’aurez fait !"

#: admin/class-yoast-dashboard-widget.php:127
msgid "Read more like this on our SEO blog"
msgstr "Lisez plus d’informations comme celle-ci sur notre blog SEO"

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:194 inc/class-wpseo-rank.php:199
#: inc/class-wpseo-rank.php:204
msgid "Readability: %s"
msgstr "Lisibilité : %s"

#: admin/views/licenses.php:159 admin/views/licenses.php:264
msgid "Not activated"
msgstr "Non activé"

#: admin/views/licenses.php:145 admin/views/licenses.php:249
msgid "Activated"
msgstr "Activé"

#: admin/class-meta-columns.php:267
msgid "All Readability Scores"
msgstr "Tous les scores de lisibilité"

#: admin/class-meta-columns.php:263
msgid "Filter by Readability Score"
msgstr "Filtrer par score de lisibilité"

#. translators: %1$s expands to the product name. %2$s expands to a link to My
#. Yoast
#: inc/class-addon-manager.php:469
msgid "You are not receiving updates or support! Fix this problem by adding this site and enabling %1$s for it in %2$s."
msgstr "Vous ne recevez ni mises à jour, ni support ! Réglez ce problème en ajoutant ce site et activez %1$s pour ce domaine dans %2$s."

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:28 js/dist/new-settings.js:11
msgid "%1$s recommendations for you"
msgstr "Recommandations %1$s pour vous"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:95
msgid "Request method %1$s is not valid."
msgstr "La méthode de requête %1$s n’est pas valide."

#: admin/views/class-yoast-feature-toggles.php:113 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Text link counter"
msgstr "Compteur de lien textuel"

#: src/integrations/admin/link-count-columns-integration.php:144
msgid "Number of internal links linking to this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Nombre de liens internes VERS cette publication. Voir le texte « Colonnes Yoast » dans l’onglet aide pour plus d’info."

#: src/integrations/admin/link-count-columns-integration.php:137
msgid "Number of outgoing internal links in this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Nombre de liens internes DEPUIS cette publication. Regardez le texte « Colonnes Yoast » dans l’onglet Aide pour plus d’info."

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:60
msgid "%s Columns"
msgstr "Colonnes %s"

#: admin/class-meta-columns.php:104
#: admin/taxonomy/class-taxonomy-columns.php:91
msgid "Readability score"
msgstr "Score de lisibilité"

#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/formatter/class-metabox-formatter.php:319
#: src/deprecated/admin/add-keyword-modal.php:41
#: src/deprecated/admin/keyword-synonyms-modal.php:41
#: src/deprecated/admin/multiple-keywords-modal.php:41
#: js/dist/block-editor.js:380 js/dist/classic-editor.js:376
#: js/dist/elementor.js:380 js/dist/externals-components.js:13
#: js/dist/externals-components.js:75
msgid "Other benefits of %s for you:"
msgstr "Les autres atouts de %s :"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:105
#: js/dist/externals-components.js:18 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Cornerstone content"
msgstr "Contenu Cornestone"

#: src/integrations/admin/crawl-settings-integration.php:332
msgid "Upgrade Premium"
msgstr "Passez à la version Premium"

#: admin/class-premium-upsell-admin-block.php:56 js/dist/block-editor.js:376
#: js/dist/classic-editor.js:372 js/dist/elementor.js:376
#: js/dist/externals-components.js:9 js/dist/externals-components.js:71
#: js/dist/new-settings.js:237
msgid "Superfast internal linking suggestions"
msgstr "Suggestions de liens internes super-rapides"

#: admin/class-yoast-form.php:141 admin/class-yoast-form.php:146
#: js/dist/first-time-configuration.js:3 js/dist/new-settings.js:1
#: js/dist/settings.js:112
msgid "Save changes"
msgstr "Enregistrer les modifications"

#: admin/class-premium-popup.php:88
#: admin/formatter/class-metabox-formatter.php:328
#: src/deprecated/admin/add-keyword-modal.php:50
#: src/deprecated/admin/keyword-synonyms-modal.php:50
#: src/deprecated/admin/multiple-keywords-modal.php:50
#: js/dist/block-editor.js:382 js/dist/classic-editor.js:378
#: js/dist/elementor.js:382 js/dist/externals-components.js:15
#: js/dist/externals-components.js:77
msgid "1 year free support and updates included!"
msgstr "1 an de mises à jour et de support offert !"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:85
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "L’extension %2$s modifie l’apparence de votre site et, ce faisant, elle diffère entre les moteurs de recherche et les utilisateurs normaux, c’est un processus qui se nomme cloaking. Nous vous recommandons vivement de la désactiver."

#: admin/class-premium-upsell-admin-block.php:59 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "No ads!"
msgstr "Sans publicités !"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Facebook & Twitter"
msgstr "Facebook & Twitter"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Social media preview"
msgstr "Prévisualisation sur les réseaux sociaux"

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "Easy redirect manager"
msgstr "Gestionnaire de redirections facile"

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "No more dead links"
msgstr "Il n’y a plus de liens morts"

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Increase your SEO reach"
msgstr "Augmentez votre portée SEO"

#: admin/views/tabs/metas/paper-content/rss-content.php:32
#: js/dist/new-settings.js:189
msgid "Variable"
msgstr "Variable"

#: admin/views/tabs/metas/paper-content/rss-content.php:25
#: js/dist/new-settings.js:189
msgid "Available variables"
msgstr "Variables disponibles"

#: admin/class-admin.php:341
msgid "Scroll to see the table content."
msgstr "Faire défiler pour voir le contenu du tableau."

#: admin/views/partial-notifications-warnings.php:22
msgid "No new notifications."
msgstr "Aucune nouvelle notification."

#: admin/class-bulk-editor-list-table.php:882
msgid "Save all"
msgstr "Tout enregistrer"

#: admin/class-bulk-editor-list-table.php:881
msgid "Save"
msgstr "Enregistrer"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:247
msgid "%1$s, Author at %2$s"
msgstr "%1$s, auteur sur %2$s"

#: admin/formatter/class-metabox-formatter.php:204
#: js/dist/externals/analysis.js:383 js/dist/externals/analysisReport.js:17
msgid "Marks are disabled in current view"
msgstr "Les marques sont désactivés dans la vue actuelle"

#: inc/class-wpseo-replace-vars.php:1513 js/dist/first-time-configuration.js:5
#: js/dist/settings.js:102
msgid "Name"
msgstr "Nom"

#: admin/views/tool-import-export.php:88
msgid "Export settings"
msgstr "Exporter les réglages"

#: admin/class-product-upsell-notice.php:161
msgid "Please don't show me this notification anymore"
msgstr "Merci de ne plus afficher cette notification"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:154
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Si vous rencontrez des problèmes, %1$sveuillez remplir un rapport de bug%2$s et nous ferons notre possible pour vous aider."

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:146
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Nous avons remarqué que vous utilisez %1$s depuis un certain temps ; nous espérons que vous l’adorez ! Nous serions très heureux si vous pouviez %2$snous attribuer 5 étoiles sur WordPress.org%3$s !"

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:335
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "Avertissement : la variable %1$s ne peut pas être utilisée dans ce modèle. Consultez le centre d’aide %2$s pour plus d’informations."

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:129
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "Au fait, savez-vous que nous avons aussi une %1$sextension Premium%2$s ? Elle offre des fonctionnalités avancées comme un gestionnaire de redirection et la prise en charge des requêtes multiples. Elle donne également accès à un support personnalisé 24/7."

#: admin/class-bulk-editor-list-table.php:789
msgid "(no title)"
msgstr "(sans titre)"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:155
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "La barre de menu d’administration de %1$s contient des liens utiles vers des outils tiers pour l’analyse de vos pages et facilite l’affichage de nouvelles notifications."

#: admin/views/class-yoast-feature-toggles.php:152 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "Admin bar menu"
msgstr "Menu de la barre d’administration"

#: admin/pages/dashboard.php:41 admin/pages/network.php:19
#: admin/views/tabs/dashboard/features.php:22
#: admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "Fonctionnalités"

#: admin/metabox/class-metabox.php:173 js/dist/externals/analysis.js:211
#: js/dist/externals/analysis.js:265 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "SEO title"
msgstr "Méta titre"

#: inc/options/class-wpseo-option-titles.php:1007
msgid "Greater than sign"
msgstr "Signe supérieur à"

#: inc/options/class-wpseo-option-titles.php:1003
msgid "Less than sign"
msgstr "Signe inférieur à"

#: inc/options/class-wpseo-option-titles.php:999
msgid "Right angle quotation mark"
msgstr "Guillemet droit à angle droit"

#: inc/options/class-wpseo-option-titles.php:995
msgid "Left angle quotation mark"
msgstr "Guillemet gauche à angle droit"

#: inc/options/class-wpseo-option-titles.php:991
msgid "Small tilde"
msgstr "Petit tilde"

#: inc/options/class-wpseo-option-titles.php:987
msgid "Vertical bar"
msgstr "Barre verticale"

#: inc/options/class-wpseo-option-titles.php:983
msgid "Low asterisk"
msgstr "Astérisque bas"

#: inc/options/class-wpseo-option-titles.php:979
msgid "Asterisk"
msgstr "Astérisque"

#: inc/options/class-wpseo-option-titles.php:975
msgid "Bullet"
msgstr "Puces"

#: inc/options/class-wpseo-option-titles.php:971
msgid "Middle dot"
msgstr "Point au milieu"

#: inc/options/class-wpseo-option-titles.php:963
msgid "Em dash"
msgstr "Tiret Em"

#: inc/options/class-wpseo-option-titles.php:959
msgid "En dash"
msgstr "Tiret En"

#: inc/options/class-wpseo-option-titles.php:955
msgid "Dash"
msgstr "Tiret"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:10
msgid "Choose the symbol to use as your title separator. This will display, for instance, between your post title and site name. Symbols are shown in the size they'll appear in the search results."
msgstr "Choisissez le symbole à utiliser comme séparateur de titre. il sera affiché, par exemple, entre le titre de votre publication et le nom de votre site. Les symboles sont présentés à la taille affichée dans les résultats de recherche."

#: admin/metabox/class-metabox.php:184 admin/metabox/class-metabox.php:189
#: admin/views/tabs/metas/paper-content/media-content.php:20
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402
msgid "No"
msgstr "Non"

#: admin/metabox/class-metabox.php:183 admin/metabox/class-metabox.php:188
#: admin/views/tabs/metas/paper-content/media-content.php:19
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402 js/dist/externals/schemaBlocks.js:13
msgid "Yes"
msgstr "Oui"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Liste des publications"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigation dans la liste des publications"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtrer la liste des publications"

#: admin/views/tabs/metas/archives/help.php:20
msgid "Note that links to archives might be still output by your theme and you would need to remove them separately."
msgstr "Notez que les liens vers les archives pourraient être encore affichés par votre thème et que vous aurez besoin de les enlever séparément."

#. translators: 1: expands to Yoast SEO extensions
#: admin/views/licenses.php:225
msgid "%1$s to optimize your site even further"
msgstr "%1$s pour optimiser encore plus votre site"

#. translators: 1: expands to Yoast SEO
#: admin/views/licenses.php:220
msgid "%1$s extensions"
msgstr "%1$s extensions"

#: admin/views/licenses.php:209
msgid "Comes with our 30-day no questions asked money back guarantee"
msgstr "Livré avec une garantie 100 % satisfait ou remboursé sans demande de justification."

#. translators: Text between 1: and 2: will only be shown to screen readers. 3:
#. expands to the product name.
#: admin/views/licenses.php:196 admin/views/licenses.php:300
msgid "More information %1$sabout %3$s%2$s"
msgstr "Plus d’informations %1$sà propos de %3$s %2$s"

#: admin/views/licenses.php:142 admin/views/licenses.php:246
msgid "Installed"
msgstr "Installée"

#: admin/views/licenses.php:137
msgid "gain access to our 24/7 support team."
msgstr "accédez à notre équipe de support 24/7."

#: admin/views/licenses.php:136
msgid "Premium support"
msgstr "Support premium"

#: admin/views/licenses.php:133
msgid "check what your Facebook or Twitter post will look like."
msgstr "vérifiez à quoi ressemblera votre statut Facebook ou Twitter."

#: admin/views/licenses.php:132
msgid "Social previews"
msgstr "Prévisualisation des réseaux sociaux"

#: admin/views/licenses.php:125
msgid "create and manage redirects from within your WordPress install."
msgstr "créez et gérez les redirections directement depuis votre installation WordPress."

#: admin/views/licenses.php:124
msgid "Redirect manager"
msgstr "Gestionnaire de redirections"

#. translators: 1: expands to Yoast SEO Premium
#: admin/views/licenses.php:112
msgid "%1$s, take your optimization to the next level!"
msgstr "%1$s emmène votre optimisation au niveau supérieur !"

#. translators: %1$s expands to Yoast SEO, %2$s expands to WooCommerce
#: admin/views/licenses.php:84
msgid "A seamless integration between %1$s and %2$s"
msgstr "Une intégration invisible entre %1$s et %2$s"

#. translators: %1$s expands to Yoast, %2$s expands to WooCommerce
#: admin/views/licenses.php:82
msgid "Use %1$s breadcrumbs instead of %2$s ones"
msgstr "Utiliser les fils d’Ariane de %1$s plutôt que ceux de %2$s."

#: admin/views/licenses.php:36
msgid "Easily insert Google Maps, a store locator, opening hours and more"
msgstr "Insérez facilement Google Maps, un localisateur de boutique, des horaires d’ouverture et bien plus"

#: admin/views/licenses.php:64
msgid "Creates XML News Sitemaps"
msgstr "Crée des plans de site d’actualités XML"

#: admin/views/licenses.php:63
msgid "Immediately pings Google on the publication of a new post"
msgstr "Notifie immédiatement Google de la sortie d’une nouvelle publication."

#: admin/views/licenses.php:62
msgid "Optimize your site for Google News"
msgstr "Optimisez votre site pour Google Actualités."

#: admin/views/licenses.php:51
msgid "Make videos responsive through enabling fitvids.js"
msgstr "Rendez vos vidéos responsive en activant fitvids.js"

#: admin/views/licenses.php:50
msgid "Enhance the experience of sharing posts with videos"
msgstr "Améliorez l’expérience du partage de publications contenant des vidéos."

#: admin/views/licenses.php:49
msgid "Show your videos in Google Videos"
msgstr "Affiche vos vidéos dans Google Vidéos"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:803
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifier &#8220;%s&#8221;"

#: admin/menu/class-base-menu.php:258
#: src/integrations/admin/menu-badge-integration.php:35
msgid "Premium"
msgstr "Premium"

#: admin/class-admin.php:255
msgid "Get Premium"
msgstr "Passer à la version premium"

#: inc/class-wpseo-admin-bar-menu.php:293
msgid "Google Trends"
msgstr "Tendances Google"

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:244
msgid "Notifications"
msgstr "Notifications"

#: admin/views/user-profile.php:48
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Retire l’onglet de la requête cible du gestionnaire de métadonnées et désactive toutes les suggestions SEO."

#: admin/views/user-profile.php:45
msgid "Disable SEO analysis"
msgstr "Désactiver l’analyse SEO"

#: admin/views/tabs/social/twitterbox.php:17
msgid "Twitter settings"
msgstr "Réglages Twitter"

#: admin/views/tabs/social/pinterest.php:16
msgid "Pinterest settings"
msgstr "Réglages Pinterest"

#: admin/views/tabs/social/facebook.php:18
msgid "Facebook settings"
msgstr "Réglages Facebook"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:12
msgid "Title separator symbol"
msgstr "Caractère de séparation du titre"

#: admin/views/tabs/metas/rss.php:15
msgid "RSS feed settings"
msgstr "Réglages des flux RSS"

#: admin/views/tabs/metas/breadcrumbs.php:15
msgid "Breadcrumbs settings"
msgstr "Réglages du fil d’Ariane"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Rendre principal"

#. translators: %s: number of notifications
#: admin/menu/class-admin-menu.php:120 inc/class-wpseo-admin-bar-menu.php:660
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s notification"
msgstr[1] "%s notifications"

#: admin/views/user-profile.php:57
msgid "Disable readability analysis"
msgstr "Désactiver l’analyse de lisibilité"

#: admin/views/user-profile.php:60
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Retire l’onglet de l’analyse de lisibilité du gestionnaire de métadonnées et désactive toutes les suggestions liées."

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:44 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Readability analysis"
msgstr "Analyse de la lisibilité"

#: admin/formatter/class-metabox-formatter.php:120
#: admin/formatter/class-metabox-formatter.php:150
#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:163
#: inc/class-wpseo-rank.php:195 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "Besoin d’amélioration"

#: admin/formatter/class-metabox-formatter.php:68
#: admin/metabox/class-metabox-section-readability.php:28
msgid "Readability"
msgstr "Lisibilité"

#: admin/formatter/class-metabox-formatter.php:199
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Improvements"
msgstr "Améliorations"

#: admin/views/partial-notifications-errors.php:22
msgid "Good job! We could detect no serious SEO problems."
msgstr "Beau travail ! Nous n’avons détecté aucun problème sérieux de référencement."

#: admin/views/partial-notifications-errors.php:21
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Nous avons détecté les problèmes suivants qui affectent le référencement de votre site."

#: admin/formatter/class-metabox-formatter.php:198
#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Problems"
msgstr "Problèmes"

#: admin/formatter/class-metabox-formatter.php:170
#: js/dist/externals/schemaBlocks.js:9
msgid "Analysis"
msgstr "Analyse"

#: admin/formatter/class-metabox-formatter.php:113
#: admin/formatter/class-metabox-formatter.php:143 inc/class-wpseo-rank.php:138
msgid "Not available"
msgstr "Non disponible"

#: admin/class-meta-columns.php:235
msgid "Filter by SEO Score"
msgstr "Filtrer par score SEO"

#: admin/class-meta-columns.php:151
msgid "Meta description not set."
msgstr "La méta description n’est pas définie."

#: admin/menu/class-admin-menu.php:54 admin/pages/dashboard.php:32
msgid "Dashboard"
msgstr "Tableau de bord"

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Vous pouvez corriger cela sur la %1$spage des réglages des permaliens%2$s."

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the primary category of the post/page"
msgstr "Remplacé par la catégorie principale de l’article/page"

#: admin/views/tabs/social/pinterest.php:36
msgid "Pinterest confirmation"
msgstr "Vérification de Pinterest"

#: admin/views/tabs/social/pinterest.php:24
msgid "If you have already confirmed your website with Pinterest, you can skip the step below."
msgstr "Si vous avez déjà confirmé votre site Web avec Pinterest, vous pouvez ignorer l’étape ci-dessous."

#: admin/views/tabs/metas/paper-content/taxonomy-content.php:18
#: js/dist/new-settings.js:155 js/dist/new-settings.js:228
msgid "Format-based archives"
msgstr "Archives par formats"

#: admin/views/tabs/dashboard/webmaster-tools.php:24
msgid "Webmaster Tools verification"
msgstr "Vérification des outils pour les webmasters"

#. translators: $s expands to Yoast SEO Premium
#. translators: %s expands to the product name
#. translators: 1: Yoast WooCommerce SEO
#: admin/views/licenses.php:182 admin/views/licenses.php:285
#: js/dist/integrations-page.js:5
msgid "Buy %s"
msgstr "Achetez %s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "Nouveau titre %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "Titre %1$s actuel"

#: inc/sitemaps/class-sitemaps-cache-validator.php:294
msgid "Expected an integer as input."
msgstr "L’entrée doit être un entier."

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Tentative de construction d’une clé de cache sécurisée pour le plan de site, mais la combinaison du suffixe et du préfixe laisse trop peu de place pour le faire. Vous demandez probablement une page qui est bien loin de la plage attendue."

#: admin/views/redirects.php:32
#: src/integrations/admin/redirects-integration.php:48
msgid "Redirects"
msgstr "Redirections"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:280
msgid "Remove"
msgstr "Supprimer"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:279
msgid "Keep"
msgstr "Conserver"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:32
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Bold the last page"
msgstr "Mettre en gras la dernière page"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:27
msgid "Regular"
msgstr "Normal"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:26
msgid "Bold"
msgstr "Gras"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:22
msgid "Show Blog page"
msgstr "Afficher la page de blog"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "%s principal"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Principal"

#. translators: accessibility text. %1$s expands to the term title, %2$s to the
#. taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Faire de %1$s le %2$s principal"

#: admin/taxonomy/class-taxonomy-columns.php:152
msgid "Term is set to noindex."
msgstr "Le terme est réglé sur noindex."

#: admin/views/tabs/metas/archives.php:19
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:16
#: js/dist/new-settings.js:71 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Author archives"
msgstr "Archives d’auteur"

#: admin/pages/dashboard.php:47 admin/pages/network.php:20
#: admin/views/tabs/dashboard/integrations.php:21
#: admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:73
#: js/dist/integrations-page.js:33
msgid "Integrations"
msgstr "Intégrations"

#: src/integrations/admin/crawl-settings-integration.php:214
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "Activé"

#: src/integrations/admin/crawl-settings-integration.php:213
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Disabled"
msgstr "Désactivé"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1459
msgid "The separator defined in your theme's %s tag."
msgstr "Le séparateur défini dans la balise %s de votre thème."

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "No index"

#: src/deprecated/admin/ryte/class-ryte.php:137
msgid "Once Weekly"
msgstr "Une fois par semaine"

#: admin/class-meta-columns.php:100
#: admin/taxonomy/class-taxonomy-columns.php:87
msgid "SEO score"
msgstr "Score SEO"

#. Author of the plugin
msgid "Team Yoast"
msgstr "L’équipe Yoast"

#. Description of the plugin
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "La première solution SEO tout-en-un pour WordPress, y compris l’analyse des pages de contenu, les plans de site XML et bien plus encore."

#. Plugin Name of the plugin
#: admin/capabilities/class-capability-manager-integration.php:72
#: js/dist/block-editor.js:468
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: wp-seo-main.php:546
msgid "Activation failed:"
msgstr "Échec de l’activation :"

#: wp-seo-main.php:535
msgid "The filter extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L’extension \"filter\" semble indisponible. Veuillez demander à votre hébergeur de l’activer."

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:511
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "L’installation de l’extension %1$s est incomplète. Veuillez vous référer %2$saux informations d’installation%3$s."

#: wp-seo-main.php:489
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "Les extensions de la Librairie Standard PHP (SPL) semblent ne pas être disponibles.Demandez à votre hébergeur de les activer."

#: inc/class-wpseo-admin-bar-menu.php:429
#: inc/class-wpseo-admin-bar-menu.php:477
msgid "SEO Settings"
msgstr "Réglages SEO"

#: inc/class-wpseo-admin-bar-menu.php:394
msgid "Mobile-Friendly Test"
msgstr "Test Mobile-Friendly"

#: inc/class-wpseo-admin-bar-menu.php:389
msgid "Google Page Speed Test"
msgstr "Test de vitesse de page Google"

#: inc/class-wpseo-admin-bar-menu.php:384
msgid "CSS Validator"
msgstr "Validateur CSS"

#: inc/class-wpseo-admin-bar-menu.php:379
msgid "HTML Validator"
msgstr "Validateur HTML"

#: inc/class-wpseo-admin-bar-menu.php:374
msgid "Pinterest Rich Pins Validator"
msgstr "Validateur de Rich Pins Pinterest"

#: inc/class-wpseo-admin-bar-menu.php:369
msgid "Facebook Debugger"
msgstr "Facebook Debugger"

#: inc/class-wpseo-admin-bar-menu.php:359
msgid "Check Google Cache"
msgstr "Vérifier le cache de Google"

#: inc/class-wpseo-admin-bar-menu.php:339
msgid "Analyze this page"
msgstr "Analyser cette page"

#: inc/class-wpseo-admin-bar-menu.php:275
msgid "Keyword Research"
msgstr "Recherche de mots-clés"

#. translators: %s expands to an invalid URL.
#: inc/options/class-wpseo-option.php:394
msgid "%s does not seem to be a valid url. Please correct."
msgstr "%s ne semble pas être une URL valide. Veuillez corriger cela."

#. translators: 1: Verification string from user input; 2: Service name.
#: inc/options/class-wpseo-option.php:358
msgid "%1$s does not seem to be a valid %2$s verification string. Please correct."
msgstr "%1$s ne semble pas être une chaine de caractères de vérification valide pour %2$s. Veuillez corriger."

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1310 inc/options/class-wpseo-option-titles.php:284
msgid "%s Archive"
msgstr "%s Archive"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:249
msgid "You searched for %s"
msgstr "Vous avez cherché %s"

#. translators: %s expands to a twitter user name.
#: inc/options/class-wpseo-option-social.php:182
msgid "%s does not seem to be a valid Twitter Username. Please correct."
msgstr "%s ne semble pas être un identifiant Twitter valide. Veuillez le corriger."

#: inc/options/class-wpseo-option-social.php:109
msgid "Summary with large image"
msgstr "Résumé avec grande image"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:252
msgid "The post %1$s appeared first on %2$s."
msgstr "L’article %1$s est apparu en premier sur %2$s."

#: inc/options/class-wpseo-option-ms.php:245
msgid "No numeric value was received."
msgstr "Les chiffres ne sont pas acceptés"

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:233
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Blog inexistant. Le blog %s n’existe pas ou a été marqué comme étant supprimé."

#: inc/options/class-wpseo-option-ms.php:229
#: inc/options/class-wpseo-option-ms.php:245
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "Le réglage par défaut du blog doit être l’identifiant numérique du blog que vous souhaitez utiliser par défaut."

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:209
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$s n’est pas un choix valide pour ceux qui devraient être autorisés d’accéder aux réglages de %2$s. La valeur par défaut a été remise."

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:559
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Veuillez choisir un type de publication valide pour la taxonomie « %s »"

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:521
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Veuillez choisir une taxonomie valide pour le type de publication « %s »"

#: inc/options/class-wpseo-option-titles.php:257
msgid "You searched for"
msgstr "Vous avez cherché"

#: inc/options/class-wpseo-option-titles.php:256
msgid "Home"
msgstr "Accueil"

#: inc/options/class-wpseo-option-titles.php:255
msgid "Archives for"
msgstr "Archives pour"

#: inc/options/class-wpseo-option-titles.php:254
msgid "Error 404: Page not found"
msgstr "Erreur 404 : Page introuvable"

#: admin/formatter/class-metabox-formatter.php:134
#: admin/formatter/class-metabox-formatter.php:164
#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:173
#: inc/class-wpseo-rank.php:205 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "Bon"

#: admin/views/tabs/metas/paper-content/rss-content.php:33
#: admin/views/tool-bulk-editor.php:111 js/dist/new-settings.js:189
msgid "Description"
msgstr "Description"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:77
#: js/dist/new-settings.js:239
msgid "Blog"
msgstr "Blog"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Replaced with a custom taxonomies description"
msgstr "Remplacé par une description des taxonomies personnalisées."

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Remplacé par la(les) catégorie(s) de l’article, séparé par des virgules"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with a posts custom field value"
msgstr "Remplacé par une valeur de champ personnalisé"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the slug which caused the 404"
msgstr "Remplacé par le slug qui a causé l’erreur 404"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the posts focus keyphrase"
msgstr "Remplacé par la requête cible des publications"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Attachment caption"
msgstr "Légende de la pièce-jointe"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the current page number"
msgstr "Remplacé par le numéro de la page en cours"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the current page total"
msgstr "Remplacé par le nombre total de pages"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Remplacé par le numéro de page en cours avec le contexte (ex: page 2 sur 4)"

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Remplacé par « les informations biographiques » de l’auteur de la publication"

#: inc/class-wpseo-replace-vars.php:1513
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Remplacé par l’identifiant normalisé de l’auteur de la publication"

#: inc/class-wpseo-replace-vars.php:1512
msgid "Replaced with the post/page ID"
msgstr "Remplacé par l’ID de la publication"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Replaced with the post/page modified time"
msgstr "Remplacé par l’heure de modification de la publication"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Replaced with the content type plural label"
msgstr "Remplacé par l’intitulé au pluriel du type de contenu"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Replaced with the content type single label"
msgstr "Remplacé par l’intitulé au singulier du type de contenu"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the current search phrase"
msgstr "Remplacé par la phrase recherchée"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the term name"
msgstr "Remplacé par le nom du terme"

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the term description"
msgstr "Remplacé par la description du terme"

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the tag description"
msgstr "Remplacé par la description du mot-clé"

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the category description"
msgstr "Remplacé par la description de la catégorie"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post categories (comma separated)"
msgstr "Remplacé par la(les) catégorie(s) de l’article (séparées par des virgules)"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Replaced with the current tag/tags"
msgstr "Remplacé par le(les) mot(s)-clé(s)"

#: inc/class-wpseo-replace-vars.php:1474
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Remplacé par l’extrait de la publication (sans auto-génération)"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Remplacé par l’extrait de la publication (ou auto-généré, si l’extrait n’existe pas)"

#: inc/class-wpseo-replace-vars.php:1471
msgid "The site's name"
msgstr "Le nom du site"

#: inc/class-wpseo-replace-vars.php:1469
msgid "Replaced with the title of the parent page of the current page"
msgstr "Remplacé par le titre de la page parent de la présente page"

#: inc/class-wpseo-replace-vars.php:1468
msgid "Replaced with the title of the post/page"
msgstr "Remplacé par le titre de la publication"

#: inc/class-wpseo-replace-vars.php:1467
msgid "Replaced with the date of the post/page"
msgstr "Remplacé par la date de la publication"

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1030
msgid "Page %1$d of %2$d"
msgstr "Page %1$d sur %2$d"

#: inc/class-wpseo-replace-vars.php:122
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Vous ne pouvez pas annuler une variable de remplacement standard de WPSEO en enregistrant une variable avec le même nom. Utilisez le  filtre \"wpseo_replacements\"  au lieu d’ajuster la valeur de remplacement."

#: inc/class-wpseo-replace-vars.php:118
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "Une variable de remplacement portant le même nom est déjà enregistrée. Essayez d’utiliser un nom de variable unique."

#: inc/class-wpseo-replace-vars.php:108
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "Une variable de remplacement ne peut commencer par \"%%cf_\" ou \"%%ct_\" car ces préfixes sont réservés aux variables standards de WPSEO pour les champs et les taxonomies personnalisés. Essayez d’utiliser un nom de variable unique."

#: inc/class-wpseo-replace-vars.php:105
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "Une variable de remplacement ne peut contenir que des caractères alpha-numériques, un souligné ou un tiret. Veuillez renommer votre variable"

#. Translators: %1$s resolves to the SEO menu item, %2$s resolves to the Search
#. Appearance submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Avis aux administrateurs : cette page n’affiche pas de méta description car elle n’en a pas. Vous pouvez donc soit l’ajouter spécifiquement pour cette page soit vous rendre dans vos réglages (%1$s - %2$s) pour configurer un modèle."

#: inc/options/class-wpseo-option-titles.php:250
msgid "Page not found"
msgstr "Page non trouvée"

#. translators: %s expands to the variable used for term title.
#: admin/formatter/class-term-metabox-formatter.php:164
#: inc/class-upgrade.php:1313 inc/options/class-wpseo-option-titles.php:320
msgid "%s Archives"
msgstr "Archives des %s"

#: admin/views/user-profile.php:28
msgid "Meta description to use for Author page"
msgstr "Méta description à utiliser pour la page Auteur"

#: admin/views/user-profile.php:24
msgid "Title to use for Author page"
msgstr "Titre à utiliser pour la page Auteur"

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:11
msgid "%1$s settings"
msgstr "Réglages de %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Exporter vos réglages de %1$s"

#: admin/class-export.php:61 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:85
msgid "Import settings"
msgstr "Importer les réglages"

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:91
msgid "Import from other SEO plugins"
msgstr "Importer à partir d’autres extensions de SEO"

#: admin/views/tabs/tool/import-seo.php:82
msgid "Import"
msgstr "Importer"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Si vous aviez un %s et qu’il était modifiable, vous pourriez le modifier à partir d’ici."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "Si votre %s était accessible en écriture, vous pourriez le modifier à partir d’ici."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "Vous n’avez pas de fichier %s, créez-en un ici :"

#: admin/views/tabs/metas/paper-content/rss-content.php:51
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name and description as anchor text."
msgstr "Un lien vers votre site, avec le nom de votre site et la description comme texte d’ancrage."

#: admin/views/tabs/metas/paper-content/rss-content.php:47
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name as anchor text."
msgstr "Un lien vers votre site, avec le nom de votre site comme texte d’ancrage."

#: admin/views/tabs/metas/paper-content/rss-content.php:43
#: js/dist/new-settings.js:189
msgid "A link to the post, with the title as anchor text."
msgstr "Un lien vers la publication, avec le titre pour ancre de lien."

#: admin/views/tabs/metas/paper-content/rss-content.php:39
#: js/dist/new-settings.js:189
msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr "Un lien vers les archives de l’auteur, avec le nom de l’auteur pour ancre de lien."

#: admin/views/tabs/metas/paper-content/rss-content.php:20
#: js/dist/new-settings.js:189
msgid "You can use the following variables within the content, they will be replaced by the value on the right."
msgstr "Vous pouvez utiliser les variables suivantes dans votre contenu, elles seront automatiquement remplacées par la valeur à droite."

#: admin/views/tabs/metas/paper-content/rss-content.php:15
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put after each post in the feed"
msgstr "Contenu à insérer après chaque article dans le flux"

#: admin/views/tabs/metas/paper-content/rss-content.php:14
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put before each post in the feed"
msgstr "Le contenu à ajouter avant chaque article dans le flux"

#: admin/views/tabs/metas/rss.php:21
msgid "This feature is used to automatically add content to your RSS, more specifically, it's meant to add links back to your blog and your blog posts, so dumb scrapers will automatically add these links too, helping search engines identify you as the original source of the content."
msgstr "Cette fonctionnalité est utilisée pour ajouter automatiquement du contenu à vos flux RSS, plus spécifiquement, pour ajouter des liens vers votre blog et vos articles. Les agrégateurs de contenus vont également ajouter ces liens, aidant ainsi les moteurs de recherche à identifier votre site comme étant l’origine du contenu."

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#. translators: %1$s and %2$s are replaced by opening and closing <a> tags.
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:103
#: js/dist/new-settings.js:92
msgid "Usage of this breadcrumbs feature is explained in %1$sour knowledge-base article on breadcrumbs implementation%2$s."
msgstr "L’utilisation de la fonctionnalité du fil d’Ariane est expliquée dans l’article %1$sde notre base de connaissances sur l’intégration du fil d’Ariane%2$s."

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:98
#: js/dist/new-settings.js:91
msgid "How to insert breadcrumbs in your theme"
msgstr "Comment insérer un fil d’Ariane dans votre thème ?"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:73
msgid "Content type archive to show in breadcrumbs for taxonomies"
msgstr "Archive de type de contenu à afficher dans le fil d’Ariane pour les taxonomies"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:43
msgid "Taxonomy to show in breadcrumbs for content types"
msgstr "Taxonomie à afficher dans le fil d’Ariane pour les types de publications"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:17
msgid "Breadcrumb for 404 Page"
msgstr "Fil d’Ariane pour la page 404 "

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:16
msgid "Prefix for Search Page breadcrumbs"
msgstr "Préfixe pour le fil d’Ariane des pages de recherche "

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:15
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for Archive breadcrumbs"
msgstr "Préfixe pour le fil d’Ariane des archives "

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:14
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for the breadcrumb path"
msgstr "Préfixe pour le fil d’Ariane "

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:13
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Anchor text for the Homepage"
msgstr "Texte d’ancrage pour la page d’accueil "

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:12
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Separator between breadcrumbs"
msgstr "Séparateur pour le fil d’Ariane "

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Retrouvez ci-dessous les scores SEO de vos articles publiés. C’est le moment de commencer à améliorer certains d’entre eux !"

#: admin/views/tabs/dashboard/dashboard.php:51
msgid "Credits"
msgstr "Crédits"

#: admin/pages/tools.php:70
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Retour à la page Outils"

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:43
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s propose des outils intégrés très puissants :"

#: admin/pages/tools.php:31
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Cet outil vous permet de modifier rapidement des fichiers importants pour votre référencement, comme le fichier robots.txt ou le fichier .htaccess si vous en avez un."

#: admin/pages/tools.php:30
msgid "File editor"
msgstr "Éditeur de fichiers"

#: admin/pages/tools.php:24
msgid "Import and Export"
msgstr "Import et Export"

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Cet outil vous permet de modifier rapidement les titres et les descriptions de vos articles et pages, sans avoir à aller dans l’éditeur de chaque publication."

#: admin/pages/tools.php:36
msgid "Bulk editor"
msgstr "Éditeur par lot"

#. translators: %1$s / %2$s expands to a link to pinterest.com's help page.
#: admin/views/tabs/social/pinterest.php:30
msgid "To %1$sconfirm your site with Pinterest%2$s, add the meta tag here:"
msgstr "Pour %1$svérifier votre site avec Pinterest%2$s, veuillez ajouter la balise méta ici :"

#: admin/views/tabs/social/pinterest.php:20
msgid "Pinterest uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph meta data\" setting on the Facebook tab enabled if you want to optimize your site for Pinterest."
msgstr "Pinterest, tout comme Facebook, utilise les métadonnées Open Graph. Assurez-vous donc d’avoir activé le réglage « Ajouter les métadonnées Open Graph » dans l’onglet Facebook si vous souhaitez optimiser votre site pour Pinterest."

#: admin/views/tabs/social/twitterbox.php:24
msgid "Add Twitter card meta data"
msgstr "Ajoute les métadonnées de carte Twitter"

#: admin/views/tabs/social/facebook.php:54
msgid "This image is used if the post/page being shared does not contain any images."
msgstr "Cette image est utilisée si la publication partagée ne contient aucune image."

#: src/integrations/admin/import-integration.php:117
msgid "Default settings"
msgstr "Réglages par défaut"

#: admin/views/tabs/social/facebook.php:20
msgid "Add Open Graph meta data"
msgstr "Ajouter les métadonnées OpenGraph"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current date"
msgstr "Remplacé par la date actuelle"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current day"
msgstr "Remplacé par la date actuelle"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Replaced with the current month"
msgstr "Remplacé par le mois en cours"

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the current year"
msgstr "Remplacé par l’année en cours"

#: admin/pages/tools.php:25
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "Importez les réglages d’autres extensions de référencement et exportez les vôtres pour les réutiliser sur un autre site."

#: admin/views/redirects.php:112
msgid "URL"
msgstr "Adresse web (URL)"

#: admin/pages/social.php:18
msgid "Accounts"
msgstr "Comptes"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Restaurer les réglages du site aux valeurs par défaut"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "ID du site"

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Grâce à ce formulaire, vous pouvez réinitialiser les réglages SEO de votre site aux réglages par défaut."

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "Les réglages confidentiels (administrateurs Facebook etc.), spécifiques aux thèmes (réécriture des titres) et quelques réglages très spécifiques au site actuel ne seront pas importés vers les nouveaux sites."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Saisissez l’%1$sID du site%2$s que vous utiliserez comme base pour les réglages de tous les nouveaux sites ajoutés à votre réseau. Laissez vide pour n’en choisir aucun. (Les réglages par défaut de l’extension seront alors utilisés)."

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Choisissez le site dont les réglages serviront de base à tous les nouveaux sites de votre réseau. Si vous choisissez ’Aucun’, les réglages par défaut de WordPress SEO seront utilisés."

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "Les nouveaux sites du réseau héritent des réglages SEO de ce site"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "Super Administrateur uniquement"

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "Administrateurs du site (par défaut)"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "Qui devrait avoir accès aux réglages de %1$s"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "indésirable"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "adulte"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "archivé"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "public"

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:164
msgid "%s restored to default SEO settings."
msgstr "%s a été rétabli aux réglages SEO par défaut."

#: admin/class-yoast-network-admin.php:130
msgid "Settings Updated."
msgstr "Les réglages ont été mis à jour."

#: admin/views/tabs/metas/paper-content/special-pages.php:26
#: js/dist/new-settings.js:228
msgid "404 pages"
msgstr "Pages 404"

#: admin/views/tabs/metas/paper-content/special-pages.php:21
#: js/dist/new-settings.js:228
msgid "Search pages"
msgstr "Pages de recherche"

#. translators: %s expands to <code>noindex, follow</code>.
#: admin/views/tabs/metas/paper-content/special-pages.php:15
msgid "These pages will be %s by default, so they will never show up in search results."
msgstr "Ces pages seront réglées sur %s par défaut, donc elles n’apparaitront jamais dans les résultats de recherche."

#: admin/views/tabs/metas/archives/help.php:21
msgid "Date-based archives could in some cases also be seen as duplicate content."
msgstr "Les archives basées sur la date pourraient, dans certains cas également être considérées comme du contenu dupliqué."

#. translators: %s expands to <code>noindex, follow</code>
#: admin/views/tabs/metas/archives/help.php:17
msgid "If this is the case on your site, you can choose to either disable it (which makes it redirect to the homepage), or to add %s to it so it doesn't show up in the search results."
msgstr "Si c’est le cas pour votre site, vous pouvez choisir de le désactiver (ce qui la redirigera vers la page d’accueil), ou d’y ajouter %s, de sorte qu’il n’apparaisse pas dans les résultats de recherche."

#. translators: %1$s / %2$s: links to an article about duplicate content on
#. yoast.com
#: admin/views/tabs/metas/archives/help.php:11
msgid "If you're running a one author blog, the author archive will be exactly the same as your homepage. This is what's called a %1$sduplicate content problem%2$s."
msgstr "Si vous administrez un blog avec un seul auteur, la page d’archive de l’auteur sera exactement identique à votre page d’accueil. C’est ce qu’on appelle un %1$sproblème de contenu dupliqué%2$s."

#: admin/views/tabs/metas/archives.php:24
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:16
#: js/dist/new-settings.js:127 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Date archives"
msgstr "Archives par date"

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:39
msgid "author archives"
msgstr "Archives de l’auteur"

#: admin/views/tool-bulk-editor.php:109 inc/class-wpseo-replace-vars.php:1468
#: js/dist/externals-redux.js:1
msgid "Title"
msgstr "Titre"

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "À savoir :"

#: admin/views/tabs/metas/general.php:31
msgid "Title Separator"
msgstr "Séparateur de titre"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:14
msgid "%1$s has auto-detected whether it needs to force rewrite the titles for your pages, if you think it's wrong and you know what you're doing, you can change the setting here."
msgstr "%1$s détecte automatiquement s’il a besoin de forcer la réécriture des titres de vos pages, si vous pensez que c’est incorrect et que vous savez ce que vous faites, vous pouvez modifier ce réglage ici."

#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:10
msgid "Force rewrite titles"
msgstr "Forcer la réécriture des titres"

#: admin/pages/metas.php:21
msgid "Taxonomies"
msgstr "Taxonomies"

#: admin/views/tabs/metas/general.php:47 js/dist/new-settings.js:172
#: js/dist/new-settings.js:224 js/dist/new-settings.js:235
msgid "Homepage"
msgstr "Page d’accueil"

#. translators: %1$s expands to Yoast SEO.
#: admin/views/licenses.php:94
msgid "%1$s Extensions"
msgstr "Extensions de %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:82 admin/views/licenses.php:77
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Intégrez WooCommerce et %1$s et obtenez des fonctionnalités supplémentaires !"

#: admin/class-plugin-availability.php:70 admin/views/licenses.php:32
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Améliorez votre classement localement dans Google Maps, sans verser une goutte de sueur!"

#: admin/class-plugin-availability.php:60 admin/views/licenses.php:59
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Êtes-vous sur Google Actualités ? Augmentez votre trafic de Google News grâce à son optimisation !"

#: admin/class-plugin-availability.php:50 admin/views/licenses.php:46
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Optimisez vos vidéos pour qu’elles apparaissent dans les résultats des recherches et obtenir plus de clics !"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:38 admin/views/licenses.php:22
msgid "The premium version of %1$s with more features & support."
msgstr "La version Premium de %1$s avec plus de fonctionnalités & de support."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:64
#: src/integrations/admin/first-time-configuration-integration.php:491
#: js/dist/new-settings.js:230
msgid "Person"
msgstr "Personne"

#. translators: %1$s opens the link to the Yoast.com article about Google's
#. Knowledge Graph, %2$s closes the link,
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:29
msgid "This data is shown as metadata in your site. It is intended to appear in %1$sGoogle's Knowledge Graph%2$s. You can be either an organization, or a person."
msgstr "Cette donnée est affichée comme une métadonnée dans votre site. Elle apparaît dans le %1$sKnowledge Graph de Google%2$s. Vous pouvez être une société ou une personne."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:14
msgid "Website name"
msgstr "Nom du Site Web"

#: admin/pages/dashboard.php:56
msgid "Webmaster Tools"
msgstr "Outils pour les webmasters"

#: admin/pages/metas.php:24 js/dist/new-settings.js:189
#: js/dist/new-settings.js:235
msgid "RSS"
msgstr "RSS"

#: admin/pages/metas.php:23 js/dist/new-settings.js:91
#: js/dist/new-settings.js:224 js/dist/new-settings.js:226
#: js/dist/new-settings.js:235
msgid "Breadcrumbs"
msgstr "Fil d’Ariane"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "Pour pouvoir créer une redirection et régler ce problème, vous avez besoin de %1$s. Vous pouvez acheter l&rsquo;extension, incluant un an de support et de mises à jour, sur %2$s."

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "la création de redirection est une fonctionnalité de %s."

#. translators: 1: link open tag; 2: link close tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:39
#: js/dist/new-settings.js:170
msgid "You can determine the title and description for the homepage by %1$sediting the homepage itself%2$s."
msgstr "Vous pouvez définir le titre et la description de la page d’accueil en %1$sla modifiant directement%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:48
msgid "You can determine the title and description for the posts page by %1$sediting the posts page itself%2$s."
msgstr "Vous pouvez définir le titre et la description de la page des articles en %1$sla modifiant directement%2$s."

#: admin/views/tabs/metas/general.php:50
msgid "Homepage &amp; Posts page"
msgstr "Page d’accueil &amp; page des articles"

#: admin/views/tabs/metas/archives.php:29 js/dist/new-settings.js:235
msgid "Special pages"
msgstr "Pages spéciales"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Permalink"
msgstr "Permalien"

#: admin/views/redirects.php:175 admin/views/redirects.php:222
msgid "New URL"
msgstr "Nouvelle URL"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:214
msgid "Deactivate %s"
msgstr "Désactiver %s"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:210
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "L’extension %1$s pourrait provoquer des problèmes si utiliser en conjonction avec %2$s."

#: admin/class-yoast-form.php:741 admin/metabox/class-metabox.php:649
#: admin/taxonomy/class-taxonomy-fields-presenter.php:127
msgid "Upload Image"
msgstr "Téléverser une image"

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:75
msgid "%s Posts Overview"
msgstr "Vue d’ensemble des publications de %s"

#: admin/views/tabs/metas/paper-content/post-type-content.php:81
#: js/dist/new-settings.js:45
msgid "Breadcrumbs title"
msgstr "Titre du fil d’Ariane"

#: admin/pages/social.php:21 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:235
msgid "Pinterest"
msgstr "Pinterest "

#: admin/pages/metas.php:22
msgid "Archives"
msgstr "Archives"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/externals-components.js:88 js/dist/externals/componentsNew.js:136
msgid "Close"
msgstr "Fermer"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:76
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "%1$s et %2$s peuvent tous les deux créer des plans de site XML. En avoir deux n’est pas recommandé pour les moteurs de recherche et pourrait même ralentir votre site."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:69
msgid "Configure %1$s's Open Graph settings"
msgstr "Configurer les réglages de l’OpenGraph de %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:65
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, Twitter, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "%1$s et %2$s créent des métadonnées OpenGraph, ce qui pourrait faire que Facebook, Twitter, LinkedIn et d’autres réseaux sociaux utilisent de mauvais textes et de mauvaises images lorsque vos pages sont partagées."

#: admin/pages/social.php:20 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Twitter"
msgstr "Twitter"

#: admin/pages/social.php:19 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Facebook"
msgstr "Facebook"

#: admin/formatter/class-metabox-formatter.php:127
#: admin/formatter/class-metabox-formatter.php:157
#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:168
#: inc/class-wpseo-rank.php:200 js/dist/block-editor.js:227
#: js/dist/editor-modules.js:227 js/dist/externals-components.js:120
#: js/dist/externals/analysis.js:383 js/dist/frontend-inspector-resources.js:1
#: js/dist/post-edit.js:11 js/dist/term-edit.js:1
msgid "OK"
msgstr "OK"

#: admin/class-meta-columns.php:108
msgid "Meta Desc."
msgstr "Méta Desc."

#: admin/class-meta-columns.php:239
msgid "All SEO Scores"
msgstr "Tous les scores SEO"

#: admin/metabox/class-metabox.php:927
#: src/integrations/third-party/elementor.php:443
msgid "SEO issue: The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr "Problème SEO : L’image à la Une doit être au moins de 200 x 200 pixels pour être exploitable par Facebook et les autres réseaux sociaux."

#: admin/views/tabs/metas/paper-content/front-page-content.php:26
#: src/integrations/admin/social-templates-integration.php:188
msgid "Social settings"
msgstr "Réglages réseaux sociaux"

#: admin/class-bulk-editor-list-table.php:1001
msgid "Action"
msgstr "Action"

#: admin/metabox/class-metabox.php:882 admin/taxonomy/class-taxonomy.php:151
#: admin/taxonomy/class-taxonomy.php:273
#: src/integrations/third-party/elementor.php:398
msgid "(no parent)"
msgstr "(aucun parent)"

#: admin/class-meta-columns.php:673
msgid "Post is set to noindex."
msgstr "La publication est réglée sur noindex."

#: admin/metabox/class-metabox.php:216
msgid "The URL that this page should redirect to."
msgstr "L’URL vers laquelle cette page devrait rediriger."

#: admin/metabox/class-metabox.php:215
msgid "301 Redirect"
msgstr "Redirection 301"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:204
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "L’URL canonique vers laquelle cette page devrait pointer. Laissez ce champ vide pour utiliser le slug par défaut. Les %1$sURL canoniques vers d’autres domaines%2$s sont aussi prises en charge."

#: admin/metabox/class-metabox.php:200 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Canonical URL"
msgstr "URL canonique"

#: admin/metabox/class-metabox.php:198
msgid "Title to use for this page in breadcrumb paths"
msgstr "Titre à utiliser pour cette page dans le fil d’Ariane"

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Breadcrumbs Title"
msgstr "Titre pour le fil d’Ariane"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Snippet"
msgstr "Pas de métadonnées"

#: admin/metabox/class-metabox.php:194 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Archive"
msgstr "Aucune archive"

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Image Index"
msgstr "Pas d’index pour l’image"

#: admin/class-yoast-network-admin.php:43
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:47
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:75
#: src/config/schema-types.php:163 js/dist/new-settings.js:239
msgid "None"
msgstr "Aucun"

#: admin/metabox/class-metabox.php:191 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Meta robots advanced"
msgstr "Méta robots avancés"

#: admin/metabox/class-metabox.php:179
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Avertissement : même si vous pouvez modifier les méta « robots » ici, tout votre site est réglé en mode « noindex » dans les réglages de vie privée, toute modification effectuée ici n’aura donc aucun effet."

#: admin/metabox/class-metabox.php:174 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "Meta description"
msgstr "Méta description"

#: admin/class-meta-columns.php:107
msgid "SEO Title"
msgstr "Méta titre "

#: inc/class-wpseo-replace-vars.php:1519
msgid "Focus keyword"
msgstr "Requête cible"

#: admin/import/class-import-settings.php:111
msgid "Settings successfully imported."
msgstr "Les réglages ont été importés."

#: admin/import/class-import-settings.php:79
msgid "Settings could not be imported:"
msgstr "Les réglagles n’ont pas été importés :"

#: admin/class-customizer.php:193
msgid "Breadcrumb for 404 pages:"
msgstr "Fil d’Ariane pour la page 404 :"

#: admin/class-customizer.php:181
msgid "Prefix for search result pages:"
msgstr "Préfixe pour les pages de résultats de recherche :"

#: admin/class-customizer.php:169
msgid "Prefix for archive pages:"
msgstr "Préfixe pour les pages d’archive :"

#: admin/class-customizer.php:157
msgid "Prefix for breadcrumbs:"
msgstr "Préfixe pour le fil d’Ariane :"

#: admin/class-customizer.php:145
msgid "Anchor text for the homepage:"
msgstr "Texte d’ancrage pour la page d’accueil"

#: admin/class-customizer.php:132
msgid "Breadcrumbs separator:"
msgstr "Séparateur pour le fil d’Ariane :"

#. translators: %s is the name of the plugin
#: admin/class-customizer.php:86
msgid "%s Breadcrumbs"
msgstr "Fil d’Ariane de %s"

#: admin/class-config.php:135 admin/metabox/class-metabox.php:907
#: admin/taxonomy/class-taxonomy.php:166
#: src/integrations/third-party/elementor.php:424
msgid "Use Image"
msgstr "Utiliser l’image"

#: admin/class-bulk-editor-list-table.php:996
msgid "Page URL/Slug"
msgstr "URL/Slug de la page"

#: admin/class-bulk-editor-list-table.php:995
msgid "Publication date"
msgstr "Date de Publication"

#: admin/class-bulk-editor-list-table.php:994
msgid "Post Status"
msgstr "État de la publication"

#: admin/class-bulk-editor-list-table.php:992
msgid "WP Page Title"
msgstr "Titre de la page"

#: admin/class-bulk-editor-list-table.php:826 js/dist/block-editor.js:311
#: js/dist/classic-editor.js:307 js/dist/dashboard-widget.js:1
#: js/dist/dashboard-widget.js:20 js/dist/editor-modules.js:261
#: js/dist/elementor.js:311
msgid "View"
msgstr "Voir"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:825
msgid "View &#8220;%s&#8221;"
msgstr "Voir &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:816
msgid "Preview"
msgstr "Prévisualisation"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:815
msgid "Preview &#8220;%s&#8221;"
msgstr "Prévisualiser &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:804
#: js/dist/first-time-configuration.js:13
#: js/dist/first-time-configuration.js:225 js/dist/indexables-page.js:34
#: js/dist/indexables-page.js:35 js/dist/indexables-page.js:46
#: js/dist/installation-success.js:13
msgid "Edit"
msgstr "Modifier"

#: admin/class-bulk-editor-list-table.php:415 admin/views/redirects.php:141
msgid "Filter"
msgstr "Filtrer"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:337
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Supprimez le <span class=\"count\">(%s)</span>"
msgstr[1] "Supprimez les <span class=\"count\">(%s)</span>"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:288
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Tout <span class=\"count\">(%s)</span>"
msgstr[1] "Tous <span class=\"count\">(%s)</span>"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Nouvelle méta description Yoast"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Méta description Yoast actuelle"

#: admin/class-admin.php:288
msgid "Facebook profile URL"
msgstr "URL du profil Facebook"

#: admin/class-admin.php:295
msgid "Twitter username (without @)"
msgstr "Identifiant Twitter (sans le @)"

#: admin/class-admin.php:227 js/dist/structured-data-blocks.js:13
msgid "FAQ"
msgstr "FAQ"

#: admin/class-admin.php:222 admin/views/redirects.php:42
#: src/integrations/settings-integration.php:261
#: js/dist/structured-data-blocks.js:9
msgid "Settings"
msgstr "Réglages"

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Important problème SEO : Vous bloquez actuellement l’accès aux robots des moteurs de recherche."

#: admin/class-admin.php:182
msgid "Posts"
msgstr "Articles"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Modifier les fichiers"

#: admin/menu/class-admin-menu.php:89
#: admin/menu/class-network-admin-menu.php:56 admin/pages/metas.php:18
#: admin/pages/network.php:18 js/dist/new-settings.js:235
msgid "General"
msgstr "Réglages généraux"

#: admin/menu/class-network-admin-menu.php:66
msgid "Extensions"
msgstr "Extensions"

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "Search Console"

#: admin/menu/class-admin-menu.php:97 js/dist/new-settings.js:217
msgid "Tools"
msgstr "Outils"

#: admin/views/class-yoast-feature-toggles.php:141 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "XML sitemaps"
msgstr "Plans de site XML"

#: admin/menu/class-admin-menu.php:96 admin/metabox/class-metabox.php:439
#: admin/taxonomy/class-taxonomy-metabox.php:146
msgid "Social"
msgstr "Réseaux sociaux"

#: admin/metabox/class-metabox.php:418
#: admin/taxonomy/class-taxonomy-metabox.php:132
#: inc/class-wpseo-admin-bar-menu.php:508
#: src/presenters/meta-description-presenter.php:36
#: src/services/health-check/report-builder.php:168
#: js/dist/structured-data-blocks.js:13
msgid "SEO"
msgstr "SEO"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s a détecté que vous utiliser la version de %2$s de %3$s. Veuillez la mettre à jour pour éviter les problèmes de compatibilité."

#: admin/class-admin-init.php:386
#: src/services/health-check/default-tagline-runner.php:31
msgid "Just another WordPress site"
msgstr "Un site utilisant WordPress"

#: admin/ajax.php:177
msgid "You have used HTML in your value which is not allowed."
msgstr "Votre valeur inclut du code HTML non autorisé."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:168
msgid "You can't edit %s that aren't yours."
msgstr "Vous ne pouvez pas modifier les %s qui ne sont pas à/de vous."

#. translators: %s expands to post type name.
#: admin/ajax.php:156
msgid "You can't edit %s."
msgstr "Vous ne pouvez pas modifier %s."

#. translators: %s expands to post type.
#: admin/ajax.php:144
msgid "Post has an invalid Content Type: %s."
msgstr "L’article a un type de publication non valide : %s."

#: admin/ajax.php:133
msgid "Post doesn't exist."
msgstr "L’article n’existe pas."