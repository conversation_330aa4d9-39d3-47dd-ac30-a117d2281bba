{"translation-revision-date": "2024-11-05 12:17:31+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["L'editor non visualizza il valore reale del conteggio, ma un segnaposto per indicare come apparirà sul front-end."], "Only if cart has items": ["Solo se il carrello contiene articoli"], "Always (even if empty)": ["Sempre (anche se vuoto)"], "Show Cart Item Count:": ["Mostra conteggio articoli nel carrello:"], "Product Count": ["Numero di prodotti"], "Cart Icon": ["Icona del carrello"], "Icon": ["Icona"], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["Attiva/disattiva per aprire cassetto Mini carrello quando un acquirente aggiunge un prodotto al suo carrello."], "Open drawer when adding": ["Apri il cassetto all'aggiunta"], "Behavior": ["Comportamento"], "Edit Mini-Cart Drawer template": ["Modifica il template del cassetto Mini carrello"], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["<PERSON>uando aperto, il cassetto del Mini carrello offre all'acquirente la possibilità di accede rapidamente per vedere i prodotti selezionati e il pagamento."], "Cart Drawer": ["Cassetto del carrello"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["Attiva/disattiva per mostrare il prezzo totale dei prodotti nel carrello dello shopping. Se non sono stati aggiunti prodotti, il prezzo non verrà visualizzato."], "Display total price": ["Mostra prezzo totale"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["Seleziona come si comporta il Mini carrello nelle pagine Carrello e Pagamento. Questa azione potrebbe influire sul layout dell'intestazione."], "Mini-Cart in cart and checkout pages": ["Mini carrello nelle pagine Carrello e Pagamento"], "Hide": ["Nascondi"], "Display": ["Visualizzazione"], "Never": ["<PERSON>"], "Price": ["Prezzo"], "Remove": ["Rimuovere"], "Settings": ["Impostazioni"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}