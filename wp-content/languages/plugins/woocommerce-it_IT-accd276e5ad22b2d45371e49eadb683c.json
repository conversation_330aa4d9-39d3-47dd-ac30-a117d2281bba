{"translation-revision-date": "2024-11-05 12:17:31+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "Enable customers to filter the product collection by selecting one or more %s attributes.": ["Consenti ai clienti di filtrare la raccolta di prodotti selezionando uno o più %s attributi."], "Rating (Experimental)": ["Valutazione (sperimentale)"], "Attribute (Experimental)": ["Attributo (sperimentale)"], "Status (Experimental)": ["Stato (sperimentale)"], "Price (Experimental)": ["Prezzo (sperimentale)"], "Active (Experimental)": ["Attivo (sperimentale)"], "Enable customers to filter the product collection by rating.": ["Consenti ai clienti di filtrare la raccolta di prodotti per valutazione."], "Enable customers to filter the product collection by selecting one or more attributes, such as color.": ["Consenti ai clienti di filtrare la raccolta di prodotti selezionando uno o più attributi, come il colore."], "Enable customers to filter the product collection by stock status.": ["Consenti ai clienti di filtrare la raccolta di prodotti per stato del magazzino."], "Enable customers to filter the product collection by choosing a price range.": ["Consenti ai clienti di filtrare la raccolta di prodotti scegliendo un intervallo di prezzo."], "When added to a post or page, Collection Filters block needs to be nested inside a Product Collection block to function properly.": ["Se aggiunto a un articolo o a una pagina, il blocco Filtri della raccolta deve essere raggruppato in un blocco Raccolta di prodotti per funzionare correttamente."], "The widget area containing Collection Filters block needs to be placed on a product archive page for filters to function properly.": ["L'area widget contenente il blocco Filtri della raccolta deve essere posizionata in una pagina dell'archivio del prodotto per funzionare correttamente."], "Display the currently active filters.": ["Visualizza i filtri attualmente attivi."], "Attribute": ["Attributo"], "Active filters": ["<PERSON><PERSON><PERSON> attivi"], "Header": ["Intestazione"], "Status": ["Stato"], "Price": ["Prezzo"], "Rating": ["Valutazione"]}}, "comment": {"reference": "assets/client/blocks/product-filter.js"}}