# Translation of Plugins - Yoast SEO - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Yoast SEO - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-10-25 16:19:39+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0-alpha.3\n"
"Language: it\n"
"Project-Id-Version: Plugins - Yoast SEO - Stable (latest release)\n"

#: admin/views/tabs/dashboard/features.php:94
msgid "Please update your Yoast SEO Premium plugin to the latest version to be able to use this feature."
msgstr "Aggiorna il plugin Yoast SEO Premium all'ultima versione per poter utilizzare questa funzione."

#: src/deprecated/admin/metabox/class-metabox-section-inclusive-language.php:49
msgid "Inclusive language"
msgstr "Linguaggio inclusivo"

#: admin/views/tabs/dashboard/features.php:84
msgid "This feature has been disabled, since it is not supported for your language yet."
msgstr "Questa funzionalità è stata disabilitata, in quanto non ancora supportata per la tua lingua."

#: admin/views/class-yoast-feature-toggles.php:98
msgid "Discover why inclusive language is important for SEO."
msgstr "Scopri perché il linguaggio inclusivo è importante per la SEO."

#: admin/views/class-yoast-feature-toggles.php:91 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Inclusive language analysis"
msgstr "Analisi del linguaggio inclusivo"

#: admin/views/class-yoast-feature-toggles.php:97 js/dist/new-settings.js:213
msgid "The inclusive language analysis offers suggestions to write more inclusive copy."
msgstr "L'analisi del linguaggio inclusivo offre suggerimenti per scrivere un contenuto più inclusivo. "

#. translators: 1: expands to a link opening tag; 2: expands to a link closing
#. tag
#: inc/language-utils.php:81
msgid "A company name and logo need to be set for structured data to work properly. Since you haven’t set these yet, we are using the site name and logo as default values. %1$sLearn more about the importance of structured data.%2$s"
msgstr "È necessario impostare il nome e il logo dell'azienda affinché i dati strutturati funzionino correttamente. Poiché non li hai ancora impostati, stiamo utilizzando il nome e il logo del sito come valori predefiniti. %1$sUlteriori informazioni sull'importanza dei dati strutturati.%2$s"

#: admin/class-yoast-form.php:912 js/dist/block-editor.js:468
#: js/dist/indexables-page.js:35
msgid "Unlock with Premium!"
msgstr "Sblocca con la versione Premium!"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/integrations.php:27
msgid "Looking for your integrations settings? We've moved them to a %1$sseparate Integrations page%2$s."
msgstr "Cerchi le impostazioni di integrazione? Le abbiamo spostate in una %1$spagina Integrazioni separata%2$s."

#. translators: %s expands to the post type name.
#. Translators: %s translates to the Post type in singular form
#: admin/metabox/class-metabox.php:209 js/dist/block-editor.js:402
#: js/dist/classic-editor.js:398 js/dist/elementor.js:402
msgid "Timestamp this %s"
msgstr "Aggiungi un timestamp a %s"

#: src/integrations/admin/crawl-settings-integration.php:249
msgid "Please use a comma to separate multiple URL parameters."
msgstr "Usa una virgola per separare i parametri URL multipli."

#: src/integrations/admin/crawl-settings-integration.php:242
#: js/dist/new-settings.js:127 js/dist/new-settings.js:224
msgid "Additional URL parameters to allow"
msgstr "Parametri URL aggiuntivi da consentire"

#: src/integrations/admin/crawl-settings-integration.php:236
msgid "Remove unwanted URL parameters from your URLs."
msgstr "Rimuovi i parametri URL indesiderati dagli URL."

#: src/integrations/admin/crawl-settings-integration.php:236
msgid "Permalink cleanup settings"
msgstr "Impostazioni di pulizia dei permalink"

#: src/integrations/admin/crawl-settings-integration.php:223
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Max number of characters to allow in searches"
msgstr "Numero massimo di caratteri da consentire nelle ricerche"

#: src/integrations/admin/crawl-settings-integration.php:217
msgid "Clean up and filter searches to prevent search spam."
msgstr "Pulisci e filtra le ricerche per evitare lo spam."

#: src/integrations/admin/crawl-settings-integration.php:217
msgid "Search cleanup settings"
msgstr "Impostazioni di pulizia della ricerca"

#: src/integrations/admin/crawl-settings-integration.php:143
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Filter searches with common spam patterns"
msgstr "Filtra le ricerche con modelli di spam comuni"

#: src/integrations/admin/crawl-settings-integration.php:142
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Filter searches with emojis and other special characters"
msgstr "Filtra le ricerche con emoji e altri caratteri speciali"

#: src/integrations/admin/crawl-settings-integration.php:141
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Filter search terms"
msgstr "Filtra i termini di ricerca"

#: src/integrations/admin/crawl-settings-integration.php:137
#: js/dist/new-settings.js:224
msgid "Unregistered URL parameters"
msgstr "Parametri URL non registrati"

#: src/integrations/admin/crawl-settings-integration.php:136
msgid "Campaign tracking URL parameters"
msgstr "Parametri URL di monitoraggio della campagna"

#: vendor_prefixed/wordproof/wordpress-sdk/app/Controllers/PostEditorTimestampController.php:133
msgid "WordProof Timestamp"
msgstr "WordProof Timestamp"

#: vendor_prefixed/wordproof/wordpress-sdk/app/Config/OptionsConfig.php:14
msgid "View this content's Timestamp certificate"
msgstr "Visualizza il certificato di Timestamp di questo contenuto"

#: admin/views/class-yoast-feature-toggles.php:200 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "IndexNow"
msgstr "IndexNow"

#: admin/views/class-yoast-feature-toggles.php:204
msgid "Find out how IndexNow can help your site."
msgstr "Scopri come IndexNow può aiutare il tuo sito."

#: src/config/wordproof-translations.php:88
msgid "Contact WordProof support"
msgstr "Contatta il supporto di WordProof"

#. translators: %s expands to WordProof.
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:53
msgid "Contact %s support."
msgstr "Contatta il supporto di %s"

#: admin/views/class-yoast-feature-toggles.php:203 js/dist/new-settings.js:217
msgid "Automatically ping search engines like Bing and Yandex whenever you publish, update or delete a post."
msgstr "Esegui automaticamente il ping ai motori di ricerca come Bing e Yandex ogni volta che pubblichi, aggiorni o elimini un articolo."

#. translators: 1: Yoast SEO Premium
#: src/integrations/admin/deactivated-premium-integration.php:99
msgid "Activate %1$s!"
msgstr "Attiva %1$s!"

#. translators: 1: Yoast SEO Premium 2: Link start tag to activate premium, 3:
#. Link closing tag.
#: src/integrations/admin/deactivated-premium-integration.php:86
msgid "You've installed %1$s but it's not activated yet. %2$sActivate %1$s now!%3$s"
msgstr "Hai installato %1$s ma non è ancora attivato. %2$sAttiva %1$s ora!%3$s"

#: src/integrations/admin/crawl-settings-integration.php:113
#: js/dist/new-settings.js:224
msgid "Global feed"
msgstr "Feed globale"

#: src/integrations/admin/crawl-settings-integration.php:114
#: js/dist/new-settings.js:224
msgid "Global comment feeds"
msgstr "Feed dei commenti globali"

#: src/integrations/admin/crawl-settings-integration.php:116
#: js/dist/new-settings.js:224
msgid "Post authors feeds"
msgstr "Feed autori degli articoli"

#: src/integrations/admin/crawl-settings-integration.php:117
#: js/dist/new-settings.js:224
msgid "Post type feeds"
msgstr "Feed dei post type"

#: src/integrations/admin/crawl-settings-integration.php:118
#: js/dist/new-settings.js:224
msgid "Category feeds"
msgstr "Feed di categoria"

#: src/integrations/admin/crawl-settings-integration.php:119
#: js/dist/new-settings.js:224
msgid "Tag feeds"
msgstr "Feed dei tag"

#: src/integrations/admin/crawl-settings-integration.php:120
#: js/dist/new-settings.js:224
msgid "Custom taxonomy feeds"
msgstr "Feed di tassonomia personalizzati"

#: src/integrations/admin/crawl-settings-integration.php:121
#: js/dist/new-settings.js:224
msgid "Search results feeds"
msgstr "Feed dei risultati di ricerca"

#: src/integrations/admin/crawl-settings-integration.php:122
#: js/dist/new-settings.js:224
msgid "Atom/RDF feeds"
msgstr "Feed Atom/RDF"

#: src/integrations/admin/crawl-settings-integration.php:126
#: js/dist/new-settings.js:224
msgid "Shortlinks"
msgstr "Shortlink"

#: src/integrations/admin/crawl-settings-integration.php:127
msgid "REST API links"
msgstr "Link REST API"

#: src/integrations/admin/crawl-settings-integration.php:128
#: js/dist/new-settings.js:224
msgid "RSD / WLW links"
msgstr "Link RSD / WLW"

#: src/integrations/admin/crawl-settings-integration.php:129
#: js/dist/new-settings.js:224
msgid "oEmbed links"
msgstr "Link oEmbed"

#: src/integrations/admin/crawl-settings-integration.php:130
#: js/dist/new-settings.js:224
msgid "Generator tag"
msgstr "Tag generatore"

#: src/integrations/admin/crawl-settings-integration.php:149
#: js/dist/new-settings.js:224
msgid "Emoji scripts"
msgstr "Script emoji"

#: src/integrations/admin/crawl-settings-integration.php:131
#: js/dist/new-settings.js:126 js/dist/new-settings.js:224
msgid "Pingback HTTP header"
msgstr "Pingback degli header HTTP"

#: src/integrations/admin/crawl-settings-integration.php:132
#: js/dist/new-settings.js:224
msgid "Powered by HTTP header"
msgstr "Creato da header HTTP"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Basic crawl settings"
msgstr "Impostazioni base di crawl"

#: src/integrations/admin/crawl-settings-integration.php:206
msgid "Remove links added by WordPress to the header and &lt;head&gt;."
msgstr "Rimuovi i link aggiunti da WordPress a header e &lt;head&gt;."

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Feed crawl settings"
msgstr "Impostazioni di crawl del feed"

#: src/integrations/admin/crawl-settings-integration.php:207
msgid "Remove feed links added by WordPress that aren't needed for this site."
msgstr "Rimuovi i link al feed aggiunti da WordPress che non sono necessari per questo sito."

#: src/integrations/admin/crawl-settings-integration.php:313
msgid "By removing Global comments feed, Post comments feeds will be removed too."
msgstr "Se rimuovi il feed dei commenti globali, verranno rimossi anche i feed dei commenti agli articoli."

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "SEO configuration"
msgstr "Configurazione SEO"

#. translators: 1: Link start tag to the first-time configuration, 2: Link
#. closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:151
msgid "We noticed that you haven't fully configured Yoast SEO yet. Optimize your SEO settings even further by using our improved %1$s First-time configuration%2$s."
msgstr "Abbiamo notato che non hai ancora completato la configurazione di Yoast SEO. Ottimizza tutte le tue impostazioni SEO con la nostra procedura di %1$sprima configurazione%2$s."

#: admin/views/redirects.php:188
msgid "No items found."
msgstr "Nessun elemento trovato"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/crawl-settings.php:22
msgid "To make the crawling of your site more efficient and environmental friendly, %1$s allows you to remove URLs (added by WordPress) that might not be needed for your site."
msgstr "Per rendere il crawling del tuo sito più efficiente e rispettoso dell'ambiente, %1$s ti permette di rimuovere gli URL (aggiunti da WordPress) che potrebbero non essere necessari per il tuo sito."

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/dashboard/crawl-settings.php:29
msgid "%1$sLearn more about crawl settings and how they could benefit your site.%2$s"
msgstr "%1$sMaggiori informazioni sulle impostazioni di crawl e sui vantaggi che danno al tuo sito.%2$s"

#. translators: %1$s opens the link to the Yoast.com article about Crawl
#. settings, %2$s closes the link,
#: admin/views/tabs/network/crawl-settings.php:31
msgid "%1$sLearn more about crawl settings.%2$s"
msgstr "%1$sMaggiori informazioni sulle impostazioni di crawl.%2$s"

#: src/integrations/admin/crawl-settings-integration.php:115
#: js/dist/new-settings.js:224
msgid "Post comments feeds"
msgstr "Feed dei commenti agli articoli"

#: admin/pages/network.php:28 admin/views/tabs/dashboard/crawl-settings.php:17
#: admin/views/tabs/network/crawl-settings.php:19
#: src/integrations/admin/crawl-settings-integration.php:165
msgid "Crawl settings"
msgstr "Impostazioni di crawl"

#: admin/views/redirects.php:37
msgid "Regex Redirects"
msgstr "Regex redirect"

#: admin/views/redirects.php:50
msgid "Plain redirects"
msgstr "Reindirizza"

#: admin/views/redirects.php:59 admin/views/redirects.php:155
#: admin/views/redirects.php:203
msgid "Type"
msgstr "Tipo"

#: admin/views/redirects.php:66 admin/views/redirects.php:75
msgid "301 Moved Permanently"
msgstr "301 Spostato Permanentemente"

#. translators: 1: opens a link. 2: closes the link.
#: admin/views/redirects.php:91
msgid "The redirect type is the HTTP response code sent to the browser telling the browser what type of redirect is served. %1$sLearn more about redirect types%2$s."
msgstr "Il tipo di redirect è il codice di risposta HTTP inviato al browser, che dice al browser  quale tipo di redirect viene fornito. %1$sImpara di più sui tipi di redirect%2$s."

#: admin/views/redirects.php:100 admin/views/redirects.php:165
#: admin/views/redirects.php:212
msgid "Old URL"
msgstr "Vecchia URL"

#: admin/views/redirects.php:122
msgid "Add Redirect"
msgstr "Aggiungi redirect"

#: admin/views/redirects.php:137
msgid "All redirect types"
msgstr "Tutti i tipi di redirezione"

#: src/integrations/admin/first-time-configuration-integration.php:125
msgid "First-time configuration"
msgstr "Configurazione iniziale"

#. translators: 1: Link start tag to the First time configuration tab in the
#. General page, 2: Link closing tag.
#: admin/views/tabs/tool/import-seo.php:99
msgid "You should finish the %1$sfirst time configuration%2$s to make sure your SEO data has been optimized and you’ve set the essential Yoast SEO settings for your site."
msgstr "Dovresti completare la %1$s configurazione iniziale%2$s per assicurarti che i tuoi dati SEO siano stati ottimizzati e che tu abbia salvato le impostazioni essenziali di Yoast SEO per il tuo sito."

#: admin/views/tabs/tool/import-seo.php:94
msgid "Step 4: Go through the first time configuration"
msgstr "Passaggio 4: imposta la configurazione iniziale"

#: src/services/health-check/page-comments-check.php:48
msgid "Page comments"
msgstr "Commenti alla pagina"

#: src/services/health-check/postname-permalink-check.php:48
msgid "Postname permalink"
msgstr "Permalink dell'articolo"

#: src/services/health-check/links-table-check.php:48
msgid "Links table"
msgstr "Tabella dei link"

#: src/exceptions/importing/aioseo-validation-exception.php:17
msgid "The validation of the AIOSEO data structure has failed."
msgstr "La convalida della struttura dati AIOSEO non è andata a buon fine."

#: src/integrations/admin/import-integration.php:212
msgid "The AIOSEO import was cancelled because some AIOSEO data is missing. Please try and take the following steps to fix this:"
msgstr "L'importazione dei dati AIOSEO è stata annullata perché mancano alcuni dati. Prova a seguire i prossimi passaggi per risolvere il problema:"

#: src/integrations/admin/import-integration.php:215
msgid "If you have never saved any AIOSEO 'Search Appearance' settings, please do that first and run the import again."
msgstr "Se non hai mai salvato nessuna impostazione per l'\"Aspetto di ricerca\" AIOSEO, fai prima quello e prova a ripetere l'importazione."

#: src/integrations/admin/import-integration.php:218
msgid "If you already have saved AIOSEO 'Search Appearance' settings and the issue persists, please contact our support team so we can take a closer look."
msgstr "Se hai già salvato le impostazioni per l'\"Aspetto di ricerca\" AIOSEO e il problema persiste, contatta il nostro team di assistenza così possiamo dare un'occhiata."

#. translators: %s expands to Wincher
#. translators: %s expands to WordProof
#: src/integrations/third-party/wincher.php:125
#: src/integrations/third-party/wordproof-integration-toggle.php:130
#: src/integrations/third-party/wordproof-integration-toggle.php:152
msgid "Currently, the %s integration is not available for multisites."
msgstr "Al momento l'integrazione %s non è disponibile per i multisiti."

#. translators: %s expands to WordProof.
#: src/config/wordproof-translations.php:21
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:11
msgid "You are out of timestamps. Please upgrade your account by opening the %s settings."
msgstr "Non ci sono più timestamp. Aggiorna il tuo account aprendo le impostazioni %s."

#: admin/metabox/class-metabox.php:210
msgid "Use WordProof to timestamp this page to comply with legal regulations and join the fight for a more transparant and accountable internet."
msgstr "Usa WordProof per inserire un timestamp in questa pagina in modo da rispettare le norme legali e unirti alla missione per un Internet più trasparente e responsabile."

#: vendor_prefixed/wordproof/wordpress-sdk/app/Controllers/PostEditorTimestampController.php:142
msgid "Timestamp this post"
msgstr "Aggiungi un timestamp a questo articolo"

#: src/integrations/third-party/wordproof-integration-toggle.php:136
msgid "The WordProof Timestamp plugin needs to be disabled before you can activate this integration."
msgstr "Il plugin WordProof Timestamp deve essere disattivato prima di poter attivare questa integrazione."

#. translators: %s expands to WordProof.
#: src/config/wordproof-translations.php:51
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:34
msgid "The timestamp is not retrieved by your site. Please try again or contact %1$s support."
msgstr "Il timestamp non viene recuperato dal sito. Riprova o contatta l'assistenza %1$s"

#. translators: %s expands to WordProof.
#: src/config/wordproof-translations.php:61
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:39
msgid "The timestamp is not created because you need to authenticate with %s first."
msgstr "Il timestamp non viene creato perché è necessario autenticarti prima con %s."

#: src/config/wordproof-translations.php:79
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:47
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-block-editor.js:1
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-classic-editor.js:1
msgid "Open settings"
msgstr "Impostazioni di apertura"

#: src/config/wordproof-translations.php:70
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-block-editor.js:1
#: vendor_prefixed/wordproof/wordpress-sdk/build/wordproof-classic-editor.js:1
msgid "Open authentication"
msgstr "Autenticazione aperta"

#. translators: %s expands to WordProof.
#: src/config/wordproof-translations.php:31
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:27
msgid "%s has successfully timestamped this page."
msgstr "%s ha eseguito il timestamp di questa pagina."

#. translators: %s expands to WordProof.
#: src/config/wordproof-translations.php:41
#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:19
msgid "%1$s failed to timestamp this page. Please check if you're correctly authenticated with %1$s and try to save this page again."
msgstr "%1$s non è riuscito a salvare la pagina con il timestamp. Verifica la corretta autenticazione con %1$s e riprova a salvare la pagina."

#. translators: %s expands to WordProof
#: src/integrations/third-party/wordproof-integration-toggle.php:86
msgid "%1$s can be used to timestamp your privacy page."
msgstr "%1$s può essere usato per datare la pagina della privacy."

#: src/integrations/admin/import-integration.php:105
msgid "Clean up"
msgstr "Cancella"

#. translators: %s: expands to the name of the plugin that is selected to be
#. imported
#: src/integrations/admin/import-integration.php:109
msgid "The import from %s includes:"
msgstr "L'importazione da %s include:"

#: src/integrations/admin/import-integration.php:95
msgid "The cleanup can take a long time depending on your site's size."
msgstr "A seconda della dimensione del tuo sito, la pulizia può richiedere un po' di tempo."

#: src/integrations/admin/import-integration.php:113
#: src/integrations/admin/import-integration.php:123
msgid "Post metadata (SEO titles, descriptions, etc.)"
msgstr "Metadati dell'articolo (titolo SEO, descrizione, etc.)"

#: src/integrations/admin/import-integration.php:114
#: src/integrations/admin/import-integration.php:124
msgid "Note: This metadata will only be imported if there is no existing Yoast SEO metadata yet."
msgstr "Nota: Questi metadati verranno importati solo se non esistono già metadati di Yoast SEO."

#: src/integrations/admin/import-integration.php:96
msgid "Note: "
msgstr "Nota:"

#: src/integrations/admin/import-integration.php:97
msgid "After you've imported data from another SEO plugin, please make sure to clean up all the original data from that plugin. (step 5)"
msgstr "Dopo aver importato i dati da un altro plugin SEO, assicurati di aver rimosso i dati utilizzati originariamente da quel plugin. "

#: src/integrations/admin/import-integration.php:106
msgid "Please select an SEO plugin below to see what data can be imported."
msgstr "Seleziona un plugin SEO per vedere quali dati possono essere importati."

#: src/integrations/admin/import-integration.php:107
msgid "Once you're certain that your site is working properly with the imported data from another SEO plugin, you can clean up all the original data from that plugin."
msgstr "Quando sei sicuro che il tuo sito funzioni bene con i dati importati da un altro plugin SEO, puoi rimuovere tutti i dati usati originariamente da quel plugin."

#: src/integrations/admin/import-integration.php:118
msgid "Note: These settings will overwrite the default settings of Yoast SEO."
msgstr "Nota: queste impostazioni rimpiazzeranno le impostazioni predefinite di Yoast SEO."

#: src/integrations/admin/import-integration.php:234
msgid "Cleanup failed with the following error:"
msgstr "Procedura di cancellazione fallita con il seguente errore:"

#: src/integrations/admin/import-integration.php:236
msgid "Import failed with the following error:"
msgstr "L'importazione è fallita con il seguente errore:"

#: src/integrations/admin/import-integration.php:99
msgid "No data found from other SEO plugins."
msgstr "Non è stato trovato alcun dato dagli altri plugin SEO."

#: src/integrations/admin/import-integration.php:98
msgid "Select SEO plugin"
msgstr "Seleziona il plugin SEO"

#: src/services/health-check/default-tagline-check.php:48
msgid "Default tagline"
msgstr "Descrizione predefinita"

#: src/integrations/admin/import-integration.php:94
msgid "The import can take a long time depending on your site's size."
msgstr "L'importazione può richiedere molto tempo a seconda delle dimensioni del tuo sito"

#: src/integrations/admin/installation-success-integration.php:104
msgid "Installation Successful"
msgstr "Installazione riuscita"

#: src/config/schema-types.php:131
msgid "Blog Post"
msgstr "Articolo del blog"

#. translators: %s: 'Wincher'
#: src/integrations/third-party/wincher.php:77
msgid "The %s integration offers the option to track specific keyphrases and gain insights in their positions."
msgstr "L'integrazione %s offre la possibilità di tracciare frasi chiave specifiche e ottenere approfondimenti sulle loro posizioni."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:296
msgid "Renew %s"
msgstr "Rinnova %s"

#: src/integrations/admin/workouts-integration.php:238
msgid "Get help activating your subscription"
msgstr "Ottieni assistenza per attivare il tuo abbonamento"

#. translators: %s: expands to 'Yoast SEO Premium'.
#. translators: 1: Yoast WooCommerce SEO
#: src/integrations/admin/workouts-integration.php:309
#: js/dist/integrations-page.js:3
msgid "Activate %s"
msgstr "Attiva %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:303
msgid "Update %s"
msgstr "Aggiorna %s"

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:232
msgid "It looks like you’re running an outdated and unactivated version of %1$s, please activate your subscription in %2$sMyYoast%3$s and update to the latest version (at least 17.7) to gain access to our updated workouts section."
msgstr "Sembra che tu stia utilizzando una versione obsoleta e non attiva di %1$s, per favore attiva il tuo abbonamento in %2$sMyYoast%3$s e aggiorna all'ultima versione (almeno alla 17.7)  per accedere alla nostra sessione aggiornata di allenamenti."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:228
msgid "Activate your subscription of %s"
msgstr "Attiva il tuo abbonamento a %s"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:216
msgid "Update to the latest version of %s"
msgstr "Aggiorna all'ultima versione di %s"

#: src/integrations/admin/workouts-integration.php:209
msgid "Renew your subscription"
msgstr "Rinnova il tuo abbonamento"

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:202
msgid "Accessing the latest workouts requires an updated version of %s (at least 17.7), but it looks like your subscription has expired. Please renew your subscription to update and gain access to all the latest features."
msgstr "L'accesso agli ultimi allenamenti richiede una versione aggiornata di %s (almeno 17.7), ma sembra che il tuo abbonamento sia scaduto. Si prega di rinnovare l'abbonamento per aggiornare e accedere a tutte le ultime funzionalità."

#. translators: 1: expands to 'Yoast SEO Premium', 2: Link start tag to the
#. page to update Premium, 3: Link closing tag.
#: src/integrations/admin/workouts-integration.php:219
msgid "It looks like you're running an outdated version of %1$s, please %2$supdate to the latest version (at least 17.7)%3$s to gain access to our updated workouts section."
msgstr "Sembra che tu stia eseguendo una versione obsoleta di %1$s, per favore %2$s aggiorna all'ultima versione (almeno 17.7)%3$s per accedere alla nostra sezione allenamenti aggiornata."

#. translators: %s: expands to 'Yoast SEO Premium'.
#: src/integrations/admin/workouts-integration.php:199
msgid "Renew your subscription of %s"
msgstr "Rinnova il tuo abbonamento a %s"

#. translators: 1: Link start tag to the first-time configuration, 2: Yoast
#. SEO, 3: Link closing tag.
#: src/integrations/admin/first-time-configuration-notice-integration.php:142
msgid "Get started quickly with the %1$s%2$s First-time configuration%3$s and configure Yoast SEO with the optimal SEO settings for your site!"
msgstr "Fai subito i primi passi con la %1$s%2$s configurazione iniziale %3$s e configura Yoast SEO con le impostazioni SEO ottimali per il tuo sito!"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Category Title"
msgstr "Titolo della categoria"

#: inc/class-wpseo-replace-vars.php:1495
msgid "Replaced with the post content"
msgstr "Sostituito con il contenuto dell'articolo"

#: inc/class-wpseo-replace-vars.php:1496
msgid "Current or first category title"
msgstr "Titolo della prima categoria o della categoria attuale "

#: inc/class-wpseo-replace-vars.php:1495
msgid "Post Content"
msgstr "Contenuto dell'articolo"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Replaced with the permalink"
msgstr "Sostituito dal permalink"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Author last name"
msgstr "Cognome dell'autore"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Author first name"
msgstr "Nome dell'autore"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Post year"
msgstr "Anno di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Post month"
msgstr "Mese di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Post day"
msgstr "Giorno di pubblicazione"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Current date"
msgstr "Data corrente"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Current month"
msgstr "Mese corrente"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Current day"
msgstr "Giorno corrente"

#: inc/class-wpseo-replace-vars.php:1492
msgid "Replaced with the first name of the author"
msgstr "Sostituito dal nome dell'autore"

#: inc/class-wpseo-replace-vars.php:1491
msgid "Replaced with the day the post was published"
msgstr "Sostituito dal giorno in cui l'articolo è stato pubblicato"

#: inc/class-wpseo-replace-vars.php:1490
msgid "Replaced with the month the post was published"
msgstr "Sostituito dal mese in cui l'articolo è stato pubblicato"

#: inc/class-wpseo-replace-vars.php:1493
msgid "Replaced with the last name of the author"
msgstr "Sostituito dal cognome dell'autore"

#: inc/class-wpseo-replace-vars.php:1489
msgid "Replaced with the year the post was published"
msgstr "Sostituito dall'anno in cui l'articolo è stato pubblicato"

#. translators: %1$s expands to an opening strong tag, %2$s expands to the
#. dependency name, %3$s expands to a closing strong tag, %4$s expands to an
#. opening anchor tag, %5$s expands to a closing anchor tag.
#: admin/class-suggested-plugins.php:110
msgid "It looks like you aren't using our %1$s%2$s addon%3$s. %4$sUpgrade today%5$s to unlock more tools and SEO features to make your products stand out in search results."
msgstr "Sembra che tu non stia usando il nostro %1$sadd-on %2$s%3$s. %4$sAggiorna subito%5$s per sbloccare nuovi strumenti e funzionalità SEO che faranno risaltare i tuoi prodotti nei risultati di ricerca."

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:102
msgid "Below are the technical details for the error. See %1$sthis page%2$s for a more detailed explanation."
msgstr "Di seguito sono riportati i dettagli tecnici dell'errore. Dai un'occhiata a %1$squesta pagina%2$s per una spiegazione più dettagliata."

#. translators: %1$s is the plugin name, %2$s and %3$s are a link.
#: inc/class-addon-manager.php:370
msgid "A new version of %1$s is available. %2$sRenew your subscription%3$s if you want to update to the latest version."
msgstr "È disponibile una nuova versione di %1$s. %2$sRinnova il tuo abbonamento%3$s se vuoi aggiornare all'ultima versione."

#: src/integrations/admin/workouts-integration.php:93
msgid "Workouts"
msgstr "Esercizi"

#: src/schema-templates/cooking-time.block.php:12
msgid "Recipe cooking time"
msgstr "Il tempo di cottura della ricetta"

#: src/schema-templates/cooking-time.block.php:12
msgid "The time it takes to actually cook the dish."
msgstr "Il tempo necessario per cucinare il piatto."

#: src/schema-templates/preparation-time.block.php:12
msgid "Recipe prep time"
msgstr "Tempo di preparazione della ricetta"

#: src/schema-templates/preparation-time.block.php:12
msgid "The time it takes to prepare the items to be used in the instructions."
msgstr "Il tempo necessario per preparare gli elementi da usare nelle istruzioni."

#: src/schema-templates/preparation-time.block.php:14
msgid "Prep time"
msgstr "Tempo di preparazione"

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients"
msgstr "Ingredienti"

#: admin/views/class-yoast-integration-toggles.php:99
msgid "Improve the quality of your site search! Automatically helps your users find your cornerstone and most important content in your internal search results. It also removes noindexed posts & pages from your site’s search results."
msgstr "Migliora la qualità della ricerca all'interno del tuo sito! Aiuta automaticamente i tuoi utenti a trovare i contenuti Cornerstone e quelli più importanti nei risultati di ricerca interni. Rimuove anche gli articoli e le pagine non indicizzate dai risultati di ricerca del tuo sito."

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "Recipe ingredient(s)"
msgstr "Ingredienti della ricetta"

#: src/schema-templates/recipe-ingredients.block.php:12
msgid "The ingredients used in the recipe, e.g. sugar, flour or garlic."
msgstr "Gli ingredienti usati nella ricetta, per esempio zucchero, farina o aglio."

#: src/schema-templates/recipe-ingredients.block.php:14
msgid "Ingredients title"
msgstr "Titolo degli ingredienti"

#: src/schema-templates/recipe-ingredients.block.php:15
msgid "Enter an ingredient"
msgstr "Aggiungi un ingrediente"

#: src/schema-templates/recipe-instructions.block.php:13
msgid "Recipe instructions"
msgstr "Istruzioni per la ricetta"

#: src/schema-templates/recipe-instructions.block.php:13
msgid "The steps of making the recipe, in the form of an ordered list with HowToStep and/or HowToSection items."
msgstr "I passi per realizzare la ricetta sotto forma di una lista ordinata di voci HowToStep e/o HowToSection (passi e sezioni da seguire)."

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions title"
msgstr "Titolo delle istruzioni"

#: src/schema-templates/recipe-instructions.block.php:15
msgid "Instructions"
msgstr "Istruzioni"

#: src/schema-templates/recipe-instructions.block.php:16
msgid "Enter step"
msgstr "Inserisci un passo"

#. translators: 1: Yoast SEO, 2: Zapier.
#: admin/views/class-yoast-integration-toggles.php:83
msgid "Set up automated actions when you publish or update your content. By connecting %1$s with %2$s, you can easily send out your published posts to any of its 2000+ destinations, such as Twitter, Facebook and more."
msgstr "Imposta azioni automatiche quando pubblichi o aggiorni i tuoi contenuti. Collegando %1$s con %2$s, puoi inviare con facilità i tuoi articoli pubblicati a qualsiasi delle 2000 (e più!) destinazioni, come Twitter, Facebook e altro."

#. translators: %s: Zapier.
#. translators: %s: Algolia.
#: admin/views/class-yoast-integration-toggles.php:88
#: admin/views/class-yoast-integration-toggles.php:101
msgid "Find out more about our %s integration."
msgstr "Scopri di più sulla nostra integrazione %s."

#: admin/views/class-yoast-feature-toggles.php:134
msgid "Read more about how internal linking can improve your site structure."
msgstr "Approfondisci come i link interni possono migliorare la struttura del tuo sito. "

#: admin/views/class-yoast-feature-toggles.php:130 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Link suggestions"
msgstr "Link suggeriti"

#: admin/views/class-yoast-feature-toggles.php:123
msgid "Find relevant data about your content right in the Insights section in the Yoast SEO metabox. You’ll see what words you use most often and if they’re a match with your keywords! "
msgstr "Trova dati rilevanti per il tuo contenuto nella sezione Approfondimenti (Insights) della metabox di Yoast SEO. Potrai vedere quali parole usi più spesso e se corrispondono alle tue parole chiave! "

#: admin/views/class-yoast-feature-toggles.php:124
msgid "Find out how Insights can help you improve your content."
msgstr "Scopri come gli Approfondimenti (Insights) ti aiutano a migliorare il tuo contenuto."

#: admin/views/class-yoast-feature-toggles.php:121 js/dist/block-editor.js:447
#: js/dist/classic-editor.js:443 js/dist/elementor.js:447
#: js/dist/new-settings.js:215 js/dist/new-settings.js:224
msgid "Insights"
msgstr "Approfondimenti (Insights)"

#. translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Premium installation page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-error-presenter.php:76
#: src/presenters/admin/indexing-failed-notification-presenter.php:77
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please make sure to activate your subscription in MyYoast by completing %1$sthese steps%2$s."
msgstr "Oops, qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. Attiva il tuo abbonamento a MyYoast seguendo %1$squeste istruzioni%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:148
msgid "The social appearance settings for archives require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Le impostazioni di aspetto social degli archivi hanno bisogno dei metadati Open Graph (che al momento sono disabilitati). Puoi abilitarli nelle impostazioni %1$s‘Social’ della scheda ‘Facebook’%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:136
msgid "The social appearance settings for taxonomies require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Le impostazioni di aspetto social delle tassonomie hanno bisogno dei metadati Open Graph (che al momento sono disabilitati). Puoi abilitarli nelle impostazioni %1$s‘Social’ della scheda ‘Facebook’%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:124
msgid "The social appearance settings for content types require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Le impostazioni di aspetto social dei tipi di contenuto hanno bisogno dei metadati Open Graph (che al momento sono disabilitati). Puoi abilitarli nelle impostazioni %1$s‘Social’ della scheda ‘Facebook’%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/class-view-utils.php:108
msgid "The social appearance settings for your homepage require Open Graph metadata (which is currently disabled). You can enable this in the %1$s‘Social’ settings under the ‘Facebook’ tab%2$s."
msgstr "Le impostazioni di aspetto social della tua homepage hanno bisogno dei metadati Open Graph (che al momento sono disabilitati). Puoi abilitarli nelle impostazioni %1$s‘Social’ della scheda ‘Facebook’%2$s."

#: admin/views/tabs/social/facebook.php:48
msgid "Default image"
msgstr "Immagine predefinita"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/social/facebook.php:33
msgid "The social homepage settings have been moved to the %1$s‘Search appearance’ settings under the ‘General’ tab%2$s."
msgstr "Le impostazioni della homepage social sono state spostate nelle impostazioni dell' %1$s‘Aspetto della ricerca’ nella scheda ‘Generale’%2$s."

#: admin/views/tabs/metas/paper-content/front-page-content.php:58
#: src/integrations/admin/social-templates-integration.php:223
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social title"
msgstr "Titolo social"

#: admin/views/tabs/metas/paper-content/front-page-content.php:59
#: src/integrations/admin/social-templates-integration.php:224
#: js/dist/new-settings.js:32 js/dist/new-settings.js:45
#: js/dist/new-settings.js:71 js/dist/new-settings.js:91
#: js/dist/new-settings.js:147 js/dist/new-settings.js:161
#: js/dist/new-settings.js:168 js/dist/new-settings.js:220
#: js/dist/new-settings.js:224 js/dist/new-settings.js:228
msgid "Social description"
msgstr "Descrizione social"

#. translators: %s expands to 'Yoast SEO Premium'.
#: src/integrations/admin/social-templates-integration.php:236
msgid "To unlock this feature please update %s to the latest version."
msgstr "Per sbloccare questa funzionalità aggiorna %s all'ultima versione."

#: admin/views/redirects.php:22
#: src/integrations/admin/crawl-settings-integration.php:332
#: src/integrations/admin/social-templates-integration.php:251
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "Unlock with Premium"
msgstr "Sblocca con Premium"

#: src/schema-templates/recipe-description.block.php:12
msgid "Recipe description"
msgstr "Descrizione ricetta"

#: src/schema-templates/recipe-description.block.php:12
msgid "A description of the recipe."
msgstr "Una descrizione della ricetta."

#: src/schema-templates/recipe-description.block.php:14
msgid "Enter a recipe description"
msgstr "Inserisci una descrizione per la ricetta "

#: src/schema-templates/cooking-time.block.php:14
#: js/dist/externals/schemaBlocks.js:13
msgid "Cooking time"
msgstr "Tempo di cottura"

#. translators: %s is the plural version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:42
msgid "%s archive settings"
msgstr "Impostazioni degli archivi %s"

#. translators: %s is the singular version of the post type's name.
#: admin/views/tabs/metas/paper-content/post-type-content.php:18
msgid "Single %s settings"
msgstr "Impostazioni dell'articolo %s"

#: admin/views/tabs/metas/paper-content/front-page-content.php:31
msgid "These are the image, title and description used when a link to your homepage is shared on social media."
msgstr "Questi sono il titolo, la descrizione e l'immagine che verranno utilizzati quando viene condiviso un link alla tua homepage sui social media."

#: admin/views/tabs/metas/general.php:15
msgid "Rewrite titles"
msgstr "Riscrivere i titoli"

#: src/integrations/admin/addon-installation/installation-integration.php:97
msgid "Installing and activating addons"
msgstr "Installare e attivare gli add-on"

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:145
msgid "Addon activated."
msgstr "Add-on attivato."

#: src/integrations/admin/addon-installation/installation-integration.php:147
msgid "You are not allowed to activate plugins."
msgstr "Non hai i permessi per attivare i plugin."

#. Translators:%s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:151
msgid "Addon activation failed because of an error: %s."
msgstr "L'attivazione dell'add-on non è riuscita a causa di un errore: %s."

#. Translators: %s expands to the name of the addon.
#: src/integrations/admin/addon-installation/installation-integration.php:175
msgid "Addon installed."
msgstr "Add-on installato."

#: src/integrations/admin/addon-installation/installation-integration.php:179
msgid "You are not allowed to install plugins."
msgstr "Non hai i permessi per installare i plugin."

#. translators: %1$s expands to an anchor tag to the admin premium page, %2$s
#. expands to Yoast SEO Premium, %3$s expands to a closing anchor tag
#: src/integrations/admin/addon-installation/installation-integration.php:120
msgid "%1$s Continue to %2$s%3$s"
msgstr "%1$s Continua %2$s%3$s"

#. Translators: %s expands to the error message.
#: src/integrations/admin/addon-installation/installation-integration.php:183
msgid "Addon installation failed because of an error: %s."
msgstr "L'installazione dell'add-on non è riuscita a causa di un errore: %s."

#. translators: %1$s expands to Yoast SEO
#: src/integrations/admin/addon-installation/dialog-integration.php:92
msgid "No %1$s plugins have been installed. You don't seem to own any active subscriptions."
msgstr "Nessun %1$s plugin è stato installato. Pare che tu non abbia nessun abbonamento attivo."

#: src/schema-templates/recipe.block.php:39
msgid "Add a block to your recipe..."
msgstr "Aggiungi un blocco alla tua ricetta..."

#: src/schema-templates/recipe.block.php:36
msgid "Create a Recipe in an SEO-friendly way. You can only use one Recipe block per post."
msgstr "Crea una ricetta che sia SEO-friendly. Puoi usare un solo blocco Ricetta per articolo."

#. translators: %1$s expands to Yoast
#: src/schema-templates/recipe.block.php:9
msgid "%1$s Recipe"
msgstr "Ricetta %1$s"

#: src/schema-templates/recipe-name.block.php:10
msgid "Enter a recipe name"
msgstr "Inserisci un nome per la ricetta"

#: src/schema-templates/recipe.block.php:37
msgid "Serves #"
msgstr "Porzioni #"

#. translators: %s expands to Yoast SEO Premium.
#: admin/class-admin.php:237
msgid "Required by %s"
msgstr "È necessario affinché %s funzioni correttamente"

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:83
msgid "Auto-updates are enabled based on this setting for %1$s."
msgstr "Gli aggiornamenti automatici sono abilitati in base alle impostazioni che hai definito per %1$s."

#. Translators: %1$s resolves to Yoast SEO.
#: src/integrations/watchers/addon-update-watcher.php:93
msgid "Auto-updates are disabled based on this setting for %1$s."
msgstr "Gli aggiornamenti automatici sono disabilitati in base alle impostazioni che hai definito per %1$s."

#: src/presenters/admin/badge-presenter.php:80
#: src/presenters/admin/badge-presenter.php:87
#: js/dist/externals/componentsNew.js:136 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:13 js/dist/integrations-page.js:15
msgid "New"
msgstr "Nuovo"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:114
msgid "Enable Breadcrumbs for your theme"
msgstr "Abilita i breadcrumb per il tuo tema"

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:111
msgid "Note: You can always choose to enable / disable them for your theme below. This setting will not apply to breadcrumbs inserted through a widget, a block or a shortcode."
msgstr "Nota: puoi sempre scegliere di abilitarli / disabilitarli per il tuo tema qui sotto. Questa impostazione non si applica ai breadcrumb inseriti attraverso un widget, un blocco o uno shortcode."

#. translators: %s is the reason given by WordPress.
#: src/exceptions/indexable/invalid-term-exception.php:21
msgid "The term is considered invalid. The following reason was given by WordPress: %s"
msgstr "Il termine è considerato non valido. WordPress indica come motivo: %s"

#: src/exceptions/indexable/post-not-found-exception.php:16
msgid "The post could not be found."
msgstr "L'articolo non è stato trovato."

#: src/exceptions/indexable/term-not-found-exception.php:16
msgid "The term could not be found."
msgstr "Il termine non è stato trovato."

#: admin/class-yoast-form.php:1045 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled since subsites never send tracking data."
msgstr "Questa funzione è stata disabilitata in quanto i sottositi non inviano mai dati di tracciamento."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to an
#. closing anchor tag.
#: src/integrations/third-party/wpml-wpseo-notification.php:110
msgid "We notice that you have installed WPML. To make sure your canonical URLs are set correctly, %1$sinstall and activate the Yoast SEO Multilingual add-on%2$s as well!"
msgstr "Abbiamo rilevato che hai installato WPML. Per assicurarti che i tuoi URLs canonici siano impostati in modo corretto, %1$sinstalla e attiva anche il plugin add-on Yoast SEO Multilingual%2$swell!"

#: src/presenters/admin/indexing-notification-presenter.php:81
msgid "Because of a change in your category base setting, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nelle impostazioni dell'URL della categoria, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#: admin/views/class-yoast-feature-toggles.php:194 js/dist/new-settings.js:217
msgid "This adds an author byline and reading time estimate to the article’s snippet when shared on Slack."
msgstr "Questo aggiunge l'autore ed il tempo di lettura allo snippet dell'articolo quando viene condiviso su Slack"

#: admin/views/class-yoast-feature-toggles.php:195
msgid "Find out how a rich snippet can improve visibility and click-through-rate."
msgstr "Scopri come i rich snippet possono migliorare la visibilità del tuo sito e la tua percentuale di clic (CTR)."

#: src/presenters/slack/enhanced-data-presenter.php:50
msgid "Written by"
msgstr "Scritto da"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "Est. reading time"
msgstr "Tempo di lettura stimato"

#. translators: %s expands to the reading time number, in minutes
#: src/presenters/slack/enhanced-data-presenter.php:55
msgid "%s minute"
msgid_plural "%s minutes"
msgstr[0] "%s minuto"
msgstr[1] "%s minuti"

#. translators: 1: Expands to Yoast SEO
#: src/presenters/admin/indexing-notification-presenter.php:120
msgid "Wait for a week or so, until %1$s automatically processes most of your content in the background."
msgstr "Entro una settimana %1$s elaborerà automaticamente la maggior parte dei tuoi contenuti in background."

#: admin/views/class-yoast-feature-toggles.php:192
msgid "Enhanced Slack sharing"
msgstr "Condivisione su Slack migliorata"

#: inc/class-wpseo-admin-bar-menu.php:364
msgid "Google Rich Results Test"
msgstr "Test Google Rich Results"

#: src/presenters/admin/indexing-notification-presenter.php:84
msgid "Because of a change in your tag base setting, some of your SEO data needs to be reprocessed."
msgstr "Poiché ci sono stati dei cambiamenti nelle impostazioni dell'URL della categoria, alcuni dei tuoi dati SEO devono essere elaborati di nuovo."

#. translators: %s: 'Semrush'
#: admin/views/class-yoast-integration-toggles.php:71
msgid "The %s integration offers suggestions and insights for keywords related to the entered focus keyphrase."
msgstr "L'integrazione con %s offre suggerimenti e approfondimenti per le parole chiave correlate alla frase chiave principale inserita."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/integrations.php:27
msgid "This tab allows you to selectively disable %1$s integrations with third-party products for all sites in the network. By default all integrations are enabled, which allows site admins to choose for themselves if they want to toggle an integration on or off for their site. When you disable an integration here, site admins will not be able to use that integration at all."
msgstr "Questa scheda ti permette di disabilitare le integrazioni %1$s da servizi di terze parti per tutti i siti nella tua rete. L'impostazione predefinita per tutte le integrazioni è di essere abilitate, così da permettere agli amministratori del sito di scegliere autonomamente se mostrare o nascondere un'integrazione per il loro sito. Quando disattivi un'integrazione qui, gli amministratori del sito non potranno più usare quell'integrazione."

#: admin/class-admin.php:248
msgid "Activate your subscription"
msgstr "Attiva il tuo abbonamento"

#: src/presenters/admin/indexing-error-presenter.php:64
msgid "Oops, something has gone wrong and we couldn't complete the optimization of your SEO data. Please click the button again to re-start the process. "
msgstr "Oops, qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. Fai di nuovo clic sul pulsante per iniziare di nuovo il processo."

#: src/presenters/admin/indexing-notification-presenter.php:87
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. "
msgstr "Puoi rendere più veloce il tuo sito e ottenere informazioni sulla sua struttura dei link interni permettendoci di effettuare alcune ottimizzazioni al modo in cui i dati SEO sono memorizzati. "

#: src/presenters/admin/indexing-notification-presenter.php:59
#: js/dist/first-time-configuration.js:3 js/dist/indexation.js:8
msgid "Start SEO data optimization"
msgstr "Inizia l'ottimizzazione dei dati SEO"

#: src/presenters/admin/indexing-list-item-presenter.php:42
msgid "Learn more about the benefits of optimized SEO data."
msgstr "Approfondisci i vantaggi dell'ottimizzazione dei dati SEO."

#: src/presenters/admin/indexing-list-item-presenter.php:40
msgid "You can speed up your site and get insight into your internal linking structure by letting us perform a few optimizations to the way SEO data is stored. If you have a lot of content it might take a while, but trust us, it's worth it."
msgstr "Puoi rendere più veloce il tuo sito e ottenere informazioni sulla sua struttura dei link interni permettendoci di effettuare alcune ottimizzazioni al modo in cui i dati SEO sono memorizzati. Se hai molti contenuti potrebbe volerci un po' di tempo, ma fidati, ne vale la pena."

#: src/integrations/watchers/indexable-homeurl-watcher.php:97
msgid "All permalinks were successfully reset"
msgstr "Tutti i permalink sono stati reimpostati con successo."

#: src/presenters/admin/indexing-list-item-presenter.php:37
msgid "Optimize SEO Data"
msgstr "Ottimizza i dati SEO"

#: src/presenters/admin/indexing-error-presenter.php:71
#: src/presenters/admin/indexing-failed-notification-presenter.php:71
msgid "If the problem persists, please contact support."
msgstr "Se il problema permane, contatta il supporto."

#. Translators: %1$s expands to an opening anchor tag for a link leading to the
#. Yoast SEO tools page, %2$s expands to a closing anchor tag.
#: src/presenters/admin/indexing-failed-notification-presenter.php:59
msgid "Something has gone wrong and we couldn't complete the optimization of your SEO data. Please %1$sre-start the process%2$s."
msgstr "Qualcosa non ha funzionato e non è possibile completare l'ottimizzazione dei dati SEO. %1$sInizia di nuovo il processo%2$s."

#. translators: %s expands to a mailto support link.
#: inc/class-addon-manager.php:822
msgid "If you still need support and have an active subscription for this product, please email %s."
msgstr "Se hai ancora bisogno del nostro aiuto e hai un'abbonamento attivo per questo plugin, inviaci una email a %s."

#. translators: 1: expands to <a> that refers to the help page, 2: </a> closing
#. tag.
#: inc/class-addon-manager.php:819
msgid "You can probably find an answer to your question in our %1$shelp center%2$s."
msgstr "Probabilmente nella %1$ssezione Help%2$s del nostro sito trovi la risposta alla tua domanda."

#: inc/class-addon-manager.php:816
msgid "Need support?"
msgstr "Hai bisogno di aiuto?"

#. translators: %1$s: expands to an opening anchor tag, %2$s: expands to a
#. closing anchor tag
#: admin/views/class-yoast-feature-toggles.php:235
msgid "Disabling Yoast SEO's XML sitemaps will not disable WordPress' core sitemaps. In some cases, this %1$s may result in SEO errors on your site%2$s. These may be reported in Google Search Console and other tools."
msgstr "La disabilitazione delle sitemap XML di Yoast SEO non disabilita le sitemap di WordPress. In alcuni casi, questo %1$s può causare errori di SEO sul tuo sito%2$s. Questi possono essere segnalati in Google Search Console e altri strumenti."

#: admin/views/licenses.php:75
msgid "Make your products stand out in Google"
msgstr "Make your products stand out in Google"

#: admin/views/licenses.php:58
msgid "Everything you need for Google News"
msgstr "Ogni cosa che ti serve per Google News"

#: admin/views/licenses.php:45
msgid "Start ranking better for your videos"
msgstr "Inizia a posizionarti meglio con i tuoi video"

#: admin/views/licenses.php:31
msgid "Stop losing customers to other local businesses"
msgstr "Smetti di perdere clienti a favore di altre aziende locali"

#. translators: 1: Yoast SEO, 2: translated version of "Off"
#: admin/views/class-yoast-feature-toggles.php:163
msgid "The advanced section of the %1$s meta box allows a user to remove posts from the search results or change the canonical. The settings in the schema tab allows a user to change schema meta data for a post. These are things you might not want any author to do. That's why, by default, only editors and administrators can do this. Setting to \"%2$s\" allows all users to change these settings."
msgstr "La sezione delle impostazioni avanzate del meta-box di %1$s permette all'utente di rimuovere gli articoli dai risultati di ricerca o di cambiare l'URL canonico. Le impostazioni nella scheda schema permettono all'utente di cambiare i metadati schema per un articolo. Queste sono azioni che è desiderabile che nessun autore faccia. Ecco perché, in modalità predefinita, solo gli editori e gli amministratori possono farlo. L'impostazione su \"%2$s\" permette a tutti gli utenti di cambiare queste impostazioni."

#: admin/views/class-yoast-feature-toggles.php:159
msgid "Security: no advanced or schema settings for authors"
msgstr "Sicurezza: non c'è alcuna impostazione avanzata o schema per gli autori"

#: src/config/schema-types.php:159
msgid "Report"
msgstr "Rapporto"

#: src/config/schema-types.php:155
msgid "Tech Article"
msgstr "Articolo tecnico"

#: src/config/schema-types.php:147
msgid "Satirical Article"
msgstr "Articolo di satira"

#: src/config/schema-types.php:143
msgid "Advertiser Content Article"
msgstr "Articolo con contenuti pubblicitari"

#: src/config/schema-types.php:139
msgid "News Article"
msgstr "Articolo di notizie"

#: src/config/schema-types.php:135
msgid "Social Media Posting"
msgstr "Pubblicazione sui social media"

#: src/config/schema-types.php:127
msgid "Article"
msgstr "Articolo"

#: src/config/schema-types.php:104
msgid "Search Results Page"
msgstr "Pagina dei risultati della ricerca "

#: src/config/schema-types.php:100
msgid "Real Estate Listing"
msgstr "Annunci immobiliari"

#: src/config/schema-types.php:96
msgid "Checkout Page"
msgstr "Pagina del pagamento"

#: src/config/schema-types.php:92
msgid "Collection Page"
msgstr "Pagina della collezione"

#: src/config/schema-types.php:88
msgid "Medical Web Page"
msgstr "Pagina web per servizi medici"

#: src/config/schema-types.php:84
msgid "Contact Page"
msgstr "Pagina dei contatti"

#: src/config/schema-types.php:80
msgid "Profile Page"
msgstr "Pagina del profilo"

#: src/config/schema-types.php:76
msgid "QA Page"
msgstr "Pagina QA (domande e risposte)"

#: src/config/schema-types.php:72
msgid "FAQ Page"
msgstr "Pagina per le FAQ (domande frequenti)"

#: src/config/schema-types.php:68
msgid "About Page"
msgstr "Pagina About"

#: src/config/schema-types.php:64
msgid "Item Page"
msgstr "Pagina elemento"

#: src/config/schema-types.php:60
msgid "Web Page"
msgstr "Pagina web"

#: src/config/schema-types.php:151
msgid "Scholarly Article"
msgstr "Articolo scolastico"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:175
msgid "Allow us to track some data about your site to improve our plugin."
msgstr "Permettici di tracciare qualche dato del tuo sito per migliorare il nostro plugin."

#: admin/views/class-yoast-feature-toggles.php:170
#: admin/views/class-yoast-feature-toggles.php:171 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Usage tracking"
msgstr "Tracciamento d'uso"

#: src/presenters/admin/indexing-notification-presenter.php:75
msgid "Because of a change in your permalink structure, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nella struttura dei permalink, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#: src/presenters/admin/indexing-notification-presenter.php:78
msgid "Because of a change in your home URL setting, some of your SEO data needs to be reprocessed."
msgstr "Poichè ci sono stati dei cambiamenti nelle impostazioni dell'URL della home, alcuni dei tuoi dati SEO devono essere processati di nuovo."

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:70
msgid "%1$s Internal Linking Blocks"
msgstr "Internal Linking Block di %1$s"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#. translators: %s: Expands to an indexable object's name, like a post type or
#. taxonomy.
#: admin/views/tabs/metas/paper-content/post_type/post-type.php:31
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:39
msgid "Show SEO settings for %1$s?"
msgstr "Vuoi mostrare le impostazioni SEO per %1$s?"

#. translators: %1$s: Link to article about text links, %2$s: Anchor closing
#. tag, %3$s: Emphasis open tag, %4$s: Emphasis close tag
#: admin/class-yoast-columns.php:48
msgid "The links columns show the number of articles on this site linking %3$sto%4$s this article and the number of URLs linked %3$sfrom%4$s this article. Learn more about %1$show to use these features to improve your internal linking%2$s, which greatly enhances your SEO."
msgstr "Le colonne dei collegamenti mostrano il numero di articoli su questo sito che collega %3$sa%4$s questo articolo e il numero di URL linkati %3$sda%4$s questo articolo. Ulteriori informazioni su %1$s come utilizzare queste funzionalità per migliorare i tuoi collegamenti interni%2$s, che migliorerà notevolmente la tua strategia SEO."

#: src/services/health-check/links-table-reports.php:59
msgid "The text link counter feature is not working as expected"
msgstr "Il contatore dei link inseriti nel testo non funziona come previsto"

#. translators: 1: Link to the Yoast SEO blog, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:74
msgid "The text link counter helps you improve your site structure. %1$sFind out how the text link counter can enhance your SEO%2$s."
msgstr "Il contatore dei link inseriti nel testo ti aiuta a migliorare la struttura del tuo sito. %1$sScopri come il contatore dei link inseriti nel testo può migliorare la tua strategia SEO%2$s."

#: src/services/health-check/links-table-reports.php:46
msgid "The text link counter is working as expected"
msgstr "Il contatore dei link nel testo sta funzionando correttamente"

#. translators: 1: Link to the Yoast help center, 2: Link closing tag.
#: src/services/health-check/links-table-reports.php:101
msgid "%1$sFind out how to solve this problem on our help center%2$s."
msgstr "%1$sScopri come risolvere questo problema nella nostra sezione di aiuto%2$s."

#. translators: %1$s: Yoast SEO
#: admin/class-yoast-columns.php:32
msgid "%1$s adds several columns to this page."
msgstr "%1$s aggiunge diverse colonne a questa pagina."

#: src/presenters/admin/indexing-notification-presenter.php:108
msgid "We estimate this will take less than a minute."
msgstr "Stimiamo che ci vorrà meno di un minuto."

#: src/presenters/admin/indexing-notification-presenter.php:112
msgid "We estimate this will take a couple of minutes."
msgstr "Stimiamo che ci vorranno un paio di minuti."

#. translators: %1$s: Link to article about content analysis, %2$s: Anchor
#. closing
#: admin/class-yoast-columns.php:39
msgid "We've written an article about %1$show to use the SEO score and Readability score%2$s."
msgstr "Abbiamo scritto un articolo su %1$scome utilizzare il punteggio SEO e il punteggio di leggibilità%2$s."

#: src/presenters/admin/migration-error-presenter.php:59
msgid "Show debug information"
msgstr "Mostra le informazioni di debug"

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:53
msgid "Your site will continue to work normally, but won't take full advantage of %s."
msgstr "Il tuo sito continuerà a funzionare normalmente, ma non sfrutterà al massimo %s."

#. translators: %s: Yoast SEO.
#: src/presenters/admin/migration-error-presenter.php:42
msgid "%s had problems creating the database tables needed to speed up your site."
msgstr "%s ha riscontrato problemi durante la creazione delle tabelle del database necessarie per rendere più veloce il tuo sito."

#: src/presenters/admin/search-engines-discouraged-presenter.php:41
msgid "I don't want this site to show in the search results."
msgstr "Non voglio che questo sito venga mostrato nei risultati di ricerca."

#. translators: 1: Link start tag to the WordPress Reading Settings page, 2:
#. Link closing tag.
#: src/presenters/admin/search-engines-discouraged-presenter.php:36
msgid "If you want search engines to show this site in their results, you must %1$sgo to your Reading Settings%2$s and uncheck the box for Search Engine Visibility."
msgstr "Se vuoi che i motori di ricerca mostrino questo sito nei loro risultati, %1$svai alle impostazioni di lettura%2$s e deseleziona la casella Visibilità ai motori di ricerca."

#: src/presenters/admin/indexing-notification-presenter.php:115
msgid "We estimate this could take a long time, due to the size of your site. As an alternative to waiting, you could:"
msgstr "Stimiamo che ciò potrebbe richiedere molto tempo a causa delle dimensioni del tuo sito. Invece di aspettare, potresti:"

#. translators: 1: Link to article about indexation command, 2: Anchor closing
#. tag, 3: Link to WP CLI.
#: src/presenters/admin/indexing-notification-presenter.php:127
msgid "%1$sRun the indexation process on your server%2$s using %3$sWP CLI%2$s."
msgstr "%1$sEsegui il processo di indicizzazione sul tuo server%2$s usando %3$sWP CLI%2$s."

#. translators: %1$s: link to help article about solving table issue. %2$s: is
#. anchor closing.
#: src/presenters/admin/migration-error-presenter.php:47
msgid "Please read %1$sthis help article%2$s to find out how to resolve this problem."
msgstr "Leggi %1$squesto articolo nell'Help%2$s per capire come risolvere il problema."

#: src/integrations/front-end/theme-titles.php:49
msgid "a theme that has proper title-tag theme support, or adapt your theme to have that support"
msgstr "un tema che ha un appropriato supporto al title-tag, o adatta il tuo tema per avere questo supporto"

#: admin/views/class-yoast-feature-toggles.php:182
msgid "REST API: Head endpoint"
msgstr "REST API: head endpoint"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Term hierarchy"
msgstr "Gerarchia dei termini"

#: inc/class-wpseo-replace-vars.php:1483
msgid "Replaced with the term ancestors hierarchy"
msgstr "Sostituito con il termine gerarchico progenitore"

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:186
msgid "This %1$s REST API endpoint gives you all the metadata you need for a specific URL. This will make it very easy for headless WordPress sites to use %1$s for all their SEO meta output."
msgstr "L’endpoint REST API di %1$s ti fornisce tutti i metadati di cui hai bisogno per uno specifico URL. Questo renderà molto facile per i siti WordPress headless usare %1$s per tutti i loro meta output SEO."

#. translators: 1: link open tag; 2: link close tag.
#: src/services/health-check/default-tagline-reports.php:64
msgid "%1$sYou can change the tagline in the customizer%2$s."
msgstr "%1$sPuoi cambiare il motto in Personalizza%2$s."

#: src/services/health-check/default-tagline-reports.php:46
msgid "You still have the default WordPress tagline. Even an empty one is probably better."
msgstr "Stai ancora usando il motto predefinito di WordPress. Puoi anche lasciare vuoto, che è meglio che usare il motto predefinito."

#: src/services/health-check/default-tagline-reports.php:44
msgid "You should change the default WordPress tagline"
msgstr "Dovresti cambiare il motto predefinito di WordPress."

#: src/services/health-check/default-tagline-reports.php:33
msgid "You are using a custom tagline or an empty one."
msgstr "Stai usando un motto personalizzato oppure hai lasciato lo spazio vuoto."

#: src/services/health-check/default-tagline-reports.php:31
msgid "You changed the default WordPress tagline"
msgstr "Hai cambiato il motto predefinito di WordPress"

#: src/services/health-check/page-comments-reports.php:32
msgid "Comments on your posts are displayed on a single page. This is just like we'd suggest it. You're doing well!"
msgstr "I commenti ai tuoi articoli sono visualizzati in una singola pagina. Questo è proprio quello che suggeriamo. Ottimo!"

#. translators: %s expands to '/%postname%/'
#: src/services/health-check/postname-permalink-reports.php:58
msgid "It's highly recommended to have your postname in the URL of your posts and pages. Consider setting your permalink structure to %s."
msgstr "Ti consigliamo vivamente di inserire il nome degli articoli e delle pagine nel loro URL. Considera di impostare la struttura dei permalink su %s."

#: src/services/health-check/postname-permalink-reports.php:43
msgid "You do not have your postname in the URL of your posts and pages"
msgstr "Non hai usato il nome degli articoli e delle pagine nel loro URL."

#: src/services/health-check/postname-permalink-reports.php:32
msgid "You do have your postname in the URL of your posts and pages."
msgstr "Hai usato il nome degli articoli e delle pagine nel loro URL."

#: src/services/health-check/postname-permalink-reports.php:30
msgid "Your permalink structure includes the post name"
msgstr "La struttura del permalink include il nome dell’articolo"

#: src/services/health-check/page-comments-reports.php:45
msgid "Comments on your posts break into multiple pages. As this is not needed in 999 out of 1000 cases, we recommend you disable it. To fix this, uncheck \"Break comments into pages...\" on the Discussion Settings page."
msgstr "I commenti sui tuoi articoli si dividono in più pagine. Poiché questo non è necessario in 999 casi su 1000, ti consigliamo di disabilitarlo. Per risolvere questo problema, deseleziona \"Dividi i commenti in pagine...\" nel menu Impostazioni>Discussione."

#: src/services/health-check/page-comments-reports.php:43
msgid "Comments break into multiple pages"
msgstr "I commenti si dividono in più pagine"

#: src/services/health-check/page-comments-reports.php:30
msgid "Comments are displayed on a single page"
msgstr "I commenti vengono visualizzati su una singola pagina"

#: src/helpers/post-helper.php:101
msgid "No title"
msgstr "Nessun titolo"

#. translators: 1: Opening tag of the link to the discussion settings page, 2:
#. Link closing tag.
#: src/services/health-check/page-comments-reports.php:58
msgid "%1$sGo to the Discussion Settings page%2$s"
msgstr "%1$sVai al menu Impostazioni>Discussione%2$s"

#. translators: 1: Start of a paragraph beginning with the Yoast icon, 2:
#. Expands to 'Yoast SEO', 3: Paragraph closing tag.
#: src/services/health-check/report-builder.php:201
msgid "%1$sThis was reported by the %2$s plugin%3$s"
msgstr "%1$sQuesto è stato riportato dal plugin %2$s%3$s"

#: admin/metabox/class-metabox.php:192
msgid "If you want to apply advanced <code>meta</code> robots settings for this page, please define them in the following field."
msgstr "Se vuoi applicare le impostazioni avanzate per i <code>meta</code> robot in questa pagina, definiscile nel campo seguente."

#. translators: 1: Link start tag to the Firefox website, 2: Link start tag to
#. the Chrome website, 3: Link start tag to the Edge website, 4: Link closing
#. tag.
#: admin/metabox/class-metabox.php:155 admin/taxonomy/class-taxonomy.php:99
msgid "The browser you are currently using is unfortunately rather dated. Since we strive to give you the best experience possible, we no longer support this browser. Instead, please use %1$sFirefox%4$s, %2$sChrome%4$s or %3$sMicrosoft Edge%4$s."
msgstr "Il browser che stai usando attualmente è obsoleto. Poiché ci teniamo a fornirti la miglior esperienza possibile, non supportiamo più questo browser. Al suo posto, puoi usare %1$sFirefox%4$s, %2$sChrome%4$s o %3$sMicrosoft Edge%4$s."

#: src/presenters/admin/sidebar-presenter.php:87 js/dist/new-settings.js:11
msgid "Learn SEO"
msgstr "Impara la SEO"

#: src/presenters/admin/sidebar-presenter.php:97 js/dist/new-settings.js:11
msgid "We have both free and premium online courses to learn everything you need to know about SEO."
msgstr "Abbiamo corsi online, sia gratuiti sia a pagamento per imparare tutto ciò che devi sapere riguardo la SEO."

#. translators: %1$s expands to Yoast SEO academy
#: src/presenters/admin/sidebar-presenter.php:104 js/dist/new-settings.js:13
msgid "Check out %1$s"
msgstr "Vai alla cassa di %1$s"

#. translators: %1$s expands to Yoast SEO academy, which is a clickable link.
#: src/presenters/admin/sidebar-presenter.php:95 js/dist/new-settings.js:9
msgid "Want to learn SEO from Team Yoast? Check out our %1$s!"
msgstr "Vuoi imparare la SEO dal Team di Yoast? Vai alla cassa della nostra %1$s"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-import.php:39
msgid "%s settings to import:"
msgstr "Impostazioni di %s da importare:"

#. translators: %1$s expands to Yoast SEO
#: admin/class-export.php:68
msgid "Your %1$s settings:"
msgstr "Le tue impostazioni di %1$s:"

#. translators: 1: expands to Yoast SEO, 2: expands to Import settings.
#: admin/views/tabs/tool/wpseo-import.php:23
msgid "Import settings from another %1$s installation by pasting them here and clicking \"%2$s\"."
msgstr "Importa le impostazioni da un'altra installazione di %1$s installation incollandole qui e facendo clic su \"%2$s\"."

#: admin/metabox/class-metabox.php:431 js/dist/block-editor.js:464
#: js/dist/elementor.js:447 js/dist/new-settings.js:32
#: js/dist/new-settings.js:188 js/dist/structured-data-blocks.js:13
msgid "Schema"
msgstr "Schema"

#: admin/views/tabs/social/twitterbox.php:21
msgid "Twitter uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph metadata\" setting on the Facebook tab enabled if you want to optimize your site for Twitter."
msgstr "Twitter usa i metadati Open Graph come Facebook, perciò assicurati di aver selezionato le impostazioni \"Aggiungi i metadati di Open Graph\" nella scheda Facebook se vuoi ottimizzare il tuo sito per Twitter."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:167
msgid "Please check the format of the YouTube URL you entered. %s"
msgstr "Controlla il formato dell'URL di Youtube che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:157
msgid "Please check the format of the Wikipedia URL you entered. %s"
msgstr "Controlla il formato dell'URL di Wikipedia che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:142
msgid "Please check the format of the Pinterest URL you entered. %s"
msgstr "Controlla il formato dell'URL di Pinterest che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:122
msgid "Please check the format of the Instagram URL you entered. %s"
msgstr "Controlla il formato dell'URL di Instagram che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:137
msgid "Please check the format of the MySpace URL you entered. %s"
msgstr "Controlla il formato dell'URL di MySpace che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:112
msgid "Please check the format of the Facebook Page URL you entered. %s"
msgstr "Controlla il formato dell'URL della pagina Facebook che hai inserito. %s"

#: admin/admin-settings-changed-listener.php:81
msgid "Settings saved."
msgstr "Impostazioni salvate."

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:127
msgid "Please check the format of the LinkedIn URL you entered. %s"
msgstr "Controlla il formato dell'URL di LinkedIn che hai inserito. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:162
msgid "Yandex confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Il codice di conferma di Yandex può contenere solo lettere dalla A alla F, numeri, trattini e trattini bassi. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:152
msgid "Twitter usernames can only contain letters, numbers, and underscores. %s"
msgstr "I nomi utente di Twitter possono solo contenere lettere, numeri e trattini bassi. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:147
msgid "Pinterest confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Il codice di conferma di Pinterest può contenere solo lettere dalla A alla F, numeri, trattini e trattini bassi. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:132
msgid "Bing confirmation codes can only contain letters from A to F, numbers, hyphens, and underscores. %s"
msgstr "Il codice di conferma di Bing può contenere solo lettere dalla A alla F, numeri, trattini e trattini bassi. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:117
msgid "Google verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "Il codice di conferma di Google può contenere solo lettere, numeri, trattini e trattini bassi. %s"

#. translators: %s: additional message with the submitted invalid value
#: admin/class-yoast-input-validation.php:107
msgid "Baidu verification codes can only contain letters, numbers, hyphens, and underscores. %s"
msgstr "Il codice di conferma di Baidu può contenere solo lettere, numeri, trattini e trattini bassi. %s"

#: admin/views/partial-notifications-template.php:45
msgid "Show this item."
msgstr "Mostra questa voce."

#: admin/views/partial-notifications-template.php:38
msgid "Hide this item."
msgstr "Nascondi questa voce"

#. translators: %d expands the amount of hidden problems.
#: admin/views/partial-notifications-errors.php:25
msgid "You have %d hidden problem:"
msgid_plural "You have %d hidden problems:"
msgstr[0] "Hai %d problema nascosto:"
msgstr[1] "Hai %d problemi nascosti:"

#. translators: %d expands the amount of hidden notifications.
#: admin/views/partial-notifications-warnings.php:25
msgid "You have %d hidden notification:"
msgid_plural "You have %d hidden notifications:"
msgstr[0] "Hai %d notifica nascosta:"
msgstr[1] "Hai %d notifiche nascoste:"

#. translators: %s expands to an invalid Facebook App ID.
#: inc/options/class-wpseo-option.php:482
msgid "%s does not seem to be a valid Facebook App ID. Please correct."
msgstr "%s sembra non essere un ID di Facebook valido. Correggilo."

#. translators: %s: form value as submitted.
#: admin/class-yoast-input-validation.php:319
msgid "The submitted value was: %s"
msgstr "Il valore inserito è: %s"

#: src/helpers/score-icon-helper.php:66
msgid "Focus keyphrase not set"
msgstr "La frase chiave non è stata impostata"

#. translators: %1$s: amount of errors, %2$s: the admin page title
#: admin/class-yoast-input-validation.php:65
msgid "The form contains %1$s error. %2$s"
msgid_plural "The form contains %1$s errors. %2$s"
msgstr[0] "Il modulo contiene %1$s errore. %2$s"
msgstr[1] "Il modulo contiene %1$s errori. %2$s"

#. translators: %s expands to the extension title
#: admin/views/licenses.php:165 admin/views/licenses.php:270
msgid "Activate %s for your site on MyYoast"
msgstr "Attiva %s per il tuo sito su MyYoast"

#: admin/class-customizer.php:109
msgid "Show blog page in breadcrumbs"
msgstr "Mostra la pagina blog nei breadcrumb"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the SEO score.
#: admin/formatter/class-metabox-formatter.php:110
#: admin/formatter/class-metabox-formatter.php:117
#: admin/formatter/class-metabox-formatter.php:124
#: admin/formatter/class-metabox-formatter.php:131
msgid "%1$sSEO%2$s: %3$s"
msgstr "%1$sSEO%2$s: %3$s"

#. translators: %1$s expands to the opening anchor tag, %2$s to the closing
#. anchor tag, %3$s to the readability score.
#: admin/formatter/class-metabox-formatter.php:140
#: admin/formatter/class-metabox-formatter.php:147
#: admin/formatter/class-metabox-formatter.php:154
#: admin/formatter/class-metabox-formatter.php:161
msgid "%1$sReadability%2$s: %3$s"
msgstr "%1$sLeggibilità%2$s: %3$s"

#. translators: %s expands to the score
#: admin/statistics/class-statistics-service.php:216
#: admin/statistics/class-statistics-service.php:221
#: admin/statistics/class-statistics-service.php:226
msgid "Posts with the SEO score: %s"
msgstr "Articoli con il punteggio SEO: %s"

#: inc/class-wpseo-rank.php:178
msgid "Post Noindexed"
msgstr "Articoli non indicizzati"

#: inc/class-wpseo-rank.php:158
msgid "No Focus Keyphrase"
msgstr "Manca la frase chiave"

#. translators: %s: expends to Yoast SEO
#: admin/class-admin.php:340
msgid "%s video tutorial"
msgstr "%s video tutorial"

#. translators: %s expands to the SEO score
#: inc/class-wpseo-rank.php:157 inc/class-wpseo-rank.php:162
#: inc/class-wpseo-rank.php:167 inc/class-wpseo-rank.php:172
#: inc/class-wpseo-rank.php:177
msgid "SEO: %s"
msgstr "SEO: %s"

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:39
msgid "To view your current crawl errors, %1$splease visit Google Search Console%2$s."
msgstr "Per visualizzare i tuoi errori di crawl attuali, %1$svisita Google Search Console%2$s."

#. Translators: %1$s: expands to opening anchor tag, %2$s expands to closing
#. anchor tag.
#: admin/google_search_console/views/gsc-display.php:32
msgid "Google has discontinued its Crawl Errors API. Therefore, any possible crawl errors you might have cannot be displayed here anymore. %1$sRead our statement on this for further information%2$s."
msgstr "Google ha sospeso la Crawl Errors API. Perciò, gli eventuali errori di crawl che potresti avere non possono più essere visualizzati qui. %1$sLeggi il nostro articolo per avere ulteriori informazioni%2$s."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:95
#: js/dist/first-time-configuration.js:5 js/dist/new-settings.js:224
#: js/dist/new-settings.js:231
msgid "Organization name"
msgstr "Nome dell'Organizzazione"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:59
msgid "Choose whether the site represents an organization or a person."
msgstr "Scegli se il sito rappresenta un'Organizzazione o una Persona."

#: admin/views/tabs/metas/general.php:67
msgid "Knowledge Graph & Schema.org"
msgstr "Knowledge Graph e Schema.org"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:66
msgid "Organization or person"
msgstr "Organizzazione o Persona"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:63
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:69
#: src/integrations/admin/first-time-configuration-integration.php:486
#: src/integrations/admin/first-time-configuration-integration.php:499
#: js/dist/new-settings.js:230
msgid "Organization"
msgstr "Organizzazione"

#. translators: %1$s is a link start tag to the Search Appearance settings,
#. %2$s is the link closing tag.
#: admin/class-schema-person-upgrade-notification.php:60
msgid "You have previously set your site to represent a person. We’ve improved our functionality around Schema and the Knowledge Graph, so you should go in and %1$scomplete those settings%2$s."
msgstr "In precedenza hai impostato il sito per rappresentare una persona. Abbiamo migliorato le nostre funzionalità riguardanti i dati Schema e il Knowledge Graph, per questo dovresti andare a %1$scompletare queste impostazioni%2$s."

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:113
#: js/dist/new-settings.js:232
msgid "Personal info"
msgstr "Informazioni personali"

#: admin/class-admin.php:297
msgid "(if one exists)"
msgstr "(se uno esiste)"

#: admin/class-admin.php:297
msgid "Wikipedia page about you"
msgstr "La tua pagina Wikipedia"

#: admin/class-admin.php:296
msgid "YouTube profile URL"
msgstr "URL del profilo YouTube"

#: admin/class-admin.php:294
msgid "Tumblr profile URL"
msgstr "URL del profiloTumblr"

#: admin/class-admin.php:293
msgid "SoundCloud profile URL"
msgstr "URL del profilo SoundCloud"

#: admin/class-admin.php:291
msgid "MySpace profile URL"
msgstr "URL del profilo MySpace"

#: src/generators/schema/article.php:136
msgid "Uncategorized"
msgstr "Senza categoria"

#: admin/views/tabs/social/accounts.php:20
msgid "If a Wikipedia page for you or your organization exists, add it too."
msgstr "Se hai una pagina Wikipedia a tuo nome della tua organizzazione, aggiungila. "

#: admin/class-admin.php:292
msgid "Pinterest profile URL"
msgstr "URL del profilo di Pinterest "

#: admin/class-admin.php:290
msgid "LinkedIn profile URL"
msgstr "URL del profilo LinkedIn "

#: admin/class-admin.php:289
msgid "Instagram profile URL"
msgstr "URL del profilo Instagram "

#: inc/class-my-yoast-api-request.php:141
msgid "No JSON object was returned."
msgstr "Non è stato restituito nessun oggetto JSON. "

#: src/integrations/admin/link-count-columns-integration.php:145
msgid "Received internal links"
msgstr "Link interni ricevuti"

#: src/integrations/admin/link-count-columns-integration.php:138
msgid "Outgoing internal links"
msgstr "Link interni in uscita"

#: admin/class-meta-columns.php:111 js/dist/block-editor.js:347
#: js/dist/classic-editor.js:343 js/dist/dashboard-widget.js:24
#: js/dist/editor-modules.js:289 js/dist/elementor.js:347
msgid "Keyphrase"
msgstr "Frase chiave"

#. translators: 1: Yoast SEO.
#: src/services/health-check/links-table-reports.php:88
msgid "For this feature to work, %1$s needs to create a table in your database. We were unable to create this table automatically."
msgstr "Perché questa opzione funzioni, %1$s deve creare una tabella nel tuo database. Non è stato possibile creare questa tabella in automatico."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:40
msgid "Cannot get the size of %1$s because of unknown reasons."
msgstr "Impossibile recuperare la dimensione di %1$s per motivi sconosciuti."

#. translators: %1$s expands to the requested url
#: admin/exceptions/class-file-size-exception.php:23
msgid "Cannot get the size of %1$s because it is hosted externally."
msgstr "Impossibile recuperare la dimensione di %1$s perché è ospitata esternamente."

#. translators: %s expands to the current page number
#: src/generators/breadcrumbs-generator.php:394
msgid "Page %s"
msgstr "Pagina %s"

#. translators: %1$s expands to the method name. %2$s expands to the class name
#: src/exceptions/missing-method.php:24
msgid "Method %1$s() does not exist in class %2$s"
msgstr "Il metodo  %1$s() non esiste nella classe %2$s"

#: admin/class-export.php:50
msgid "You do not have the required rights to export settings."
msgstr "Non hai i diritti necessari per esportare le impostazioni."

#: admin/import/class-import-settings.php:79
msgid "No settings found."
msgstr "Nessuna impostazione trovata."

#: admin/views/licenses.php:80
msgid "Improve sharing on Facebook and Pinterest"
msgstr "Migliora la condivisione su Facebook e Pinterest"

#: admin/views/tabs/tool/wpseo-import.php:15
msgid "Import of settings is only supported on servers that run PHP 5.3 or higher."
msgstr "L'importazione delle impostazioni è supportata solo sui server che eseguono PHP 5.3 o versioni successive."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:25
msgid "Export your %1$s settings here, to copy them on another site."
msgstr "Esporta le impostazioni di %1$s qui, per poi copiarle in un altro sito."

#. translators: %1$s expands to Yoast SEO, %2$s expands to Yoast.com
#: admin/class-export.php:89
msgid "These are settings for the %1$s plugin by %2$s"
msgstr "Queste sono le impostazioni per il plugin %1$s di %2$s"

#. translators: %1$s expands to Import settings
#: admin/class-export.php:57
msgid "Copy all these settings to another site's %1$s tab and click \"%1$s\" there."
msgstr "Copia tutte queste impostazioni nella scheda %1$s di un altro sito e fai clic \"%1$s\" lì."

#. translators: %s expands to Yoast SEO Premium
#: admin/watchers/class-slug-change-watcher.php:219
msgid "With %s, you can easily create such redirects."
msgstr "Con %s, puoi facilmente creare reindirizzamenti (redirects)."

#. translators: %1$s and %2$s expand to <em> items to emphasize the word in the
#. middle.
#: admin/class-admin-init.php:327
msgid "Changing your permalinks settings can seriously impact your search engine visibility. It should almost %1$s never %2$s be done on a live website."
msgstr "Cambiare le impostazioni dei permalink può influenzare la visibilità sui motori di ricerca. Non dovresti quasi  %1$s mai %2$s farlo quando un sito è online."

#: admin/class-admin-init.php:324
msgid "WARNING:"
msgstr "ATTENZIONE:"

#: admin/class-admin-init.php:333
msgid "Learn about why permalinks are important for SEO."
msgstr "Approfondisci il motivo per cui i permalink sono così importanti per la SEO. "

#: inc/class-wpseo-admin-bar-menu.php:288
msgid "Google Ads"
msgstr "Google Ads"

#. translators: %s expands to Yoast SEO Premium
#: admin/class-premium-upsell-admin-block.php:84 js/dist/new-settings.js:237
msgid "Upgrade to %s"
msgstr "Passa a %s"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:28
msgid "Not showing the date archives in the search results technically means those will have a %1$s robots meta. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per data nei motori di ricerca significa tecnicamente  che essi avranno impostato un %1$s nei metadati dei robots. %2$sLeggi di più riguardo alle impostazioni dei risultati di ricerca%3$s."

#: inc/class-wpseo-admin-bar-menu.php:353
msgid "Check Keyphrase Density"
msgstr "Controlla la densità della frase chiave"

#: admin/views/tabs/network/features.php:95
#: admin/views/tabs/network/integrations.php:82
#: src/integrations/admin/crawl-settings-integration.php:293
msgid "Disable"
msgstr "Disattiva"

#: admin/views/tabs/network/features.php:94
#: admin/views/tabs/network/integrations.php:81
#: src/integrations/admin/crawl-settings-integration.php:291
msgid "Allow Control"
msgstr "Permetti il controllo"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/network/crawl-settings.php:24
#: admin/views/tabs/network/features.php:27
msgid "This tab allows you to selectively disable %s features for all sites in the network. By default all features are enabled, which allows site admins to choose for themselves if they want to toggle a feature on or off for their site. When you disable a feature here, site admins will not be able to use that feature at all."
msgstr "Questa scheda ti permette di disattivare in modo selettivo le funzionalità di %s per tutti i siti del network. In modalità predefinita, tutte le funzionalità sono attivate: questo permette agli amministratori di scegliere quali funzioni attivare o meno per i propri siti. Quando disattivi una funzionalità qui, gli amministratori dei siti non potranno usarla per nulla."

#: admin/formatter/class-metabox-formatter.php:69
msgid "Keyphrase:"
msgstr "Frase chiave:"

#: admin/views/licenses.php:129
msgid "optimize a single post for synonyms and related keyphrases."
msgstr "ottimizza un singolo articolo per i sinonimi e le frasi chiave correlate."

#: admin/views/licenses.php:128
msgid "Synonyms & related keyphrases"
msgstr "Sinonimi e frasi chiave correlate."

#: admin/formatter/class-metabox-formatter.php:70
msgid "Remove keyphrase"
msgstr "Rimuovi la frase chiave"

#. translators: %s: argument name
#: admin/views/class-yoast-feature-toggle.php:161
msgid "%s is a required feature toggle argument."
msgstr "%s è un argomento della funzionalità di attivazione obbligatorio."

#: admin/class-yoast-form.php:1041 js/dist/first-time-configuration.js:12
msgid "This feature has been disabled by the network admin."
msgstr "Questa funzionalità è stata disattivata dall'amministratore del network."

#: admin/class-meta-columns.php:166
msgid "Focus keyphrase not set."
msgstr "La frase chiave non è stata impostata."

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Multiple keyphrases"
msgstr "Frasi chiave multiple"

#: admin/class-yoast-form.php:749 admin/metabox/class-metabox.php:654
#: admin/taxonomy/class-taxonomy-fields-presenter.php:133
msgid "Clear Image"
msgstr "Cancella l'immagine"

#. translators: %s expands to Yoast SEO Premium
#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/class-premium-popup.php:81
#: admin/class-premium-upsell-admin-block.php:67
#: admin/formatter/class-metabox-formatter.php:325
#: admin/watchers/class-slug-change-watcher.php:223
#: src/deprecated/admin/add-keyword-modal.php:47
#: src/deprecated/admin/keyword-synonyms-modal.php:47
#: src/deprecated/admin/multiple-keywords-modal.php:47
#: src/presenters/admin/sidebar-presenter.php:48
#: src/presenters/admin/sidebar-presenter.php:61 js/dist/block-editor.js:382
#: js/dist/classic-editor.js:378 js/dist/elementor.js:382
#: js/dist/externals-components.js:15 js/dist/externals-components.js:77
#: js/dist/externals-components.js:95 js/dist/externals-components.js:97
#: js/dist/externals-components.js:99 js/dist/externals-components.js:101
#: js/dist/new-settings.js:7 js/dist/new-settings.js:239
msgid "Get %s"
msgstr "Passa a %s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'SEO' plugin name of possibly
#. conflicting plugin with regard to the creation of duplicate SEO meta.
#: admin/class-plugin-conflict.php:88
msgid "Both %1$s and %2$s manage the SEO of your site. Running two SEO plugins at the same time is detrimental."
msgstr "Sia %1$s sia %2$s gestiscono la SEO del tuo sito. Avere due plugin SEO attivi contemporaneamente può esere dannoso."

#: inc/class-wpseo-admin-bar-menu.php:680
msgid "There is a new notification."
msgid_plural "There are new notifications."
msgstr[0] "C'è una nuova notifica. "
msgstr[1] "Ci sono nuove notifiche. "

#: inc/options/class-wpseo-option-titles.php:967
msgid "Colon"
msgstr "Due punti"

#. translators: %1$s expands to Yoast.
#: src/integrations/blocks/block-categories.php:62
msgid "%1$s Structured Data Blocks"
msgstr "Blocchi di Dati Strutturati di %1$s"

#. translators: %s expands to a 'Yoast SEO Premium' text linked to the
#. yoast.com website.
#: admin/formatter/class-metabox-formatter.php:313
#: src/deprecated/admin/add-keyword-modal.php:35
#: src/deprecated/admin/keyword-synonyms-modal.php:35
#: src/deprecated/admin/multiple-keywords-modal.php:35
#: js/dist/block-editor.js:374 js/dist/classic-editor.js:370
#: js/dist/elementor.js:374 js/dist/externals-components.js:7
msgid "Great news: you can, with %s!"
msgstr "Ottime notizie: puoi con %s!"

#: inc/class-wpseo-admin-bar-menu.php:348
msgid "Check links to this URL"
msgstr "Controlla i link a questo URL"

#: inc/class-wpseo-admin-bar-menu.php:283
msgid "Keyword research training"
msgstr "Keyword research training"

#. translators: %s: error message
#: admin/class-yoast-network-admin.php:198
msgid "Error: %s"
msgstr "Errore: %s"

#: admin/pages/network.php:36
msgid "Restore Site"
msgstr "Ripristina il sito web"

#. translators: %s: success message
#: admin/class-yoast-network-admin.php:196
msgid "Success: %s"
msgstr "Successo: %s"

#: admin/class-yoast-network-admin.php:149
msgid "No site has been selected to restore."
msgstr "Nessun sito web è stato selezionato per il ripristino."

#: admin/class-yoast-network-admin.php:110
msgid "You are not allowed to modify unregistered network settings."
msgstr "Non sei autorizzato a modificare le impostazioni di rete non registrate."

#: admin/menu/class-network-admin-menu.php:34
msgid "Network Settings"
msgstr "Impostazioni di rete"

#: admin/class-yoast-network-admin.php:266
msgid "You are not allowed to perform this action."
msgstr "Non sei autorizzato a eseguire questa azione."

#. translators: %s expands to the ID of a site within a multisite network.
#: admin/class-yoast-network-admin.php:158
msgid "Site with ID %d not found."
msgstr "Il sito web con l'ID %d non è stato trovato."

#: admin/class-yoast-network-admin.php:81
msgid "deleted"
msgstr "eliminato"

#: src/deprecated/admin/multiple-keywords-modal.php:32
msgid "Would you like to add another keyphrase?"
msgstr "Vorresti aggiungere un'altra frase chiave?"

#: inc/class-wpseo-replace-vars.php:1472
msgid "The site's tagline"
msgstr "Tagline (motto) del sito"

#. translators: %1$s expands to the missing field name.
#: admin/menu/class-replacevar-editor.php:150
msgid "Not all required fields are given. Missing field %1$s"
msgstr "Non sono stati riempiti tutti i campi obbligatori. Manca il campo %1$s"

#: src/deprecated/admin/keyword-synonyms-modal.php:32
#: js/dist/externals-components.js:97
msgid "Would you like to add keyphrase synonyms?"
msgstr "Vuoi aggiungere dei sinonimi della frase chiave?"

#: admin/formatter/class-metabox-formatter.php:310
#: src/deprecated/admin/add-keyword-modal.php:32
msgid "Would you like to add more than one keyphrase?"
msgstr "Vuoi aggiungere più di una frase chiave?"

#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:10
msgid "You haven't set a Shop page in your WooCommerce settings. Please do this first."
msgstr "Non hai impostato una pagina Negozio nelle impostazioni di WooCommerce. Devi farlo come prima cosa."

#. translators: %1$s expands to an opening anchor tag, %2$s expands to a
#. closing anchor tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:15
#: js/dist/new-settings.js:21
msgid "You can edit the SEO metadata for this custom type on the %1$sShop page%2$s."
msgstr "Puoi modificare i metadati SEO per questo tipo di contenuto personalizzato sulla %1$spagina del Negozio%2$s."

#: inc/class-wpseo-replace-vars.php:1486 js/dist/externals-redux.js:1
msgid "Current year"
msgstr "Anno corrente"

#: inc/class-wpseo-replace-vars.php:1523
msgid "description (custom taxonomy)"
msgstr "descrizione (tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1522
msgid "(custom taxonomy)"
msgstr "(tassonomia personalizzata)"

#: inc/class-wpseo-replace-vars.php:1521
msgid "(custom field)"
msgstr "(campo personalizzato)"

#: inc/class-wpseo-replace-vars.php:1512 js/dist/externals-redux.js:1
msgid "ID"
msgstr "ID"

#: inc/class-wpseo-replace-vars.php:1484 js/dist/externals-redux.js:1
msgid "Separator"
msgstr "Separatore"

#: inc/class-wpseo-replace-vars.php:1476 js/dist/externals-redux.js:1
msgid "Category"
msgstr "Categoria"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Tag"
msgstr "Tag"

#: inc/class-wpseo-replace-vars.php:1467 js/dist/externals-redux.js:1
msgid "Date"
msgstr "Data"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Modified"
msgstr "Modificato"

#: inc/class-wpseo-replace-vars.php:1482 js/dist/externals-redux.js:1
msgid "Search phrase"
msgstr "Frase di ricerca"

#. translators: %1$s expands to the translated name of the post type.
#. translators: 1: term label
#: admin/watchers/class-slug-change-watcher.php:84
#: admin/watchers/class-slug-change-watcher.php:104
msgid "You just deleted a %1$s."
msgstr "Hai appena eliminato %1$s."

#: inc/class-wpseo-replace-vars.php:1471 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Site title"
msgstr "Titolo del sito"

#: inc/class-wpseo-replace-vars.php:1477 js/dist/externals-redux.js:1
msgid "Primary category"
msgstr "Categoria primaria"

#: inc/class-wpseo-replace-vars.php:1469 js/dist/externals-redux.js:1
msgid "Parent title"
msgstr "Titolo genitore"

#: admin/views/tabs/metas/post-types.php:25
msgid "The settings on this page allow you to specify what the default search appearance should be for any type of content you have. You can choose which content types appear in search results and what their default description should be."
msgstr "Le impostazioni in questa pagina ti permettono di specificare quale sarà l'aspetto predefinito della ricerca per ciascuno tipo di contenuto che hai. Puoi scegliere quali tipi di contenuto fare apparire nei risultati di ricerca e quale sarà la loro descrizione predefinita."

#: inc/class-wpseo-replace-vars.php:1518
msgid "Caption"
msgstr "Didascalia"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Term title"
msgstr "Titolo del termine"

#: inc/class-wpseo-replace-vars.php:1480 js/dist/externals-redux.js:1
msgid "Term description"
msgstr "Descrizione del termine"

#: inc/class-wpseo-replace-vars.php:1472 js/dist/externals-redux.js:1
#: js/dist/new-settings.js:211 js/dist/new-settings.js:224
msgid "Tagline"
msgstr "Motto del sito"

#: admin/watchers/class-slug-change-watcher.php:217
msgid "You should create a redirect to ensure your visitors do not get a 404 error when they click on the no longer working URL."
msgstr "Dovresti creare un re-indirizzamento per assicurarti che i visitatori non incorrano in un errore 404 facendo clic su un URL non più funzionante."

#: inc/class-wpseo-replace-vars.php:1515
msgid "Page number"
msgstr "Numero di pagina"

#: inc/class-wpseo-replace-vars.php:1473 js/dist/externals-redux.js:1
msgid "Excerpt"
msgstr "Riassunto"

#: inc/class-wpseo-replace-vars.php:1470
msgid "Archive title"
msgstr "Titolo dell'archivio"

#. translators: %s expands to the post type name.
#: admin/views/tabs/metas/paper-content/post_type/woocommerce-shop-page.php:22
msgid "Settings for %s archive"
msgstr "Impostazioni per l'archivio %s"

#. translators: %1$s expands to the translated name of the post type.
#: admin/watchers/class-slug-change-watcher.php:65
msgid "You just trashed a %1$s."
msgstr "Hai appena spostato nel cestino %1$s."

#: admin/watchers/class-slug-change-watcher.php:216
msgid "Search engines and other websites can still send traffic to your deleted post."
msgstr "I motori di ricerca e altri siti web possono ancora inviare il traffico all'articolo eliminato."

#: inc/class-wpseo-replace-vars.php:1479 js/dist/externals-redux.js:1
msgid "Tag description"
msgstr "Descrizione del tag"

#: inc/class-wpseo-replace-vars.php:1478 js/dist/externals-redux.js:1
msgid "Category description"
msgstr "Descrizione della categoria"

#: admin/watchers/class-slug-change-watcher.php:213
msgid "Make sure you don't miss out on traffic!"
msgstr "Assicurati di non perdere traffico!"

#: inc/class-wpseo-replace-vars.php:1514
msgid "User description"
msgstr "Descrizione dell'utente"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Pagetotal"
msgstr "Totale pagine"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Pagenumber"
msgstr "Numero di pagina"

#: inc/class-wpseo-replace-vars.php:1520
msgid "Term404"
msgstr "Errore 404"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Post type (plural)"
msgstr "Post type (plurale)"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Post type (singular)"
msgstr "Post type (singolare)"

#: inc/class-wpseo-replace-vars.php:1474 js/dist/externals-redux.js:1
msgid "Excerpt only"
msgstr "Solo riassunto"

#: admin/views/tabs/dashboard/webmaster-tools.php:40
msgid "Baidu verification code"
msgstr "Codice di verifica di Baidu"

#. translators: %1$s expands to a link start tag to the Baidu Webmaster Tools
#. site add page, %2$s is the link closing tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:44
msgid "Get your Baidu verification code in %1$sBaidu Webmaster Tools%2$s."
msgstr "Puoi ottenere il codice di verifica Baidu su %1$sBaidu Webmaster Tools%2$s."

#: admin/class-bulk-editor-list-table.php:993
msgid "Content Type"
msgstr "Tipo di contenuto"

#: admin/class-bulk-editor-list-table.php:389
msgid "Show All Content Types"
msgstr "Mostra tutti i tipi di contenuto"

#: admin/class-bulk-editor-list-table.php:406
msgid "Filter by content type"
msgstr "Filtra per tipo di contenuto"

#. translators: %s is replaced with Yoast SEO.
#: admin/import/plugins/class-abstract-plugin-importer.php:257
msgid "The %s importer functionality uses temporary database tables. It seems your WordPress install does not have the capability to do this, please consult your hosting provider."
msgstr "La funzione %s di importazione usa tabelle temporanee di database. Sembra che la tua installazione di WordPress non sia in grado di gestirle, consulta il tuo provider."

#. translators: %s is replaced with the plugin's name.
#: admin/import/plugins/class-abstract-plugin-importer.php:132
msgid "Cleanup of %s data failed."
msgstr "La pulizia dati di %s non è andata a buon fine."

#: inc/class-wpseo-replace-vars.php:1470
msgid "Replaced with the normal title for an archive generated by WordPress"
msgstr "Sostituisci con il titolo normale per un archivio generato da WordPress"

#: admin/views/tabs/tool/import-seo.php:119
msgid "Clean"
msgstr "Pulisci"

#: admin/views/tabs/tool/import-seo.php:87
msgid "Step 3: Check your data"
msgstr "Passaggio 3: Verifica i tuoi dati"

#: admin/views/tabs/tool/import-seo.php:62
msgid "Step 2: Import"
msgstr "Passaggio 2: Importa"

#: admin/views/tabs/tool/import-seo.php:57
msgid "Please make a backup of your database before starting this process."
msgstr "Fai un backup del database prima di iniziare questo processo."

#: admin/views/tabs/tool/import-seo.php:39
msgid "Plugin: "
msgstr "Plugin:"

#. translators: %s is replaced with the name of the plugin we're removing data
#. from.
#: admin/import/class-import-status.php:124
msgid "%s data successfully removed."
msgstr "%s dati rimossi con successo"

#: admin/views/tabs/tool/import-seo.php:89
msgid "Please check your posts and pages and see if the metadata was successfully imported."
msgstr "Controlla le tue pagine e articoli e verifica che i metadata sono stati importati con successo."

#: admin/views/tabs/tool/import-seo.php:51
msgid "We've detected data from one or more SEO plugins on your site. Please follow the following steps to import that data:"
msgstr "Abbiamo trovato uno o più dati appartenenti ad altri plugin di SEO nel tuo sito. Segui i seguenti passi per importare i dati."

#. translators: %s is replaced with the name of the plugin we've found data
#. from.
#: admin/import/class-import-status.php:128
msgid "%s data found."
msgstr "Dati trovati da %s."

#. translators: %s is replaced with the name of the plugin we're importing data
#. from.
#: admin/import/class-import-status.php:121
msgid "%s data successfully imported."
msgstr "Dati importati con successo da %s."

#. translators: %s is replaced with the name of the plugin we're trying to find
#. data from.
#: admin/import/class-import-status.php:61
msgid "%s data not found."
msgstr "Non sono stati trovati dati da %s."

#: admin/views/tabs/tool/import-seo.php:108
msgid "Step 5: Clean up"
msgstr "Passaggio 5: Pulisci"

#: admin/views/tabs/tool/import-seo.php:110
msgid "Once you're certain your site is OK, you can clean up. This will remove all the original data."
msgstr "Una volta che sei sicuro che il tuo sito sia a posto, puoi pulire. Questo rimuoverà tutti i dati originali."

#: admin/views/tabs/tool/import-seo.php:55
msgid "Step 1: Create a backup"
msgstr "Passaggio 1: Crea un backup"

#: admin/statistics/class-statistics-service.php:229
msgid "Posts that should not show up in search results"
msgstr "Articoli da non mostrare nei risultati delle ricerche"

#. translators: %s expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:22
msgid "%s did not detect any plugin data from plugins it can import from."
msgstr "%s non ha trovato nessun dato dai plugin che possa essere importato."

#. translators: 1: expands to Yoast SEO
#: admin/views/tabs/tool/import-seo.php:67
msgid "This will import the post metadata like SEO titles and descriptions into your %1$s metadata. It will only do this when there is no existing %1$s metadata yet. The original data will remain in place."
msgstr "Questo importerà i metadata degli articoli come i titoli SEO e le descrizioni nei metadati di %1$s. Esegui l'azione solo se non ci sono ancora i metadati di %1$s. I dati esistenti non andranno persi."

#: admin/class-premium-upsell-admin-block.php:58 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "24/7 email support"
msgstr "Supporto 24/7 via email"

#: admin/views/tabs/metas/paper-content/media-content.php:15
msgid "We recommend you set this to Yes."
msgstr "Ti consigliamo di impostarlo su Sì."

#: admin/views/tabs/metas/paper-content/media-content.php:25
msgid "Redirect attachment URLs to the attachment itself?"
msgstr "Reindirizzare gli URL degli allegati all'allegato stesso?"

#: admin/views/tabs/metas/rss.php:20
msgid "Learn more about the RSS feed setting"
msgstr "Ulteriori informazioni sull'impostazione del feed RSS"

#: admin/views/tabs/metas/paper-content/rss-content.php:19
msgid "Learn more about the available variables"
msgstr "Ulteriori informazioni sulle variabili disponibili"

#: admin/views/tabs/social/accounts.php:19
msgid "To let search engines know which social profiles are associated to this site, enter your site social profiles data below."
msgstr "Per consentire ai motori di ricerca di sapere quali profili social sono associati a questo sito, inserisci i dati dei profili social del tuo sito qui sotto."

#. translators: %s expands to "this author's archives".
#: admin/views/user-profile.php:14
msgid "Do not allow search engines to show %s in search results."
msgstr "Non consentire ai motori di ricerca di mostrare %s nei risultati di ricerca."

#: admin/views/user-profile.php:15
msgid "this author's archives"
msgstr "archivi di questo autore"

#: admin/views/tabs/metas/taxonomies/category-url.php:14
msgid "Help on the category prefix setting"
msgstr "Aiuto per le impostazioni del prefisso delle categorie"

#. translators: %s expands to the post type's name with a link to the archive.
#: admin/views/tabs/metas/paper-content/post-type-content.php:52
msgid "the archive for %s"
msgstr "l'archivio per %s"

#. translators: %s expands to <code>/category/</code>
#: admin/views/tabs/metas/taxonomies/category-url.php:17
msgid "Category URLs in WordPress contain a prefix, usually %s, this feature removes that prefix, for categories only."
msgstr "Gli URL di categoria all'interno di WordPress usano un prefisso, solitamente %s. Questa funzione rimuove quel prefisso, soltanto dalle categorie."

#: admin/views/tabs/metas/media.php:15
msgid "Media & attachment URLs"
msgstr "URL dei Media e degli allegati"

#: admin/views/tabs/metas/taxonomies.php:54
msgid "Category URLs"
msgstr "URL delle categorie."

#: admin/views/tabs/metas/media.php:21
msgid "When you upload media (an image or video for example) to WordPress, it doesn't just save the media, it creates an attachment URL for it. These attachment pages are quite empty: they contain the media item and maybe a title if you entered one. Because of that, if you never use these attachment URLs, it's better to disable them, and redirect them to the media item itself."
msgstr "Quando carichi contenuti multimediali (un'immagine o un video ad esempio), WordPress non salva solo i file multimediali, ma crea un URL di allegato. Queste pagine di allegati sono abbastanza vuote: contengono l'elemento multimediale e forse un titolo, se ne inserisci uno. Per questo motivo, se non usi mai questi URL degli allegati, è meglio disabilitarli e reindirizzarlili all'elemento multimediale stesso."

#: admin/views/tabs/social/accounts.php:18
msgid "Learn more about your social profiles settings"
msgstr "Leggi di più sulle impostazioni dei tuoi profili sui social network."

#: admin/views/tabs/metas/taxonomies/category-url.php:24
msgid "Remove the categories prefix?"
msgstr "Vuoi rimuovere il prefisso delle categorie?"

#: admin/views/tabs/metas/paper-content/special-pages.php:12
msgid "Learn more about the special pages setting"
msgstr "Ulteriori informazioni sull'impostazione delle pagine speciali"

#: admin/views/tabs/metas/media.php:20
msgid "Learn more about the Media and attachment URLs setting"
msgstr "Ulteriori informazioni sull'impostazione degli URL dei Media e degli allegati"

#: admin/views/tabs/metas/paper-content/general/homepage.php:13
msgid "This is what shows in the search results when people find your homepage. This means this is probably what they see when they search for your brand name."
msgstr "Questo è ciò che viene mostrato nei risultati della ricerca quando le persone trovano la tua homepage. Ciò significa che probabilmente è quello che vedono quando cercano il tuo marchio."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:62
msgid "archives for authors without posts"
msgstr "archivi per gli autori senza articoli"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:53
msgid "Not showing the archives for authors without posts in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per gli autori senza articoli nei risultati di ricerca tecnicamente significa che quelli avranno una meta robot %1$s e saranno esclusi dalle sitemap XML. %2$sLeggi di più sulle impostazioni dei risultati di ricerca%3$s."

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:37
msgid "date archives"
msgstr "archivi per data"

#: admin/views/tabs/metas/paper-content/date-archives-settings.php:25
msgid "Help on the date archives search results setting"
msgstr "Aiuto per le impostazioni dei motori di ricerca degli archivi per data"

#: admin/views/tabs/metas/archives/help.php:32
msgid "Archives settings help"
msgstr "Guida alle impostazioni degli archivi"

#: admin/views/tabs/metas/archives/help.php:26
msgid "Learn more about the archives setting"
msgstr "Ulteriori informazioni sull'impostazione degli archivi"

#: admin/views/tabs/dashboard/webmaster-tools.php:75
msgid "Yandex verification code"
msgstr "Codice di verifica Yandex"

#: admin/views/tabs/dashboard/webmaster-tools.php:65
msgid "Google verification code"
msgstr "Codice di verifica Google"

#: admin/views/tabs/dashboard/webmaster-tools.php:55
msgid "Bing verification code"
msgstr "Codice di verifica Bing"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:79
msgid "Get your Yandex verification code in %1$sYandex Webmaster Tools%2$s."
msgstr "Acquisisci il tuo codice di verifica Yandex da %1$sYandex Webmaster Tools%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:69
msgid "Get your Google verification code in %1$sGoogle Search Console%2$s."
msgstr "Acquisisci il codice di verifica Google da %1$sGoogle Search Console%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/dashboard/webmaster-tools.php:59
msgid "Get your Bing verification code in %1$sBing Webmaster Tools%2$s."
msgstr "Acquisisci il codice di verifica Bing da %1$sBing Webmaster Tools%2$s."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:50
msgid "Help on the authors without posts archive search results setting"
msgstr "Aiuto per le impostazioni dei risultati di ricerca per gli autori senza un archivio degli articoli"

#. translators: 1: expands to <code>noindex</code>; 2: link open tag; 3: link
#. close tag.
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:30
msgid "Not showing the archive for authors in the search results technically means those will have a %1$s robots meta and will be excluded from XML sitemaps. %2$sMore info on the search results settings%3$s."
msgstr "Non mostrare gli archivi per gli autori nei risultati di ricerca tecnicamente significa che quelli avranno una meta robot %1$s e saranno esclusi dalle sitemap XML. %2$sLeggi di più sulle impostazioni dei risultati di ricerca%3$s."

#: admin/views/tabs/dashboard/webmaster-tools.php:19
msgid "You can use the boxes below to verify with the different Webmaster Tools. This feature will add a verification meta tag on your home page. Follow the links to the different Webmaster Tools and look for instructions for the meta tag verification method to get the verification code. If your site is already verified, you can just forget about these."
msgstr "Puoi usare i box qui sotto per fare la verifica con i differenti Webmaster Tools. Questa funzione aggiungerà un meta tag di verifica sulla tua home page. Segui i links per i differenti Webmaster Tools e leggi le istruzioni per i metodi di verifica dei meta tag per ottenere il tuo codice di verifica. Se il tuo sito è già stato verificato, puoi lasciare tutto così com'è."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:27
msgid "Help on the author archives search results setting"
msgstr "Aiuto per l'impostazione dei risultati della ricerca per gli archivi degli autori"

#. translators: %s expands to a feature's name
#. translators: %s expands to an integration's name
#: admin/views/tabs/dashboard/features.php:55
#: admin/views/tabs/network/features.php:62
#: admin/views/tabs/network/integrations.php:50
msgid "Help on: %s"
msgstr "Aiuto su: %s"

#: admin/views/tabs/dashboard/webmaster-tools.php:18
msgid "Learn more about the Webmaster Tools verification"
msgstr "Leggi di più sulla verifica con i Webmaster Tools"

#: admin/class-yoast-form.php:937 admin/class-yoast-form.php:977
#: admin/metabox/class-metabox.php:212
#: admin/views/tabs/dashboard/features.php:106
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:13
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:13
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "On"
msgstr "On"

#: admin/class-yoast-form.php:938 admin/class-yoast-form.php:978
#: admin/metabox/class-metabox.php:211
#: admin/views/class-yoast-feature-toggles.php:165
#: admin/views/tabs/dashboard/features.php:107
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:14
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:14
#: admin/views/tabs/metas/paper-content/taxonomy-content.php:19
#: js/dist/externals/componentsNew.js:778
msgid "Off"
msgstr "Off"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/features.php:27
msgid "%1$s comes with a lot of features. You can enable / disable some of them below. Clicking the question mark gives more information about the feature."
msgstr "%1$s ha molte caratteristiche. Qui sotto, puoi abilitare - disabilitare alcune di queste. Facendo clic sul punto interrogativo, potrai avere più informazioni sulle caratteristiche."

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/dashboard/dashboard.php:20
msgid "See who contributed to %1$s."
msgstr "Guarda chi ha contribuito a %1$s."

#: admin/views/class-view-utils.php:56
msgid "Help on this search results setting"
msgstr "Aiuto per il settaggio di questi risultati di ricerca."

#: admin/views/class-yoast-feature-toggles.php:70
msgid "See the XML sitemap."
msgstr "Vedi la mappa XML del sito."

#: admin/views/class-yoast-feature-toggles.php:145
msgid "Read why XML Sitemaps are important for your site."
msgstr "Leggi perché gli le sitemap XML sono importanti per il tuo sito."

#. translators: %s: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:144
msgid "Enable the XML sitemaps that %s generates."
msgstr "Abilita le sitemap XML generate da %s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:50
msgid "Not showing the archive for %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "Non mostrare l'archivio per %1$s nei risultati di ricerca tecnicamente significa che avranno meta robot %2$s e saranno esclusi dalle sitemap XML. %3$sLeggi di più sulle impostazioni dei risultati di ricerca%4$s."

#. translators: 1: expands to an indexable object's name, like a post type or
#. taxonomy; 2: expands to <code>noindex</code>; 3: link open tag; 4: link
#. close tag.
#: admin/views/class-view-utils.php:46
msgid "Not showing %1$s in the search results technically means those will have a %2$s robots meta and will be excluded from XML sitemaps. %3$sMore info on the search results settings%4$s."
msgstr "La mancata visualizzazione di %1$s nei risultati di ricerca significa tecnicamente che avranno un meta  robot %2$s e saranno esclusi dalle sitemap XML. %3$sLeggi di più sulle impostazioni dei risultati di ricerca %4$s."

#: admin/pages/metas.php:20
msgid "Media"
msgstr "Media"

#. translators: %1$s expands to the post type name.
#: admin/metabox/class-metabox.php:187
msgid "Should search engines follow links on this %1$s?"
msgstr "I motori di ricerca dovrebbero seguire i link per questo %1$s?"

#. translators: %s expands to the post type name.
#. Translators: %s translates to the Post Label in singular form
#: admin/metabox/class-metabox.php:177 js/dist/block-editor.js:408
#: js/dist/classic-editor.js:404 js/dist/elementor.js:408
msgid "Allow search engines to show this %s in search results?"
msgstr "Consenti ai motori di ricerca di mostrare %s nei risultati delle ricerche?"

#: admin/menu/class-admin-menu.php:90
#: src/presenters/meta-description-presenter.php:37
msgid "Search Appearance"
msgstr "Aspetto della ricerca"

#. translators: %s expands to an indexable object's name, like a post type or
#. taxonomy
#: admin/class-yoast-form.php:948
msgid "Show %s in search results?"
msgstr "Vuoi mostrare %s nei risultati di ricerca?"

#. translators: %1$s expands to Yes or No,  %2$s expands to the post type name.
#: admin/metabox/class-metabox.php:182
msgid "Default for %2$s, currently: %1$s"
msgstr "Predefinito per %2$s, attualmente: %1$s"

#: admin/pages/metas.php:19
msgid "Content Types"
msgstr "Tipi di contenuto"

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:80
msgid "Toggle %1$s's XML Sitemap"
msgstr "Attiva la sitemap XML di %1$s"

#: admin/views/tabs/social/twitterbox.php:27
msgid "Enable this feature if you want Twitter to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Abilita questa funzione se vuoi che Twitter mostri un'anteprima con le immagini ed un riassunto del testo quando un link del tuo sito viene condiviso."

#: admin/views/class-yoast-feature-toggles.php:75
#: js/dist/externals-components.js:104 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "SEO analysis"
msgstr "Analisi SEO"

#: admin/views/class-yoast-feature-toggles.php:115 js/dist/new-settings.js:215
msgid "The text link counter helps you improve your site structure."
msgstr "Il contatore di link nel testo ti aiuta a migliorare la struttura del tuo sito."

#: admin/formatter/class-metabox-formatter.php:201
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Good results"
msgstr "Risultati buoni"

#: admin/views/class-yoast-feature-toggles.php:85 js/dist/new-settings.js:213
msgid "The readability analysis offers suggestions to improve the structure and style of your text."
msgstr "L'analisi di leggibilità offre suggerimenti per migliorare la struttura e lo stile del testo."

#: admin/views/class-yoast-feature-toggles.php:108
msgid "Find out how cornerstone content can help you improve your site structure."
msgstr "Scopri come i contenuti cornerstone possono aiutarti a migliorare la struttura del tuo sito."

#. translators: %1$s expands to WooCommerce
#: admin/views/licenses.php:38
msgid "Allow customers to pick up their %s order locally"
msgstr "Consenti ai clienti di ritirare il loro ordine %s a livello locale"

#: admin/views/class-yoast-feature-toggles.php:107 js/dist/new-settings.js:215
msgid "The cornerstone content feature lets you to mark and filter cornerstone content on your website."
msgstr "La funzione del contenuto cornerstone ti consente di contrassegnare e filtrare i contenuti cornerstone sul tuo sito web."

#: admin/views/licenses.php:35
msgid "Get better search results in local search"
msgstr "Ottieni risultati migliori nella ricerca locale"

#: admin/views/class-yoast-feature-toggles.php:86
msgid "Discover why readability is important for SEO."
msgstr "Scopri perché la leggibilità è importante per la strategia SEO."

#. translators: %s expands to WordProof
#: src/integrations/third-party/wordproof-integration-toggle.php:90
msgid "Read more about how %s works."
msgstr "Ulteriori informazioni su come funziona %s."

#: admin/views/class-yoast-feature-toggles.php:77 js/dist/new-settings.js:213
msgid "The SEO analysis offers suggestions to improve the SEO of your text."
msgstr "L'analisi SEO offre suggerimenti per migliorare la strategia SEO del tuo testo."

#: admin/views/tabs/social/facebook.php:25
msgid "Enable this feature if you want Facebook and other social media to display a preview with images and a text excerpt when a link to your site is shared."
msgstr "Abilita questa funzione se vuoi che Facebook e altri social media visualizzino un'anteprima con immagini e un riassunto di testo quando viene condiviso un link al tuo sito."

#: admin/views/class-yoast-feature-toggles.php:78
msgid "Learn how the SEO analysis can help you rank."
msgstr "Scopri come l'analisi SEO può aiutare il tuo posizionamento."

#. translators: %s: 'Semrush'
#. translators: %s: Zapier.
#. translators: %s: Algolia.
#. translators: %s: 'Wincher'
#. translators: %s expands to WordProof
#: admin/views/class-yoast-integration-toggles.php:67
#: admin/views/class-yoast-integration-toggles.php:78
#: admin/views/class-yoast-integration-toggles.php:96
#: src/integrations/third-party/wincher.php:73
#: src/integrations/third-party/wordproof-integration-toggle.php:82
msgid "%s integration"
msgstr "Integrazione con %s"

#: admin/views/class-yoast-feature-toggles.php:116
msgid "Find out how the text link counter can enhance your SEO."
msgstr "Scopri come il contatore di link di testo può migliorare la tua SEO."

#. Author URI of the plugin
msgid "https://yoa.st/1uk"
msgstr "https://yoa.st/1uk"

#. Plugin URI of the plugin
msgid "https://yoa.st/1uj"
msgstr "https://yoa.st/1uj"

#. translators: %1$s resolves to Yoast.com
#: admin/class-yoast-dashboard-widget.php:124
msgid "Latest blog posts on %1$s"
msgstr "Post recenti sul blog %1$s"

#: admin/formatter/class-metabox-formatter.php:203
#: js/dist/externals/analysisReport.js:17
msgid "Remove highlight from the text"
msgstr "Rimuovi l'evidenziazione nel testo"

#: admin/formatter/class-metabox-formatter.php:202
#: js/dist/externals/analysisReport.js:17
msgid "Highlight this result in the text"
msgstr "Evidenzia questo risultato nel testo"

#: admin/formatter/class-metabox-formatter.php:200
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Considerations"
msgstr "Considerazioni"

#: admin/formatter/class-metabox-formatter.php:197
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Errors"
msgstr "Errori"

#: src/integrations/admin/first-time-configuration-notice-integration.php:138
msgid "First-time SEO configuration"
msgstr "Configurazione SEO iniziale"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:65
msgid "Updated %s"
msgstr "%s aggiornato"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:165 admin/views/tool-file-editor.php:215
msgid "Edit the content of your %s:"
msgstr "Modifica il contenuto del tuo %s:"

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:28
msgid "You cannot create a %s file."
msgstr "Non è possibile creare un file %s."

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:49 admin/views/tool-file-editor.php:76
msgid "You cannot edit the %s file."
msgstr "Non è possibile modificare il file %s."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:125
msgid "Create %s file"
msgstr "Crea file %s"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:174 admin/views/tool-file-editor.php:224
msgid "Save changes to %s"
msgstr "Salva le modifiche al %s"

#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:186
msgid "%s file"
msgstr "File %s"

#. translators: %1$s expands to the dependency name.
#: admin/class-suggested-plugins.php:136
msgid "More information about %1$s"
msgstr "Ulteriori informazioni su %1$s"

#: src/integrations/admin/old-configuration-integration.php:38
msgid "Old Configuration Wizard"
msgstr "Precedente Configurazione guidata"

#: admin/class-admin-utils.php:78 admin/class-premium-popup.php:82
#: admin/class-premium-upsell-admin-block.php:68 admin/class-yoast-form.php:912
#: admin/formatter/class-metabox-formatter.php:205
#: admin/formatter/class-metabox-formatter.php:329 admin/views/licenses.php:97
#: admin/watchers/class-slug-change-watcher.php:224
#: src/deprecated/admin/add-keyword-modal.php:51
#: src/deprecated/admin/keyword-synonyms-modal.php:51
#: src/deprecated/admin/multiple-keywords-modal.php:51
#: src/integrations/admin/workouts-integration.php:210
#: src/integrations/admin/workouts-integration.php:239
#: src/presenters/admin/help-link-presenter.php:75 js/dist/block-editor.js:468
#: js/dist/externals/componentsNew.js:136 js/dist/externals/helpers.js:18
#: js/dist/indexables-page.js:35 js/dist/integrations-page.js:1
#: js/dist/integrations-page.js:5 js/dist/integrations-page.js:13
#: js/dist/integrations-page.js:15
msgid "(Opens in a new browser tab)"
msgstr "(Si apre in una nuova scheda del browser)"

#. translators: %1$s expands to the posttype label, %2$s expands anchor to blog
#. post about cornerstone content, %3$s expands to </a>
#: admin/filters/class-cornerstone-filter.php:104
msgid "Mark the most important %1$s as 'cornerstone content' to improve your site structure. %2$sLearn more about cornerstone content%3$s."
msgstr "Contrassegna gli (le) %1$s più importanti come 'cornerstone content' (contenuti centrali) per migliorare la struttura del tuo sito. %2$sLeggi di più sui contenuti cornerstone%3$s."

#. translators: %s expands to the extension title
#: admin/views/licenses.php:151 admin/views/licenses.php:255
msgid "Manage your %s subscription on MyYoast"
msgstr "Gestisci il tuo abbonamento per  %s su MyYoast"

#: admin/statistics/class-statistics-service.php:77
msgid "Hey, your SEO is doing pretty well! Check out the stats:"
msgstr "Ehi, la tua SEO sta andando abbastanza bene! Guarda le statistiche:"

#: admin/statistics/class-statistics-service.php:73
msgid "You don't have any published posts, your SEO scores will appear here once you make your first post!"
msgstr "Non hai pubblicato nessun post, i tuoi punteggi SEO appariranno qui appena scriverari il tuo primo post!"

#. translators: %1$s expands to an opening strong tag, %2$s expands to a
#. closing strong tag
#: admin/statistics/class-statistics-service.php:210
msgid "Posts %1$swithout%2$s a focus keyphrase"
msgstr "Articoli %1$ssenza%2$s frase chiave"

#: admin/class-yoast-dashboard-widget.php:127
msgid "Read more like this on our SEO blog"
msgstr "Leggi più articoli di questo tipo nel nostro blog SEO."

#: admin/views/licenses.php:159 admin/views/licenses.php:264
msgid "Not activated"
msgstr "Non attivato"

#: admin/views/licenses.php:145 admin/views/licenses.php:249
msgid "Activated"
msgstr "Attivato"

#: admin/class-meta-columns.php:267
msgid "All Readability Scores"
msgstr "Tutti i punteggi di leggibilità"

#: admin/class-meta-columns.php:263
msgid "Filter by Readability Score"
msgstr "Filtra per punteggio di leggibilità"

#. translators: %1$s expands to the product name. %2$s expands to a link to My
#. Yoast
#: inc/class-addon-manager.php:469
msgid "You are not receiving updates or support! Fix this problem by adding this site and enabling %1$s for it in %2$s."
msgstr "Non stai ricevendo aggiornamenti o assistenza! Risolvi il problema aggiungendo questo sito e abilitando %1$s in %2$s."

#. translators: %s expands to the readability score
#: inc/class-wpseo-rank.php:194 inc/class-wpseo-rank.php:199
#: inc/class-wpseo-rank.php:204
msgid "Readability: %s"
msgstr "Leggibilità: %s"

#. translators: %1$s expands to Yoast
#: src/presenters/admin/sidebar-presenter.php:28 js/dist/new-settings.js:11
msgid "%1$s recommendations for you"
msgstr "Consigli per te da %1$s"

#. translators: %1$s expands to the request method
#: admin/class-remote-request.php:95
msgid "Request method %1$s is not valid."
msgstr "Il metodo di richiesta %1$s non è valido."

#: admin/views/class-yoast-feature-toggles.php:113 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Text link counter"
msgstr "Contatore di link nei testi"

#: src/integrations/admin/link-count-columns-integration.php:144
msgid "Number of internal links linking to this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Numero di link interni che puntano a questo articolo. Vai nella sezione aiuto alla voce \"Colonne Yoast\" per maggiori informazioni."

#: src/integrations/admin/link-count-columns-integration.php:137
msgid "Number of outgoing internal links in this post. See \"Yoast Columns\" text in the help tab for more info."
msgstr "Numero di link interni in questo articolo. Vai nella sezione aiuto alla voce \"Colonne Yoast\" per maggiori informazioni."

#. translators: %s expands to Yoast
#: admin/class-yoast-columns.php:60
msgid "%s Columns"
msgstr "Colonne %s"

#: admin/class-meta-columns.php:104
#: admin/taxonomy/class-taxonomy-columns.php:91
msgid "Readability score"
msgstr "Punteggio di leggibilità"

#. translators: %s expands to 'Yoast SEO Premium'.
#: admin/formatter/class-metabox-formatter.php:319
#: src/deprecated/admin/add-keyword-modal.php:41
#: src/deprecated/admin/keyword-synonyms-modal.php:41
#: src/deprecated/admin/multiple-keywords-modal.php:41
#: js/dist/block-editor.js:380 js/dist/classic-editor.js:376
#: js/dist/elementor.js:380 js/dist/externals-components.js:13
#: js/dist/externals-components.js:75
msgid "Other benefits of %s for you:"
msgstr "Altri benefici da %s per te:"

#: admin/filters/class-cornerstone-filter.php:87
#: admin/views/class-yoast-feature-toggles.php:105
#: js/dist/externals-components.js:18 js/dist/new-settings.js:215
#: js/dist/new-settings.js:224
msgid "Cornerstone content"
msgstr "Contenuto Cornerstone (contenuto centrale)"

#: src/integrations/admin/crawl-settings-integration.php:332
msgid "Upgrade Premium"
msgstr "Passa a Premium"

#: admin/class-premium-upsell-admin-block.php:56 js/dist/block-editor.js:376
#: js/dist/classic-editor.js:372 js/dist/elementor.js:376
#: js/dist/externals-components.js:9 js/dist/externals-components.js:71
#: js/dist/new-settings.js:237
msgid "Superfast internal linking suggestions"
msgstr "Suggerimenti super veloci di link interni"

#: admin/class-yoast-form.php:141 admin/class-yoast-form.php:146
#: js/dist/first-time-configuration.js:3 js/dist/new-settings.js:1
#: js/dist/settings.js:112
msgid "Save changes"
msgstr "Salva le modifiche"

#. translators: %2$s expands to 'RS Head Cleaner' plugin name of possibly
#. conflicting plugin with regard to differentiating output between search
#. engines and normal users.
#: admin/class-plugin-conflict.php:85
msgid "The plugin %2$s changes your site's output and in doing that differentiates between search engines and normal users, a process that's called cloaking. We highly recommend that you disable it."
msgstr "Il plugin %2$s modifica l'aspetto del tuo sito differenziando il contenuto per i motori di ricerca da quello per gli utenti \"normali\". Questo è un processo chiamato cloaking, che potrebbe portarti a brutte penalizzazioni. Ti suggeriamo di disabilitarlo."

#: admin/class-premium-popup.php:88
#: admin/formatter/class-metabox-formatter.php:328
#: src/deprecated/admin/add-keyword-modal.php:50
#: src/deprecated/admin/keyword-synonyms-modal.php:50
#: src/deprecated/admin/multiple-keywords-modal.php:50
#: js/dist/block-editor.js:382 js/dist/classic-editor.js:378
#: js/dist/elementor.js:382 js/dist/externals-components.js:15
#: js/dist/externals-components.js:77
msgid "1 year free support and updates included!"
msgstr "1 anno di aggiornamenti e supporto personalizzato inclusi!"

#: admin/class-premium-upsell-admin-block.php:59 js/dist/block-editor.js:378
#: js/dist/classic-editor.js:374 js/dist/elementor.js:378
#: js/dist/externals-components.js:11 js/dist/externals-components.js:73
#: js/dist/new-settings.js:237
msgid "No ads!"
msgstr "Nessuna pubblicità!"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Facebook & Twitter"
msgstr "Facebook e Twitter"

#: admin/class-premium-upsell-admin-block.php:57 js/dist/new-settings.js:237
msgid "Social media preview"
msgstr "Anteprima social media"

#: admin/class-premium-upsell-admin-block.php:54 js/dist/new-settings.js:237
msgid "Increase your SEO reach"
msgstr "Aumenta la tua visibilità SEO"

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "No more dead links"
msgstr "Niente più link non funzionanti"

#: admin/class-premium-upsell-admin-block.php:55 js/dist/new-settings.js:237
msgid "Easy redirect manager"
msgstr "Facile gestore per il reindirizzamento"

#: admin/views/tabs/metas/paper-content/rss-content.php:32
#: js/dist/new-settings.js:189
msgid "Variable"
msgstr "Variabile"

#: admin/views/tabs/metas/paper-content/rss-content.php:25
#: js/dist/new-settings.js:189
msgid "Available variables"
msgstr "Variabili disponibili"

#: admin/class-admin.php:341
msgid "Scroll to see the table content."
msgstr "Scorri per vedere il contenuto della tabella."

#: admin/views/partial-notifications-warnings.php:22
msgid "No new notifications."
msgstr "Nessuna nuova notifica."

#: admin/class-bulk-editor-list-table.php:882
msgid "Save all"
msgstr "Salva tutto"

#: admin/class-bulk-editor-list-table.php:881
msgid "Save"
msgstr "Salva"

#. translators: 1: Author name; 2: Site name.
#: inc/options/class-wpseo-option-titles.php:247
msgid "%1$s, Author at %2$s"
msgstr "%1$s, Autore presso %2$s"

#: admin/formatter/class-metabox-formatter.php:204
#: js/dist/externals/analysis.js:383 js/dist/externals/analysisReport.js:17
msgid "Marks are disabled in current view"
msgstr "L'evidenziazione è disabilitata"

#: admin/views/tool-import-export.php:88
msgid "Export settings"
msgstr "Esporta le impostazioni"

#: inc/class-wpseo-replace-vars.php:1513 js/dist/first-time-configuration.js:5
#: js/dist/settings.js:102
msgid "Name"
msgstr "Nome"

#. translators: %1$s expands to Yoast SEO, %2$s is a link start tag to the
#. plugin page on WordPress.org, %3$s is the link closing tag.
#: admin/class-product-upsell-notice.php:146
msgid "We've noticed you've been using %1$s for some time now; we hope you love it! We'd be thrilled if you could %2$sgive us a 5 stars rating on WordPress.org%3$s!"
msgstr "Abbiamo notato che stai utilizzando %1$s da un po' di tempo; ci auguriamo che ti stia piacendo! Saremmo entusiasti se tu volessi %2$sdarci un punteggio di 5 stelle su WordPress.org%3$s!"

#. translators: %1$s is a link start tag to the bugreport guidelines on the
#. Yoast help center, %2$s is the link closing tag.
#: admin/class-product-upsell-notice.php:154
msgid "If you are experiencing issues, %1$splease file a bug report%2$s and we'll do our best to help you out."
msgstr "Se stai riscontrando problemi, %1$sinviaci un bug report%2$s e faremo del nostro meglio per aiutarti."

#: admin/class-product-upsell-notice.php:161
msgid "Please don't show me this notification anymore"
msgstr "Non mostrarmi più questa notifica"

#. translators: %1$s: '%%term_title%%' variable used in titles and meta's
#. template that's not compatible with the given template, %2$s: expands to
#. 'HelpScout beacon'
#: admin/class-admin.php:335
msgid "Warning: the variable %1$s cannot be used in this template. See the %2$s for more info."
msgstr "Attenzione: la variabile %1$s non può essere utilizzata in questo template. Vai su %2$s per maggiori informazioni."

#: admin/class-bulk-editor-list-table.php:789
msgid "(no title)"
msgstr "(senza titolo)"

#. translators: %1$s expands anchor to premium plugin page, %2$s expands to
#. </a>
#: admin/class-product-upsell-notice.php:129
msgid "By the way, did you know we also have a %1$sPremium plugin%2$s? It offers advanced features, like a redirect manager and support for multiple keyphrases. It also comes with 24/7 personal support."
msgstr "A proposito, sapevi che abbiamo anche un %1$sPlugin Premium%2$s? Fornisce funzionalità più avanzate, quali un gestore di redirect ed il supporto per frasi chiave multiple. Inoltre dispone di una assistenza personalizzata 24/7."

#. translators: 1: Yoast SEO
#: admin/views/class-yoast-feature-toggles.php:155
msgid "The %1$s admin bar menu contains useful links to third-party tools for analyzing pages and makes it easy to see if you have new notifications."
msgstr "La barra di amministrazione di %1$s contiene dei link utili a strumenti di terze parti per analizzare le pagine e rendere semplice verificare se si hanno nuove notifiche."

#: admin/views/class-yoast-feature-toggles.php:152 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "Admin bar menu"
msgstr "Menu della barra di amministrazione"

#: admin/pages/dashboard.php:41 admin/pages/network.php:19
#: admin/views/tabs/dashboard/features.php:22
#: admin/views/tabs/network/features.php:22
msgid "Features"
msgstr "Funzionalità"

#: admin/metabox/class-metabox.php:173 js/dist/externals/analysis.js:211
#: js/dist/externals/analysis.js:265 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "SEO title"
msgstr "Titolo SEO"

#: inc/options/class-wpseo-option-titles.php:987
msgid "Vertical bar"
msgstr "Barra verticale"

#: admin/metabox/class-metabox.php:184 admin/metabox/class-metabox.php:189
#: admin/views/tabs/metas/paper-content/media-content.php:20
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402
msgid "No"
msgstr "No"

#: admin/metabox/class-metabox.php:183 admin/metabox/class-metabox.php:188
#: admin/views/tabs/metas/paper-content/media-content.php:19
#: js/dist/block-editor.js:402 js/dist/classic-editor.js:398
#: js/dist/elementor.js:402 js/dist/externals/schemaBlocks.js:13
msgid "Yes"
msgstr "Si"

#: inc/options/class-wpseo-option-titles.php:979
msgid "Asterisk"
msgstr "Asterisco"

#: inc/options/class-wpseo-option-titles.php:975
msgid "Bullet"
msgstr "Punto"

#: inc/options/class-wpseo-option-titles.php:983
msgid "Low asterisk"
msgstr "Asterisco basso"

#: inc/options/class-wpseo-option-titles.php:955
msgid "Dash"
msgstr "Trattino"

#: inc/options/class-wpseo-option-titles.php:971
msgid "Middle dot"
msgstr "punto medio"

#: inc/options/class-wpseo-option-titles.php:963
msgid "Em dash"
msgstr "trattino em"

#: inc/options/class-wpseo-option-titles.php:959
msgid "En dash"
msgstr "trattino en"

#: inc/options/class-wpseo-option-titles.php:1003
msgid "Less than sign"
msgstr "Simboli di minore"

#: inc/options/class-wpseo-option-titles.php:991
msgid "Small tilde"
msgstr "Tilde piccola"

#: inc/options/class-wpseo-option-titles.php:1007
msgid "Greater than sign"
msgstr "Simbolo di maggiore"

#: inc/options/class-wpseo-option-titles.php:999
msgid "Right angle quotation mark"
msgstr "Virgoletta bassa destra"

#: inc/options/class-wpseo-option-titles.php:995
msgid "Left angle quotation mark"
msgstr "Virgoletta bassa sinistra"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:10
msgid "Choose the symbol to use as your title separator. This will display, for instance, between your post title and site name. Symbols are shown in the size they'll appear in the search results."
msgstr "Scegli il simbolo da utilizzare come separatore del titolo. Apparirà, ad esempio, fra il titolo del tuo articolo ed il nome del sito. I simboli sono visualizzati nella dimensione con cui appariranno nei risultati delle ricerche."

#: admin/views/licenses.php:62
msgid "Optimize your site for Google News"
msgstr "Ottimizza il tuo sito per Google News"

#: admin/views/licenses.php:142 admin/views/licenses.php:246
msgid "Installed"
msgstr "Installato"

#. translators: 1: expands to Yoast SEO extensions
#: admin/views/licenses.php:225
msgid "%1$s to optimize your site even further"
msgstr "%1$s per ottimizzare ulteriormente il tuo sito"

#. translators: 1: expands to Yoast SEO Premium
#: admin/views/licenses.php:112
msgid "%1$s, take your optimization to the next level!"
msgstr "%1$s, porta la tua ottimizzazione ad un livello superiore!"

#: admin/views/tool-bulk-editor.php:63
msgid "Posts list"
msgstr "Elenco articoli"

#: admin/views/tool-bulk-editor.php:62
msgid "Posts list navigation"
msgstr "Navigazione elenco articoli"

#: admin/views/tool-bulk-editor.php:61
msgid "Filter posts list"
msgstr "Filtro elenco articoli"

#: admin/views/tabs/metas/archives/help.php:20
msgid "Note that links to archives might be still output by your theme and you would need to remove them separately."
msgstr "Si noti che i link agli archivi possono continuare ad essere visualizzati dal tema e occorrerà rimuoverli separatamente,"

#: admin/views/licenses.php:124
msgid "Redirect manager"
msgstr "Gestore dei redirect"

#. translators: %1$s expands to Yoast SEO, %2$s expands to WooCommerce
#: admin/views/licenses.php:84
msgid "A seamless integration between %1$s and %2$s"
msgstr "Una integrazione senza interruzioni fra  %1$s e %2$s"

#. translators: %1$s expands to Yoast, %2$s expands to WooCommerce
#: admin/views/licenses.php:82
msgid "Use %1$s breadcrumbs instead of %2$s ones"
msgstr "Utilizza le bricole di pane %1$s al posto di %2$s"

#: admin/views/licenses.php:36
msgid "Easily insert Google Maps, a store locator, opening hours and more"
msgstr "Inserisci facilmente Google Maps, un localizzatore di punti vendita, gli orari di apertura e molto altro"

#: admin/views/licenses.php:64
msgid "Creates XML News Sitemaps"
msgstr "Crea sitemap XML per le news"

#: admin/views/licenses.php:63
msgid "Immediately pings Google on the publication of a new post"
msgstr "Fai un ping a Google alla pubblicazione di un nuovo articolo"

#: admin/views/licenses.php:51
msgid "Make videos responsive through enabling fitvids.js"
msgstr "Rendi i video responsive tramite l'abilitazione di fitvids.js"

#: admin/views/licenses.php:50
msgid "Enhance the experience of sharing posts with videos"
msgstr "Migliora l'esperienza di condivisione di articoli contenenti video"

#: admin/views/licenses.php:49
msgid "Show your videos in Google Videos"
msgstr "Visualizza i tuoi video su Google Videos"

#: admin/views/licenses.php:133
msgid "check what your Facebook or Twitter post will look like."
msgstr "verifica come appaiono i tuoi articoli su Facebook o Twitter."

#: admin/views/licenses.php:125
msgid "create and manage redirects from within your WordPress install."
msgstr "crea e gestisci i redirect direttamente dalla tua installazione WordPress."

#. translators: Text between 1: and 2: will only be shown to screen readers. 3:
#. expands to the product name.
#: admin/views/licenses.php:196 admin/views/licenses.php:300
msgid "More information %1$sabout %3$s%2$s"
msgstr "Ulteriori informazioni %1$ssu %3$s%2$s"

#. translators: 1: expands to Yoast SEO
#: admin/views/licenses.php:220
msgid "%1$s extensions"
msgstr "Estensioni %1$s"

#: admin/views/licenses.php:209
msgid "Comes with our 30-day no questions asked money back guarantee"
msgstr "Include la nostra garanzia di restituzione dei soldi entro 30 giorni senza nessuna domanda sul motivo della richiesta"

#: admin/views/licenses.php:137
msgid "gain access to our 24/7 support team."
msgstr "Ottieni accesso al nostro team di assistenza 24/7."

#: admin/views/licenses.php:136
msgid "Premium support"
msgstr "Assistenza premium"

#: admin/views/licenses.php:132
msgid "Social previews"
msgstr "Anteprime social"

#: admin/class-admin.php:255
msgid "Get Premium"
msgstr "Passa a Premium"

#: admin/menu/class-base-menu.php:258
#: src/integrations/admin/menu-badge-integration.php:35
msgid "Premium"
msgstr "Premium"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:803
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifica &#8220;%s&#8221;"

#: admin/views/partial-notifications-warnings.php:20
#: inc/class-wpseo-admin-bar-menu.php:244
msgid "Notifications"
msgstr "Notifiche"

#: admin/views/tabs/social/twitterbox.php:17
msgid "Twitter settings"
msgstr "Impostazioni Twitter"

#: admin/views/tabs/social/facebook.php:18
msgid "Facebook settings"
msgstr "Impostazioni Facebook"

#: inc/class-wpseo-admin-bar-menu.php:293
msgid "Google Trends"
msgstr "Google Trends"

#: admin/views/user-profile.php:45
msgid "Disable SEO analysis"
msgstr "Disabilita l'analisi SEO"

#: admin/views/user-profile.php:48
msgid "Removes the focus keyphrase section from the metabox and disables all SEO-related suggestions."
msgstr "Rimuovi la scheda delle frasi chiave dal meta box e disabilita tutti i suggerimenti relativi alla SEO."

#: admin/views/tabs/social/pinterest.php:16
msgid "Pinterest settings"
msgstr "Impostazioni Pinterest"

#. translators: %s: number of notifications
#: admin/menu/class-admin-menu.php:120 inc/class-wpseo-admin-bar-menu.php:660
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] "%s notifica"
msgstr[1] "%s notifiche"

#: admin/views/tabs/metas/rss.php:15
msgid "RSS feed settings"
msgstr "Impostazioni feed RSS"

#: admin/views/tabs/metas/paper-content/general/title-separator.php:12
msgid "Title separator symbol"
msgstr "Simbolo separatore del titolo"

#: admin/views/js-templates-primary-term.php:28
msgid "Make primary"
msgstr "Rendi primario"

#: admin/views/tabs/metas/breadcrumbs.php:15
msgid "Breadcrumbs settings"
msgstr "Impostazioni dei Breadcrumbs"

#: admin/formatter/class-metabox-formatter.php:68
#: admin/metabox/class-metabox-section-readability.php:28
msgid "Readability"
msgstr "Leggibilità"

#: admin/formatter/class-metabox-formatter.php:120
#: admin/formatter/class-metabox-formatter.php:150
#: admin/statistics/class-statistics-service.php:217
#: inc/class-wpseo-rank.php:140 inc/class-wpseo-rank.php:163
#: inc/class-wpseo-rank.php:195 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Needs improvement"
msgstr "Da migliorare"

#: admin/views/user-profile.php:60
msgid "Removes the readability analysis section from the metabox and disables all readability-related suggestions."
msgstr "Rimuovi la scheda dell'analisi di leggibilità dal meta box e disabilita tutti i suggerimenti sulla leggibilità."

#: admin/views/class-yoast-feature-toggles.php:83
#: js/dist/externals-components.js:44 js/dist/new-settings.js:213
#: js/dist/new-settings.js:224
msgid "Readability analysis"
msgstr "Analisi di leggibilità"

#: admin/views/user-profile.php:57
msgid "Disable readability analysis"
msgstr "Disabilita l'analisi di leggibilità"

#: admin/formatter/class-metabox-formatter.php:113
#: admin/formatter/class-metabox-formatter.php:143 inc/class-wpseo-rank.php:138
msgid "Not available"
msgstr "Non disponibile"

#: admin/formatter/class-metabox-formatter.php:198
#: admin/views/partial-notifications-errors.php:20
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Problems"
msgstr "Problemi"

#: admin/formatter/class-metabox-formatter.php:199
#: js/dist/editor-modules.js:319 js/dist/externals-components.js:15
#: js/dist/externals/analysisReport.js:35
msgid "Improvements"
msgstr "Miglioramenti"

#: admin/class-meta-columns.php:151
msgid "Meta description not set."
msgstr "Meta descrizione non impostata."

#: admin/class-meta-columns.php:235
msgid "Filter by SEO Score"
msgstr "Filtra per punteggio SEO"

#: admin/formatter/class-metabox-formatter.php:170
#: js/dist/externals/schemaBlocks.js:9
msgid "Analysis"
msgstr "Analisi"

#: admin/menu/class-admin-menu.php:54 admin/pages/dashboard.php:32
msgid "Dashboard"
msgstr "Bacheca"

#. translators: %1$s is a link start tag to the permalink settings page, %2$s
#. is the link closing tag.
#: src/services/health-check/postname-permalink-reports.php:71
msgid "You can fix this on the %1$sPermalink settings page%2$s."
msgstr "Puoi correggerlo nella %1$sPagina delle impostazioni dei permalink%2$s."

#: admin/views/partial-notifications-errors.php:22
msgid "Good job! We could detect no serious SEO problems."
msgstr "Buon lavoro! Non siamo in grado di individuare nessun problema SEO."

#: admin/views/partial-notifications-errors.php:21
msgid "We have detected the following issues that affect the SEO of your site."
msgstr "Abbiamo individuato i seguenti problemi che riguardano la SEO del tuo sito."

#: admin/views/tabs/social/pinterest.php:36
msgid "Pinterest confirmation"
msgstr "Conferma Pinterest"

#: inc/class-wpseo-replace-vars.php:1477
msgid "Replaced with the primary category of the post/page"
msgstr "Sostituito con la categoria principale nell'articolo/pagina"

#: admin/views/tabs/metas/paper-content/taxonomy-content.php:18
#: js/dist/new-settings.js:155 js/dist/new-settings.js:228
msgid "Format-based archives"
msgstr "Archivio Format-based"

#: admin/views/tabs/social/pinterest.php:24
msgid "If you have already confirmed your website with Pinterest, you can skip the step below."
msgstr "Se hai già confermato il tuo sito in Pinterest, puoi saltare il passaggio successivo."

#: admin/views/tabs/dashboard/webmaster-tools.php:24
msgid "Webmaster Tools verification"
msgstr "Verifica per gli strumenti per webmaster"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:48
msgid "Existing %1$s Title"
msgstr "Titolo %1$s Esistente"

#. translators: $s expands to Yoast SEO Premium
#. translators: %s expands to the product name
#. translators: 1: Yoast WooCommerce SEO
#: admin/views/licenses.php:182 admin/views/licenses.php:285
#: js/dist/integrations-page.js:5
msgid "Buy %s"
msgstr "Compra %s"

#. translators: %1$s expands to Yoast SEO
#: admin/class-bulk-title-editor-list-table.php:50
msgid "New %1$s Title"
msgstr "Nuovo %1$s Titolo"

#: inc/sitemaps/class-sitemaps-cache-validator.php:111
msgid "Trying to build the sitemap cache key, but the postfix and prefix combination leaves too little room to do this. You are probably requesting a page that is way out of the expected range."
msgstr "Sto provando a costruire una chiave sicura della cache della sitemap, ma la combinazione di suffisso e prefisso lascia poco spazio di azione. Probabilmente stai cercando di elaborare una pagina con molti caratteri."

#: inc/sitemaps/class-sitemaps-cache-validator.php:294
msgid "Expected an integer as input."
msgstr "L'input dev'essere un intero"

#: admin/views/redirects.php:32
#: src/integrations/admin/redirects-integration.php:48
msgid "Redirects"
msgstr "Redirect"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:27
msgid "Regular"
msgstr "Standard"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:279
msgid "Keep"
msgstr "Mantieni"

#: admin/views/tabs/metas/taxonomies/category-url.php:10
#: src/integrations/admin/crawl-settings-integration.php:280
msgid "Remove"
msgstr "Rimuovi"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:32
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Bold the last page"
msgstr "Evidenzia in grassetto l'ultima pagina"

#: admin/pages/dashboard.php:47 admin/pages/network.php:20
#: admin/views/tabs/dashboard/integrations.php:21
#: admin/views/tabs/network/integrations.php:22
#: src/integrations/admin/integrations-page.php:73
#: js/dist/integrations-page.js:33
msgid "Integrations"
msgstr "Integrazioni"

#: admin/taxonomy/class-taxonomy-columns.php:152
msgid "Term is set to noindex."
msgstr "Il termine è impostato su noidex."

#. translators: accessibility text. %1$s expands to the term title, %2$s to the
#. taxonomy title.
#: admin/views/js-templates-primary-term.php:18
msgid "Make %1$s primary %2$s"
msgstr "Rendi %1$s primario %2$s"

#: admin/views/js-templates-primary-term.php:32
msgid "Primary"
msgstr "Primario"

#. translators: %s is the taxonomy title. This will be shown to screenreaders
#: admin/views/js-templates-primary-term.php:38
msgid "Primary %s"
msgstr "%s Primario"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:22
msgid "Show Blog page"
msgstr "Vedi la pagina del Blog"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:26
msgid "Bold"
msgstr "Grassetto"

#: admin/views/tabs/metas/archives.php:19
#: admin/views/tabs/metas/paper-content/author-archive-settings.php:16
#: js/dist/new-settings.js:71 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Author archives"
msgstr "Archivi autore"

#: src/integrations/admin/crawl-settings-integration.php:214
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Enabled"
msgstr "Abilitato"

#: src/integrations/admin/crawl-settings-integration.php:213
#: src/presenters/admin/light-switch-presenter.php:120
msgid "Disabled"
msgstr "Disabilitato"

#. translators: %s: wp_title() function.
#: inc/class-wpseo-replace-vars.php:1459
msgid "The separator defined in your theme's %s tag."
msgstr "Il separatore definito nel tag %s del tuo tema."

#: inc/class-wpseo-rank.php:139
msgid "No index"
msgstr "Noindex"

#: src/deprecated/admin/ryte/class-ryte.php:137
msgid "Once Weekly"
msgstr "Una volta alla settimana"

#: admin/class-meta-columns.php:100
#: admin/taxonomy/class-taxonomy-columns.php:87
msgid "SEO score"
msgstr "Punteggio SEO"

#: inc/class-wpseo-admin-bar-menu.php:374
msgid "Pinterest Rich Pins Validator"
msgstr "Pinterest Rich Pins Validator"

#. translators: %s expands to the name of a post type (plural).
#: inc/class-upgrade.php:1310 inc/options/class-wpseo-option-titles.php:284
msgid "%s Archive"
msgstr "Archivi %s"

#: wp-seo-main.php:546
msgid "Activation failed:"
msgstr "Attivazione non riuscita:"

#: inc/class-wpseo-admin-bar-menu.php:339
msgid "Analyze this page"
msgstr "Analizza questa pagina"

#: inc/options/class-wpseo-option-titles.php:255
msgid "Archives for"
msgstr "Archivi per"

#: inc/class-wpseo-admin-bar-menu.php:384
msgid "CSS Validator"
msgstr "Validazione CSS"

#: inc/class-wpseo-admin-bar-menu.php:359
msgid "Check Google Cache"
msgstr "Controlla la Cache di Google"

#: inc/options/class-wpseo-option-titles.php:254
msgid "Error 404: Page not found"
msgstr "Errore 404: Pagina non trovata"

#: inc/class-wpseo-admin-bar-menu.php:369
msgid "Facebook Debugger"
msgstr "Debugger Facebook"

#: inc/class-wpseo-admin-bar-menu.php:389
msgid "Google Page Speed Test"
msgstr "Test Google Page Speed"

#: inc/class-wpseo-admin-bar-menu.php:379
msgid "HTML Validator"
msgstr "Validazione HTML"

#: inc/options/class-wpseo-option-titles.php:256
msgid "Home"
msgstr "Home"

#: inc/class-wpseo-admin-bar-menu.php:275
msgid "Keyword Research"
msgstr "Ricerca Parole chiave"

#: inc/class-wpseo-admin-bar-menu.php:394
msgid "Mobile-Friendly Test"
msgstr "Test Mobile-Friendly"

#: inc/options/class-wpseo-option-ms.php:245
msgid "No numeric value was received."
msgstr "Nessun valore numerico inserito."

#: inc/class-wpseo-admin-bar-menu.php:429
#: inc/class-wpseo-admin-bar-menu.php:477
msgid "SEO Settings"
msgstr "Impostazioni SEO"

#: inc/options/class-wpseo-option-social.php:109
msgid "Summary with large image"
msgstr "Sommario con immagini grandi"

#: wp-seo-main.php:489
msgid "The Standard PHP Library (SPL) extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L'estensione Standard PHP Library (SPL) non pare disponibile. Richiedi al tuo host di abilitarla."

#: inc/options/class-wpseo-option-ms.php:229
#: inc/options/class-wpseo-option-ms.php:245
msgid "The default blog setting must be the numeric blog id of the blog you want to use as default."
msgstr "L'impostazione di default del blog deve essere l'id numerico del blog che vuoi usare come default."

#: wp-seo-main.php:535
msgid "The filter extension seem to be unavailable. Please ask your web host to enable it."
msgstr "L'estensione del filtro sembra non essere disponibile. Suggeriamo di richiedere l'abilitazione al proprio gestore hosting."

#. translators: %s is the ID number of a blog.
#: inc/options/class-wpseo-option-ms.php:233
msgid "This must be an existing blog. Blog %s does not exist or has been marked as deleted."
msgstr "Questo deve essere un blog esistente. Il blog %s non esiste o è stato segnato come cancellato."

#. Plugin Name of the plugin
#: admin/capabilities/class-capability-manager-integration.php:72
#: js/dist/block-editor.js:468
msgid "Yoast SEO"
msgstr "Yoast SEO"

#: inc/options/class-wpseo-option-titles.php:257
msgid "You searched for"
msgstr "Hai cercato"

#. translators: %1$s expands to the option name and %2$sexpands to Yoast SEO
#: inc/options/class-wpseo-option-ms.php:209
msgid "%1$s is not a valid choice for who should be allowed access to the %2$s settings. Value reset to the default."
msgstr "%1$snon è una scelta valida per chi dovrebbe essere autorizzato ad accedere alle impostazioni  di %2$s. Valori ripristinati al default."

#. translators: %s expands to an invalid URL.
#: inc/options/class-wpseo-option.php:394
msgid "%s does not seem to be a valid url. Please correct."
msgstr "%s non sembra essere una url valida. Correggi e riprova."

#: admin/formatter/class-metabox-formatter.php:134
#: admin/formatter/class-metabox-formatter.php:164
#: admin/statistics/class-statistics-service.php:227
#: inc/class-wpseo-rank.php:142 inc/class-wpseo-rank.php:173
#: inc/class-wpseo-rank.php:205 js/dist/block-editor.js:227
#: js/dist/block-editor.js:464 js/dist/editor-modules.js:227
#: js/dist/externals-components.js:120 js/dist/externals/analysis.js:383
#: js/dist/frontend-inspector-resources.js:1 js/dist/post-edit.js:11
#: js/dist/term-edit.js:1
msgid "Good"
msgstr "Buona"

#. translators: 1: link to post; 2: link to blog.
#: inc/options/class-wpseo-option-titles.php:252
msgid "The post %1$s appeared first on %2$s."
msgstr "L'articolo %1$s proviene da %2$s."

#. translators: %1$s expands to Yoast SEO, %2$s / %3$s: links to the
#. installation manual in the Readme for the Yoast SEO code repository on
#. GitHub
#: wp-seo-main.php:511
msgid "The %1$s plugin installation is incomplete. Please refer to %2$sinstallation instructions%3$s."
msgstr "L'installazione del plugin %1$s è incompleta. Consulta le %2$sistruzioni per l'installazione%3$s."

#. translators: %s expands to a post type.
#: inc/options/class-wpseo-option-titles.php:521
msgid "Please select a valid taxonomy for post type \"%s\""
msgstr "Seleziona una tassonomia valida per i contenuti di tipo \"%s\""

#. translators: %s expands to a taxonomy slug.
#: inc/options/class-wpseo-option-titles.php:559
msgid "Please select a valid post type for taxonomy \"%s\""
msgstr "Seleziona un tipo di contenuto valido per la tassonomia \"%s\""

#. Author of the plugin
msgid "Team Yoast"
msgstr "Team Yoast"

#. translators: %s expands to the search phrase.
#: inc/options/class-wpseo-option-titles.php:249
msgid "You searched for %s"
msgstr "Hai cercato %s"

#. translators: %s expands to a twitter user name.
#: inc/options/class-wpseo-option-social.php:182
msgid "%s does not seem to be a valid Twitter Username. Please correct."
msgstr "%s sembra essere un nome utente di Twitter non valido. Correggilo."

#. translators: 1: Verification string from user input; 2: Service name.
#: inc/options/class-wpseo-option.php:358
msgid "%1$s does not seem to be a valid %2$s verification string. Please correct."
msgstr "%1$s non sembra essere una string di verifica valida %2$s. Correggi e riprova."

#. Description of the plugin
msgid "The first true all-in-one SEO solution for WordPress, including on-page content analysis, XML sitemaps and much more."
msgstr "La prima vera soluzione SEO tutto-in-uno per WordPress, compresa l'analisi dei contenuti su ogni pagina, sitemap XML e molto altro."

#. translators: %1$s / %2$s expands to a link to pinterest.com's help page.
#: admin/views/tabs/social/pinterest.php:30
msgid "To %1$sconfirm your site with Pinterest%2$s, add the meta tag here:"
msgstr "Per %1$sconvalidare il tuo sito con Pinterest%2$s, aggiungi qui il meta tag:"

#. translators: %1$s expands to Yoast SEO
#: admin/pages/tools.php:43
msgid "%1$s comes with some very powerful built-in tools:"
msgstr "%1$s viene fornito con alcuni strumenti integrati molto potenti:"

#. translators: %s expands to the variable used for term title.
#: admin/formatter/class-term-metabox-formatter.php:164
#: inc/class-upgrade.php:1313 inc/options/class-wpseo-option-titles.php:320
msgid "%s Archives"
msgstr "%s Archivi"

#: admin/pages/tools.php:70
msgid "&laquo; Back to Tools page"
msgstr "&laquo; Torna alla pagina degli strumenti"

#: admin/views/tabs/metas/paper-content/rss-content.php:43
#: js/dist/new-settings.js:189
msgid "A link to the post, with the title as anchor text."
msgstr "Un link all'articolo, con il titolo come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:51
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name and description as anchor text."
msgstr "Un link al tuo sito, con il nome e la descrizione del tuo sito come testo di ancoraggio."

#: admin/views/tabs/metas/paper-content/rss-content.php:47
#: js/dist/new-settings.js:189
msgid "A link to your site, with your site's name as anchor text."
msgstr "Un link al tuo sito, con il nome del tuo sito come testo di ancoraggio."

#: inc/class-wpseo-replace-vars.php:108
msgid "A replacement variable can not start with \"%%cf_\" or \"%%ct_\" as these are reserved for the WPSEO standard variable variables for custom fields and custom taxonomies. Try making your variable name unique."
msgstr "Una variabile di sostituzione non può iniziare con \"%%cf_\" o \"%%ct_\" perchè sono riservate per le variabili di variabili standard di WPSEO per i campi personalizzati e per le tassonomie personalizzate. Prova a rendere il nome di questa variabile univoco."

#: inc/class-wpseo-replace-vars.php:105
msgid "A replacement variable can only contain alphanumeric characters, an underscore or a dash. Try renaming your variable."
msgstr "Una variabile di sostituzione può contenere solo caratteri alfanumerici, il carattere di sottolineatura o il trattino. Prova a rinoinare la tua variabile."

#: admin/views/tabs/social/facebook.php:20
msgid "Add Open Graph meta data"
msgstr "Aggiungi meta dati Open Graph"

#: inc/class-wpseo-replace-vars.php:1518
msgid "Attachment caption"
msgstr "Didascalia Allegato"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:77
#: js/dist/new-settings.js:239
msgid "Blog"
msgstr "Blog"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:17
msgid "Breadcrumb for 404 Page"
msgstr "Breadcrumb per la pagina 404"

#: admin/pages/tools.php:36
msgid "Bulk editor"
msgstr "Editor di massa"

#: admin/views/tabs/metas/paper-content/rss-content.php:15
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put after each post in the feed"
msgstr "Contenuto da inserire dopo qualsiasi post nel feed"

#: admin/views/tabs/metas/paper-content/rss-content.php:14
#: js/dist/new-settings.js:189 js/dist/new-settings.js:228
msgid "Content to put before each post in the feed"
msgstr "Contenuto da inserire prima di ogni post nel feed"

#: admin/views/tabs/dashboard/dashboard.php:51
msgid "Credits"
msgstr "Crediti"

#: admin/views/tabs/metas/paper-content/rss-content.php:33
#: admin/views/tool-bulk-editor.php:111 js/dist/new-settings.js:189
msgid "Description"
msgstr "Descrizione"

#: admin/pages/tools.php:30
msgid "File editor"
msgstr "Modifica file"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:98
#: js/dist/new-settings.js:91
msgid "How to insert breadcrumbs in your theme"
msgstr "Come inserire i breadcrumbs nel tuo tema"

#: admin/views/tabs/tool/import-seo.php:82
msgid "Import"
msgstr "Importa"

#: admin/pages/tools.php:24
msgid "Import and Export"
msgstr "Importa ed esporta"

#: admin/views/tabs/tool/import-seo.php:18
#: admin/views/tabs/tool/import-seo.php:49
#: admin/views/tool-import-export.php:91
msgid "Import from other SEO plugins"
msgstr "Importa da altri plugin SEO"

#: admin/views/user-profile.php:28
msgid "Meta description to use for Author page"
msgstr "Meta descrizione da usare per la pagina autore "

#. translators: 1: current page number, 2: total number of pages.
#: inc/class-wpseo-replace-vars.php:1030
msgid "Page %1$d of %2$d"
msgstr "Pagina %1$d di %2$d"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:15
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for Archive breadcrumbs"
msgstr "Prefisso per gli archivi dei Breadcrumbs"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:16
msgid "Prefix for Search Page breadcrumbs"
msgstr "Prefisso nel breadcrumbs delle pagina di ricerca"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:14
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Prefix for the breadcrumb path"
msgstr "Prefisso per il percorso dei Breadcrumbs"

#: inc/class-wpseo-replace-vars.php:1523
msgid "Replaced with a custom taxonomies description"
msgstr "Sostituito con la descrizione di una tassonomia custom"

#: inc/class-wpseo-replace-vars.php:1521
msgid "Replaced with a posts custom field value"
msgstr "Sostituita con un valore di un campo personalizzato"

#: inc/class-wpseo-replace-vars.php:1522
msgid "Replaced with a posts custom taxonomies, comma separated."
msgstr "Sostituito con una tassonomia personalizzata per gli articoli, separata da virgole."

#: inc/class-wpseo-replace-vars.php:1478
msgid "Replaced with the category description"
msgstr "Sostituito dalla descrizione della categoria"

#: inc/class-wpseo-replace-vars.php:1485
msgid "Replaced with the current date"
msgstr "Sostituito con la data corrente"

#: inc/class-wpseo-replace-vars.php:1488
msgid "Replaced with the current day"
msgstr "Sostituito con la giornata in corso"

#: inc/class-wpseo-replace-vars.php:1487
msgid "Replaced with the current month"
msgstr "Sostituito con il mese corrente"

#: inc/class-wpseo-replace-vars.php:1517
msgid "Replaced with the current page number"
msgstr "Sostituito con il numero di pagina corrente"

#: inc/class-wpseo-replace-vars.php:1515
msgid "Replaced with the current page number with context (i.e. page 2 of 4)"
msgstr "Sostituito con il numero di pagina corrente e relativo contesto(es. pagina 2 di 4)"

#: inc/class-wpseo-replace-vars.php:1516
msgid "Replaced with the current page total"
msgstr "Sostituito con il numero di pagine totale"

#: inc/class-wpseo-replace-vars.php:1482
msgid "Replaced with the current search phrase"
msgstr "Sostituito con la frase di ricerca corrente"

#: inc/class-wpseo-replace-vars.php:1475
msgid "Replaced with the current tag/tags"
msgstr "Rimpiazzato dagli attuali tag"

#: inc/class-wpseo-replace-vars.php:1486
msgid "Replaced with the current year"
msgstr "Sostituito con l'anno in corso"

#: inc/class-wpseo-replace-vars.php:1467
msgid "Replaced with the date of the post/page"
msgstr "Rimpiazzato dalla data articolo/pagina"

#: inc/class-wpseo-replace-vars.php:1476
msgid "Replaced with the post categories (comma separated)"
msgstr "Sostituito dalle categorie dell'articolo (separate da una virgola)"

#: inc/class-wpseo-replace-vars.php:1510
msgid "Replaced with the content type plural label"
msgstr "Sostituito con l'etichetta plurima per il tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1509
msgid "Replaced with the content type single label"
msgstr "Sostituito con l'etichetta singola del tipo di articolo"

#: inc/class-wpseo-replace-vars.php:1512
msgid "Replaced with the post/page ID"
msgstr "Sostituito con l'ID del post/pagina"

#: inc/class-wpseo-replace-vars.php:1513
msgid "Replaced with the post/page author's 'nicename'"
msgstr "Sostituito con il 'nicename' dell'autore del post/pagina"

#: inc/class-wpseo-replace-vars.php:1473
msgid "Replaced with the post/page excerpt (or auto-generated if it does not exist)"
msgstr "Sostituito dal riassunto articolo/pagina (autogenerato se non esiste)"

#: inc/class-wpseo-replace-vars.php:1474
msgid "Replaced with the post/page excerpt (without auto-generation)"
msgstr "Sostituito dal riassunto articolo/pagina (senza autogenerazione)"

#: inc/class-wpseo-replace-vars.php:1511
msgid "Replaced with the post/page modified time"
msgstr "Sostituito con l'ora di modifica del post/pagina "

#: inc/class-wpseo-replace-vars.php:1479
msgid "Replaced with the tag description"
msgstr "Sostituito dalla descrizione del tag"

#: inc/class-wpseo-replace-vars.php:1480
msgid "Replaced with the term description"
msgstr "Sostituito dalla descrizione del termine"

#: inc/class-wpseo-replace-vars.php:1481
msgid "Replaced with the term name"
msgstr "Sostituito dal nome del termine"

#: inc/class-wpseo-replace-vars.php:1469
msgid "Replaced with the title of the parent page of the current page"
msgstr "Sostituito con il titolo della pagina padre della pagina corrente"

#: inc/class-wpseo-replace-vars.php:1468
msgid "Replaced with the title of the post/page"
msgstr "Sostituito dal titolo del post/pagina"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:12
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Separator between breadcrumbs"
msgstr "Separatore tra i breadcrumbs"

#: inc/class-wpseo-replace-vars.php:1471
msgid "The site's name"
msgstr "Il nome del sito"

#: admin/views/tabs/social/facebook.php:54
msgid "This image is used if the post/page being shared does not contain any images."
msgstr "Questa immagine viene utilizzata se un/a articolo/pagina che vengono condivisi non contengono alcuna immagine."

#: admin/pages/tools.php:37
msgid "This tool allows you to quickly change titles and descriptions of your posts and pages without having to go into the editor for each page."
msgstr "Questo strumento permette di cambiare rapidamente i titoli e le descrizioni dei tuoi post e pagine senza dover andare nell'editor per ognuno di essi."

#: admin/views/user-profile.php:24
msgid "Title to use for Author page"
msgstr "Titolo da usare per la pagina autore"

#: admin/views/tabs/metas/paper-content/rss-content.php:20
#: js/dist/new-settings.js:189
msgid "You can use the following variables within the content, they will be replaced by the value on the right."
msgstr "È possibile utilizzare le seguenti variabili all'interno del contenuto, verranno sostituite dal valore mostrato qui a destra."

#: inc/class-wpseo-replace-vars.php:122
msgid "You cannot overrule a WPSEO standard variable replacement by registering a variable with the same name. Use the \"wpseo_replacements\" filter instead to adjust the replacement value."
msgstr "Non puoi sovrascrivere una variabile di sostituzione standard di WPSEO registrando una variabile con lo stesso nome. Utilizza piuttosto il filtro \"wpseo_replacements\" per aggiustare il valore di sostituzione."

#: src/integrations/admin/import-integration.php:117
msgid "Default settings"
msgstr "Impostazioni standard"

#: admin/views/tabs/metas/paper-content/rss-content.php:39
#: js/dist/new-settings.js:189
msgid "A link to the archive for the post author, with the authors name as anchor text."
msgstr "Un link all'archivio dell'autore dell'articolo, con il nome dell'autore come testo di ancoraggio."

#: inc/class-wpseo-replace-vars.php:1514
msgid "Replaced with the post/page author's 'Biographical Info'"
msgstr "Rimpiazzato con \"Informazioni biografiche\" dell'autore del post/pagina."

#. translators: %1$s expands to Yoast SEO
#: admin/views/user-profile.php:11
msgid "%1$s settings"
msgstr "Impostazioni %1$s"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/tool/wpseo-export.php:15
msgid "Export your %1$s settings"
msgstr "Esporta le tue impostazioni %1$s"

#: admin/views/tabs/social/twitterbox.php:24
msgid "Add Twitter card meta data"
msgstr "Aggiungi i metadati della Twitter card"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:13
#: js/dist/new-settings.js:91 js/dist/new-settings.js:224
msgid "Anchor text for the Homepage"
msgstr "Testo del link per la Home Page"

#. translators: %1$s / %2$s: links to the breadcrumbs implementation page on
#. the Yoast knowledgebase
#. translators: %1$s and %2$s are replaced by opening and closing <a> tags.
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:103
#: js/dist/new-settings.js:92
msgid "Usage of this breadcrumbs feature is explained in %1$sour knowledge-base article on breadcrumbs implementation%2$s."
msgstr "L'utilizzo della funzione breadcrumbs è spiegata nel %1$snostro articolo nella knowledge-base sull'implementazione dei breadcrumbs%2$s."

#: admin/views/tabs/metas/rss.php:21
msgid "This feature is used to automatically add content to your RSS, more specifically, it's meant to add links back to your blog and your blog posts, so dumb scrapers will automatically add these links too, helping search engines identify you as the original source of the content."
msgstr "Questa funzionalità è utilizzata per aggiungere automaticamente contenuti al tuo RSS. Più nello specifico, è progettata per aggiungere link che rimandino al tuo blog e ai tuoi articoli, in modo che gli scrapers aggiungeranno a loro volta automaticamente questi links, aiutando i motori di ricerca ad identificarti come la fonte originale dei contenuti."

#: inc/class-wpseo-replace-vars.php:1520
msgid "Replaced with the slug which caused the 404"
msgstr "Sostituito con lo slug che ha causato l'errore 404"

#: inc/class-wpseo-replace-vars.php:118
msgid "A replacement variable with the same name has already been registered. Try making your variable name unique."
msgstr "Una variabile di sostituzione con questo nome è già stata registrata. Prova a rendere il nome di questa variabile ancora più univoco."

#: admin/statistics/class-statistics-service.php:80
msgid "Below are your published posts' SEO scores. Now is as good a time as any to start improving some of your posts!"
msgstr "Di seguito sono elencati i punteggi SEO dei tuoi articoli pubblicati. Ora è il momento giusto per iniziare a migliorare alcuni dei tuoi post!"

#: admin/class-export.php:61 admin/views/tabs/tool/wpseo-import.php:25
#: admin/views/tabs/tool/wpseo-import.php:45
#: admin/views/tool-import-export.php:85
msgid "Import settings"
msgstr "Importa impostazioni"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:153 admin/views/tool-file-editor.php:203
msgid "If your %s were writable, you could edit it from here."
msgstr "Se il tuo %s fosse modificabile, potresti modificarlo da qui."

#. translators: %s expands to robots.txt.
#: admin/views/tool-file-editor.php:116
msgid "You don't have a %s file, create one here:"
msgstr "Non hai un file %s, creane uno qui:"

#. translators: %s expands to robots.txt.
#. translators: %s expands to ".htaccess".
#: admin/views/tool-file-editor.php:135 admin/views/tool-file-editor.php:235
msgid "If you had a %s file and it was editable, you could edit it from here."
msgstr "Se tu avessi un file %s e questo fosse modificabile, potresti modificarlo da qui."

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:73
msgid "Content type archive to show in breadcrumbs for taxonomies"
msgstr "Archivio dei post type da mostrare nei breadcrumbs per le tassonomie"

#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:43
msgid "Taxonomy to show in breadcrumbs for content types"
msgstr "Tassonomia da mostrare nei breadcrumbs per i post type"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Replaced with the posts focus keyphrase"
msgstr "Sostituito dalla frase chiave principale dell'articolo"

#: admin/views/tabs/social/pinterest.php:20
msgid "Pinterest uses Open Graph metadata just like Facebook, so be sure to keep the \"Add Open Graph meta data\" setting on the Facebook tab enabled if you want to optimize your site for Pinterest."
msgstr "Pinterest usa i metadata Open Graph come Facebook, perciò assicurati di aver selezionato le impostazioni \"Aggiungi i dati meta di Open Graph\" nella scheda Facebook se vuoi ottimizzare il tuo sito per Pinterest."

#: admin/pages/tools.php:25
msgid "Import settings from other SEO plugins and export your settings for re-use on (another) site."
msgstr "Importa le impostazioni da altri plugin SEO ed esporta le tue impostazione per riutilizzarle su un altro sito."

#: admin/pages/tools.php:31
msgid "This tool allows you to quickly change important files for your SEO, like your robots.txt and, if you have one, your .htaccess file."
msgstr "Questo strumento consente di modificare rapidamente file importanti per la SEO, come il vostro robots.txt e, se ne avete uno, il file .htaccess."

#: inc/options/class-wpseo-option-titles.php:250
msgid "Page not found"
msgstr "Pagina non trovata"

#. Translators: %1$s resolves to the SEO menu item, %2$s resolves to the Search
#. Appearance submenu item.
#: src/presenters/meta-description-presenter.php:35
msgid "Admin only notice: this page does not show a meta description because it does not have one, either write it for this page specifically or go into the [%1$s - %2$s] menu and set up a template."
msgstr "Avviso per l'amministratore: questa pagina non mostra una meta descrizione perché non ne ha una, è necessario scriverla per questa pagina o andare nel menu  [%1$s - %2$s] e impostare un template."

#: admin/views/tabs/metas/paper-content/author-archive-settings.php:39
msgid "author archives"
msgstr "Archivi autori"

#: admin/views/tabs/metas/archives.php:24
#: admin/views/tabs/metas/paper-content/date-archives-settings.php:16
#: js/dist/new-settings.js:127 js/dist/new-settings.js:228
#: js/dist/new-settings.js:235
msgid "Date archives"
msgstr "Archivi per data"

#. Translators: %1$s: expands to 'Yoast SEO Premium', %2$s: links to Yoast SEO
#. Premium plugin page.
#: admin/google_search_console/views/gsc-redirect-nopremium.php:22
msgid "To be able to create a redirect and fix this issue, you need %1$s. You can buy the plugin, including one year of support and updates, on %2$s."
msgstr "Per essere in grado di creare un redirect e risolvere questo problema, hai bisogno di %1$s. È possibile acquistare il plugin, compreso un anno di assistenza e aggiornamenti, su %2$s."

#. translators: %s expands to the name of a site within a multisite network.
#: admin/class-yoast-network-admin.php:164
msgid "%s restored to default SEO settings."
msgstr "%s ripristinato ai valori di default SEO."

#: admin/views/tabs/metas/paper-content/special-pages.php:26
#: js/dist/new-settings.js:228
msgid "404 pages"
msgstr "Pagine 404"

#: admin/pages/social.php:18
msgid "Accounts"
msgstr "Account"

#: admin/class-plugin-availability.php:60 admin/views/licenses.php:59
msgid "Are you in Google News? Increase your traffic from Google News by optimizing for it!"
msgstr "Sei su Google News? Aumenta il tuo traffico da Google News ottimizzandoti per esso!"

#: vendor_prefixed/wordproof/wordpress-sdk/app/Translations/DefaultTranslations.php:43
msgid "Authenticate"
msgstr "Autenticati"

#: admin/views/tabs/network/general.php:40
msgid "Choose the site whose settings you want to use as default for all sites that are added to your network. If you choose 'None', the normal plugin defaults will be used."
msgstr "Scegli il sito le cui impostazioni verranno utilizzate come standard per tutti i siti che verranno aggiunti al network. Se scegli 'Nessuno', verranno utilizzati i valori standard del plugin."

#. Translators: %s: expands to Yoast SEO Premium
#: admin/google_search_console/views/gsc-redirect-nopremium.php:15
msgid "Creating redirects is a %s feature"
msgstr "Creare redirect è una caratteristica di %s"

#: admin/views/tabs/metas/archives/help.php:21
msgid "Date-based archives could in some cases also be seen as duplicate content."
msgstr "Gli archivi per data potrebbero in alcuni casi essere rilevati come contenuti duplicati."

#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:10
msgid "Force rewrite titles"
msgstr "Forza la riscrittura dei titoli"

#: admin/views/tabs/metas/general.php:47 js/dist/new-settings.js:172
#: js/dist/new-settings.js:224 js/dist/new-settings.js:235
msgid "Homepage"
msgstr "Homepage"

#: admin/views/tabs/network/general.php:37
#: admin/views/tabs/network/general.php:43
msgid "New sites in the network inherit their SEO settings from this site"
msgstr "I nuovi siti del network erediteranno le impostazioni SEO da questo sito"

#: admin/class-plugin-availability.php:50 admin/views/licenses.php:46
msgid "Optimize your videos to show them off in search results and get more clicks!"
msgstr "Ottimizza i tuoi video per visualizzarli nei risultati di ricerca ed avere più click!"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:64
#: src/integrations/admin/first-time-configuration-integration.php:491
#: js/dist/new-settings.js:230
msgid "Person"
msgstr "Persona"

#: admin/pages/metas.php:24 js/dist/new-settings.js:189
#: js/dist/new-settings.js:235
msgid "RSS"
msgstr "RSS"

#: admin/class-plugin-availability.php:70 admin/views/licenses.php:32
msgid "Rank better locally and in Google Maps, without breaking a sweat!"
msgstr "Migliora il posizionamento locale e su Google Maps, senza fare nessuno sforzo!"

#: admin/views/tabs/network/restore-site.php:32
msgid "Restore site to defaults"
msgstr "Reimposta il sito"

#: admin/class-yoast-network-admin.php:130
msgid "Settings Updated."
msgstr "Impostazioni aggiornate."

#: admin/views/tabs/network/general.php:27
msgid "Site Admins (default)"
msgstr "Amministratori del sito (predefinito)"

#: admin/views/tabs/network/restore-site.php:23
#: admin/views/tabs/network/restore-site.php:28
msgid "Site ID"
msgstr "ID Sito"

#: admin/views/tabs/network/general.php:28
msgid "Super Admins only"
msgstr "Solo super amministratori"

#: admin/views/tabs/network/general.php:54
msgid "Take note:"
msgstr "Prendi nota:"

#: admin/pages/metas.php:21
msgid "Taxonomies"
msgstr "Tassonomie"

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:38 admin/views/licenses.php:22
msgid "The premium version of %1$s with more features & support."
msgstr "La versione premium di  %1$s con molte altre funzionalità e l'assistenza."

#. translators: %s expands to <code>noindex, follow</code>.
#: admin/views/tabs/metas/paper-content/special-pages.php:15
msgid "These pages will be %s by default, so they will never show up in search results."
msgstr "Queste pagine saranno %s di default, quindi non appariranno mai nei risultati delle ricerche."

#: admin/views/tool-bulk-editor.php:109 inc/class-wpseo-replace-vars.php:1468
#: js/dist/externals-redux.js:1
msgid "Title"
msgstr "Titolo"

#: admin/views/tabs/metas/general.php:31
msgid "Title Separator"
msgstr "Separatore del titolo"

#: admin/views/redirects.php:112
msgid "URL"
msgstr "URL"

#: admin/views/tabs/network/restore-site.php:16
msgid "Using this form you can reset a site to the default SEO settings."
msgstr "Usando questo modulo sarà possibile impostare il sito con i valori predefiniti di SEO."

#: admin/pages/dashboard.php:56
msgid "Webmaster Tools"
msgstr "Strumenti per Webmaster"

#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:14
msgid "Website name"
msgstr "Nome sito web"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/network/general.php:25
msgid "Who should have access to the %1$s settings"
msgstr "Chi dovrebbe avere accesso alle impostazioni di %1$s"

#: admin/class-yoast-network-admin.php:78
msgid "archived"
msgstr "archiviato"

#: admin/class-yoast-network-admin.php:79
msgid "mature"
msgstr "maturo"

#: admin/class-yoast-network-admin.php:77
msgid "public"
msgstr "pubblico"

#: admin/class-yoast-network-admin.php:80
msgid "spam"
msgstr "spam"

#. translators: %s expands to <code>noindex, follow</code>
#: admin/views/tabs/metas/archives/help.php:17
msgid "If this is the case on your site, you can choose to either disable it (which makes it redirect to the homepage), or to add %s to it so it doesn't show up in the search results."
msgstr "Se questo è il caso sul tuo sito, è possibile disabilitarlo  (questo lo reindirizza alla home page), o aggiungergli %s in modo da non mostrarlo nei risultati della ricerca."

#. translators: %1$s / %2$s: links to an article about duplicate content on
#. yoast.com
#: admin/views/tabs/metas/archives/help.php:11
msgid "If you're running a one author blog, the author archive will be exactly the same as your homepage. This is what's called a %1$sduplicate content problem%2$s."
msgstr "Se il tuo blog è curato solo da un autore l'archivio autori sarà esattamente uguale alla tua homepage. Questo è ciò che si intende quando si parla di %1$scontenuti duplicati%2$s."

#. translators: %1$s expands to Yoast SEO.
#: admin/views/licenses.php:94
msgid "%1$s Extensions"
msgstr "Estensioni %1$s"

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/network/general.php:47
msgid "Enter the %1$sSite ID%2$s for the site whose settings you want to use as default for all sites that are added to your network. Leave empty for none (i.e. the normal plugin defaults will be used)."
msgstr "Inserisci lo %1$sID del sito%2$s per il sito dal quale ricavare le impostazioni che desideri utilizzare come predefinite per tutti i siti già aggiunti al tuo network. Lascia vuoto per nessun sito (verranno utilizzati i valori predefiniti del plugin)."

#. translators: %1$s expands to Yoast SEO
#: admin/class-plugin-availability.php:82 admin/views/licenses.php:77
msgid "Seamlessly integrate WooCommerce with %1$s and get extra features!"
msgstr "Integra al meglio WooCommerce con %1$s ed ottieni funzionalità extra!"

#: admin/views/tabs/metas/paper-content/special-pages.php:21
#: js/dist/new-settings.js:228
msgid "Search pages"
msgstr "Pagine di ricerca"

#. translators: %1$s opens the link to the Yoast.com article about Google's
#. Knowledge Graph, %2$s closes the link,
#: admin/views/tabs/metas/paper-content/general/knowledge-graph.php:29
msgid "This data is shown as metadata in your site. It is intended to appear in %1$sGoogle's Knowledge Graph%2$s. You can be either an organization, or a person."
msgstr "Questo dato viene mostrato come dato meta del tuo sito. &Egrave; destinato a comparire nel %1$sKnowledge Graph di Google%2$s. Puoi scegliere se essere una Organizzazione o una Persona."

#: admin/views/tabs/network/general.php:54
msgid "Privacy sensitive (FB admins and such), theme specific (title rewrite) and a few very site specific settings will not be imported to new sites."
msgstr "I dati sensibili della privacy (amministratori FB e simili), specifici per il tema (riscrittura titolo) ed alcune altre impostazioni specifiche non verranno importate nei nuovi siti."

#. translators: 1: link open tag; 2: link close tag.
#. translators: %1$s expands to an opening tag. %2$s expands to a closing tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:39
#: js/dist/new-settings.js:170
msgid "You can determine the title and description for the homepage by %1$sediting the homepage itself%2$s."
msgstr "Puoi definire il titolo e la descrizione della pagina home %1$smodificando direttamente da qui la pagina%2$s."

#. translators: 1: link open tag; 2: link close tag.
#: admin/views/tabs/metas/paper-content/general/homepage.php:48
msgid "You can determine the title and description for the posts page by %1$sediting the posts page itself%2$s."
msgstr "Puoi impostare il titolo e la descrizione per la pagina degli articoli %1$smodificando direttamente da qui a pagina%2$s."

#: admin/views/tabs/metas/general.php:50
msgid "Homepage &amp; Posts page"
msgstr "Homepage & Pagina degli articoli"

#: admin/views/tabs/metas/archives.php:29 js/dist/new-settings.js:235
msgid "Special pages"
msgstr "Pagine speciali"

#: inc/class-wpseo-replace-vars.php:1494
msgid "Permalink"
msgstr "Permalink"

#: admin/views/redirects.php:175 admin/views/redirects.php:222
msgid "New URL"
msgstr "Nuova URL"

#: admin/pages/metas.php:23 js/dist/new-settings.js:91
#: js/dist/new-settings.js:224 js/dist/new-settings.js:226
#: js/dist/new-settings.js:235
msgid "Breadcrumbs"
msgstr "Breadcrumb"

#. translators: %1$s expands to Yoast SEO
#: admin/views/tabs/metas/paper-content/general/force-rewrite-title.php:14
msgid "%1$s has auto-detected whether it needs to force rewrite the titles for your pages, if you think it's wrong and you know what you're doing, you can change the setting here."
msgstr "%1$s determina in modo automatico quando è necessario forzare la riscrittura dei titoli delle pagine, se pensi sia sbagliato e sai quello che stai facendo puoi cambiare le impostazioni da qui."

#: admin/views/tabs/metas/paper-content/front-page-content.php:26
#: src/integrations/admin/social-templates-integration.php:188
msgid "Social settings"
msgstr "Impostazioni Social"

#. translators: %1$s: 'Facebook & Open Graph' plugin name(s) of possibly
#. conflicting plugin(s), %2$s to Yoast SEO
#: admin/class-yoast-plugin-conflict.php:210
msgid "The %1$s plugin might cause issues when used in conjunction with %2$s."
msgstr "I plugin %1$s potrebbero causare problemi quando utilizzati in combinazione con %2$s."

#. translators: %s is the plugin name
#: admin/class-yoast-dashboard-widget.php:75
msgid "%s Posts Overview"
msgstr "Panoramica degli articoli %s"

#: admin/class-meta-columns.php:239
msgid "All SEO Scores"
msgstr "Tutti i punteggi SEO"

#: admin/pages/metas.php:22
msgid "Archives"
msgstr "Archivi"

#: admin/google_search_console/views/gsc-redirect-nopremium.php:27
#: js/dist/externals-components.js:88 js/dist/externals/componentsNew.js:136
msgid "Close"
msgstr "Chiudi"

#. translators: %s: 'Facebook' plugin name of possibly conflicting plugin
#: admin/class-yoast-plugin-conflict.php:214
msgid "Deactivate %s"
msgstr "Disattivare %s"

#: admin/pages/social.php:19 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Facebook"
msgstr "Facebook"

#: admin/class-meta-columns.php:108
msgid "Meta Desc."
msgstr "Desc. meta"

#: admin/formatter/class-metabox-formatter.php:127
#: admin/formatter/class-metabox-formatter.php:157
#: admin/statistics/class-statistics-service.php:222
#: inc/class-wpseo-rank.php:141 inc/class-wpseo-rank.php:168
#: inc/class-wpseo-rank.php:200 js/dist/block-editor.js:227
#: js/dist/editor-modules.js:227 js/dist/externals-components.js:120
#: js/dist/externals/analysis.js:383 js/dist/frontend-inspector-resources.js:1
#: js/dist/post-edit.js:11 js/dist/term-edit.js:1
msgid "OK"
msgstr "OK"

#: admin/pages/social.php:21 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:235
msgid "Pinterest"
msgstr "Pinterest"

#: admin/pages/social.php:20 js/dist/block-editor.js:468
#: js/dist/first-time-configuration.js:1 js/dist/new-settings.js:228
#: js/dist/new-settings.js:229 js/dist/new-settings.js:232
#: js/dist/settings.js:18
msgid "Twitter"
msgstr "Twitter"

#: admin/class-yoast-form.php:741 admin/metabox/class-metabox.php:649
#: admin/taxonomy/class-taxonomy-fields-presenter.php:127
msgid "Upload Image"
msgstr "Carica immagine"

#: admin/metabox/class-metabox.php:927
#: src/integrations/third-party/elementor.php:443
msgid "SEO issue: The featured image should be at least 200 by 200 pixels to be picked up by Facebook and other social media sites."
msgstr "Problema SEO: l'immagine in evidenza deve avere una dimensione di almeno 200 per 200 pixel per poter essere utilizzata da Facebook e altri social media."

#: admin/views/tabs/metas/paper-content/post-type-content.php:81
#: js/dist/new-settings.js:45
msgid "Breadcrumbs title"
msgstr "Titolo dei breadcrumbs"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Google XML Sitemaps' plugin
#. name of possibly conflicting plugin with regard to the creation of sitemaps.
#: admin/class-plugin-conflict.php:76
msgid "Both %1$s and %2$s can create XML sitemaps. Having two XML sitemaps is not beneficial for search engines and might slow down your site."
msgstr "Sia %1$s che %2$s generano una mappa del sito XML. Avere due file XML non porta benefici per i motori di ricerca e potrebbe rallentare il sito."

#. translators: %1$s expands to Yoast SEO.
#: admin/class-plugin-conflict.php:69
msgid "Configure %1$s's Open Graph settings"
msgstr "Configura le impostazioni OpenGraph di %1$s"

#. translators: %1$s expands to Yoast SEO, %2$s: 'Facebook' plugin name of
#. possibly conflicting plugin with regard to creating OpenGraph output.
#: admin/class-plugin-conflict.php:65
msgid "Both %1$s and %2$s create Open Graph output, which might make Facebook, Twitter, LinkedIn and other social networks use the wrong texts and images when your pages are being shared."
msgstr "Sia %1$s sia %2$s generano dei meta tag OpenGraph, che potrebbero causare immagini e testi sbagliati qualora fossero condivisi su Facebook, Twitter, LinkedIn ed altri social."

#: admin/class-admin-init.php:386
#: src/services/health-check/default-tagline-runner.php:31
msgid "Just another WordPress site"
msgstr "Solo un altro sito WordPress"

#. translators: %s expands to the number of trashed posts in localized format.
#: admin/class-bulk-editor-list-table.php:337
msgctxt "posts"
msgid "Trash <span class=\"count\">(%s)</span>"
msgid_plural "Trash <span class=\"count\">(%s)</span>"
msgstr[0] "Cestinato <span class=\"count\">(%s)</span>"
msgstr[1] "Cestinati <span class=\"count\">(%s)</span>"

#: admin/metabox/class-metabox.php:191 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Meta robots advanced"
msgstr "Meta Robots avanzate"

#. translators: %1$s expands to Yoast SEO, %2$s expands to the installed
#. version, %3$s expands to Gutenberg
#: admin/class-admin-gutenberg-compatibility-notification.php:88
msgid "%1$s detected you are using version %2$s of %3$s, please update to the latest version to prevent compatibility issues."
msgstr "%1$s ha scoperto che stai utilizzando la versione %2$s di %3$s, aggiorna all'ultima versione per evitare problemi di compatibilità."

#. translators: %s is the name of the plugin
#: admin/class-customizer.php:86
msgid "%s Breadcrumbs"
msgstr "%s Breadcrumb"

#: admin/metabox/class-metabox.php:882 admin/taxonomy/class-taxonomy.php:151
#: admin/taxonomy/class-taxonomy.php:273
#: src/integrations/third-party/elementor.php:398
msgid "(no parent)"
msgstr "(nessun genitore)"

#: admin/metabox/class-metabox.php:215
msgid "301 Redirect"
msgstr "Redirect 301"

#: admin/class-bulk-editor-list-table.php:1001
msgid "Action"
msgstr "Azione"

#: admin/class-customizer.php:145
msgid "Anchor text for the homepage:"
msgstr "Anchor text per la homepage:"

#: admin/class-customizer.php:193
msgid "Breadcrumb for 404 pages:"
msgstr "Breadcrumb per le pagine 404:"

#: admin/class-customizer.php:132
msgid "Breadcrumbs separator:"
msgstr "Separatore dei Breadcrumb:"

#: admin/class-bulk-editor-list-table.php:804
#: js/dist/first-time-configuration.js:13
#: js/dist/first-time-configuration.js:225 js/dist/indexables-page.js:34
#: js/dist/indexables-page.js:35 js/dist/indexables-page.js:46
#: js/dist/installation-success.js:13
msgid "Edit"
msgstr "Modifica"

#: admin/menu/class-network-admin-menu.php:63
msgid "Edit Files"
msgstr "Modifica Files"

#: admin/class-bulk-description-editor-list-table.php:46
msgid "Existing Yoast Meta Description"
msgstr "Meta descrizione Yoast esistente"

#: admin/menu/class-network-admin-menu.php:66
msgid "Extensions"
msgstr "Estensioni"

#: admin/class-admin.php:227 js/dist/structured-data-blocks.js:13
msgid "FAQ"
msgstr "FAQ"

#: admin/class-admin.php:288
msgid "Facebook profile URL"
msgstr "URL profilo Facebook"

#: admin/class-bulk-editor-list-table.php:415 admin/views/redirects.php:141
msgid "Filter"
msgstr "Filtro"

#: admin/menu/class-admin-menu.php:89
#: admin/menu/class-network-admin-menu.php:56 admin/pages/metas.php:18
#: admin/pages/network.php:18 js/dist/new-settings.js:235
msgid "General"
msgstr "Generale"

#: src/presenters/admin/search-engines-discouraged-presenter.php:33
msgid "Huge SEO Issue: You're blocking access to robots."
msgstr "Grande problema SEO: stai bloccando l'accesso ai robot."

#: admin/metabox/class-metabox.php:174 js/dist/externals/analysis.js:383
#: js/dist/externals/replacementVariableEditor.js:78
#: js/dist/externals/searchMetadataPreviews.js:243 js/dist/new-settings.js:29
#: js/dist/new-settings.js:42 js/dist/new-settings.js:68
#: js/dist/new-settings.js:88 js/dist/new-settings.js:144
#: js/dist/new-settings.js:158 js/dist/new-settings.js:167
#: js/dist/new-settings.js:188 js/dist/new-settings.js:219
#: js/dist/new-settings.js:221 js/dist/new-settings.js:224
#: js/dist/new-settings.js:228
msgid "Meta description"
msgstr "Meta descrizione"

#: admin/class-bulk-description-editor-list-table.php:47
msgid "New Yoast Meta Description"
msgstr "Nuova meta descrizione Yoast"

#: admin/class-yoast-network-admin.php:43
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:47
#: admin/views/tabs/metas/paper-content/breadcrumbs-content.php:75
#: src/config/schema-types.php:163 js/dist/new-settings.js:239
msgid "None"
msgstr "Nessuno"

#: admin/class-bulk-editor-list-table.php:994
msgid "Post Status"
msgstr "Stato articolo"

#: admin/ajax.php:133
msgid "Post doesn't exist."
msgstr "L'articolo non esiste."

#. translators: %s expands to post type.
#: admin/ajax.php:144
msgid "Post has an invalid Content Type: %s."
msgstr "L'articolo ha impostato un tipo invalido: %s."

#: admin/class-meta-columns.php:673
msgid "Post is set to noindex."
msgstr "Articolo impostato come da non indicizzare"

#: admin/class-admin.php:182
msgid "Posts"
msgstr "Articoli"

#: admin/class-customizer.php:169
msgid "Prefix for archive pages:"
msgstr "Prefisso per le pagine archivio:"

#: admin/class-customizer.php:157
msgid "Prefix for breadcrumbs:"
msgstr "Prefisso per i breadcrumb:"

#: admin/class-customizer.php:181
msgid "Prefix for search result pages:"
msgstr "Prefisso per le pagine dei risultati della ricerca:"

#: admin/class-bulk-editor-list-table.php:816
msgid "Preview"
msgstr "Anteprima"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:815
msgid "Preview &#8220;%s&#8221;"
msgstr "Anteprima &#8220;%s&#8221;"

#: admin/class-bulk-editor-list-table.php:995
msgid "Publication date"
msgstr "Data di pubblicazione"

#: admin/metabox/class-metabox.php:418
#: admin/taxonomy/class-taxonomy-metabox.php:132
#: inc/class-wpseo-admin-bar-menu.php:508
#: src/presenters/meta-description-presenter.php:36
#: src/services/health-check/report-builder.php:168
#: js/dist/structured-data-blocks.js:13
msgid "SEO"
msgstr "SEO"

#: admin/class-meta-columns.php:107
msgid "SEO Title"
msgstr "Titolo SEO"

#: admin/menu/class-admin-menu.php:92
msgid "Search Console"
msgstr "Console di ricerca"

#: admin/class-admin.php:222 admin/views/redirects.php:42
#: src/integrations/settings-integration.php:261
#: js/dist/structured-data-blocks.js:9
msgid "Settings"
msgstr "Impostazioni"

#: admin/import/class-import-settings.php:79
msgid "Settings could not be imported:"
msgstr "Le impostazioni non possono essere importate:"

#: admin/import/class-import-settings.php:111
msgid "Settings successfully imported."
msgstr "Impostazioni importate con successo."

#: admin/menu/class-admin-menu.php:96 admin/metabox/class-metabox.php:439
#: admin/taxonomy/class-taxonomy-metabox.php:146
msgid "Social"
msgstr "Social"

#: admin/metabox/class-metabox.php:198
msgid "Title to use for this page in breadcrumb paths"
msgstr "Titolo da usare nel percorso del breadcrumb in questa pagina"

#: admin/menu/class-admin-menu.php:97 js/dist/new-settings.js:217
msgid "Tools"
msgstr "Strumenti"

#: admin/class-admin.php:295
msgid "Twitter username (without @)"
msgstr "Username Twitter (senza @)"

#: admin/class-config.php:135 admin/metabox/class-metabox.php:907
#: admin/taxonomy/class-taxonomy.php:166
#: src/integrations/third-party/elementor.php:424
msgid "Use Image"
msgstr "Usa immagine"

#: admin/class-bulk-editor-list-table.php:992
msgid "WP Page Title"
msgstr "Titolo pagina WP"

#: admin/metabox/class-metabox.php:179
msgid "Warning: even though you can set the meta robots setting here, the entire site is set to noindex in the sitewide privacy settings, so these settings won't have an effect."
msgstr "Attenzione: anche se da qui puoi impostare i valori meta per le impostazioni per i robot, l'intero sito è impostato come da non indicizzare nelle impostazioni generali di privacy, quindi queste impostazioni non avranno alcun effetto."

#. translators: %s expands to the name of a post type (plural).
#: admin/ajax.php:168
msgid "You can't edit %s that aren't yours."
msgstr "Non puoi modificare i %s che non sono tuoi."

#. translators: %s expands to post type name.
#: admin/ajax.php:156
msgid "You can't edit %s."
msgstr "Non puoi modificare %s."

#: admin/ajax.php:177
msgid "You have used HTML in your value which is not allowed."
msgstr "Non è consentito utilizzare HTML."

#: admin/metabox/class-metabox.php:193 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Image Index"
msgstr "Nessuna immagine indice"

#: admin/metabox/class-metabox.php:194 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Archive"
msgstr "Nessun Archivio"

#: admin/class-bulk-editor-list-table.php:826 js/dist/block-editor.js:311
#: js/dist/classic-editor.js:307 js/dist/dashboard-widget.js:1
#: js/dist/dashboard-widget.js:20 js/dist/editor-modules.js:261
#: js/dist/elementor.js:311
msgid "View"
msgstr "Visualizza"

#. translators: %s: post title
#: admin/class-bulk-editor-list-table.php:825
msgid "View &#8220;%s&#8221;"
msgstr "Visualizza &#8220;%s&#8221;"

#: inc/class-wpseo-replace-vars.php:1519
msgid "Focus keyword"
msgstr "Parola chiave principale"

#: admin/class-bulk-editor-list-table.php:996
msgid "Page URL/Slug"
msgstr "URL/Slug pagina"

#: admin/metabox/class-metabox.php:195 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "No Snippet"
msgstr "Nessuno snippet"

#: admin/metabox/class-metabox.php:197 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Breadcrumbs Title"
msgstr "Titolo dei breadcrumbs"

#: admin/metabox/class-metabox.php:200 js/dist/block-editor.js:410
#: js/dist/classic-editor.js:406 js/dist/elementor.js:410
msgid "Canonical URL"
msgstr "URL canonico"

#. translators: 1: link open tag; 2: link close tag.
#: admin/metabox/class-metabox.php:204
msgid "The canonical URL that this page should point to. Leave empty to default to permalink. %1$sCross domain canonical%2$s supported too."
msgstr "L'URL canonico a cui questa pagina dovrebbe puntare. Lascia in bianco per utilizzare il permalink predefinito. Sono supportate anche gli %1$sURL canonici tra domini diversi (cross domain)%2$s."

#: admin/metabox/class-metabox.php:216
msgid "The URL that this page should redirect to."
msgstr "L'URL al quale questa pagina deve reindirizzare."

#: admin/views/class-yoast-feature-toggles.php:141 js/dist/new-settings.js:217
#: js/dist/new-settings.js:224
msgid "XML sitemaps"
msgstr "Sitemaps XML"

#. translators: %s expands to the number of posts in localized format.
#: admin/class-bulk-editor-list-table.php:288
msgctxt "posts"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] "Tutto <span class=\"count\">(%s)</span>"
msgstr[1] "Tutti <span class=\"count\">(%s)</span>"