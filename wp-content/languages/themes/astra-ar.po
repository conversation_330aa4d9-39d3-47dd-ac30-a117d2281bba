# Translation of Themes - Astra in Arabic
# This file is distributed under the same license as the Themes - Astra package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-02-20 05:28:41+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ar\n"
"Project-Id-Version: Themes - Astra\n"

#. Theme Name of the theme
#: style.css admin/includes/class-astra-menu.php:162
#: inc/lib/class-astra-nps-notice.php:108
#, gp-priority: high
msgid "Astra"
msgstr "Astra"

#: admin/includes/class-astra-admin-ajax.php:333
msgid "Plugin installation function not found."
msgstr "لم يتم العثور على وظيفة تثبيت المكون الإضافي."

#: admin/includes/class-astra-admin-ajax.php:310
msgid "Plugin slug is missing."
msgstr "الاسم اللطيف للإضافة مفقود."

#: inc/blog/blog-config.php:181
msgctxt "Blogs: Author Prefix Label"
msgid "%astra%"
msgstr "%astra%"

#: inc/class-astra-global-palette.php:304
msgid "Other Supporting"
msgstr "دعم آخر"

#: inc/class-astra-global-palette.php:300
#: inc/class-astra-global-palette.php:301
msgid "Secondary Background"
msgstr "خلفية ثانوية"

#: inc/class-astra-global-palette.php:300
#: inc/class-astra-global-palette.php:301
msgid "Primary Background"
msgstr "الخلفية الأساسية"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:97
msgid "Note: This is not applicable on Transparent and Sticky Headers!"
msgstr "ملحوظة: هذا لا ينطبق على الترويسات الشفافة والثابتة!"

#: inc/core/builder/class-astra-builder-helper.php:824
msgid "Button 1"
msgstr "زر 1"

#: inc/core/builder/class-astra-builder-helper.php:819
msgid "Widget 6"
msgstr "ودجت 6"

#: inc/core/builder/class-astra-builder-helper.php:814
msgid "Widget 5"
msgstr "ودجت 5"

#: inc/core/builder/class-astra-builder-helper.php:759
#: inc/core/builder/class-astra-builder-helper.php:930
msgid "Widget 4"
msgstr "ودجت 4"

#: inc/core/builder/class-astra-builder-helper.php:753
#: inc/core/builder/class-astra-builder-helper.php:925
msgid "Widget 3"
msgstr "ودجت 3"

#: inc/core/builder/class-astra-builder-helper.php:747
#: inc/core/builder/class-astra-builder-helper.php:920
msgid "HTML 3"
msgstr "HTML 3"

#: inc/core/builder/class-astra-builder-helper.php:741
#: inc/core/builder/class-astra-builder-helper.php:829
#: inc/core/builder/class-astra-builder-helper.php:915
msgid "Button 2"
msgstr "زر 2"

#: inc/core/builder/class-astra-builder-helper.php:723
#: inc/core/builder/class-astra-builder-helper.php:910
#: inc/customizer/configurations/builder/header/configs/header-builder.php:706
msgid "Language Switcher"
msgstr "مبدل اللغة"

#: inc/lib/class-astra-nps-notice.php:118
msgid "Thank you for your feedback"
msgstr "نشكرك على ملاحظاتك"

#: inc/lib/class-astra-nps-notice.php:112
msgid "Thanks a lot for your feedback! 😍"
msgstr "شكرا جزيلا على ملاحظاتك! 😍"

#: inc/customizer/class-astra-customizer-partials.php:79
#: inc/markup-extras.php:903
msgctxt "Primary Menu: Custom Menu Text / HTML for Last Item in Menu"
msgid "%astra%"
msgstr "%astra%"

#: inc/template-parts.php:166
msgctxt "Primary Menu: Menu Label for Toggle Button"
msgid "%astra%"
msgstr "%astra%"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:217
msgctxt "Blogs: Read More Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/modules/related-posts/class-astra-related-posts-markup.php:71
msgctxt "Single Blog/Post Related Posts: Title"
msgid "%astra%"
msgstr "%astra%"

#: inc/markup-extras.php:1062
msgctxt "Footer small section 2 credit"
msgid "%astra%"
msgstr "%astra%"

#: inc/markup-extras.php:1061
msgctxt "Footer small section 1 credit"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1393
msgctxt "Builder: Footer Button 7 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1392
msgctxt "Builder: Footer Button 6 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1391
msgctxt "Builder: Footer Button 5 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1390
msgctxt "Builder: Footer Button 4 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1389
msgctxt "Builder: Footer Button 3 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1388
msgctxt "Builder: Footer Button 2 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1387
msgctxt "Builder: Footer Button 1 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1385
msgctxt "Builder: Header Button 10 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1384
msgctxt "Builder: Header Button 9 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:584
msgctxt "Header Builder: Cart Widget - Cart Label"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:407
msgctxt "WooCommerce Single Product: Shipping Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/edd/edd-common-functions.php:324
msgctxt "EDD Product Archive: Variable Product Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/edd/edd-common-functions.php:323
msgctxt "EDD Product Archive: Cart Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/markup/class-astra-builder-footer.php:226
msgctxt "Footer Builder: Copyright Editor Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/builder/controllers/class-astra-builder-ui-controller.php:460
msgctxt "Header Builder: Account Widget - Logged Out View Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1406
msgctxt "Builder: Header HTML 9"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1405
msgctxt "Builder: Header HTML 8"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1404
msgctxt "Builder: Header HTML 7"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1403
msgctxt "Builder: Header HTML 6"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1402
msgctxt "Builder: Header HTML 5"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1401
msgctxt "Builder: Header HTML 4"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1383
msgctxt "Builder: Header Button 8 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1382
msgctxt "Builder: Header Button 7 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1381
msgctxt "Builder: Header Button 6 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1380
msgctxt "Builder: Header Button 5 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1379
msgctxt "Builder: Header Button 4 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1378
msgctxt "Builder: Header Button 3 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1377
msgctxt "Builder: Header Button 2 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/core/builder/class-astra-builder-helper.php:1376
msgctxt "Builder: Header Button 1 Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3651
msgctxt "WooCommerce Single Product: Payments - Payment Title."
msgid "%astra%"
msgstr "%astra%"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3546
msgctxt "WooCommerce Cart: Cart Button Text"
msgid "%astra%"
msgstr "%astra%"

#: inc/customizer/class-astra-customizer.php:1637
msgid "Extra"
msgstr "إضافي"

#: inc/compatibility/surecart/class-astra-surecart.php:406
msgid "Title Area"
msgstr "قسم العنوان"

#: inc/compatibility/surecart/class-astra-surecart.php:399
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:59
msgid "Upsells"
msgstr "منتجات يوصى بها"

#: inc/compatibility/surecart/class-astra-surecart.php:395
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:52
msgid "Collections"
msgstr "المجموعات"

#: inc/compatibility/surecart/class-astra-surecart.php:390
msgid "Product"
msgstr "منتج"

#: inc/customizer/class-astra-customizer.php:1647
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Close"
msgstr "إغلاق"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:428
msgid "Sign up"
msgstr "التسجيل"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:427
msgid "Log in"
msgstr "تسجيل الدخول"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6390
msgid "Brands"
msgstr "العلامات التجارية"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6426
msgid "Travel"
msgstr "السفر"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6414
msgid "Lifestyle"
msgstr "نمط الحياة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6410
msgid "Environment"
msgstr "البيئة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6406
msgid "Education"
msgstr "التعليم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6398
msgid "Communication"
msgstr "تواصل"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5851
msgid "Wine Glass"
msgstr "كأس نبيذ"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5838
msgid "Wine Bottle"
msgstr "زجاجة نبيذ"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5488
msgid "Water"
msgstr "الماء"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5408
msgid "Wallet"
msgstr "محفظة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5460
msgid "Warehouse"
msgstr "المستودع"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4699
msgid "Users"
msgstr "المستخدمين"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4664
msgid "User Tag"
msgstr "وسم المستخدم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4376
msgid "Upload"
msgstr "تحميل"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4164
msgid "Umbrella"
msgstr "مظلة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3649
msgid "Tree"
msgstr "شجرة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6418
msgid "Science"
msgstr "علوم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5774
msgid "Window Maximize"
msgstr "تكبير النافذة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5755
msgid "Wind"
msgstr "رياح"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5726
msgid "Wifi"
msgstr "واي فاي"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5668
msgid "Wheelchair"
msgstr "كرسي متحرك"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4318
msgid "Up Down"
msgstr "أعلى أسفل"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4247
msgid "Universal Access"
msgstr "الوصول الشامل"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3981
msgid "Turn Down"
msgstr "خفض"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3968
msgid "Turkish Lira Sign"
msgstr "رمز الليرة التركية"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5968
msgid "WordPress Logo"
msgstr "شعار ووردبريس"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5587
msgid "Weight Scale"
msgstr "مقياس الوزن"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4825
msgid "V"
msgstr "V"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4789
msgid "United States Postal Service"
msgstr "خدمة الرمز البريدي للولايات المتحدة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4756
msgid "Users Rectangle"
msgstr "مستطيل المستخدمين"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4745
msgid "Users Rays"
msgstr "أشعة المستخدمين"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4734
msgid "Users Line"
msgstr "خط المستخدمين"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4522
msgid "User Injured"
msgstr "المستخدم مصاب"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4509
msgid "User Group"
msgstr "مجموعة المستخدمين"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4496
msgid "User Graduate"
msgstr "تخرج المستخدم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4472
msgid "User Doctor"
msgstr "طبيب المستخدم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4461
msgid "User Clock"
msgstr "ساعة المستخدم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4450
msgid "User Check"
msgstr "التحقق من المستخدم"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4091
msgid "U"
msgstr "U"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6085
msgid "X"
msgstr "X"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4003
msgid "Tv"
msgstr "Tv"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5894
msgid "Wix"
msgstr "Wix"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6111
msgid "Xbox"
msgstr "Xbox"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3178
msgid "Thumbs Down"
msgstr "إبهام للأسفل"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3145
msgid "Thermometer"
msgstr "مقياس الحرارة"

#: assets/svg/logo-svg-icons/icons-v6-3.php:3089
msgid "Text Width"
msgstr "عرض النص"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3026
msgid "O"
msgstr "O"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3283
msgid "P"
msgstr "P"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6487
msgid "I"
msgstr "I"

#: assets/svg/logo-svg-icons/icons-v6-2.php:272
msgid "J"
msgstr "J"

#: assets/svg/logo-svg-icons/icons-v6-2.php:512
msgid "K"
msgstr "K"

#: assets/svg/logo-svg-icons/icons-v6-2.php:727
msgid "L"
msgstr "L"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1409
msgid "M"
msgstr "M"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2528
msgid "T"
msgstr "T"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5287
msgid "Q"
msgstr "Q"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5397
msgid "R"
msgstr "R"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4802
msgid "H"
msgstr "H"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6319
msgid "S"
msgstr "S"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2758
msgid "N"
msgstr "N"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5300
msgid "QQ"
msgstr "QQ"

#: assets/svg/logo-svg-icons/icons-v6-2.php:553
msgid "Key"
msgstr "مفتاح"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4579
msgid "PHP"
msgstr "PHP"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4740
msgid "Pix"
msgstr "Pix"

#: assets/svg/logo-svg-icons/icons-v6-2.php:6182
msgid "Rss"
msgstr "Rss"

#: assets/svg/logo-svg-icons/icons-v6-3.php:963
msgid "Skype"
msgstr "Skype"

#: assets/svg/logo-svg-icons/icons-v6-0.php:146
msgid "500px"
msgstr "500px"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1534
msgid "Award"
msgstr "جائزة"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1588
msgid "Baby Carriage"
msgstr "عربة أطفال"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1791
msgid "Bars"
msgstr "الأشرطة"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1777
msgid "Barcode"
msgstr "الباركود"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1562
msgid "B"
msgstr "B"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3868
msgid "G"
msgstr "G"

#: assets/svg/logo-svg-icons/icons-v6-1.php:182
msgid "D"
msgstr "D"

#: assets/svg/logo-svg-icons/icons-v6-0.php:3440
msgid "C"
msgstr "C"

#: assets/svg/logo-svg-icons/icons-v6-0.php:161
msgid "A"
msgstr "A"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1147
msgid "E"
msgstr "E"

#: assets/svg/logo-svg-icons/icons-v6-1.php:1775
msgid "F"
msgstr "F"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4382
msgid "Go"
msgstr "انتقال"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2553
msgid "Fax"
msgstr "فاكس"

#: assets/svg/logo-svg-icons/icons-v6-0.php:1721
msgid "Ban"
msgstr "حظر"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2758
msgid "Box"
msgstr "صندوق"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4149
msgid "Git"
msgstr "Git"

#: assets/svg/logo-svg-icons/icons-v6-1.php:4043
msgid "Gem"
msgstr "Gem"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6011
msgid "Copy"
msgstr "نسخ"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5291
msgid "Clone"
msgstr "استنساخ"

#: inc/core/class-theme-strings.php:56
msgid "Name"
msgstr "اسم"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:129
msgid "This setting is generally not recommended on archives pages, etc. If you would like to enable it, uncheck this option"
msgstr "لا يُنصح عمومًا بهذا الإعداد في صفحات الأرشيف وما إلى ذلك. إذا كنت ترغب في تمكينه، قم بإلغاء تحديد هذا الخيار"

#: inc/compatibility/surecart/class-astra-surecart.php:394
msgid "Collection"
msgstr "مجموعة"

#: inc/compatibility/surecart/class-astra-surecart.php:391
#: inc/compatibility/surecart/customizer/class-astra-customizer-register-surecart-section.php:45
msgid "Products"
msgstr "المنتجات"

#: inc/core/class-astra-admin-settings.php:238
msgid "I want to build this website from scratch"
msgstr "أريد بناء هذا الموقع الإلكتروني من الصفر"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:89
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:104
msgid " Page Title"
msgstr " عنوان الصفحة"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:284
msgid "Read More"
msgstr "قراءة المزيد"

#: inc/core/class-theme-strings.php:71 inc/core/class-theme-strings.php:73
#: inc/core/class-theme-strings.php:90
msgid "Previous"
msgstr "السابق"

#: inc/core/class-theme-strings.php:70 inc/core/class-theme-strings.php:72
#: inc/core/class-theme-strings.php:89
msgid "Next"
msgstr "التالي"

#: inc/customizer/configurations/builder/header/configs/search.php:128
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:771
msgid "Live Search"
msgstr "بحث مباشر"

#. translators: %s: Name of special page type
#: inc/customizer/class-astra-customizer-register-sections-panels.php:440
msgid "%s Page"
msgstr "%s صفحة"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:230
msgid "Single Page"
msgstr "صفحة واحدة"

#: inc/blog/blog-config.php:497
msgid "Read More »"
msgstr "قراءة المزيد »"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:562
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:585
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:798
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1601
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:474
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:493
msgid "Badge"
msgstr "الشارة"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:486
msgid "Post Cards"
msgstr "بطاقات بريدية"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:118
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:736
msgid "Above Comments"
msgstr "فوق التعليقات"

#: assets/svg/logo-svg-icons/icons-v6-1.php:5530
#: inc/customizer/class-astra-customizer.php:1613
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:64
msgid "Heading"
msgstr "عنوان"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:732
msgid "Location"
msgstr "الموقع"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:248
msgid "When Results Not Found"
msgstr "عندما لا يتم العثور على النتائج"

#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:233
msgid "When Results Found"
msgstr "عندما يتم العثور على النتائج"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1202
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:69
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:45
msgid "List"
msgstr "القائمة"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:90
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:100
msgid "Note:"
msgstr "ملاحظة:"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:98
#: admin/assets/theme-builder/build/index.js:9271
#: admin/assets/theme-builder/build/index.js:9482
msgid "404 Page"
msgstr "صفحة 404"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:474
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:704
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:878
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:373
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:414
msgid "Image Size"
msgstr "حجم الصورة"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:352
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:608
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:279
msgid "Original"
msgstr "الأصلي"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:589
msgid "Wide"
msgstr "عريض"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:571
msgid "Below"
msgstr "أدناه"

#: inc/extras.php:1204 inc/extras.php:1211
msgid "Full Size"
msgstr "الحجم الكامل"

#: inc/extras.php:1203
msgid "Large"
msgstr "كبير"

#: inc/extras.php:1202
msgid "Medium Large"
msgstr "متوسط كبير"

#: inc/metabox/class-astra-meta-boxes.php:612
msgid "Page Background"
msgstr "خلفية الصفحة"

#: inc/customizer/configurations/builder/header/configs/search.php:98
msgid "Search Width"
msgstr "عرض شريط البحث"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:290
msgid "Heading Color"
msgstr "لون العنوان"

#: inc/core/common-functions.php:1408
msgctxt "placeholder"
msgid "Search..."
msgstr "بحث..."

#: inc/core/class-theme-strings.php:44 inc/core/class-theme-strings.php:45
#: inc/core/class-theme-strings.php:46
msgid "Search..."
msgstr "بحث..."

#: inc/core/class-astra-enqueue-scripts.php:441
msgid "No results found"
msgstr "لم يتم العثور على نتائج"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:383
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:636
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:306
msgid "4:3"
msgstr "4:3"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:382
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:635
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:305
msgid "1:1"
msgstr "1:1"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:552
msgid "Behind"
msgstr "خلف"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:385
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:638
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:308
msgid "2:1"
msgstr "2:1"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:384
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:637
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:307
msgid "16:9"
msgstr "16:9"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:53
#: inc/customizer/configurations/builder/header/configs/site-identity.php:76
msgid "Logo Color"
msgstr "لون الشعار"

#: inc/customizer/configurations/comments/class-astra-comments-configs.php:92
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:57
#: inc/metabox/class-astra-meta-boxes.php:289
#: inc/metabox/class-astra-meta-boxes.php:805
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:45
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:45
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:654
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:766
msgid "Narrow"
msgstr "ضيق"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:77
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:77
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:31
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:76
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:71
#: inc/metabox/class-astra-meta-boxes.php:332
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:184
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:164
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:722
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar Style"
msgstr "تنسيق الشريط الجانبي"

#: admin/includes/class-astra-menu.php:1162
msgid "Thank you for using"
msgstr "شكرا لإستخدامك"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1004
#: inc/theme-update/astra-update-functions.php:402
msgid "Discover"
msgstr "اكتشف"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:980
#: inc/theme-update/astra-update-functions.php:366
msgid "Visa"
msgstr "فيزا"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:327
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:924
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:452
msgid "Format"
msgstr "الصيغة"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:305
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:903
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:435
msgid "Last Updated"
msgstr "آخر تحديث"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:304
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:902
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:434
msgid "Published"
msgstr "منشور"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:302
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:900
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:432
msgid "Type"
msgstr "النوع"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:340
msgid "Read Time"
msgstr "وقت القراءة"

#: admin/includes/class-astra-menu.php:407
msgid "Menus"
msgstr "القوائم"

#: admin/includes/class-astra-menu.php:399
msgid "Blog Options"
msgstr "خيارات المدونة"

#: admin/includes/class-astra-menu.php:379
msgid "Header Settings"
msgstr "إعدادات الترويسة"

#: admin/includes/class-astra-menu.php:338
#: admin/assets/build/dashboard-app.js:2
msgid "Activated"
msgstr "تم التفعيل"

#: admin/includes/class-astra-menu.php:336
msgid "Installed"
msgstr "تم التثبيت"

#: admin/includes/class-astra-menu.php:335
msgid "Installing"
msgstr "جاري التنصيب"

#: admin/includes/class-astra-menu.php:568
#: admin/includes/class-astra-menu.php:581
#: admin/includes/class-astra-menu.php:594
#: admin/includes/class-astra-menu.php:607
#: admin/includes/class-astra-menu.php:620
#: admin/includes/class-astra-menu.php:633
#: admin/includes/class-astra-menu.php:646
#: admin/includes/class-astra-menu.php:659
#: admin/includes/class-astra-menu.php:672
#: admin/includes/class-astra-menu.php:685
#: admin/includes/class-astra-menu.php:700
#: admin/includes/class-astra-menu.php:715
#: admin/includes/class-astra-menu.php:729
#: admin/includes/class-astra-menu.php:743
#: admin/includes/class-astra-menu.php:758
#: admin/includes/class-astra-menu.php:772
#: admin/includes/class-astra-menu.php:785
msgid "Documentation"
msgstr "الوثائق"

#: admin/includes/class-astra-menu.php:226
#: admin/assets/theme-builder/build/index.js:8286
msgid "Dashboard"
msgstr "لوحة التحكم"

#: admin/includes/class-astra-api-init.php:142
msgid "Sorry, you cannot list resources."
msgstr "عذرًا، لا يمكنك إدراج الموارد."

#: admin/includes/class-astra-admin-ajax.php:236
#: admin/includes/class-astra-admin-ajax.php:270
msgid "Successfully saved data!"
msgstr "تم حفظ البيانات بنجاح!"

#: admin/includes/class-astra-admin-ajax.php:71
msgid "No post data found!"
msgstr "لم يتم العثور على بيانات المقالة!"

#: admin/includes/class-astra-admin-ajax.php:70
msgid "Sorry, something went wrong."
msgstr "عذرا، هناك خطأ ما."

#: inc/compatibility/class-astra-starter-content.php:222
msgid "Services"
msgstr "الخدمات"

#: inc/customizer/configurations/builder/header/configs/account.php:525
msgid "Text Options"
msgstr "خيارات النص"

#: inc/compatibility/class-astra-starter-content.php:251
msgctxt "Theme starter content"
msgid "Logo"
msgstr "الشعار"

#: inc/compatibility/class-astra-starter-content.php:242
msgid "Contact"
msgstr "الاتصال"

#: inc/compatibility/class-astra-starter-content.php:237
msgid "Why Us"
msgstr "لماذا نحن"

#: inc/compatibility/class-astra-starter-content.php:232
msgid "Reviews"
msgstr "المراجعات"

#: inc/compatibility/class-astra-starter-content.php:227
msgid "About"
msgstr "حول"

#: inc/compatibility/starter-content/home.php:236
msgctxt "Theme starter content"
msgid "Home"
msgstr "الصفحة الرئيسية"

#: inc/compatibility/class-astra-starter-content.php:261
#: inc/compatibility/class-astra-starter-content.php:265
#: inc/customizer/class-astra-customizer.php:1621
#: inc/customizer/class-astra-customizer.php:1674
msgid "Primary"
msgstr "أساسي"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:46
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:324
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:439
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:125
msgid "Layout 2"
msgstr "التخطيط 2"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:42
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:320
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:435
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:121
msgid "Layout 1"
msgstr "التخطيط 1"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:262
msgid "Description"
msgstr "الوصف"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:428
msgid "Blog Title"
msgstr "عنوان المدونة"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:781
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1585
msgid "Taxonomy"
msgstr "الفئة"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:243
msgid "Select"
msgstr "اختر"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:517
msgid "Add payment title"
msgstr "إضافة عنوان الدفعة"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:405
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:634
msgid "More color options"
msgstr "المزيد من خيارات الألوان"

#: inc/core/class-astra-admin-settings.php:289
#: admin/assets/build/dashboard-app.js:1 admin/assets/utils/helpers.js:46
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Upgrade Now"
msgstr "ترقية الآن"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2804
msgid "Telegram"
msgstr "تلغرام"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1943 inc/extras.php:1201
msgid "Medium"
msgstr "متوسط"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1129
msgid "LinkedIn"
msgstr "لينكد إن"

#: assets/svg/logo-svg-icons/icons-v6-2.php:4699
msgid "Pinterest"
msgstr "بنترست"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5063
msgid "Video"
msgstr "فيميو"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6338
msgid "YouTube"
msgstr "يوتيوب"

#: assets/svg/logo-svg-icons/icons-v6-2.php:115
msgid "Instagram"
msgstr "إنستغرام"

#: assets/svg/logo-svg-icons/icons-v6-3.php:4035
#: assets/svg/logo-svg-icons/icons-v6-3.php:4050
msgid "Twitter"
msgstr "تويتر"

#: assets/svg/logo-svg-icons/icons-v6-1.php:2441
msgid "Facebook"
msgstr "فيسبوك"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:72
msgid "Payments"
msgstr "المدفوعات"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:47
msgid "Extras"
msgstr "إضافات"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:78
msgid "Design 3"
msgstr "تصميم 3"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:74
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:89
msgid "Design 2"
msgstr "تصميم 2"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:70
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:85
msgid "Design 1"
msgstr "تصميم 1"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:300
msgid "Sale!"
msgstr "تخفيضات!"

#: assets/svg/logo-svg-icons/icons-v6-3.php:5224
msgid "VK"
msgstr "VK"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:495
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:516
msgid "Payment Title"
msgstr "عنوان الدفعة"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:44
msgid "Columns"
msgstr "الأعمدة"

#: inc/core/builder/class-astra-builder-helper.php:765
#: inc/core/builder/class-astra-builder-helper.php:935
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:70
msgid "Menu 3"
msgstr "القائمة 3"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:66
msgid "Menu 2"
msgstr "القائمة 2"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3511
msgid "Continue Shopping"
msgstr "مواصلة التسوق"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3507
msgid "No products in the cart."
msgstr "لا توجد منتجات في السلة."

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:534
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:557
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:576
msgid "Button Width"
msgstr "عرض الزر"

#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:123
msgid "Outside"
msgstr "بالخارج"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:104
msgid "Links"
msgstr "الروابط"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:31
msgid "Product Options"
msgstr "خيارات المنتج"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:213
msgid "Shipping Text"
msgstr "نص الشحن"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:111
msgid "Total amount"
msgstr "المبلغ الإجمالي"

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:110
msgid "Currency Name"
msgstr "اسم العملة"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:159
msgid "Borders"
msgstr "الحدود"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:146
msgid "Body Text"
msgstr "النص الأساسي"

#: inc/core/common-functions.php:1184
msgid "Author name: "
msgstr "اسم الكاتب: "

#: inc/modules/related-posts/class-astra-related-posts-markup.php:234
msgid "Related post link"
msgstr "رابط ذات صله"

#: theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "متوسط"

#: inc/metabox/class-astra-meta-boxes.php:887
msgid "Disable Header"
msgstr "تعطيل الترويسة"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:405
msgid "Block Editor"
msgstr "محرِّر المكوِّنات"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:346
msgid "H2 Font"
msgstr "خط ترويسة 2"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:331
msgid "H1 Font"
msgstr "خط ترويسة 1"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:186
msgid "Headings Font"
msgstr "خط العناوين"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:406
msgid "H6 Font"
msgstr "خط ترويسة 6"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:391
msgid "H5 Font"
msgstr "خط ترويسة 5"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:377
msgid "H4 Font"
msgstr "خط ترويسة 4"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:361
msgid "H3 Font"
msgstr "خط ترويسة 3"

#: inc/customizer/class-astra-font-families.php:128
msgid "Regular 400"
msgstr "عادي 400"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:57
msgid "Body Font"
msgstr "خط النص الأساسي"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:392
msgid "Type 3"
msgstr "النوع 3"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:388
msgid "Type 2"
msgstr "النوع 2"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:384
msgid "Type 1"
msgstr "النوع 1"

#: inc/metabox/class-astra-meta-boxes.php:637
msgid "Page Header"
msgstr "رأس الصفحة"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:462
msgid "Text / Icon"
msgstr "نص / أيقونة"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 9"
msgstr "لون القالب 9"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 8"
msgstr "لون القالب 8"

#: theme.json
msgctxt "Color name"
msgid "Theme Color 7"
msgstr "لون القالب 7"

#: inc/class-astra-global-palette.php:314
msgid "Color 8"
msgstr "لون 8"

#: inc/class-astra-global-palette.php:313
msgid "Color 7"
msgstr "لون 7"

#: inc/class-astra-global-palette.php:312
msgid "Color 6"
msgstr "لون 6"

#: inc/class-astra-global-palette.php:311
msgid "Color 5"
msgstr "لون 5"

#: inc/class-astra-global-palette.php:310
msgid "Color 4"
msgstr "لون 4"

#: inc/class-astra-global-palette.php:309
msgid "Color 3"
msgstr "لون 3"

#: inc/class-astra-global-palette.php:308
msgid "Color 2"
msgstr "لون 2"

#: inc/class-astra-global-palette.php:307
msgid "Color 1"
msgstr "لون 1"

#: assets/svg/logo-svg-icons/icons-v6-2.php:3381
msgid "Palette"
msgstr "طبق الألوان"

#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:123
msgid "Site Background"
msgstr "خلفية الموقع"

#: inc/class-astra-global-palette.php:315
msgid "Color 9"
msgstr "لون 9"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6402
msgid "Design"
msgstr "تصميم"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5757
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Repeat"
msgstr "تكرار"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:549
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:568
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Image Position"
msgstr "موضع الصورة"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:161
msgid "Title Alignment"
msgstr "محاذاة العنوان"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:985
msgid "Section Title"
msgstr "عنوان القسم"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:688
msgid "Order"
msgstr "الترتيب"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:694
msgid "Descending"
msgstr "تنازليّاً"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:693
msgid "Ascending"
msgstr "تصاعُديّاً"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:673
msgid "Post Order"
msgstr "ترتيب المقال"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2693
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:262
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:702
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:650
msgid "Tags"
msgstr "الوسوم"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:605
msgid "4"
msgstr "4"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:604
msgid "3"
msgstr "3"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:603
msgid "2"
msgstr "2"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:602
msgid "1"
msgstr "1"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:674
msgid "Random"
msgstr "عشوائي"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:700
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:327
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:69
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:671
msgid "Date"
msgstr "التاريخ"

#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:668
msgid "Order by"
msgstr "الترتيب حسب"

#: searchform.php:27
msgid "Search for:"
msgstr "البحث عن:"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:644
msgid "Search Color"
msgstr "لون البحث"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:591
msgid "HTML Color"
msgstr "لون HTML "

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:282
msgid "Checkout"
msgstr "إتمام الدفع"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:410
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:427
#: inc/customizer/configurations/builder/header/configs/menu.php:373
msgid "Text / Link"
msgstr "النص / الرابط"

#: inc/customizer/configurations/builder/header/configs/menu.php:190
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:107
msgid "Divider Size"
msgstr "حجم الفاصل"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:51
msgid "After"
msgstr "بعد"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3598
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:60
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:76
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:338
#: inc/customizer/configurations/builder/header/configs/menu.php:502
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:286
msgid "Font"
msgstr "الخط"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:63
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:714
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1180
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:448
msgid "Text Font"
msgstr "خطّ النص"

#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:305
#: inc/customizer/configurations/builder/header/configs/menu.php:515
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:301
msgid "Menu Font"
msgstr "خط القائمة"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:59
msgid "Full-Screen"
msgstr "شاشة كاملة"

#: inc/core/builder/class-astra-builder-helper.php:841
msgid "Woo Cart"
msgstr "سلة ووكومرس"

#: inc/core/builder/class-astra-builder-helper.php:851
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:36
msgid "EDD Cart"
msgstr "سلة EDD "

#: inc/customizer/class-astra-extended-base-configuration.php:150
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:292
#: inc/customizer/configurations/builder/header/configs/menu.php:262
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:207
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:464
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:570
msgid "Border Width"
msgstr "عُرض الحد"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:56
msgid "Header Type"
msgstr "نمط الترويسة"

#: inc/customizer/configurations/builder/header/configs/account.php:321
msgid "Preview"
msgstr "معاينة"

#: inc/customizer/configurations/builder/header/configs/account.php:228
msgid "Account URL"
msgstr "عنوان URL للحساب"

#: inc/customizer/configurations/builder/header/configs/account.php:142
#: inc/customizer/configurations/builder/header/configs/account.php:171
msgid "Avatar"
msgstr "الصورة الرمزية / الشخصية"

#: inc/customizer/configurations/builder/header/configs/account.php:139
#: inc/customizer/configurations/builder/header/configs/account.php:262
msgid "Profile Type"
msgstr "نوع الملف الشخصي"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:60
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:197
msgid "Dropdown"
msgstr "القائمة المنسدلة"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:394
msgid "Icon Spacing"
msgstr "تباعد الأيقونة"

#. translators: 1: index
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:59
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:352
msgid "Social Icons"
msgstr "الأيقونات الاجتماعية"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:125
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:125
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:124
#: inc/customizer/configurations/builder/header/configs/above-header.php:53
#: inc/customizer/configurations/builder/header/configs/below-header.php:53
#: inc/customizer/configurations/builder/header/configs/primary-header.php:65
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:419
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:674
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:344
msgid "Height"
msgstr "الارتفاع"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:60
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:60
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:60
msgid "Column"
msgstr "عمود"

#: inc/customizer/configurations/builder/footer/configs/below-footer.php:32
msgid "Below Footer"
msgstr "أسفل التذييل"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1115
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1131
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:208
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:223
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:224
#: inc/customizer/configurations/builder/header/configs/menu.php:471
#: inc/customizer/configurations/builder/header/configs/menu.php:472
#: inc/customizer/configurations/builder/header/configs/menu.php:488
#: inc/customizer/configurations/builder/header/configs/menu.php:489
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:253
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:254
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:270
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:271
msgid "Active"
msgstr "مفعل"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1013
msgid "Divider"
msgstr "فاصل"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:824
#: inc/core/builder/class-astra-builder-helper.php:699
#: inc/core/builder/class-astra-builder-helper.php:884
#: inc/customizer/configurations/builder/header/configs/account.php:95
msgid "Account"
msgstr "حساب"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:139
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:102
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:177
#: inc/customizer/configurations/builder/header/configs/account.php:494
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:141
#: inc/customizer/configurations/builder/header/configs/search.php:61
msgid "Icon Color"
msgstr "لون الأيقونة"

#: inc/customizer/configurations/builder/header/configs/menu.php:96
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:457
msgid "Zoom In"
msgstr "تكبير"

#: inc/admin-functions.php:41
#: inc/customizer/configurations/builder/header/configs/header-builder.php:115
#: inc/customizer/configurations/builder/header/configs/menu.php:35
msgid "Secondary Menu"
msgstr "القائمة الثانوية"

#. translators: %s Index
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:64
msgid "Button %s"
msgstr "زر %s"

#. translators: %s Index
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:69
msgid "HTML %s"
msgstr "HTML %s"

#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:87
msgid "Official"
msgstr "رسميّ"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:120
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:440
#: inc/customizer/configurations/builder/header/configs/account.php:449
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:159
#: inc/customizer/configurations/builder/header/configs/search.php:75
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:613
msgid "Icon Size"
msgstr "حجم الأيقونة"

#: inc/core/builder/class-astra-builder-options.php:1261
msgid "Log In"
msgstr "تسجيل الدخول"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6538
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:58
msgid "Icons"
msgstr "أيقونات"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:189
msgid "Visibility"
msgstr "الظهور"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6422
msgid "Social"
msgstr "اجتماعي"

#: inc/admin-functions.php:56
#: inc/customizer/configurations/builder/header/configs/header-builder.php:118
#: inc/customizer/configurations/builder/header/configs/menu.php:38
msgid "Menu "
msgstr "القائمة "

#: inc/customizer/configurations/builder/header/configs/edd-cart.php:90
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:316
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:553
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:576
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:624
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:646
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:795
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1598
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:471
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:490
msgid "Style"
msgstr "التنسيق"

#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:229
msgid "Widget "
msgstr "ودجت "

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:515
msgid "Tagline"
msgstr "سطر الوصف"

#: assets/svg/logo-svg-icons/icons-v6-0.php:6030
#: inc/customizer/configurations/builder/footer/configs/copyright.php:31
msgid "Copyright"
msgstr "حقوق النشر"

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:29
msgid "Off-Canvas"
msgstr "Off-Canvas"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:416
#: admin/assets/build/dashboard-app.js:1
msgid "Usage Tracking"
msgstr "تتبع الاستخدام"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:291
msgid "No Thanks"
msgstr "لا, شكراً"

#. translators: %s usage doc link
#: inc/lib/bsf-analytics/class-bsf-analytics.php:270
msgid " Know More."
msgstr "معرفة المزيد"

#: inc/lib/bsf-analytics/class-bsf-analytics.php:280
msgid "Yes! Allow it"
msgstr "نعم! أسمح بذلك"

#: inc/core/class-astra-admin-settings.php:494
msgid "Details &#187;"
msgstr "التفاصيل &#187;"

#: inc/core/class-astra-admin-settings.php:467
#: inc/core/class-astra-admin-settings.php:479
#: inc/core/class-astra-admin-settings.php:491
msgid "See Library &#187;"
msgstr "انظر المكتبة &#187;"

#: inc/extras.php:446
msgid "Astra WordPress Theme"
msgstr "قالب Astra للووردبريس"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:339
msgid "Letter Spacing"
msgstr "تباعد الأحرف"

#. translators: %d: Minutes interval
#: inc/lib/batch-processing/class-astra-wp-background-process.php:410
msgid "Every %d Minutes"
msgstr "كل %d دقيقة"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:255
msgid "Customize Button Style."
msgstr "تخصيص نمط الزر"

#: inc/customizer/class-astra-customizer.php:1719
msgid "Heading 1"
msgstr "العنوان 1"

#: inc/customizer/class-astra-customizer.php:1725
msgid "Heading 2"
msgstr "العنوان 2"

#: inc/customizer/class-astra-customizer.php:1731
msgid "Heading 3"
msgstr "العنوان 3"

#: inc/customizer/class-astra-customizer.php:1737
msgid "Heading 4"
msgstr "العنوان 4"

#: inc/customizer/class-astra-customizer.php:1743
msgid "Heading 5"
msgstr "العنوان 5"

#: inc/customizer/class-astra-customizer.php:1749
msgid "Heading 6"
msgstr "العنوان 6"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:289
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:449
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:216
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:529
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:270
#: inc/customizer/configurations/builder/footer/configs/copyright.php:80
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:99
msgid "Alignment"
msgstr "محاذاة"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:380
msgid "Separator"
msgstr "فاصل"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:40
msgid "Global"
msgstr "عام"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:90
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:119
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:245
msgid "Position"
msgstr "الموضع"

#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:110
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:393
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:636
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1103
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:384
msgid "Title Color"
msgstr "لون العنوان"

#: inc/customizer/class-astra-customizer.php:1412
#: inc/customizer/class-astra-customizer.php:1662
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:569
msgid "Site Icon"
msgstr "أيقونة الموقع"

#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:65
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Size"
msgstr "حجم"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:49
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:197
#: inc/customizer/class-astra-customizer-register-sections-panels.php:276
#: inc/customizer/class-astra-customizer.php:1686
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:659
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:72
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:393
msgid "Colors"
msgstr "الألوان"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:161
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:163
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:128
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:129
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:164
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:165
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:181
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:218
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:219
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:235
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:272
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:273
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:496
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:502
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:534
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:540
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:606
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:722
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:726
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:756
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:760
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:790
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:793
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1047
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1071
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:50
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:50
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:58
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:50
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:385
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:386
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:439
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:440
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:165
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:171
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:203
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:209
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:259
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:144
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:240
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:246
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:278
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:284
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:316
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:322
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:343
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:142
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:144
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:159
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:161
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:162
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:306
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:322
#: inc/customizer/configurations/builder/header/configs/menu.php:403
#: inc/customizer/configurations/builder/header/configs/menu.php:405
#: inc/customizer/configurations/builder/header/configs/menu.php:421
#: inc/customizer/configurations/builder/header/configs/menu.php:423
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:185
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:187
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:203
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:205
#: inc/customizer/configurations/builder/header/configs/site-identity.php:90
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:363
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:416
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:694
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:754
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:830
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:846
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:937
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:953
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:460
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:489
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:516
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:704
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:738
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:127
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:161
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:136
#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:119
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:56
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:53
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:411
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:450
#: inc/metabox/class-astra-meta-boxes.php:287
#: inc/metabox/class-astra-meta-boxes.php:798
#: inc/metabox/class-astra-meta-boxes.php:804
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:60
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:60
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:650
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:934
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:949
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1019
msgid "Normal"
msgstr "عادي"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:149
msgid "Post Content"
msgstr "محتوى المقالة"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:428
msgid "Archive Title"
msgstr "عنوان الأرشيف"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:253
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:269
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:840
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:433
msgid "Hover Color"
msgstr "لون تمرير الفأرة"

#: inc/customizer/class-astra-extended-base-configuration.php:80
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:543
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:781
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:373
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:920
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1499
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:620
msgid "Padding"
msgstr "الهوامش الداخلية"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:176
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:178
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:146
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:147
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:200
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:254
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:290
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:291
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:515
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:521
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:553
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:559
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:621
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:739
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:743
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:773
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:777
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:806
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:809
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1081
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1101
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:402
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:403
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:456
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:457
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:184
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:190
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:222
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:228
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:277
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:163
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:259
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:265
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:297
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:303
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:335
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:341
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:360
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:171
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:175
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:188
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:192
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:338
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:354
#: inc/customizer/configurations/builder/header/configs/menu.php:433
#: inc/customizer/configurations/builder/header/configs/menu.php:437
#: inc/customizer/configurations/builder/header/configs/menu.php:451
#: inc/customizer/configurations/builder/header/configs/menu.php:455
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:215
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:219
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:233
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:237
#: inc/customizer/configurations/builder/header/configs/site-identity.php:104
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:380
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:434
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:710
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:770
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:862
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:878
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:969
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:985
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:476
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:502
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:529
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:721
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:755
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:144
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:178
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:149
#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:133
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:69
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:477
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:504
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1034
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1049
msgid "Hover"
msgstr "تمرير الفأرة"

#: admin/includes/class-astra-menu.php:387
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:221
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:237
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:38
#: inc/customizer/class-astra-extended-base-configuration.php:172
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:84
#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:139
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:112
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:825
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:417
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:515
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Color"
msgstr "اللون"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:924
#: inc/customizer/class-astra-customizer.php:1629
#: inc/customizer/class-astra-extended-base-configuration.php:136
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:680
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:98
msgid "Border"
msgstr "إطار"

#: inc/customizer/configurations/builder/header/configs/menu.php:216
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:133
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:373
msgid "Divider Color"
msgstr "لون الفاصل"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:476
msgid "Mobile Menu"
msgstr "قائمة الجوال"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:125
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:550
msgid "Menu Label"
msgstr "تسمية القائمة"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:587
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:679
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:858
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:947
#: inc/class-astra-global-palette.php:299
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:63
#: inc/customizer/class-astra-customizer.php:1617
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:93
#: inc/customizer/configurations/builder/header/configs/account.php:143
#: inc/customizer/configurations/builder/header/configs/account.php:201
#: inc/customizer/configurations/builder/header/configs/account.php:267
#: inc/customizer/configurations/builder/header/configs/account.php:348
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:798
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:905
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:87
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:157
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:218
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Text"
msgstr "نص"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:184
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:184
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:185
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:384
msgid "Top Border Size"
msgstr "حجم الحدّ العلوي"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:398
msgid "Top Border Color"
msgstr "لون الحدّ العلوي"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:405
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:527
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:204
msgid "Structure"
msgstr "الهيكل"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:466
msgid "Take Last Item Outside Menu"
msgstr "انقل العنصر الأخير خارج القائمة"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:362
msgid "Submenu Divider"
msgstr "فاصل القائمة الفرعية"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:331
msgid "Container Border"
msgstr "حدود الحاوية"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:289
msgid "Sub Menu"
msgstr "القائمة الفرعية"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:230
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:296
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:132
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:75
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:384
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:214
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:506
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:186
msgid "Custom Width"
msgstr "عرض مخصص"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:105
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:105
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:105
#: inc/customizer/configurations/builder/header/configs/header-builder.php:645
#: inc/customizer/configurations/builder/header/configs/menu.php:133
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:803
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:395
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:400
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:355
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:71
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:654
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:324
msgid "Width"
msgstr "العرض"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:120
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:690
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:349
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:823
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:258
msgid "Meta"
msgstr "البيانات الوصفية"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:519
msgid "The parent menu should have a # link for the submenu to open on a link."
msgstr "يجب أن تحتوي القائمة الأصلية على رابط # لفتح القائمة الفرعية على الرابط."

#: inc/customizer/configurations/builder/header/configs/off-canvas.php:175
msgid "Center"
msgstr "وسط"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:261
msgid "All Pages, All Posts, All Attachments"
msgstr "كل الصفحات، كل المشاركات، كل المرفقات"

#. Translators: Minute archive title. %s is the minute time format.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:413
msgid "Minute %s"
msgstr "%s دقيقة"

#. Translators: %s is the page number.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:411
msgid "Comment Page %s"
msgstr "تعليقات صفحة %s"

#. Translators: %s is the page number.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:409
msgid "Page %s"
msgstr "صفحة %s"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:405
msgid "Archives"
msgstr "الأرشيف"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:404
msgid "404 Not Found"
msgstr "404 غير موجود"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:402
msgctxt "breadcrumbs aria label"
msgid "Breadcrumbs"
msgstr "Breadcrumbs"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:401
msgid "Browse:"
msgstr "استعراض:"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:44
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:52
msgid "Before Title"
msgstr "قبل العنوان"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:350
msgid "Breadcrumb Source"
msgstr "مصدر Breadcrumb"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:73
msgid "Breadcrumb Overview"
msgstr "نظرة عامة Breadcrumb"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:117
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:261
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:725
msgid "Separator Color"
msgstr "لون الفاصل"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:953
msgctxt "weekly archives date format"
msgid "W"
msgstr "W"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:925
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1351
msgctxt "daily archives date format"
msgid "j"
msgstr "j"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:924
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:981
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1347
msgctxt "monthly archives date format"
msgid "F"
msgstr "F"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:923
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:952
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:980
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1007
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:1343
msgctxt "yearly archives date format"
msgid "Y"
msgstr "Y"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:907
msgctxt "hour archives time format"
msgid "g a"
msgstr "g a"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:890
msgctxt "minute archives time format"
msgid "i"
msgstr "i"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:873
msgctxt "minute and hour archives time format"
msgid "g:i a"
msgstr "g:i a"

#. Translators: Weekly archive title. %s is the week date format.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:415
msgid "Week %s"
msgstr "%s أسبوع"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:66
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:409
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:532
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:73
msgid "Breadcrumb"
msgstr "مسارات التنقّل"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:190
msgid "Latest posts page or when any page is selected as blog page"
msgstr "صفحة احدث المقالات أو عند تحديد أي صفحة كصفحة مدونة "

#. Translators: %s is the search query.
#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:407
msgid "Search results for: %s"
msgstr "نتائج البحث عن: %s"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:159
msgid "Latest Posts page is your site's front page when the latest posts are displayed on the home page."
msgstr "صفحة أحدث المشاركات هي الصفحة الأولى لموقعك عندما يتم عرض أحدث المشاركات على الصفحة الرئيسية."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:144
msgid "Blog Page is when Latest Posts are selected to be displayed on a particular page."
msgstr "صفحة المدونة هي عندما يتم اختيار أحدث المشاركات ليتم عرضها على صفحة معينة."

#: inc/class-astra-mobile-header.php:110
#: inc/core/class-astra-walker-page.php:86
#: inc/core/class-astra-walker-page.php:96 inc/extras.php:545
msgid "Menu Toggle"
msgstr "القائمة"

#: admin/includes/class-astra-admin-ajax.php:463
msgid "Plugin Successfully Deactivated"
msgstr "تم تعطيل الإضافة بنجاح"

#: inc/core/class-astra-admin-settings.php:147
msgid "Deactivate"
msgstr "تعطيل"

#: admin/includes/class-astra-menu.php:339
#: inc/core/class-astra-admin-settings.php:146
#: admin/assets/build/dashboard-app.js:2
msgid "Activate"
msgstr "تفعيل"

#: admin/includes/class-astra-menu.php:337
#: inc/core/class-astra-admin-settings.php:144
msgid "Activating"
msgstr "جاري التفعيل"

#: inc/core/class-astra-admin-settings.php:145
msgid "Deactivating"
msgstr "جاري التعطيل"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:383
msgid "Primary Header Button"
msgstr "زر الشريط العلوي الأساسي"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:705
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:235
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:207
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:206
#: inc/customizer/configurations/builder/header/configs/menu.php:281
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:234
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:440
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:254
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:348
msgid "Border Color"
msgstr "لون الإطار"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:219
msgid "Border Size"
msgstr "حجم الإطار"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:227
msgid "Button Style"
msgstr "نمط الزر"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:242
#: inc/customizer/configurations/builder/header/configs/site-identity.php:34
msgid "Logo"
msgstr "الشعار"

#: assets/svg/logo-svg-icons/icons-v6-2.php:2313
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:63
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:205
msgid "Mobile"
msgstr "الجوال"

#: assets/svg/logo-svg-icons/icons-v6-1.php:351
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:62
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:204
msgid "Desktop"
msgstr "سطح المكتب"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:201
msgid "Enable On"
msgstr "تفعيل في"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:59
msgid "Enable on Complete Website"
msgstr "تمكين على الموقع الكامل"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:339
#: inc/metabox/class-astra-meta-boxes.php:978
#: inc/metabox/class-astra-meta-boxes.php:991
msgid "Enabled"
msgstr "مفعل"

#: inc/customizer/configurations/builder/header/configs/menu.php:635
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:62
msgid "Menu"
msgstr "القائمة"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:224
msgid "Theme Button"
msgstr "زر القالب"

#: inc/customizer/configurations/builder/header/configs/menu.php:115
msgid "Submenu"
msgstr "القائمة الفرعية"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1049
msgid "Link / Text Color"
msgstr "لون الرابط / النص"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:360
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:274
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:183
msgid "Button Text"
msgstr "نص الزر"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:203
msgid "Button Link"
msgstr "رابط الزر"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:394
msgid "Transparent Header Button"
msgstr "زر شفاف لأعلى الموقع"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:64
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:206
msgid "Desktop + Mobile"
msgstr "سطح المكتب + الجوال"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:372
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:639
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:46
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:225
msgid "Header Button"
msgstr "زر الشريط العلوي"

#: inc/compatibility/edd/class-astra-edd.php:861
msgid "View Details"
msgstr "عرض التفاصيل"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:54
msgid "Archive Columns"
msgstr "أرشيف الأعمدة"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:152
msgid "Options"
msgstr "الخيارات"

#. translators: %s Index
#: admin/includes/class-astra-menu.php:395
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:151
#: inc/core/builder/class-astra-builder-options.php:1339
#: inc/core/builder/class-astra-builder-options.php:1508
#: inc/core/class-astra-theme-options.php:484
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:64
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:148
msgid "Button"
msgstr "زر"

#: assets/svg/logo-svg-icons/icons-v6-1.php:6639
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:93
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Image"
msgstr "صورة"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:70
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:90
msgid "Product Structure"
msgstr "هيكلة المنتج"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:199
msgid "Archive Content Width"
msgstr "عرض محتوى الأرشيف"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:187
msgid "Variable Product Button Text"
msgstr "نص زر المنتج المتغير"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:148
msgid "Variable Product Button"
msgstr "زر المنتج المتغير"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:126
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-cart-layout-configs.php:53
msgid "Cart Button Text"
msgstr "نص زر سلة المشتريات"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:51
msgid "Product Archive"
msgstr "أرشيف المنتج"

#: inc/compatibility/edd/class-astra-edd.php:935
msgid "This sidebar will be used on EDD Single Product page."
msgstr "هذا الشريط الجانبي سيستعمل في صفحة المنتج المنفرد من نوع EDD."

#: inc/compatibility/edd/class-astra-edd.php:919
msgid "Easy Digital Downloads Sidebar"
msgstr "الشريط الجانبي للتنزيلات الرقمية السهلة EDD"

#: inc/compatibility/edd/class-astra-edd.php:319
#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:37
#: admin/assets/build/dashboard-app.js:1
msgid "Easy Digital Downloads"
msgstr "للتنزيلات الرقمية السهلة EDD"

#. Translators: %s is the theme name.
#: inc/metabox/class-astra-meta-boxes.php:145
#: inc/metabox/class-astra-meta-boxes.php:578
msgid "%s Settings"
msgstr "إعدادات %s"

#: inc/customizer/class-astra-customizer.php:624
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:137
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:128
msgid "900 Italic"
msgstr "900 Italic"

#: inc/customizer/class-astra-customizer.php:622
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:135
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:126
msgid "800 Italic"
msgstr "800 Italic"

#: inc/customizer/class-astra-customizer.php:620
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:133
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:124
msgid "700 Italic"
msgstr "700 Italic"

#: inc/customizer/class-astra-customizer.php:618
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:131
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:122
msgid "600 Italic"
msgstr "600 Italic"

#: inc/customizer/class-astra-customizer.php:616
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:129
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:120
msgid "500 Italic"
msgstr "500 Italic"

#: inc/customizer/class-astra-customizer.php:614
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:127
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:118
msgid "400 Italic"
msgstr "400 Italic"

#: inc/customizer/class-astra-customizer.php:611
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:124
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:115
msgid "300 Italic"
msgstr "300 Italic"

#: inc/customizer/class-astra-customizer.php:609
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:122
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:113
msgid "200 Italic"
msgstr "200 Italic"

#: inc/customizer/class-astra-customizer.php:607
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:120
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:111
msgid "100 Italic"
msgstr "100 Italic"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:328
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:92
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:203
#: inc/customizer/class-astra-extended-base-configuration.php:87
#: inc/customizer/class-astra-extended-base-configuration.php:112
#: inc/customizer/class-astra-extended-base-configuration.php:159
#: inc/customizer/class-astra-extended-base-configuration.php:191
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:74
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:298
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:321
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:202
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:472
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:513
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:255
#: inc/customizer/configurations/builder/footer/configs/copyright.php:143
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:263
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:287
#: inc/customizer/configurations/builder/header/configs/account.php:612
#: inc/customizer/configurations/builder/header/configs/header-builder.php:682
#: inc/customizer/configurations/builder/header/configs/menu.php:267
#: inc/customizer/configurations/builder/header/configs/menu.php:306
#: inc/customizer/configurations/builder/header/configs/menu.php:362
#: inc/customizer/configurations/builder/header/configs/menu.php:642
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:431
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:455
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:212
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:266
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:315
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:121
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:174
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:276
#: inc/customizer/configurations/builder/header/configs/search.php:223
#: inc/customizer/configurations/builder/header/configs/site-identity.php:142
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:247
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:493
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:521
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:551
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:575
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:598
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:786
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:808
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:378
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:400
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:541
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:130
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:155
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:336
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:906
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:928
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1477
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1507
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:606
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:628
msgid "Left"
msgstr "يسار"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:326
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:93
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:201
#: inc/customizer/class-astra-extended-base-configuration.php:85
#: inc/customizer/class-astra-extended-base-configuration.php:110
#: inc/customizer/class-astra-extended-base-configuration.php:157
#: inc/customizer/class-astra-extended-base-configuration.php:189
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:72
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:296
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:319
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:200
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:470
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:511
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:253
#: inc/customizer/configurations/builder/footer/configs/copyright.php:141
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:261
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:285
#: inc/customizer/configurations/builder/header/configs/account.php:610
#: inc/customizer/configurations/builder/header/configs/header-builder.php:680
#: inc/customizer/configurations/builder/header/configs/menu.php:265
#: inc/customizer/configurations/builder/header/configs/menu.php:304
#: inc/customizer/configurations/builder/header/configs/menu.php:360
#: inc/customizer/configurations/builder/header/configs/menu.php:640
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:429
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:453
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:210
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:264
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:313
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:122
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:176
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:274
#: inc/customizer/configurations/builder/header/configs/search.php:221
#: inc/customizer/configurations/builder/header/configs/site-identity.php:140
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:248
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:491
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:522
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:549
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:573
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:596
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:784
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:806
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:376
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:398
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:539
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:128
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:153
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:334
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:904
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:926
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1475
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1505
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:604
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:626
msgid "Right"
msgstr "يمين"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:325
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:200
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:94
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:266
#: inc/customizer/class-astra-extended-base-configuration.php:84
#: inc/customizer/class-astra-extended-base-configuration.php:109
#: inc/customizer/class-astra-extended-base-configuration.php:156
#: inc/customizer/class-astra-extended-base-configuration.php:188
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:71
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:295
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:318
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:199
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:469
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:510
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:252
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:150
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:150
#: inc/customizer/configurations/builder/footer/configs/copyright.php:140
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:260
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:284
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:149
#: inc/customizer/configurations/builder/header/configs/account.php:609
#: inc/customizer/configurations/builder/header/configs/header-builder.php:679
#: inc/customizer/configurations/builder/header/configs/menu.php:264
#: inc/customizer/configurations/builder/header/configs/menu.php:303
#: inc/customizer/configurations/builder/header/configs/menu.php:359
#: inc/customizer/configurations/builder/header/configs/menu.php:639
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:428
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:452
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:209
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:263
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:312
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:273
#: inc/customizer/configurations/builder/header/configs/search.php:220
#: inc/customizer/configurations/builder/header/configs/site-identity.php:139
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:490
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:548
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:572
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:595
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:783
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:805
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:375
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:397
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:538
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:127
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:152
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:333
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:512
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:903
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:925
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:992
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1474
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1504
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:277
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:603
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:625
msgid "Top"
msgstr "أعلى"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:327
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:202
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:95
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:267
#: inc/customizer/class-astra-extended-base-configuration.php:86
#: inc/customizer/class-astra-extended-base-configuration.php:111
#: inc/customizer/class-astra-extended-base-configuration.php:158
#: inc/customizer/class-astra-extended-base-configuration.php:190
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:73
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:297
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:320
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:201
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:471
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:512
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:254
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:152
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:152
#: inc/customizer/configurations/builder/footer/configs/copyright.php:142
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:262
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:286
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:151
#: inc/customizer/configurations/builder/header/configs/account.php:611
#: inc/customizer/configurations/builder/header/configs/header-builder.php:681
#: inc/customizer/configurations/builder/header/configs/menu.php:266
#: inc/customizer/configurations/builder/header/configs/menu.php:305
#: inc/customizer/configurations/builder/header/configs/menu.php:361
#: inc/customizer/configurations/builder/header/configs/menu.php:641
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:430
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:454
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:211
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:265
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:314
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:275
#: inc/customizer/configurations/builder/header/configs/search.php:222
#: inc/customizer/configurations/builder/header/configs/site-identity.php:141
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:492
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:523
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:550
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:574
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:597
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:785
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:807
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:377
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:399
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:540
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:129
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:154
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:335
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:514
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:905
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:927
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:994
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1476
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1506
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:279
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:605
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:627
msgid "Bottom"
msgstr "أسفل"

#: admin/includes/class-astra-menu.php:678
msgid "Nav Menu"
msgstr "شريط القائمة"

#: inc/customizer/configurations/builder/header/configs/menu.php:159
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:317
msgid "Fade"
msgstr "تلاشي"

#: inc/customizer/configurations/builder/header/configs/menu.php:158
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:316
msgid "Slide Up"
msgstr "تحرك للأعلى"

#: inc/customizer/configurations/builder/header/configs/menu.php:157
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:315
msgid "Slide Down"
msgstr "تحرك للأسفل"

#. Translators: Author Name.
#: inc/blog/blog-config.php:435
msgid "View all posts by %1$s"
msgstr "عرض جميع المقالات بواسطة %1$s"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:325
msgid "Typography Overview"
msgstr "نظرة عامة على تنسيق الخطوط"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:283
msgid "Colors & Background Overview"
msgstr "نظرة عامة على الالوان والخلفية "

#: inc/customizer/class-astra-customizer-register-sections-panels.php:55
msgid "Site Layout Overview"
msgstr "نظرة عامة على تخطيط الموقع"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:70
#: inc/customizer/class-astra-customizer-register-sections-panels.php:52
#: inc/customizer/class-astra-customizer-register-sections-panels.php:97
#: inc/customizer/class-astra-customizer-register-sections-panels.php:124
#: inc/customizer/class-astra-customizer-register-sections-panels.php:180
#: inc/customizer/class-astra-customizer-register-sections-panels.php:243
#: inc/customizer/class-astra-customizer-register-sections-panels.php:280
#: inc/customizer/class-astra-customizer-register-sections-panels.php:322
msgid "Helpful Information"
msgstr "معلومات مفيدة"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:246
msgid "Sidebar Overview"
msgstr "نظرة عامة على الشريط الجانبي "

#: inc/customizer/class-astra-customizer-register-sections-panels.php:183
msgid "Footer Bar Overview"
msgstr "نظرة عامة على شريط التذييل"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:100
msgid "Site Identity Overview"
msgstr "نظرة عامة على هوية الموقع"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:61
msgid "Container Overview"
msgstr "نظرة عامة على الحاوية"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:127
msgid "Primary Header Overview"
msgstr "نظرة عامة على الشريط العلوي الأساسي"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:639
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:821
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:890
#: inc/customizer/configurations/builder/header/configs/account.php:141
#: inc/customizer/configurations/builder/header/configs/account.php:172
#: inc/customizer/configurations/builder/header/configs/account.php:266
#: inc/customizer/configurations/builder/header/configs/account.php:294
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:151
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:649
#: inc/lib/bsf-analytics/modules/deactivation-survey/classes/class-deactivation-survey-feedback.php:112
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Icon"
msgstr "أيقونة"

#: assets/svg/logo-svg-icons/icons-v6-2.php:1103
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:570
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:963
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:115
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:151
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:152
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:650
msgid "Link"
msgstr "رابط"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:258
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:85
msgid "Different Logo For Retina Devices?"
msgstr "شعار مختلف لأجهزة Retina؟"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:183
msgid "Mobile Logo (optional)"
msgstr "شعار الجوال (اختياري)"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:601
msgid "Toggle Button Color"
msgstr "تبديل زر اللون"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:92
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:567
msgid "Toggle Button Style"
msgstr "تبديل نمط الزر"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:142
msgid "Last Item in Menu"
msgstr "العنصر الأخير في القائمة"

#: admin/includes/class-astra-menu.php:375
#: inc/customizer/class-astra-customizer-register-sections-panels.php:92
#: inc/customizer/class-astra-customizer.php:224
#: inc/customizer/class-astra-customizer.php:236
msgid "Site Identity"
msgstr "هوية الموقع"

#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:99
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:581
msgid "Minimal"
msgstr "الحد الأدنى"

#: inc/customizer/configurations/builder/header/configs/header-builder.php:179
msgid "Header Layout"
msgstr "تنسيق الشريط العلوي"

#: admin/includes/class-astra-menu.php:613
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:394
msgid "Mobile Header"
msgstr "الشريط العلوي للجوال"

#: assets/svg/logo-svg-icons/icons-v6-1.php:3086
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:95
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:97
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:321
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:579
msgid "Fill"
msgstr "تعبئة"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:439
msgid "Hide Last Item in Menu on Mobile"
msgstr "إخفاء العنصر الأخير في القائمة على الجوال"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:148
msgid "Different Logo For Mobile Devices?"
msgstr "شعار مختلف لأجهزة الجوال ؟"

#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:184
#: inc/customizer/class-astra-extended-base-configuration.php:182
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:313
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:143
#: inc/customizer/configurations/builder/header/configs/menu.php:298
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:258
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:485
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:590
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:856
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:449
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:530
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:617
msgid "Border Radius"
msgstr "زوايا الإطار"

#: inc/compatibility/learndash/customizer/class-astra-customizer-register-learndash-section.php:37
#: admin/assets/build/dashboard-app.js:1
msgid "LearnDash"
msgstr "LearnDash"

#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:657
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:677
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:327
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:347
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Auto"
msgstr "تلقائي"

#: assets/svg/logo-svg-icons/icons-v6-3.php:259
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Scroll"
msgstr "التمرير"

#: admin/includes/class-astra-menu.php:751
msgid "Supercharge your LearnDash website with amazing design features."
msgstr "اشحن موقع LearnDash الخاص بك من خلال مميزات تصميم مذهلة."

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:58
msgid "Differentiate Rows"
msgstr "التفريق بين الصفوف"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:73
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:49
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Cover"
msgstr "غلاف"

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:41
msgid "Display Serial Number"
msgstr "عرض الرقم التسلسلي"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:182
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:236
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:478
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:694
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:909
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-store-notice-configs.php:77
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:227
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:227
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:127
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:230
#: inc/customizer/configurations/builder/header/configs/above-header.php:75
#: inc/customizer/configurations/builder/header/configs/below-header.php:75
#: inc/customizer/configurations/builder/header/configs/menu.php:387
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:166
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:188
#: inc/customizer/configurations/builder/header/configs/off-canvas.php:247
#: inc/customizer/configurations/builder/header/configs/primary-header.php:92
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:813
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:920
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:163
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:86
#: inc/metabox/class-astra-meta-boxes.php:610
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:611
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1073
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:363
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Background"
msgstr "الخلفية"

#: inc/compatibility/learndash/customizer/sections/layout/class-astra-learndash-general-configs.php:45
msgid "Course Content Table"
msgstr "جدول محتويات المساق"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:425
msgid "Scroll To Top"
msgstr "التمرير إلى الأعلى"

#: admin/includes/class-astra-menu.php:652
msgid "Site Layouts"
msgstr "تنسيقات الموقع"

#: admin/includes/class-astra-menu.php:194
#: admin/includes/class-astra-menu.php:195
#: admin/includes/class-astra-menu.php:691
msgid "Custom Layouts"
msgstr "تنسيقات مخصصة"

#: admin/includes/class-astra-menu.php:707
msgid "Make your header layouts look more appealing and sexy!"
msgstr "جعل تنسيقات أعلى الموقع تبدو أكثر أناقة!"

#: inc/core/class-astra-admin-settings.php:468
#: inc/core/class-astra-admin-settings.php:480
msgid "Activating Importer Plugin "
msgstr "تفعيل إضافة الاستيراد"

#: admin/includes/class-astra-admin-ajax.php:403
msgid "Plugin Successfully Activated"
msgstr "تم تفعيل الإضافة بنجاح"

#: admin/includes/class-astra-admin-ajax.php:369
#: admin/includes/class-astra-admin-ajax.php:440
msgid "No plugin specified"
msgstr "لا يوجد إضافة محددة"

#: admin/includes/class-astra-menu.php:383
msgid "Footer Settings"
msgstr "إعدادات تذييل الموقع"

#: admin/includes/class-astra-menu.php:706
msgid "Page Headers"
msgstr "الأشرطة الرأسية للصفحات"

#: admin/includes/class-astra-menu.php:639
#: inc/customizer/configurations/builder/header/configs/header-builder.php:740
#: inc/metabox/class-astra-meta-boxes.php:634
msgid "Sticky Header"
msgstr "شريط رأسي ثابت"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:335
#: inc/addons/transparent-header/classes/class-astra-transparent-header-panels-and-sections.php:45
#: inc/customizer/configurations/builder/header/configs/header-builder.php:630
#: inc/metabox/class-astra-meta-boxes.php:636
msgid "Transparent Header"
msgstr "شريط رأسي شفاف"

#: admin/includes/class-astra-menu.php:626
msgid "Header Sections"
msgstr "أقسام الشريط رأسي"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:463
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:673
#: admin/assets/build/dashboard-app.js:2
msgid "Learn More"
msgstr "تعلم المزيد"

#: inc/customizer/configurations/colors-background/class-astra-body-colors-configs.php:66
msgid "Theme Color"
msgstr "لون القالب"

#: inc/customizer/astra-pro/class-astra-pro-upgrade-link-configs.php:31
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:463
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:673
msgid "More Options Available in Astra Pro!"
msgstr "المزيد من الخيارات المتاحة في Astra Pro!"

#: admin/includes/class-astra-menu.php:778
#: admin/assets/build/dashboard-app.js:1
msgid "White Label"
msgstr "White Label"

#: admin/includes/class-astra-menu.php:600
msgid "Blog Pro"
msgstr "مدونة Pro"

#: admin/includes/class-astra-menu.php:587
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:321
#: inc/customizer/class-astra-extended-base-configuration.php:59
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:173
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:485
#: inc/customizer/configurations/builder/footer/configs/copyright.php:115
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:236
#: inc/customizer/configurations/builder/header/configs/account.php:584
#: inc/customizer/configurations/builder/header/configs/menu.php:618
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:405
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:287
#: inc/customizer/configurations/builder/header/configs/search.php:195
#: inc/customizer/configurations/builder/header/configs/site-identity.php:114
#: admin/assets/build/dashboard-app.js:1
msgid "Spacing"
msgstr "مسافة التباعد"

#: inc/customizer/class-astra-customizer.php:1817
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:72
msgid "Default System Font"
msgstr "خط النطام الافتراضي"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:96
msgid "Membership Columns"
msgstr "أعمدة العضوية"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:275
msgid "Shop Archive Content Width"
msgstr "عرض محتوى أرشيف المتجر"

#: inc/compatibility/lifterlms/customizer/sections/layout/class-astra-lifter-general-configs.php:69
msgid "Course Columns"
msgstr "أعمدة الدورة التدريبية"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:234
msgid "What Others Have Said"
msgstr "ماذا قال الآخرون"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:302
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:320
msgid "Thank you for your review!"
msgstr "شكرا لك على مراجعتك!"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:309
msgid "Write a Review"
msgstr "اكتب مراجعة"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:310
msgid "Review Title"
msgstr "عنوان المراجعة"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:311
msgid "Review Title is required."
msgstr "عنوان المراجعة إلزامي."

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:312
msgid "Review Text"
msgstr "نص المراجعة"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:317
msgid "Leave Review"
msgstr "أترك مراجعة"

#: inc/compatibility/lifterlms/customizer/class-astra-liferlms-section-configs.php:36
#: inc/customizer/configurations/builder/header/configs/account.php:44
#: admin/assets/build/dashboard-app.js:1
msgid "LifterLMS"
msgstr "LifterLMS"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:313
msgid "Review Text is required."
msgstr "يجب إدخال نص المراجعة."

#. translators: 1 Author Name.
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:265
msgid "By: %s"
msgstr "بواسطة: %s"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:109
msgid "Sidebar width will apply only when one of the above sidebar is set."
msgstr "عرض الشريط الجانبي سيطبق فقط عندما يتم تحديد أحد الأشرطة الجانبية أعلاه."

#: inc/customizer/configurations/builder/header/configs/woo-cart.php:199
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:220
msgid "Cart Page"
msgstr "صفحة السلة"

#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:373
msgid "Inline Logo & Site Title"
msgstr "الشعار وعنوان الموقع على سطر واحد"

#: inc/core/class-astra-admin-settings.php:148
#: admin/assets/build/dashboard-app.js:2
msgid "Settings"
msgstr "الإعدادات"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:43
#: inc/compatibility/learndash/customizer/class-astra-customizer-register-learndash-section.php:43
#: inc/compatibility/lifterlms/customizer/class-astra-liferlms-section-configs.php:45
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:59
#: inc/customizer/class-astra-customizer-register-sections-panels.php:203
#: admin/assets/build/dashboard-app.js:2
msgid "General"
msgstr "عام"

#: assets/svg/logo-svg-icons/icons-v6-3.php:621
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:35
msgid "Shop"
msgstr "متجر"

#: inc/compatibility/edd/customizer/class-astra-customizer-register-edd-section.php:60
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:44
msgid "Single Product"
msgstr "منتج منفرد"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:237
msgid "Shop Columns"
msgstr "أعمدة المتجر"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:253
msgid "Products Per Page"
msgstr "عدد المنتجات في الصفحة"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:96
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:177
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:114
msgid "Price"
msgstr "السعر"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:97
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:178
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:119
msgid "Short Description"
msgstr "الوصف القصير"

#: inc/compatibility/edd/class-astra-edd.php:859
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:98
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:53
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:60
msgid "Add To Cart"
msgstr "إضافة للسلة"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:95
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:176
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:113
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:264
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:251
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:531
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:134
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:672
msgid "Title"
msgstr "العنوان"

#: inc/compatibility/woocommerce/woocommerce-common-functions.php:81
msgid "Out of stock"
msgstr "غير متوفر في المخزون"

#: assets/svg/logo-svg-icons/icons-v6-0.php:2310
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:1000
msgid "Blog"
msgstr "المدونة"

#: inc/customizer/configurations/builder/header/configs/account.php:48
#: admin/assets/build/dashboard-app.js:1
msgid "WooCommerce"
msgstr "ووكومرس"

#: inc/metabox/class-astra-meta-boxes.php:413
#: inc/metabox/class-astra-meta-boxes.php:909
msgid "Disable Breadcrumb"
msgstr "إلغاء Breadcrumb"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:64
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:115
msgid "Ratings"
msgstr "التقييمات"

#: inc/compatibility/woocommerce/woocommerce-common-functions.php:134
msgid "Availability:"
msgstr "حالة التوفر:"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-colors-transparent-header-configs.php:93
#: inc/customizer/class-astra-customizer-register-sections-panels.php:119
#: inc/customizer/configurations/builder/header/configs/primary-header.php:41
msgid "Primary Header"
msgstr "الشريط العلوي الرئيسي"

#: inc/compatibility/edd/class-astra-edd.php:101
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:557
#: inc/compatibility/woocommerce/customizer/class-astra-customizer-register-woo-section.php:52
#: inc/core/builder/class-astra-builder-helper.php:841
#: inc/core/builder/class-astra-builder-helper.php:851
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:66
msgid "Cart"
msgstr "سلة المشتريات"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1210
msgid "This sidebar will be used on Single Product page."
msgstr "هذا الشريط الجانبي سيستعمل في صفحة المنتج المنفرد."

#: inc/compatibility/edd/class-astra-edd.php:439
msgid "View your shopping cart"
msgstr "شاهد سلة مشترياتك"

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:358
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1194
msgid "WooCommerce Sidebar"
msgstr "شريط ووكومرس الجانبي"

#: inc/compatibility/edd/class-astra-edd.php:921
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:360
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1196
msgid "This sidebar will be used on Product archive, Cart, Checkout and My Account pages."
msgstr "هذا الشريط الجانبي سيستعمل في صفحات أرشيف المنتجات، السلة، الدفع وصفحة حسابي."

#: inc/compatibility/woocommerce/class-astra-woocommerce.php:1208
msgid "Product Sidebar"
msgstr "الشريط الجانبي للمنتج"

#: inc/addons/breadcrumbs/class-astra-breadcrumb-trail.php:403
#: inc/core/common-functions.php:1065
msgid "Home"
msgstr "الرئيسية"

#: inc/core/common-functions.php:1071
msgid "This page doesn't seem to exist."
msgstr "لا يبدو أن هذه الصفحة موجودة."

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:296
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:129
msgid "Retina Logo"
msgstr "شعار Retina"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:322
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:313
msgid "Logo Width"
msgstr "عرض الشعار"

#: inc/metabox/class-astra-meta-boxes.php:421
msgid "Disable Featured Image"
msgstr "إلغاء الصورة المميزة"

#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:294
msgid "Paragraph Margin Bottom"
msgstr "الهامش السفلي للفقرة"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:248
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:357
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:41
msgid "Featured Image"
msgstr "الصورة البارزة للمقالة"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:793
#: inc/metabox/class-astra-meta-boxes.php:452
msgid "Disable Footer Widgets"
msgstr "إلغاء ودجات شريط التذييل"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:339
msgid "Base Typography"
msgstr "الخط الأساسي"

#: inc/markup-extras.php:1853
msgid "Click here to assign a widget for this area."
msgstr "إضغط هنا لتحديد ودجت لهذه المنطقة"

#: inc/template-parts.php:168
msgid "Main Menu"
msgstr "القائمة الرئيسية"

#. translators: %s: Name of current post.
#: template-parts/content.php:69
msgid "Continue reading %s"
msgstr "تابع قراءة %s"

#: inc/class-astra-global-palette.php:298
#: inc/customizer/class-astra-customizer-register-sections-panels.php:348
#: inc/customizer/class-astra-customizer.php:1707
msgid "Headings"
msgstr "الترويسات"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:174
msgid "Footer Bar"
msgstr "شريط التذييل"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:297
msgid "Base Colors"
msgstr "الألوان الأساسية"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1085
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1116
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:121
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:321
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:230
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:737
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:85
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:311
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:667
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1133
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:407
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:956
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1040
msgid "Link Color"
msgstr "لون الرابط"

#: inc/customizer/configurations/accessibility/class-astra-accessibility-configs.php:108
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:439
msgid "Disable"
msgstr "تعطيل"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:443
msgid "Layout 4"
msgstr "تخطيط 4"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:51
msgid "Footer Bar Layout 1"
msgstr "تخطيط شريط التذييل 1"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:55
msgid "Footer Bar Layout 2"
msgstr "تخطيط شريط التذييل 2"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:838
msgid "Full Width / Stretched"
msgstr "عرض كامل / ممتد"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:44
msgid "Default Layout"
msgstr "التخطيط الافتراضي"

#: inc/widgets.php:121
msgid "Footer Bar Section 1"
msgstr "قسم 1 شريط التذييل"

#: inc/widgets.php:130
msgid "Footer Bar Section 2"
msgstr "قسم 2 شريط التذييل"

#: inc/widgets.php:146
msgid "Footer Widget Area 1"
msgstr "منطقة ودجات شريط التذييل 1"

#: inc/widgets.php:155
msgid "Footer Widget Area 2"
msgstr "منطقة ودجات شريط التذييل 2"

#: inc/widgets.php:164
msgid "Footer Widget Area 3"
msgstr "منطقة ودجات شريط التذييل 3"

#: inc/widgets.php:173
msgid "Footer Widget Area 4"
msgstr "منطقة ودجات شريط التذييل 4"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:837
msgid "Full Width / Contained"
msgstr "عرض كامل / مؤطر"

#. translators: 1: Post type label
#: inc/core/class-theme-strings.php:78 inc/core/class-theme-strings.php:93
msgid "Next %s"
msgstr "ال%s التالية"

#. translators: 1: Post type label
#: inc/core/class-theme-strings.php:80 inc/core/class-theme-strings.php:95
msgid "Previous %s"
msgstr "ال%s السابقة"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:338
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:822
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:834
#: inc/metabox/class-astra-meta-boxes.php:286
#: inc/metabox/class-astra-meta-boxes.php:304
#: inc/metabox/class-astra-meta-boxes.php:319
#: inc/metabox/class-astra-meta-boxes.php:335
#: inc/metabox/class-astra-meta-boxes.php:761
#: inc/metabox/class-astra-meta-boxes.php:797
#: inc/metabox/class-astra-meta-boxes.php:803
msgid "Customizer Setting"
msgstr "إعدادات التخصيص"

#: inc/customizer/class-astra-customizer.php:606
#: inc/customizer/class-astra-font-families.php:125
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:119
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:110
msgid "Thin 100"
msgstr "رقيق 100"

#: inc/customizer/class-astra-customizer.php:610
#: inc/customizer/class-astra-font-families.php:127
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:123
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:114
msgid "Light 300"
msgstr "خفيف 300"

#: inc/customizer/class-astra-customizer.php:612
#: inc/customizer/class-astra-customizer.php:613
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:125
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:126
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:116
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:117
msgid "Normal 400"
msgstr "عادي 400"

#: inc/customizer/class-astra-customizer.php:615
#: inc/customizer/class-astra-font-families.php:129
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:128
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:119
msgid "Medium 500"
msgstr "متوسط 500 "

#: inc/customizer/class-astra-customizer.php:608
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:121
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:112
msgid "Extra-Light 200"
msgstr "رقيق-جداً 200"

#: inc/customizer/class-astra-customizer.php:617
#: inc/customizer/class-astra-font-families.php:130
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:130
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:121
msgid "Semi-Bold 600"
msgstr "غامق قليلاً 600 "

#: inc/customizer/class-astra-customizer.php:619
#: inc/customizer/class-astra-font-families.php:131
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:132
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:123
msgid "Bold 700"
msgstr "غامق 700"

#: inc/customizer/class-astra-customizer.php:621
#: inc/customizer/class-astra-font-families.php:132
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:134
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:125
msgid "Extra-Bold 800"
msgstr "غامق جداً 800"

#: inc/customizer/class-astra-customizer.php:623
#: inc/customizer/class-astra-font-families.php:133
#: inc/customizer/custom-controls/class-astra-customizer-control-base.php:136
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:127
msgid "Ultra-Bold 900"
msgstr "غامق جداً جداً 900"

#: comments.php:71 comments.php:94
msgid "Comments Navigation"
msgstr "تصفح التعليقات"

#: inc/admin-functions.php:30
#: inc/customizer/class-astra-customizer-register-sections-panels.php:141
#: inc/customizer/configurations/builder/header/configs/header-builder.php:112
#: inc/customizer/configurations/builder/header/configs/menu.php:32
msgid "Primary Menu"
msgstr "القائمة الرئيسية"

#: inc/blog/single-blog.php:104
msgid "Pingback:"
msgstr "Pingback:"

#: inc/blog/single-blog.php:104
msgid "(Edit)"
msgstr "(تحرير)"

#: inc/compatibility/class-astra-beaver-themer.php:347
msgid "Before Page"
msgstr "قبل الصفحة"

#: inc/compatibility/class-astra-beaver-themer.php:348
msgid "After Page"
msgstr "بعد الصفحة"

#: inc/compatibility/class-astra-beaver-themer.php:354
msgid "Before Header"
msgstr "قبل الترويسة"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:43
#: inc/compatibility/class-astra-beaver-themer.php:355
msgid "After Header"
msgstr "بعد الترويسة"

#: inc/compatibility/class-astra-beaver-themer.php:361
msgid "Before Content"
msgstr "قبل المحتوى"

#: inc/compatibility/class-astra-beaver-themer.php:362
msgid "After Content"
msgstr "بعد المحتوى"

#: inc/compatibility/class-astra-beaver-themer.php:375
msgid "Before Sidebar"
msgstr "قبل الشريط الجانبي"

#: inc/compatibility/class-astra-beaver-themer.php:376
msgid "After Sidebar"
msgstr "بعد الشريط الجانبي"

#: inc/compatibility/class-astra-beaver-themer.php:383
msgid "Before Post"
msgstr "قبل المقالة"

#: inc/compatibility/class-astra-beaver-themer.php:384
msgid "Before Post Content"
msgstr "قبل محتوى المقالة"

#: inc/compatibility/class-astra-beaver-themer.php:385
msgid "After Post Content"
msgstr "بعد محتوى المقالة"

#: inc/compatibility/class-astra-beaver-themer.php:386
msgid "After Post"
msgstr "بعد المقالة"

#: inc/compatibility/class-astra-beaver-themer.php:387
msgid "Before Comments"
msgstr "قبل التعليقات"

#: inc/compatibility/class-astra-beaver-themer.php:388
msgid "After Comments"
msgstr "بعد التعليقات"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:51
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:51
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:825
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:72
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:127
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:50
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:47
#: inc/metabox/class-astra-meta-boxes.php:322
#: inc/metabox/class-astra-meta-boxes.php:762
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:160
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:140
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:701
msgid "No Sidebar"
msgstr "بدون شريط جانبي"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:59
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:59
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:824
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:80
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:135
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:58
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:55
#: inc/metabox/class-astra-meta-boxes.php:321
#: inc/metabox/class-astra-meta-boxes.php:764
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:168
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:709
msgid "Right Sidebar"
msgstr "شريط جانبي أيمن"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:41
#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:49
#: inc/customizer/configurations/builder/header/configs/account.php:265
#: inc/customizer/configurations/builder/header/configs/menu.php:95
#: inc/customizer/configurations/builder/header/configs/menu.php:156
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:298
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:456
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:611
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:86
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:124
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:156
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:146
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:314
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:221
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:227
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:551
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:570
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:953
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:350
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:231
#: inc/customizer/extend-custom-controls/build/index.js:1
#: inc/metabox/extend-metabox/build/index.js:1
msgid "None"
msgstr "بدون"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:94
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:182
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:121
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:55
msgid "Category"
msgstr "تصنيف"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:204
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:278
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:31
#: inc/customizer/configurations/block-editor/class-astra-block-editor-configs.php:38
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:86
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:106
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:354
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:48
#: inc/extras.php:1225
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:222
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:228
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:343
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:187
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:465
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:610
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:351
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:281
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Custom"
msgstr "مخصص"

#: inc/core/class-astra-theme-options.php:439
#: inc/core/class-astra-theme-options.php:441
msgid "Copyright &copy; [current_year] [site_title] | Powered by [theme_author]"
msgstr "حميع الحقوق محفوظة &copy; [current_year] [site_title] |  تطوير [theme_author]"

#: assets/svg/logo-svg-icons/icons-v6-2.php:5771
#: inc/core/class-theme-strings.php:49
msgid "Reply"
msgstr "رد"

#: inc/core/class-theme-strings.php:50
msgid "Edit"
msgstr "تحرير"

#: inc/core/class-theme-strings.php:51
msgid "Your comment is awaiting moderation."
msgstr "تعليقك بانتطار المصادقة عليه."

#: inc/core/class-theme-strings.php:53
msgid "Cancel Reply"
msgstr "إلغاء الرد"

#: inc/core/class-theme-strings.php:55
msgid "Type here.."
msgstr "اكتب هنا..."

#: inc/core/class-theme-strings.php:58
msgid "Website"
msgstr "الموقع"

#: inc/core/class-theme-strings.php:60
msgid "Comment navigation"
msgstr "تصفح التعليق"

#: inc/core/class-theme-strings.php:61
msgid "Newer Comments"
msgstr "التعليقات الأحدث"

#: inc/core/class-theme-strings.php:62
msgid "Older Comments"
msgstr "التعليقات الأقدم"

#: inc/core/class-theme-strings.php:65 inc/core/class-theme-strings.php:76
msgid "Pages:"
msgstr "الصفحات:"

#: inc/core/class-theme-strings.php:69
msgid "% Comments"
msgstr "% تعليقات"

#: admin/includes/class-astra-menu.php:180
#: admin/includes/class-astra-menu.php:181
#: inc/compatibility/woocommerce/class-astra-woocommerce.php:3591
#: admin/assets/build/dashboard-app.js:1
msgid "Customize"
msgstr "تخصيص"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:297
#: inc/customizer/custom-controls/typography/class-astra-control-typography.php:108
#: inc/metabox/class-astra-meta-boxes.php:977
#: inc/metabox/class-astra-meta-boxes.php:990
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Inherit"
msgstr "وراثة"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:209
msgid "Blog / Archive"
msgstr "مدونة / أرشيف"

#: admin/includes/class-astra-menu.php:391
#: admin/includes/class-astra-menu.php:574
#: inc/customizer/class-astra-customizer-register-sections-panels.php:317
#: inc/customizer/class-astra-customizer.php:1703
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:335
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:194
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:588
#: inc/customizer/configurations/layout/class-astra-site-identity-configs.php:604
#: admin/assets/build/dashboard-app.js:1
msgid "Typography"
msgstr "الخطوط"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:952
msgid "Content"
msgstr "المحتوى"

#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:108
#: inc/customizer/class-astra-customizer-register-sections-panels.php:361
#: inc/customizer/class-astra-customizer.php:1670
msgid "Buttons"
msgstr "الأزرار"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:96
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:316
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:134
#: inc/customizer/configurations/builder/base/class-astra-html-component-configs.php:112
#: inc/customizer/configurations/builder/footer/configs/copyright.php:103
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:214
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:665
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:412
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:708
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:725
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:131
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:148
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:123
#: inc/customizer/configurations/colors-background/class-astra-footer-colors-configs.php:41
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:651
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1117
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:395
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:941
msgid "Text Color"
msgstr "لون النص"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-color-configs.php:75
#: inc/addons/scroll-to-top/classes/customizer/class-astra-scroll-to-top-configs.php:159
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1070
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1097
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:1130
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:340
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:147
#: inc/customizer/configurations/builder/base/class-astra-social-icon-component-configs.php:215
#: inc/customizer/configurations/builder/header/configs/edd-cart.php:245
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:181
#: inc/customizer/configurations/builder/header/configs/woo-cart.php:677
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:426
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:742
#: inc/customizer/configurations/buttons/class-astra-customizer-button-configs.php:759
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:165
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:182
#: inc/customizer/configurations/colors-background/class-astra-advanced-footer-colors-configs.php:39
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:266
msgid "Background Color"
msgstr "لون الخلفية"

#: assets/svg/logo-svg-icons/icons-v6-3.php:2678
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:76
msgid "Tag"
msgstr "وسم"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:156
msgid "Full Content"
msgstr "المحتوى الكامل"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:83
msgid "Section 1"
msgstr "المقطع 1"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:125
msgid "Custom Text"
msgstr "نص مخصص"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:88
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:126
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:158
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:150
msgid "Widget"
msgstr "ودجت"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:122
msgid "Section 1 Custom Text"
msgstr "نص مخصص للمقطع 1"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:154
msgid "Section 2"
msgstr "المقطع 2"

#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:188
msgid "Section 2 Custom Text"
msgstr "نص مخصص للمقطع 2"

#: inc/core/builder/class-astra-builder-helper.php:693
#: inc/core/builder/class-astra-builder-helper.php:869
#: inc/customizer/configurations/builder/header/configs/search.php:32
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:147
#: inc/markup-extras.php:876 searchform.php:31 searchform.php:37
#: admin/assets/build/dashboard-app.js:2
#: inc/customizer/extend-custom-controls/build/index.js:1
msgid "Search"
msgstr "البحث"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:149
msgid "Text / HTML"
msgstr "Text / HTML"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:342
#: inc/customizer/configurations/builder/header/configs/above-header.php:110
#: inc/customizer/configurations/builder/header/configs/below-header.php:110
#: inc/customizer/configurations/builder/header/configs/primary-header.php:127
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:90
msgid "Bottom Border Size"
msgstr "حجم الحد السفلي"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:54
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:54
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:62
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:54
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:107
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:107
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:107
#: inc/customizer/configurations/builder/header/configs/header-builder.php:647
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:93
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:357
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:73
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:61
#: inc/metabox/class-astra-meta-boxes.php:291
#: inc/metabox/class-astra-meta-boxes.php:799
#: inc/metabox/class-astra-meta-boxes.php:806
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:49
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:64
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:342
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:49
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:64
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:464
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:590
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:147
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:658
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:767
msgid "Full Width"
msgstr "العرض الكامل"

#: inc/customizer/configurations/builder/footer/configs/above-footer.php:108
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:108
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:108
#: inc/customizer/configurations/builder/header/configs/header-builder.php:648
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:103
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:45
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:358
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:74
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:184
msgid "Content Width"
msgstr "عرض المحتوى"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:365
#: inc/customizer/configurations/builder/header/configs/above-header.php:92
#: inc/customizer/configurations/builder/header/configs/below-header.php:92
#: inc/customizer/configurations/builder/header/configs/primary-header.php:107
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:117
msgid "Bottom Border Color"
msgstr "لون الحد السفلي"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:185
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:169
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:169
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:81
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:168
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:413
msgid "Stack"
msgstr "Stack"

#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:91
msgid "Sidebar Width"
msgstr "عرض الشريط الجانبي"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:300
msgid "Uppercase"
msgstr "أحرف كبيرة"

#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:396
msgid "Site Title"
msgstr "عنوان الموقع"

#: inc/markup-extras.php:908
msgid "Add Custom HTML"
msgstr "أضف كود HTML"

#: assets/svg/logo-svg-icons/icons-v6-3.php:6181
msgid "Y"
msgstr "Y"

#: inc/markup-extras.php:1813
msgid "Add Widget"
msgstr "إضافة ودجت"

#. translators: %s: Name of current post
#: inc/template-tags.php:35 template-parts/content-page.php:34
#: template-parts/single/single-layout.php:53
msgid "Edit %s"
msgstr "تحرير %s"

#: inc/widgets.php:93
msgid "Main Sidebar"
msgstr "الشريط الجانبي الرئيسي"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:115
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:166
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:206
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:391
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:85
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:129
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:391
#: inc/customizer/configurations/builder/class-astra-builder-base-configuration.php:444
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:322
#: inc/customizer/configurations/builder/header/configs/menu.php:566
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:355
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:358
#: inc/customizer/configurations/builder/header/configs/mobile-trigger.php:387
#: inc/customizer/configurations/builder/header/configs/site-identity.php:162
#: inc/customizer/configurations/builder/header/configs/site-identity.php:197
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:235
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:138
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:88
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:178
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:268
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:358
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:447
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:536
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:765
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:850
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1231
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1316
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1421
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:487
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:556
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1103
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1188
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1273
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1357
msgid "Font Size"
msgstr "حجم الخط"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:97
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:128
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:146
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:374
#: inc/customizer/configurations/builder/header/configs/menu.php:551
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:340
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:276
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:122
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:237
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:64
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:154
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:246
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:335
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:425
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:515
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:749
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:833
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1214
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1299
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1405
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:475
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:544
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1086
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1171
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1256
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1341
msgid "Font Weight"
msgstr "وزن الخط"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-configs.php:33
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:46
#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:74
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:47
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:79
#: inc/compatibility/edd/customizer/sections/layout/class-astra-edd-archive-layout-configs.php:203
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:46
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:74
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:47
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:79
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:54
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:82
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:68
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:100
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:123
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:155
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:46
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:74
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:46
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:78
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:277
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:331
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-layout-configs.php:355
#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:474
#: inc/customizer/configurations/builder/header/configs/account.php:24
#: inc/customizer/configurations/builder/header/configs/account.php:170
#: inc/customizer/configurations/builder/header/configs/account.php:293
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:74
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:105
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:329
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:561
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:584
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:632
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:654
#: inc/customizer/configurations/layout/class-astra-blog-single-layout-configs.php:47
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:92
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:119
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:253
#: inc/metabox/class-astra-meta-boxes.php:816
#: inc/metabox/class-astra-meta-boxes.php:828
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:37
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:56
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:130
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:156
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:186
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:37
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:56
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:111
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:136
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:166
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:186
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:797
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:926
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1600
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:646
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:675
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:697
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:724
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:454
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:473
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:492
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:711
msgid "Default"
msgstr "افتراضي"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:157
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:277
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:533
msgid "Excerpt"
msgstr "المقتطف"

#: assets/svg/logo-svg-icons/icons-v6-0.php:5818
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:38
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:49
#: inc/customizer/configurations/comments/class-astra-comments-configs.php:61
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:692
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:826
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:261
msgid "Comments"
msgstr "تعليقات"

#: inc/compatibility/class-astra-beaver-themer.php:368
msgid "Before Footer"
msgstr "قبل شريط التذييل"

#: inc/compatibility/class-astra-beaver-themer.php:369
msgid "After Footer"
msgstr "بعد شريط التذييل"

#: inc/compatibility/class-astra-beaver-themer.php:382
msgid "Loop Start"
msgstr "بدء التكرار"

#: inc/compatibility/class-astra-beaver-themer.php:389
msgid "Loop End"
msgstr "نهاية التكرار"

#: inc/core/class-theme-strings.php:29
msgid "Post Comment &raquo;"
msgstr "انشر تعليقا &raquo;"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:147
#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:138
#: inc/customizer/configurations/builder/footer/configs/footer-builder.php:171
#: admin/assets/theme-builder/build/index.js:8795
#: admin/assets/theme-builder/build/index.js:9335
msgid "Footer"
msgstr "شريط التذييل"

#: admin/includes/class-astra-menu.php:665
#: inc/customizer/class-astra-customizer-register-sections-panels.php:166
#: inc/customizer/class-astra-customizer-register-sections-panels.php:306
msgid "Footer Widgets"
msgstr "ودجات شريط التذييل"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:819
#: inc/customizer/class-astra-customizer-register-sections-panels.php:238
#: inc/metabox/class-astra-meta-boxes.php:626
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar"
msgstr "الشريط الجانبي"

#: admin/includes/class-astra-menu.php:561
#: inc/addons/transparent-header/classes/sections/class-astra-customizer-transparent-header-configs.php:384
msgid "Colors & Background"
msgstr "الألوان والخلفية"

#: inc/addons/transparent-header/classes/class-astra-transparent-header-panels-and-sections.php:54
#: inc/customizer/class-astra-customizer-register-sections-panels.php:80
#: inc/customizer/configurations/builder/header/configs/primary-header.php:34
#: inc/widgets.php:107 admin/assets/theme-builder/build/index.js:8732
#: admin/assets/theme-builder/build/index.js:9320
msgid "Header"
msgstr "الشريط العلوي"

#: inc/addons/transparent-header/classes/class-astra-ext-transparent-header-markup.php:340
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:47
#: inc/metabox/class-astra-meta-boxes.php:979
msgid "Disabled"
msgstr "معطّل"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:47
msgid "Logo Left"
msgstr "شعار في اليسار"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:51
msgid "Logo Center"
msgstr "شعار في الوسط"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:55
msgid "Logo Right"
msgstr "شعار في اليمين"

#: inc/markup-extras.php:1115
msgid "Assign Footer Menu"
msgstr "إسناد قائمة لشريط التذييل"

#: admin/includes/class-astra-menu.php:403
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:82
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:82
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:70
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:82
#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:193
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:44
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:434
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:40
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:420
msgid "Layout"
msgstr "التخطيط"

#: inc/customizer/configurations/layout/class-astra-blog-layout-configs.php:694
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:334
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:62
msgid "Author"
msgstr "الكاتب"

#: inc/admin-functions.php:77
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:42
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:89
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:127
#: inc/customizer/configurations/layout/class-astra-footer-layout-configs.php:159
msgid "Footer Menu"
msgstr "قائمة شريط التذييل"

#: inc/blog/blog-config.php:739
msgid "Read More &raquo;"
msgstr "قراءة المزيد &raquo;"

#: inc/core/class-theme-strings.php:66
msgid "By "
msgstr "بواسطة"

#: inc/core/class-theme-strings.php:68
msgid "1 Comment"
msgstr "تعليق واحد"

#: inc/customizer/class-astra-customizer-sanitizes.php:467
msgid "Enter valid email address!"
msgstr "أدخل بريد إلكتروني صحيح"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:126
msgid "Disable Menu"
msgstr "تعطيل القائمة"

#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:281
msgid "Custom Menu Text / HTML"
msgstr "قائمة مخصصة Text / HTML"

#: inc/customizer/configurations/layout/class-astra-site-layout-configs.php:39
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:340
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:462
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:145
msgid "Container Width"
msgstr "عرض الحاوية"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:831
#: inc/metabox/class-astra-meta-boxes.php:630
msgid "Content Layout"
msgstr "تخطيط المحتوى"

#: inc/core/class-astra-theme-options.php:483
msgid "Contact Us"
msgstr "اتصل بنا"

#: inc/core/class-theme-strings.php:35
msgid "Skip to content"
msgstr "تخطي إلى المحتوى"

#: inc/core/class-theme-strings.php:38
msgid "It looks like the link pointing here was faulty. Maybe try searching?"
msgstr "يبدو أن الرابط الذي يشير هنا كان خاطئ. جرب محاولة البحث؟"

#: inc/core/class-theme-strings.php:41
msgid "Nothing Found"
msgstr "لم يتم العثور على نتائج"

#: inc/core/class-theme-strings.php:43
msgid "Start typing and press enter to search"
msgstr "إبدأ بالكتابة ثم اضغط زر enter للبحث"

#: inc/metabox/class-astra-meta-boxes.php:347
#: inc/metabox/class-astra-meta-boxes.php:631
msgid "Disable Sections"
msgstr "تعطيل الأقسام"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:746
#: inc/metabox/class-astra-meta-boxes.php:371
#: inc/metabox/class-astra-meta-boxes.php:852
msgid "Disable Primary Header"
msgstr "تعطيل الشريط العلوي الرئيسي"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:784
#: inc/metabox/class-astra-meta-boxes.php:403
msgid "Disable Title"
msgstr "تعطيل العنوان"

#. translators: 1: number of comments
#: comments.php:58
msgctxt "comments title"
msgid "%1$s thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] "لا توجد أفكار عن &ldquo;%2$s&rdquo;"
msgstr[1] "%1$s فكرة عن &ldquo;%2$s&rdquo;"
msgstr[2] "فكرتين عن&ldquo;%2$s&rdquo;"
msgstr[3] "%1$s أفكار عن &ldquo;%2$s&rdquo;"
msgstr[4] "%1$s فكرة عن &ldquo;%2$s&rdquo;"
msgstr[5] "%1$s فكرة عن &ldquo;%2$s&rdquo;"

#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:682
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1148
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:418
msgid "Link Hover Color"
msgstr "لون الرابط عند التمرير"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:810
msgid "Astra Settings"
msgstr "إعدادات Astra"

#: inc/compatibility/lifterlms/class-astra-lifterlms.php:836
msgid "Content Boxed"
msgstr "محتوى داخل صندوق"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-container-configs.php:76
#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:81
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-container-configs.php:76
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:81
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:835
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-container-configs.php:84
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:102
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:157
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-container-configs.php:76
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:80
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:74
#: inc/customizer/configurations/layout/class-astra-site-container-layout-configs.php:82
#: inc/metabox/class-astra-meta-boxes.php:306
#: inc/metabox/class-astra-meta-boxes.php:337
#: inc/metabox/class-astra-meta-boxes.php:818
#: inc/metabox/class-astra-meta-boxes.php:830
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:132
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:188
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:113
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:168
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:677
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:726
msgid "Boxed"
msgstr "داخل صندوق"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:154
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:317
msgid "Line Height"
msgstr "ارتفاع السطر"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:55
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:55
#: inc/compatibility/lifterlms/class-astra-lifterlms.php:823
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:76
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:131
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:54
#: inc/customizer/configurations/layout/class-astra-sidebar-layout-configs.php:51
#: inc/metabox/class-astra-meta-boxes.php:320
#: inc/metabox/class-astra-meta-boxes.php:763
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:164
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:144
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:705
msgid "Left Sidebar"
msgstr "الشريط الجانبي (يسار)"

#. translators: 1: date, 2: time
#: inc/blog/single-blog.php:163
msgid "%1$s at %2$s"
msgstr "%1$s الساعة %2$s"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:47
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Container"
msgstr "حاوية"

#: inc/core/class-theme-strings.php:83
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "يبدو أننا لا نستطيع العثور على ما تبحث عنه. ربما قد يساعدك البحث."

#: inc/customizer/class-astra-customizer-register-sections-panels.php:216
msgid "Single Post"
msgstr "مقالة منفردة"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:290
msgid "Text Transform"
msgstr "تحويل النص"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:299
msgid "Capitalize"
msgstr "الكتابة بأحرف كبيرة"

#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:301
msgid "Lowercase"
msgstr "أحرف صغيرة"

#: inc/compatibility/woocommerce/customizer/sections/layout/class-astra-woo-shop-single-layout-configs.php:184
#: inc/customizer/configurations/builder/footer/configs/above-footer.php:170
#: inc/customizer/configurations/builder/footer/configs/below-footer.php:170
#: inc/customizer/configurations/builder/footer/configs/menu-footer.php:80
#: inc/customizer/configurations/builder/footer/configs/primary-footer.php:169
#: inc/customizer/configurations/layout/class-astra-header-layout-configs.php:409
msgid "Inline"
msgstr "مضمنة (على سطر واحد)"

#. translators: 1: link to new post
#: template-parts/content-none.php:23
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "هل أنت مستعدّ لنشر مقالتك الأولى؟ <a href=\"%1$s\">ابدأ من هنا</a>."

#: inc/core/class-theme-strings.php:42
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "عذرًا، لا يوجد شيء يتطابق مع كلمات البحث التي استعملتها، الرجاء المحاولة من جديد باستعمال كلمات مفتاحية أخرى."

#: inc/core/class-theme-strings.php:59
msgid "Comments are closed."
msgstr "التعليقات مغلقة."

#: inc/core/class-theme-strings.php:52 inc/core/class-theme-strings.php:67
msgid "Leave a Comment"
msgstr "اترك تعليقاً"

#: inc/addons/breadcrumbs/customizer/class-astra-breadcrumbs-typo-configs.php:79
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:94
#: inc/addons/heading-colors/customizer/class-astra-heading-colors-configs.php:111
#: inc/customizer/configurations/builder/base/class-astra-button-component-configs.php:356
#: inc/customizer/configurations/builder/header/configs/menu.php:533
#: inc/customizer/configurations/builder/header/configs/mobile-menu.php:322
#: inc/customizer/configurations/buttons/class-astra-existing-button-configs.php:223
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:76
#: inc/customizer/configurations/typography/class-astra-body-typo-configs.php:218
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:47
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:136
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:227
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:316
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:407
#: inc/customizer/configurations/typography/class-astra-headings-typo-configs.php:496
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:732
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:816
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1197
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1282
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:1388
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:462
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:531
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1069
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1154
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1239
#: inc/modules/related-posts/customizer/class-astra-related-posts-configs.php:1324
msgid "Font Family"
msgstr "عائلة الخط"

#: inc/customizer/class-astra-customizer-register-sections-panels.php:224
msgid "Page"
msgstr "الصفحة"

#: inc/compatibility/edd/customizer/sections/class-astra-edd-sidebar-configs.php:44
#: inc/compatibility/learndash/customizer/sections/class-astra-learndash-sidebar-configs.php:44
#: inc/compatibility/lifterlms/customizer/sections/class-astra-lifter-sidebar-configs.php:29
#: inc/compatibility/woocommerce/customizer/sections/class-astra-woo-shop-sidebar-configs.php:43
#: inc/metabox/class-astra-meta-boxes.php:316
#: inc/modules/posts-structures/customizer/class-astra-posts-archive-structures-configs.php:151
#: inc/modules/posts-structures/customizer/class-astra-posts-single-structures-configs.php:132
#: inc/modules/posts-structures/customizer/class-astra-posts-special-archive-structures-configs.php:693
#: inc/metabox/extend-metabox/build/index.js:1
msgid "Sidebar Layout"
msgstr "تخطيط الشريط الجانبي"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wpastra.com/"
msgstr "https://wpastra.com/"