<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/betterdocs" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/betterdocs-pro" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/betterdocs-pro/public/instant-answer" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/betterdocs/includes/gutenberg/util" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/essential-addons-elementor" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/essential-addons-for-elementor-lite" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/flexia-core" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/flexia-pro" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/notificationx" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/notificationx-pro" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/plugins/product-quotation-for-woocommerce" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/themes/firsttheme" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/wp-content/themes/pmc-sportico-2020" vcs="Git" />
  </component>
</project>